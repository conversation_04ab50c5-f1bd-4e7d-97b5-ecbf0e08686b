package com.dt.domain.bill.dto;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/20 10:26
 */
@Data
@Accessors(chain = true)
@ApiModel(value="AllocationOrder", description="分配单")
public class AllocationOrderDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    private String allocationOrderCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "波次号")
    private String waveCode;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "拣选单号")
    private String pickCode;

    @ApiModelProperty(value = "包裹编码")
    private String packageCode;

    @ApiModelProperty(value = "包裹明细")
    private Long pUid;

    @ApiModelProperty(value = "UUID")
    private String packUid;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "巷道编码")
    private String tunnelCode;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "库位拣货序号")
    private Long pickSeq;

    @ApiModelProperty(value = "取值分配的货品批次号")
    private String skuLotNo;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "实发数量")
    private BigDecimal realQty;

    @ApiModelProperty(value = "拣选数量")
    private BigDecimal pickQty;

    @ApiModelProperty(value = "分拣选数量")
    private BigDecimal splitQty;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "10:原始明细,20:预包商品,30:预包剩余商品明细,40:预包商品对应子商品 AllocationIsPreEnum")
    private String isPre;

    @ApiModelProperty(value = "预包商品对应子商品的配比")
    private Map<String, BigDecimal> skuNeedMap;

    @ApiModelProperty(value = "失效/过期日期 【业务字段,分配表无记录】")
    private Long expireDate;

    @ApiModelProperty(value = "生产批次号,【业务字段,分配表无记录】")
    private String productionNo;

    @ApiModelProperty(value = "效期码 ,【业务字段,分配表无记录】")
    private String validityCode;
}