package com.dt.domain.bill.dto;

import java.util.List;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by nobody on 2020/12/28 17:48
 */
@Data
@ApiModel(value = "TransferDTO", description = "库存转移")
public class TransferDTO  implements java.io.Serializable  {

    private Long id;

    @ApiModelProperty(value = "转移单编码")
    private String code;

    @ApiModelProperty(value = "ERP系统对应单号")
    private String erpCode;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "单据状态 字典组:TRANSFER_STATUS")
    private String status;

    @ApiModelProperty(value = "转移原因 字典组:REASON_TYPE")
    private String reason;

    @ApiModelProperty(value = "转移描叙")
    private String note;

    @ApiModelProperty(value = "完成日期 (时间戳)")
    private Long completeDate;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "创建时间")
    private Long createdTime;

    @ApiModelProperty(value = "更新时间")
    private Long updatedTime;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "是否通知erp true 通知 false 不通知")
    private Boolean isErpCancel;

    @ApiModelProperty(value = "审核说明")
    private String remark;

    @ApiModelProperty(value = "业务场景")
    private String businessType;

    private List<TransferDetailDTO> transferDetailDTOList;

    /**
     * 是否需要ERP审核
     */
    private String needERPCheck;

    @ApiModelProperty(value = "是否淘天仓库编码")
    private Boolean isTaoTian;

    @ApiModelProperty(value = "拓传json")
    private String extraJson;

    public JSONObject extraInfo() {
            if (StrUtil.isBlank(extraJson)) return JSONUtil.createObj();
            return JSONUtil.parseObj(extraJson);
    }

    public void occupyOrderCode(String occupyOrderCode) {
        JSONObject jsonObject = extraInfo();
        jsonObject.set("occupyOrderCode", occupyOrderCode);
        setExtraJson(jsonObject.toString());
    }


    public String occupyOrderCode() {
        JSONObject jsonObject = extraInfo();
        if (jsonObject.containsKey("occupyOrderCode")) {
            return jsonObject.getStr("occupyOrderCode");
        }
        return StrUtil.EMPTY;
    }


    @ApiModelProperty(value = "订单标记")
    private Integer orderTag;

    @ApiModelProperty(value = "订单tag")
    private String orderTagName;
}