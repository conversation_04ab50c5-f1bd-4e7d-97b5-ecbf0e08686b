package com.dt.domain.bill.param.rs;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 销退单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SalesReturnOrder对象", description = "销退单")
public class SalesReturnOrderParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 全局单号-在但丁云系统中唯一单号
     */
    @ApiModelProperty(value = "全局单号-在但丁云系统中唯一单号")
    private String globalNo;
    private List<String> globalNoList;

    /**
     * 实体仓编码
     */
    @ApiModelProperty(value = "实体仓编码")
    private String realWarehouseCode;
    private List<String> realWarehouseCodeList;

    /**
     * 实体仓名称
     */
    @ApiModelProperty(value = "实体仓名称")
    private String realWarehouseName;

    /**
     * 云仓编码
     */
    @ApiModelProperty(value = "云仓编码")
    private String logicWarehouseCode;

    /**
     * 销退单
     */
    @ApiModelProperty(value = "销退单")
    private String salesReturnOrderNo;
    private List<String> salesReturnOrderNoList;

    /**
     * 已创建，已登记，已交接，质检中，已质检，申报中，已申报，已上架，已拒收，已出库
     */
    @ApiModelProperty(value = "已创建，已登记，已交接，质检中，已质检，申报中，已申报，已上架，已拒收，已出库")
    private Integer status;
    @ApiModelProperty(value = "状态  查询时not in语法")
    private List<Integer> statusListNotIn;
    private Integer noStatus;
    private List<Integer> statusList;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    /**
     * 货主名称
     */
    @ApiModelProperty(value = "货主名称")
    private String cargoName;

    /**
     * 销售店铺ID
     */
    @ApiModelProperty(value = "销售店铺ID")
    private String saleShopId;
    private List<String> saleShopIdList;

    /**
     * 销售店铺名称
     */
    @ApiModelProperty(value = "销售店铺名称")
    private String saleShopName;

    @ApiModelProperty(value = "淘天店铺名称")
    private List<String> shopNickList;

    /**
     * 客户单号
     */
    @ApiModelProperty(value = "客户单号")
    private String poNo;
    private List<String> poNoList;

    /**
     * 售后单号
     */
    @ApiModelProperty(value = "售后单号")
    private String afterSalesTrackingNo;
    private List<String> afterSalesTrackingNoList;

    /**
     * 快递公司编码
     */
    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;
    private List<String> carrierCodeList;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String carrierName;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String expressNo;
    private List<String> expressNoList;

    /**
     * 快递公司编码
     */
    @ApiModelProperty(value = "快递公司编码")
    private String reverseCarrierCode;
    private List<String> reverseCarrierCodeList;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String reverseCarrierName;

    /**
     * 逆向快递单号
     */
    @ApiModelProperty(value = "逆向快递单号")
    private String reverseExpressNo;
    private List<String> reverseExpressNoList;

    /**
     * 保税类型
     */
    @ApiModelProperty(value = "保税类型")
    private String taxType;

    /**
     * 退货类型
     */
    @ApiModelProperty(value = "退货类型")
    private Integer returnType;

    /**
     * 质检结果
     */
    @ApiModelProperty(value = "质检结果")
    private Integer inspectionResult;

    /**
     * 异常原因
     */
    @ApiModelProperty(value = "异常原因")
    private String abnormalCause;

    /**
     * 申报结果
     */
    @ApiModelProperty(value = "申报结果")
    private Integer declarationResults;

    /**
     * 存放容器
     */
    @ApiModelProperty(value = "存放容器")
    private String container;
    private List<String> containerList;

    /**
     * 上架类型
     */
    @ApiModelProperty(value = "上架类型")
    private Integer shelfType;

    /**
     * 暂存位
     */
    @ApiModelProperty(value = "暂存位")
    private String locationCode;
    private List<String> locationCodeList;

    /**
     * 出库类型
     */
    @ApiModelProperty(value = "出库类型")
    private Integer outType;

    /**
     * 交接时间
     */
    @ApiModelProperty(value = "交接时间")
    private Long handoverTime;
    private Long handoverTimeStart;
    private Long handoverTimeEnd;

    /**
     * 质检时间
     */
    @ApiModelProperty(value = "质检时间")
    private Long inspectionTime;
    private Long inspectionTimeStart;
    private Long inspectionTimeEnd;

    /**
     * 申报时间
     */
    @ApiModelProperty(value = "申报时间")
    private Long declareTime;
    private Long declareTimeStart;
    private Long declareTimeEnd;

    /**
     * 拓展字段
     */
    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "退货入库单号")
    private String asnId;
    private List<String> asnIdList;

    @ApiModelProperty(value = "是否申报超过30天")
    private Integer overdue;

    @ApiModelProperty(value = "退货原因")
    private String returnReason;
    @ApiModelProperty(value = "单据来源")
    private Integer billSource;
    @ApiModelProperty(value = "单据来源")
    private List<Integer> billSourceList;


    @ApiModelProperty(value = "正向快递公司模糊查询")
    private String carrierNameLike;
    @ApiModelProperty(value = "逆向快递公司模糊查询")
    private String reverseCarrierNameLike;

    @ApiModelProperty(value = "LP单号")
    private String lpNo;
    private List<String> lpNoList;

    @ApiModelProperty(value = "MFC单号")
    private String mfcNo;
    private List<String> mfcNoList;
    @ApiModelProperty("是否支持二次入区")
    private Integer returnAllowEntry;
}