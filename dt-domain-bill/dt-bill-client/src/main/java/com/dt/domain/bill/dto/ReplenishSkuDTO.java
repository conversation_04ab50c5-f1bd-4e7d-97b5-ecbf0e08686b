package com.dt.domain.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/10/16 16:08
 */
@Data
public class ReplenishSkuDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "正次品")
    private String skuQuality;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private String upcCode;

    @ApiModelProperty(value = "批次编码")
    private String skuLotNo;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDateStart;

    @ApiModelProperty(value = "失效日期")
    private Long expireDateEnd;

    @ApiModelProperty(value = "禁售比对时间")
    private Long withdrawCompareDate;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "包裹数量")
    private Integer packNum;

    @ApiModelProperty(value = "是否淘天货主 true 淘天  false 非淘天")
    private Boolean taoTianCargo;
}
