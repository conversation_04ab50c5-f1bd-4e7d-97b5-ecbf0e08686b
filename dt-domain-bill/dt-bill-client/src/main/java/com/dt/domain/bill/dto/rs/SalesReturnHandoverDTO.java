package com.dt.domain.bill.dto.rs;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 交接单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SalesReturnHandover对象", description = "交接单")
public class SalesReturnHandoverDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 交接单
     */
    @ApiModelProperty(value = "交接单")
    private String handoverNo;

    /**
     * 交接方
     */
    @ApiModelProperty(value = "交接方")
    private String handoverParty;

    /**
     * 交接中、已交接、取消
     */
    @ApiModelProperty(value = "交接中、已交接、取消")
    private Integer status;

    /**
     * 1 是 2 否
     */
    @ApiModelProperty(value = "1 是 2 否")
    private Integer damage;

    /**
     * 1 是 2 否
     */
    @ApiModelProperty(value = "1 是 2 否")
    private Integer reject;

    /**
     * 包裹数量
     */
    @ApiModelProperty(value = "包裹数量")
    private Integer packageCount;

    /**
     * 拒收数量
     */
    @ApiModelProperty(value = "拒收数量")
    private Integer rejectCount;
    @ApiModelProperty(value = "破损数量")
    private Integer damageCount;

    /**
     * 交接时间
     */
    @ApiModelProperty(value = "交接时间")
    private Long handoverTime;

    /**
     * 拓展字段
     */
    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "仓库")
    private String warehouseCode;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;
    @ApiModelProperty(value = "快递公司")
    private String carrierName;
}