package com.dt.domain.bill.dto;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/9/21 15:06
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Asn明细对象", description = "Asn明细")
public class AsnDetailDTO extends BaseDTO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;
    private String warehouseName;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "到货通知id")
    private String asnId;

    @ApiModelProperty(value = "上游行号")
    private String extNo;

    @ApiModelProperty(value = "客户端号")
    private String soNo;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal expSkuQty;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal recSkuQty;

    @ApiModelProperty(value = "体积")
    private BigDecimal volume;

    @ApiModelProperty(value = "毛重")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "总价")
    private BigDecimal price;

    @ApiModelProperty(value = "商品属性 字典组:SKU_STATUS")
    private String skuQuality;
    private String skuQualityName;

    @ApiModelProperty(value = "外部批次编码")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "批次编码")
    private String skuLotNo;

    @ApiModelProperty(value = "单位 字典组：PACKAGE_UNIT")
    private String packageUnitCode;
    private String packageUnitName;

    @ApiModelProperty(value = "ASN保持一致")
    private String status;
    private String statusName;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "长宽高 默认1位")
    private Integer lengthWidthHeightFormat;

    @ApiModelProperty(value = "体积 默认3位*")
    private Integer volumeFormat;

    @ApiModelProperty(value = "高 默认3位")
    private Integer weightFormat;

    @ApiModelProperty(value = "数量 默认0位")
    private Integer numberFormat;

    @ApiModelProperty(value = "明细标记")
    private Integer mark;

    @ApiModelProperty(value = "理货单号")
    private String relatedTallyOrder;

    @ApiModelProperty(value = "关联异常ID")
    private String relatedAbnormalId;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;
    @ApiModelProperty(value = "恒温仓")
    private String thermostatic;

    @ApiModelProperty("恒温策略")
    private String thermostaticStrategy;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    public Integer getExportCartonPcs() {
        if (!StringUtils.isEmpty(this.extraJson)) {
            JSONObject jsonObject = JSONUtil.parseObj(this.extraJson);
            if (jsonObject.containsKey("cartonPcs")) {
                return Integer.valueOf(jsonObject.getStr("cartonPcs"));
            }
        }
        return 0;
    }

    public String getExportUserName() {
        if (!StringUtils.isEmpty(this.extraJson)) {
            JSONObject jsonObject = JSONUtil.parseObj(this.extraJson);
            if (jsonObject.containsKey("exportUserName")) {
                return jsonObject.getStr("exportUserName");
            }
        }
        return "";
    }

    public Long getExportUserId() {
        if (!StringUtils.isEmpty(this.extraJson)) {
            JSONObject jsonObject = JSONUtil.parseObj(this.extraJson);
            if (jsonObject.containsKey("exportUserId")) {
                return Long.valueOf(jsonObject.getStr("exportUserId"));
            }
        }
        return null;
    }

}