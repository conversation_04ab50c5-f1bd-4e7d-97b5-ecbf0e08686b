package com.dt.domain.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/30 14:21
 */
@Data
public class CollectWaveAnalysisDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "货主是否淘天")
    Boolean cargoTaoTian;

    @ApiModelProperty(value = "是否预包")
    private String isPre;

    @ApiModelProperty(value = "快递网点编码")
    private String carrierCode;

    @ApiModelProperty(value = "平台")
    private String salePlatform;

    @ApiModelProperty(value = "正次品")
    private String skuQuality;

    @ApiModelProperty(value = "分析SKU")
    private String analysisSku;

    @ApiModelProperty(value = "分析SKU")
    private String spikeAnalysisSku;

    @ApiModelProperty(value = "快递网点编码")
    private String expressBranch;

    @ApiModelProperty(value = "包裹数量")
    private Long num;

    @ApiModelProperty(value = "包裹编码")
    private String packageCode;

    @ApiModelProperty(value = "明细")
    List<CollectWaveAnalysisSkuDTO> collectWaveAnalysisSkuDTOList;
}
