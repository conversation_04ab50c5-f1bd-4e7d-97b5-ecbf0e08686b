package com.dt.domain.bill.dto;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 移位管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Move对象", description="移位管理")
public class MoveDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "移位单号")
    private String code;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据状态 字典：MOVE_STATUS")
    private String status;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "计划品种数")
    private Integer expSkuType;

    @ApiModelProperty(value = "计划商品数")
    private BigDecimal expSkuQty;

    @ApiModelProperty(value = "实际品种数")
    private Integer actualSkuType;

    @ApiModelProperty(value = "数据商品数")
    private BigDecimal actualSkuQty;

    @ApiModelProperty(value = "操作方式 字典组：OP_TYPE")
    private String opType;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "操作日期")
    private Long completeDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "移位单列表")
    private List<MoveDetailDTO> detailList;

    @ApiModelProperty(value = "移位单列表")
    private List<MoveDetailDTO> removeDetailList;


}