package com.dt.domain.bill.dto;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/9/21 15:06
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "收货作业批次明细", description = "Receipt")
public class ReceiptDetailDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收货作业批次")
    private String recId;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "拓展行号/淘天理货异常行号")
    private String extNo;

    @ApiModelProperty(value = "明细行号")
    private Long pUid;

    @ApiModelProperty(value = "到货通知单号")
    private String asnId;

    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    @ApiModelProperty(value = "容器号")
    private String contCode;

    @ApiModelProperty(value = "入库单上游行号")
    private String lineSeq;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "总体积(m³)")
    private BigDecimal volume;

    @ApiModelProperty(value = "总毛重(KG)")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "总净重(KG)")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "批次ID 不允许为空，默认标准规则(批次规则档案)")
    private String skuLotNo;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;
    private String receiveDateFormat;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;
    private String manufDateFormat;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;
    private String expireDateFormat;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "禁售日期")
    private Long withdrawDate;
    private String withdrawDateFormat;

    @ApiModelProperty(value = "收货库位")
    private String locationCode;

    @ApiModelProperty(value = "收货上架目标库位")
    private String targetLocationCode;

    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

}