package com.dt.domain.bill.param.pkg;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 包裹分析查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("包裹分析查询参数")
public class PackDetainBillParam extends BaseSearchParam implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码列表")
    private List<String> cargoCodeList;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "销售平台")
    private String salePlatform;

    @ApiModelProperty(value = "付款时间开始")
    private Long payDateStart;

    @ApiModelProperty(value = "付款时间结束")
    private Long payDateEnd;

    @ApiModelProperty(value = "预计出库时间开始")
    private Long expOutStockDateStart;

    @ApiModelProperty(value = "预计出库时间结束")
    private Long expOutStockDateEnd;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    @ApiModelProperty(value = "创建时间开始")
    private Long createTimeStart;

    @ApiModelProperty(value = "创建时间结束")
    private Long createTimeEnd;

    @ApiModelProperty(value = "包裹状态")
    private String status;

    /**
     * {@link com.dt.domain.bill.dto.PackageDetainDTO}
     */
    @ApiModelProperty(value = "滞留时长字段")
    private String detainFiled;
}
