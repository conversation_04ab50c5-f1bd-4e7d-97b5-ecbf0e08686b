package com.dt.domain.bill.param;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_package")
@ApiModel(value="Package对象", description="包裹")
public class PackageParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    private String warehouseCode;

    @ApiModelProperty(value = "包裹号")
    private List<String> packageCodeList;

    private String packageCode;

    /**包裹结构*/
    private String packageStruct;
    private List<String> packageStructList;
    @ApiModelProperty(value = "交易单号")
    private String tradeNo;
    /**客户单号*/
    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    /**上游单号*/
    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;
    private List<String> soNoList;

    @ApiModelProperty(value = "拣选单号")
    private String pickCode;
    @ApiModelProperty(value = "拣选单号")
    private List<String> pickCodeList;

    private String waveCode;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;
    @ApiModelProperty(value = "出库单号,多个")
    private List<String> shipmentOrderCodeList;
    @ApiModelProperty(value = "客户端号")
    private List<String> poNoList;

    @ApiModelProperty(value = "货主编码")
    private List<String> cargoCodeList;
    private String cargoCode;

    @ApiModelProperty(value = "包裹状态")
    private List<String> statusList;
    private String status;

    @ApiModelProperty(value = "汇单包裹状态码")
    private String collectStatus;

    @ApiModelProperty(value = "单据类型")
    private String orderType;

    @ApiModelProperty(value = "交易单号")
    private List<String> tradeNoList;

    @ApiModelProperty(value = "商品代码")
    private List<String> skuCodeList;

    private List<String> skuCodeLikeList;

    @ApiModelProperty(value = "商品条码")
    private List<String> upcCodeList;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;
    private List<String> carrierCodeList;

    @ApiModelProperty(value = "快递单号")
    private List<String> expressNoList;
    private String expressNo;


    @ApiModelProperty(value = "销售平台")
    private String salePlatform;
    private String salePlatformT;
    private List<String> salePlatformList;

    private List<String> notOtherSalePlatformCodeList;

    @ApiModelProperty(value = "销售店铺")
    private String saleShop;
    private List<String> saleShopList;


    @ApiModelProperty(value = "订单品种类型")
    private List<String> orderSkuTypeList;

   /* @ApiModelProperty(value = "创建开始时间")
    private Long startCreateTime;

    @ApiModelProperty(value = "创建结束时间")
    private Long endCreateTime;*/

    @ApiModelProperty(value = "理论重量")
    private BigDecimal weight;

    @ApiModelProperty(value = "实际重量")
    private BigDecimal realWeight;

    @ApiModelProperty(value = "体积(cm³)")
    private BigDecimal volume;

    @ApiModelProperty(value = "出库开始时间")
    private Long startOutStockDate;

    @ApiModelProperty(value = "出库结束时间")
    private Long endOutStockDate;

    @ApiModelProperty(value = "商品正/次品属性")
    private String skuQuality;

    @ApiModelProperty(value = "预计出库时间开始时间")
    private Long startExpOutStockDate;

    @ApiModelProperty(value = "预计出库时间结束时间")
    private Long endExpOutStockDate;

    @ApiModelProperty(value = "收货人姓名")
    private String receiverMan;
    @ApiModelProperty(value = "联系电话")
    private String receiverTel;
    @ApiModelProperty(value = "收货省份")
    private String receiverProv;
    private String receiverProvName;
    @ApiModelProperty(value = "收货市")
    private String receiverCity;
    private String receiverCityName;
    @ApiModelProperty(value = "收货区")
    private String receiverArea;
    private String receiverAreaName;

    //省
    private List<String> receiverProvList;
    //市
    private List<String> receiverCityList;
    //区
    private List<String> receiverAreaList;

    @ApiModelProperty(value = "是否预包包裹 1:普通包裹 2:预包包裹  枚举：PackEnum.TYPE")
    private String isPre;

    @ApiModelProperty(value = "订单标记")
    private Integer orderTag;
    private List<Integer> orderTagList;

    @ApiModelProperty(value = "订单标记仅用于查询不包含订单标记")
    private Integer noContainOrderTag;

    @ApiModelProperty(value = "拓传json")
    private String extraJson;
//
//    @ApiModelProperty(value = "包裹号")
//    private String packageCode;
//
//    @ApiModelProperty(value = "出库单号")
//    private String shipmentOrderCode;
//
//    @ApiModelProperty(value = "客户端号")
//    private String poNo;
//
//    @ApiModelProperty(value = "上游单号")
//    private String soNo;
//
//    @ApiModelProperty(value = "业务类型")
//    private String businessType;
//
//    @ApiModelProperty(value = "货主编码")
//    private String cargoCode;
//
//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;
//
//    @ApiModelProperty(value = "包裹状态")
//    private String status;
//
//    @ApiModelProperty(value = "快递公司编码")
//    private String carrierCode;

    private String carrierName;

//    @ApiModelProperty(value = "快递单号")
//    private String expressNo;

    @ApiModelProperty(value = "销售店铺ID")
    private String saleShopId;
    private List<String> saleShopIdList;

//    @ApiModelProperty(value = "销售平台")
//    private String salePlatform;
//
//    @ApiModelProperty(value = "包裹结构struct")
//    private String packageStruct;
//    private List<String> packageStructList;

    @ApiModelProperty(value = "拣货开始时间")
    private Long pickSkuDate;

    @ApiModelProperty(value = "拣货完成时间")
    private Long pickCompleteSkuDate;

    @ApiModelProperty(value = "复核开始时间")
    private Long checkStartDate;

    @ApiModelProperty(value = "复核完成时间")
    private Long checkCompleteDate;

    @ApiModelProperty(value = "出库时间")
    private Long outStockDate;
    private Long outStockDateEqStart;
    private Long outStockDateEqEnd;

    @ApiModelProperty(value = "拦截取消时间")
    private Long interceptCancelDate;

    @ApiModelProperty(value = "商品品种数")
    private Integer skuTypeQty;

    @ApiModelProperty(value = "包裹商品数量")
    private BigDecimal packageSkuQty;

    @ApiModelProperty(value = "出库商品数量")
    private BigDecimal outSkuQty;

    @ApiModelProperty(value = "推荐包材条码")
    private String recPackUpc;

    @ApiModelProperty(value = "实际使用包材条码")
    private String actualPackUpc;

    @ApiModelProperty(value = "包材重量")
    private BigDecimal actualPackWeight;

    @ApiModelProperty(value = "包材数量")
    private Integer actualPackNum;

    @ApiModelProperty(value = "操作人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "分析包裹明细结构")
    private String analysisSku;
    private List<String> analysisSkuList;

    @ApiModelProperty(value = "秒杀标识-使用MD5值(skuCode#skuLotNo#qty)拼接")
    private String spikeAnalysisSku;
    private String spikeAnalysisSkuNotBlank;

//    @ApiModelProperty(value = "波次汇单波次号")
//    private String waveCode;
//
//    @ApiModelProperty(value = "包裹编码列表")
//    private List<String> packageCodeList;
    private List<String> noPackageCodeList;
//
//    @ApiModelProperty(value = "状态列表")
//    private List<String> statusList;

    @ApiModelProperty(value = "日汇总页面查询")
    private Boolean summarySelect;

    @ApiModelProperty(value = "包材")
    private String materialUpcCode;

    @ApiModelProperty(value = "查询特定字段")
    private Boolean selectColumn = false;

    @ApiModelProperty(value = "预处理状态")
    private String pretreatmentStatus;
    private List<String> pretreatmentStatusList;

    @ApiModelProperty(value = "预处理更新锁定状态字段")
    private String pretreatmentLockStatus;

    @ApiModelProperty(value = "订单创建开始时间")
    @Getter
    @Setter
    private Long ordCreatedTimeStart;

    @ApiModelProperty(value = "订单创建结束时间")
    @Getter
    @Setter
    private Long ordCreatedTimeEnd;

    @ApiModelProperty(value = "预售类型")
    private Integer preSaleType;

    @ApiModelProperty(value = "发货超时时间")
    private Long expShipTimeStart;
    private Long expShipTimeEnd;

    @ApiModelProperty(value = "快递网点编码")
    private String expressBranch;
    private String expressBranchNotBlank;
    private List<String> expressBranchList;

    @ApiModelProperty(value = "快递网点编码名称")
    private String expressBranchName;

    @ApiModelProperty(value = "快递账号")
    private String expressAccount;
    private List<String> expressAccountList;


    private List<String> tableFieldList;
    private Long expShipTimeEmpty;

    @ApiModelProperty(value = "源包裹号")
    private String originPackageCode;

    @ApiModelProperty(value = "限制当前数量")
    private Long limitNum;

    @ApiModelProperty(value = "复核完成查询条件")
    private Long checkCompleteDateStart;
    private Long checkCompleteDateEnd;
    @ApiModelProperty(value = "拣选完成查询条件")
    private Long pickCompleteSkuDateStart;
    private Long pickCompleteSkuDateEnd;

    //----
    @ApiModelProperty(value = "品种数(开始)")
    private Integer startSkuTypeCount;

    @ApiModelProperty(value = "品种数(结束)")
    private Integer endSkuTypeCount;

    @ApiModelProperty(value = "商品数(开始)")
    private Integer startSkuCount;

    @ApiModelProperty(value = "商品数(结束)")
    private Integer endSkuCount;

    @ApiModelProperty(value = "理论重量")
    private BigDecimal weightStart;
    private BigDecimal weightEnd;

    @ApiModelProperty(value = "付款时间(开始)")
    private Long startPayTime;

    @ApiModelProperty(value = "付款时间(结束)")
    private Long endPayTime;
    //----
}