package com.dt.domain.bill.dto;

import com.dt.component.common.enums.pkg.PackEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value="PackageDetail对象", description="包裹管理")
public class PackageDetainDTO   implements java.io.Serializable  {
    public PackageDetainDTO() {
    }

    public PackageDetainDTO(String status) {
        this.status = status;
        this.statusDesc = PackEnum.STATUS.findEnumDesc(status).getDesc();
    }

    @ApiModelProperty("包裹状态")
    private String status;
    @ApiModelProperty("包裹状态")
    private String statusDesc;
    @ApiModelProperty("总量汇总")
    private BigDecimal total = BigDecimal.ZERO;
    @ApiModelProperty("小于30分钟")
    private BigDecimal lt30 = BigDecimal.ZERO;
    @ApiModelProperty("0.5-1小时")
    private BigDecimal lt60 = BigDecimal.ZERO;
    @ApiModelProperty("1-1.5小时")
    private BigDecimal lt90 = BigDecimal.ZERO;
    @ApiModelProperty("1.5-2小时")
    private BigDecimal lt120 = BigDecimal.ZERO;
    @ApiModelProperty("2-2.5小时")
    private BigDecimal lt150 = BigDecimal.ZERO;
    @ApiModelProperty("2.5-3小时")
    private BigDecimal lt180 = BigDecimal.ZERO;
    @ApiModelProperty("3-3.5小时")
    private BigDecimal lt210 = BigDecimal.ZERO;
    @ApiModelProperty("3.5-4小时")
    private BigDecimal lt240 = BigDecimal.ZERO;
    @ApiModelProperty("4-4.5小时")
    private BigDecimal lt270 = BigDecimal.ZERO;
    @ApiModelProperty("4.5-5小时")
    private BigDecimal lt300 = BigDecimal.ZERO;
    @ApiModelProperty("5-6小时")
    private BigDecimal lt360 = BigDecimal.ZERO;
    @ApiModelProperty("6-7小时")
    private BigDecimal lt420 = BigDecimal.ZERO;
    @ApiModelProperty("7-8小时")
    private BigDecimal lt480 = BigDecimal.ZERO;
    @ApiModelProperty("8-12小时")
    private BigDecimal lt720 = BigDecimal.ZERO;
    @ApiModelProperty("12-24小时")
    private BigDecimal lt1440 = BigDecimal.ZERO;
    @ApiModelProperty("24-48小时")
    private BigDecimal lt2880 = BigDecimal.ZERO;
    @ApiModelProperty("大于48小时")
    private BigDecimal gt2880 = BigDecimal.ZERO;


}