package com.dt.domain.bill.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "包裹滞留查询参数", description = "包裹滞留查询参数")
public class PackageDetainParam implements java.io.Serializable  {
    @ApiModelProperty(value = "创建开始时间")
    private Long createdTimeStart;
    @ApiModelProperty(value = "创建结束时间")
    private Long createdTimeEnd;
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    @ApiModelProperty(value = "货主编码")
    private List<String> cargoCodeList;
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    @ApiModelProperty(value = "销售平台")
    private String salePlatform;
    @ApiModelProperty(value = "快递公司")
    private String carrierCode;
    @ApiModelProperty(value = "预计出库时间开始时间")
    private Long expOutStockDateStart;
    @ApiModelProperty(value = "预计出库时间结束时间")
    private Long expOutStockDateEnd;
    @ApiModelProperty(value = "付款时间")
    private Long payDateStart;
    @ApiModelProperty(value = "付款时间")
    private Long payDateEnd;
}