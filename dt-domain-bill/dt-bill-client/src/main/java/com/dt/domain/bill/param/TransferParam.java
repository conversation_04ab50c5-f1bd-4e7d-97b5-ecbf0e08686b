package com.dt.domain.bill.param;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by nobody on 2020/12/28 17:48
 */
@Data
@ApiModel(value="TransferParam", description="库存转移")
public class TransferParam extends BaseSearchParam  implements java.io.Serializable  {

    @ApiModelProperty(value = "转移单编码")
    private String code;
    private List<String> codeList;

    @ApiModelProperty(value = "单据状态 字典组:TRANSFER_STATUS")
    private String status;
    private List<String> statusList;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    @ApiModelProperty(value = "来源商品批次")
    private String originSkuLotNo;

    @ApiModelProperty(value = "来源商品编码")
    private List<String> originSkuCodeList;

    @ApiModelProperty(value = "来源商品条码")
    private List<String> originUpcCodeList;

    @ApiModelProperty(value = "来源库区编码")
    private String originZoneCode;

    @ApiModelProperty(value = "来源库位编码")
    private String originLocationCode;
    private List<String> originLocationCodeList;

    @ApiModelProperty(value = "目标商品编码")
    private String targetSkuCode;
    private List<String> targetSkuCodeList;

    @ApiModelProperty(value = "目标商品条码")
    private String targetUpcCode;
    private List<String> targetUpcCodeList;

    @ApiModelProperty(value = "目标库区编码")
    private String targetZoneCode;

    @ApiModelProperty(value = "目标库位编码")
    private String targetLocationCode;
    private List<String> targetLocationCodeList;

    @ApiModelProperty(value = "创建时间start")
    private Long createDateStart;

    @ApiModelProperty(value = "创建时间end")
    private Long createDateEnd;

    @ApiModelProperty(value = "完成时间start")
    private Long completeDateStart;

    @ApiModelProperty(value = "完成时间end")
    private Long completeDateEnd;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "描述")
    private String note;

    @ApiModelProperty(value = "业务场景")
    private String businessType;
    private List<String> businessTypeList;



    @ApiModelProperty(value = "订单标记")
    private Integer orderTag;
    private List<Integer> orderTagList;
}