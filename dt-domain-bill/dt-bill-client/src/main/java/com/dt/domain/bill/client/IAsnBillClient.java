package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.*;
import com.dt.domain.bill.bo.asn.AsnZeroReceiveBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.param.AsnModifyParam;
import com.dt.domain.bill.param.AsnParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/21 14:58
 */
public interface IAsnBillClient {

    @ApiOperation("取消收货")
    Result<Boolean> cancelAsn(AsnCancelBO asnCancelBO);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(AsnParam param);

    /**
     * 获取Asn信息
     *
     * @param param
     * @return
     */
    Result<AsnDTO> get(AsnParam param);

    /**
     * 获取Asn列表
     *
     * @param param
     * @return
     */
    Result<List<AsnDTO>> getList(AsnParam param);

    /**
     * 功能描述:  获取出库单日志
     * 创建时间:  2021/11/9 11:31 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.domain.bill.dto.AsnLogDTO>>
     * <AUTHOR>
     */
    Result<List<AsnLogDTO>> getAsnLogList(AsnParam param);

    /**
     * 分页获取Asn
     *
     * @param param
     * @return
     */
    Result<Page<AsnDTO>> getPage(AsnParam param);


    /**
     * 功能描述:  根据主键Id修改
     * 创建时间:  2022/1/10 4:10 下午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> updateById(AsnDTO asnDTO);

    /**
     * ASN修改状态码
     *
     * @param param
     * @return
     */
    Result<Boolean> modifyStatus(AsnModifyParam param);

    /**
     * @param asnId
     * @param recIds
     * @param notifyTime
     * @param notifyStatus
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description: 回传状态码改变
     * create time: 2022/3/29 9:46
     */
    Result<Boolean> modifyNotifyStatus(String asnId, List<String> recIds, Long notifyTime, Integer notifyStatus);

    /**
     * ASN明细
     *
     * @param param
     * @return
     */
    Result<AsnDetailDataDTO> queryAsnDetail(AsnParam param);

    /**
     * 下发ASN
     *
     * @param dataDTO
     * @return
     */
    Result<Boolean> receiptAsn(AsnReceiptDataDTO dataDTO);

    /**
     * 检查上游到货通知单号
     *
     * @param param
     * @return
     */
    Result<Boolean> checkSoNoExistsByCargoCode(AsnParam param);

    /**
     * 上有参数查询ASN
     *
     * @param asnParam
     * @return
     */
    Result<AsnDTO> queryAsnBySoNo(AsnParam asnParam);

    /**
     * 收货作业批次修改ASN明细
     *
     * @param asnDTO
     * @return
     */
    Result<Boolean> commitAsn(AsnDTO asnDTO);

    /**
     * 更新打印次数
     *
     * @param asnParam
     * @return
     */
    public Result<Integer> updateAsnPrintNum(AsnParam asnParam);

    public Result<Integer> updateRecPrintNum(AsnParam asnParam);

    /**
     * 插入ASN日志
     *
     * @param asnLogDTO
     * @return
     */
    Result<Boolean> saveAsnLog(AsnLogDTO asnLogDTO);

    /**
     * 明细分页
     *
     * @param param
     * @return
     */
    Result<Page<AsnDetailDTO>> queryDetailPage(AsnParam param);

    /**
     * @param param
     * @return
     */
    Result<List<AsnDetailDTO>> getDetailList(AsnParam param);

    /**
     * 明细分组分页
     *
     * @param param
     * @return
     */
    Result<Page<AsnDetailDTO>> getPageGroupDetail(AsnParam param);

    /**
     * 批量修改
     *
     * @param asnCompleteBO
     * @return
     */
    Result<Boolean> modifyBatch(AsnBillCompleteBO asnCompleteBO);

    /**
     * 操作日志分页查询
     */
    Result<Page<AsnLogDTO>> queryLogPage(AsnParam asnParam);

    /**
     * @param asnDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2021/7/15 16:32
     */
    Result<Boolean> save(AsnDTO asnDTO);

    /**
     * @param asnDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2021/7/15 16:32
     */
    Result<Boolean> modifyAndRomve(AsnDTO asnDTO);

    /**
     * @param asnBillCancelBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2021/7/16 14:40
     */
    Result<Boolean> cancelBatch(AsnBillCancelBO asnBillCancelBO);

    /**
     * @param asnBillImportBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2021/7/20 15:28
     */
    Result<Boolean> saveImportBatch(AsnBillImportBO asnBillImportBO);

    Result<Boolean> modify(AsnDTO asnDTO);

    /**
     * @param asnBatchArrivalBO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2022/3/11 18:02
     */
    Result<Boolean> modifyArrivalBatch(AsnBatchArrivalBO asnBatchArrivalBO);

    /**
     * @param asnDTOList
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2022/3/16 14:21
     */
    Result<Boolean> modifyBatchAsnDTO(List<AsnDTO> asnDTOList);

    /**
     * @param asnLogDTOList
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2022/3/16 14:21
     */
    Result<Boolean> saveAsnLogBatch(List<AsnLogDTO> asnLogDTOList);

    /**
     * @param asnParam
     * @param tableFields
     * @return java.util.List<com.dt.domain.bill.dto.AsnDTO>
     * @author: WuXian
     * description: 获取指定字段
     * create time: 2022/3/29 9:32
     */
    Result<List<AsnDTO>> getAppointMultipleParam(AsnParam asnParam, List<String> tableFields);

    /**
     * @param asnId
     * @param recIds
     * @param notifyTime
     * @param notifyStatus
     * @return java.lang.Boolean
     * @author: WuXian
     * description: 退货入库回传状态码修改
     * create time: 2022/3/29 9:49
     */
    Result<Boolean> modifyReturnNotifyStatus(String asnId, List<String> recIds, Long notifyTime, Integer notifyStatus);
    /**
     * @author: WuXian
     * description:
     * create time: 2022/4/22 17:05
     *
     * @param asnId
     * @param extraRecIdList
     * @param currentTimeMillis
     * @param notifyStatus
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     */

    Result<Boolean> modifyExtraNotifyStatus(String asnId, List<String> extraRecIdList, long currentTimeMillis, Integer notifyStatus);
    /**
     * @author: WuXian
     * description:
     * create time: 2022/4/24 10:05
     *
     * @param asnId
     * @param recIdList
     * @param backFlag
     * @param notifyTime
     * @param notifyStatus
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     */
    Result<Boolean> modifyNotifyPartStatus(String asnId, List<String> recIdList, String backFlag, long notifyTime, Integer notifyStatus);
    /**
     * @param asnDTO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe:
     * @date 2022/9/20 16:12
     */
    Result<Boolean> commitBoxDataAsn(AsnDTO asnDTO);

    Result<Boolean> modifyAndAddMessage(AsnDTO asnDTO);
    /**
     * @param asnLinkModifyBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/7/26 14:36
     */
    Result<Boolean> modifyLinkAsn(AsnLinkModifyBO asnLinkModifyBO);

    Result<Page<AsnDetailDTO>> getPageGroupDetailByReTurn(AsnParam param);

    Result<Page<AsnDetailDTO>> getSkuGroupPage(AsnParam asnParam);
    /**
     * @param asnZeroReceiveBO
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @describe: 零收货
     * @date 2025/2/13 9:35
     */
    Result<String> zeroReceive(AsnZeroReceiveBO asnZeroReceiveBO);
    /**
     * @param asnDTO
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @describe:
     * @date 2025/2/13 13:27
     */
    Result<String> modifyAsnByZeroReceive(AsnDTO asnDTO);
}
