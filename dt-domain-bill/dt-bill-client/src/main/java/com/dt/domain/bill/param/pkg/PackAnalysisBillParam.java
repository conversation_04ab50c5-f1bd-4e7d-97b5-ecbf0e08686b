package com.dt.domain.bill.param.pkg;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 包裹分析查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("包裹分析查询参数")
public class PackAnalysisBillParam extends BaseSearchParam implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码列表")
    private List<String> cargoCodeList;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "包裹类型")
    private String isPre;

    @ApiModelProperty(value = "销售平台")
    private String salePlatform;

    @ApiModelProperty(value = "创建时间开始")
    private Long createTimeStart;

    @ApiModelProperty(value = "创建时间结束")
    private Long createTimeEnd;

    @ApiModelProperty(value = "付款时间开始")
    private Long payDateStart;

    @ApiModelProperty(value = "付款时间结束")
    private Long payDateEnd;

    @ApiModelProperty(value = "预计出库时间开始")
    private Long expOutStockDateStart;

    @ApiModelProperty(value = "预计出库时间结束")
    private Long expOutStockDateEnd;

    @ApiModelProperty(value = "出库时间开始")
    private Long outStockDateStart;

    @ApiModelProperty(value = "出库时间结束")
    private Long outStockDateEnd;

    @ApiModelProperty(value = "拣选结束开始")
    private Long pickCompleteDateStart;

    @ApiModelProperty(value = "拣选结束结束")
    private Long pickCompleteDateEnd;

    @ApiModelProperty(value = "复核结束时间开始")
    private Long checkCompleteDateStart;

    @ApiModelProperty(value = "复核结束时间结束")
    private Long checkCompleteDateEnd;

    @ApiModelProperty(value = "订单品种类型列表")
    private List<String> packageStructList;

    @ApiModelProperty(value = "品种数最小值")
    private Integer skuTypeQtyMin;

    @ApiModelProperty(value = "品种数最大值")
    private Integer skuTypeQtyMax;

    @ApiModelProperty(value = "商品数最小值")
    private Integer skuQtyMin;

    @ApiModelProperty(value = "商品数最大值")
    private Integer skuQtyMax;

    @ApiModelProperty(value = "理论重量最小值")
    private Integer weightMin;

    @ApiModelProperty(value = "理论重量最大值")
    private Integer weightMax;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    @ApiModelProperty(value = "收货省份")
    private List<String> receiverProvNameList;

    @ApiModelProperty(value = "收货城市")
    private List<String> receiverCityNameList;

    @ApiModelProperty(value = "收货区县")
    private List<String> receiverAreaNameList;

    //省
    private List<String> receiverProvList;
    //市
    private List<String> receiverCityList;
    //区
    private List<String> receiverAreaList;

    @ApiModelProperty(value = "分析维度列表，可选值: cargoCode, businessType, salePlatform, packageStruct, is_pre" +
            "expOutStockDate_day, expOutStockDate_hour, createTime_day, createTime_hour, " +
            "payDate_day, payDate_hour, outStockDate_day, outStockDate_hour, " +
            "pickCompleteSkuDate_day, pickCompleteSkuDate_hour, checkCompleteDate_day, checkCompleteDate_hour, " +
            "carrierCode, receiverProvName, receiverCityName, receiverAreaName")
    private List<String> analysisDimensions;

    @ApiModelProperty(value = "排序字段，默认为orderCount（订单总数）")
    private String sortField;
    private String field;

    @ApiModelProperty(value = "排序方式，ASC升序，DESC降序，默认为DESC")
    private String sortOrder;
    private String order;

    //----------------------------点击查看数据-------------------------

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "订单品种类型")
    private String packageStruct;

    @ApiModelProperty(value = "预计出库时间（天）")
    private String expOutStockDate_day;

    @ApiModelProperty(value = "预计出库时间（小时）")
    private String expOutStockDate_hour;

    @ApiModelProperty(value = "拣选完成时间（天）")
    private String pickCompleteSkuDate_day;

    @ApiModelProperty(value = "拣选完成时间（小时）")
    private String pickCompleteSkuDate_hour;

    @ApiModelProperty(value = "复核完成时间（天）")
    private String checkCompleteDate_day;

    @ApiModelProperty(value = "复核完成时间（小时）")
    private String checkCompleteDate_hour;

    @ApiModelProperty(value = "创建时间（天）")
    private String createdTime_day;

    @ApiModelProperty(value = "创建时间（小时）")
    private String createdTime_hour;

    @ApiModelProperty(value = "付款时间（天）")
    private String payDate_day;

    @ApiModelProperty(value = "付款时间（小时）")
    private String payDate_hour;

    @ApiModelProperty(value = "出库时间（天）")
    private String outStockDate_day;

    @ApiModelProperty(value = "出库时间（小时）")
    private String outStockDate_hour;

    @ApiModelProperty(value = "省份")
    private String receiverProvName;

    @ApiModelProperty(value = "城市")
    private String receiverCityName;

    @ApiModelProperty(value = "区/县")
    private String receiverAreaName;

    @ApiModelProperty(value = "列key")
    private String columnKey;

    /**
     {
     "createdOrderCount": "创建状态订单数",
     "orderCount": "订单总数",
     "pretreatmentFailOrderCount": "预处理失败订单数",
     "pretreatmentCompleteOrderCount": "预处理完成订单数",
     "collectedFailOrderCount": "汇单失败订单数",
     "collectedOrderCount": "已汇总订单数",
     "pickStartOrderCount": "拣选开始订单数",
     "pickEndOrderCount": "拣选完成订单数",
     "checkStartOrderCount": "复核开始订单数",
     "checkCompleteOrderCount": "复核完成订单数",
     "partialOutOrderCount": "部分出库订单数",
     "outOrderCount": "已出库订单数",
     "interceptOrderCount": "拦截订单数",
     "cancelOrderCount": "取消订单数",
     "shortageOutOrderCount": "缺货出库订单数",
     "interceptCancelOrderCount": "拦截取消订单数"

     }
     */

    //----------------------------点击查看数据-------------------------
}
