package com.dt.domain.bill.dto.rs;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 多货登记详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SalesReturnExtraDetail对象", description = "多货登记详情")
public class SalesReturnExtraDetailDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 多货单号
     */
    @ApiModelProperty(value = "多货单号")
    private String salesReturnExtraOrderNo;

    /**
     * 商品条码
     */
    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 货主名称
     */
    @ApiModelProperty(value = "货主名称")
    private String cargoName;

    /**
     * 实收正品数量
     */
    @ApiModelProperty(value = "实收正品数量")
    private BigDecimal avlQty;

    /**
     * 实收次品数量
     */
    @ApiModelProperty(value = "实收次品数量")
    private BigDecimal damageQty;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "仓库")
    private String warehouseCode;

    @ApiModelProperty(value = "工作台")
    private String workbenchCode;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal qty;
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;
}