package com.dt.domain.bill.dto.rs;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class SalesReturnOrderExtraDTO implements Serializable {
    @ApiModelProperty("抖音超市仓编码")
    private String dyMarketWarehouseCode;
    @ApiModelProperty("质检操作人")
    private String inspectionBy;
    private List<SalesReturnOrderNodeDTO> nodes;    
    private List<String> noListOutAtSameTime;
    private String orderType;
    private String senderInfo;
    private String extendProps;
    private String preDeliveryOrderCode;
    private String instructionType;
    private String highRiskCustom;
    private String highRiskPackage;
    private String fulfilOutBizCode;
    private String rejectReason;
    private List<String> rejectImageList;
    private String shipmentCargoCode;
    private String cancleReason;
    private String hold;
    @ApiModelProperty("部分退")
    private boolean partPass = false;
    
    public static SalesReturnOrderExtraDTO fromJson(String json) {
        if (StrUtil.isBlank(json)) return new SalesReturnOrderExtraDTO();
        return JSONUtil.toBean(json, SalesReturnOrderExtraDTO.class);
    }
    
    public String toJson() {
        return JSONUtil.toJsonStr(this);
    }
}
