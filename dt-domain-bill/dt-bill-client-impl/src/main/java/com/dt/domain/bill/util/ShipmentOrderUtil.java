package com.dt.domain.bill.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.domain.bill.shipment.entity.ShipmentOrder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Component
public class ShipmentOrderUtil extends QueryWrapper<ShipmentOrder, ShipmentOrderParam> {

    public LambdaQueryWrapper<ShipmentOrder> getQueryWrapper(String shipmentOrderCode) {
        ShipmentOrderParam param = new ShipmentOrderParam();
        param.setShipmentOrderCode(shipmentOrderCode);
        return this.getQueryWrapper(param);
    }

    @Override
    public LambdaQueryWrapper<ShipmentOrder> getQueryWrapper(ShipmentOrderParam param) {
        LambdaQueryWrapper<ShipmentOrder> lambdaQueryWrapper = super.getQueryWrapper(param);
        lambdaQueryWrapper
                .eq(!ObjectUtils.isEmpty(param.getShipmentOrderCode()), ShipmentOrder::getShipmentOrderCode, param.getShipmentOrderCode())
                .eq(!ObjectUtils.isEmpty(param.getPoNo()), ShipmentOrder::getPoNo, param.getPoNo())
                .eq(!ObjectUtils.isEmpty(param.getFromSource()), ShipmentOrder::getFromSource, param.getFromSource())

                .eq(!ObjectUtils.isEmpty(param.getGlobalNo()), ShipmentOrder::getGlobalNo, param.getGlobalNo())
                .in(!ObjectUtils.isEmpty(param.getGlobalNoList()), ShipmentOrder::getGlobalNo, param.getGlobalNoList())

                .eq(!ObjectUtils.isEmpty(param.getSoNo()), ShipmentOrder::getSoNo, param.getSoNo())
                .in(!ObjectUtils.isEmpty(param.getSoNoList()), ShipmentOrder::getSoNo, param.getSoNoList())
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), ShipmentOrder::getWarehouseCode, param.getWarehouseCode())
                .in(!ObjectUtils.isEmpty(param.getPoNoList()), ShipmentOrder::getPoNo, param.getPoNoList())
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), ShipmentOrder::getCargoCode, param.getCargoCode())
                .in(!ObjectUtils.isEmpty(param.getCargoCodeList()), ShipmentOrder::getCargoCode, param.getCargoCodeList())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), ShipmentOrder::getStatus, param.getStatus())
                .in(!ObjectUtils.isEmpty(param.getStatusList()), ShipmentOrder::getStatus, param.getStatusList())
                .eq(!ObjectUtils.isEmpty(param.getPretreatmentStatus()), ShipmentOrder::getPretreatmentStatus, param.getPretreatmentStatus())
                .eq(!ObjectUtils.isEmpty(param.getOrderType()), ShipmentOrder::getOrderType, param.getOrderType())
                .eq(!ObjectUtils.isEmpty(param.getBusinessType()), ShipmentOrder::getBusinessType, param.getBusinessType())
                .eq(!ObjectUtils.isEmpty(param.getCarrierCode()), ShipmentOrder::getCarrierCode, param.getCarrierCode())
                .in(!ObjectUtils.isEmpty(param.getCarrierCodeList()), ShipmentOrder::getCarrierCode, param.getCarrierCodeList())

                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), ShipmentOrder::getExpressNo, param.getExpressNo())
                .in(!ObjectUtils.isEmpty(param.getExpressNoList()), ShipmentOrder::getExpressNo, param.getExpressNoList())
                .eq(!ObjectUtils.isEmpty(param.getTradeNo()), ShipmentOrder::getTradeNo, param.getTradeNo())
                .in(!ObjectUtils.isEmpty(param.getTradeNoList()), ShipmentOrder::getTradeNo, param.getTradeNoList())
                .eq(!ObjectUtils.isEmpty(param.getSalePlatform()), ShipmentOrder::getSalePlatform, param.getSalePlatform())
                .eq(!ObjectUtils.isEmpty(param.getSaleShop()), ShipmentOrder::getSaleShop, param.getSaleShop())
                .eq(!ObjectUtils.isEmpty(param.getPackageStruct()), ShipmentOrder::getPackageStruct, param.getPackageStruct())
                .in(!ObjectUtils.isEmpty(param.getPackageStructList()), ShipmentOrder::getPackageStruct, param.getPackageStructList())
                // 出库单回传状态
                .eq(!ObjectUtils.isEmpty(param.getNotifyStatus()), ShipmentOrder::getNotifyStatus, param.getNotifyStatus())
                .eq(!ObjectUtils.isEmpty(param.getReceiverMan()), ShipmentOrder::getReceiverMan, param.getReceiverMan())
                .eq(!ObjectUtils.isEmpty(param.getReceiverTel()), ShipmentOrder::getReceiverTel, param.getReceiverTel())
                .eq(!ObjectUtils.isEmpty(param.getReceiverProv()), ShipmentOrder::getReceiverProv, param.getReceiverProv())
                .in(!ObjectUtils.isEmpty(param.getReceiverProvList()), ShipmentOrder::getReceiverProv, param.getReceiverProvList())
                .eq(!ObjectUtils.isEmpty(param.getReceiverCity()), ShipmentOrder::getReceiverCity, param.getReceiverCity())

                .eq(!ObjectUtils.isEmpty(param.getReceiverProvName()), ShipmentOrder::getReceiverProvName, param.getReceiverProvName())
                .eq(!ObjectUtils.isEmpty(param.getReceiverCityName()), ShipmentOrder::getReceiverCityName, param.getReceiverCityName())
                .eq(!ObjectUtils.isEmpty(param.getReceiverAreaName()), ShipmentOrder::getReceiverAreaName, param.getReceiverAreaName())

                .in(!ObjectUtils.isEmpty(param.getReceiverCityList()), ShipmentOrder::getReceiverCity, param.getReceiverCityList())
                .eq(!ObjectUtils.isEmpty(param.getReceiverArea()), ShipmentOrder::getReceiverArea, param.getReceiverArea())
                .in(!ObjectUtils.isEmpty(param.getReceiverAreaList()), ShipmentOrder::getReceiverArea, param.getReceiverAreaList())
                .in(!CollectionUtils.isEmpty(param.getShipmentOrderCodeList()), ShipmentOrder::getShipmentOrderCode, param.getShipmentOrderCodeList())
                .in(!ObjectUtils.isEmpty(param.getPretreatmentStatusList()), ShipmentOrder::getPretreatmentStatus, param.getPretreatmentStatusList())
                .in(!ObjectUtils.isEmpty(param.getOrderSkuType()), ShipmentOrder::getPackageStruct, param.getOrderSkuType())
                .ge(!ObjectUtils.isEmpty(param.getStartExpOutStockDate()), ShipmentOrder::getExpOutStockDate, param.getStartExpOutStockDate())
                .le(!ObjectUtils.isEmpty(param.getEndExpOutStockDate()), ShipmentOrder::getExpOutStockDate, param.getEndExpOutStockDate())
                .ge(!ObjectUtils.isEmpty(param.getStartOutStockDate()), ShipmentOrder::getOutStockDate, param.getStartOutStockDate())
                .le(!ObjectUtils.isEmpty(param.getEndOutStockDate()), ShipmentOrder::getOutStockDate, param.getEndOutStockDate())
                .ge(!ObjectUtils.isEmpty(param.getStartPayTime()), ShipmentOrder::getPayDate, param.getStartPayTime())
                .le(!ObjectUtils.isEmpty(param.getEndPayTime()), ShipmentOrder::getPayDate, param.getEndPayTime())
                .ge(!ObjectUtils.isEmpty(param.getStartTradeOrderDate()), ShipmentOrder::getPlaceTradeOrderDate, param.getStartTradeOrderDate())
                .le(!ObjectUtils.isEmpty(param.getEndTradeOrderDate()), ShipmentOrder::getPlaceTradeOrderDate, param.getEndTradeOrderDate())
                .ge(!ObjectUtils.isEmpty(param.getStartSkuCount()), ShipmentOrder::getSkuQty, param.getStartSkuCount())
                .le(!ObjectUtils.isEmpty(param.getEndSkuCount()), ShipmentOrder::getSkuQty, param.getEndSkuCount())
                .ge(!ObjectUtils.isEmpty(param.getStartSkuCount()), ShipmentOrder::getSkuQty, param.getStartSkuCount())
                .le(!ObjectUtils.isEmpty(param.getEndSkuCount()), ShipmentOrder::getSkuQty, param.getEndSkuCount())
                .ge(!ObjectUtils.isEmpty(param.getStartSkuTypeCount()), ShipmentOrder::getSkuTypeQty, param.getStartSkuTypeCount())
                .le(!ObjectUtils.isEmpty(param.getEndSkuTypeCount()), ShipmentOrder::getSkuTypeQty, param.getEndSkuTypeCount())
                .ge(!ObjectUtils.isEmpty(param.getFirstPackOutStockDateStart()), ShipmentOrder::getFirstPackOutStockDate, param.getFirstPackOutStockDateStart())
                .le(!ObjectUtils.isEmpty(param.getFirstPackOutStockDateEnd()), ShipmentOrder::getFirstPackOutStockDate, param.getFirstPackOutStockDateEnd())
                // 预售类型
                .eq(!ObjectUtils.isEmpty(param.getPreSaleType()), ShipmentOrder::getPreSaleType, param.getPreSaleType())
                // 发货超时时间
                .ge(!ObjectUtils.isEmpty(param.getExpShipTimeStart()), ShipmentOrder::getExpShipTime, param.getExpShipTimeStart())
                .le(!ObjectUtils.isEmpty(param.getExpShipTimeEnd()), ShipmentOrder::getExpShipTime, param.getExpShipTimeEnd())
                // 理论重量
                .ge(!ObjectUtils.isEmpty(param.getWeightStart()), ShipmentOrder::getWeight, param.getWeightStart())
                .le(!ObjectUtils.isEmpty(param.getWeightEnd()), ShipmentOrder::getWeight, param.getWeightEnd())
                /**
                 * 测试反应，指定时间范围，未空的被查询出来了，
                 */
                .gt((!ObjectUtils.isEmpty(param.getStartExpOutStockDate()) || !ObjectUtils.isEmpty(param.getEndExpOutStockDate())), ShipmentOrder::getExpOutStockDate, 0L)
                .gt((!ObjectUtils.isEmpty(param.getStartOutStockDate()) || !ObjectUtils.isEmpty(param.getEndOutStockDate())), ShipmentOrder::getOutStockDate, 0L)
                .gt((!ObjectUtils.isEmpty(param.getStartPayTime()) || !ObjectUtils.isEmpty(param.getEndPayTime())), ShipmentOrder::getPayDate, 0L)
                .gt((!ObjectUtils.isEmpty(param.getStartTradeOrderDate()) || !ObjectUtils.isEmpty(param.getEndTradeOrderDate())), ShipmentOrder::getPlaceTradeOrderDate, 0L)
                .gt((!ObjectUtils.isEmpty(param.getFirstPackOutStockDateStart()) || !ObjectUtils.isEmpty(param.getFirstPackOutStockDateEnd())), ShipmentOrder::getFirstPackOutStockDate, 0L)
                .gt((!ObjectUtils.isEmpty(param.getExpShipTimeStart()) || !ObjectUtils.isEmpty(param.getExpShipTimeEnd())), ShipmentOrder::getExpShipTime, 0L)
                .eq(StrUtil.isNotBlank(param.getCustomsClearanceType()), ShipmentOrder::getCustomsClearanceType, param.getCustomsClearanceType())
                .in(CollectionUtil.isNotEmpty(param.getCustomsClearanceStatusList()), ShipmentOrder::getCustomsClearanceStatus, param.getCustomsClearanceStatusList())


                .ge(!ObjectUtils.isEmpty(param.getCheckCompleteDateStart()), ShipmentOrder::getCheckCompleteDate, param.getCheckCompleteDateStart())
                .le(!ObjectUtils.isEmpty(param.getCheckCompleteDateEnd()), ShipmentOrder::getCheckCompleteDate, param.getCheckCompleteDateEnd())
                .gt((!ObjectUtils.isEmpty(param.getCheckCompleteDateStart()) || !ObjectUtils.isEmpty(param.getCheckCompleteDateEnd())), ShipmentOrder::getCheckCompleteDate, 0L)

                .ge(!ObjectUtils.isEmpty(param.getPickCompleteSkuDateStart()), ShipmentOrder::getPickCompleteSkuDate, param.getPickCompleteSkuDateStart())
                .le(!ObjectUtils.isEmpty(param.getPickCompleteSkuDateEnd()), ShipmentOrder::getPickCompleteSkuDate, param.getPickCompleteSkuDateEnd())
                .gt((!ObjectUtils.isEmpty(param.getPickCompleteSkuDateStart()) || !ObjectUtils.isEmpty(param.getPickCompleteSkuDateEnd())), ShipmentOrder::getPickCompleteSkuDate, 0L)



                .ge(!ObjectUtils.isEmpty(param.getPayDateStart()), ShipmentOrder::getPayDate, param.getPayDateStart())
                .le(!ObjectUtils.isEmpty(param.getPayDateEnd()), ShipmentOrder::getPayDate, param.getPayDateEnd())
                .gt((!ObjectUtils.isEmpty(param.getPayDateStart()) || !ObjectUtils.isEmpty(param.getPayDateEnd())), ShipmentOrder::getPayDate, 0L)
        ;
        if (param.getOrderTag() != null && param.getOrderTag() > 0) {
            lambdaQueryWrapper.apply(" order_tag = (order_tag|" + param.getOrderTag() + ")");
        }
        if (param.getNoContainOrderTag() != null && param.getNoContainOrderTag() != 0) {
            lambdaQueryWrapper.apply(" order_tag = (order_tag&" + param.getNoContainOrderTag() + ")");
        }
        return lambdaQueryWrapper;

    }
}
