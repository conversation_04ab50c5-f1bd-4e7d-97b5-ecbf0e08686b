package com.dt.domain.bill.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.bill.param.TransferParam;
import com.dt.domain.bill.transfer.entity.Transfer;
import org.springframework.stereotype.Component;

@Component
public class TransferUtil extends QueryWrapper<Transfer, TransferParam> {

    public LambdaQueryWrapper<Transfer> getQueryWrapper(String transferCode) {
        TransferParam param = new TransferParam();
        param.setCode(transferCode);
        return this.getQueryWrapper(param);
    }

    @Override
    public LambdaQueryWrapper<Transfer> getQueryWrapper(TransferParam param) {
        LambdaQueryWrapper<Transfer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getCode()), Transfer::getCode, param.getCode());
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getStatus()), Transfer::getStatus, param.getStatus());
        lambdaQueryWrapper.in(CollectionUtil.isNotEmpty(param.getStatusList()), Transfer::getStatus, param.getStatusList());
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getCargoCode()), Transfer::getCargoCode, param.getCargoCode());

        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getBusinessType()), Transfer::getBusinessType, param.getBusinessType());
        lambdaQueryWrapper.in(CollectionUtil.isNotEmpty(param.getBusinessTypeList()), Transfer::getBusinessType, param.getBusinessTypeList());

        lambdaQueryWrapper.in(CollectionUtil.isNotEmpty(param.getCodeList()), Transfer::getCode, param.getCodeList());
        lambdaQueryWrapper.in(CollectionUtil.isNotEmpty(param.getCargoCodeList()), Transfer::getCargoCode, param.getCargoCodeList());

        lambdaQueryWrapper.ge(ObjectUtil.isNotEmpty(param.getCreateDateStart()), Transfer::getCreatedTime, param.getCreateDateStart());
        lambdaQueryWrapper.ge(ObjectUtil.isNotEmpty(param.getCompleteDateStart()), Transfer::getCompleteDate, param.getCompleteDateStart());
        lambdaQueryWrapper.le(ObjectUtil.isNotEmpty(param.getCreateDateEnd()), Transfer::getCreatedTime, param.getCreateDateEnd());
        lambdaQueryWrapper.le(ObjectUtil.isNotEmpty(param.getCompleteDateEnd()), Transfer::getCompleteDate, param.getCompleteDateEnd());
        lambdaQueryWrapper.ne(ObjectUtil.isNotEmpty(param.getCompleteDateEnd()), Transfer::getCompleteDate, 0);
        lambdaQueryWrapper.orderByDesc(Transfer::getId);
        if (param.getOrderTag() != null && param.getOrderTag() > 0) {
            lambdaQueryWrapper.apply(" order_tag = (order_tag|" + param.getOrderTag() + ")");
        }
        return lambdaQueryWrapper;
    }
}
