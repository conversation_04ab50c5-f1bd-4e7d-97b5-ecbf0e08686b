package com.dt.domain.bill.client;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.pkg.entity.PackageDetain;
import com.dt.domain.bill.pkg.entity.PackageDetainEntityParam;
import com.dt.domain.bill.pkg.mapper.PackageDetainMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class PackageDetainClient implements IPackageDetainClient {

    @Autowired
    private PackageDetainMapper packageDetainMapper;

    @Override
    @Transactional(readOnly = true)
    public List<PackageDetainDTO> packageDetain(PackageDetainParam param) {
        DateTime now = DateTime.now();
        PackageDetainEntityParam bean = BeanUtil.toBean(param, PackageDetainEntityParam.class);
        Map<String, PackageDetainDTO> detainDTOMap = new HashMap<>();
        try (Cursor<PackageDetain> packageDetains = packageDetainMapper.packageDetain(bean)) {
            packageDetains.forEach(packageDetain -> {
                detainDTOMap.computeIfPresent(packageDetain.getStatus(), (s, packageDetainDTO) -> packageDetainDTO(packageDetain, packageDetainDTO, now));
                detainDTOMap.computeIfAbsent(packageDetain.getStatus(), s -> packageDetainDTO(packageDetain, new PackageDetainDTO(packageDetain.getStatus()), now));
            });
        } catch (Exception e) {
            log.error("packageDetain error", e);
        }

        for (PackageDetainDTO packageDetainDTO : detainDTOMap.values()) {
            BigDecimal total = getBigDecimal(packageDetainDTO);
            packageDetainDTO.setTotal(total);
        }
        return new ArrayList<>(detainDTOMap.values());
    }

    private static BigDecimal getBigDecimal(PackageDetainDTO packageDetainDTO) {
        BigDecimal total = BigDecimal.ZERO;
        total = total.add(packageDetainDTO.getLt30());
        total = total.add(packageDetainDTO.getLt60());
        total = total.add(packageDetainDTO.getLt90());
        total = total.add(packageDetainDTO.getLt120());
        total = total.add(packageDetainDTO.getLt150());
        total = total.add(packageDetainDTO.getLt180());
        total = total.add(packageDetainDTO.getLt210());
        total = total.add(packageDetainDTO.getLt240());
        total = total.add(packageDetainDTO.getLt270());
        total = total.add(packageDetainDTO.getLt300());
        total = total.add(packageDetainDTO.getLt360());
        total = total.add(packageDetainDTO.getLt420());
        total = total.add(packageDetainDTO.getLt480());
        total = total.add(packageDetainDTO.getLt720());
        total = total.add(packageDetainDTO.getLt1440());
        total = total.add(packageDetainDTO.getLt2880());
        total = total.add(packageDetainDTO.getGt2880());
        return total;
    }

    private PackageDetainDTO packageDetainDTO(PackageDetain packageDetain,PackageDetainDTO packageDetainDTO,DateTime time) {

        if (packageDetain.getStatus().equalsIgnoreCase(PackEnum.STATUS.CREATE_STATUS.getCode())) {
            packageDetain.setUpdatedTime(packageDetain.getCreatedTime());
        }

        DateTime date = DateUtil.date(packageDetain.getUpdatedTime());
        long between = DateUtil.between(date, time, DateUnit.MINUTE);
        if (between < 30) {
            packageDetainDTO.setLt30(packageDetainDTO.getLt30().add(BigDecimal.ONE));
        } else if (between < 60) {
            packageDetainDTO.setLt60(packageDetainDTO.getLt60().add(BigDecimal.ONE));
        }else if (between < 90) {
            packageDetainDTO.setLt90(packageDetainDTO.getLt90().add(BigDecimal.ONE));
        } else if (between < 120) {
            packageDetainDTO.setLt120(packageDetainDTO.getLt120().add(BigDecimal.ONE));
        }else if (between < 150) {
            packageDetainDTO.setLt150(packageDetainDTO.getLt150().add(BigDecimal.ONE));
        } else if (between < 180) {
            packageDetainDTO.setLt180(packageDetainDTO.getLt180().add(BigDecimal.ONE));
        } else if (between < 210) {
            packageDetainDTO.setLt210(packageDetainDTO.getLt210().add(BigDecimal.ONE));
        } else if (between < 240) {
            packageDetainDTO.setLt240(packageDetainDTO.getLt240().add(BigDecimal.ONE));
        } else if (between < 270) {
            packageDetainDTO.setLt270(packageDetainDTO.getLt270().add(BigDecimal.ONE));
        } else if (between < 300) {
            packageDetainDTO.setLt300(packageDetainDTO.getLt300().add(BigDecimal.ONE));
        } else if (between < 360) {
            packageDetainDTO.setLt360(packageDetainDTO.getLt360().add(BigDecimal.ONE));
        } else if (between < 420) {
            packageDetainDTO.setLt420(packageDetainDTO.getLt420().add(BigDecimal.ONE));
        } else if (between < 480) {
            packageDetainDTO.setLt480(packageDetainDTO.getLt480().add(BigDecimal.ONE));
        } else if (between < 720) {
            packageDetainDTO.setLt720(packageDetainDTO.getLt720().add(BigDecimal.ONE));
        } else if (between < 1440) {
            packageDetainDTO.setLt1440(packageDetainDTO.getLt1440().add(BigDecimal.ONE));
        } else if (between < 2880) {
            packageDetainDTO.setLt2880(packageDetainDTO.getLt2880().add(BigDecimal.ONE));
        } else {
            packageDetainDTO.setGt2880(packageDetainDTO.getGt2880().add(BigDecimal.ONE));
        }

        return packageDetainDTO;
    }
}
