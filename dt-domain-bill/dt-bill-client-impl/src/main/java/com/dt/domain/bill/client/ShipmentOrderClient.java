package com.dt.domain.bill.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.RelatedBillTypeEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.mp.entity.BaseEntity;
import com.dt.domain.bill.abnormal.entity.AbnormalOrder;
import com.dt.domain.bill.abnormal.service.IAbnormalOrderService;
import com.dt.domain.bill.bo.DouChaoModifyAddressBO;
import com.dt.domain.bill.bo.ShipmentOrderCancelBO;
import com.dt.domain.bill.bo.ShipmentOrderImportBO;
import com.dt.domain.bill.bo.pretreatment.PretreatmentOperationBO;
import com.dt.domain.bill.client.message.MessageMqClientImpl;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.related.RelateOrdersTaoTianDTO;
import com.dt.domain.bill.dto.ship.ShipmentAnalysisBillDTO;
import com.dt.domain.bill.message.service.IMessageMqService;
import com.dt.domain.bill.order.entity.OrderIntercept;
import com.dt.domain.bill.order.service.IOrderInterceptService;
import com.dt.domain.bill.param.AllBoxParam;
import com.dt.domain.bill.param.NotifyParams;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.domain.bill.param.ship.ShipmentAnalysisBillParam;
import com.dt.domain.bill.pick.entity.PickDetail;
import com.dt.domain.bill.pick.service.IPickDetailService;
import com.dt.domain.bill.pkg.entity.Package;
import com.dt.domain.bill.pkg.entity.PackageDetail;
import com.dt.domain.bill.pkg.entity.PackageLog;
import com.dt.domain.bill.pkg.service.IPackageDetailService;
import com.dt.domain.bill.pkg.service.IPackageLogService;
import com.dt.domain.bill.pkg.service.IPackageService;
import com.dt.domain.bill.related.entity.RelatedBill;
import com.dt.domain.bill.related.service.IRelatedBillService;
import com.dt.domain.bill.replenish.service.IReplenishTaskService;
import com.dt.domain.bill.rs.entity.SalesReturnOrder;
import com.dt.domain.bill.rs.service.ISalesReturnOrderService;
import com.dt.domain.bill.shipment.entity.ShipmentOrder;
import com.dt.domain.bill.shipment.entity.ShipmentOrderDetail;
import com.dt.domain.bill.shipment.entity.ShipmentOrderLog;
import com.dt.domain.bill.shipment.entity.ShipmentOrderMaterial;
import com.dt.domain.bill.shipment.mapper.ShipmentOrderMapper;
import com.dt.domain.bill.shipment.param.ShipmentAnalysisParam;
import com.dt.domain.bill.shipment.service.IShipmentOrderDetailService;
import com.dt.domain.bill.shipment.service.IShipmentOrderLogService;
import com.dt.domain.bill.shipment.service.IShipmentOrderMaterialService;
import com.dt.domain.bill.shipment.service.IShipmentOrderService;
import com.dt.domain.bill.util.ShipmentOrderDetailUtil;
import com.dt.domain.bill.util.ShipmentOrderLogUtil;
import com.dt.domain.bill.util.ShipmentOrderUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:57
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class ShipmentOrderClient implements IShipmentOrderClient {

    @Resource
    private IMessageMqService messageMqService;
    @Resource
    private ShipmentOrderDetailUtil shipmentOrderDetailUtil;
    @Resource
    private IShipmentOrderService shipmentOrderService;
    @Resource
    private IPickDetailService pickDetailService;
    @Resource
    private ShipmentOrderLogUtil shipmentOrderLogUtil;
    @Resource
    private ShipmentOrderUtil shipmentOrderUtil;
    @Resource
    private IShipmentOrderDetailService shipmentOrderDetailService;
    @Resource
    private IShipmentOrderLogService shipmentOrderLogService;
    @Resource
    private IShipmentOrderMaterialService iShipmentOrderMaterialService;
    @Resource
    private IPackageDetailService packageDetailService;
    @Resource
    private IPackageService packageService;
    @Resource
    private IPackageLogService packageLogService;
    @Resource
    private IAbnormalOrderService abnormalOrderService;
    @Resource
    private IReplenishTaskService replenishTaskService;
    @Resource
    private IOrderInterceptService orderInterceptService;

    @Resource
    private IRelatedBillService relatedBillService;

    @Resource
    private ISalesReturnOrderService salesReturnOrderService;

    @Resource
    ShipmentOrderMapper shipmentOrderMapper;

    @Override
    public Result<List<ShipmentOrderDTO>> initCount() {
        return Result.success(ConverterUtil.convertList(shipmentOrderService.initCount(), ShipmentOrderDTO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveOrUpdate(ShipmentOrderParam shipmentOrderParam) {
        if (ObjectUtils.isEmpty(shipmentOrderParam) || CollectionUtils.isEmpty(shipmentOrderParam.getListShipmentOrderDetailParam())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        ShipmentOrder shipmentOrder = ConverterUtil.convert(shipmentOrderParam, ShipmentOrder.class);
        List<ShipmentOrderDetail> detailList = ConverterUtil.convertList(shipmentOrderParam.getListShipmentOrderDetailParam(), ShipmentOrderDetail.class);
        if (ObjectUtils.isEmpty(shipmentOrder) || CollectionUtils.isEmpty(detailList)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }

        //出库单
        try {
            if (shipmentOrder.getOrderTag() != null
                    && shipmentOrder.getOrderTag() > 0
                    && JSONUtil.isJson(shipmentOrder.getExtraJson())
                    && Objects.equals(shipmentOrder.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())
                    && OrderTagEnum.NumToEnum(shipmentOrder.getOrderTag()).contains(OrderTagEnum.TAOTAIN)) {
                String tobFulfillOrderNo = getTobFulfillOrderNo(shipmentOrder);
                if (!StringUtils.isEmpty(tobFulfillOrderNo)) {
                    String tradeNo = StringUtils.isEmpty(shipmentOrder.getTradeNo()) ? "" : shipmentOrder.getTradeNo();
                    shipmentOrder.setTradeNo(tradeNo + tobFulfillOrderNo);
                }
            }
        } catch (Exception e) {
            //..ex
        }

        // 淘天销退从销退捞运单号
        if (shipmentOrder.getOrderTag() != null && OrderTagEnum.NumToEnum(shipmentOrder.getOrderTag()).contains(OrderTagEnum.TAOTAIN) &&
                !StringUtils.isEmpty(shipmentOrder.getExtraJson()) && JSONUtil.isJson(shipmentOrder.getExtraJson())) {
            JSONObject jsonObject = JSONUtil.parseObj(shipmentOrder.getExtraJson());
            //销退出库作业单号(出库)
            if (OrderTagEnum.NumToEnum(shipmentOrder.getOrderTag()).contains(OrderTagEnum.TAOTIAN_XT)) {
                if (jsonObject.containsKey("refundFcUnitCode")) {
                    String refundFcUnitCode = jsonObject.getStr("refundFcUnitCode");
                    if (StrUtil.isNotBlank(refundFcUnitCode)) {
                        LambdaQueryWrapper<SalesReturnOrder> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(SalesReturnOrder::getSalesReturnOrderNo, refundFcUnitCode);
                        SalesReturnOrder one = salesReturnOrderService.getOne(queryWrapper);
                        if (null != one && StrUtil.isBlank(shipmentOrder.getExpressNo())) {
                            shipmentOrder.setExpressNo(one.getReverseExpressNo());
                        }
                    }
                }
            }
        }

        Boolean result = shipmentOrderService.save(shipmentOrder);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        result = shipmentOrderDetailService.saveBatch(detailList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        if (!CollectionUtils.isEmpty(shipmentOrderParam.getListShipmentOrderMaterialDetailParam())) {
            List<ShipmentOrderMaterial> materialDetailList = ConverterUtil.convertList(shipmentOrderParam.getListShipmentOrderMaterialDetailParam(), ShipmentOrderMaterial.class);
            result = iShipmentOrderMaterialService.saveBatch(materialDetailList);
            if (!result) {
                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
        }
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        //淘天销退需要存储属性 淘天B单清关需要存储
        List<RelatedBill> relatedBillList = new ArrayList<>();
        if (shipmentOrder.getOrderTag() != null && OrderTagEnum.NumToEnum(shipmentOrder.getOrderTag()).contains(OrderTagEnum.TAOTAIN) &&
                !StringUtils.isEmpty(shipmentOrder.getExtraJson()) && JSONUtil.isJson(shipmentOrder.getExtraJson())) {
            JSONObject jsonObject = JSONUtil.parseObj(shipmentOrder.getExtraJson());
            //销退出库作业单号(出库)
            if (OrderTagEnum.NumToEnum(shipmentOrder.getOrderTag()).contains(OrderTagEnum.TAOTIAN_XT)) {
                if (jsonObject.containsKey("refundFcUnitCode")) {
                    String refundFcUnitCode = jsonObject.getStr("refundFcUnitCode");

                    if (!StringUtils.isEmpty(refundFcUnitCode)) {
                        RelatedBill relatedBill = buildRelatedBill(shipmentOrder, refundFcUnitCode, RelatedBillTypeEnum.BILL_TYPE_SHIPMENT_XT);
                        relatedBillList.add(relatedBill);
                    }
                }
            }
            //出库作业单号(清关)
            if (jsonObject.containsKey("relatedOrders") && !StringUtils.isEmpty(jsonObject.getStr("relatedOrders"))
                    && JSONUtil.isJsonArray(jsonObject.getStr("relatedOrders"))) {
                JSONArray relatedOrders = JSONUtil.parseArray(jsonObject.getStr("relatedOrders"));
                List<RelateOrdersTaoTianDTO> relateOrders = JSONUtil.toList(relatedOrders, RelateOrdersTaoTianDTO.class);
                RelateOrdersTaoTianDTO relateOrder = relateOrders.stream().filter(a -> a.getOrderType().equals("TOB_FULFILL")).findFirst().orElse(null);
                if (relateOrder != null) {
                    RelatedBill relatedBill = buildRelatedBill(shipmentOrder, relateOrder.getOrderCode(), RelatedBillTypeEnum.BILL_TYPE_SHIPMENT_CUSTOMS);
                    relatedBillList.add(relatedBill);
                }
            }
            //主运单号
            if (jsonObject.containsKey("mainExpressNo") && !StringUtils.isEmpty(jsonObject.getStr("mainExpressNo"))) {
                String mainExpressNo = jsonObject.getStr("mainExpressNo");
                RelatedBill relatedBill = buildRelatedBill(shipmentOrder, mainExpressNo, RelatedBillTypeEnum.BILL_TYPE_SHIPMENT_MAIN_EXPRESS_NO);
                relatedBillList.add(relatedBill);
            }
            //淘天销退[合并清关]标识 合并清关标：
            // inboundCustomsMergeFlag   true/false（用于多包裹二次入区合流控制
            // sourceOrderCode  前序交易单号（用于多包裹二次入区合流控制）
            // inboundCustomsPkgNum 合并清关包裹数量（用于多包裹二次入区合流控制）
            //销退出库作业单号(出库)
            if (OrderTagEnum.NumToEnum(shipmentOrder.getOrderTag()).contains(OrderTagEnum.MERGE_CUSTOMS_CLEARANCE)) {
                if (jsonObject.containsKey("sourceOrderCode")) {
                    String sourceOrderCode = jsonObject.getStr("sourceOrderCode");
                    if (!StringUtils.isEmpty(sourceOrderCode)) {
                        RelatedBill relatedBill = buildRelatedBill(shipmentOrder, sourceOrderCode, RelatedBillTypeEnum.BILL_TYPE_SHIPMENT_XT_MERGE);
                        relatedBillList.add(relatedBill);
                    }
                }
            }


            if (!CollectionUtils.isEmpty(relatedBillList)) {
                boolean save = relatedBillService.saveBatch(relatedBillList);
                if (!save) {
                    throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
                }
            }
        }
        return Result.success(true);
    }

    /**
     * @param shipmentOrder
     * @return java.lang.String
     * <AUTHOR>
     * @describe: 淘天获取清关关联单号
     * @date 2024/4/3 15:41
     */
    private String getTobFulfillOrderNo(ShipmentOrder shipmentOrder) {
        String orderCode = "";
        try {
            JSONObject jsonObject = JSONUtil.parseObj(shipmentOrder.getExtraJson());
            if (!jsonObject.containsKey("relatedOrders") || StringUtils.isEmpty(jsonObject.getStr("relatedOrders"))
                    || !JSONUtil.isJsonArray(jsonObject.getStr("relatedOrders"))) {
                return orderCode;
            }
            JSONArray relatedOrders = JSONUtil.parseArray(jsonObject.getStr("relatedOrders"));
            List<RelateOrdersTaoTianDTO> relateOrders = JSONUtil.toList(relatedOrders, RelateOrdersTaoTianDTO.class);
            if (CollectionUtils.isEmpty(relatedOrders)) {
                return orderCode;
            }
            RelateOrdersTaoTianDTO relateOrder = relateOrders.stream().filter(a -> a.getOrderType().equals("TOB_FULFILL")).findFirst().orElse(null);
            if (relateOrder != null) {
                orderCode = relateOrder.getOrderCode();
            }
        } catch (Exception e) {
            //..ex
        }
        return orderCode;
    }


    /**
     * @param shipmentOrder
     * @param refundFcUnitCode
     * @param billTypeShipmentXt
     * @return com.dt.domain.bill.related.entity.RelatedBill
     * <AUTHOR>
     * @describe:
     * @date 2024/3/17 14:19
     */
    private RelatedBill buildRelatedBill(ShipmentOrder shipmentOrder, String refundFcUnitCode, RelatedBillTypeEnum billTypeShipmentXt) {
        RelatedBill relatedBill = new RelatedBill();
        relatedBill.setWarehouseCode(shipmentOrder.getWarehouseCode());
        relatedBill.setCargoCode(shipmentOrder.getCargoCode());
        relatedBill.setBillNo(shipmentOrder.getShipmentOrderCode());
        relatedBill.setRelatedNo(refundFcUnitCode);
        relatedBill.setType(billTypeShipmentXt.getType());
        return relatedBill;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyReceiverInfo(ShipmentOrderParam shipmentOrderParam) {
        if (ObjectUtils.isEmpty(shipmentOrderParam)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        ShipmentOrder shipmentOrder = shipmentOrderService.getById(shipmentOrderParam.getId());
        shipmentOrder.setReceiverProv(shipmentOrderParam.getReceiverProv());
        shipmentOrder.setReceiverCity(shipmentOrderParam.getReceiverCity());
        shipmentOrder.setReceiverArea(shipmentOrderParam.getReceiverArea());
        shipmentOrder.setReceiverAddress(shipmentOrderParam.getReceiverAddress());
        shipmentOrder.setReceiverAreaName(shipmentOrderParam.getReceiverAreaName());
        shipmentOrder.setReceiverCityName(shipmentOrderParam.getReceiverCityName());
        shipmentOrder.setReceiverProvName(shipmentOrderParam.getReceiverProvName());
        shipmentOrder.setReceiverStreetName(shipmentOrderParam.getReceiverStreetName());
        shipmentOrder.setReceiverMan(shipmentOrderParam.getReceiverMan());
        shipmentOrder.setReceiverTel(shipmentOrderParam.getReceiverTel());
        shipmentOrder.setSecureType(shipmentOrderParam.getSecureType());
        return Result.success(shipmentOrderService.saveOrUpdate(shipmentOrder));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> cancelShipment(ShipmentOrderCancelBO shipmentOrderCancelBO) {
        List<ShipmentOrder> shipmentOrderList = ConverterUtil.convertList(shipmentOrderCancelBO.getShipmentOrderDTOList(), ShipmentOrder.class);
        Boolean result = false;
        for (ShipmentOrder shipmentOrder : shipmentOrderList) {
            result = shipmentOrderService.updateById(shipmentOrder);
            if (!result) {
                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
        }
        List<ShipmentOrderDetail> shipmentOrderDetailList = ConverterUtil.convertList(shipmentOrderCancelBO.getShipmentOrderDetailDTOList(), ShipmentOrderDetail.class);
        for (ShipmentOrderDetail shipmentOrderDetail : shipmentOrderDetailList) {
            result = shipmentOrderDetailService.updateById(shipmentOrderDetail);
            if (!result) {
                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
        }
        List<ShipmentOrderLog> shipmentOrderLogList = ConverterUtil.convertList(shipmentOrderCancelBO.getShipmentOrderLogDTOList(), ShipmentOrderLog.class);
        result = shipmentOrderLogService.saveBatch(shipmentOrderLogList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveShipment(ShipmentOrderDTO shipmentOrderDTO) {
        ShipmentOrder shipmentOrder = ConverterUtil.convert(shipmentOrderDTO, ShipmentOrder.class);
        Boolean result = shipmentOrderService.save(shipmentOrder);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<ShipmentOrderDetail> shipmentOrderDetailList = ConverterUtil.convertList(shipmentOrderDTO.getListShipmentOrderDetailDTO(), ShipmentOrderDetail.class);
        result = shipmentOrderDetailService.saveBatch(shipmentOrderDetailList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<ShipmentOrderLog> shipmentOrderLogList = ConverterUtil.convertList(shipmentOrderDTO.getListShipmentOrderLogDTO(), ShipmentOrderLog.class);
        result = shipmentOrderLogService.saveBatch(shipmentOrderLogList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<ShipmentOrderMaterial> shipmentOrderMaterialList = ConverterUtil.convertList(shipmentOrderDTO.getShipmentOrderMaterialDTOList(), ShipmentOrderMaterial.class);
        result = iShipmentOrderMaterialService.saveBatch(shipmentOrderMaterialList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyToSpecialShipment(ShipmentOrderDTO shipmentOrderDTO) {
        ShipmentOrder shipmentOrder = ConverterUtil.convert(shipmentOrderDTO, ShipmentOrder.class);
        Boolean result = shipmentOrderService.updateById(shipmentOrder);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<ShipmentOrderDetail> shipmentOrderDetailList = ConverterUtil.convertList(shipmentOrderDTO.getListShipmentOrderDetailDTO(), ShipmentOrderDetail.class);
        //插入新纪录
        List<ShipmentOrderDetail> addShipmentOrderDetailList = shipmentOrderDetailList.stream()
                .filter(a -> a.getDeleted().equals(1)).collect(Collectors.toList());
        result = shipmentOrderDetailService.saveBatch(addShipmentOrderDetailList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        //移除老记录
        List<Long> removeShipmentOrderDetailIdList = shipmentOrderDetailList.stream()
                .filter(a -> a.getDeleted().equals(-1))
                .map(ShipmentOrderDetail::getId)
                .collect(Collectors.toList());
        result = shipmentOrderDetailService.removeByIds(removeShipmentOrderDetailIdList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<ShipmentOrderLog> shipmentOrderLogList = ConverterUtil.convertList(shipmentOrderDTO.getListShipmentOrderLogDTO(), ShipmentOrderLog.class);
        result = shipmentOrderLogService.saveBatch(shipmentOrderLogList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        //包材
        List<ShipmentOrderMaterial> shipmentOrderMaterialList = ConverterUtil.convertList(shipmentOrderDTO.getShipmentOrderMaterialDTOList(), ShipmentOrderMaterial.class);
        //插入新纪录
        List<ShipmentOrderMaterial> addShipmentOrderMaterialList = shipmentOrderMaterialList.stream()
                .filter(a -> a.getDeleted().equals(1)).collect(Collectors.toList());
        result = iShipmentOrderMaterialService.saveBatch(addShipmentOrderMaterialList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        //移除老记录
        List<Long> removeShipmentOrderMaterialList = shipmentOrderMaterialList.stream()
                .filter(a -> a.getDeleted().equals(-1))
                .map(ShipmentOrderMaterial::getId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(removeShipmentOrderMaterialList)) {
            result = iShipmentOrderMaterialService.removeByIds(removeShipmentOrderMaterialList);
        }
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> save(ShipmentOrderParam param) {
        if (ObjectUtils.isEmpty(param)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        ShipmentOrder shipmentOrder = ConverterUtil.convert(param, ShipmentOrder.class);
        Boolean result = shipmentOrderService.save(shipmentOrder);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> modify(ShipmentOrderParam param) {
        if (StringUtils.isEmpty(param.getId()) &&
                CollectionUtils.isEmpty(param.getIdList())
        ) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<ShipmentOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(param.getId()), ShipmentOrder::getId, param.getId())
                .in(!CollectionUtils.isEmpty(param.getIdList()), ShipmentOrder::getId, param.getIdList())
        ;
        ShipmentOrder shipmentOrder = ConverterUtil.convert(param, ShipmentOrder.class);
        if (ObjectUtils.isEmpty(shipmentOrder)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = shipmentOrderService.update(shipmentOrder, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(ShipmentOrderParam param) {
        LambdaQueryWrapper<ShipmentOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(param.getId()), ShipmentOrder::getId, param.getId())
                .in(!CollectionUtils.isEmpty(param.getIdList()), ShipmentOrder::getId, param.getIdList())
        ;
        Integer count = shipmentOrderService.count(wrapper);
        return Result.success(count != 0);
    }


    @Override
    public Result<Integer> count(ShipmentOrderParam param) {
        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(param);
        Integer count = shipmentOrderService.count(queryWrapper);
        return Result.success(count);
    }

    @Override
    public Result<ShipmentOrderDTO> get(ShipmentOrderParam param) {
        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(param);
        ShipmentOrder shipmentOrder = shipmentOrderService.getOne(queryWrapper);
        if (shipmentOrder == null) {
            return Result.success(null);
        }
        ShipmentOrderDTO result = ConverterUtil.convert(shipmentOrder, ShipmentOrderDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<ShipmentOrderDTO> getDetail(ShipmentOrderParam param) {
        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(param);
        ShipmentOrder shipmentOrder = shipmentOrderService.getOne(queryWrapper);
        ShipmentOrderDTO result = ConverterUtil.convert(shipmentOrder, ShipmentOrderDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<ShipmentOrderDTO>> getList(ShipmentOrderParam param) {
        //条件查询逻辑
        Boolean flag = getPageResult(param);
        if (flag) {
            return Result.success(Lists.newArrayList());
        }
        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(param);
        if (param.getSelectColumn()) {
            queryWrapper.select(BaseEntity::getId, ShipmentOrder::getStatus, ShipmentOrder::getCargoCode, ShipmentOrder::getShipmentOrderCode, ShipmentOrder::getWarehouseCode, ShipmentOrder::getPretreatmentStatus);
        }
        List<ShipmentOrder> shipmentOrderList = shipmentOrderService.list(queryWrapper);
        List<ShipmentOrderDTO> result = ConverterUtil.convertList(shipmentOrderList, ShipmentOrderDTO.class);
        return Result.success(result);
    }


    @Override
    public Result<Page<ShipmentOrderDTO>> getPage(ShipmentOrderParam param) {
        //条件查询逻辑
        Boolean flag = getPageResult(param);
        if (flag) {
            return Result.success(shipmentOrderUtil.<ShipmentOrderDTO>emptyPage(param));
        }
        if (!CollectionUtils.isEmpty(param.getPickCodeList())) {
            LambdaQueryWrapper<PickDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(PickDetail::getPickCode, param.getPickCodeList());
            List<PickDetail> pickDetailList = pickDetailService.list(lambdaQueryWrapper);
            if (pickDetailList == null) {
                pickDetailList = new ArrayList<>();
            }
            List<String> shipmentOrderCodeList = pickDetailList.stream().map(s -> s.getShipmentOrderCode()).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(shipmentOrderCodeList)) {
                if (param.getShipmentOrderCodeList() == null) {
                    param.setShipmentOrderCodeList(new ArrayList<String>());
                }
                param.getShipmentOrderCodeList().addAll(shipmentOrderCodeList);
            } else {
                return Result.success(shipmentOrderUtil.<ShipmentOrderDTO>emptyPage(param));
            }
        }

        Page<ShipmentOrder> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(param);
        IPage<ShipmentOrder> shipmentOrderPage = shipmentOrderService.page(page, queryWrapper);
        Page<ShipmentOrderDTO> result = ConverterUtil.convertPage(shipmentOrderPage, ShipmentOrderDTO.class);
        return Result.success(result);
    }


    @Override
    public Result<Page<ShipmentOrderDTO>> getAgainPage(AllBoxParam param) {
        Page<ShipmentOrder> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<ShipmentOrder> lambdaQueryWrapper = new LambdaQueryWrapper();
        List<String> pickCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(param.getPickCodeList())) {
            pickCodeList.addAll(param.getPickCodeList());
        }
        lambdaQueryWrapper
                .in(!ObjectUtils.isEmpty(param.getShipmentOrderCodeList()), ShipmentOrder::getShipmentOrderCode, param.getShipmentOrderCodeList())
                .eq(!ObjectUtils.isEmpty(param.getShipmentOrderCode()), ShipmentOrder::getShipmentOrderCode, param.getShipmentOrderCode())
                .in(!ObjectUtils.isEmpty(param.getSoNoList()), ShipmentOrder::getSoNo, param.getSoNoList())
                .eq(!ObjectUtils.isEmpty(param.getSoNo()), ShipmentOrder::getSoNo, param.getSoNo())
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), ShipmentOrder::getCargoCode, param.getCargoCode())
                .eq(!ObjectUtils.isEmpty(param.getBusinessType()), ShipmentOrder::getBusinessType, param.getBusinessType())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), ShipmentOrder::getStatus, param.getStatus())
                .in(!ObjectUtils.isEmpty(param.getStatusList()), ShipmentOrder::getStatus, param.getStatusList())
                .in(!ObjectUtils.isEmpty(param.getCargoCodeList()), ShipmentOrder::getCargoCode, param.getCargoCodeList())
                .eq(!ObjectUtils.isEmpty(param.getCarrierCode()), ShipmentOrder::getCarrierCode, param.getCarrierCode())
                .in(!ObjectUtils.isEmpty(param.getCarrierCodeList()), ShipmentOrder::getCarrierCode, param.getCarrierCodeList())
                .exists(!ObjectUtils.isEmpty(pickCodeList), "SELECT 1 FROM dt_pick_detail AA WHERE" +
                        " AA.shipment_order_code = dt_shipment_order.shipment_order_code AND AA.pick_code IN (" + pickCodeList.stream().map(s -> {
                    return "'" + s + "'";
                }).collect(Collectors.joining(",")) + ")");
        lambdaQueryWrapper.orderByDesc(ShipmentOrder::getCreatedTime);
        IPage<ShipmentOrder> shipmentOrderPage = shipmentOrderService.page(page, lambdaQueryWrapper);
        Page<ShipmentOrderDTO> result = ConverterUtil.convertPage(shipmentOrderPage, ShipmentOrderDTO.class);
        return Result.success(result);
    }


    @Override
    public Result<Page<ShipmentOrderDetailDTO>> getPage2(ShipmentOrderParam param) {
        //条件查询逻辑
        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(param);
        List<String> shipmentOrderNo = shipmentOrderService.list(queryWrapper).stream().map(s -> s.getShipmentOrderCode()).collect(Collectors.toList());
        Page<ShipmentOrderDetail> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        if (CollectionUtils.isEmpty(shipmentOrderNo)) {
            ShipmentOrderDetailParam detailParam = new ShipmentOrderDetailParam();
            detailParam.setSearchCount(param.getSearchCount());
            detailParam.setCurrent(param.getCurrent());
            detailParam.setSize(param.getSize());
            return Result.success(shipmentOrderDetailUtil.<ShipmentOrderDetailDTO>emptyPage(detailParam));
        }
        LambdaQueryWrapper<ShipmentOrderDetail> queryDetailWrapper = new LambdaQueryWrapper<>();
        queryDetailWrapper.in(!CollectionUtils.isEmpty(shipmentOrderNo), ShipmentOrderDetail::getShipmentOrderCode, shipmentOrderNo);
        queryDetailWrapper.orderByDesc(ShipmentOrderDetail::getCreatedTime);
        IPage<ShipmentOrderDetail> shipmentOrderDetailPage = shipmentOrderDetailService.page(page, queryDetailWrapper);
        Page<ShipmentOrderDetailDTO> result = ConverterUtil.convertPage(shipmentOrderDetailPage, ShipmentOrderDetailDTO.class);
        return Result.success(result);
    }

    /**
     * 功能描述:  分页查询和导出走同一个条件查询逻辑
     * 创建时间:  2020/12/19 1:57 下午
     *
     * @param param:
     * @return java.lang.Boolean
     * <AUTHOR>
     */
    private Boolean getPageResult(ShipmentOrderParam param) {
        List<String> list = new ArrayList<>();
        if (param.getShipmentOrderCodeList() != null) {
            list.addAll(param.getShipmentOrderCodeList());
        }
        if (!ObjectUtils.isEmpty(param.getPackageCodeList()) && !ObjectUtils.isEmpty(param.getPackageCodeList().stream().filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList()))) {
            QueryWrapper<PackageDetail> queryWrapper = new QueryWrapper();
            LambdaQueryWrapper<PackageDetail> lambdaQueryWrapper = queryWrapper.lambda();
            lambdaQueryWrapper.in(!ObjectUtils.isEmpty(param.getSkuCodeList()), PackageDetail::getSkuCode, param.getSkuCodeList());
            lambdaQueryWrapper.in(!ObjectUtils.isEmpty(param.getUpcCodeList()), PackageDetail::getUpcCode, param.getUpcCodeList());
            lambdaQueryWrapper.in(!ObjectUtils.isEmpty(param.getPackageCodeList()), PackageDetail::getPackageCode, param.getPackageCodeList());
            List<String> shipmentOrderNo = packageDetailService.getReferShipmentOrderCode(lambdaQueryWrapper);
            if (CollectionUtils.isEmpty(list)) {
                list = shipmentOrderNo;
            } else {
                list.retainAll(shipmentOrderNo);
            }
            if (CollectionUtils.isEmpty(list)) {
                return Boolean.TRUE;
            }
        }
        /**
         * 先查询商品信息，得到出库单号
         */
        else if (!ObjectUtils.isEmpty(param.getSkuCodeList()) || !ObjectUtils.isEmpty(param.getUpcCodeList())) {
            QueryWrapper<ShipmentOrderDetail> queryWrapper = new QueryWrapper();
            LambdaQueryWrapper<ShipmentOrderDetail> lambdaQueryWrapper = queryWrapper.lambda();
            lambdaQueryWrapper.in(!ObjectUtils.isEmpty(param.getSkuCodeList()), ShipmentOrderDetail::getSkuCode, param.getSkuCodeList());
            lambdaQueryWrapper.in(!ObjectUtils.isEmpty(param.getUpcCodeList()), ShipmentOrderDetail::getUpcCode, param.getUpcCodeList());
            List<String> shipmentOrderNo = shipmentOrderDetailService.list(lambdaQueryWrapper).stream().map(s -> s.getShipmentOrderCode()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                list = shipmentOrderNo;
            } else {
                list.retainAll(shipmentOrderNo);
            }
            if (CollectionUtils.isEmpty(list)) {
                return Boolean.TRUE;
            }
        }

        // 包材
        if (ObjectUtil.isNotEmpty(param.getMaterialUpcCode())) {
            QueryWrapper<ShipmentOrderMaterial> queryWrapper = new QueryWrapper<>();
            LambdaQueryWrapper<ShipmentOrderMaterial> lambda = queryWrapper.lambda();
            lambda.eq(StrUtil.isNotBlank(param.getMaterialUpcCode()), ShipmentOrderMaterial::getRecPackUpcCode, param.getMaterialUpcCode());
            List<ShipmentOrderMaterial> shipmentOrderMaterials = iShipmentOrderMaterialService.list(lambda);
            List<String> collect = shipmentOrderMaterials.stream().map(ShipmentOrderMaterial::getShipmentOrderCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(list)) {
                list = collect;
            } else {
                list.retainAll(collect);
            }
            if (CollectionUtil.isEmpty(list)) {
                return Boolean.TRUE;
            }
        }

        param.setShipmentOrderCodeList(list);
        return Boolean.FALSE;
    }
//
//    @Override
//    public Result<Page<ShipmentOrderDTO>> getPage(ShipmentOrderParam param) {
//        Page<ShipmentOrder> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
//        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(param);
//        IPage<ShipmentOrder> shipmentOrderPage = shipmentOrderService.page(page, queryWrapper);
//        Page<ShipmentOrderDTO> result = ConverterUtil.convertPage(shipmentOrderPage, ShipmentOrderDTO.class);
//        return Result.success(result);
//    }

    @Override
    public Result<List<Long>> getIdList(ShipmentOrderParam param) {
        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(param);
        queryWrapper.select(ShipmentOrder::getId);
        List<ShipmentOrder> shipmentOrderList = shipmentOrderService.list(queryWrapper);
        List<Long> result = shipmentOrderList
                .stream()
                .flatMap(a -> Stream.of(a.getId()))
                .collect(Collectors.toList());
        return Result.success(result);
    }

    @Override
    public Result<List<ShipmentOrderDTO>> getAppointMultipleParam(ShipmentOrderParam param, List<String> tableFields) {
        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(param);
        if (!CollectionUtils.isEmpty(tableFields)) {
            queryWrapper.select(ShipmentOrder.class, i -> tableFields.contains(i.getColumn()));
        }
        List<ShipmentOrder> shipmentOrderList = shipmentOrderService.list(queryWrapper);
        return Result.success(ConverterUtil.convertList(shipmentOrderList, ShipmentOrderDTO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveExcelImportShipment(ShipmentOrderImportBO shipmentOrderImportBO) {
        //新增出库单明细
        shipmentOrderDetailService.saveBatch(ConverterUtil.convertList(shipmentOrderImportBO.getShipmentOrderDetailDTOList(), ShipmentOrderDetail.class));
        //新增出口段
        shipmentOrderService.saveBatch(ConverterUtil.convertList(shipmentOrderImportBO.getShipmentOrderDTOList(), ShipmentOrder.class));
        //新增出库单日志
        if (!CollectionUtils.isEmpty(shipmentOrderImportBO.getShipmentOrderLogDTOList())) {
            shipmentOrderLogService.saveBatch(ConverterUtil.convertList(shipmentOrderImportBO.getShipmentOrderLogDTOList(), ShipmentOrderLog.class));
        }
        //新增出库单包材
        if (!CollectionUtils.isEmpty(shipmentOrderImportBO.getShipmentOrderMaterialDTOList())) {
            iShipmentOrderMaterialService.saveBatch(ConverterUtil.convertList(shipmentOrderImportBO.getShipmentOrderMaterialDTOList(), ShipmentOrderMaterial.class));
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> pretreatmentSubmit(PretreatmentOperationBO param) {
        if (CollectionUtil.isNotEmpty(param.getShipmentOrderDetailDTOList())) {
            for (ShipmentOrderDetailDTO shipmentOrderDetailDTO : param.getShipmentOrderDetailDTOList()) {
                boolean update = shipmentOrderDetailService.updateById(ConverterUtil.convert(shipmentOrderDetailDTO, ShipmentOrderDetail.class));
                if (!update) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            }
        }
        //出库单
        if (ObjectUtil.isNotEmpty(param.getShipmentOrderDTO())) {
            Boolean result = shipmentOrderService.updateById(ConverterUtil.convert(param.getShipmentOrderDTO(), ShipmentOrder.class));
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        // 异常单
        if (!CollectionUtils.isEmpty(param.getAbnormalOrderDTOList())) {
            param.getAbnormalOrderDTOList().forEach(abnormalOrderDTO -> {
                Boolean result = abnormalOrderService.saveOrUpdate(ConverterUtil.convert(abnormalOrderDTO, AbnormalOrder.class));
                if (!result) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        // 出库单日志
        if (ObjectUtil.isNotEmpty(param.getShipmentOrderLogDTO())) {
            Boolean result = shipmentOrderLogService.save(ConverterUtil.convert(param.getShipmentOrderLogDTO(), ShipmentOrderLog.class));
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        //包裹日志
        if (ObjectUtil.isNotEmpty(param.getPackageLogDTO())) {
            Boolean result = packageLogService.save(ConverterUtil.convert(param.getPackageLogDTO(), PackageLog.class));
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        if (CollectionUtil.isNotEmpty(param.getPackageLogDTOList())) {
            for (PackageLogDTO packageLogDTO : param.getPackageLogDTOList()) {
                if (!packageLogService.save(ConverterUtil.convert(packageLogDTO, PackageLog.class))) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            }
        }
        //更新包裹明细
        if (!CollectionUtils.isEmpty(param.getPackageDetailDTOList())) {
            //明细
            param.getPackageDetailDTOList().forEach(packageDetailDTO -> {
                Boolean re = packageDetailService.saveOrUpdate(ConverterUtil.convert(packageDetailDTO, PackageDetail.class));
                if (!re) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        //更新包裹
        if (ObjectUtil.isNotEmpty(param.getPackageDTO())) {
            if (!packageService.saveOrUpdate(ConverterUtil.convert(param.getPackageDTO(), Package.class))) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        if (!CollectionUtils.isEmpty(param.getPackageDTOList())) {
            param.getPackageDTOList().forEach(packageDTO -> {
                Boolean result = packageService.saveOrUpdate(ConverterUtil.convert(packageDTO, Package.class));
                if (!result) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        //删除包裹明细
        if (!CollectionUtils.isEmpty(param.getDeletePackageDetailDTOList())) {
            //明细
            param.getDeletePackageDetailDTOList().forEach(packageDetailDTO -> {
                Boolean re = packageDetailService.removeById(ConverterUtil.convert(packageDetailDTO, PackageDetail.class));
                if (!re) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        if (CollectionUtil.isNotEmpty(param.getOrderInterceptDTOList())) {
            param.getOrderInterceptDTOList().forEach(orderInterceptDTO -> {
                boolean re = orderInterceptService.updateById(ConverterUtil.convert(orderInterceptDTO, OrderIntercept.class));
                if (!re) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        return Result.success(true);
    }

    @Override
    public Result<List<ShipmentOrderMaterialDTO>> getPackMaterialCollectWaveShipmentOrderList(ShipmentOrderParam param, List<String> tableFields) {
        LambdaQueryWrapper<ShipmentOrderMaterial> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(!StringUtils.isEmpty(param.getShipmentOrderCode()), ShipmentOrderMaterial::getShipmentOrderCode, param.getShipmentOrderCode());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getShipmentOrderCodeList()), ShipmentOrderMaterial::getShipmentOrderCode, param.getShipmentOrderCodeList());
        lambdaQueryWrapper.eq(!StringUtils.isEmpty(param.getRecPackUpcCode()), ShipmentOrderMaterial::getRecPackUpcCode, param.getRecPackUpcCode());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getRecPackUpcCodeList()), ShipmentOrderMaterial::getRecPackUpcCode, param.getRecPackUpcCodeList());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getCargoCodeList()), ShipmentOrderMaterial::getCargoCode, param.getCargoCodeList());
        lambdaQueryWrapper.gt(!StringUtils.isEmpty(param.getCreatedTimeStart()), ShipmentOrderMaterial::getCreatedTime, param.getCreatedTimeStart());
        lambdaQueryWrapper.lt(!StringUtils.isEmpty(param.getCreatedTimeEnd()), ShipmentOrderMaterial::getCreatedTime, param.getCreatedTimeEnd());
        if (!CollectionUtils.isEmpty(tableFields)) {
            lambdaQueryWrapper.select(ShipmentOrderMaterial.class, i -> tableFields.contains(i.getColumn()));
        }
        List<ShipmentOrderMaterial> ShipmentOrderMaterialS = iShipmentOrderMaterialService.list(lambdaQueryWrapper);
        List<ShipmentOrderMaterialDTO> entityList = ConverterUtil.convertList(ShipmentOrderMaterialS, ShipmentOrderMaterialDTO.class);
        return Result.success(entityList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(ShipmentOrderDTO shipmentOrderDTO) {
        Boolean result = shipmentOrderService.updateById(ConverterUtil.convert(shipmentOrderDTO, ShipmentOrder.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> commitBoxDataShipmentOrder(ShipmentOrderDTO shipmentOrderDTO) {
        Boolean result = shipmentOrderService.updateById(ConverterUtil.convert(shipmentOrderDTO, ShipmentOrder.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //更新出库单明细
        if (!CollectionUtils.isEmpty(shipmentOrderDTO.getListShipmentOrderDetailDTO())) {
            //明细
            shipmentOrderDTO.getListShipmentOrderDetailDTO().forEach(shipmentOrderDetailDTO -> {
                Boolean re = shipmentOrderDetailService.saveOrUpdate(ConverterUtil.convert(shipmentOrderDetailDTO, ShipmentOrderDetail.class));
                if (!re) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }

        if (!CollectionUtils.isEmpty(shipmentOrderDTO.getRemoveIdDetailList())) {
            //明细
            shipmentOrderDTO.getRemoveIdDetailList().forEach(id -> {
                Boolean re = shipmentOrderDetailService.removeById(id);
                if (!re) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        if (!CollectionUtils.isEmpty(shipmentOrderDTO.getListShipmentOrderLogDTO())) {
            Boolean re = shipmentOrderLogService.saveBatch(ConverterUtil.convertList(shipmentOrderDTO.getListShipmentOrderLogDTO(), ShipmentOrderLog.class));
            if (!re) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        return Result.success(true);
    }

    @Override
    public Result<List<ShipmentOrderLogDTO>> getShipmentLog(ShipmentOrderParam shipmentOrderLogParam) {
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setShipmentOrderCode(shipmentOrderLogParam.getShipmentOrderCode());
        shipmentOrderParam.setShipmentOrderCodeList(shipmentOrderLogParam.getShipmentOrderCodeList());
        LambdaQueryWrapper<ShipmentOrderLog> logEntityDetailWrapper = shipmentOrderLogUtil.getQueryWrapper(shipmentOrderParam);
        List<ShipmentOrderLog> logEntityList = shipmentOrderLogService.list(logEntityDetailWrapper);
        return Result.success(ConverterUtil.convertList(logEntityList, ShipmentOrderLogDTO.class));
    }

    @Override
    public Result<Boolean> modifyPretreatmentStatus(ShipmentOrderParam param) {
        if (StringUtils.isEmpty(param.getId()) &&
                CollectionUtils.isEmpty(param.getIdList()) &&
                StringUtils.isEmpty(param.getShipmentOrderCode()) &&
                CollectionUtils.isEmpty(param.getShipmentOrderCodeList())
        ) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<ShipmentOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(param.getId()), ShipmentOrder::getId, param.getId())
                .eq(!StringUtils.isEmpty(param.getShipmentOrderCode()), ShipmentOrder::getShipmentOrderCode, param.getShipmentOrderCode())
                .eq(!StringUtils.isEmpty(param.getPretreatmentLockStatus()), ShipmentOrder::getPretreatmentStatus, param.getPretreatmentLockStatus())
                .in(!CollectionUtils.isEmpty(param.getPretreatmentLockStatusList()), ShipmentOrder::getPretreatmentStatus, param.getPretreatmentLockStatusList())
                .in(!CollectionUtils.isEmpty(param.getIdList()), ShipmentOrder::getId, param.getIdList())
                .in(!CollectionUtils.isEmpty(param.getShipmentOrderCodeList()), ShipmentOrder::getShipmentOrderCode, param.getShipmentOrderCodeList())
        ;
        ShipmentOrder shipmentOrder = new ShipmentOrder();
        shipmentOrder.setStatus(param.getStatus());
        shipmentOrder.setPretreatmentStatus(param.getPretreatmentStatus());
        shipmentOrder.setInterceptCancelDate(param.getInterceptCancelDate());
        Boolean result = shipmentOrderService.update(shipmentOrder, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<ShipmentOrderDTO> findShipmentOrder(ShipmentOrderParam param) {
        LambdaQueryWrapper<ShipmentOrder> detailWrapper = shipmentOrderUtil.getQueryWrapper(param);
        ShipmentOrder shipmentOrder = shipmentOrderService.getOne(detailWrapper);
        if (shipmentOrder == null) {
            return Result.success(null);
        }
        ShipmentOrderDetailParam param1 = new ShipmentOrderDetailParam();
        param1.setShipmentOrderCode(shipmentOrder.getShipmentOrderCode());
        LambdaQueryWrapper<ShipmentOrderDetail> entityDetailWrapper = shipmentOrderDetailUtil.getQueryWrapper(param1);
        List<ShipmentOrderDetail> list = shipmentOrderDetailService.list(entityDetailWrapper);
        List<ShipmentOrderDetailDTO> entityList = ConverterUtil.convertList(list, ShipmentOrderDetailDTO.class);
        ShipmentOrderDTO shipmentOrderDTO = ConverterUtil.convert(shipmentOrder, ShipmentOrderDTO.class);
        shipmentOrderDTO.setListShipmentOrderDetailDTO(entityList);
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setShipmentOrderCode(shipmentOrder.getShipmentOrderCode());
        LambdaQueryWrapper<ShipmentOrderLog> logEntityDetailWrapper = shipmentOrderLogUtil.getQueryWrapper(shipmentOrderParam);
        List<ShipmentOrderLog> logEntityList = shipmentOrderLogService.list(logEntityDetailWrapper);
        List<ShipmentOrderLogDTO> dtoEntityList = ConverterUtil.convertList(logEntityList, ShipmentOrderLogDTO.class);
        shipmentOrderDTO.setListShipmentOrderLogDTO(dtoEntityList);
        return Result.success(shipmentOrderDTO);
    }


    @Override
    public Result<List<ShipmentOrderDTO>> getCollectWaveShipmentOrderList(ShipmentOrderParam param, List<String> fieldList) {
        LambdaQueryWrapper<ShipmentOrder> wrapper = shipmentOrderUtil.getQueryWrapper(param);
        if (!CollectionUtils.isEmpty(fieldList)) {
            wrapper.select(ShipmentOrder.class, i -> fieldList.contains(i.getColumn()));
        }
        List<ShipmentOrder> shipmentOrders = shipmentOrderService.list(wrapper);
        return Result.success(ConverterUtil.convertList(shipmentOrders, ShipmentOrderDTO.class));
    }

    @Override
    public Result<List<ShipmentOrderDetailDTO>> queryShipmentOrderList(ShipmentOrderParam param) {
        ShipmentOrderDetailParam detailParam = new ShipmentOrderDetailParam();
        detailParam.setShipmentOrderCodeList(param.getShipmentOrderCodeList());
        LambdaQueryWrapper<ShipmentOrderDetail> entityDetailWrapper = shipmentOrderDetailUtil.getQueryWrapper(detailParam);
        List<ShipmentOrderDetail> list = shipmentOrderDetailService.list(entityDetailWrapper);
        List<ShipmentOrderDetailDTO> entityList = ConverterUtil.convertList(list, ShipmentOrderDetailDTO.class);
        return Result.success(entityList);
    }

    @Override
    @Transactional
    public Result<Boolean> submitShipment(List<ShipmentOrderDTO> shipmentOrderDTOS) {
        for (ShipmentOrderDTO entity : shipmentOrderDTOS) {
            Boolean result = shipmentOrderService.updateById(ConverterUtil.convert(entity, ShipmentOrder.class));
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
            List<ShipmentOrderDetail> shipmentOrderDetailList = ConverterUtil.convertList(entity.getListShipmentOrderDetailDTO(), ShipmentOrderDetail.class);
            shipmentOrderDetailList.forEach(shipmentOrderDetail -> {
                Boolean updateResult = shipmentOrderDetailService.updateById(shipmentOrderDetail);
                if (!updateResult) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> modifyNotifyStatus(String shipmentOrderCode, List<String> packageCodeList, Long notifyTime, Integer notifyStatus) {
        LambdaQueryWrapper<ShipmentOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ShipmentOrder::getShipmentOrderCode, shipmentOrderCode);
        ShipmentOrder shipmentOrder = shipmentOrderService.getOne(lambdaQueryWrapper);
        if (ObjectUtils.isEmpty(shipmentOrder)) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        if (!CollectionUtils.isEmpty(packageCodeList)) {
            List<Package> packageList = packageService.list(Wrappers.<Package>query().lambda().in(Package::getPackageCode, packageCodeList));
            if (!CollectionUtils.isEmpty(packageList)) {
                for (Package _package : packageList) {
                    _package.setNotifyCount((_package.getNotifyCount() == null ? 0 : _package.getNotifyCount()) + 1);
                    _package.setNotifyStatus(notifyStatus);
                    _package.setNotifyTime(notifyTime);
                    _package.setBackFlag(shipmentOrderCode);
                    Boolean updateResult = packageService.updateById(_package);
                    if (!updateResult) {
                        throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                    }
                }
            }
        }
        shipmentOrder.setNotifyStatus(notifyStatus);
        shipmentOrder.setNotifyTime(notifyTime);
        shipmentOrder.setNotifyCount((shipmentOrder.getNotifyCount() == null ? 0 : shipmentOrder.getNotifyCount()) + 1);
        boolean updateResult = shipmentOrderService.updateById(shipmentOrder);
        if (!updateResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<List<ShipmentOrderDTO>> getNotifyShipmentOrderList(NotifyParams notifyParams) {
        QueryWrapper<ShipmentOrder> shipmentOrderQueryWrapper = new QueryWrapper();
        LambdaQueryWrapper<ShipmentOrder> lambdaQueryWrapper = shipmentOrderQueryWrapper.lambda();
        lambdaQueryWrapper.in(ShipmentOrder::getStatus, notifyParams.getStatusList())
                .in(ShipmentOrder::getNotifyStatus, notifyParams.getNodifyStatusList());
        lambdaQueryWrapper.and(wrapper -> wrapper.isNull(ShipmentOrder::getNotifyCount)
                        .or().lt(ShipmentOrder::getNotifyCount, notifyParams.getNotifyCount()))
                .select(ShipmentOrder::getWarehouseCode, ShipmentOrder::getShipmentOrderCode);
        //编码扫描全表
        lambdaQueryWrapper.ge(!ObjectUtils.isEmpty(notifyParams.getOutStockDateStart()) && notifyParams.getOutStockDateStart() > 0, ShipmentOrder::getOutStockDate, notifyParams.getOutStockDateStart());
        return Result.success(ConverterUtil.convertList(shipmentOrderService.list(lambdaQueryWrapper), ShipmentOrderDTO.class));
    }

    @Override
    public Result<List<ShipmentOrderMaterialDTO>> getPackMaterialList(ShipmentOrderParam param) {
        LambdaQueryWrapper<ShipmentOrderMaterial> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(!StringUtils.isEmpty(param.getShipmentOrderCode()), ShipmentOrderMaterial::getShipmentOrderCode, param.getShipmentOrderCode());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getShipmentOrderCodeList()), ShipmentOrderMaterial::getShipmentOrderCode, param.getShipmentOrderCodeList());
        lambdaQueryWrapper.eq(!StringUtils.isEmpty(param.getRecPackUpcCode()), ShipmentOrderMaterial::getRecPackUpcCode, param.getRecPackUpcCode());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getRecPackUpcCodeList()), ShipmentOrderMaterial::getRecPackUpcCode, param.getRecPackUpcCodeList());

        List<ShipmentOrderMaterial> ShipmentOrderMaterialS = iShipmentOrderMaterialService.list(lambdaQueryWrapper);
        List<ShipmentOrderMaterialDTO> entityList = ConverterUtil.convertList(ShipmentOrderMaterialS, ShipmentOrderMaterialDTO.class);
        return Result.success(entityList);

    }

    @Override
    public Result<List<String>> getShipmentCodeList(ShipmentOrderParam shipmentOrderParam) {
        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(shipmentOrderParam);
        queryWrapper.select(ShipmentOrder::getShipmentOrderCode);
        List<ShipmentOrder> shipmentOrderList = shipmentOrderService.list(queryWrapper);
        List<String> result = shipmentOrderList
                .stream()
                .flatMap(a -> Stream.of(a.getShipmentOrderCode()))
                .collect(Collectors.toList());
        return Result.success(result);
    }

    @Override
    public Result<Boolean> modifyStatus(String shipmentOrderCode, Long updateTime, String status) {
        LambdaQueryWrapper<ShipmentOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ShipmentOrder::getShipmentOrderCode, shipmentOrderCode);
        ShipmentOrder shipmentOrder = shipmentOrderService.getOne(lambdaQueryWrapper);
        if (ObjectUtils.isEmpty(shipmentOrder)) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        shipmentOrder.setStatus(status);
        if (status.equals(ShipmentOrderEnum.STATUS.INTERCEPT_STATUS.getCode())) {
            shipmentOrder.setInterceptCancelDate(System.currentTimeMillis());
        }
        shipmentOrder.setUpdatedTime(updateTime);
        boolean updateResult = shipmentOrderService.updateById(shipmentOrder);
        if (!updateResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkSoNoExistsByCargoCode(ShipmentOrderParam param) {
        Integer i = shipmentOrderService.count(Wrappers.<ShipmentOrder>query().lambda().eq(ShipmentOrder::getCargoCode, param.getCargoCode())
                .in(ShipmentOrder::getSoNo, param.getSoNo()).ne(ShipmentOrder::getStatus, "00"));
        return Result.success(i >= 1);
    }

    @Override
    public Result<List<String>> checkPoNoExistsByCargoCode(ShipmentOrderParam param) {
        List<ShipmentOrder> shipmentOrderlist = shipmentOrderService.list(Wrappers.<ShipmentOrder>query().lambda().eq(ShipmentOrder::getCargoCode, param.getCargoCode())
                .in(ShipmentOrder::getPoNo, param.getPoNo()).ne(ShipmentOrder::getStatus, "00")
                .in(ShipmentOrder::getStatus, param.getStatusList()));
        if (shipmentOrderlist == null) {
            shipmentOrderlist = new ArrayList<>();
        }
        return Result.success(shipmentOrderlist.stream().map(s -> s.getShipmentOrderCode()).distinct().collect(Collectors.toList()));
    }

    @Override
    public Result<Boolean> checkExpressExistsByCarrierCode(ShipmentOrderParam param) {
        Integer i = shipmentOrderService.count(Wrappers.<ShipmentOrder>query().lambda().eq(ShipmentOrder::getCarrierCode, param.getCarrierCode())
                .eq(ShipmentOrder::getExpressNo, param.getExpressNo())
                .ne(param.getId() != null, ShipmentOrder::getId, param.getId())
                .notIn(ShipmentOrder::getStatus, Arrays.asList(ShipmentOrderEnum.STATUS.INTERCEPT_STATUS.getCode(),
                        ShipmentOrderEnum.STATUS.CANCEL_STATUS.getCode())));
        return Result.success(i >= 1);
    }

    @Override
    @Transactional
    public Result<Boolean> submitUpdateCollect(List<ShipmentOrderDTO> shipmentOrderDTOS) {
        for (ShipmentOrderDTO entity : shipmentOrderDTOS) {
            Boolean result = false;
            for (ShipmentOrderDetail shipmentOrderDetail : ConverterUtil.convertList(entity.getListShipmentOrderDetailDTO(), ShipmentOrderDetail.class)) {
                result = shipmentOrderDetailService.updateById(shipmentOrderDetail);
                if (!result) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            }
            result = shipmentOrderService.updateById(ConverterUtil.convert(entity, ShipmentOrder.class));
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> modifyBatch(List<ShipmentOrderDTO> shipmentOrderDTOS) {
        for (ShipmentOrder shipmentOrder : ConverterUtil.convertList(shipmentOrderDTOS, ShipmentOrder.class)) {
            Boolean result = shipmentOrderService.updateById(shipmentOrder);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        return Result.success(true);
    }

    @Override
    public Result<List<ShipmentOrderDetailDTO>> getDetailList(ShipmentOrderDetailParam param) {
        LambdaQueryWrapper<ShipmentOrderDetail> entityDetailWrapper = shipmentOrderDetailUtil.getQueryWrapper(param);
        List<ShipmentOrderDetail> list = shipmentOrderDetailService.list(entityDetailWrapper);
        List<ShipmentOrderDetailDTO> entityList = ConverterUtil.convertList(list, ShipmentOrderDetailDTO.class);
        return Result.success(entityList);
    }

    @Override
    public Result<List<ShipmentOrderMaterialDTO>> queryOrderMaterialByOrderCode(ShipmentOrderDetailParam param) {
        LambdaQueryWrapper<ShipmentOrderMaterial> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ShipmentOrderMaterial::getShipmentOrderCode, param.getShipmentOrderCode());
        List<ShipmentOrderMaterial> ShipmentOrderMaterialS = iShipmentOrderMaterialService.list(lambdaQueryWrapper);
        List<ShipmentOrderMaterialDTO> entityList = ConverterUtil.convertList(ShipmentOrderMaterialS, ShipmentOrderMaterialDTO.class);
        return Result.success(entityList);
    }

    @Override
    @Transactional
    public Result<Boolean> modifyDetailList(List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOS) {
        for (ShipmentOrderDetail shipmentOrderDetail : ConverterUtil.convertList(shipmentOrderDetailDTOS, ShipmentOrderDetail.class)) {
            Boolean result = shipmentOrderDetailService.updateById(shipmentOrderDetail);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> saveShipmentLog(ShipmentOrderLogDTO shipmentOrderLogDTO) {
        // 设置默认备注
        if (StringUtils.isEmpty(shipmentOrderLogDTO.getOpRemark())) {
            shipmentOrderLogDTO.setOpRemark("");
        }
        Boolean result = shipmentOrderLogService.save(ConverterUtil.convert(shipmentOrderLogDTO, ShipmentOrderLog.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> saveShipmentLogList(List<ShipmentOrderLogDTO> shipmentOrderLogDTOList) {
        shipmentOrderLogDTOList.forEach(it -> {
            if (StringUtils.isEmpty(it.getOpRemark())) {
                it.setOpRemark("");
            }
        });
        Boolean result = shipmentOrderLogService.saveBatch(ConverterUtil.convertList(shipmentOrderLogDTOList, ShipmentOrderLog.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> cancelPretreatment(PretreatmentOperationBO pretreatmentOperationBO) {
        AbnormalOrderClient.persist(pretreatmentOperationBO.getAbnormalOrderDTOList(), abnormalOrderService);
        ReplenishTaskClient.persist(pretreatmentOperationBO.getReplenishTaskDTOList(), replenishTaskService);
        PackageDetailClient.persist(pretreatmentOperationBO.getPackageDetailDTOList(), packageDetailService);
        PackageClient.persist(pretreatmentOperationBO.getPackageDTOList(), packageService);
        PackageClient.persistLog(pretreatmentOperationBO.getPackageLogDTOList(), packageLogService);
        persist(ListUtil.toList(pretreatmentOperationBO.getShipmentOrderDTO()), shipmentOrderService);
        persistLog(ListUtil.toList(pretreatmentOperationBO.getShipmentOrderLogDTO()), shipmentOrderLogService);
        ShipmentOrderDetailClient.persist(pretreatmentOperationBO.getShipmentOrderDetailDTOList(), shipmentOrderDetailService);
        MessageMqClientImpl.persist(pretreatmentOperationBO.getMessageMqDTOList(), messageMqService);
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyAndDetail(ShipmentOrderDTO shipmentOrderDTO) {
        Boolean result = false;
        if (!CollectionUtils.isEmpty(shipmentOrderDTO.getListShipmentOrderDetailDTO())) {
            for (ShipmentOrderDetail shipmentOrderDetail : ConverterUtil.convertList(shipmentOrderDTO.getListShipmentOrderDetailDTO(), ShipmentOrderDetail.class)) {
                result = shipmentOrderDetailService.updateById(shipmentOrderDetail);
                if (!result) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            }
        }
        result = shipmentOrderService.updateById(ConverterUtil.convert(shipmentOrderDTO, ShipmentOrder.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> commitDouChaoModifyAddress(DouChaoModifyAddressBO douChaoModifyAddressBO) {
        //插入拦截单
        Boolean save = orderInterceptService.save(ConverterUtil.convert(douChaoModifyAddressBO.getOrderInterceptDTO(), OrderIntercept.class));
        if (!save) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //插入新出库单明细
        save = shipmentOrderDetailService.saveBatch(ConverterUtil.convertList(douChaoModifyAddressBO.getShipmentOrderDetailDTONewList(), ShipmentOrderDetail.class));
        if (!save) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //插入新出库单
        save = shipmentOrderService.save(ConverterUtil.convert(douChaoModifyAddressBO.getShipmentOrderNewDTO(), ShipmentOrder.class));
        if (!save) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //插入日志
        save = shipmentOrderLogService.saveBatch(ConverterUtil.convertList(douChaoModifyAddressBO.getShipmentOrderLogDTOList(), ShipmentOrderLog.class));
        if (!save) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        Boolean result = shipmentOrderService.updateById(ConverterUtil.convert(douChaoModifyAddressBO.getShipmentOrderOldDTO(), ShipmentOrder.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<Page<ShipmentAnalysisBillDTO>> getShipmentAnalysisPage(ShipmentAnalysisBillParam param) {

        // 使用 Mapper 方法执行查询
        Page<Map<String, Object>> page = new Page<>(param.getCurrent(), param.getSize());
        IPage<Map<String, Object>> shipmentAnalysisPage = shipmentOrderService.getShipmentAnalysisPage(page, ConverterUtil.convert(param, ShipmentAnalysisParam.class));

        if (shipmentAnalysisPage == null || shipmentAnalysisPage.getRecords() == null || shipmentAnalysisPage.getRecords().isEmpty()) {
            return Result.success(new Page<>(param.getCurrent(), param.getSize(), 0));
        }
        // 转换查询结果为DTO对象
        List<ShipmentAnalysisBillDTO> resultList = new ArrayList<>();
        for (Map<String, Object> map : shipmentAnalysisPage.getRecords()) {
            ShipmentAnalysisBillDTO dto = new ShipmentAnalysisBillDTO();
            dto.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
            // 设置维度值
            Map<String, String> dimensionValues = new HashMap<>();
            // 设置各个维度字段
            if (map.containsKey("cargoCode")) {
                String value = map.get("cargoCode") != null ? map.get("cargoCode").toString() : "";
                dto.setCargoCode(value);
                dimensionValues.put("cargoCode", value);
            }

            if (map.containsKey("businessType")) {
                String value = map.get("businessType") != null ? map.get("businessType").toString() : "";
                dto.setBusinessType(value);
                dimensionValues.put("businessType", value);
            }

            if (map.containsKey("salePlatform")) {
                String value = map.get("salePlatform") != null ? map.get("salePlatform").toString() : "";
                dto.setSalePlatform(value);
                dimensionValues.put("salePlatform", value);
            }

            if (map.containsKey("packageStruct")) {
                String value = map.get("packageStruct") != null ? map.get("packageStruct").toString() : "";
                dto.setPackageStruct(value);
                dimensionValues.put("packageStruct", value);
            }

            if (map.containsKey("expOutStockDate_day")) {
                String value = map.get("expOutStockDate_day") != null ? map.get("expOutStockDate_day").toString() : "";
                dto.setExpOutStockDate_day(value);
                dimensionValues.put("expOutStockDate_day", value);
            }

            if (map.containsKey("expOutStockDate_hour")) {
                String value = map.get("expOutStockDate_hour") != null ? map.get("expOutStockDate_hour").toString() : "";
                dto.setExpOutStockDate_hour(value);
                dimensionValues.put("expOutStockDate_hour", value);
            }

            if (map.containsKey("createTime_day")) {
                String value = map.get("createTime_day") != null ? map.get("createTime_day").toString() : "";
                dto.setCreatedTime_day(value);
                dimensionValues.put("createTime_day", value);
            }

            if (map.containsKey("createTime_hour")) {
                String value = map.get("createTime_hour") != null ? map.get("createTime_hour").toString() : "";
                dto.setCreatedTime_hour(value);
                dimensionValues.put("createTime_hour", value);
            }

            if (map.containsKey("payDate_day")) {
                String value = map.get("payDate_day") != null ? map.get("payDate_day").toString() : "";
                dto.setPayDate_day(value);
                dimensionValues.put("payDate_day", value);
            }

            if (map.containsKey("payDate_hour")) {
                String value = map.get("payDate_hour") != null ? map.get("payDate_hour").toString() : "";
                dto.setPayDate_hour(value);
                dimensionValues.put("payDate_hour", value);
            }

            if (map.containsKey("outStockDate_day")) {
                String value = map.get("outStockDate_day") != null ? map.get("outStockDate_day").toString() : "";
                dto.setOutStockDate_day(value);
                dimensionValues.put("outStockDate_day", value);
            }

            if (map.containsKey("outStockDate_hour")) {
                String value = map.get("outStockDate_hour") != null ? map.get("outStockDate_hour").toString() : "";
                dto.setOutStockDate_hour(value);
                dimensionValues.put("outStockDate_hour", value);
            }

            if (map.containsKey("carrierCode")) {
                String value = map.get("carrierCode") != null ? map.get("carrierCode").toString() : "";
                dto.setCarrierCode(value);
                dimensionValues.put("carrierCode", value);
            }

            if (map.containsKey("receiverProvName")) {
                String value = map.get("receiverProvName") != null ? map.get("receiverProvName").toString() : "";
                dto.setReceiverProvName(value);
                dimensionValues.put("receiverProvName", value);
            }

            if (map.containsKey("receiverCityName")) {
                String value = map.get("receiverCityName") != null ? map.get("receiverCityName").toString() : "";
                dto.setReceiverCityName(value);
                dimensionValues.put("receiverCityName", value);
            }

            if (map.containsKey("receiverAreaName")) {
                String value = map.get("receiverAreaName") != null ? map.get("receiverAreaName").toString() : "";
                dto.setReceiverAreaName(value);
                dimensionValues.put("receiverAreaName", value);
            }

            // 设置维度值映射
            dto.setDimensionValues(dimensionValues);

            // 设置统计值
            if (map.containsKey("orderCount")) {
                dto.setOrderCount(Integer.valueOf(map.get("orderCount").toString()));
            }

            if (map.containsKey("skuQtySum")) {
                dto.setSkuQtySum(new BigDecimal(map.get("skuQtySum").toString()));
            }

            if (map.containsKey("createdOrderCount")) {
                dto.setCreatedOrderCount(Integer.valueOf(map.get("createdOrderCount").toString()));
            }

            if (map.containsKey("pretreatmentFailOrderCount")) {
                dto.setPretreatmentFailOrderCount(Integer.valueOf(map.get("pretreatmentFailOrderCount").toString()));
            }

            if (map.containsKey("pretreatmentCompleteOrderCount")) {
                dto.setPretreatmentCompleteOrderCount(Integer.valueOf(map.get("pretreatmentCompleteOrderCount").toString()));
            }

            if (map.containsKey("collectedOrderCount")) {
                dto.setCollectedOrderCount(Integer.valueOf(map.get("collectedOrderCount").toString()));
            }

            if (map.containsKey("checkStartOrderCount")) {
                dto.setCheckStartOrderCount(Integer.valueOf(map.get("checkStartOrderCount").toString()));
            }

            if (map.containsKey("checkCompleteOrderCount")) {
                dto.setCheckCompleteOrderCount(Integer.valueOf(map.get("checkCompleteOrderCount").toString()));
            }

            if (map.containsKey("partialOutOrderCount")) {
                dto.setPartialOutOrderCount(Integer.valueOf(map.get("partialOutOrderCount").toString()));
            }

            if (map.containsKey("outOrderCount")) {
                dto.setOutOrderCount(Integer.valueOf(map.get("outOrderCount").toString()));
            }

            if (map.containsKey("interceptOrderCount")) {
                dto.setInterceptOrderCount(Integer.valueOf(map.get("interceptOrderCount").toString()));
            }

            if (map.containsKey("cancelOrderCount")) {
                dto.setCancelOrderCount(Integer.valueOf(map.get("cancelOrderCount").toString()));
            }

            if (map.containsKey("shortageOutOrderCount")) {
                dto.setShortageOutOrderCount(Integer.valueOf(map.get("shortageOutOrderCount").toString()));
            }

            resultList.add(dto);
        }
        // 构建分页结果
        Page<ShipmentAnalysisBillDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resultPage.setRecords(resultList);

        return Result.success(resultPage);

    }

    @Override
    public Result<ShipmentAnalysisBillDTO> getShipmentAnalysisSummary(ShipmentAnalysisBillParam param) {

        QueryWrapper<ShipmentOrder> queryWrapper = new QueryWrapper();
        LambdaQueryWrapper<ShipmentOrder> lambdaQueryWrapper = queryWrapper.lambda();

        // 构建SELECT子句
        List<String> selectFields = new ArrayList<>();

        // 添加统计字段
        selectFields.add("COUNT(1) AS orderCount");
        selectFields.add("SUM(sku_qty) AS skuQtySum");
        selectFields.add("SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) AS createdOrderCount");
        selectFields.add("SUM(CASE WHEN status = '15' THEN 1 ELSE 0 END) AS pretreatmentFailOrderCount");
        selectFields.add("SUM(CASE WHEN status = '20' THEN 1 ELSE 0 END) AS pretreatmentCompleteOrderCount");
        selectFields.add("SUM(CASE WHEN status = '25' THEN 1 ELSE 0 END) AS collectedOrderCount");
        selectFields.add("SUM(CASE WHEN status = '30' THEN 1 ELSE 0 END) AS checkStartOrderCount");
        selectFields.add("SUM(CASE WHEN status = '35' THEN 1 ELSE 0 END) AS checkCompleteOrderCount");
        selectFields.add("SUM(CASE WHEN status = '45' THEN 1 ELSE 0 END) AS partialOutOrderCount");
        selectFields.add("SUM(CASE WHEN status = '50' THEN 1 ELSE 0 END) AS outOrderCount");
        selectFields.add("SUM(CASE WHEN status = '55' THEN 1 ELSE 0 END) AS interceptOrderCount");
        selectFields.add("SUM(CASE WHEN status = '60' THEN 1 ELSE 0 END) AS cancelOrderCount");
        selectFields.add("SUM(CASE WHEN status = '40' THEN 1 ELSE 0 END) AS shortageOutOrderCount");

        // 添加查询条件
        if (CollectionUtil.isNotEmpty(param.getCargoCodeList())) {
            lambdaQueryWrapper.in(ShipmentOrder::getCargoCode, param.getCargoCodeList());
        }

        if (StrUtil.isNotEmpty(param.getBusinessType())) {
            lambdaQueryWrapper.eq(ShipmentOrder::getBusinessType, param.getBusinessType());
        }

        if (StrUtil.isNotEmpty(param.getSalePlatform())) {
            lambdaQueryWrapper.eq(ShipmentOrder::getSalePlatform, param.getSalePlatform());
        }

        if (param.getCreateTimeStart() != null) {
            lambdaQueryWrapper.ge(ShipmentOrder::getCreatedTime, param.getCreateTimeStart());
        }

        if (param.getCreateTimeEnd() != null) {
            lambdaQueryWrapper.le(ShipmentOrder::getCreatedTime, param.getCreateTimeEnd());
        }

        if (param.getPayDateStart() != null) {
            lambdaQueryWrapper.ge(ShipmentOrder::getPayDate, param.getPayDateStart());
        }

        if (param.getPayDateEnd() != null) {
            lambdaQueryWrapper.le(ShipmentOrder::getPayDate, param.getPayDateEnd());
        }

        if (param.getExpOutStockDateStart() != null) {
            lambdaQueryWrapper.ge(ShipmentOrder::getExpOutStockDate, param.getExpOutStockDateStart());
        }

        if (param.getExpOutStockDateEnd() != null) {
            lambdaQueryWrapper.le(ShipmentOrder::getExpOutStockDate, param.getExpOutStockDateEnd());
        }

        if (param.getOutStockDateStart() != null) {
            lambdaQueryWrapper.ge(ShipmentOrder::getOutStockDate, param.getOutStockDateStart());
        }

        if (param.getOutStockDateEnd() != null) {
            lambdaQueryWrapper.le(ShipmentOrder::getOutStockDate, param.getOutStockDateEnd());
        }

        if (CollectionUtil.isNotEmpty(param.getPackageStructList())) {
            lambdaQueryWrapper.in(ShipmentOrder::getPackageStruct, param.getPackageStructList());
        }

        if (param.getSkuTypeQtyMin() != null) {
            lambdaQueryWrapper.ge(ShipmentOrder::getSkuTypeQty, param.getSkuTypeQtyMin());
        }

        if (param.getSkuTypeQtyMax() != null) {
            lambdaQueryWrapper.le(ShipmentOrder::getSkuTypeQty, param.getSkuTypeQtyMax());
        }

        if (param.getSkuQtyMin() != null) {
            lambdaQueryWrapper.ge(ShipmentOrder::getSkuQty, param.getSkuQtyMin());
        }

        if (param.getSkuQtyMax() != null) {
            lambdaQueryWrapper.le(ShipmentOrder::getSkuQty, param.getSkuQtyMax());
        }

        if (param.getWeightMin() != null) {
            lambdaQueryWrapper.ge(ShipmentOrder::getWeight, param.getWeightMin());
        }

        if (param.getWeightMax() != null) {
            lambdaQueryWrapper.le(ShipmentOrder::getWeight, param.getWeightMax());
        }

        if (StrUtil.isNotEmpty(param.getCarrierCode())) {
            lambdaQueryWrapper.eq(ShipmentOrder::getCarrierCode, param.getCarrierCode());
        }

        if (CollectionUtil.isNotEmpty(param.getReceiverProvList())) {
            lambdaQueryWrapper.in(ShipmentOrder::getReceiverProv, param.getReceiverProvList());
        }

        if (CollectionUtil.isNotEmpty(param.getReceiverCityList())) {
            lambdaQueryWrapper.in(ShipmentOrder::getReceiverCity, param.getReceiverCityList());
        }

        if (CollectionUtil.isNotEmpty(param.getReceiverAreaList())) {
            lambdaQueryWrapper.in(ShipmentOrder::getReceiverArea, param.getReceiverAreaList());
        }

        // 使用MyBatis-Plus的原生SQL查询功能
        Map<String, Object> result = shipmentOrderService.getMap(queryWrapper.select(String.join(", ", selectFields)));

        // 如果没有数据，返回空的汇总结果
        if (result == null || result.isEmpty()) {
            ShipmentAnalysisBillDTO emptyResult = new ShipmentAnalysisBillDTO();
            emptyResult.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
            emptyResult.setWarehouseName(null);
            emptyResult.setCargoCode("");
            emptyResult.setOrderCount(0);
            emptyResult.setSkuQtySum(BigDecimal.ZERO);
            emptyResult.setCreatedOrderCount(0);
            emptyResult.setPretreatmentFailOrderCount(0);
            emptyResult.setPretreatmentCompleteOrderCount(0);
            emptyResult.setCollectedOrderCount(0);
            emptyResult.setCheckStartOrderCount(0);
            emptyResult.setCheckCompleteOrderCount(0);
            emptyResult.setPartialOutOrderCount(0);
            emptyResult.setOutOrderCount(0);
            emptyResult.setInterceptOrderCount(0);
            emptyResult.setCancelOrderCount(0);
            emptyResult.setShortageOutOrderCount(0);

            // 设置维度值映射
            Map<String, String> dimensionValues = new HashMap<>();
            dimensionValues.put("summary", "汇总");
            emptyResult.setDimensionValues(dimensionValues);

            return Result.success(emptyResult);
        }

        // 转换查询结果为DTO对象
        ShipmentAnalysisBillDTO dto = new ShipmentAnalysisBillDTO();
        dto.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        dto.setWarehouseName(null);
        dto.setCargoCode(null);

        // 设置维度值映射
        Map<String, String> dimensionValues = new HashMap<>();
        dimensionValues.put("summary", "汇总");
        dto.setDimensionValues(dimensionValues);

        // 设置统计值
        if (result.containsKey("orderCount")) {
            dto.setOrderCount(Integer.valueOf(result.get("orderCount").toString()));
        } else {
            dto.setOrderCount(0);
        }

        if (result.containsKey("skuQtySum")) {
            dto.setSkuQtySum(new BigDecimal(result.get("skuQtySum").toString()));
        } else {
            dto.setSkuQtySum(BigDecimal.ZERO);
        }

        if (result.containsKey("createdOrderCount")) {
            dto.setCreatedOrderCount(Integer.valueOf(result.get("createdOrderCount").toString()));
        } else {
            dto.setCreatedOrderCount(0);
        }

        if (result.containsKey("pretreatmentFailOrderCount")) {
            dto.setPretreatmentFailOrderCount(Integer.valueOf(result.get("pretreatmentFailOrderCount").toString()));
        } else {
            dto.setPretreatmentFailOrderCount(0);
        }

        if (result.containsKey("pretreatmentCompleteOrderCount")) {
            dto.setPretreatmentCompleteOrderCount(Integer.valueOf(result.get("pretreatmentCompleteOrderCount").toString()));
        } else {
            dto.setPretreatmentCompleteOrderCount(0);
        }

        if (result.containsKey("collectedOrderCount")) {
            dto.setCollectedOrderCount(Integer.valueOf(result.get("collectedOrderCount").toString()));
        } else {
            dto.setCollectedOrderCount(0);
        }

        if (result.containsKey("checkStartOrderCount")) {
            dto.setCheckStartOrderCount(Integer.valueOf(result.get("checkStartOrderCount").toString()));
        } else {
            dto.setCheckStartOrderCount(0);
        }

        if (result.containsKey("checkCompleteOrderCount")) {
            dto.setCheckCompleteOrderCount(Integer.valueOf(result.get("checkCompleteOrderCount").toString()));
        } else {
            dto.setCheckCompleteOrderCount(0);
        }

        if (result.containsKey("partialOutOrderCount")) {
            dto.setPartialOutOrderCount(Integer.valueOf(result.get("partialOutOrderCount").toString()));
        } else {
            dto.setPartialOutOrderCount(0);
        }

        if (result.containsKey("outOrderCount")) {
            dto.setOutOrderCount(Integer.valueOf(result.get("outOrderCount").toString()));
        } else {
            dto.setOutOrderCount(0);
        }

        if (result.containsKey("interceptOrderCount")) {
            dto.setInterceptOrderCount(Integer.valueOf(result.get("interceptOrderCount").toString()));
        } else {
            dto.setInterceptOrderCount(0);
        }

        if (result.containsKey("cancelOrderCount")) {
            dto.setCancelOrderCount(Integer.valueOf(result.get("cancelOrderCount").toString()));
        } else {
            dto.setCancelOrderCount(0);
        }

        if (result.containsKey("shortageOutOrderCount")) {
            dto.setShortageOutOrderCount(Integer.valueOf(result.get("shortageOutOrderCount").toString()));
        } else {
            dto.setShortageOutOrderCount(0);
        }

        return Result.success(dto);

    }

    public static void persist(List<ShipmentOrderDTO> shipmentOrderDTOList, IShipmentOrderService shipmentOrderService) {

        if (CollectionUtil.isEmpty(shipmentOrderDTOList)) {
            return;
        }

        List<ShipmentOrder> saveList = shipmentOrderDTOList.stream()
                .filter(stock -> ObjectUtil.isEmpty(stock.getId()))
                .map(it -> ConverterUtil.convert(it, ShipmentOrder.class))
                .collect(Collectors.toList());

        List<ShipmentOrder> updateList = shipmentOrderDTOList.stream()
                .filter(stock -> ObjectUtil.isNotEmpty(stock.getId()))
                .map(it -> ConverterUtil.convert(it, ShipmentOrder.class))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(saveList)) {
            if (!shipmentOrderService.saveBatch(saveList)) {
                throw ExceptionUtil.exceptionWithMessage("保存出库单失败");
            }
        }

        for (ShipmentOrder aPackage : updateList) {
            if (!shipmentOrderService.updateById(aPackage)) {
                throw ExceptionUtil.exceptionWithMessage("修改出库单失败");
            }
        }
    }

    public static void persistLog(List<ShipmentOrderLogDTO> shipmentOrderLogDTOList, IShipmentOrderLogService shipmentOrderLogService) {

        if (CollectionUtil.isEmpty(shipmentOrderLogDTOList)) {
            return;
        }

        List<ShipmentOrderLog> saveList = shipmentOrderLogDTOList.stream()
                .filter(stock -> ObjectUtil.isEmpty(stock.getId()))
                .map(it -> ConverterUtil.convert(it, ShipmentOrderLog.class))
                .collect(Collectors.toList());

        List<ShipmentOrderLog> updateList = shipmentOrderLogDTOList.stream()
                .filter(stock -> ObjectUtil.isNotEmpty(stock.getId()))
                .map(it -> ConverterUtil.convert(it, ShipmentOrderLog.class))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(saveList)) {
            if (!shipmentOrderLogService.saveBatch(saveList)) {
                throw ExceptionUtil.exceptionWithMessage("保存出库单日志失败");
            }
        }

        for (ShipmentOrderLog aPackage : updateList) {
            if (!shipmentOrderLogService.updateById(aPackage)) {
                throw ExceptionUtil.exceptionWithMessage("修改出库单日志失败");
            }
        }
    }
}
