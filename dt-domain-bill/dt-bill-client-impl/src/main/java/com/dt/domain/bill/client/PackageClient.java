package com.dt.domain.bill.client;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.Deleted;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.wave.WaveNavigationLevelEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.mp.entity.BaseEntity;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.pkg.PackAnalysisBillDTO;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.pkg.PackAnalysisBillParam;
import com.dt.domain.bill.pick.entity.PickDetail;
import com.dt.domain.bill.pick.service.IPickDetailService;
import com.dt.domain.bill.pkg.entity.Package;
import com.dt.domain.bill.pkg.entity.PackageDetail;
import com.dt.domain.bill.pkg.entity.PackageLog;
import com.dt.domain.bill.pkg.mapper.CollectWaveMapper;
import com.dt.domain.bill.pkg.param.PackAnalysisParam;
import com.dt.domain.bill.pkg.service.IPackageDetailService;
import com.dt.domain.bill.pkg.service.IPackageLogService;
import com.dt.domain.bill.pkg.service.IPackageService;
import com.dt.domain.bill.shipment.entity.ShipmentOrder;
import com.dt.domain.bill.shipment.entity.ShipmentOrderMaterial;
import com.dt.domain.bill.shipment.service.IShipmentOrderMaterialService;
import com.dt.domain.bill.shipment.service.IShipmentOrderService;
import com.dt.domain.bill.util.PackageDetailUtil;
import com.dt.domain.bill.util.PackageUtil;
import com.dt.domain.bill.util.ShipmentOrderUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.DefaultSortUtil;
import com.dt.platform.utils.ExceptionUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:57
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class PackageClient implements IPackageClient {

    @Resource
    private IPackageService ipackageService;
    @Resource
    private IPackageDetailService ipackageDetailService;
    @Resource
    private IPackageLogService ipackageLogService;
    @Resource
    private PackageUtil packageUtil;
    @Resource
    private PackageDetailUtil packageDetailUtil;
    @Resource
    private ShipmentOrderUtil shipmentOrderUtil;
    @Resource
    private IShipmentOrderService shipmentOrderService;
    @Resource
    private IShipmentOrderMaterialService shipmentOrderMaterialService;
    @Resource
    private IPickDetailService pickDetailService;
    @Resource
    CollectWaveMapper collectWaveMapper;
    @Resource
    private IPackageService packageService;
//
//    @Resource
//    private PackageUtil packageUtil;


    @Override
    public Result<List<PackageDTO>> initCount() {
        return Result.success(ConverterUtil.convertList(packageService.initCount(), PackageDTO.class));
    }

    @Override
    @Transactional
    public Result<Boolean> save(PackageParam param) {
        if (ObjectUtils.isEmpty(param)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Package pack = ConverterUtil.convert(param, Package.class);
        Boolean result = packageService.save(pack);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> saveOrUpdate(PackageParam param) {
        if (ObjectUtils.isEmpty(param)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Package pack = ConverterUtil.convert(param, Package.class);
        Boolean result = packageService.saveOrUpdate(pack);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> modify(PackageParam param) {
        if (StringUtils.isEmpty(param.getId()) &&
                CollectionUtils.isEmpty(param.getIdList()) &&
                StringUtils.isEmpty(param.getPackageCode()) &&
                CollectionUtils.isEmpty(param.getPackageCodeList())
        ) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<Package> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(param.getId()), Package::getId, param.getId())
                .eq(!StringUtils.isEmpty(param.getPackageCode()), Package::getPackageCode, param.getPackageCode())
                .in(!CollectionUtils.isEmpty(param.getIdList()), Package::getId, param.getIdList())
                .in(!CollectionUtils.isEmpty(param.getPackageCodeList()), Package::getPackageCode, param.getPackageCodeList())
        ;
        Package pack = ConverterUtil.convert(param, Package.class);
        pack.setId(null);
        pack.setPackageCode(null);
        if (ObjectUtils.isEmpty(pack)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = packageService.update(pack, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }


    @Override
    @Transactional
    public Result<Boolean> modifyBatch(PackageBatchParam param) {
        if (ObjectUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getPackageList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<Package> packageList = ConverterUtil.convertList(param.getPackageList(), Package.class);
        packageList.forEach(entity -> {
            boolean saveOrUpdate = packageService.saveOrUpdate(entity);
            if (!saveOrUpdate) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> modifyBatch(List<PackageDTO> packageDTOList) {
        List<Package> packageList = ConverterUtil.convertList(packageDTOList, Package.class);
        packageList.forEach(entity -> {
            boolean saveOrUpdate = packageService.saveOrUpdate(entity);
            if (!saveOrUpdate) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(PackageParam param) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(param);
        Integer count = packageService.count(queryWrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<List<PackageDTO>> getPackageCodeOrExpressNo(PackageParam param) {
        LambdaQueryWrapper<Package> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Package::getPackageCode, param.getPackageCode());

        List<Package> listPack = packageService.list(queryWrapper);
        if (CollectionUtils.isEmpty(listPack)) {
            queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Package::getExpressNo, param.getPackageCode());
            listPack = packageService.list(queryWrapper);
        }
        List<PackageDTO> listResult = ConverterUtil.convertList(listPack, PackageDTO.class);
        /**
         * 修复BUG 把取消，拦截也查询出来了
         */
        listResult = listResult.stream().filter(s -> ((!PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode().equalsIgnoreCase(s.getStatus())) &&
                (!PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode().equalsIgnoreCase(s.getStatus())))).collect(Collectors.toList());
        return Result.success(listResult);
    }


    @Override
    public Result<PackageDTO> get(PackageParam param) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(param);
        Package pack = packageService.getOne(queryWrapper);
        PackageDTO result = ConverterUtil.convert(pack, PackageDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<PackageDTO> getDetail(PackageParam param) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(param);
        Package pack = packageService.getOne(queryWrapper);
        PackageDTO result = ConverterUtil.convert(pack, PackageDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<PackageDTO>> getList(PackageParam param) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(param);
        if (param.getSelectColumn()) {
            queryWrapper.select(BaseEntity::getId, Package::getCargoCode, Package::getPackageCode, BaseEntity::getWarehouseCode, Package::getPretreatmentStatus, Package::getShipmentOrderCode);
        }
        List<Package> packList = packageService.list(queryWrapper);
        List<PackageDTO> result = ConverterUtil.convertList(packList, PackageDTO.class);
        return Result.success(result);
    }


    @Override
    public Result<Page<PackageDTO>> getExportPage(PackageParam param) {
        if (!checkWarpContinueQueryParam(param)) {
            return Result.success(new Page<>());
        }
        Page<Package> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(param);
        IPage<Package> packPage = packageService.page(page, queryWrapper);
        Page<PackageDTO> result = ConverterUtil.convertPage(packPage, PackageDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<PackageDTO>> getPage(PackageParam param) {
        DefaultSortUtil.formatSortSort(param);

        Page<Package> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(param);
        if (param.getSelectColumn()) {
            queryWrapper.select(BaseEntity::getId, Package::getCargoCode, Package::getShipmentOrderCode, Package::getPackageCode);
        }
        IPage<Package> packPage = packageService.page(page, queryWrapper);
        Page<PackageDTO> result = ConverterUtil.convertPage(packPage, PackageDTO.class);
        return Result.success(result);
    }

    private boolean isQueryShipmentOrder(PackageParam param) {
        if (!StringUtils.isEmpty(param.getOrderType())) {
            return true;
        }
        if (!CollectionUtils.isEmpty(param.getTradeNoList())) {
            return true;
        }
        if (!StringUtils.isEmpty(param.getSaleShop())) {
            return true;
        }
        if (!StringUtils.isEmpty(param.getReceiverMan())) {
            return true;
        }
        if (!StringUtils.isEmpty(param.getReceiverTel())) {
            return true;
        }
        if (!StringUtils.isEmpty(param.getReceiverProv())) {
            return true;
        }
        if (!StringUtils.isEmpty(param.getReceiverCity())) {
            return true;
        }
        if (!StringUtils.isEmpty(param.getReceiverArea())) {
            return true;
        }
        if ((param.getStartExpOutStockDate() != null && param.getStartExpOutStockDate() > 0) || (param.getEndExpOutStockDate() != null && param.getEndExpOutStockDate() > 0)) {
            return true;
        }
        if ((param.getOrdCreatedTimeStart() != null && param.getOrdCreatedTimeStart() > 0) || (param.getOrdCreatedTimeEnd() != null && param.getOrdCreatedTimeEnd() > 0)) {
            return true;
        }
        if (!StringUtils.isEmpty(param.getPreSaleType())) {
            return true;
        }
        if ((param.getExpShipTimeStart() != null && param.getExpShipTimeStart() > 0) || (param.getExpShipTimeEnd() != null && param.getExpShipTimeEnd() > 0)) {
            return true;
        }
        return false;
    }

    /**
     * 导入与页面查询保持一致，同一参数处理逻辑
     *
     * @param param
     * @return
     */
    private boolean checkWarpContinueQueryParam(PackageParam param) {
        List<String> shipmentOrderCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(param.getShipmentOrderCodeList())) {
            shipmentOrderCodeList.addAll(param.getShipmentOrderCodeList());
        }
        if (isQueryShipmentOrder(param)) {
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setOrderType(param.getOrderType());
            shipmentOrderParam.setTradeNoList(param.getTradeNoList());
            shipmentOrderParam.setSaleShop(param.getSaleShop());
            shipmentOrderParam.setStartExpOutStockDate(param.getStartExpOutStockDate());
            shipmentOrderParam.setEndExpOutStockDate(param.getEndExpOutStockDate());
            shipmentOrderParam.setReceiverMan(param.getReceiverMan());
            shipmentOrderParam.setReceiverTel(param.getReceiverTel());
            shipmentOrderParam.setReceiverProv(param.getReceiverProv());
            shipmentOrderParam.setReceiverCity(param.getReceiverCity());
            shipmentOrderParam.setReceiverArea(param.getReceiverArea());
            shipmentOrderParam.setCreatedTimeStart(param.getOrdCreatedTimeStart());
            shipmentOrderParam.setCreatedTimeEnd(param.getOrdCreatedTimeEnd());

            shipmentOrderParam.setPreSaleType(param.getPreSaleType());
            shipmentOrderParam.setExpShipTimeStart(param.getExpShipTimeStart());
            shipmentOrderParam.setExpShipTimeEnd(param.getExpShipTimeEnd());

            LambdaQueryWrapper<ShipmentOrder> shipOrderWrapper = shipmentOrderUtil.getQueryWrapper(shipmentOrderParam);
            shipOrderWrapper.select(ShipmentOrder::getShipmentOrderCode);
            List<String> listShipOrderCode = shipmentOrderService.list(shipOrderWrapper).stream().map(s -> s.getShipmentOrderCode()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shipmentOrderCodeList)) {
                shipmentOrderCodeList = listShipOrderCode;
            } else {
                shipmentOrderCodeList.retainAll(listShipOrderCode);
            }
            if (CollectionUtils.isEmpty(shipmentOrderCodeList)) {

                return false;
            }
        }
        // 包材
        if (StrUtil.isNotBlank(param.getMaterialUpcCode())) {
            QueryWrapper<ShipmentOrderMaterial> queryWrapper = new QueryWrapper<>();
            LambdaQueryWrapper<ShipmentOrderMaterial> lambda = queryWrapper.lambda();
            lambda.eq(StrUtil.isNotBlank(param.getMaterialUpcCode()), ShipmentOrderMaterial::getRecPackUpcCode, param.getMaterialUpcCode());
            List<ShipmentOrderMaterial> shipmentOrderMaterials = shipmentOrderMaterialService.list(lambda);
            List<String> collect = shipmentOrderMaterials.stream().map(ShipmentOrderMaterial::getShipmentOrderCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(shipmentOrderCodeList)) {
                shipmentOrderCodeList = collect;
            } else {
                shipmentOrderCodeList.retainAll(collect);
            }
            if (CollectionUtil.isEmpty(shipmentOrderCodeList)) {
                return false;
            }
        }

        /**
         * 根据条件查询出出库单据号
         */
        param.setShipmentOrderCodeList(shipmentOrderCodeList);
        List<String> packageCodes = new ArrayList<>();
        if (param.getPackageCodeList() != null && param.getPackageCodeList().size() > 0) {
            packageCodes.addAll(param.getPackageCodeList());
        }
        if (!CollectionUtils.isEmpty(param.getSkuCodeList()) || !CollectionUtils.isEmpty(param.getUpcCodeList())) {
            LambdaQueryWrapper<PackageDetail> listDetailQueryWrapper = packageDetailUtil.getQueryWrapper(param);
            List<String> listPackageCodes = ipackageDetailService.list(listDetailQueryWrapper).stream().map(s -> s.getPackageCode()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageCodes)) {
                packageCodes = listPackageCodes;
            } else {
                packageCodes.retainAll(listPackageCodes);
            }
            if (CollectionUtils.isEmpty(packageCodes)) {
                return false;
            }
        }
        if (!StringUtils.isEmpty(param.getPickCode()) || !CollectionUtils.isEmpty(param.getPickCodeList())) {
            QueryWrapper<PickDetail> detailQueryWrapper = new QueryWrapper();
            LambdaQueryWrapper<PickDetail> lambdaQueryWrapper = detailQueryWrapper.lambda();
            lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getPickCodeList()), PickDetail::getPickCode, param.getPickCodeList());
            lambdaQueryWrapper.eq(!StringUtils.isEmpty(param.getPickCode()), PickDetail::getPickCode, param.getPickCode());
            List<PickDetail> pickDetails = pickDetailService.list(detailQueryWrapper);
            if (CollectionUtils.isEmpty(pickDetails)) {
                return false;
            }
            if (CollectionUtils.isEmpty(packageCodes)) {
                packageCodes = pickDetails.stream().map(PickDetail::getPackageCode).collect(Collectors.toList());
            } else {
                packageCodes.retainAll(pickDetails.stream().map(PickDetail::getPackageCode).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(packageCodes)) {
                return false;
            }
        }

        param.setPackageCodeList(packageCodes);
        return true;
    }

    @Override
    public Result<Page<PackageDTO>> queryPage(PackageParam param) {
        if (!checkWarpContinueQueryParam(param)) {
            return Result.success(packageUtil.<PackageDTO>emptyPage(param));
        }
        Page<Package> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(param);
        IPage<Package> packageIPage = ipackageService.page(page, queryWrapper);
        Page<PackageDTO> result = ConverterUtil.convertPage(packageIPage, PackageDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<PackageDTO> getDetailAndEntityDetail(PackageParam param) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(param);
        Package packageInfo = ipackageService.getOne(queryWrapper);
        PackageDTO result = null;
        if (packageInfo != null) {
            QueryWrapper<PackageDetail> packageDetailQueryWrapper = new QueryWrapper();
            LambdaQueryWrapper<PackageDetail> lambdaQueryWrapper = packageDetailQueryWrapper.lambda();
            lambdaQueryWrapper.eq(!StringUtils.isEmpty(packageInfo.getPackageCode()), PackageDetail::getPackageCode, packageInfo.getPackageCode());
            List<PackageDetail> listDetail = ipackageDetailService.list(lambdaQueryWrapper);
            List<PackageDetailDTO> listDtoDetail = ConverterUtil.convertList(listDetail, PackageDetailDTO.class);
            result = ConverterUtil.convert(packageInfo, PackageDTO.class);
            result.setListDetail(listDtoDetail);
            QueryWrapper<PackageLog> packageLogQueryWrapper = new QueryWrapper();
            LambdaQueryWrapper<PackageLog> loglambdaQueryWrapper = packageLogQueryWrapper.lambda();
            loglambdaQueryWrapper.eq(!StringUtils.isEmpty(packageInfo.getPackageCode()), PackageLog::getPackageCode, packageInfo.getPackageCode());
            List<PackageLog> logListDetail = ipackageLogService.list(loglambdaQueryWrapper);
            List<PackageLogDTO> logListDtoDetail = ConverterUtil.convertList(logListDetail, PackageLogDTO.class);
            result.setLogListDetail(logListDtoDetail);
        }
        return Result.success(result);
    }


    @Override
    public Result<List<CollectWaveDTO>> getCollectWaveList(CollectWaveBillParam param) {
        //----------------------------old--------------------------------
        //过滤删除数据
//        param.setDeleted(1);
//        List<CollectWave> packageDTOS = collectWaveMapper.getCollectWaveList(ConverterUtil.convert(param, CollectWaveParam.class));
//        return Result.success(ConverterUtil.convertList(packageDTOS, CollectWaveDTO.class));
        //----------------------------old--------------------------------
        QueryWrapper<Package> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .eq(!ObjectUtils.isEmpty(param.getPackageCode()), Package::getPackageCode, param.getPackageCode())
                .eq(!ObjectUtils.isEmpty(param.getShipmentOrderCode()), Package::getShipmentOrderCode, param.getShipmentOrderCode())
                .eq(!ObjectUtils.isEmpty(param.getPoNo()), Package::getPoNo, param.getPoNo())
                .eq(!ObjectUtils.isEmpty(param.getSoNo()), Package::getSoNo, param.getSoNo())
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), Package::getCargoCode, param.getCargoCode())
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), Package::getWarehouseCode, param.getWarehouseCode())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), Package::getStatus, param.getStatus())
                .eq(!ObjectUtils.isEmpty(param.getIsPre()), Package::getIsPre, param.getIsPre())
                .eq(!ObjectUtils.isEmpty(param.getCollectStatus()), Package::getCollectStatus, param.getCollectStatus())
                .eq(!ObjectUtils.isEmpty(param.getCarrierCode()), Package::getCarrierCode, param.getCarrierCode())
                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), Package::getExpressNo, param.getExpressNo())
                .eq(!ObjectUtils.isEmpty(param.getSaleShopId()), Package::getSaleShopId, param.getSaleShopId())
                .eq(!ObjectUtils.isEmpty(param.getSalePlatform()), Package::getSalePlatform, param.getSalePlatform())
                .in(!ObjectUtils.isEmpty(param.getSalePlatformList()), Package::getSalePlatform, param.getSalePlatformList())
                .eq(!ObjectUtils.isEmpty(param.getPackageStruct()), Package::getPackageStruct, param.getPackageStruct())
                .in(!ObjectUtils.isEmpty(param.getPackageStructList()), Package::getPackageStruct, param.getPackageStructList())
                .eq(!ObjectUtils.isEmpty(param.getWaveCode()), Package::getWaveCode, param.getWaveCode())
                .in(!CollectionUtils.isEmpty(param.getStatusList()), Package::getStatus, param.getStatusList())
                .in(!CollectionUtils.isEmpty(param.getPackageCodeList()), Package::getPackageCode, param.getPackageCodeList())
                .notIn(!CollectionUtils.isEmpty(param.getNoPackageCodeList()), Package::getPackageCode, param.getNoPackageCodeList())
                .eq(ObjectUtils.isEmpty(param.getDeleted()), Package::getDeleted, Deleted.NORMAL.getCode())
                .in(!ObjectUtils.isEmpty(param.getPoNoList()), Package::getPoNo, param.getPoNoList())
                .in(!ObjectUtils.isEmpty(param.getCargoCodeList()), Package::getCargoCode, param.getCargoCodeList())
                .in(!ObjectUtils.isEmpty(param.getCarrierCodeList()), Package::getCarrierCode, param.getCarrierCodeList())
                .in(!ObjectUtils.isEmpty(param.getExpressNoList()), Package::getExpressNo, param.getExpressNoList())
                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), Package::getExpressNo, param.getExpressNo())
                .eq(!ObjectUtils.isEmpty(param.getBusinessType()), Package::getBusinessType, param.getBusinessType())
                .in(!ObjectUtils.isEmpty(param.getSoNoList()), Package::getSoNo, param.getSoNoList())
                .in(!ObjectUtils.isEmpty(param.getShipmentOrderCodeList()), Package::getShipmentOrderCode, param.getShipmentOrderCodeList())
                .eq(!ObjectUtils.isEmpty(param.getExpressBranch()), Package::getExpressBranch, param.getExpressBranch())
                .eq(param.getExpressBranchNotBlank() != null, Package::getExpressBranch, param.getExpressBranchNotBlank())
                .in(!ObjectUtils.isEmpty(param.getIdList()), Package::getId, param.getIdList())
        ;
        if (!CollectionUtils.isEmpty(param.getSkuCodeLikeList())) {
            List<String> likeSqlList = new ArrayList<>();
            for (String skuCodeLike : param.getSkuCodeLikeList()) {
//                queryWrapper.lambda().or().like(Package::getAnalysisSku,skuCodeLike);
                likeSqlList.add(" analysis_sku LIKE '%" + skuCodeLike + "%' ");
            }
            if (!CollectionUtils.isEmpty(likeSqlList)) {
                queryWrapper.apply("  (" + likeSqlList.stream().collect(Collectors.joining(" or ")) + " ) ");
            }
        }

        if (param.getOrderTag() != null && param.getOrderTag() > 0) {
            queryWrapper.apply(" order_tag = (order_tag|" + param.getOrderTag() + ")");
        }
        if (param.getNoContainOrderTag() != null && param.getNoContainOrderTag() != 0) {
            queryWrapper.apply(" order_tag = (order_tag&" + param.getNoContainOrderTag() + ")");
        }
        queryWrapper.select(" warehouse_code,cargo_code,carrier_code,business_type,sale_platform,sale_shop_id,express_branch,express_branch_name,count(1) as num  ");
        queryWrapper.groupBy("  warehouse_code,cargo_code,carrier_code,business_type,sale_platform,sale_shop_id,express_branch ");
        List<Map<String, Object>> mapList = packageService.listMaps(queryWrapper);
        if (CollectionUtils.isEmpty(mapList)) {
            return Result.success(new ArrayList<>());
        }
        List<CollectWaveDTO> collectWaveDTOList = mapList.stream().map(a -> {
            CollectWaveDTO collectWaveDTO = BeanUtil.mapToBean(a, CollectWaveDTO.class, true, CopyOptions.create().ignoreNullValue());
            return collectWaveDTO;
        }).collect(Collectors.toList());
        return Result.success(collectWaveDTOList);
    }

//    @Override
//    public Result<List<PackageDetailDTO>> getCollectWavePackageDetailList(PackageDetailParam param) {
//        QueryWrapper<PackageDetail> packageDetailQueryWrapper = new QueryWrapper();
//        LambdaQueryWrapper<PackageDetail> lambdaQueryWrapper = packageDetailQueryWrapper.lambda();
//        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getSkuCodeList()), PackageDetail::getSkuCode, param.getSkuCodeList());
//        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getStatusList()), PackageDetail::getStatus, param.getStatusList());
//        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getPackageCodeList()), PackageDetail::getPackageCode, param.getPackageCodeList());
//        //TODO 库区类型
//        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getZoneTypeList()), PackageDetail::getZoneType, param.getZoneTypeList());
//        //先处理有批次号的
//        lambdaQueryWrapper.orderByDesc(PackageDetail::getSkuLotNo, PackageDetail::getPackageCode);
//        List<PackageDetail> listDetail = ipackageDetailService.list(lambdaQueryWrapper);
//        List<PackageDetailDTO> listDtoDetail = ConverterUtil.convertList(listDetail, PackageDetailDTO.class);
//        return Result.success(listDtoDetail);
//    }
//
//    @Override
//    public Result<List<PackageDTO>> getCollectWaveCommitList(CollectWaveBillParam param) {
//        //TODO组装查询条件
//        LambdaQueryWrapper<Package> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(!StringUtils.isEmpty(param.getPackageCode()), Package::getPackageCode, param.getPackageCode());
//        List<Package> packages = ipackageService.list(wrapper);
//        return Result.success(ConverterUtil.convertList(packages, PackageDTO.class));
//    }

    @Override
    public Result<List<PackageDetailDTO>> getPackageDetailListByCode(PackageParam param) {
        QueryWrapper<PackageDetail> packageDetailQueryWrapper = new QueryWrapper();
        LambdaQueryWrapper<PackageDetail> lambdaQueryWrapper = packageDetailQueryWrapper.lambda();
        lambdaQueryWrapper.eq(!StringUtils.isEmpty(param.getPackageCode()), PackageDetail::getPackageCode, param.getPackageCode());
        List<PackageDetail> listDetail = ipackageDetailService.list(lambdaQueryWrapper);
        List<PackageDetailDTO> listDtoDetail = ConverterUtil.convertList(listDetail, PackageDetailDTO.class);
        return Result.success(listDtoDetail);
    }

    @Override
    public Result<PackageDTO> getPackageByCode(PackageParam param) {
        LambdaQueryWrapper<Package> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(!StringUtils.isEmpty(param.getPackageCode()), Package::getPackageCode, param.getPackageCode());
        Package entity = ipackageService.getOne(wrapper);
        return Result.success(ConverterUtil.convert(entity, PackageDTO.class));
    }

    @Override
    @Transactional
    public Result<Boolean> submitUpdateCollect(List<PackageDTO> packageDTOS) {
        for (PackageDTO entity : packageDTOS) {
            Boolean result = false;
            for (PackageDetail packageDetail : ConverterUtil.convertList(entity.getListDetail(), PackageDetail.class)) {
                result = ipackageDetailService.updateById(packageDetail);
                if (!result) {
                    throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
                }
            }
            result = ipackageService.updateById(ConverterUtil.convert(entity, Package.class));
            if (!result) {
                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
        }
        return Result.success(true);
    }

    @Override
    public Result<List<PackageDTO>> getCollectWaveList(PackageParam param) {
        LambdaQueryWrapper<Package> wrapper = packageUtil.getQueryWrapper(param);
        return Result.success(ConverterUtil.convertList(ipackageService.list(wrapper), PackageDTO.class));
    }

    @Override
    public Result<List<PackageDTO>> getAllocationWave(PackageParam param) {
        LambdaQueryWrapper<Package> wrapper = packageUtil.getQueryWrapper(param);
        wrapper.orderByAsc(Package::getPackageStruct, Package::getAnalysisSku, Package::getCreatedTime);
        return Result.success(ConverterUtil.convertList(ipackageService.list(wrapper), PackageDTO.class));
    }

    @Override
    @Transactional
    public Result<Boolean> savePackageLog(PackageLogDTO packageLogDTO) {
        Boolean result = ipackageLogService.save(ConverterUtil.convert(packageLogDTO, PackageLog.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> savePackLogList(List<PackageLogDTO> packageLogDTOList) {
        Boolean result = ipackageLogService.saveBatch(ConverterUtil.convertList(packageLogDTOList, PackageLog.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<List<PackageDTO>> getNotifyPackageList(NotifyParams notifyParams) {
        QueryWrapper<Package> packageQueryWrapper = new QueryWrapper();
        LambdaQueryWrapper<Package> lambdaQueryWrapper = packageQueryWrapper.lambda();
        lambdaQueryWrapper.in(Package::getStatus, notifyParams.getStatusList())
                .in(Package::getNotifyStatus, notifyParams.getNodifyStatusList());
        lambdaQueryWrapper.and(wrapper -> wrapper.isNull(Package::getNotifyCount).or().lt(Package::getNotifyCount, notifyParams.getNotifyCount()));
        return Result.success(ConverterUtil.convertList(ipackageService.list(lambdaQueryWrapper), PackageDTO.class));
    }

    @Override
    public Result<Boolean> modifyPretreatmentStatus(PackageParam packageParam) {
        if (StringUtils.isEmpty(packageParam.getId()) &&
                CollectionUtils.isEmpty(packageParam.getIdList()) &&
                StringUtils.isEmpty(packageParam.getPackageCode()) &&
                CollectionUtils.isEmpty(packageParam.getPackageCodeList())
        ) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<Package> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(packageParam.getId()), Package::getId, packageParam.getId())
                .eq(!StringUtils.isEmpty(packageParam.getPackageCode()), Package::getPackageCode, packageParam.getPackageCode())
                .eq(!StringUtils.isEmpty(packageParam.getPretreatmentLockStatus()), Package::getPretreatmentStatus, packageParam.getPretreatmentLockStatus())
                .in(!CollectionUtils.isEmpty(packageParam.getIdList()), Package::getId, packageParam.getIdList())
                .in(!CollectionUtils.isEmpty(packageParam.getPackageCodeList()), Package::getPackageCode, packageParam.getPackageCodeList())
        ;
        Package packaage = new Package();
        packaage.setStatus(packageParam.getStatus());
        packaage.setPretreatmentStatus(packageParam.getPretreatmentStatus());
        packaage.setInterceptCancelDate(packageParam.getInterceptCancelDate());
        Boolean result = ipackageService.update(packaage, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<List<PackageDTO>> getAppointMultipleParam(PackageParam packageParam, List<String> tableFields) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(packageParam);
        if (!CollectionUtils.isEmpty(tableFields)) {
            queryWrapper.select(Package.class, i -> tableFields.contains(i.getColumn()));
        }
        List<Package> packList = packageService.list(queryWrapper);
        List<PackageDTO> result = ConverterUtil.convertList(packList, PackageDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<PackageDetailDTO>> getCollectWavePackageDetailListAppointColumn(PackageDetailParam packageDetailParam, List<String> filedList) {
        QueryWrapper<PackageDetail> packageDetailQueryWrapper = new QueryWrapper();
        LambdaQueryWrapper<PackageDetail> lambdaQueryWrapper = packageDetailQueryWrapper.lambda();
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(packageDetailParam.getSkuCodeList()), PackageDetail::getSkuCode, packageDetailParam.getSkuCodeList());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(packageDetailParam.getStatusList()), PackageDetail::getStatus, packageDetailParam.getStatusList());
        lambdaQueryWrapper.eq(!StringUtils.isEmpty(packageDetailParam.getCargoCode()), PackageDetail::getCargoCode, packageDetailParam.getCargoCode());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(packageDetailParam.getPackageCodeList()), PackageDetail::getPackageCode, packageDetailParam.getPackageCodeList());

        if (!CollectionUtils.isEmpty(filedList)) {
            lambdaQueryWrapper.select(PackageDetail.class, i -> filedList.contains(i.getColumn()));
        }
        if (packageDetailParam.getIsAppointSkuLotNoNotEmpty() != null && packageDetailParam.getIsAppointSkuLotNoNotEmpty()) {
            lambdaQueryWrapper.apply(" sku_lot_no !='' ");
        }
        List<PackageDetail> listDetail = ipackageDetailService.list(lambdaQueryWrapper);
        List<PackageDetailDTO> listDtoDetail = ConverterUtil.convertList(listDetail, PackageDetailDTO.class);
        return Result.success(listDtoDetail);
    }

    @Override
    public Result<Integer> getWaveNavigationHead(PackageParam packageParam) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(packageParam);
        Integer count = packageService.count(queryWrapper);
        return Result.success(count);
    }

    @Override
    public Result<List<Map<String, Object>>> getWaveNavigationBodyGroup(WaveNavigationParam waveNavigationParam) {
        if (StringUtils.isEmpty(waveNavigationParam.getLevel()) || StringUtils.isEmpty(waveNavigationParam.getStatus())
                || StringUtils.isEmpty(waveNavigationParam.getCollectStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        QueryWrapper<Package> queryWrapper = new QueryWrapper<>();
        //必填条件
        queryWrapper.lambda().eq(Package::getStatus, waveNavigationParam.getStatus());
        queryWrapper.lambda().eq(Package::getCollectStatus, waveNavigationParam.getCollectStatus());
        queryWrapper.lambda().eq(Package::getBusinessType, waveNavigationParam.getBusinessType());
        queryWrapper.lambda().eq(Package::getIsPre, waveNavigationParam.getIsPre());
        if (waveNavigationParam.getCreatedTimeStart() != null && waveNavigationParam.getCreatedTimeStart() > 0) {
            queryWrapper.lambda().gt(Package::getCreatedTime, waveNavigationParam.getCreatedTimeStart());
        }
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getPackageStructList()), Package::getPackageStruct, waveNavigationParam.getPackageStructList());
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getPackCodeList()), Package::getPackageCode, waveNavigationParam.getPackCodeList());
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getIdList()), Package::getId, waveNavigationParam.getIdList());
        //非必填
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getCargoCodeList()), Package::getCargoCode, waveNavigationParam.getCargoCodeList());
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getSalePlatformList()), Package::getSalePlatform, waveNavigationParam.getSalePlatformList());
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getCarrierCodeList()), Package::getCarrierCode, waveNavigationParam.getCarrierCodeList());
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getSaleShopList()), Package::getSaleShopId, waveNavigationParam.getSaleShopList());
        //处理时间 gt 大于 ---- lt 小于
        //开始时间非0
        if (!ObjectUtils.isEmpty(waveNavigationParam.getExpShipTimeStart())) {
            queryWrapper.lambda().apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND dt_shipment_order.exp_ship_time >" + waveNavigationParam.getExpShipTimeStart() + ")");
        }
        if (!ObjectUtils.isEmpty(waveNavigationParam.getExpShipTimeEnd())) {
            //结束时间非0
            queryWrapper.lambda().apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND dt_shipment_order.exp_ship_time <" + waveNavigationParam.getExpShipTimeEnd() + ")");
        }
        if (!ObjectUtils.isEmpty(waveNavigationParam.getExpShipTimeEmpty())) {
            //时间0
            queryWrapper.lambda().apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND (dt_shipment_order.exp_ship_time =" + waveNavigationParam.getExpShipTimeEmpty() + " or dt_shipment_order.exp_ship_time is null ))");
        }
        //处理网点
        if (!CollectionUtils.isEmpty(waveNavigationParam.getExpressBranchList())) {
//            queryWrapper.lambda().apply(" express_branch in (" + buildExpressBranchListToDB(waveNavigationParam.getExpressBranchList()) + ")");
            queryWrapper.lambda().inSql(Package::getExpressBranch, buildExpressBranchListToDB(waveNavigationParam.getExpressBranchList()));
        }

        if (waveNavigationParam.getLevel().equalsIgnoreCase(WaveNavigationLevelEnum.SALE_PLATFORM.getCode())) {
            queryWrapper.select(" sale_platform as code,count(id) as num ");
            queryWrapper.lambda().groupBy(Package::getSalePlatform);
        }
        if (waveNavigationParam.getLevel().equalsIgnoreCase(WaveNavigationLevelEnum.CARGO.getCode())) {
            queryWrapper.select(" cargo_code as code,count(id) as num ");
            queryWrapper.lambda().groupBy(Package::getCargoCode);
        }
        if (waveNavigationParam.getLevel().equalsIgnoreCase(WaveNavigationLevelEnum.CARRIER.getCode())) {
            queryWrapper.select(" carrier_code as code,express_branch as expressBranch,express_branch_name as expressBranchName,count(id) as num ");
            queryWrapper.lambda().groupBy(Package::getCarrierCode, Package::getExpressBranch);
        }
        if (waveNavigationParam.getLevel().equalsIgnoreCase(WaveNavigationLevelEnum.SALE_SHOP.getCode())) {
            queryWrapper.select(" sale_shop_id as code,count(id) as num ");
            queryWrapper.lambda().groupBy(Package::getSaleShopId);
        }
        if (waveNavigationParam.getLevel().equalsIgnoreCase(WaveNavigationLevelEnum.ANALYSIS_SKU.getCode())) {
            queryWrapper.select(" cargo_code as code,analysis_sku as analysis_sku,count(id) as num ");
            queryWrapper.lambda().groupBy(Package::getCargoCode, Package::getAnalysisSku);
        }
        List<Map<String, Object>> mapList = packageService.listMaps(queryWrapper);
        return Result.success(mapList);
    }

    /**
     * @param expressBranchList
     * @return java.lang.String
     * @author: WuXian
     * description:  处理网点
     * create time: 2022/3/22 17:56
     */
    private String buildExpressBranchListToDB(List<String> expressBranchList) {
        List<String> expressBranchNewList = new ArrayList<>();
        expressBranchNewList.addAll(expressBranchList);
        String str = "";
        if (CollectionUtils.isEmpty(expressBranchList)) {
            return str;
        }
        if (expressBranchNewList.contains("''")) {
            expressBranchNewList.removeIf(a -> a.equalsIgnoreCase("''"));
            if (!CollectionUtils.isEmpty(expressBranchNewList)) {
                str = "'" + expressBranchNewList.stream().collect(Collectors.joining("','")) + "'" + ",''";
            } else {
                str = "''";
            }
        } else {
            str = "'" + expressBranchNewList.stream().collect(Collectors.joining("','")) + "'";
        }
        return str;
    }

    @Override
    public Result<Integer> getWaveNavigationBodyByLastShipTime(WaveNavigationParam waveNavigationParam) {
        if (StringUtils.isEmpty(waveNavigationParam.getLevel()) || StringUtils.isEmpty(waveNavigationParam.getStatus())
                || StringUtils.isEmpty(waveNavigationParam.getCollectStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        QueryWrapper<Package> queryWrapper = new QueryWrapper<>();
        //必填条件
        queryWrapper.lambda().eq(Package::getStatus, waveNavigationParam.getStatus());
        queryWrapper.lambda().eq(Package::getCollectStatus, waveNavigationParam.getCollectStatus());
        queryWrapper.lambda().eq(Package::getBusinessType, waveNavigationParam.getBusinessType());
        queryWrapper.lambda().eq(Package::getIsPre, waveNavigationParam.getIsPre());
        if (waveNavigationParam.getCreatedTimeStart() != null && waveNavigationParam.getCreatedTimeStart() > 0) {
            queryWrapper.lambda().gt(Package::getCreatedTime, waveNavigationParam.getCreatedTimeStart());
        }
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getPackageStructList()), Package::getPackageStruct, waveNavigationParam.getPackageStructList());
        //非必填
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getPackCodeList()), Package::getPackageCode, waveNavigationParam.getPackCodeList());
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getIdList()), Package::getId, waveNavigationParam.getIdList());

        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getCargoCodeList()), Package::getCargoCode, waveNavigationParam.getCargoCodeList());
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getSalePlatformList()), Package::getSalePlatform, waveNavigationParam.getSalePlatformList());
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getCarrierCodeList()), Package::getCarrierCode, waveNavigationParam.getCarrierCodeList());
        queryWrapper.lambda().in(!ObjectUtils.isEmpty(waveNavigationParam.getSaleShopList()), Package::getSaleShopId, waveNavigationParam.getSaleShopList());
        //处理时间 gt 大于 ---- lt 小于
        //处理时间 gt 大于 ---- lt 小于

        //开始时间非0
        if (!ObjectUtils.isEmpty(waveNavigationParam.getExpShipTimeStart())) {
            queryWrapper.lambda().apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND dt_shipment_order.exp_ship_time >" + waveNavigationParam.getExpShipTimeStart() + ")");
        }
        if (!ObjectUtils.isEmpty(waveNavigationParam.getExpShipTimeEnd())) {
            //结束时间非0
            queryWrapper.lambda().apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND dt_shipment_order.exp_ship_time <" + waveNavigationParam.getExpShipTimeEnd() + ")");
        }
        if (!ObjectUtils.isEmpty(waveNavigationParam.getExpShipTimeEmpty())) {
            //时间0
            queryWrapper.lambda().apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND (dt_shipment_order.exp_ship_time =" + waveNavigationParam.getExpShipTimeEmpty() + " or dt_shipment_order.exp_ship_time is null ))");
        }
        //处理网点
        if (!CollectionUtils.isEmpty(waveNavigationParam.getExpressBranchList())) {
            queryWrapper.lambda().apply(" express_branch in (" + buildExpressBranchListToDB(waveNavigationParam.getExpressBranchList()) + ")");
        }
        int count = packageService.count(queryWrapper);
        return Result.success(count);
    }

    @Override
    public Result<PackageDTO> getWaveNavigationByAnalysisSkuLimitOne(PackageParam packageParam) {
        LambdaQueryWrapper<Package> wrapper = packageUtil.getQueryWrapper(packageParam);
        wrapper.last(" limit 1");
        Package aPackage = ipackageService.getOne(wrapper);
        return Result.success(ConverterUtil.convert(aPackage, PackageDTO.class));
    }

    @Override
    public Result<List<CollectWaveDTO>> getWaveNavigationCollectGroup(PackageParam packageParam) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(packageParam);
        //开始时间非0
        if (!ObjectUtils.isEmpty(packageParam.getExpShipTimeStart())) {
            queryWrapper.apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND dt_shipment_order.exp_ship_time >" + packageParam.getExpShipTimeStart() + ")");
        }
        if (!ObjectUtils.isEmpty(packageParam.getExpShipTimeEnd())) {
            //结束时间非0
            queryWrapper.apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND dt_shipment_order.exp_ship_time <" + packageParam.getExpShipTimeEnd() + ")");
        }
        if (!ObjectUtils.isEmpty(packageParam.getExpShipTimeEmpty())) {
            //时间0
            queryWrapper.apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND (dt_shipment_order.exp_ship_time =" + packageParam.getExpShipTimeEmpty() + " or dt_shipment_order.exp_ship_time is null ))");
        }
        //处理网点
        if (!CollectionUtils.isEmpty(packageParam.getExpressBranchList())) {
            queryWrapper.apply(" express_branch in (" + buildExpressBranchListToDB(packageParam.getExpressBranchList()) + ")");
        }
        //"分组 货主，快递公司，快递网点，平台"
        queryWrapper.select(Package::getWarehouseCode, Package::getCargoCode, Package::getCarrierCode, Package::getExpressBranch, Package::getSalePlatform);
        queryWrapper.groupBy(Package::getWarehouseCode, Package::getCargoCode, Package::getCarrierCode, Package::getExpressBranch, Package::getSalePlatform);

        List<Map<String, Object>> mapList = packageService.listMaps(queryWrapper);
        if (CollectionUtils.isEmpty(mapList)) {
            return Result.success(new ArrayList<>());
        }
        List<CollectWaveDTO> collectWaveDTOList = mapList.stream().map(a -> {
            CollectWaveDTO collectWaveDTO = BeanUtil.mapToBean(a, CollectWaveDTO.class, true, CopyOptions.create().ignoreNullValue());
            return collectWaveDTO;
        }).collect(Collectors.toList());
        return Result.success(collectWaveDTOList);
    }

    @Override
    public Result<List<PackageDTO>> getWaveNavigationCollectWaveList(PackageParam packageParam) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(packageParam);
        //开始时间非0
        if (!ObjectUtils.isEmpty(packageParam.getExpShipTimeStart())) {
            queryWrapper.apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND dt_shipment_order.exp_ship_time >" + packageParam.getExpShipTimeStart() + ")");
        }
        if (!ObjectUtils.isEmpty(packageParam.getExpShipTimeEnd())) {
            //结束时间非0
            queryWrapper.apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND dt_shipment_order.exp_ship_time <" + packageParam.getExpShipTimeEnd() + ")");
        }
        if (!ObjectUtils.isEmpty(packageParam.getExpShipTimeEmpty())) {
            //时间0
            queryWrapper.apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
                    " AND (dt_shipment_order.exp_ship_time =" + packageParam.getExpShipTimeEmpty() + " or dt_shipment_order.exp_ship_time is null ))");
        }
        //处理网点
        if (!CollectionUtils.isEmpty(packageParam.getExpressBranchList())) {
            queryWrapper.apply(" express_branch in (" + buildExpressBranchListToDB(packageParam.getExpressBranchList()) + ")");
        }
        List<Package> packList = packageService.list(queryWrapper);
        List<PackageDTO> packageDTOList = ConverterUtil.convertList(packList, PackageDTO.class);
        return Result.success(packageDTOList);
    }

    @Override
    public Result<List<CollectWaveAnalysisDTO>> getWaveCollectFrontAnalysisGroupBy(PackageParam param) {
        QueryWrapper<Package> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .gt(!StringUtils.isEmpty(param.getCreatedTimeStart()), Package::getCreatedTime, param.getCreatedTimeStart())
                .eq(!ObjectUtils.isEmpty(param.getPackageCode()), Package::getPackageCode, param.getPackageCode())
                .eq(!ObjectUtils.isEmpty(param.getShipmentOrderCode()), Package::getShipmentOrderCode, param.getShipmentOrderCode())
                .eq(!ObjectUtils.isEmpty(param.getPoNo()), Package::getPoNo, param.getPoNo())
                .eq(!ObjectUtils.isEmpty(param.getSoNo()), Package::getSoNo, param.getSoNo())
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), Package::getCargoCode, param.getCargoCode())
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), Package::getWarehouseCode, param.getWarehouseCode())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), Package::getStatus, param.getStatus())
                .eq(!ObjectUtils.isEmpty(param.getIsPre()), Package::getIsPre, param.getIsPre())
                .eq(!ObjectUtils.isEmpty(param.getCollectStatus()), Package::getCollectStatus, param.getCollectStatus())
                .eq(!ObjectUtils.isEmpty(param.getCarrierCode()), Package::getCarrierCode, param.getCarrierCode())
                .eq(!ObjectUtils.isEmpty(param.getCarrierName()), Package::getCarrierName, param.getCarrierName())
                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), Package::getExpressNo, param.getExpressNo())
                .eq(!ObjectUtils.isEmpty(param.getSaleShopId()), Package::getSaleShopId, param.getSaleShopId())
                .eq(!ObjectUtils.isEmpty(param.getSalePlatform()), Package::getSalePlatform, param.getSalePlatform())
                .eq(!Objects.isNull(param.getSalePlatformT()), Package::getSalePlatform, param.getSalePlatformT())
                .notIn(!CollectionUtils.isEmpty(param.getNotOtherSalePlatformCodeList()), Package::getSalePlatform, param.getNotOtherSalePlatformCodeList())
                .eq(!ObjectUtils.isEmpty(param.getAnalysisSku()), Package::getAnalysisSku, param.getAnalysisSku())
                .eq(!ObjectUtils.isEmpty(param.getPackageStruct()), Package::getPackageStruct, param.getPackageStruct())
                .in(!ObjectUtils.isEmpty(param.getPackageStructList()), Package::getPackageStruct, param.getPackageStructList())
                .eq(!ObjectUtils.isEmpty(param.getWaveCode()), Package::getWaveCode, param.getWaveCode())
                .gt(!ObjectUtils.isEmpty(param.getUpdatedTimeStart()), Package::getUpdatedTime, param.getUpdatedTimeStart())
                .le(!ObjectUtils.isEmpty(param.getUpdatedTimeEnd()), Package::getUpdatedTime, param.getUpdatedTimeEnd())
                .in(!CollectionUtils.isEmpty(param.getStatusList()), Package::getStatus, param.getStatusList())
                .in(!CollectionUtils.isEmpty(param.getPackageCodeList()), Package::getPackageCode, param.getPackageCodeList())
                .notIn(!CollectionUtils.isEmpty(param.getNoPackageCodeList()), Package::getPackageCode, param.getNoPackageCodeList())
                .eq(ObjectUtils.isEmpty(param.getDeleted()), Package::getDeleted, Deleted.NORMAL.getCode())
                .in(!ObjectUtils.isEmpty(param.getPoNoList()), Package::getPoNo, param.getPoNoList())
                .in(!ObjectUtils.isEmpty(param.getCargoCodeList()), Package::getCargoCode, param.getCargoCodeList())
                .in(!ObjectUtils.isEmpty(param.getCarrierCodeList()), Package::getCarrierCode, param.getCarrierCodeList())
                .in(!ObjectUtils.isEmpty(param.getExpressNoList()), Package::getExpressNo, param.getExpressNoList())
                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), Package::getExpressNo, param.getExpressNo())
                .eq(!ObjectUtils.isEmpty(param.getBusinessType()), Package::getBusinessType, param.getBusinessType())
                .in(!ObjectUtils.isEmpty(param.getSoNoList()), Package::getSoNo, param.getSoNoList())
                .in(!ObjectUtils.isEmpty(param.getShipmentOrderCodeList()), Package::getShipmentOrderCode, param.getShipmentOrderCodeList())
                .gt(!ObjectUtils.isEmpty(param.getStartOutStockDate()), Package::getOutStockDate, param.getStartOutStockDate())
                .le(!ObjectUtils.isEmpty(param.getEndOutStockDate()), Package::getOutStockDate, param.getEndOutStockDate())
                .gt((!ObjectUtils.isEmpty(param.getUpdatedTimeStart()) || !ObjectUtils.isEmpty(param.getUpdatedTimeEnd())), Package::getUpdatedTime, 0L)
                .gt((!ObjectUtils.isEmpty(param.getStartOutStockDate()) || !ObjectUtils.isEmpty(param.getEndOutStockDate())), Package::getOutStockDate, 0L)
                .eq(!ObjectUtils.isEmpty(param.getPretreatmentStatus()), Package::getPretreatmentStatus, param.getPretreatmentStatus())
                .in(!CollectionUtils.isEmpty(param.getPretreatmentStatusList()), Package::getPretreatmentStatus, param.getPretreatmentStatusList())
                .eq(!ObjectUtils.isEmpty(param.getSkuQuality()), Package::getSkuQuality, param.getSkuQuality())
                .eq(!ObjectUtils.isEmpty(param.getExpressBranch()), Package::getExpressBranch, param.getExpressBranch())
        ;
        if (param.getOrderTag() != null && param.getOrderTag() > 0) {
            queryWrapper.apply(" order_tag = (order_tag|" + param.getOrderTag() + ")");
        }
        if (param.getNoContainOrderTag() != null && param.getNoContainOrderTag() != 0) {
            queryWrapper.apply(" order_tag = (order_tag&" + param.getNoContainOrderTag() + ")");
        }
        if (!CollectionUtils.isEmpty(param.getSkuCodeLikeList())) {
            List<String> likeSqlList = new ArrayList<>();
            for (String skuCodeLike : param.getSkuCodeLikeList()) {
//                queryWrapper.lambda().or().like(Package::getAnalysisSku,skuCodeLike);
                likeSqlList.add(" analysis_sku LIKE '%" + skuCodeLike + "%' ");
            }
            if (!CollectionUtils.isEmpty(likeSqlList)) {
                queryWrapper.apply("  (" + likeSqlList.stream().collect(Collectors.joining(" or ")) + " ) ");
            }
        }
        queryWrapper.select(" warehouse_code,cargo_code,is_pre,sale_platform,carrier_code,sku_quality,analysis_sku,spike_analysis_sku,express_branch,count(id) as num,max(package_code) as package_code  ");
        queryWrapper.groupBy("  warehouse_code,cargo_code,is_pre,sale_platform,carrier_code,sku_quality,analysis_sku,spike_analysis_sku,express_branch ");
        List<Map<String, Object>> mapList = packageService.listMaps(queryWrapper);
        if (CollectionUtils.isEmpty(mapList)) {
            return Result.success(new ArrayList<>());
        }
        List<CollectWaveAnalysisDTO> collectWaveFrontAnalysisDTOList = mapList.stream().map(a -> {
            CollectWaveAnalysisDTO collectWaveFrontAnalysisDTO = BeanUtil.mapToBean(a, CollectWaveAnalysisDTO.class, true, CopyOptions.create().ignoreNullValue());
            return collectWaveFrontAnalysisDTO;
        }).collect(Collectors.toList());
        return Result.success(collectWaveFrontAnalysisDTOList);
    }

    @Override
    public Result<Integer> getExportCountNum(PackageParam packageParam) {
        LambdaQueryWrapper<Package> queryParamNew = getQueryParamNew(packageParam);
        int count = packageService.count(queryParamNew);
        return Result.success(count);
    }

    @Override
    public Result<Page<PackageDTO>> getPageNew(PackageParam packageParam) {
        Long startTime = System.currentTimeMillis();
        Page<Package> page = new Page<>(packageParam.getCurrent(), packageParam.getSize(), packageParam.getSearchCount());
        LambdaQueryWrapper<Package> queryWrapper = getQueryParamNew(packageParam);
        IPage<Package> packageIPage = ipackageService.page(page, queryWrapper);
        Page<PackageDTO> result = ConverterUtil.convertPage(packageIPage, PackageDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<CollectWaveAnalysisPackStructDTO>> getCollectWaveAnalysisPackStruct(PackageParam param) {
        QueryWrapper<Package> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .gt(!StringUtils.isEmpty(param.getCreatedTimeStart()), Package::getCreatedTime, param.getCreatedTimeStart())
                .eq(!ObjectUtils.isEmpty(param.getPackageCode()), Package::getPackageCode, param.getPackageCode())
                .eq(!ObjectUtils.isEmpty(param.getShipmentOrderCode()), Package::getShipmentOrderCode, param.getShipmentOrderCode())
                .eq(!ObjectUtils.isEmpty(param.getPoNo()), Package::getPoNo, param.getPoNo())
                .eq(!ObjectUtils.isEmpty(param.getSoNo()), Package::getSoNo, param.getSoNo())
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), Package::getCargoCode, param.getCargoCode())
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), Package::getWarehouseCode, param.getWarehouseCode())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), Package::getStatus, param.getStatus())
                .eq(!ObjectUtils.isEmpty(param.getIsPre()), Package::getIsPre, param.getIsPre())
                .eq(!ObjectUtils.isEmpty(param.getCollectStatus()), Package::getCollectStatus, param.getCollectStatus())
                .eq(!ObjectUtils.isEmpty(param.getCarrierCode()), Package::getCarrierCode, param.getCarrierCode())
                .eq(!ObjectUtils.isEmpty(param.getCarrierName()), Package::getCarrierName, param.getCarrierName())
                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), Package::getExpressNo, param.getExpressNo())
                .eq(!ObjectUtils.isEmpty(param.getSaleShopId()), Package::getSaleShopId, param.getSaleShopId())
                .eq(!ObjectUtils.isEmpty(param.getSalePlatform()), Package::getSalePlatform, param.getSalePlatform())
                .eq(!Objects.isNull(param.getSalePlatformT()), Package::getSalePlatform, param.getSalePlatformT())
                .notIn(!CollectionUtils.isEmpty(param.getNotOtherSalePlatformCodeList()), Package::getSalePlatform, param.getNotOtherSalePlatformCodeList())
                .eq(!ObjectUtils.isEmpty(param.getAnalysisSku()), Package::getAnalysisSku, param.getAnalysisSku())
                .eq(!ObjectUtils.isEmpty(param.getPackageStruct()), Package::getPackageStruct, param.getPackageStruct())
                .in(!ObjectUtils.isEmpty(param.getPackageStructList()), Package::getPackageStruct, param.getPackageStructList())
                .eq(!ObjectUtils.isEmpty(param.getWaveCode()), Package::getWaveCode, param.getWaveCode())
                .gt(!ObjectUtils.isEmpty(param.getUpdatedTimeStart()), Package::getUpdatedTime, param.getUpdatedTimeStart())
                .le(!ObjectUtils.isEmpty(param.getUpdatedTimeEnd()), Package::getUpdatedTime, param.getUpdatedTimeEnd())
                .in(!CollectionUtils.isEmpty(param.getStatusList()), Package::getStatus, param.getStatusList())
                .in(!CollectionUtils.isEmpty(param.getPackageCodeList()), Package::getPackageCode, param.getPackageCodeList())
                .notIn(!CollectionUtils.isEmpty(param.getNoPackageCodeList()), Package::getPackageCode, param.getNoPackageCodeList())
                .eq(ObjectUtils.isEmpty(param.getDeleted()), Package::getDeleted, Deleted.NORMAL.getCode())
                .in(!ObjectUtils.isEmpty(param.getPoNoList()), Package::getPoNo, param.getPoNoList())
                .in(!ObjectUtils.isEmpty(param.getCargoCodeList()), Package::getCargoCode, param.getCargoCodeList())
                .in(!ObjectUtils.isEmpty(param.getCarrierCodeList()), Package::getCarrierCode, param.getCarrierCodeList())
                .in(!ObjectUtils.isEmpty(param.getExpressNoList()), Package::getExpressNo, param.getExpressNoList())
                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), Package::getExpressNo, param.getExpressNo())
                .eq(!ObjectUtils.isEmpty(param.getBusinessType()), Package::getBusinessType, param.getBusinessType())
                .in(!ObjectUtils.isEmpty(param.getSoNoList()), Package::getSoNo, param.getSoNoList())
                .in(!ObjectUtils.isEmpty(param.getShipmentOrderCodeList()), Package::getShipmentOrderCode, param.getShipmentOrderCodeList())
                .gt(!ObjectUtils.isEmpty(param.getStartOutStockDate()), Package::getOutStockDate, param.getStartOutStockDate())
                .le(!ObjectUtils.isEmpty(param.getEndOutStockDate()), Package::getOutStockDate, param.getEndOutStockDate())
                .gt((!ObjectUtils.isEmpty(param.getUpdatedTimeStart()) || !ObjectUtils.isEmpty(param.getUpdatedTimeEnd())), Package::getUpdatedTime, 0L)
                .gt((!ObjectUtils.isEmpty(param.getStartOutStockDate()) || !ObjectUtils.isEmpty(param.getEndOutStockDate())), Package::getOutStockDate, 0L)
                .eq(!ObjectUtils.isEmpty(param.getPretreatmentStatus()), Package::getPretreatmentStatus, param.getPretreatmentStatus())
                .in(!CollectionUtils.isEmpty(param.getPretreatmentStatusList()), Package::getPretreatmentStatus, param.getPretreatmentStatusList())
                .eq(!ObjectUtils.isEmpty(param.getSkuQuality()), Package::getSkuQuality, param.getSkuQuality())
                .eq(!ObjectUtils.isEmpty(param.getExpressBranch()), Package::getExpressBranch, param.getExpressBranch())
        ;
        if (param.getOrderTag() != null && param.getOrderTag() > 0) {
            queryWrapper.apply(" order_tag = (order_tag|" + param.getOrderTag() + ")");
        }
        queryWrapper.select(" warehouse_code,is_pre,package_struct,count(id) as num");
        queryWrapper.groupBy("  warehouse_code,is_pre,package_struct ");
        List<Map<String, Object>> mapList = packageService.listMaps(queryWrapper);
        if (CollectionUtils.isEmpty(mapList)) {
            return Result.success(new ArrayList<>());
        }
        List<CollectWaveAnalysisPackStructDTO> collectWaveFrontAnalysisDTOList = mapList.stream().map(a -> {
            CollectWaveAnalysisPackStructDTO collectWaveFrontAnalysisDTO = BeanUtil.mapToBean(a, CollectWaveAnalysisPackStructDTO.class, true, CopyOptions.create().ignoreNullValue());
            return collectWaveFrontAnalysisDTO;
        }).collect(Collectors.toList());
        return Result.success(collectWaveFrontAnalysisDTOList);
    }

    @Override
    public Result<List<PackageLogDTO>> getPackLog(PackageParam packageParam) {
        QueryWrapper<PackageLog> packageLogQueryWrapper = new QueryWrapper();
        LambdaQueryWrapper<PackageLog> loglambdaQueryWrapper = packageLogQueryWrapper.lambda();
        loglambdaQueryWrapper.eq(!StringUtils.isEmpty(packageParam.getPackageCode()), PackageLog::getPackageCode, packageParam.getPackageCode());
        List<PackageLog> logListDetail = ipackageLogService.list(loglambdaQueryWrapper);
        List<PackageLogDTO> logListDtoDetail = ConverterUtil.convertList(logListDetail, PackageLogDTO.class);
        return Result.success(logListDtoDetail);
    }

    @Override
    public Result<Map<String, Long>> getPackPrintNum(PackageParam packageParam) {
        Map<String, Long> map = new HashMap<>();
        if (packageParam == null || CollectionUtils.isEmpty(packageParam.getPackageCodeList())) {
            return Result.success(map);
        }
        QueryWrapper<PackageLog> packageLogQueryWrapper = new QueryWrapper();
        LambdaQueryWrapper<PackageLog> loglambdaQueryWrapper = packageLogQueryWrapper.lambda();
        loglambdaQueryWrapper.in(PackageLog::getPackageCode, packageParam.getPackageCodeList());
        loglambdaQueryWrapper.like(PackageLog::getOpContent, "打印包裹");
        loglambdaQueryWrapper.select(PackageLog::getPackageCode, PackageLog::getOpContent);
        List<PackageLog> logListDetail = ipackageLogService.list(loglambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(logListDetail)) {
            Map<String, Long> listMap = logListDetail.stream().collect(Collectors.groupingBy(PackageLog::getPackageCode, Collectors.counting()));
            return Result.success(listMap);
        }
        return Result.success(map);
    }

    @Override
    public Result<Page<PackAnalysisBillDTO>> getPackAnalysisPage(PackAnalysisBillParam param) {
        // 使用 Mapper 方法执行查询
        Page<Map<String, Object>> page = new Page<>(param.getCurrent(), param.getSize());
        IPage<Map<String, Object>> packAnalysisPage = packageService.getPackAnalysisPage(page, ConverterUtil.convert(param, PackAnalysisParam.class));

        if (packAnalysisPage == null || packAnalysisPage.getRecords() == null || packAnalysisPage.getRecords().isEmpty()) {
            return Result.success(new Page<>(param.getCurrent(), param.getSize(), 0));
        }
        // 转换查询结果为DTO对象
        List<PackAnalysisBillDTO> resultList = new ArrayList<>();
        for (Map<String, Object> map : packAnalysisPage.getRecords()) {
            PackAnalysisBillDTO dto = new PackAnalysisBillDTO();
            dto.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
            // 设置维度值
            Map<String, String> dimensionValues = new HashMap<>();
            // 设置各个维度字段
            if (map.containsKey("cargoCode")) {
                String value = map.get("cargoCode") != null ? map.get("cargoCode").toString() : "";
                dto.setCargoCode(value);
                dimensionValues.put("cargoCode", value);
            }

            if (map.containsKey("businessType")) {
                String value = map.get("businessType") != null ? map.get("businessType").toString() : "";
                dto.setBusinessType(value);
                dimensionValues.put("businessType", value);
            }

            if (map.containsKey("isPre")) {
                String value = map.get("isPre") != null ? map.get("isPre").toString() : "";
                dto.setIsPre(value);
                dimensionValues.put("isPre", value);
            }

            if (map.containsKey("salePlatform")) {
                String value = map.get("salePlatform") != null ? map.get("salePlatform").toString() : "";
                dto.setSalePlatform(value);
                dimensionValues.put("salePlatform", value);
            }

            if (map.containsKey("packageStruct")) {
                String value = map.get("packageStruct") != null ? map.get("packageStruct").toString() : "";
                dto.setPackageStruct(value);
                dimensionValues.put("packageStruct", value);
            }

            if (map.containsKey("expOutStockDate_day")) {
                String value = map.get("expOutStockDate_day") != null ? map.get("expOutStockDate_day").toString() : "";
                dto.setExpOutStockDate_day(value);
                dimensionValues.put("expOutStockDate_day", value);
            }

            if (map.containsKey("expOutStockDate_hour")) {
                String value = map.get("expOutStockDate_hour") != null ? map.get("expOutStockDate_hour").toString() : "";
                dto.setExpOutStockDate_hour(value);
                dimensionValues.put("expOutStockDate_hour", value);
            }

            if (map.containsKey("createTime_day")) {
                String value = map.get("createTime_day") != null ? map.get("createTime_day").toString() : "";
                dto.setCreatedTime_day(value);
                dimensionValues.put("createTime_day", value);
            }

            if (map.containsKey("createTime_hour")) {
                String value = map.get("createTime_hour") != null ? map.get("createTime_hour").toString() : "";
                dto.setCreatedTime_hour(value);
                dimensionValues.put("createTime_hour", value);
            }

            if (map.containsKey("payDate_day")) {
                String value = map.get("payDate_day") != null ? map.get("payDate_day").toString() : "";
                dto.setPayDate_day(value);
                dimensionValues.put("payDate_day", value);
            }

            if (map.containsKey("payDate_hour")) {
                String value = map.get("payDate_hour") != null ? map.get("payDate_hour").toString() : "";
                dto.setPayDate_hour(value);
                dimensionValues.put("payDate_hour", value);
            }

            if (map.containsKey("outStockDate_day")) {
                String value = map.get("outStockDate_day") != null ? map.get("outStockDate_day").toString() : "";
                dto.setOutStockDate_day(value);
                dimensionValues.put("outStockDate_day", value);
            }

            if (map.containsKey("outStockDate_hour")) {
                String value = map.get("outStockDate_hour") != null ? map.get("outStockDate_hour").toString() : "";
                dto.setOutStockDate_hour(value);
                dimensionValues.put("outStockDate_hour", value);
            }

            if (map.containsKey("pickCompleteSkuDate_day")) {
                String value = map.get("pickCompleteSkuDate_day") != null ? map.get("pickCompleteSkuDate_day").toString() : "";
                dto.setPickCompleteSkuDate_day(value);
                dimensionValues.put("pickCompleteSkuDate_day", value);
            }

            if (map.containsKey("pickCompleteSkuDate_hour")) {
                String value = map.get("pickCompleteSkuDate_hour") != null ? map.get("pickCompleteSkuDate_hour").toString() : "";
                dto.setPickCompleteSkuDate_hour(value);
                dimensionValues.put("pickCompleteSkuDate_hour", value);
            }

            if (map.containsKey("checkCompleteDate_day")) {
                String value = map.get("checkCompleteDate_day") != null ? map.get("checkCompleteDate_day").toString() : "";
                dto.setCheckCompleteDate_day(value);
                dimensionValues.put("checkCompleteDate_day", value);
            }

            if (map.containsKey("checkCompleteDate_hour")) {
                String value = map.get("checkCompleteDate_hour") != null ? map.get("checkCompleteDate_hour").toString() : "";
                dto.setCheckCompleteDate_hour(value);
                dimensionValues.put("checkCompleteDate_hour", value);
            }



            if (map.containsKey("carrierCode")) {
                String value = map.get("carrierCode") != null ? map.get("carrierCode").toString() : "";
                dto.setCarrierCode(value);
                dimensionValues.put("carrierCode", value);
            }

            if (map.containsKey("receiverProvName")) {
                String value = map.get("receiverProvName") != null ? map.get("receiverProvName").toString() : "";
                dto.setReceiverProvName(value);
                dimensionValues.put("receiverProvName", value);
            }

            if (map.containsKey("receiverCityName")) {
                String value = map.get("receiverCityName") != null ? map.get("receiverCityName").toString() : "";
                dto.setReceiverCityName(value);
                dimensionValues.put("receiverCityName", value);
            }

            if (map.containsKey("receiverAreaName")) {
                String value = map.get("receiverAreaName") != null ? map.get("receiverAreaName").toString() : "";
                dto.setReceiverAreaName(value);
                dimensionValues.put("receiverAreaName", value);
            }

            // 设置维度值映射
            dto.setDimensionValues(dimensionValues);

            // 设置统计值
            if (map.containsKey("orderCount")) {
                dto.setOrderCount(Integer.valueOf(map.get("orderCount").toString()));
            }

            if (map.containsKey("skuQtySum")) {
                dto.setSkuQtySum(new BigDecimal(map.get("skuQtySum").toString()));
            }

            if (map.containsKey("createdOrderCount")) {
                dto.setCreatedOrderCount(Integer.valueOf(map.get("createdOrderCount").toString()));
            }

            if (map.containsKey("pretreatmentFailOrderCount")) {
                dto.setPretreatmentFailOrderCount(Integer.valueOf(map.get("pretreatmentFailOrderCount").toString()));
            }

            if (map.containsKey("pretreatmentCompleteOrderCount")) {
                dto.setPretreatmentCompleteOrderCount(Integer.valueOf(map.get("pretreatmentCompleteOrderCount").toString()));
            }

            if (map.containsKey("collectedOrderCount")) {
                dto.setCollectedOrderCount(Integer.valueOf(map.get("collectedOrderCount").toString()));
            }

            if (map.containsKey("collectedFailOrderCount")) {
                dto.setCollectedFailOrderCount(Integer.valueOf(map.get("collectedFailOrderCount").toString()));
            }

            if (map.containsKey("pickStartOrderCount")) {
                dto.setPickStartOrderCount(Integer.valueOf(map.get("pickStartOrderCount").toString()));
            }

            if (map.containsKey("pickEndOrderCount")) {
                dto.setPickEndOrderCount(Integer.valueOf(map.get("pickEndOrderCount").toString()));
            }

            if (map.containsKey("checkStartOrderCount")) {
                dto.setCheckStartOrderCount(Integer.valueOf(map.get("checkStartOrderCount").toString()));
            }

            if (map.containsKey("checkCompleteOrderCount")) {
                dto.setCheckCompleteOrderCount(Integer.valueOf(map.get("checkCompleteOrderCount").toString()));
            }

            if (map.containsKey("outOrderCount")) {
                dto.setOutOrderCount(Integer.valueOf(map.get("outOrderCount").toString()));
            }

            if (map.containsKey("interceptOrderCount")) {
                dto.setInterceptOrderCount(Integer.valueOf(map.get("interceptOrderCount").toString()));
            }

            if (map.containsKey("cancelOrderCount")) {
                dto.setCancelOrderCount(Integer.valueOf(map.get("cancelOrderCount").toString()));
            }

            if (map.containsKey("interceptCancelOrderCount")) {
                dto.setInterceptCancelOrderCount(Integer.valueOf(map.get("interceptCancelOrderCount").toString()));
            }

            if (map.containsKey("shortageOutOrderCount")) {
                dto.setShortageOutOrderCount(Integer.valueOf(map.get("shortageOutOrderCount").toString()));
            }
            resultList.add(dto);
        }
        // 构建分页结果
        Page<PackAnalysisBillDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resultPage.setRecords(resultList);

        return Result.success(resultPage);
    }

    private LambdaQueryWrapper<Package> getQueryParamNew(PackageParam param) {
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(param);
        if (param.getOrderTag() != null && param.getOrderTag() > 0) {
            queryWrapper.apply(" order_tag = (order_tag|" + param.getOrderTag() + ")");
        }
        //包裹明细查询商品编码
//        if (!CollectionUtils.isEmpty(param.getSkuCodeList())) {
//            queryWrapper.apply(" exists (select 1 from dt_package_detail where dt_package.package_code = dt_package_detail.package_code " +
//                    " AND dt_package_detail.deleted =1 AND  dt_package_detail.sku_code in(" + "'" + param.getSkuCodeList().stream().collect(Collectors.joining("','")) + "'" + "  ))");
//        }
        if (!CollectionUtils.isEmpty(param.getSkuCodeList())) {
            queryWrapper.apply(" package_code in (select package_code from dt_package_detail where  " +
                    "   dt_package_detail.deleted =1 AND  dt_package_detail.sku_code in(" + "'" + param.getSkuCodeList().stream().collect(Collectors.joining("','")) + "'" + "  ))");
        }
        //拣选单号查询包裹
//        if (!StringUtils.isEmpty(param.getPickCode())) {
//            queryWrapper.apply(" exists (select 1 from dt_pick_detail where dt_package.package_code = dt_pick_detail.package_code " +
//                    " AND dt_pick_detail.deleted =1 AND  dt_pick_detail.pick_code ='" + param.getPickCode() + "')");
//        }
        if (!StringUtils.isEmpty(param.getPickCode())) {
            queryWrapper.apply(" package_code in (select package_code from dt_pick_detail where  " +
                    "   dt_pick_detail.deleted =1 AND  dt_pick_detail.pick_code ='" + param.getPickCode() + "')");
        }
        //拣选单号查询包裹
//        if (!CollectionUtils.isEmpty(param.getPickCodeList())) {
//            queryWrapper.apply(" exists (select 1 from dt_pick_detail where dt_package.package_code = dt_pick_detail.package_code " +
//                    " AND dt_pick_detail.deleted =1 AND  dt_pick_detail.pick_code in(" + "'" + param.getPickCodeList().stream().collect(Collectors.joining("','")) + "'" + "  ))");
//        }
        if (!CollectionUtils.isEmpty(param.getPickCodeList())) {
            queryWrapper.apply(" package_code in (select package_code from dt_pick_detail where  " +
                    "   dt_pick_detail.deleted =1 AND  dt_pick_detail.pick_code in(" + "'" + param.getPickCodeList().stream().collect(Collectors.joining("','")) + "'" + "  ))");
        }
        //出库单参数查询数据
        if (!StringUtils.isEmpty(param.getOrderType()) || !StringUtils.isEmpty(param.getSaleShop()) ||
                !StringUtils.isEmpty(param.getPreSaleType()) || !CollectionUtils.isEmpty(param.getTradeNoList()) ||
                !StringUtils.isEmpty(param.getStartExpOutStockDate()) || !StringUtils.isEmpty(param.getEndExpOutStockDate()) ||  //getExpOutStockDate
                !StringUtils.isEmpty(param.getReceiverProv()) ||
                !StringUtils.isEmpty(param.getReceiverProvName()) ||
                !StringUtils.isEmpty(param.getReceiverCity()) ||
                !StringUtils.isEmpty(param.getReceiverCityName()) ||
                !StringUtils.isEmpty(param.getReceiverArea()) ||
                !StringUtils.isEmpty(param.getReceiverAreaName()) ||
                !CollectionUtils.isEmpty(param.getReceiverAreaList()) ||
                !CollectionUtils.isEmpty(param.getReceiverProvList()) ||
                !CollectionUtils.isEmpty(param.getReceiverCityList()) ||
                !StringUtils.isEmpty(param.getOrdCreatedTimeStart()) || !StringUtils.isEmpty(param.getOrdCreatedTimeEnd()) ||
                !StringUtils.isEmpty(param.getExpShipTimeStart()) || !StringUtils.isEmpty(param.getExpShipTimeEnd())) {     //getExpShipTime
            //拼接查询条件
            String sqlShip = "";
            if (!CollectionUtils.isEmpty(param.getShipmentOrderCodeList())) {
                sqlShip += " AND  dt_shipment_order.shipment_order_code in(" + "'" + param.getShipmentOrderCodeList().stream().collect(Collectors.joining("','")) + "'" + ") ";
            }
            if (!CollectionUtils.isEmpty(param.getSoNoList())) {
                sqlShip += " AND  dt_shipment_order.so_no in(" + "'" + param.getSoNoList().stream().collect(Collectors.joining("','")) + "'" + ") ";
            }
            if (!CollectionUtils.isEmpty(param.getPoNoList())) {
                sqlShip += " AND  dt_shipment_order.po_no in(" + "'" + param.getPoNoList().stream().collect(Collectors.joining("','")) + "'" + ") ";
            }
            if (!CollectionUtils.isEmpty(param.getCargoCodeList())) {
                sqlShip += " AND  dt_shipment_order.cargo_code in(" + "'" + param.getCargoCodeList().stream().collect(Collectors.joining("','")) + "'" + ") ";
            }
            if (!CollectionUtils.isEmpty(param.getTradeNoList())) {
                sqlShip += " AND  dt_shipment_order.trade_no in(" + "'" + param.getTradeNoList().stream().collect(Collectors.joining("','")) + "'" + ") ";
            }
            // ------------ 省市区
            if (!CollectionUtils.isEmpty(param.getReceiverProvList())) {
                sqlShip += " AND  dt_shipment_order.receiver_prov in(" + "'" + param.getReceiverProvList().stream().collect(Collectors.joining("','")) + "'" + ") ";
            }
            if (!CollectionUtils.isEmpty(param.getReceiverCityList())) {
                sqlShip += " AND  dt_shipment_order.receiver_city in(" + "'" + param.getReceiverCityList().stream().collect(Collectors.joining("','")) + "'" + ") ";
            }
            if (!CollectionUtils.isEmpty(param.getReceiverAreaList())) {
                sqlShip += " AND  dt_shipment_order.receiver_area in(" + "'" + param.getReceiverAreaList().stream().collect(Collectors.joining("','")) + "'" + ") ";
            }

            if (!StringUtils.isEmpty(param.getReceiverProvName())) {
                sqlShip += " AND  dt_shipment_order.receiver_prov_name =  '" + param.getReceiverProvName() + "'" ;
            }
            if (!StringUtils.isEmpty(param.getReceiverCityName())) {
                sqlShip += " AND  dt_shipment_order.receiver_city_name ='" +  param.getReceiverCityName() + "'" ;
            }
            if (!StringUtils.isEmpty(param.getReceiverAreaName())) {
                sqlShip += " AND  dt_shipment_order.receiver_area_name ='" +  param.getReceiverAreaName() + "'";
            }

            // ------------ 省市区
            if (!StringUtils.isEmpty(param.getSaleShop())) {
                sqlShip += " AND  dt_shipment_order.sale_shop ='" + param.getSaleShop() + "' ";
            }
            if (!StringUtils.isEmpty(param.getOrderType())) {
                sqlShip += " AND  dt_shipment_order.order_type ='" + param.getOrderType() + "' ";
            }
            if (!StringUtils.isEmpty(param.getPreSaleType())) {
                sqlShip += " AND  dt_shipment_order.pre_sale_type ='" + param.getPreSaleType() + "' ";
            }
            if (!StringUtils.isEmpty(param.getReceiverProv())) {
                sqlShip += " AND  dt_shipment_order.receiver_prov ='" + param.getReceiverProv() + "' ";
            }
            if (!StringUtils.isEmpty(param.getReceiverCity())) {
                sqlShip += " AND  dt_shipment_order.receiver_city ='" + param.getReceiverCity() + "' ";
            }
            if (!StringUtils.isEmpty(param.getReceiverArea())) {
                sqlShip += " AND  dt_shipment_order.receiver_area ='" + param.getReceiverArea() + "' ";
            }
            if (!StringUtils.isEmpty(param.getBusinessType())) {
                sqlShip += " AND  dt_shipment_order.business_type ='" + param.getBusinessType() + "' ";
            }
            if (!StringUtils.isEmpty(param.getCarrierCode())) {
                sqlShip += " AND  dt_shipment_order.carrier_code ='" + param.getCarrierCode() + "' ";
            }
            //出库单创建时间
            if (!StringUtils.isEmpty(param.getOrdCreatedTimeStart())) {
                sqlShip += " AND  dt_shipment_order.created_time >" + param.getOrdCreatedTimeStart() + " ";
            }
            if (!StringUtils.isEmpty(param.getOrdCreatedTimeEnd())) {
                sqlShip += " AND  dt_shipment_order.created_time <" + param.getOrdCreatedTimeEnd() + " ";
            }
            if (StringUtils.isEmpty(param.getOrdCreatedTimeStart()) && !StringUtils.isEmpty(param.getOrdCreatedTimeEnd())) {
                sqlShip += " AND  dt_shipment_order.created_time >0 ";
            }
            //出库单预计出库时间 exp_out_stock_date
            if (!StringUtils.isEmpty(param.getStartExpOutStockDate())) {
                sqlShip += " AND  dt_shipment_order.exp_out_stock_date >" + param.getStartExpOutStockDate() + " ";
            }
            if (!StringUtils.isEmpty(param.getEndExpOutStockDate())) {
                sqlShip += " AND  dt_shipment_order.exp_out_stock_date <" + param.getEndExpOutStockDate() + " ";
            }
            if (StringUtils.isEmpty(param.getStartExpOutStockDate()) && !StringUtils.isEmpty(param.getEndExpOutStockDate())) {
                sqlShip += " AND  dt_shipment_order.exp_out_stock_date >0 ";
            }

            //付款时间 payDate
            if (!StringUtils.isEmpty(param.getStartPayTime())) {
                sqlShip += " AND  dt_shipment_order.pay_date >" + param.getStartPayTime() + " ";
            }
            if (!StringUtils.isEmpty(param.getEndPayTime())) {
                sqlShip += " AND  dt_shipment_order.pay_date <" + param.getEndPayTime() + " ";
            }
            if (StringUtils.isEmpty(param.getStartPayTime()) && !StringUtils.isEmpty(param.getEndPayTime())) {
                sqlShip += " AND  dt_shipment_order.pay_date >0 ";
            }
            //出库单最晚出库时间 exp_ship_time
            if (!StringUtils.isEmpty(param.getExpShipTimeStart())) {
                sqlShip += " AND  dt_shipment_order.exp_ship_time >" + param.getExpShipTimeStart() + " ";
            }
            if (!StringUtils.isEmpty(param.getExpShipTimeEnd())) {
                sqlShip += " AND  dt_shipment_order.exp_ship_time <" + param.getExpShipTimeEnd() + " ";
            }
            if (StringUtils.isEmpty(param.getExpShipTimeStart()) && !StringUtils.isEmpty(param.getExpShipTimeEnd())) {
                sqlShip += " AND  dt_shipment_order.exp_ship_time >0 ";
            }
//            queryWrapper.apply(" exists (select 1 from dt_shipment_order where dt_package.shipment_order_code = dt_shipment_order.shipment_order_code " +
//                    " AND dt_shipment_order.deleted =1 " + sqlShip + ")");
            queryWrapper.apply(" shipment_order_code in (select shipment_order_code from dt_shipment_order where  " +
                    "   dt_shipment_order.deleted =1 " + sqlShip + ")");
        }
        // 包材
        if (StrUtil.isNotBlank(param.getMaterialUpcCode())) {
//            queryWrapper.apply(" exists (select 1 from dt_shipment_order_material where dt_package.shipment_order_code = dt_shipment_order_material.shipment_order_code " +
//                    " AND dt_shipment_order_material.deleted =1 AND  dt_shipment_order_material.rec_pack_upc_code ='" + param.getMaterialUpcCode() + "')");
            queryWrapper.apply(" shipment_order_code in (select shipment_order_code from dt_shipment_order_material where   " +
                    "   dt_shipment_order_material.deleted =1 AND  dt_shipment_order_material.rec_pack_upc_code ='" + param.getMaterialUpcCode() + "')");
        }
        if (!CollectionUtils.isEmpty(param.getTableFieldList())) {
            queryWrapper.select(Package.class, i -> param.getTableFieldList().contains(i.getColumn()));
        }
        return queryWrapper;
    }

    @Override
    @Transactional
    public Result<Boolean> updatePackageDTO(PackageDTO packageDTO) {
        Boolean result = ipackageService.updateById(ConverterUtil.convert(packageDTO, Package.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> updatePackageDetailDTOS(List<PackageDetailDTO> packageDetailDTOS) {
        List<PackageDetail> packageDetails = ConverterUtil.convertList(packageDetailDTOS, PackageDetail.class);
        packageDetails.forEach(packageDetail -> {
            Boolean updateResult = ipackageDetailService.updateById(packageDetail);
            if (!updateResult) {
                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
        });
        return Result.success(true);
    }

    @Override
    public Result<List<CollectWaveDTO>> querySpikeCollectWave(CollectWaveBillParam param) {
        QueryWrapper<Package> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), Package::getCargoCode, param.getCargoCode())
                .in(!ObjectUtils.isEmpty(param.getCargoCodeList()), Package::getCargoCode, param.getCargoCodeList())
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), Package::getWarehouseCode, param.getWarehouseCode())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), Package::getStatus, param.getStatus())
                .eq(!ObjectUtils.isEmpty(param.getIsPre()), Package::getIsPre, param.getIsPre())
                .eq(!ObjectUtils.isEmpty(param.getCollectStatus()), Package::getCollectStatus, param.getCollectStatus())
                .eq(!ObjectUtils.isEmpty(param.getCarrierCode()), Package::getCarrierCode, param.getCarrierCode())
                //TODO 店铺目前未加入强制分组
                .eq(!ObjectUtils.isEmpty(param.getSaleShopId()), Package::getSaleShopId, param.getSaleShopId())
                .eq(!ObjectUtils.isEmpty(param.getSalePlatform()), Package::getSalePlatform, param.getSalePlatform())
                .eq(!ObjectUtils.isEmpty(param.getPackageStruct()), Package::getPackageStruct, param.getPackageStruct())
                .eq(!ObjectUtils.isEmpty(param.getWaveCode()), Package::getWaveCode, param.getWaveCode())
                .eq(ObjectUtils.isEmpty(param.getDeleted()), Package::getDeleted, Deleted.NORMAL.getCode())
                .eq(!ObjectUtils.isEmpty(param.getBusinessType()), Package::getBusinessType, param.getBusinessType())
                .eq(param.getExpressBranchNotBlank() != null, Package::getExpressBranch, param.getExpressBranchNotBlank())
        ;
        queryWrapper.select(" warehouse_code,cargo_code,carrier_code,sale_platform,sku_quality,package_struct,analysis_sku,spike_analysis_sku,express_branch,express_branch_name,count(1) as num ");
        queryWrapper.groupBy("  warehouse_code,cargo_code,carrier_code,sale_platform,sku_quality,package_struct,analysis_sku,spike_analysis_sku,express_branch ");
        List<Map<String, Object>> mapList = packageService.listMaps(queryWrapper);
        if (CollectionUtils.isEmpty(mapList)) {
            return Result.success(new ArrayList<>());
        }
        List<CollectWaveDTO> collectWaveDTOList = mapList.stream().map(objectMap -> {
            CollectWaveDTO collectWaveDTO = BeanUtil.mapToBean(objectMap, CollectWaveDTO.class, true, CopyOptions.create().ignoreNullValue());
            return collectWaveDTO;
        }).collect(Collectors.toList());
        return Result.success(collectWaveDTOList);
    }

//    @Override
//    public Result<List<CollectWaveDTO>> queryCollectWave(CollectWaveBillParam param) {
//        List<CollectWave> packageDTOS = collectWaveMapper.queryCollectWave(ConverterUtil.convert(param, CollectWaveParam.class));
//        return Result.success(ConverterUtil.convertList(packageDTOS, CollectWaveDTO.class));
//    }

    @Override
    public Result<List<PackageDetailDTO>> getCollectWaveToPick(PackageDetailParam param) {
        QueryWrapper<PackageDetail> packageDetailQueryWrapper = new QueryWrapper();
        LambdaQueryWrapper<PackageDetail> lambdaQueryWrapper = packageDetailQueryWrapper.lambda();
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getPackageCodeList()), PackageDetail::getPackageCode, param.getPackageCodeList());
        lambdaQueryWrapper.eq(!StringUtils.isEmpty(param.getWarehouseCode()), PackageDetail::getWarehouseCode, param.getWarehouseCode());
        lambdaQueryWrapper.eq(!StringUtils.isEmpty(param.getCargoCode()), PackageDetail::getCargoCode, param.getCargoCode());
        List<PackageDetail> listDetail = ipackageDetailService.list(lambdaQueryWrapper);
        List<PackageDetailDTO> listDtoDetail = ConverterUtil.convertList(listDetail, PackageDetailDTO.class);
        return Result.success(listDtoDetail);
    }

    @Override
    @Transactional
    public Result<Boolean> updateCollectPackage(List<PackageDTO> packageDTOS) {
        QueryWrapper<PackageDetail> packageDetailQueryWrapper = new QueryWrapper();
        LambdaQueryWrapper<PackageDetail> lambdaQueryWrapper = packageDetailQueryWrapper.lambda();
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(packageDTOS), PackageDetail::getPackageCode, packageDTOS.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
        List<PackageDetail> listDetail = ipackageDetailService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(listDetail)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<Package> packages = ConverterUtil.convertList(packageDTOS, Package.class);
        packages.forEach(entity -> {
            Boolean result = ipackageService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        listDetail.forEach(entity -> entity.setStatus(packageDTOS.get(0).getStatus()));
        listDetail.stream().forEach(packageDetail -> {
            Boolean pickResult = ipackageDetailService.updateById(packageDetail);
            if (!pickResult) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        return Result.success(true);
    }

    @Override
    public Result<List<PackageDTO>> queryList(PackageParam packageParam) {
        LambdaQueryWrapper<Package> wrapper = packageUtil.getQueryWrapper(packageParam);
        return Result.success(ConverterUtil.convertList(ipackageService.list(wrapper), PackageDTO.class));
    }

    @Override
    public Result<List<PackageDetailDTO>> getPackageDetailListByListCode(PackageDetailParam param) {
        QueryWrapper<PackageDetail> packageDetailQueryWrapper = new QueryWrapper();
        LambdaQueryWrapper<PackageDetail> lambdaQueryWrapper = packageDetailQueryWrapper.lambda();
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getPackageCodeList()), PackageDetail::getPackageCode, param.getPackageCodeList());
        List<PackageDetail> listDetail = ipackageDetailService.list(lambdaQueryWrapper);
        List<PackageDetailDTO> listDtoDetail = ConverterUtil.convertList(listDetail, PackageDetailDTO.class);
        return Result.success(listDtoDetail);
    }

    @Override
    @Transactional
    public Result<Boolean> modifyNotifyPackage(PackageDTO packageDTO) {
        Package aPackage = ConverterUtil.convert(packageDTO, Package.class);
        Boolean result = ipackageService.updateById(aPackage);
        return Result.success(result);
    }

    @Override
    @Transactional
    public Result<Boolean> submitCollectWave(List<PackageDTO> packageDTOS) {
        List<Package> packages = ConverterUtil.convertList(packageDTOS, Package.class);
        packages.forEach(entity -> {
            Boolean result = ipackageService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        return Result.success(true);
    }


    public static void persist(List<PackageDTO> packageDTOList, IPackageService packageService) {

        if (CollectionUtil.isEmpty(packageDTOList)) {
            return;
        }

        List<Package> saveList = packageDTOList.stream()
                .filter(stock -> ObjectUtil.isEmpty(stock.getId()))
                .map(it -> ConverterUtil.convert(it, Package.class))
                .collect(Collectors.toList());

        List<Package> updateList = packageDTOList.stream()
                .filter(stock -> ObjectUtil.isNotEmpty(stock.getId()))
                .map(it -> ConverterUtil.convert(it, Package.class))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(saveList)) {
            if (!packageService.saveBatch(saveList)) {
                throw ExceptionUtil.exceptionWithMessage("保存包裹失败");
            }
        }

        for (Package aPackage : updateList) {
            if (!packageService.updateById(aPackage)) {
                throw ExceptionUtil.exceptionWithMessage("修改包裹失败");
            }
        }
    }

    public static void persistLog(List<PackageLogDTO> packageLogDTOList, IPackageLogService packageLogService) {

        if (CollectionUtil.isEmpty(packageLogDTOList)) {
            return;
        }

        List<PackageLog> saveList = packageLogDTOList.stream()
                .filter(stock -> ObjectUtil.isEmpty(stock.getId()))
                .map(it -> ConverterUtil.convert(it, PackageLog.class))
                .collect(Collectors.toList());

        List<PackageLog> updateList = packageLogDTOList.stream()
                .filter(stock -> ObjectUtil.isNotEmpty(stock.getId()))
                .map(it -> ConverterUtil.convert(it, PackageLog.class))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(saveList)) {
            if (!packageLogService.saveBatch(saveList)) {
                throw ExceptionUtil.exceptionWithMessage("保存包裹日志失败");
            }
        }

        for (PackageLog aPackage : updateList) {
            if (!packageLogService.updateById(aPackage)) {
                throw ExceptionUtil.exceptionWithMessage("修改包裹日志失败");
            }
        }
    }
}
