package com.dt.domain.bill.util;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.bill.adjust.entity.Adjust;
import com.dt.domain.bill.param.AdjustParam;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
@Component
public class AdjustUtil extends QueryWrapper<Adjust,AdjustParam> {
    @Override
    public LambdaQueryWrapper<Adjust> getQueryWrapper(AdjustParam param) {
        LambdaQueryWrapper<Adjust> lambdaQueryWrapper =  super.getQueryWrapper(param);
        lambdaQueryWrapper
                .eq(!ObjectUtils.isEmpty(param.getCode()), Adjust::getCode, param.getCode())
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), Adjust::getWarehouseCode, param.getWarehouseCode())
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), Adjust::getCargoCode, param.getCargoCode())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), Adjust::getStatus, param.getStatus())
                .eq(!ObjectUtils.isEmpty(param.getReason()), Adjust::getReason, param.getReason())
                .eq(!ObjectUtils.isEmpty(param.getNote()), Adjust::getNote, param.getNote())
                .eq(!ObjectUtils.isEmpty(param.getCheckerDate()), Adjust::getCheckerDate, param.getCheckerDate())
                .eq(!ObjectUtils.isEmpty(param.getChecker()), Adjust::getChecker, param.getChecker())
                .eq(!ObjectUtils.isEmpty(param.getCompleteDate()), Adjust::getCompleteDate, param.getCompleteDate())
                .eq(!ObjectUtils.isEmpty(param.getOpBy()), Adjust::getOpBy, param.getOpBy())
                .eq(!ObjectUtils.isEmpty(param.getCreatedBy()), Adjust::getCreatedBy, param.getCreatedBy())
                .eq(!ObjectUtils.isEmpty(param.getUpdatedBy()), Adjust::getUpdatedBy, param.getUpdatedBy())
                .in(!CollectionUtils.isEmpty(param.getCodeList()), Adjust::getCode, param.getCodeList())
                .in(!CollectionUtils.isEmpty(param.getCargoCodeList()), Adjust::getCargoCode, param.getCargoCodeList())
                .in(!CollectionUtils.isEmpty(param.getStatusList()), Adjust::getStatus, param.getStatusList())
                .gt(!ObjectUtils.isEmpty(param.getCompleteDateStart()),Adjust::getCompleteDate,param.getCompleteDateStart())
                //.le(!ObjectUtils.isEmpty(param.getCompleteDateEnd()),Adjust::getCompleteDate,param.getCompleteDateEnd())
                .between(!ObjectUtils.isEmpty(param.getCompleteDateEnd()),Adjust::getCompleteDate,1L,param.getCompleteDateEnd())
                .gt(!ObjectUtils.isEmpty(param.getUpdatedTimeStart()),Adjust::getUpdatedTime,param.getUpdatedTimeStart())
                .le(!ObjectUtils.isEmpty(param.getUpdatedTimeEnd()),Adjust::getUpdatedTime,param.getUpdatedTimeEnd())

                .eq(!ObjectUtils.isEmpty(param.getBusinessType()),Adjust::getBusinessType,param.getBusinessType())
                .in(!ObjectUtils.isEmpty(param.getBusinessTypeList()),Adjust::getBusinessType,param.getBusinessTypeList())
                .like(StrUtil.isNotBlank(param.getNoteLike()),Adjust::getNote,param.getNoteLike())
        ;
        if (param.getTag() != null && param.getTag() > 0) {
            lambdaQueryWrapper.apply(" tag = (tag|" + param.getTag() + ")");
        }
        return lambdaQueryWrapper;
    }
}
