package com.dt.domain.bill.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.common.enums.Deleted;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.bill.param.PackageParam;
import com.dt.domain.bill.pkg.entity.Package;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class PackageUtil extends QueryWrapper<Package, PackageParam> {
    @Override
    public LambdaQueryWrapper<Package> getQueryWrapper(PackageParam param) {
        LambdaQueryWrapper<Package> lambdaQueryWrapper = super.getQueryWrapper(param);
        lambdaQueryWrapper
                .eq(!ObjectUtils.isEmpty(param.getPackageCode()), Package::getPackageCode, param.getPackageCode())
                .eq(!ObjectUtils.isEmpty(param.getShipmentOrderCode()), Package::getShipmentOrderCode, param.getShipmentOrderCode())
                .eq(!ObjectUtils.isEmpty(param.getPoNo()), Package::getPoNo, param.getPoNo())
                .eq(!ObjectUtils.isEmpty(param.getSoNo()), Package::getSoNo, param.getSoNo())
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), Package::getCargoCode, param.getCargoCode())
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), Package::getWarehouseCode, param.getWarehouseCode())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), Package::getStatus, param.getStatus())
                .eq(!ObjectUtils.isEmpty(param.getIsPre()), Package::getIsPre, param.getIsPre())
                .eq(!ObjectUtils.isEmpty(param.getCollectStatus()), Package::getCollectStatus, param.getCollectStatus())
                .eq(!ObjectUtils.isEmpty(param.getCarrierCode()), Package::getCarrierCode, param.getCarrierCode())
                .eq(!ObjectUtils.isEmpty(param.getCarrierName()), Package::getCarrierName, param.getCarrierName())
                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), Package::getExpressNo, param.getExpressNo())
                .eq(!ObjectUtils.isEmpty(param.getSaleShopId()), Package::getSaleShopId, param.getSaleShopId())
                .in(!ObjectUtils.isEmpty(param.getSaleShopIdList()), Package::getSaleShopId, param.getSaleShopIdList())
                .eq(!ObjectUtils.isEmpty(param.getSalePlatform()), Package::getSalePlatform, param.getSalePlatform())
                .in(!ObjectUtils.isEmpty(param.getSalePlatformList()), Package::getSalePlatform, param.getSalePlatformList())
                .eq(!Objects.isNull(param.getSalePlatformT()), Package::getSalePlatform, param.getSalePlatformT())
                .notIn(!CollectionUtils.isEmpty(param.getNotOtherSalePlatformCodeList()), Package::getSalePlatform, param.getNotOtherSalePlatformCodeList())
                .eq(!ObjectUtils.isEmpty(param.getAnalysisSku()), Package::getAnalysisSku, param.getAnalysisSku())
                .eq(!ObjectUtils.isEmpty(param.getOriginPackageCode()), Package::getOriginPackageCode, param.getOriginPackageCode())

                .eq(!ObjectUtils.isEmpty(param.getSpikeAnalysisSku()), Package::getSpikeAnalysisSku, param.getSpikeAnalysisSku())
                .eq(param.getSpikeAnalysisSkuNotBlank() != null, Package::getSpikeAnalysisSku, param.getSpikeAnalysisSkuNotBlank())

                .in(!ObjectUtils.isEmpty(param.getAnalysisSkuList()), Package::getAnalysisSku, param.getAnalysisSkuList())
                .eq(!ObjectUtils.isEmpty(param.getPackageStruct()), Package::getPackageStruct, param.getPackageStruct())
                .in(!ObjectUtils.isEmpty(param.getPackageStructList()), Package::getPackageStruct, param.getPackageStructList())
                .eq(!ObjectUtils.isEmpty(param.getWaveCode()), Package::getWaveCode, param.getWaveCode())
                .gt(!ObjectUtils.isEmpty(param.getUpdatedTimeStart()), Package::getUpdatedTime, param.getUpdatedTimeStart())
                .le(!ObjectUtils.isEmpty(param.getUpdatedTimeEnd()), Package::getUpdatedTime, param.getUpdatedTimeEnd())
                .in(!CollectionUtils.isEmpty(param.getStatusList()), Package::getStatus, param.getStatusList())
                .in(!CollectionUtils.isEmpty(param.getPackageCodeList()), Package::getPackageCode, param.getPackageCodeList())
                .notIn(!CollectionUtils.isEmpty(param.getNoPackageCodeList()), Package::getPackageCode, param.getNoPackageCodeList())
                .eq(ObjectUtils.isEmpty(param.getDeleted()), Package::getDeleted, Deleted.NORMAL.getCode())
                .in(!ObjectUtils.isEmpty(param.getPoNoList()), Package::getPoNo, param.getPoNoList())
                .in(!ObjectUtils.isEmpty(param.getCargoCodeList()), Package::getCargoCode, param.getCargoCodeList())
                .in(!ObjectUtils.isEmpty(param.getCarrierCodeList()), Package::getCarrierCode, param.getCarrierCodeList())
                .in(!ObjectUtils.isEmpty(param.getExpressNoList()), Package::getExpressNo, param.getExpressNoList())
                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), Package::getExpressNo, param.getExpressNo())
                .eq(!ObjectUtils.isEmpty(param.getBusinessType()), Package::getBusinessType, param.getBusinessType())
                .in(!ObjectUtils.isEmpty(param.getSoNoList()), Package::getSoNo, param.getSoNoList())
                .in(!ObjectUtils.isEmpty(param.getShipmentOrderCodeList()), Package::getShipmentOrderCode, param.getShipmentOrderCodeList())
                //如果来源是包裹日汇总页面，查询总包数据时，不查询已取消的包裹数
                .ne(!ObjectUtils.isEmpty(param.getSummarySelect()) && param.getSummarySelect(), Package::getStatus, PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode())


                .ge(!ObjectUtils.isEmpty(param.getStartSkuCount()), Package::getPackageSkuQty, param.getStartSkuCount())
                .le(!ObjectUtils.isEmpty(param.getEndSkuCount()), Package::getPackageSkuQty, param.getEndSkuCount())

                .ge(!ObjectUtils.isEmpty(param.getWeightStart()), Package::getWeight, param.getWeightStart())
                .le(!ObjectUtils.isEmpty(param.getWeightEnd()), Package::getWeight, param.getWeightEnd())

                .ge(!ObjectUtils.isEmpty(param.getStartSkuTypeCount()), Package::getSkuTypeQty, param.getStartSkuTypeCount())
                .le(!ObjectUtils.isEmpty(param.getEndSkuTypeCount()), Package::getSkuTypeQty, param.getEndSkuTypeCount())


                .ge(!ObjectUtils.isEmpty(param.getStartOutStockDate()), Package::getOutStockDate, param.getStartOutStockDate())
                .le(!ObjectUtils.isEmpty(param.getEndOutStockDate()), Package::getOutStockDate, param.getEndOutStockDate())
                /**
                 * 测试反应，指定时间范围，未空的被查询出来了，
                 */
                .gt((!ObjectUtils.isEmpty(param.getUpdatedTimeStart()) || !ObjectUtils.isEmpty(param.getUpdatedTimeEnd())), Package::getUpdatedTime, 0L)
                .gt((!ObjectUtils.isEmpty(param.getStartOutStockDate()) || !ObjectUtils.isEmpty(param.getEndOutStockDate())), Package::getOutStockDate, 0L)
                //预处理状态
                .eq(!ObjectUtils.isEmpty(param.getPretreatmentStatus()), Package::getPretreatmentStatus, param.getPretreatmentStatus())
                .in(!CollectionUtils.isEmpty(param.getPretreatmentStatusList()), Package::getPretreatmentStatus, param.getPretreatmentStatusList())
                .eq(!ObjectUtils.isEmpty(param.getSkuQuality()), Package::getSkuQuality, param.getSkuQuality())

                .eq(!ObjectUtils.isEmpty(param.getTradeNo()), Package::getTradeNo, param.getTradeNo())
                .in(!ObjectUtils.isEmpty(param.getTradeNoList()), Package::getTradeNo, param.getTradeNoList())

                .in(!CollectionUtils.isEmpty(param.getExpressAccountList()), Package::getExpressAccount, param.getExpressAccountList())
                .eq(!ObjectUtils.isEmpty(param.getExpressAccount()), Package::getExpressAccount, param.getExpressAccount())
                .in(!CollectionUtils.isEmpty(param.getExpressBranchList()), Package::getExpressBranch, param.getExpressBranchList())
                .eq(!ObjectUtils.isEmpty(param.getExpressBranch()), Package::getExpressBranch, param.getExpressBranch())
                .eq(param.getExpressBranchNotBlank() != null, Package::getExpressBranch, param.getExpressBranchNotBlank())
                .eq(!ObjectUtils.isEmpty(param.getExpressBranchName()), Package::getExpressBranchName, param.getExpressBranchName())

                .ge(!ObjectUtils.isEmpty(param.getCheckCompleteDateStart()), Package::getCheckCompleteDate, param.getCheckCompleteDateStart())
                .le(!ObjectUtils.isEmpty(param.getCheckCompleteDateEnd()), Package::getCheckCompleteDate, param.getCheckCompleteDateEnd())
                .gt((!ObjectUtils.isEmpty(param.getCheckCompleteDateStart()) || !ObjectUtils.isEmpty(param.getCheckCompleteDateEnd())), Package::getCheckCompleteDate, 0L)

                .ge(!ObjectUtils.isEmpty(param.getPickCompleteSkuDateStart()), Package::getPickCompleteSkuDate, param.getPickCompleteSkuDateStart())
                .le(!ObjectUtils.isEmpty(param.getPickCompleteSkuDateEnd()), Package::getPickCompleteSkuDate, param.getPickCompleteSkuDateEnd())
                .gt((!ObjectUtils.isEmpty(param.getPickCompleteSkuDateStart()) || !ObjectUtils.isEmpty(param.getPickCompleteSkuDateEnd())), Package::getPickCompleteSkuDate, 0L)

        ;

        if (param.getOrderTag() != null && param.getOrderTag() > 0) {
            lambdaQueryWrapper.apply(" order_tag = (order_tag|" + param.getOrderTag() + ")");
        }

        if (param.getNoContainOrderTag() != null && param.getNoContainOrderTag() != 0) {
            lambdaQueryWrapper.apply(" order_tag = (order_tag&" + param.getNoContainOrderTag() + ")");
        }

        if (!CollectionUtils.isEmpty(param.getSkuCodeLikeList())) {
            List<String> likeSqlList = new ArrayList<>();
            for (String skuCodeLike : param.getSkuCodeLikeList()) {
                likeSqlList.add(" analysis_sku LIKE '%" + skuCodeLike + "%' ");
            }
            if (!CollectionUtils.isEmpty(likeSqlList)) {
                lambdaQueryWrapper.apply("  (" + likeSqlList.stream().collect(Collectors.joining(" or ")) + " ) ");
            }
        }

        return lambdaQueryWrapper;
    }


}
