package com.dt.domain.bill.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.NotifyStatusEnum;
import com.dt.component.common.enums.asn.AsnOrderTagEnum;
import com.dt.component.common.enums.bill.RelatedBillTypeEnum;
import com.dt.component.common.enums.tally.TallyTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.asn.entity.*;
import com.dt.domain.bill.asn.service.*;
import com.dt.domain.bill.bo.*;
import com.dt.domain.bill.bo.asn.AsnZeroReceiveBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.rec.ReceiptExtraDTO;
import com.dt.domain.bill.dto.rec.ReceiptExtraDetailDTO;
import com.dt.domain.bill.dto.related.RelateOrdersTaoTianDTO;
import com.dt.domain.bill.message.entity.MessageMq;
import com.dt.domain.bill.message.service.IMessageMqService;
import com.dt.domain.bill.param.AsnDetailParam;
import com.dt.domain.bill.param.AsnModifyParam;
import com.dt.domain.bill.param.AsnParam;
import com.dt.domain.bill.rec.entity.Receipt;
import com.dt.domain.bill.rec.entity.ReceiptDetail;
import com.dt.domain.bill.rec.entity.ReceiptExtra;
import com.dt.domain.bill.rec.entity.ReceiptExtraDetail;
import com.dt.domain.bill.rec.service.IReceiptDetailService;
import com.dt.domain.bill.rec.service.IReceiptExtraDetailService;
import com.dt.domain.bill.rec.service.IReceiptExtraService;
import com.dt.domain.bill.rec.service.IReceiptService;
import com.dt.domain.bill.related.entity.RelatedBill;
import com.dt.domain.bill.related.service.IRelatedBillService;
import com.dt.domain.bill.rs.entity.SalesReturnOrder;
import com.dt.domain.bill.rs.service.ISalesReturnOrderService;
import com.dt.domain.bill.shelf.entity.Shelf;
import com.dt.domain.bill.shelf.entity.ShelfDetail;
import com.dt.domain.bill.shelf.service.IShelfDetailService;
import com.dt.domain.bill.shelf.service.IShelfService;
import com.dt.domain.bill.sn.entity.ScanSnReceive;
import com.dt.domain.bill.sn.service.IScanSnReceiveService;
import com.dt.domain.bill.tally.entity.Tally;
import com.dt.domain.bill.tally.entity.TallyDetail;
import com.dt.domain.bill.tally.service.ITallyDetailService;
import com.dt.domain.bill.tally.service.ITallyService;
import com.dt.domain.bill.util.AsnDetailUtil;
import com.dt.domain.bill.util.AsnUtil;
import com.dt.platform.utils.ConverterUtil;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/21 15:17
 */
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class AsnBillClientImpl implements IAsnBillClient {


    @Resource
    private IAsnService iAsnService;

    @Resource
    IAsnDetailService asnDetailService;

    @Resource
    IAsnCarrierService iAsnCarrierService;

    @Resource
    IAsnSupplierService iAsnSupplierService;

    @Resource
    IAsnReceiverService iAsnReceiverService;

    @Resource
    IAsnLogService asnLogService;

    @Resource
    private IRelatedBillService relatedBillService;

    @Resource
    private IScanSnReceiveService scanSnReceiveService;

    @Resource
    private IReceiptService receiptService;

    @Resource
    private IReceiptExtraService receiptExtraService;

    @Resource
    private IReceiptExtraDetailService receiptExtraDetailService;

    @Resource
    private IReceiptDetailService receiptDetailService;

    @Resource
    private ITallyService tallyService;

    @Resource
    private ITallyDetailService tallyDetailService;


    @Resource
    private AsnUtil anUtil;

    @Resource
    private AsnDetailUtil asnDetailUtil;

    @Resource
    private IShelfService shelfService;

    @Resource
    private IShelfDetailService shelfDetailService;

    @Resource
    private IMessageMqService messageMqService;

    @Resource
    private ISalesReturnOrderService salesReturnOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> cancelAsn(AsnCancelBO asnCancelBO) {
        boolean updateById = iAsnService.updateById(ConverterUtil.convert(asnCancelBO.getAsnDTO(), Asn.class));
        if (!updateById) throw new BaseException(BaseBizEnum.TIP, "取消收货更新入库单失败");
        if (CollectionUtil.isEmpty(asnCancelBO.getAsnDetailDTOList()))
            throw new BaseException(BaseBizEnum.TIP, "取消收货收货单明细不能为空");
        for (AsnDetailDTO asnDetailDTO : asnCancelBO.getAsnDetailDTOList()) {
            boolean update = asnDetailService.updateById(ConverterUtil.convert(asnDetailDTO, AsnDetail.class));
            if (!update) throw new BaseException(BaseBizEnum.TIP, "取消收货更新入库单明细失败");
        }
        if (null != asnCancelBO.getAsnLogDTO()) {
            asnLogService.save(ConverterUtil.convert(asnCancelBO.getAsnLogDTO(), AsnLog.class));
        }
        if (null != asnCancelBO.getReceiptDTOList()) {
            for (ReceiptDTO receiptDTO : asnCancelBO.getReceiptDTOList()) {
                boolean update = receiptService.updateById(ConverterUtil.convert(receiptDTO, Receipt.class));
                if (!update) throw new BaseException(BaseBizEnum.TIP, "取消收货更新收货作业批次失败");
            }
            //判定是否需要移除SN
            if (!CollectionUtils.isEmpty(asnCancelBO.getReceiptDTOList())) {
                List<ScanSnReceive> scanSnReceiveList = scanSnReceiveService.list(Wrappers.<ScanSnReceive>query().lambda()
                        .select(ScanSnReceive::getRecId, ScanSnReceive::getId)
                        .in(ScanSnReceive::getRecId, asnCancelBO.getReceiptDTOList().stream().map(ReceiptDTO::getRecId).collect(Collectors.toList())));
                if (!CollectionUtils.isEmpty(scanSnReceiveList)) {
                    scanSnReceiveList.forEach(scanSnReceive -> {
                        Boolean removeScan = scanSnReceiveService.removeById(scanSnReceive.getId());
                        if (!removeScan) {
                            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                        }
                    });
                }
            }
        }
        if (null != asnCancelBO.getReceiptDetailDTOList()) {
            for (ReceiptDetailDTO receiptDetailDTO : asnCancelBO.getReceiptDetailDTOList()) {
                boolean update = receiptDetailService.updateById(ConverterUtil.convert(receiptDetailDTO, ReceiptDetail.class));
                if (!update) throw new BaseException(BaseBizEnum.TIP, "取消收货更新收货作业批次明细失败");
            }
        }
        if (null != asnCancelBO.getReceiptExtraDTOList()) {
            for (ReceiptExtraDTO receiptExtraDTO : asnCancelBO.getReceiptExtraDTOList()) {
                boolean update = receiptExtraService.updateById(ConverterUtil.convert(receiptExtraDTO, ReceiptExtra.class));
                if (!update) throw new BaseException(BaseBizEnum.TIP, "取消收货更新多货失败");
            }
        }
        if (null != asnCancelBO.getReceiptExtraDetailDTOList()) {
            for (ReceiptExtraDetailDTO receiptExtraDetailDTO : asnCancelBO.getReceiptExtraDetailDTOList()) {
                boolean update = receiptExtraDetailService.updateById(ConverterUtil.convert(receiptExtraDetailDTO, ReceiptExtraDetail.class));
                if (!update) throw new BaseException(BaseBizEnum.TIP, "取消收货更新多货明细失败");
            }
        }
        if (null != asnCancelBO.getShelfDTOList()) {
            for (ShelfDTO shelfDTO : asnCancelBO.getShelfDTOList()) {
                boolean update = shelfService.updateById(ConverterUtil.convert(shelfDTO, Shelf.class));
                if (!update) throw new BaseException(BaseBizEnum.TIP, "取消收货更新上架单失败");
            }
        }
        if (null != asnCancelBO.getShelfDetailDTOList()) {
            for (ShelfDetailDTO shelfDetailDTO : asnCancelBO.getShelfDetailDTOList()) {
                boolean update = shelfDetailService.updateById(ConverterUtil.convert(shelfDetailDTO, ShelfDetail.class));
                if (!update) throw new BaseException(BaseBizEnum.TIP, "取消收货更新上架单明细失败");
            }
        }
        if (CollectionUtil.isNotEmpty(asnCancelBO.getMessageMqDTOList())) {
            boolean saveBatch = messageMqService.saveBatch(ConverterUtil.convertList(asnCancelBO.getMessageMqDTOList(), MessageMq.class));
            if (!saveBatch) throw new BaseException(BaseBizEnum.TIP, "取消收货保存本地事务消息失败");
        }

        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(AsnParam param) {
        LambdaQueryWrapper<Asn> wrapper = anUtil.getQueryWrapper(param);
        Integer count = iAsnService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<AsnDTO> get(AsnParam param) {
        LambdaQueryWrapper<Asn> queryWrapper = anUtil.getQueryWrapper(param);
        Asn asn = iAsnService.getOne(queryWrapper);
        AsnDTO result = ConverterUtil.convert(asn, AsnDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<AsnDTO>> getList(AsnParam param) {
        Boolean flag = getResult(param);
        if (flag) {
            Result.success(Lists.newArrayList());
        }
        LambdaQueryWrapper<Asn> queryWrapper = anUtil.getQueryWrapper(param);
        List<Asn> asnList = iAsnService.list(queryWrapper);
        List<AsnDTO> result = ConverterUtil.convertList(asnList, AsnDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<AsnLogDTO>> getAsnLogList(AsnParam param) {
        List<AsnLog> logs = asnLogService.list(Wrappers.<AsnLog>query().lambda()
                .eq(AsnLog::getAsnId, param.getAsnId())
                .like(AsnLog::getMsg, param.getMsg()))
//                .orderByDesc(AsnLog::getCreatedTime))
                ;
        return Result.success(ConverterUtil.convertList(logs, AsnLogDTO.class));
    }

    @Override
    public Result<Page<AsnDTO>> getPage(AsnParam param) {
        Boolean flag = getResult(param);
        if (flag) {
            return Result.success(new Page());
        }
        Page<Asn> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<Asn> queryWrapper = anUtil.getQueryWrapper(param);
        IPage<Asn> asnIPage = iAsnService.page(page, queryWrapper);
        Page<AsnDTO> result = ConverterUtil.convertPage(asnIPage, AsnDTO.class);
        return Result.success(result);
    }

    private Boolean getResult(AsnParam param) {
        if (!CollectionUtils.isEmpty(param.getSkuCodeList())) {
            List<AsnDetail> details = asnDetailService.list(Wrappers.<AsnDetail>query().lambda().in(AsnDetail::getSkuCode, param.getSkuCodeList()));
            if (!CollectionUtils.isEmpty(details)) {
                if (CollectionUtils.isEmpty(param.getAsnIdList())) {
                    param.setAsnIdList(details.stream().map(AsnDetail::getAsnId).collect(Collectors.toList()));
                } else {
                    List<String> asnIds = details.stream().map(AsnDetail::getAsnId).collect(Collectors.toList());
                    asnIds.retainAll(param.getAsnIdList());
                    if (!CollectionUtils.isEmpty(asnIds)) {
                        param.setAsnIdList(asnIds);
                    } else {
                        return Boolean.TRUE;
                    }
                }
            } else {
                return Boolean.TRUE;
            }
        }
        if (!StringUtils.isEmpty(param.getSkuCode())) {
            List<AsnDetail> details = asnDetailService.list(Wrappers.<AsnDetail>query().lambda().eq(AsnDetail::getSkuCode, param.getSkuCode()));
            if (!CollectionUtils.isEmpty(details)) {
                if (CollectionUtils.isEmpty(param.getAsnIdList())) {
                    param.setAsnIdList(details.stream().map(AsnDetail::getAsnId).collect(Collectors.toList()));
                } else {
                    List<String> asnIds = details.stream().map(AsnDetail::getAsnId).collect(Collectors.toList());
                    asnIds.retainAll(param.getAsnIdList());
                    if (!CollectionUtils.isEmpty(asnIds)) {
                        param.setAsnIdList(asnIds);
                    } else {
                        return Boolean.TRUE;
                    }
                }
            } else {
                return Boolean.TRUE;
            }
        }
        if (!CollectionUtils.isEmpty(param.getUpcCodeList())) {
            List<AsnDetail> details = asnDetailService.list(Wrappers.<AsnDetail>query().lambda().in(AsnDetail::getUpcCode, param.getUpcCodeList()));
            if (!CollectionUtils.isEmpty(details)) {
                if (CollectionUtils.isEmpty(param.getAsnIdList())) {
                    param.setAsnIdList(details.stream().map(AsnDetail::getAsnId).collect(Collectors.toList()));
                } else {
                    List<String> asnIds = details.stream().map(AsnDetail::getAsnId).collect(Collectors.toList());
                    asnIds.retainAll(param.getAsnIdList());
                    if (!CollectionUtils.isEmpty(asnIds)) {
                        param.setAsnIdList(asnIds);
                    } else {
                        return Boolean.TRUE;
                    }
                }
            } else {
                return Boolean.TRUE;
            }
        }
        if (!StringUtils.isEmpty(param.getUpcCode())) {
            List<AsnDetail> details = asnDetailService.list(Wrappers.<AsnDetail>query().lambda().eq(AsnDetail::getUpcCode, param.getUpcCode()));
            if (!CollectionUtils.isEmpty(details)) {
                if (CollectionUtils.isEmpty(param.getAsnIdList())) {
                    param.setAsnIdList(details.stream().map(AsnDetail::getAsnId).collect(Collectors.toList()));
                } else {
                    List<String> asnIds = details.stream().map(AsnDetail::getAsnId).collect(Collectors.toList());
                    asnIds.retainAll(param.getAsnIdList());
                    if (!CollectionUtils.isEmpty(asnIds)) {
                        param.setAsnIdList(asnIds);
                    } else {
                        return Boolean.TRUE;
                    }
                }
            } else {
                return Boolean.TRUE;
            }
        }
        if (!StringUtils.isEmpty(param.getTallyStatus()) || !CollectionUtils.isEmpty(param.getTallyStatusList())) {
            List<Tally> tallyList = new ArrayList<>();
            if (!StringUtils.isEmpty(param.getTallyStatus())) {
                tallyList = tallyService.list(Wrappers.<Tally>query().lambda().select(Tally::getBillNo).eq(Tally::getType, TallyTypeEnum.PURCHASE.getCode()).eq(Tally::getStatus, param.getTallyStatus()));
            }
            if (!CollectionUtils.isEmpty(param.getTallyStatusList())) {
                tallyList = tallyService.list(Wrappers.<Tally>query().lambda().select(Tally::getBillNo).eq(Tally::getType, TallyTypeEnum.PURCHASE.getCode()).in(Tally::getStatus, param.getTallyStatusList()));
            }
            if (!CollectionUtils.isEmpty(tallyList)) {
                if (CollectionUtils.isEmpty(param.getAsnIdList())) {
                    param.setAsnIdList(tallyList.stream().map(Tally::getBillNo).collect(Collectors.toList()));
                } else {
                    List<String> asnIds = tallyList.stream().map(Tally::getBillNo).collect(Collectors.toList());
                    asnIds.retainAll(param.getAsnIdList());
                    if (!CollectionUtils.isEmpty(asnIds)) {
                        param.setAsnIdList(asnIds);
                    } else {
                        return Boolean.TRUE;
                    }
                }
            } else {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyNotifyStatus(String asnId, List<String> recIds, Long notifyTime, Integer notifyStatus) {
        //更新ASN
        Asn asn = iAsnService.getOne(Wrappers.<Asn>query().lambda().eq(Asn::getAsnId, asnId));
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.TIP, "当前单据未找到");
        }
        //更新收货作业批次
        if (!CollectionUtils.isEmpty(recIds)) {
            List<Receipt> receiptList = receiptService.list(Wrappers.<Receipt>query().lambda().in(Receipt::getRecId, recIds));
            if (!CollectionUtils.isEmpty(receiptList)) {
                for (Receipt receipt : receiptList) {
                    receipt.setNotifyTime(notifyTime);
                    if (notifyStatus.equals(NotifyStatusEnum.PART_CALLBACK.getCode())) {
                        receipt.setNotifyStatus(NotifyStatusEnum.SUCCESS.getCode());
                    } else {
                        receipt.setNotifyStatus(notifyStatus);
                    }
                    Boolean updateById = receiptService.updateById(receipt);
                    if (!updateById) {
                        throw new BaseException(BaseBizEnum.TIP, "更新异常");
                    }
                }
            }
            //查询多货

            if (!CollectionUtils.isEmpty(recIds)) {
                List<ReceiptExtra> receiptExtraList = receiptExtraService.list(Wrappers.<ReceiptExtra>query().lambda().in(ReceiptExtra::getRecExtraId, recIds));
                if (!CollectionUtils.isEmpty(receiptExtraList)) {
                    for (ReceiptExtra receiptExtra : receiptExtraList) {
                        receiptExtra.setBackFlag(asn.getAsnId());
                        receiptExtra.setNotifyTime(notifyTime);
                        receiptExtra.setNotifyStatus(notifyStatus);
                        boolean updateById = receiptExtraService.updateById(receiptExtra);
                        if (!updateById) {
                            throw new BaseException(BaseBizEnum.TIP, "更新异常");
                        }
                    }
                }
            }
        }
        //待通知可以变成通知失败
        //部分通知不允许变成通知失败
        if (notifyStatus.equals(NotifyStatusEnum.FAIL.getCode()) &&
                (asn.getNotifyStatus().equals(NotifyStatusEnum.INIT.getCode()) || asn.getNotifyStatus().equals(NotifyStatusEnum.FAIL.getCode()))) {
            asn.setNotifyStatus(NotifyStatusEnum.FAIL.getCode());
        } else if (notifyStatus.equals(NotifyStatusEnum.PART_CALLBACK.getCode()) &&
                (asn.getNotifyStatus().equals(NotifyStatusEnum.INIT.getCode()) || asn.getNotifyStatus().equals(NotifyStatusEnum.FAIL.getCode()))) {
            asn.setNotifyStatus(notifyStatus);
        } else {
            asn.setNotifyStatus(notifyStatus);
        }
        asn.setNotifyTime(notifyTime);
        Boolean updateById = iAsnService.updateById(asn);
        if (!updateById) {
            throw new BaseException(BaseBizEnum.TIP, "更新异常");
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateById(AsnDTO asnDTO) {
        Asn asn = ConverterUtil.convert(asnDTO, Asn.class);
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        //进入修改ASN
        Boolean asnResult = iAsnService.updateById(asn);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyStatus(AsnModifyParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getAsnDTO())
                || StringUtils.isEmpty(param.getDetailDTOList())
                || param.getDetailDTOList().isEmpty()) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Asn asn = ConverterUtil.convert(param.getAsnDTO(), Asn.class);
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        List<AsnDetail> asnDetails = ConverterUtil.convertList(param.getDetailDTOList(), AsnDetail.class);
        if (StringUtils.isEmpty(asnDetails) || asnDetails.isEmpty()) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        //进入修改ASN
        Boolean asnResult = iAsnService.updateById(asn);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //全部修ASN_DETAIL
        asnDetails.stream().forEach(entity -> {
            Boolean result = asnDetailService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        //插入日志
        if (ObjectUtil.isNotEmpty(param.getAsnLogDTO())) {
            Boolean result = asnLogService.save(ConverterUtil.convert(param.getAsnLogDTO(), AsnLog.class));
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        return Result.success(true);
    }

    @Override
    public Result<AsnDetailDataDTO> queryAsnDetail(AsnParam param) {
        AsnDetailDataDTO dataDTO = new AsnDetailDataDTO();

        Asn asn = iAsnService.getOne(Wrappers.<Asn>query().lambda().eq(Asn::getAsnId, param.getAsnId()));
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.TIP, "找不到对应到货通知单");
        }
        dataDTO.setAsnDTO(ConverterUtil.convert(asn, AsnDTO.class));
        List<AsnDetail> details = asnDetailService.list(Wrappers.<AsnDetail>query().lambda().eq(AsnDetail::getAsnId, param.getAsnId()));
//        List<AsnDetail> details = iAsnDetailService.list(asnDetailUtil.getQueryWrapper(ConverterUtil.convert(param, AsnDetailParam.class)));
        if (StringUtils.isEmpty(details)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        //设置ASN明细
        dataDTO.setDetailList(ConverterUtil.convertList(details, AsnDetailDTO.class));
        //获取承运商
        List<AsnCarrier> carriers = iAsnCarrierService.list(Wrappers.<AsnCarrier>query().lambda().eq(AsnCarrier::getAsnId, param.getAsnId()));
        dataDTO.setCarrierList(ConverterUtil.convertList(carriers, AsnCarrierDTO.class));
        //获取日志 限制只获取前10条
        List<AsnLog> logs = asnLogService.list(Wrappers.<AsnLog>query().lambda().eq(AsnLog::getAsnId, param.getAsnId()).orderByDesc(AsnLog::getCreatedTime).last("limit 0,10"));
        dataDTO.setLogList(ConverterUtil.convertList(logs, AsnLogDTO.class));
        //获取发货方
        dataDTO.setReceiver(ConverterUtil.convert(iAsnReceiverService.getOne(Wrappers.<AsnReceiver>query().lambda().eq(AsnReceiver::getAsnId, param.getAsnId())), AsnReceiverDTO.class));
        //获取收货方
        dataDTO.setSupplier(ConverterUtil.convert(iAsnSupplierService.getOne(Wrappers.<AsnSupplier>query().lambda().eq(AsnSupplier::getAsnId, param.getAsnId())), AsnSupplierDTO.class));
        return Result.success(dataDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> receiptAsn(AsnReceiptDataDTO dataDTO) {
        //新增Asn主表
        if (dataDTO.getAsnDTO() != null && dataDTO.getAsnDTO().getOrderTag() != null
                && dataDTO.getAsnDTO().getOrderTag() > 0
                && JSONUtil.isJson(dataDTO.getAsnDTO().getExtraJson())
                && AsnOrderTagEnum.NumToEnum(dataDTO.getAsnDTO().getOrderTag()).contains(AsnOrderTagEnum.TAO_TIAN_ORDER)) {
            String tobFulfillOrderNo = getTobFulfillOrderNo(dataDTO.getAsnDTO());
            if (!StringUtils.isEmpty(tobFulfillOrderNo) && StringUtils.isEmpty(dataDTO.getAsnDTO().getDeclarationOrderNo())) {
                dataDTO.getAsnDTO().setDeclarationOrderNo(tobFulfillOrderNo);
            }
        }
        if (dataDTO.getAsnDTO().getOrderTag() != null && AsnOrderTagEnum.NumToEnum(dataDTO.getAsnDTO().getOrderTag()).contains(AsnOrderTagEnum.TAOTIAN_XT) &&
                !StringUtils.isEmpty(dataDTO.getAsnDTO().getExtraJson()) && JSONUtil.isJson(dataDTO.getAsnDTO().getExtraJson())) {
            JSONObject jsonObject = JSONUtil.parseObj(dataDTO.getAsnDTO().getExtraJson());
            if (jsonObject.containsKey("refundFcUnitCode")) {
                String refundFcUnitCode = jsonObject.getStr("refundFcUnitCode");
                if (StrUtil.isNotBlank(refundFcUnitCode)) {
                    LambdaQueryWrapper<SalesReturnOrder> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(SalesReturnOrder::getSalesReturnOrderNo, refundFcUnitCode);
                    SalesReturnOrder one = salesReturnOrderService.getOne(queryWrapper);
                    if (null != one && StrUtil.isBlank(dataDTO.getAsnDTO().getExpressNo())) {
                        dataDTO.getAsnDTO().setExpressNo(one.getReverseExpressNo());
                    }
                }
            }
        }
        iAsnService.save(ConverterUtil.convert(dataDTO.getAsnDTO(), Asn.class));
        //新增Asn明细
        asnDetailService.saveBatch(ConverterUtil.convertList(dataDTO.getDetailList(), AsnDetail.class));
        //新增Asn承运商
        List<AsnCarrier> asnCarrierList = ConverterUtil.convertList(dataDTO.getCarrierList(), AsnCarrier.class);
        if (!CollectionUtils.isEmpty(asnCarrierList)) {
            iAsnCarrierService.saveBatch(ConverterUtil.convertList(dataDTO.getCarrierList(), AsnCarrier.class));
        }
        //新增Asn发货方
        if (dataDTO.getSupplier() != null) {
            iAsnSupplierService.save(ConverterUtil.convert(dataDTO.getSupplier(), AsnSupplier.class));
        }
        //新增Asn收货方
        if (dataDTO.getReceiver() != null) {
            iAsnReceiverService.save(ConverterUtil.convert(dataDTO.getReceiver(), AsnReceiver.class));
        }
        //新增创建Asn的
        if (dataDTO.getLog() != null) {
            asnLogService.save(ConverterUtil.convert(dataDTO.getLog(), AsnLog.class));
        }
        //淘天销退需要存储属性
        List<RelatedBill> relatedBillList = new ArrayList<>();
        if (dataDTO.getAsnDTO().getOrderTag() != null && AsnOrderTagEnum.NumToEnum(dataDTO.getAsnDTO().getOrderTag()).contains(AsnOrderTagEnum.TAOTIAN_XT) &&
                !StringUtils.isEmpty(dataDTO.getAsnDTO().getExtraJson()) && JSONUtil.isJson(dataDTO.getAsnDTO().getExtraJson())) {
            JSONObject jsonObject = JSONUtil.parseObj(dataDTO.getAsnDTO().getExtraJson());
            if (jsonObject.containsKey("refundFcUnitCode")) {
                String refundFcUnitCode = jsonObject.getStr("refundFcUnitCode");
                if (!StringUtils.isEmpty(refundFcUnitCode)) {
                    RelatedBill relatedBill = buildRelatedBill(dataDTO.getAsnDTO(), refundFcUnitCode, RelatedBillTypeEnum.BILL_TYPE_ASN_XT);
                    relatedBillList.add(relatedBill);
                }
            }
            if (!CollectionUtils.isEmpty(relatedBillList)) {
                boolean save = relatedBillService.saveBatch(relatedBillList);
                if (!save) {
                    throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
                }
            }
        }
        return Result.success(true);
    }

    /**
     * @param asnDTO
     * @return java.lang.String
     * <AUTHOR>
     * @describe: 淘天获取清关关联单号
     * @date 2024/4/3 15:41
     */
    private String getTobFulfillOrderNo(AsnDTO asnDTO) {
        String orderCode = "";
        try {
            JSONObject jsonObject = JSONUtil.parseObj(asnDTO.getExtraJson());
            if (!jsonObject.containsKey("relatedOrders") || StringUtils.isEmpty(jsonObject.getStr("relatedOrders"))
                    || !JSONUtil.isJsonArray(jsonObject.getStr("relatedOrders"))) {
                return orderCode;
            }
            JSONArray relatedOrders = JSONUtil.parseArray(jsonObject.getStr("relatedOrders"));
            List<RelateOrdersTaoTianDTO> relateOrders = JSONUtil.toList(relatedOrders, RelateOrdersTaoTianDTO.class);
            if (CollectionUtils.isEmpty(relatedOrders)) {
                return orderCode;
            }
            RelateOrdersTaoTianDTO relateOrder = relateOrders.stream().filter(a -> a.getOrderType().equals("TOB_FULFILL")).findFirst().orElse(null);
            if (relateOrder != null) {
                orderCode = relateOrder.getOrderCode();
            }
        } catch (Exception e) {
            //..ex
        }
        return orderCode;
    }

    /**
     * @param asnDTO
     * @param refundFcUnitCode
     * @param billTypeShipmentXt
     * @return com.dt.domain.bill.related.entity.RelatedBill
     * <AUTHOR>
     * @describe:
     * @date 2024/3/17 14:19
     */
    private RelatedBill buildRelatedBill(AsnDTO asnDTO, String refundFcUnitCode, RelatedBillTypeEnum billTypeShipmentXt) {
        RelatedBill relatedBill = new RelatedBill();
        relatedBill.setWarehouseCode(asnDTO.getWarehouseCode());
        relatedBill.setCargoCode(asnDTO.getCargoCode());
        relatedBill.setBillNo(asnDTO.getAsnId());
        relatedBill.setRelatedNo(refundFcUnitCode);
        relatedBill.setType(billTypeShipmentXt.getType());
        return relatedBill;
    }

    @Override
    public Result<Boolean> checkSoNoExistsByCargoCode(AsnParam param) {
        Integer i = iAsnService.count(Wrappers.<Asn>query().lambda().eq(Asn::getCargoCode, param.getCargoCode())
                .eq(Asn::getSoNo, param.getSoNo()).ne(Asn::getStatus, "00"));
        return Result.success(i >= 1);
    }

    @Override
    public Result<AsnDTO> queryAsnBySoNo(AsnParam param) {
        Asn asn = iAsnService.getOne(Wrappers.<Asn>query().lambda()
                .eq(!StringUtils.isEmpty(param.getCargoCode()), Asn::getCargoCode, param.getCargoCode())
                .eq(!StringUtils.isEmpty(param.getWarehouseCode()), Asn::getWarehouseCode, param.getWarehouseCode())
                .eq(!StringUtils.isEmpty(param.getSoNo()), Asn::getSoNo, param.getSoNo())
                .ne(!StringUtils.isEmpty(param.getStatus()), Asn::getStatus, param.getStatus()));
        return Result.success(ConverterUtil.convert(asn, AsnDTO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> commitAsn(AsnDTO asnDTO) {
        Asn asn = ConverterUtil.convert(asnDTO, Asn.class);
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        List<AsnDetail> asnDetails = ConverterUtil.convertList(asnDTO.getAsnDetailDTOS(), AsnDetail.class);
        if (StringUtils.isEmpty(asnDetails) || asnDetails.isEmpty()) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //进入修改ASN
        Boolean asnResult = iAsnService.updateById(asn);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //全部修ASN_DETAIL
        asnDetails.stream().forEach(entity -> {
            Boolean result = asnDetailService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        if (!StringUtils.isEmpty(asnDTO.getAsnLogDTO())) {
            //新增创建Asn的
            asnLogService.save(ConverterUtil.convert(asnDTO.getAsnLogDTO(), AsnLog.class));
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Integer> updateAsnPrintNum(AsnParam asnParam) {
        if (CollectionUtils.isEmpty(asnParam.getAsnIdList())) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(iAsnService.updatePrintNum(false, Wrappers.<Asn>query().lambda().in(Asn::getAsnId, asnParam.getAsnIdList())));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Integer> updateRecPrintNum(AsnParam asnParam) {
        if (CollectionUtils.isEmpty(asnParam.getAsnIdList())) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(iAsnService.updatePrintNum(true, Wrappers.<Asn>query().lambda().in(Asn::getAsnId, asnParam.getAsnIdList())));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveAsnLog(AsnLogDTO asnLogDTO) {
        Boolean result = asnLogService.save(ConverterUtil.convert(asnLogDTO, AsnLog.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<Page<AsnDetailDTO>> queryDetailPage(AsnParam param) {
        Page<AsnDetail> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<AsnDetail> queryWrapper = asnDetailUtil.getQueryWrapper(ConverterUtil.convert(param, AsnDetailParam.class));
        IPage<AsnDetail> asnIPage = asnDetailService.page(page, queryWrapper);
        Page<AsnDetailDTO> result = ConverterUtil.convertPage(asnIPage, AsnDetailDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<AsnDetailDTO>> getDetailList(AsnParam param) {
        LambdaQueryWrapper<AsnDetail> queryWrapper = asnDetailUtil.getQueryWrapper(ConverterUtil.convert(param, AsnDetailParam.class));
        List<AsnDetail> list = asnDetailService.list(queryWrapper);
        List<AsnDetailDTO> dtoList = ConverterUtil.convertList(list, AsnDetailDTO.class);
        if (dtoList != null) {
            java.util.HashMap<String, Asn> map = new HashMap<>();
            dtoList.forEach(s -> {
                Asn asn = map.get(s.getAsnId());
                if (asn == null) {
                    asn = iAsnService.getOne(Wrappers.<Asn>query().lambda().eq(Asn::getAsnId, s.getAsnId()));
                    if (asn != null) {
                        map.put(s.getAsnId(), asn);
                    }
                }
                if (asn != null) {
                    s.setSoNo(asn.getSoNo());
                    s.setStatus(asn.getStatus());
                    s.setCreatedTime(asn.getCreatedTime());
                }
            });
        }
        return Result.success(dtoList);
    }


    @Override
    public Result<Page<AsnDetailDTO>> getPageGroupDetail(AsnParam param) {
        Page<AsnDetail> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
//        LambdaQueryWrapper<AsnDetail> queryWrapper = asnDetailUtil.getQueryWrapper(ConverterUtil.convert(param, AsnDetailParam.class));
        LambdaQueryWrapper<AsnDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AsnDetail::getAsnId, param.getAsnId());
        IPage<AsnDetail> asnIPage = asnDetailService.getPageGroupDetail(page, queryWrapper);
        Page<AsnDetailDTO> result = ConverterUtil.convertPage(asnIPage, AsnDetailDTO.class);
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatch(AsnBillCompleteBO asnCompleteBO) {
        List<Asn> asnList = ConverterUtil.convertList(asnCompleteBO.getAsnDTOList(), Asn.class);
        if (CollectionUtils.isEmpty(asnList)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        List<AsnDetail> asnDetails = ConverterUtil.convertList(asnCompleteBO.getAsnDetailDTOList(), AsnDetail.class);
        if (CollectionUtils.isEmpty(asnDetails)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        //全部修ASN_DETAIL
        asnDetails.stream().forEach(entity -> {
            Boolean result = asnDetailService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        //记录日志
        List<AsnLog> asnLogList = ConverterUtil.convertList(asnCompleteBO.getAsnLogDTOList(), AsnLog.class);
        Boolean asnResult = asnLogService.saveBatch(asnLogList);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //进入修改ASN
        asnList.stream().forEach(entity -> {
            Boolean result = iAsnService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        return Result.success(asnResult);
    }

    @Override
    public Result<Page<AsnLogDTO>> queryLogPage(AsnParam asnParam) {
        IPage<AsnLog> page = new Page<>();
        page.setCurrent(asnParam.getCurrentPage());
        page.setSize(asnParam.getPageSize());
        LambdaQueryWrapper<AsnLog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AsnLog::getAsnId, asnParam.getAsnId()).orderByDesc(AsnLog::getCreatedTime);
        IPage<AsnLog> pageResult = asnLogService.page(page, lambdaQueryWrapper);
        Page<AsnLogDTO> asnLogDTOPage = ConverterUtil.convertPage(pageResult, AsnLogDTO.class);
        return Result.success(asnLogDTOPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(AsnDTO asnDTO) {
        Asn asn = ConverterUtil.convert(asnDTO, Asn.class);
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        List<AsnDetail> asnDetails = ConverterUtil.convertList(asnDTO.getAsnDetailDTOS(), AsnDetail.class);
        if (StringUtils.isEmpty(asnDetails) || asnDetails.isEmpty()) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //进入修改ASN
        Boolean asnResult = iAsnService.save(asn);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //全部修ASN_DETAIL
        Boolean detailResult = asnDetailService.saveBatch(asnDetails);
        if (!detailResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        if (!StringUtils.isEmpty(asnDTO.getAsnLogDTO())) {
            //新增创建Asn的
            asnLogService.save(ConverterUtil.convert(asnDTO.getAsnLogDTO(), AsnLog.class));
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyAndRomve(AsnDTO asnDTO) {
        Asn asn = ConverterUtil.convert(asnDTO, Asn.class);
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        List<AsnDetail> asnDetails = ConverterUtil.convertList(asnDTO.getAsnDetailDTOS(), AsnDetail.class);
        if (StringUtils.isEmpty(asnDetails) || asnDetails.isEmpty()) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //进入修改ASN
        Boolean result = iAsnService.updateById(asn);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //全部修ASN_DETAIL
        List<AsnDetail> addDetailList = asnDetails.stream().filter(a -> a.getDeleted().equals(1)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(addDetailList)) {
            result = asnDetailService.saveBatch(addDetailList);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        List<AsnDetail> removeDetailList = asnDetails.stream().filter(a -> a.getDeleted().equals(-1)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(removeDetailList)) {
            result = asnDetailService.removeByIds(removeDetailList.stream().map(AsnDetail::getId).collect(Collectors.toList()));
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        if (!StringUtils.isEmpty(asnDTO.getAsnLogDTO())) {
            //新增创建Asn的
            asnLogService.save(ConverterUtil.convert(asnDTO.getAsnLogDTO(), AsnLog.class));
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> cancelBatch(AsnBillCancelBO asnBillCancelBO) {
        List<Asn> asnList = ConverterUtil.convertList(asnBillCancelBO.getAsnDTOList(), Asn.class);
        if (CollectionUtils.isEmpty(asnList)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        List<AsnDetail> asnDetailList = ConverterUtil.convertList(asnBillCancelBO.getAsnDetailDTOList(), AsnDetail.class);
        if (CollectionUtils.isEmpty(asnDetailList)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        //进入修改ASN
        Boolean asnResult = false;
        for (Asn asn : asnList) {
            asnResult = iAsnService.updateById(asn);
            if (!asnResult) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        //全部修ASN_DETAIL
        for (AsnDetail asnDetail : asnDetailList) {
            asnResult = asnDetailService.updateById(asnDetail);
            if (!asnResult) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        //记录日志
        List<AsnLog> asnLogList = ConverterUtil.convertList(asnBillCancelBO.getAsnLogDTOList(), AsnLog.class);
        asnResult = asnLogService.saveBatch(asnLogList);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(asnResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveImportBatch(AsnBillImportBO asnBillImportBO) {
        List<Asn> asnList = ConverterUtil.convertList(asnBillImportBO.getAsnDTOList(), Asn.class);
        if (CollectionUtils.isEmpty(asnList)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        List<AsnDetail> asnDetailList = ConverterUtil.convertList(asnBillImportBO.getAsnDetailDTOList(), AsnDetail.class);
        if (CollectionUtils.isEmpty(asnDetailList)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        //进入新增ASN
        Boolean asnResult = iAsnService.saveBatch(asnList);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //全部新增ASN_DETAIL
        asnResult = asnDetailService.saveBatch(asnDetailList);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //记录日志
        List<AsnLog> asnLogList = ConverterUtil.convertList(asnBillImportBO.getAsnLogDTOList(), AsnLog.class);
        asnResult = asnLogService.saveBatch(asnLogList);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(asnResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(AsnDTO asnDTO) {
        Boolean result = iAsnService.updateById(ConverterUtil.convert(asnDTO, Asn.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyArrivalBatch(AsnBatchArrivalBO asnBatchArrivalBO) {
        //进入修改ASN
        Boolean asnResult = false;
        for (AsnDTO asnDTO : asnBatchArrivalBO.getAsnDTOList()) {
            asnResult = iAsnService.updateById(ConverterUtil.convert(asnDTO, Asn.class));
            if (!asnResult) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        //全部修ASN_DETAIL
        for (AsnDetailDTO asnDetailDTO : asnBatchArrivalBO.getAsnDetailDTOList()) {
            asnResult = asnDetailService.updateById(ConverterUtil.convert(asnDetailDTO, AsnDetail.class));
            if (!asnResult) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        //记录日志
        List<AsnLog> asnLogList = ConverterUtil.convertList(asnBatchArrivalBO.getAsnLogDTOList(), AsnLog.class);
        asnResult = asnLogService.saveBatch(asnLogList);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(asnResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatchAsnDTO(List<AsnDTO> asnDTOList) {
        //进入修改ASN
        Boolean asnResult = false;
        for (AsnDTO asnDTO : asnDTOList) {
            asnResult = iAsnService.updateById(ConverterUtil.convert(asnDTO, Asn.class));
            if (!asnResult) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        return Result.success(asnResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveAsnLogBatch(List<AsnLogDTO> asnLogDTOList) {
        //记录日志
        Boolean asnResult = false;
        List<AsnLog> asnLogList = ConverterUtil.convertList(asnLogDTOList, AsnLog.class);
        asnResult = asnLogService.saveBatch(asnLogList);
        if (!asnResult) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(asnResult);
    }

    @Override
    public Result<List<AsnDTO>> getAppointMultipleParam(AsnParam asnParam, List<String> tableFields) {
        LambdaQueryWrapper<Asn> queryWrapper = anUtil.getQueryWrapper(asnParam);
        if (!CollectionUtils.isEmpty(tableFields)) {
            queryWrapper.select(Asn.class, i -> tableFields.contains(i.getColumn()));
        }
        List<Asn> asnList = iAsnService.list(queryWrapper);
        List<AsnDTO> result = ConverterUtil.convertList(asnList, AsnDTO.class);
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyReturnNotifyStatus(String asnId, List<String> recIds, Long notifyTime, Integer notifyStatus) {
        Asn asn = iAsnService.getOne(Wrappers.<Asn>query().lambda().eq(Asn::getAsnId, asnId));
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //更新收货作业批次
        if (!CollectionUtils.isEmpty(recIds)) {
            List<Receipt> receiptList = receiptService.list(Wrappers.<Receipt>query().lambda().in(Receipt::getRecId, recIds));
            if (!CollectionUtils.isEmpty(receiptList)) {
                for (Receipt receipt : receiptList) {
                    receipt.setNotifyTime(notifyTime);
                    receipt.setBackFlag(asnId);
                    receipt.setNotifyStatus(notifyStatus);
                    Boolean updateById = receiptService.updateById(receipt);
                    if (!updateById) {
                        throw new BaseException(BaseBizEnum.TIP, "更新异常");
                    }
                }
            }
        }
        asn.setNotifyStatus(notifyStatus);
        asn.setNotifyTime(notifyTime);
        Boolean updateById = iAsnService.updateById(asn);
        if (!updateById) {
            throw new BaseException(BaseBizEnum.TIP, "更新异常");
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyExtraNotifyStatus(String asnId, List<String> extraRecIdList, long notifyTime, Integer notifyStatus) {
        //更新ASN
        Asn asn = iAsnService.getOne(Wrappers.<Asn>query().lambda().eq(Asn::getAsnId, asnId));
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.TIP, "更新当前单据未找到");
        }
        //更新收货作业批次
        if (!CollectionUtils.isEmpty(extraRecIdList)) {
            List<ReceiptExtra> receiptExtraList = receiptExtraService.list(Wrappers.<ReceiptExtra>query().lambda().in(ReceiptExtra::getRecExtraId, extraRecIdList));
            if (!CollectionUtils.isEmpty(receiptExtraList)) {
                for (ReceiptExtra receiptExtra : receiptExtraList) {
                    receiptExtra.setBackFlag(asn.getAsnId());
                    receiptExtra.setNotifyTime(notifyTime);
                    receiptExtra.setNotifyStatus(notifyStatus);
                    boolean updateById = receiptExtraService.updateById(receiptExtra);
                    if (!updateById) {
                        throw new BaseException(BaseBizEnum.TIP, "更新异常");
                    }
                }
            }
        }
        asn.setNotifyStatus(notifyStatus);
        asn.setNotifyTime(notifyTime);
        boolean updateById = iAsnService.updateById(asn);
        if (!updateById) {
            throw new BaseException(BaseBizEnum.TIP, "更新异常");
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyNotifyPartStatus(String asnId, List<String> recIdList, String backFlag, long notifyTime, Integer notifyStatus) {
        //更新ASN
        Asn asn = iAsnService.getOne(Wrappers.<Asn>query().lambda().eq(Asn::getAsnId, asnId));
        if (StringUtils.isEmpty(asn)) {
            throw new BaseException(BaseBizEnum.TIP, "当前单据未找到");
        }
        //更新收货作业批次
        if (!CollectionUtils.isEmpty(recIdList)) {
            List<Receipt> receiptList = receiptService.list(Wrappers.<Receipt>query().lambda().in(Receipt::getRecId, recIdList));
            if (!CollectionUtils.isEmpty(receiptList)) {
                for (Receipt receipt : receiptList) {
                    if (notifyStatus.equals(NotifyStatusEnum.PART_CALLBACK.getCode())) {
                        if (StringUtils.isEmpty(backFlag)) {
                            receipt.setBackFlag(backFlag);
                        }
                        receipt.setNotifyTime(notifyTime);
                        receipt.setNotifyStatus(NotifyStatusEnum.SUCCESS.getCode());
                    } else {
                        if (StringUtils.isEmpty(backFlag)) {
                            receipt.setBackFlag(backFlag);
                        }
                        receipt.setNotifyTime(notifyTime);
                        receipt.setNotifyStatus(notifyStatus);
                    }
                    Boolean updateById = receiptService.updateById(receipt);
                    if (!updateById) {
                        throw new BaseException(BaseBizEnum.TIP, "更新异常");
                    }
                }
            }
        }
        //待通知可以变成通知失败
        //部分通知不允许变成通知失败
        if (notifyStatus.equals(NotifyStatusEnum.FAIL.getCode()) &&
                (asn.getNotifyStatus().equals(NotifyStatusEnum.INIT.getCode()) || asn.getNotifyStatus().equals(NotifyStatusEnum.FAIL.getCode()))) {
            asn.setNotifyStatus(notifyStatus);
        }
        if (notifyStatus.equals(NotifyStatusEnum.PART_CALLBACK.getCode()) &&
                (asn.getNotifyStatus().equals(NotifyStatusEnum.INIT.getCode()) || asn.getNotifyStatus().equals(NotifyStatusEnum.FAIL.getCode()))) {
            asn.setNotifyStatus(notifyStatus);
        }
        Boolean updateById = iAsnService.updateById(asn);
        if (!updateById) {
            throw new BaseException(BaseBizEnum.TIP, "更新异常");
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> commitBoxDataAsn(AsnDTO asnDTO) {
        boolean update = iAsnService.updateById(ConverterUtil.convert(asnDTO, Asn.class));
        if (!update) {
            throw new BaseException(BaseBizEnum.TIP, "更新异常");
        }
        //更新明细
        for (AsnDetail asnDetail : ConverterUtil.convertList(asnDTO.getAsnDetailDTOS(), AsnDetail.class)) {
            boolean update1 = asnDetailService.saveOrUpdate(asnDetail);
            if (!update1) {
                throw new BaseException(BaseBizEnum.TIP, "更新异常");
            }
        }
        //移除明细
        asnDTO.getRemoveIdDetailList().forEach(id -> {
            boolean removeById = asnDetailService.removeById(id);
            if (!removeById) {
                throw new BaseException(BaseBizEnum.TIP, "更新异常");
            }
        });

        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyAndAddMessage(AsnDTO asnDTO) {
        Boolean result = iAsnService.updateById(ConverterUtil.convert(asnDTO, Asn.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        if (asnDTO.getMessageMqDTO() != null) {
            boolean saveBatch = messageMqService.save(ConverterUtil.convert(asnDTO.getMessageMqDTO(), MessageMq.class));
            if (!saveBatch) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyLinkAsn(AsnLinkModifyBO asnLinkModifyBO) {
        Boolean result = iAsnService.updateById(ConverterUtil.convert(asnLinkModifyBO.getAsnDTO(), Asn.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //移除旧明细
        if (!CollectionUtils.isEmpty(asnLinkModifyBO.getAsnDTO().getAsnDetailDTOS())) {
            //更新明细
            for (AsnDetail asnDetail : ConverterUtil.convertList(asnLinkModifyBO.getAsnDTO().getAsnDetailDTOS(), AsnDetail.class)) {
                boolean update1 = asnDetailService.removeById(asnDetail);
                if (!update1) {
                    throw new BaseException(BaseBizEnum.TIP, "更新异常");
                }
            }
        }

        //覆盖新明细
        if (!CollectionUtils.isEmpty(asnLinkModifyBO.getAsnDTO().getAsnModifyDetailDTOList())) {
            //更新明细
            for (AsnDetail asnDetail : ConverterUtil.convertList(asnLinkModifyBO.getAsnDTO().getAsnModifyDetailDTOList(), AsnDetail.class)) {
                boolean update1 = asnDetailService.save(asnDetail);
                if (!update1) {
                    throw new BaseException(BaseBizEnum.TIP, "更新异常1");
                }
            }
        }

        result = iAsnService.updateById(ConverterUtil.convert(asnLinkModifyBO.getLinkAsnDTO(), Asn.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        if (asnLinkModifyBO.getMessageMqDTO() != null) {
            boolean saveBatch = messageMqService.save(ConverterUtil.convert(asnLinkModifyBO.getMessageMqDTO(), MessageMq.class));
            if (!saveBatch) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        if (!CollectionUtils.isEmpty(asnLinkModifyBO.getReceiptDTOList())) {
            //更新明细
            for (Receipt receipt : ConverterUtil.convertList(asnLinkModifyBO.getReceiptDTOList(), Receipt.class)) {
                boolean update1 = receiptService.updateById(receipt);
                if (!update1) {
                    throw new BaseException(BaseBizEnum.TIP, "更新异常");
                }
            }
        }
        if (!CollectionUtils.isEmpty(asnLinkModifyBO.getReceiptDetailDTOList())) {
            //更新明细
            for (ReceiptDetail receiptDetail : ConverterUtil.convertList(asnLinkModifyBO.getReceiptDetailDTOList(), ReceiptDetail.class)) {
                boolean update1 = receiptDetailService.saveOrUpdate(receiptDetail);
                if (!update1) {
                    throw new BaseException(BaseBizEnum.TIP, "更新异常");
                }
            }
        }
        if (!CollectionUtils.isEmpty(asnLinkModifyBO.getAsnLogDTOList())) {
            //记录日志
            List<AsnLog> asnLogList = ConverterUtil.convertList(asnLinkModifyBO.getAsnLogDTOList(), AsnLog.class);
            boolean update1 = asnLogService.saveBatch(asnLogList);
            if (!update1) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        return Result.success(true);
    }

    @Override
    public Result<Page<AsnDetailDTO>> getPageGroupDetailByReTurn(AsnParam param) {
        Page<AsnDetail> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<AsnDetail> queryWrapper = asnDetailUtil.getQueryWrapper(ConverterUtil.convert(param, AsnDetailParam.class));
        IPage<AsnDetail> asnIPage = asnDetailService.getPageGroupDetailByReTurn(page, queryWrapper);
        Page<AsnDetailDTO> result = ConverterUtil.convertPage(asnIPage, AsnDetailDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<AsnDetailDTO>> getSkuGroupPage(AsnParam asnParam) {
        Page<AsnDetail> page = new Page<>(asnParam.getCurrent(), asnParam.getSize(), asnParam.getSearchCount());
        LambdaQueryWrapper<AsnDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AsnDetail::getAsnId, asnParam.getAsnId());
        IPage<AsnDetail> asnIPage = asnDetailService.getSkuGroupPage(page, queryWrapper);
        Page<AsnDetailDTO> result = ConverterUtil.convertPage(asnIPage, AsnDetailDTO.class);
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> zeroReceive(AsnZeroReceiveBO asnZeroReceiveBO) {
        Boolean result = iAsnService.updateById(ConverterUtil.convert(asnZeroReceiveBO.getAsnDTO(), Asn.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        result = tallyService.save(ConverterUtil.convert(asnZeroReceiveBO.getTallyDTO(), Tally.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        result = tallyDetailService.saveBatch(ConverterUtil.convertList(asnZeroReceiveBO.getTallyDetailDTOList(), TallyDetail.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        //记录日志
        result = asnLogService.save(ConverterUtil.convert(asnZeroReceiveBO.getAsnLogDTO(), AsnLog.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success("");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> modifyAsnByZeroReceive(AsnDTO asnDTO) {
        Boolean result = iAsnService.updateById(ConverterUtil.convert(asnDTO, Asn.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        List<AsnDetail> asnDetailList = ConverterUtil.convertList(asnDTO.getAsnDetailDTOS(), AsnDetail.class);
        for (AsnDetail asnDetail : asnDetailList) {
            result = asnDetailService.updateById(asnDetail);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        //记录日志
        result = asnLogService.saveBatch(ConverterUtil.convertList(asnDTO.getAsnLogDTOList(), AsnLog.class));
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success("");
    }

}
