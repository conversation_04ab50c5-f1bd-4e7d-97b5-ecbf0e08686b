package com.dt.domain.bill.util.rs;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.bill.param.rs.SalesReturnExtraParam;
import com.dt.domain.bill.rs.entity.SalesReturnExtra;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <p>
 * 多货登记
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Component
public class SalesReturnExtraUtil extends QueryWrapper<SalesReturnExtra, SalesReturnExtraParam> {
    @Override
    public LambdaQueryWrapper<SalesReturnExtra> getQueryWrapper(SalesReturnExtraParam param) {
        LambdaQueryWrapper<SalesReturnExtra> lambdaQueryWrapper = super.getQueryWrapper(param);
        lambdaQueryWrapper
                //多货单号
                .eq(!ObjectUtils.isEmpty(param.getSalesReturnExtraOrderNo()), SalesReturnExtra::getSalesReturnExtraOrderNo, param.getSalesReturnExtraOrderNo())
                .in(!ObjectUtils.isEmpty(param.getSalesReturnExtraOrderNoList()), SalesReturnExtra::getSalesReturnExtraOrderNo, param.getSalesReturnExtraOrderNoList())
                //快递公司编码
                .eq(!ObjectUtils.isEmpty(param.getCarrierCode()), SalesReturnExtra::getCarrierCode, param.getCarrierCode())
                .in(!ObjectUtils.isEmpty(param.getCarrierCodeList()), SalesReturnExtra::getCarrierCode, param.getCarrierCodeList())
                //
                .eq(!ObjectUtils.isEmpty(param.getCarrierName()), SalesReturnExtra::getCarrierName, param.getCarrierName())
                //快递单号
                .eq(!ObjectUtils.isEmpty(param.getExpressNo()), SalesReturnExtra::getExpressNo, param.getExpressNo())
                .in(!ObjectUtils.isEmpty(param.getExpressNoList()), SalesReturnExtra::getExpressNo, param.getExpressNoList())
                //多货状态
                .eq(!ObjectUtils.isEmpty(param.getStatus()), SalesReturnExtra::getStatus, param.getStatus())
                .in(!ObjectUtils.isEmpty(param.getStatusList()), SalesReturnExtra::getStatus, param.getStatusList())
                //是否破损 1 是 2 否
                .eq(!ObjectUtils.isEmpty(param.getDamage()), SalesReturnExtra::getDamage, param.getDamage())
                //姓名
                .eq(!ObjectUtils.isEmpty(param.getReceiveName()), SalesReturnExtra::getReceiveName, param.getReceiveName())
                .like(!ObjectUtils.isEmpty(param.getReceiveNameLike()), SalesReturnExtra::getReceiveName, param.getReceiveNameLike())
                //电话
                .eq(!ObjectUtils.isEmpty(param.getReceiveTel()), SalesReturnExtra::getReceiveTel, param.getReceiveTel())
                //暂存位
                .eq(!ObjectUtils.isEmpty(param.getLocationCode()), SalesReturnExtra::getLocationCode, param.getLocationCode())
                //操作人
                .eq(!ObjectUtils.isEmpty(param.getOpBy()), SalesReturnExtra::getOpBy, param.getOpBy())
                //拓展字段
                .eq(!ObjectUtils.isEmpty(param.getExtraJson()), SalesReturnExtra::getExtraJson, param.getExtraJson())
                //登记时间
                .gt(!StringUtils.isEmpty(param.getHandoverTimeStart()), SalesReturnExtra::getHandoverTime, param.getHandoverTimeStart())
                .lt(!StringUtils.isEmpty(param.getHandoverTimeEnd()), SalesReturnExtra::getHandoverTime, param.getHandoverTimeEnd())
                .eq(StrUtil.isNotBlank(param.getBackNo()),SalesReturnExtra::getBackNo,param.getBackNo())
                .in(CollectionUtil.isNotEmpty(param.getBackNoList()),SalesReturnExtra::getBackNo,param.getBackNoList())
                .in(CollectionUtil.isNotEmpty(param.getOriginExpressNoList()),SalesReturnExtra::getOriginExpressNo,param.getOriginExpressNoList())
                .in(CollectionUtil.isNotEmpty(param.getOriginCargoNameList()),SalesReturnExtra::getOriginCargoName,param.getOriginCargoNameList())
                .in(CollectionUtil.isNotEmpty(param.getOriginOrderNoList()),SalesReturnExtra::getOriginOrderNo,param.getOriginOrderNoList())
                .in(CollectionUtil.isNotEmpty(param.getOriginWarehouseNameList()),SalesReturnExtra::getOriginWarehouseName,param.getOriginWarehouseNameList())
        ;
        return lambdaQueryWrapper;
    }
}
