<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.shipment.mapper.ShipmentOrderMapper">

    <!-- 出库单分析查询（分页版） -->
    <select id="getShipmentAnalysisPage" resultType="java.util.Map">
        <![CDATA[
        SELECT
        ]]>
        <if test="param.analysisDimensions != null and param.analysisDimensions.size() > 0">
            <foreach collection="param.analysisDimensions" item="dimension" separator=",">
                <choose>
                    <when test="dimension == 'cargoCode'">
                        <![CDATA[ cargo_code as cargoCode ]]>
                    </when>
                    <when test="dimension == 'businessType'">
                        <![CDATA[ business_type as businessType ]]>
                    </when>
                    <when test="dimension == 'salePlatform'">
                        <![CDATA[ sale_Platform as salePlatform ]]>
                    </when>
                    <when test="dimension == 'packageStruct'">
                        <![CDATA[ package_Struct as packageStruct ]]>
                    </when>
                    <when test="dimension == 'expOutStockDate_day'">
                        <![CDATA[ CASE
                            WHEN exp_out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(exp_out_stock_date / 1000), '%Y-%m-%d')
                        END AS expOutStockDate_day ]]>
                    </when>
                    <when test="dimension == 'expOutStockDate_hour'">
                        <![CDATA[ CASE
                            WHEN exp_out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(exp_out_stock_date / 1000), '%Y-%m-%d %H')
                        END AS expOutStockDate_hour ]]>
                    </when>
                    <when test="dimension == 'createdTime_day'">
                        <![CDATA[ CASE
                            WHEN created_time = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(created_time / 1000), '%Y-%m-%d')
                        END AS createTime_day ]]>
                    </when>
                    <when test="dimension == 'createdTime_hour'">
                        <![CDATA[ CASE
                            WHEN created_time = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(created_time / 1000), '%Y-%m-%d %H')
                        END AS createTime_hour ]]>
                    </when>
                    <when test="dimension == 'payDate_day'">
                        <![CDATA[ CASE
                            WHEN pay_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(pay_date / 1000), '%Y-%m-%d')
                        END AS payDate_day ]]>
                    </when>
                    <when test="dimension == 'payDate_hour'">
                        <![CDATA[ CASE
                            WHEN pay_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(pay_date / 1000), '%Y-%m-%d %H')
                        END AS payDate_hour ]]>
                    </when>
                    <when test="dimension == 'outStockDate_day'">
                        <![CDATA[ CASE
                            WHEN out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(out_stock_date / 1000), '%Y-%m-%d')
                        END AS outStockDate_day ]]>
                    </when>
                    <when test="dimension == 'outStockDate_hour'">
                        <![CDATA[ CASE
                            WHEN out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(out_stock_date / 1000), '%Y-%m-%d %H')
                        END AS outStockDate_hour ]]>
                    </when>
                    <when test="dimension == 'carrierCode'">
                        <![CDATA[ carrier_code as carrierCode ]]>
                    </when>
                    <when test="dimension == 'receiverProvName'">
                        <![CDATA[ receiver_Prov_Name as receiverProvName ]]>
                    </when>
                    <when test="dimension == 'receiverCityName'">
                        <![CDATA[ receiver_city_Name as receiverCityName ]]>
                    </when>
                    <when test="dimension == 'receiverAreaName'">
                        <![CDATA[ receiver_Area_Name as receiverAreaName ]]>
                    </when>
                </choose>
            </foreach>
            ,
        </if>
        <![CDATA[
        COUNT(1) AS orderCount,
        SUM(sku_qty) AS skuQtySum,
        SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) AS createdOrderCount,
        SUM(CASE WHEN status = '15' THEN 1 ELSE 0 END) AS pretreatmentFailOrderCount,
        SUM(CASE WHEN status = '20' THEN 1 ELSE 0 END) AS pretreatmentCompleteOrderCount,
        SUM(CASE WHEN status = '25' THEN 1 ELSE 0 END) AS collectedOrderCount,
        SUM(CASE WHEN status = '30' THEN 1 ELSE 0 END) AS checkStartOrderCount,
        SUM(CASE WHEN status = '35' THEN 1 ELSE 0 END) AS checkCompleteOrderCount,
        SUM(CASE WHEN status = '45' THEN 1 ELSE 0 END) AS partialOutOrderCount,
        SUM(CASE WHEN status = '50' THEN 1 ELSE 0 END) AS outOrderCount,
        SUM(CASE WHEN status = '55' THEN 1 ELSE 0 END) AS interceptOrderCount,
        SUM(CASE WHEN status = '60' THEN 1 ELSE 0 END) AS cancelOrderCount,
        SUM(CASE WHEN status = '40' THEN 1 ELSE 0 END) AS shortageOutOrderCount
        FROM dt_shipment_order
        ]]>
        <where>
            <![CDATA[ deleted = 1 ]]>
            <if test="param.cargoCodeList != null and param.cargoCodeList.size() > 0">
                AND cargo_code IN
                <foreach collection="param.cargoCodeList" item="cargoCode" open="(" separator="," close=")">
                    #{cargoCode}
                </foreach>
            </if>
            <if test="param.businessType != null and param.businessType != ''">
                AND business_type = #{param.businessType}
            </if>
            <if test="param.salePlatform != null and param.salePlatform != ''">
                AND sale_Platform = #{param.salePlatform}
            </if>
            <if test="param.createTimeStart != null">
                <![CDATA[ AND created_time >= #{param.createTimeStart} ]]>
            </if>
            <if test="param.createTimeEnd != null">
                <![CDATA[ AND created_time <= #{param.createTimeEnd} ]]>
            </if>
            <if test="param.payDateStart != null">
                <![CDATA[ AND pay_date >= #{param.payDateStart} ]]>
            </if>
            <if test="param.payDateEnd != null">
                <![CDATA[ AND pay_date <= #{param.payDateEnd} ]]>
            </if>
            <if test="param.expOutStockDateStart != null">
                <![CDATA[ AND exp_out_stock_date >= #{param.expOutStockDateStart} ]]>
            </if>
            <if test="param.expOutStockDateEnd != null">
                <![CDATA[ AND exp_out_stock_date <= #{param.expOutStockDateEnd} ]]>
            </if>
            <if test="param.outStockDateStart != null">
                <![CDATA[ AND out_stock_date >= #{param.outStockDateStart} ]]>
            </if>
            <if test="param.outStockDateEnd != null">
                <![CDATA[ AND out_stock_date <= #{param.outStockDateEnd} ]]>
            </if>
            <if test="param.packageStructList != null and param.packageStructList.size() > 0">
                AND package_Struct IN
                <foreach collection="param.packageStructList" item="packageStruct" open="(" separator="," close=")">
                    #{packageStruct}
                </foreach>
            </if>
            <if test="param.skuTypeQtyMin != null">
                <![CDATA[ AND sku_type_qty >= #{param.skuTypeQtyMin} ]]>
            </if>
            <if test="param.skuTypeQtyMax != null">
                <![CDATA[ AND sku_type_qty <= #{param.skuTypeQtyMax} ]]>
            </if>
            <if test="param.skuQtyMin != null">
                <![CDATA[ AND sku_qty >= #{param.skuQtyMin} ]]>
            </if>
            <if test="param.skuQtyMax != null">
                <![CDATA[ AND sku_qty <= #{param.skuQtyMax} ]]>
            </if>
            <if test="param.weightMin != null">
                <![CDATA[ AND weight >= #{param.weightMin} ]]>
            </if>
            <if test="param.weightMax != null">
                <![CDATA[ AND weight <= #{param.weightMax} ]]>
            </if>
            <if test="param.carrierCode != null and param.carrierCode != ''">
                AND carrier_code = #{param.carrierCode}
            </if>
            <if test="param.receiverProvList != null and param.receiverProvList.size() > 0">
                AND receiver_prov IN
                <foreach collection="param.receiverProvList" item="receiverProvName" open="(" separator=","
                         close=")">
                    #{receiverProvName}
                </foreach>
            </if>
            <if test="param.receiverCityList != null and param.receiverCityList.size() > 0">
                AND receiver_city IN
                <foreach collection="param.receiverCityList" item="receiverCityName" open="(" separator=","
                         close=")">
                    #{receiverCityName}
                </foreach>
            </if>
            <if test="param.receiverAreaList != null and param.receiverAreaList.size() > 0">
                AND receiver_Area IN
                <foreach collection="param.receiverAreaList" item="receiverAreaName" open="(" separator=","
                         close=")">
                    #{receiverAreaName}
                </foreach>
            </if>
        </where>
        <if test="param.analysisDimensions != null and param.analysisDimensions.size() > 0">
            <![CDATA[ GROUP BY ]]>
            <foreach collection="param.analysisDimensions" item="dimension" separator=",">
                <choose>
                    <when test="dimension == 'cargoCode'">
                        <![CDATA[ cargo_code ]]>
                    </when>
                    <when test="dimension == 'businessType'">
                        <![CDATA[ business_type ]]>
                    </when>
                    <when test="dimension == 'salePlatform'">
                        <![CDATA[ sale_Platform ]]>
                    </when>
                    <when test="dimension == 'packageStruct'">
                        <![CDATA[ package_Struct ]]>
                    </when>
                    <when test="dimension == 'expOutStockDate_day'">
                        <![CDATA[ CASE
                            WHEN exp_out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(exp_out_stock_date / 1000), '%Y-%m-%d')
                        END ]]>
                    </when>
                    <when test="dimension == 'expOutStockDate_hour'">
                        <![CDATA[ CASE
                            WHEN exp_out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(exp_out_stock_date / 1000), '%Y-%m-%d %H')
                        END ]]>
                    </when>
                    <when test="dimension == 'createdTime_day'">
                        <![CDATA[ CASE
                            WHEN created_time = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(created_time / 1000), '%Y-%m-%d')
                        END ]]>
                    </when>
                    <when test="dimension == 'createdTime_hour'">
                        <![CDATA[ CASE
                            WHEN created_time = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(created_time / 1000), '%Y-%m-%d %H')
                        END ]]>
                    </when>
                    <when test="dimension == 'payDate_day'">
                        <![CDATA[ CASE
                            WHEN pay_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(pay_date / 1000), '%Y-%m-%d')
                        END ]]>
                    </when>
                    <when test="dimension == 'payDate_hour'">
                        <![CDATA[ CASE
                            WHEN pay_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(pay_date / 1000), '%Y-%m-%d %H')
                        END ]]>
                    </when>
                    <when test="dimension == 'outStockDate_day'">
                        <![CDATA[ CASE
                            WHEN out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(out_stock_date / 1000), '%Y-%m-%d')
                        END ]]>
                    </when>
                    <when test="dimension == 'outStockDate_hour'">
                        <![CDATA[ CASE
                            WHEN out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(out_stock_date / 1000), '%Y-%m-%d %H')
                        END ]]>
                    </when>
                    <when test="dimension == 'carrierCode'">
                        <![CDATA[ carrier_code ]]>
                    </when>
                    <when test="dimension == 'receiverProvName'">
                        <![CDATA[ receiver_Prov_Name ]]>
                    </when>
                    <when test="dimension == 'receiverCityName'">
                        <![CDATA[ receiver_city_Name ]]>
                    </when>
                    <when test="dimension == 'receiverAreaName'">
                        <![CDATA[ receiver_Area_Name ]]>
                    </when>
                </choose>
            </foreach>
        </if>
        <if test="param.sortField != null and param.sortField != ''">
            <![CDATA[ ORDER BY ]]> ${param.sortField}
            <if test="param.sortOrder != null and param.sortOrder != ''">${param.sortOrder}</if>
        </if>
        <if test="param.sortField == null or param.sortField == ''">
            <![CDATA[ ORDER BY orderCount DESC ]]>
        </if>
    </select>

</mapper>
