<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.pkg.mapper.PackageMapper">

    <!-- 包裹分析查询（分页版） -->
    <select id="getPackAnalysisPage" resultType="java.util.Map">
        <![CDATA[
        SELECT
        ]]>
        <if test="param.analysisDimensions != null and param.analysisDimensions.size() > 0">
            <foreach collection="param.analysisDimensions" item="dimension" separator=",">
                <choose>
                    <when test="dimension == 'cargoCode'">
                        <![CDATA[ b.cargo_code as cargoCode ]]>
                    </when>
                    <when test="dimension == 'businessType'">
                        <![CDATA[ b.business_type as businessType ]]>
                    </when>

                    <when test="dimension == 'isPre'">
                        <![CDATA[ b.is_pre as isPre ]]>
                    </when>

                    <when test="dimension == 'salePlatform'">
                        <![CDATA[ b.sale_Platform as salePlatform ]]>
                    </when>
                    <when test="dimension == 'packageStruct'">
                        <![CDATA[ b.package_Struct as packageStruct ]]>
                    </when>
                    <when test="dimension == 'expOutStockDate_day'">
                        <![CDATA[ CASE
                            WHEN a.exp_out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(a.exp_out_stock_date / 1000), '%Y-%m-%d')
                        END AS expOutStockDate_day ]]>
                    </when>
                    <when test="dimension == 'expOutStockDate_hour'">
                        <![CDATA[ CASE
                            WHEN a.exp_out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(a.exp_out_stock_date / 1000), '%Y-%m-%d %H')
                        END AS expOutStockDate_hour ]]>
                    </when>
                    <when test="dimension == 'createdTime_day'">
                        <![CDATA[ CASE
                            WHEN b.created_time = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(b.created_time / 1000), '%Y-%m-%d')
                        END AS createTime_day ]]>
                    </when>
                    <when test="dimension == 'createdTime_hour'">
                        <![CDATA[ CASE
                            WHEN b.created_time = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(b.created_time / 1000), '%Y-%m-%d %H')
                        END AS createTime_hour ]]>
                    </when>
                    <when test="dimension == 'payDate_day'">
                        <![CDATA[ CASE
                            WHEN a.pay_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(a.pay_date / 1000), '%Y-%m-%d')
                        END AS payDate_day ]]>
                    </when>
                    <when test="dimension == 'payDate_hour'">
                        <![CDATA[ CASE
                            WHEN a.pay_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(a.pay_date / 1000), '%Y-%m-%d %H')
                        END AS payDate_hour ]]>
                    </when>

                    <when test="dimension == 'outStockDate_day'">
                        <![CDATA[ CASE
                            WHEN b.out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(b.out_stock_date / 1000), '%Y-%m-%d')
                        END AS outStockDate_day ]]>
                    </when>
                    <when test="dimension == 'outStockDate_hour'">
                        <![CDATA[ CASE
                            WHEN b.out_stock_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(b.out_stock_date / 1000), '%Y-%m-%d %H')
                        END AS outStockDate_hour ]]>
                    </when>

                    <when test="dimension == 'pickCompleteSkuDate_day'">
                        <![CDATA[ CASE
                            WHEN b.pick_complete_sku_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(b.pick_complete_sku_date / 1000), '%Y-%m-%d')
                        END AS pickCompleteSkuDate_day ]]>
                    </when>
                    <when test="dimension == 'pickCompleteSkuDate_hour'">
                        <![CDATA[ CASE
                            WHEN b.pick_complete_sku_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(b.pick_complete_sku_date / 1000), '%Y-%m-%d %H')
                        END AS pickCompleteSkuDate_hour ]]>
                    </when>

                    <when test="dimension == 'checkCompleteDate_day'">
                        <![CDATA[ CASE
                            WHEN b.check_complete_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(b.check_complete_date / 1000), '%Y-%m-%d')
                        END AS checkCompleteDate_day ]]>
                    </when>
                    <when test="dimension == 'checkCompleteDate_hour'">
                        <![CDATA[ CASE
                            WHEN b.check_complete_date = 0 THEN ''
                            ELSE DATE_FORMAT(FROM_UNIXTIME(b.check_complete_date / 1000), '%Y-%m-%d %H')
                        END AS checkCompleteDate_hour ]]>
                    </when>


                    <when test="dimension == 'carrierCode'">
                        <![CDATA[ b.carrier_code as carrierCode ]]>
                    </when>
                    <when test="dimension == 'receiverProvName'">
                        <![CDATA[ a.receiver_Prov_Name as receiverProvName ]]>
                    </when>
                    <when test="dimension == 'receiverCityName'">
                        <![CDATA[ a.receiver_city_Name as receiverCityName ]]>
                    </when>
                    <when test="dimension == 'receiverAreaName'">
                        <![CDATA[ a.receiver_Area_Name as receiverAreaName ]]>
                    </when>
                </choose>
            </foreach>
            ,
        </if>
        <![CDATA[
       COUNT(1) AS orderCount,
        SUM(b.package_sku_qty) AS skuQtySum,
        SUM(b.STATUS='10') AS createdOrderCount,
        SUM(b.STATUS='15') AS pretreatmentFailOrderCount,
        SUM(b.STATUS='20') AS pretreatmentCompleteOrderCount,
		SUM(b.STATUS='25') AS collectedFailOrderCount,
        SUM(b.STATUS='30') AS collectedOrderCount,
		SUM(b.STATUS='35') AS pickStartOrderCount,
        SUM(b.STATUS='40') AS pickEndOrderCount,
        SUM(b.STATUS='45') AS checkStartOrderCount,
        SUM(b.STATUS='50') AS checkCompleteOrderCount,
        SUM(b.STATUS='60') AS outOrderCount,
        SUM(b.STATUS='65') AS interceptOrderCount,
        SUM(b.STATUS='75') AS cancelOrderCount,
		SUM(b.STATUS='70') AS interceptCancelOrderCount,
        SUM(b.STATUS='55') AS shortageOutOrderCount
        FROM dt_shipment_order a ,dt_package b
        ]]>
        <where>
            <![CDATA[ a.deleted=1 and b.deleted=1 and a.shipment_order_code = b.shipment_order_code ]]>
            <if test="param.cargoCodeList != null and param.cargoCodeList.size() > 0">
                AND b.cargo_code IN
                <foreach collection="param.cargoCodeList" item="cargoCode" open="(" separator="," close=")">
                    #{cargoCode}
                </foreach>
            </if>
            <if test="param.businessType != null and param.businessType != ''">
                AND b.business_type = #{param.businessType}
            </if>
            <if test="param.isPre != null and param.isPre != ''">
                AND b.is_pre = #{param.isPre}
            </if>
            <if test="param.salePlatform != null and param.salePlatform != ''">
                AND b.sale_Platform = #{param.salePlatform}
            </if>
            <if test="param.createTimeStart != null">
                <![CDATA[ AND b.created_time >= #{param.createTimeStart} ]]>
            </if>
            <if test="param.createTimeEnd != null">
                <![CDATA[ AND b.created_time <= #{param.createTimeEnd} ]]>
            </if>
            <if test="param.payDateStart != null">
                <![CDATA[ AND a.pay_date >= #{param.payDateStart} ]]>
            </if>
            <if test="param.payDateEnd != null">
                <![CDATA[ AND a.pay_date <= #{param.payDateEnd} ]]>
            </if>
            <if test="param.expOutStockDateStart != null">
                <![CDATA[ AND a.exp_out_stock_date >= #{param.expOutStockDateStart} ]]>
            </if>
            <if test="param.expOutStockDateEnd != null">
                <![CDATA[ AND a.exp_out_stock_date <= #{param.expOutStockDateEnd} ]]>
            </if>
            <if test="param.outStockDateStart != null">
                <![CDATA[ AND a.out_stock_date >= #{param.outStockDateStart} ]]>
            </if>
            <if test="param.outStockDateEnd != null">
                <![CDATA[ AND a.out_stock_date <= #{param.outStockDateEnd} ]]>
            </if>


            <if test="param.pickCompleteDateStart != null">
                <![CDATA[ AND a.pick_complete_sku_date >= #{param.pickCompleteDateStart} ]]>
            </if>
            <if test="param.pickCompleteDateEnd != null">
                <![CDATA[ AND a.pick_complete_sku_date <= #{param.pickCompleteDateEnd} ]]>
            </if>

            <if test="param.checkCompleteDateStart != null">
                <![CDATA[ AND a.check_complete_date >= #{param.checkCompleteDateStart} ]]>
            </if>
            <if test="param.checkCompleteDateEnd != null">
                <![CDATA[ AND a.check_complete_date <= #{param.checkCompleteDateEnd} ]]>
            </if>


            <if test="param.packageStructList != null and param.packageStructList.size() > 0">
                AND b.package_Struct IN
                <foreach collection="param.packageStructList" item="packageStruct" open="(" separator="," close=")">
                    #{packageStruct}
                </foreach>
            </if>
            <if test="param.skuTypeQtyMin != null">
                <![CDATA[ AND b.sku_type_qty >= #{param.skuTypeQtyMin} ]]>
            </if>
            <if test="param.skuTypeQtyMax != null">
                <![CDATA[ AND b.sku_type_qty <= #{param.skuTypeQtyMax} ]]>
            </if>
            <if test="param.skuQtyMin != null">
                <![CDATA[ AND b.package_sku_qty >= #{param.skuQtyMin} ]]>
            </if>
            <if test="param.skuQtyMax != null">
                <![CDATA[ AND b.package_sku_qty <= #{param.skuQtyMax} ]]>
            </if>
            <if test="param.weightMin != null">
                <![CDATA[ AND b.weight >= #{param.weightMin} ]]>
            </if>
            <if test="param.weightMax != null">
                <![CDATA[ AND b.weight <= #{param.weightMax} ]]>
            </if>
            <if test="param.carrierCode != null and param.carrierCode != ''">
                AND b.carrier_code = #{param.carrierCode}
            </if>
            <if test="param.receiverProvList != null and param.receiverProvList.size() > 0">
                AND a.receiver_prov IN
                <foreach collection="param.receiverProvList" item="receiverProvName" open="(" separator=","
                         close=")">
                    #{receiverProvName}
                </foreach>
            </if>
            <if test="param.receiverCityList != null and param.receiverCityList.size() > 0">
                AND a.receiver_city IN
                <foreach collection="param.receiverCityList" item="receiverCityName" open="(" separator=","
                         close=")">
                    #{receiverCityName}
                </foreach>
            </if>
            <if test="param.receiverAreaList != null and param.receiverAreaList.size() > 0">
                AND a.receiver_Area IN
                <foreach collection="param.receiverAreaList" item="receiverAreaName" open="(" separator=","
                         close=")">
                    #{receiverAreaName}
                </foreach>
            </if>
        </where>
        <if test="param.analysisDimensions != null and param.analysisDimensions.size() > 0">
            <![CDATA[ GROUP BY ]]>
            <foreach collection="param.analysisDimensions" item="dimension" separator=",">
                <choose>
                    <when test="dimension == 'cargoCode'">
                        <![CDATA[ b.cargo_code ]]>
                    </when>
                    <when test="dimension == 'businessType'">
                        <![CDATA[ b.business_type ]]>
                    </when>

                    <when test="dimension == 'isPre'">
                        <![CDATA[ b.is_pre ]]>
                    </when>

                    <when test="dimension == 'salePlatform'">
                        <![CDATA[ b.sale_Platform ]]>
                    </when>
                    <when test="dimension == 'packageStruct'">
                        <![CDATA[ b.package_Struct ]]>
                    </when>
                    <when test="dimension == 'expOutStockDate_day'">
                        <![CDATA[  expOutStockDate_day ]]>
                    </when>
                    <when test="dimension == 'expOutStockDate_hour'">
                        <![CDATA[  expOutStockDate_day ]]>
                    </when>
                    <when test="dimension == 'createdTime_day'">
                        <![CDATA[  expOutStockDate_day ]]>
                    </when>
                    <when test="dimension == 'createdTime_hour'">
                        <![CDATA[  expOutStockDate_day ]]>
                    </when>
                    <when test="dimension == 'payDate_day'">
                        <![CDATA[  expOutStockDate_day ]]>
                    </when>
                    <when test="dimension == 'payDate_hour'">
                        <![CDATA[  expOutStockDate_day ]]>
                    </when>
                    <when test="dimension == 'outStockDate_day'">
                        <![CDATA[  expOutStockDate_day ]]>
                    </when>
                    <when test="dimension == 'outStockDate_hour'">
                        <![CDATA[  expOutStockDate_day ]]>
                    </when>

                    <when test="dimension == 'pickCompleteSkuDate_day'">
                        <![CDATA[  pickCompleteSkuDate_day ]]>
                    </when>
                    <when test="dimension == 'pickCompleteSkuDate_hour'">
                        <![CDATA[  pickCompleteSkuDate_hour ]]>
                    </when>

                    <when test="dimension == 'checkCompleteDate_day'">
                        <![CDATA[  checkCompleteDate_day ]]>
                    </when>
                    <when test="dimension == 'checkCompleteDate_hour'">
                        <![CDATA[  checkCompleteDate_hour ]]>
                    </when>


                    <when test="dimension == 'carrierCode'">
                        <![CDATA[ b.carrier_code ]]>
                    </when>
                    <when test="dimension == 'receiverProvName'">
                        <![CDATA[ a.receiver_Prov_Name ]]>
                    </when>
                    <when test="dimension == 'receiverCityName'">
                        <![CDATA[ a.receiver_city_Name ]]>
                    </when>
                    <when test="dimension == 'receiverAreaName'">
                        <![CDATA[ a.receiver_Area_Name ]]>
                    </when>
                </choose>
            </foreach>
        </if>
        <if test="param.sortField != null and param.sortField != ''">
            <![CDATA[ ORDER BY ]]> ${param.sortField}
            <if test="param.sortOrder != null and param.sortOrder != ''">${param.sortOrder}</if>
        </if>
        <if test="param.sortField == null or param.sortField == ''">
            <![CDATA[ ORDER BY orderCount DESC ]]>
        </if>
    </select>
</mapper>
