<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.pkg.mapper.PackageDetainMapper">

    <resultMap id="packageDetainMap" type="com.dt.domain.bill.pkg.entity.PackageDetain">
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>


    <select id="packageDetain" resultMap="packageDetainMap" fetchSize="100">
        SELECT p.status,p.created_time,p.updated_time
        FROM dt_package p join dt_shipment_order s
        on p.shipment_order_code = s.shipment_order_code
        where p.deleted = 1
        <if test="param.cargoCodeList != null and param.cargoCodeList.size() > 0">
            AND p.cargo_code IN
            <foreach collection="param.cargoCodeList" item="cargoCode" open="(" separator="," close=")">
                #{cargoCode}
            </foreach>
        </if>
        <if test="param.businessType != null and param.businessType != ''">
            AND p.business_type = #{param.businessType}
        </if>
        <if test="param.salePlatform != null and param.salePlatform != ''">
            AND p.sale_Platform = #{param.salePlatform}
        </if>
        <if test="param.carrierCode != null and param.carrierCode != ''">
            AND b.carrier_code = #{param.carrierCode}
        </if>
        <if test="param.createdTimeStart != null">
            <![CDATA[ AND b.created_time >= #{param.createdTimeStart} ]]>
        </if>
        <if test="param.createdTimeEnd != null">
            <![CDATA[ AND b.created_time <= #{param.createdTimeEnd} ]]>
        </if>
        <if test="param.payDateStart != null">
            <![CDATA[ AND a.pay_date >= #{param.payDateStart} ]]>
        </if>
        <if test="param.payDateEnd != null">
            <![CDATA[ AND a.pay_date <= #{param.payDateEnd} ]]>
        </if>
        <if test="param.expOutStockDateStart != null">
            <![CDATA[ AND a.exp_out_stock_date >= #{param.expOutStockDateStart} ]]>
        </if>
        <if test="param.expOutStockDateEnd != null">
            <![CDATA[ AND a.exp_out_stock_date <= #{param.expOutStockDateEnd} ]]>
        </if>
    </select>

</mapper>
