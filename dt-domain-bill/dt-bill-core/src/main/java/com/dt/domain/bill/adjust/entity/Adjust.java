package com.dt.domain.bill.adjust.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库存调整
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_adjust")
@ApiModel(value = "Adjust对象", description = "库存调整")
public class Adjust extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "调整单编号")
    private String code;

    @ApiModelProperty(value = "ERP系统对应单号")
    private String erpCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据状态 字典组：ADJUST_STATUS")
    private String status;

    @ApiModelProperty(value = "调整原因 字典组:REASON_TYPE")
    private String reason;

    @ApiModelProperty(value = "调整类型， 调增，调减，")
    private String type;

    @ApiModelProperty(value = "调整描叙")
    private String note;

    @ApiModelProperty(value = "审核日期 时间戳")
    private Long checkerDate;

    @ApiModelProperty(value = "审核人")
    private String checker;

    @ApiModelProperty(value = "完成日期 时间戳")
    private Long completeDate;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "业务场景")
    private String businessType;
    
    @ApiModelProperty(value = "拓传json")
    private String extraJson;

    @ApiModelProperty(value = "标记")
    private Integer tag;
}