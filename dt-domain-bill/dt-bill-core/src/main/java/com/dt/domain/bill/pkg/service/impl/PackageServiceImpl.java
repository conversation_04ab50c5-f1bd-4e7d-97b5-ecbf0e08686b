package com.dt.domain.bill.pkg.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.pkg.entity.Package;
import com.dt.domain.bill.pkg.mapper.PackageMapper;
import com.dt.domain.bill.pkg.param.PackAnalysisParam;
import com.dt.domain.bill.pkg.service.IPackageService;
import com.dt.domain.bill.shipment.entity.ShipmentOrder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
@Service
public class PackageServiceImpl extends ServiceImpl<PackageMapper, Package> implements IPackageService {

    @Override
    public List<Package> initCount() {
        return baseMapper.initCount();
    }

    @Override
    public IPage<Map<String, Object>> getPackAnalysisPage(Page<Map<String, Object>> page, PackAnalysisParam param) {
        return baseMapper.getPackAnalysisPage(page,param);
    }

}
