package com.dt.domain.bill.transfer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库存转移
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_transfer")
@ApiModel(value = "Transfer对象", description = "库存转移")
public class Transfer extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "转移单编码")
    private String code;

    @ApiModelProperty(value = "ERP系统对应单号")
    private String erpCode;

    // @ApiModelProperty(value = "仓库编码")
    // private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据状态 字典组:TRANSFER_STATUS")
    private String status;

    @ApiModelProperty(value = "转移原因 字典组:REASON_TYPE")
    private String reason;

    @ApiModelProperty(value = "转移描叙")
    private String note;

    @ApiModelProperty(value = "完成日期 (时间戳)")
    private Long completeDate;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "审核说明")
    private String remark;

    @ApiModelProperty(value = "业务场景")
    private String businessType;
    @ApiModelProperty(value = "拓传json")
    private String extraJson;

    @ApiModelProperty(value = "订单标记")
    private Integer orderTag; 
}