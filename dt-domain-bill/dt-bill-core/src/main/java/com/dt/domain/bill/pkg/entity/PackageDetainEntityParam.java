package com.dt.domain.bill.pkg.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class PackageDetainEntityParam implements java.io.Serializable  {
    @ApiModelProperty(value = "创建开始时间")
    private Long createdTimeStart;
    @ApiModelProperty(value = "创建结束时间")
    private Long createdTimeEnd;
    @ApiModelProperty(value = "货主编码")
    private List<String> cargoCodeList;
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    @ApiModelProperty(value = "销售平台")
    private String salePlatform;
    @ApiModelProperty(value = "快递公司")
    private String carrierCode;
    @ApiModelProperty(value = "预计出库时间开始时间")
    private Long expOutStockDateStart;
    @ApiModelProperty(value = "预计出库时间结束时间")
    private Long expOutStockDateEnd;
    @ApiModelProperty(value = "付款时间")
    private Long payDateStart;
    @ApiModelProperty(value = "付款时间")
    private Long payDateEnd;
}