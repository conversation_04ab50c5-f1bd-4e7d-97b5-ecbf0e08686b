package com.dt.domain.bill.pkg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.domain.bill.pkg.entity.Package;
import com.dt.domain.bill.pkg.param.PackAnalysisParam;
import org.apache.ibatis.annotations.Param;
import com.dt.domain.bill.pkg.entity.PackageDetainEntityParam;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
public interface PackageMapper extends BaseMapper<Package> {

    @Select("SELECT\n" +
            "\tdt_package.warehouse_code AS warehouseCode,\n" +
            "\tdt_package.cargo_code AS cargoCode,\n" +
            "\tdt_package.business_type AS businessType,\n" +
            "\tdt_package.carrier_code AS carrierCode,\n" +
            "\tdt_package.carrier_name AS carrierName,\n" +
            "\tdt_package.created_time AS createdTime,\n" +
            "\tFROM_UNIXTIME( dt_package.created_time / 1000, '%Y-%m-%d' ),\n" +
            "\tdt_package.`status` AS `status`,\n" +
            "\tcount( 0 ) AS version \n" +
            "FROM\n" +
            "\tdt_package \n" +
            "WHERE\n" +
            "\t`status` != 75 \n" +
            "GROUP BY\n" +
            "\tdt_package.warehouse_code,\n" +
            "\tdt_package.cargo_code,\n" +
            "\tdt_package.business_type,\n" +
            "\tdt_package.carrier_code,\n" +
            "\tFROM_UNIXTIME( dt_package.created_time / 1000, '%Y-%m-%d' ),\n" +
            "\tdt_package.`status` \n" +
            "ORDER BY\n" +
            "\tdt_package.created_time;")
    List<Package> initCount();

    /**
     * 包裹分析查询（分页版）
     * 支持按多个维度分组统计订单数量和商品数量
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 分页分析结果
     */
    IPage<Map<String, Object>> getPackAnalysisPage(IPage<Map<String, Object>> page, @Param("param") PackAnalysisParam param);

    List<Package> detain(PackageDetainEntityParam param);
}
