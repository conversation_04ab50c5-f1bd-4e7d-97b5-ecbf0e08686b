package com.dt.domain.bill.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 移位管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_move")
@ApiModel(value="Move对象", description="移位管理")
public class Move extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "移位单号")
    private String code;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据状态 字典：MOVE_STATUS")
    private String status;

    @ApiModelProperty(value = "商品质量等级")
    private String skuQuality;

    @ApiModelProperty(value = "计划品种数")
    private Integer expSkuType;

    @ApiModelProperty(value = "计划商品数")
    private BigDecimal expSkuQty;

    @ApiModelProperty(value = "实际品种数")
    private Integer actualSkuType;

    @ApiModelProperty(value = "数据商品数")
    private BigDecimal actualSkuQty;

    @ApiModelProperty(value = "操作方式 字典组：OP_TYPE")
    private String opType;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "操作日期")
    private Long completeDate;

}