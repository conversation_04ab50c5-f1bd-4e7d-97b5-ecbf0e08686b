package com.dt.domain.bill.pkg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dt.domain.bill.pkg.entity.Package;
import com.dt.domain.bill.pkg.entity.PackageDetain;
import com.dt.domain.bill.pkg.entity.PackageDetainEntityParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

@Mapper
public interface PackageDetainMapper extends BaseMapper<Package> {

    Cursor<PackageDetain> packageDetain(@Param("param") PackageDetainEntityParam param);

}
