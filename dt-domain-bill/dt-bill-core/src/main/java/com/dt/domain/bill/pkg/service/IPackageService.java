package com.dt.domain.bill.pkg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dt.domain.bill.pkg.entity.Package;
import com.dt.domain.bill.pkg.param.PackAnalysisParam;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
public interface IPackageService extends IService<Package> {

    /**
     * 功能描述:  初始化包裹日汇总
     * 创建时间:  2021/3/2 5:33 下午
     *
     * @return java.util.List<com.dt.domain.bill.pkg.entity.Package>
     * <AUTHOR>
     */
    List<Package> initCount();

    /**
     * 包裹分析查询（分页版）
     * 支持按多个维度分组统计订单数量和商品数量
     *
     * @param page 分页参数
     * @param param 查询参数
     * @return 分页分析结果
     */
    IPage<Map<String, Object>> getPackAnalysisPage(Page<Map<String, Object>> page, PackAnalysisParam param);
}
