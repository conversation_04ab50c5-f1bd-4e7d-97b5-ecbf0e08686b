package com.dt.domain.core.stock.dto;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 一级库存
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="StockLot对象", description="一级库存")
public class StockDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 用于判断是否是真实的商品 RealOrUnrealGoodsEnum
     */
    private String realGoods;
    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用=可用数")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "状态码 ")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "库存商品类型")
    private String stockSkuType;

    @ApiModelProperty(value = "库存最新变动时间")
    private Long latestUpdateTime;

}