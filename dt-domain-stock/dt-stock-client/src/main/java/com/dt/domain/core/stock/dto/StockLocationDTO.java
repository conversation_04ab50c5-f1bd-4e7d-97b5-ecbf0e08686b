package com.dt.domain.core.stock.dto;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 三级库存
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="StockLocation对象", description="三级库存")
public class StockLocationDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private String skuName;
    private String upcCode;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "库区类型")
    private String zoneType;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "库位类型")
    private String locationType;

    @ApiModelProperty(value = "库位用途")
    private String locationUseMode;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;
    private String skuQualityDesc;

    /**
     * 用于判断是否是真实的商品 RealOrUnrealGoodsEnum
     */
    private String realGoods;

    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    @ApiModelProperty(value = "待上架数量")
    private BigDecimal waitShelfQty;

    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用=可用数")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "状态码 ")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "数据条数")
    private long recordCount;

    @ApiModelProperty(value = "库存商品类型")
    private String stockSkuType;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "失效日期")
    private String expireDateDesc;
    private Long expireDate;
    @ApiModelProperty(value = "拓传json")
    private String extraJson;


    //业务字段 bom
    @ApiModelProperty(value = "完成bom商品数量 默认:0")
    private BigDecimal completeQty;
    @ApiModelProperty(value = "组套拆套需求数量")
    private BigDecimal needQty;

    @ApiModelProperty(value = "库存最新变动时间")
    private Long latestUpdateTime;
}