package com.dt.domain.core.stock.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.dto.StockStatisticDTO;
import com.dt.domain.core.stock.param.LocationStatisticsLotParam;
import com.dt.domain.core.stock.param.LocationStatisticsParam;
import com.dt.domain.core.stock.param.StockLocationBatchParam;
import com.dt.domain.core.stock.param.StockLocationParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 库位库存管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
public interface IStockLocationClient {

    Result<Boolean> save(StockLocationParam stockLocationParam);

    Result<Boolean> modify(StockLocationDTO stockLocationDTO);

    Result<Boolean> remove(StockLocationDTO stockLocationDTO);

    /**
     * 批量新增|更新库位库存信息
     * @param param
     * @return
     */
    Result<Boolean> saveOrUpdate(StockLocationBatchParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(StockLocationParam param);

    /**
     * 获取库位库存信息
     * @param param
     * @return
     */
    Result<StockLocationDTO> get(StockLocationParam param);

    /**
     * 获取库位库存列表
     * @param param
     * @return
     */
    Result<List<StockLocationDTO>> getList(StockLocationParam param);

    Result<Map> findLocationSumQty(String locationCode);
    Result<Page<StockLocationDTO>> getCustomsPageForPDA(StockLocationParam param);
    /**
     * 获取三级账库位库存列表
     * @param param
     * @return
     */
    Result<List<StockLocationDTO>> getListAccount(StockLocationParam param);

    Result<List<StockLocationDTO>> getChargingStaticGroupBy(StockLocationParam param);
    /**
     * 分页获取库位库存
     * @param param
     * @return
     */
    Result<Page<StockLocationDTO>> getPage(StockLocationParam param);

    @ApiOperation("库存汇总")
    Result<StockStatisticDTO> getStatistic(StockLocationParam param);

    /**
     * 库位统计
     * @param param
     * @return
     */
    Result<Page<StockLocationDTO>>  getStatisticsPage(LocationStatisticsParam param);

    /**
     * 货主货位商品导出统计
     * @param param
     * @return
     */
    List<StockLocationDTO>  getStatisticsList(LocationStatisticsParam param);


    /**
     * 批次库存统计
     * @param param
     * @return
     */
    Result<Page<StockLocationDTO>>  getStatisticsCargoLotPage(LocationStatisticsLotParam param);

    /**
     * 查询指定批次库存--可用数大于0
     * @param param
     * @return
     */
    Result<List<StockLocationDTO>> queryEffectiveStockBySkuLotNo(StockLocationParam param);

    /**
     * 查询不指定批次的商品库存--可用数大于0
     * @param param
     * @return
     */
    Result<List<StockLocationDTO>> queryEffectiveStockByNoSkuLotNo(StockLocationParam param);

    @ApiOperation("清理数据")
    void  cleanStockLocation(long updateEnd);
}
