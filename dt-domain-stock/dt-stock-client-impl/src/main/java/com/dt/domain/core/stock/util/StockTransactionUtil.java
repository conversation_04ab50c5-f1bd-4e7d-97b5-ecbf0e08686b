package com.dt.domain.core.stock.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.common.enums.SortEnum;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.core.stock.entity.StockTransaction;
import com.dt.domain.core.stock.param.StockTransactionParam;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * <p>
 * 库位库存交易流水
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Component
public class StockTransactionUtil {
    
    public LambdaQueryWrapper<StockTransaction> getQueryWrapper(StockTransactionParam param) {
        LambdaQueryWrapper<StockTransaction> lambdaQueryWrapper = getSortWrapper(param).lambda();
        lambdaQueryWrapper
                .eq(!StringUtils.isEmpty(param.getId()), StockTransaction::getId, param.getId())
                .gt(ObjectUtil.isNotEmpty(param.getStartId()), StockTransaction::getId, param.getStartId())
                .in(!CollectionUtils.isEmpty(param.getIdList()), StockTransaction::getId, param.getIdList())
                .gt(!StringUtils.isEmpty(param.getCreatedTimeStart()), StockTransaction::getCreatedTime, param.getCreatedTimeStart())
                .lt(!StringUtils.isEmpty(param.getCreatedTimeEnd()), StockTransaction::getCreatedTime, param.getCreatedTimeEnd())
                .gt(!StringUtils.isEmpty(param.getUpdatedTimeStart()), StockTransaction::getUpdatedTime, param.getUpdatedTimeStart())
                .lt(!StringUtils.isEmpty(param.getUpdatedTimeEnd()), StockTransaction::getUpdatedTime, param.getUpdatedTimeEnd())

                //仓库编码
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), StockTransaction::getWarehouseCode, param.getWarehouseCode())
                //货主编码
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), StockTransaction::getCargoCode, param.getCargoCode())
                .in(!CollectionUtil.isEmpty(param.getCargoCodeList()), StockTransaction::getCargoCode, param.getCargoCodeList())
                //batch serial no
                .eq(!ObjectUtils.isEmpty(param.getBatchSerialNo()), StockTransaction::getBatchSerialNo, param.getBatchSerialNo())
                .in(CollectionUtil.isNotEmpty(param.getBatchSerialNoList()), StockTransaction::getBatchSerialNo, param.getBatchSerialNoList())
                //transaction serial no
                .eq(!ObjectUtils.isEmpty(param.getTransactionSerialNo()), StockTransaction::getTransactionSerialNo, param.getTransactionSerialNo())
                .in(CollectionUtil.isNotEmpty(param.getTransactionSerialNoList()), StockTransaction::getTransactionSerialNo, param.getTransactionSerialNoList())
                //商品编码
                .eq(!ObjectUtils.isEmpty(param.getSkuCode()), StockTransaction::getSkuCode, param.getSkuCode())
                .in(!CollectionUtil.isEmpty(param.getSkuCodeList()), StockTransaction::getSkuCode, param.getSkuCodeList())
                //商品属性
                .eq(!ObjectUtils.isEmpty(param.getSkuQuality()), StockTransaction::getSkuQuality, param.getSkuQuality())
                //批次ID
                .eq(!ObjectUtils.isEmpty(param.getSkuLotNo()), StockTransaction::getSkuLotNo, param.getSkuLotNo())
                //商品条形码
                .eq(!ObjectUtils.isEmpty(param.getUpcCode()), StockTransaction::getUpcCode, param.getUpcCode())
                //单据类型 BillTypeEnum
                .eq(!ObjectUtils.isEmpty(param.getBillType()), StockTransaction::getBillType, param.getBillType())
                .eq(StrUtil.isNotBlank(param.getGlobalNo()), StockTransaction::getGlobalNo, param.getGlobalNo())
                .in(CollectionUtil.isNotEmpty(param.getGlobalNoList()),StockTransaction::getGlobalNo,param.getGlobalNoList())
                //单据编码
                .eq(!ObjectUtils.isEmpty(param.getBillNo()), StockTransaction::getBillNo, param.getBillNo())
                .in(!CollectionUtil.isEmpty(param.getBillNoList()), StockTransaction::getBillNo, param.getBillNoList())
                //操作类型 与业务类型对应
                .eq(!ObjectUtils.isEmpty(param.getOperationType()), StockTransaction::getOperationType, param.getOperationType())
                .in(!CollectionUtil.isEmpty(param.getOperationTypeList()), StockTransaction::getOperationType, param.getOperationTypeList())
                //业务类型 TradeTypeEnum
                .eq(!ObjectUtils.isEmpty(param.getTradeType()), StockTransaction::getTradeType, param.getTradeType())
                //库存层级
                .eq(!ObjectUtils.isEmpty(param.getStockLevel()), StockTransaction::getStockLevel, param.getStockLevel())
                //库区类型 ZoneTypeEnum
                .eq(!ObjectUtils.isEmpty(param.getZoneType()), StockTransaction::getZoneType, param.getZoneType())
                //库区编码
                .eq(!ObjectUtils.isEmpty(param.getZoneCode()), StockTransaction::getZoneCode, param.getZoneCode())
                //库位类型 LocationTypeEnum
                .eq(!ObjectUtils.isEmpty(param.getLocationType()), StockTransaction::getLocationType, param.getLocationType())
                //库位编码
                .eq(!ObjectUtils.isEmpty(param.getLocationCode()), StockTransaction::getLocationCode, param.getLocationCode())
                //操作时间
                .eq(!ObjectUtils.isEmpty(param.getTradeTime()), StockTransaction::getTradeTime, param.getTradeTime())
                //父单据编码
                .eq(!ObjectUtils.isEmpty(param.getParentBillNo()), StockTransaction::getParentBillNo, param.getParentBillNo())
                .in(!CollectionUtil.isEmpty(param.getParentBillNoList()), StockTransaction::getParentBillNo, param.getParentBillNoList())
                //状态
                .eq(!ObjectUtils.isEmpty(param.getStatus()), StockTransaction::getStatus, param.getStatus())
                //状态
                .ne(!ObjectUtils.isEmpty(param.getNoStatus()), StockTransaction::getStatus, param.getNoStatus())
                //状态
                .notIn(!ObjectUtils.isEmpty(param.getNoStatusList()), StockTransaction::getStatus, param.getNoStatusList())
        ;
        
        // logic delete 
        lambdaQueryWrapper.eq(param.getNoDeletedData(),StockTransaction::getDeleted, 1);
        lambdaQueryWrapper.last(ObjectUtil.isNotEmpty(param.getForUpdate()) && param.getForUpdate(), " for update");
        return lambdaQueryWrapper;
    }


    protected com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<StockTransaction> getSortWrapper(StockTransactionParam param) {
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<StockTransaction> queryWrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        Map<String, String> sortMap = param.getSortParamMap();
        if (!CollectionUtils.isEmpty(sortMap)) {
            for (Map.Entry<String, String> entry : sortMap.entrySet()) {
                SortEnum sortEnum = SortEnum.getEnum(entry.getValue());
                switch (sortEnum) {
                    case DESC:
                        queryWrapper.orderByDesc(entry.getKey());
                        break;
                    case ASC:
                    default:
                        queryWrapper.orderByAsc(entry.getKey());
                }
            }
        }else {
            queryWrapper.orderByDesc("created_time");
        }
        return queryWrapper;
    }

}
