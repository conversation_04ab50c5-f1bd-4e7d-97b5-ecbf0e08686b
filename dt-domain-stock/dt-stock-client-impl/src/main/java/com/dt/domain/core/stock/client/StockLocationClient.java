package com.dt.domain.core.stock.client;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.dto.StockStatisticDTO;
import com.dt.domain.core.stock.entity.StockLocation;
import com.dt.domain.core.stock.mapper.StockLocationMapper;
import com.dt.domain.core.stock.param.*;
import com.dt.domain.core.stock.service.IStockLocationService;
import com.dt.domain.core.stock.util.StockLocationUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.DefaultSortUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class StockLocationClient implements IStockLocationClient {


    @Resource
    private StockLocationMapper stockLocationMapper;

    @Resource
    private IStockLocationService stockLocationService;

    @Resource
    private StockLocationUtil stockLocationUtil;

    @Override
    public Result<Boolean> save(StockLocationParam stockLocationParam) {
        StockLocation stockLocation = BeanUtil.toBean(stockLocationParam, StockLocation.class);
        if (!stockLocationService.save(stockLocation)) throw new BaseException(BaseBizEnum.DATA_ERROR);
        return Result.success(true);
    }
    
    @Override
    public Result<Boolean> modify(StockLocationDTO stockLocationDTO) {
        StockLocation stockLocation = BeanUtil.toBean(stockLocationDTO, StockLocation.class);
        if (!stockLocationService.updateById(stockLocation)) throw new BaseException(BaseBizEnum.DATA_ERROR);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> remove(StockLocationDTO stockLocationDTO) {
        StockLocation stockLocation = BeanUtil.toBean(stockLocationDTO, StockLocation.class);
        if (!stockLocationService.removeById(stockLocation)) throw new BaseException(BaseBizEnum.DATA_ERROR);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> saveOrUpdate(StockLocationBatchParam param) {
        if (ObjectUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getStockLocationList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<StockLocation> stockList = ConverterUtil.convertList(param.getStockLocationList(), StockLocation.class);
        if (CollectionUtils.isEmpty(stockList)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        List<StockLocation> createStockList = stockList.stream()
                .filter(a -> StringUtils.isEmpty(a.getId()))
                .collect(Collectors.toList());
        List<StockLocation> updateStockList = stockList.stream()
                .filter(a -> !ObjectUtils.isEmpty(a.getId()))
                .filter(a -> !ObjectUtils.isEmpty(a.getVersion()))
                .collect(Collectors.toList());
        if (stockList.size() != createStockList.size() + updateStockList.size()) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        for (StockLocation stockLocation : updateStockList) {
            Boolean result = stockLocationService.updateById(stockLocation);
            if (!result) {
                throw new BaseException(BaseBizEnum.TIP,"修改三级库存失败");
            }
        }
        Boolean result = stockLocationService.saveBatch(createStockList);
        if (!result) {
            throw new BaseException(BaseBizEnum.TIP,"保存三级库存失败");
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(StockLocationParam param) {
        LambdaQueryWrapper<StockLocation> wrapper = stockLocationUtil.getQueryWrapper(param);
        Integer count = stockLocationService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<StockLocationDTO> get(StockLocationParam param) {
        LambdaQueryWrapper<StockLocation> queryWrapper = stockLocationUtil.getQueryWrapper(param);
        StockLocation stockLocation = stockLocationService.getOne(queryWrapper);
        StockLocationDTO result = ConverterUtil.convert(stockLocation, StockLocationDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<StockLocationDTO>> getList(StockLocationParam param) {
        LambdaQueryWrapper<StockLocation> queryWrapper = stockLocationUtil.getQueryWrapper(param);
        List<StockLocation> stockLocationList = stockLocationService.list(queryWrapper);
        List<StockLocationDTO> result = ConverterUtil.convertList(stockLocationList, StockLocationDTO.class);
        return Result.success(result);
    }


//    @Override
//    public Result<Page<StockLocationDTO>> getPageForPDA(StockLocationParam param) {
//        DefaultSortUtil.formatSortSort(param);
//        Page<StockLocation> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
//        LambdaQueryWrapper<StockLocation> queryWrapper = stockLocationUtil.getQueryWrapper(param);
//        if(param == null || (StringUtils.isEmpty(param.getHasAvailableQty()) && StringUtils.isEmpty(param.getHasPhysicalQty()))) {
//            queryWrapper.gt(StockLocation::getPhysicalQty, new BigDecimal("0.000"));
//        }
//        IPage<StockLocation> stockLocationPage = stockLocationService.page(page, queryWrapper);
//        Page<StockLocationDTO> result = ConverterUtil.convertPage(stockLocationPage, StockLocationDTO.class);
//        return Result.success(result);
//    }


    @Override
    public Result<Page<StockLocationDTO>> getCustomsPageForPDA(StockLocationParam param) {
        Page<StockLocation> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        IPage<StockLocation> stockLocationPage = stockLocationMapper.getCustomsPageForPDA(page,
                param.getCargoCodeList(),
                param.getSkuCodeList(),
                param.getLocationCodeList(),
                param.getSkuQuality()
        );
        Page<StockLocationDTO> result = ConverterUtil.convertPage(stockLocationPage, StockLocationDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Map> findLocationSumQty(String locationCode) {
        return Result.success(stockLocationMapper.findLocationSumQty(locationCode));
    }


    @Override
    public Result<List<StockLocationDTO>> getListAccount(StockLocationParam param) {
        LambdaQueryWrapper<StockLocation> queryWrapper = stockLocationUtil.getQueryWrapper(param);
        if (param == null || (StringUtils.isEmpty(param.getHasAvailableQty()) && StringUtils.isEmpty(param.getHasPhysicalQty()))) {
            queryWrapper.gt(StockLocation::getPhysicalQty, new BigDecimal("0.000"));
        }
        if (!CollectionUtils.isEmpty(param.getSkuCodePairQueryList())) {
            List<Map<String, String>> pair = param.getSkuCodePairQueryList();
            queryWrapper.and(s -> {
                for (Map<String, String> map : pair) {
                    String skuCode = map.keySet().stream().findFirst().get();
                    String cargoCode = map.values().stream().findFirst().get();
                    s.or(ss -> {
                        ss.eq(StockLocation::getSkuCode, skuCode).eq(StockLocation::getCargoCode, cargoCode);
                    });
                }
            });
        }
        List<StockLocation> stockLocationList = stockLocationService.list(queryWrapper);
        List<StockLocationDTO> result = ConverterUtil.convertList(stockLocationList, StockLocationDTO.class);
        return Result.success(result);
    }

    /**
     * @param param
     * @return
     */
    @Override
    public Result<List<StockLocationDTO>> getChargingStaticGroupBy(StockLocationParam param) {
        QueryWrapper<StockLocation> queryWrapper = new QueryWrapper();
        queryWrapper.select("cargo_code,warehouse_code,location_code,count(*) as occupy_qty");
        LambdaQueryWrapper LambdaQueryWrapper = queryWrapper.lambda().gt(StockLocation::getPhysicalQty, new BigDecimal("0.000"))
                .le(StockLocation::getUpdatedTime, param.getUpdatedTimeEnd()).groupBy(StockLocation::getWarehouseCode,
                        StockLocation::getCargoCode, StockLocation::getLocationCode);
        List<StockLocation> stockLocationList = stockLocationService.list(LambdaQueryWrapper);
        List<StockLocationDTO> result = new ArrayList<>();//= ConverterUtil.convertList(stockLocationList, StockLocationDTO.class);
        if (!CollectionUtil.isEmpty(stockLocationList)) {
            stockLocationList.forEach(s -> {
                StockLocationDTO stockLocationDTO = ConverterUtil.convert(s, StockLocationDTO.class);
                BigDecimal recordCount = s.getOccupyQty();
                if (recordCount == null) {
                    recordCount = BigDecimal.ZERO;
                }
                ;
                stockLocationDTO.setRecordCount(recordCount.longValue());
                result.add(stockLocationDTO);
            });
        }
        return Result.success(result);
    }

    @Override
    public Result<Page<StockLocationDTO>> getPage(StockLocationParam param) {
        DefaultSortUtil.formatSortSort(param);
        Page<StockLocation> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<StockLocation> queryWrapper = stockLocationUtil.getQueryWrapper(param);
        if (!CollectionUtils.isEmpty(param.getSkuCodePairQueryList())) {
            List<Map<String, String>> pair = param.getSkuCodePairQueryList();
            queryWrapper.and(s -> {
                for (Map<String, String> map : pair) {
                    String skuCode = map.keySet().stream().findFirst().get();
                    String cargoCode = map.values().stream().findFirst().get();
                    s.or(ss -> {
                        ss.eq(StockLocation::getSkuCode, skuCode).eq(StockLocation::getCargoCode, cargoCode);
                    });
                }
            });
        }
        IPage<StockLocation> stockLocationPage = stockLocationService.page(page, queryWrapper);
        Page<StockLocationDTO> result = ConverterUtil.convertPage(stockLocationPage, StockLocationDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<StockStatisticDTO> getStatistic(StockLocationParam param) {
        param.setHasPhysicalQty(true);
        LambdaQueryWrapper<StockLocation> queryWrapper = stockLocationUtil.getQueryWrapper(param);
        queryWrapper.select(StockLocation::getSkuQuality, StockLocation::getPhySum, StockLocation::getAvlSum, StockLocation::getOccupySum, StockLocation::getFrozenSum,StockLocation::getWaitSum);
        queryWrapper.groupBy(StockLocation::getSkuQuality);
        List<StockLocation> stockList = stockLocationService.list(queryWrapper);
        StockStatisticDTO stockStatisticDTO = new StockStatisticDTO();
        stockStatisticDTO.setTotalPhysicalQty(stockList.stream().map(StockLocation::getPhySum).reduce(BigDecimal.ZERO, BigDecimal::add));
        stockStatisticDTO.setTotalAvlAvailableQty(stockList.stream()
                .filter(it ->it.getSkuQuality().equalsIgnoreCase(SkuQualityEnum.SKU_QUALITY_AVL.getLevel()))
                .map(StockLocation::getAvlSum).reduce(BigDecimal.ZERO, BigDecimal::add));
        stockStatisticDTO.setTotalDamageAvailableQty(stockList.stream()
                .filter(it ->it.getSkuQuality().equalsIgnoreCase(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel()))
                .map(StockLocation::getAvlSum).reduce(BigDecimal.ZERO, BigDecimal::add));
        stockStatisticDTO.setTotalOccupyQty(stockList.stream().map(StockLocation::getOccupySum).reduce(BigDecimal.ZERO, BigDecimal::add));
        stockStatisticDTO.setTotalFrozenQty(stockList.stream()
                .map(it -> it.getFrozenSum().add(it.getWaitSum()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        // 商品种类数
        queryWrapper = stockLocationUtil.getQueryWrapper(param);
        queryWrapper.select(StockLocation::getSkuCode);
        queryWrapper.groupBy(StockLocation::getCargoCode,StockLocation::getSkuCode);
        List<StockLocation> count = stockLocationService.list(queryWrapper);
        stockStatisticDTO.setTotalSku(count.size());
        return Result.success(stockStatisticDTO);
    }

    @Override
    public Result<Page<StockLocationDTO>> getStatisticsPage(LocationStatisticsParam param) {
        Page<StockLocation> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
//
//        DefaultSortUtil.formatSortSort(param);
//
//        QueryWrapper<StockLocation> queryWrapper = stockLocationUtil.getSortWrapper(param);
//        queryWrapper
//                .select("id","warehouse_code","cargo_code","sku_code","zone_code","location_code","sku_lot_no","sku_quality")
//                .select("SUM(physical_qty) AS physical_qty","SUM(frozen_qty) AS frozen_qty","SUM(occupy_qty) AS occupy_qty")
//                .select("SUM(available_qty) AS available_qty","SUM(wait_shelf_qty) AS wait_shelf_qty")
//                .select("version", "deleted", "status", "created_by", "created_time", "updated_by", "updated_time");
//        LambdaQueryWrapper<StockLocation> lambdaQueryWrapper = queryWrapper.lambda();
//
//        lambdaQueryWrapper
//                .in(!CollectionUtils.isEmpty(param.getCargoCodeList()),StockLocation::getCargoCode,param.getCargoCodeList())
//                .in(!CollectionUtils.isEmpty(param.getSkuCodeList()),StockLocation::getSkuCode,param.getSkuCodeList())
//                .in(!CollectionUtils.isEmpty(param.getZoneCodeList()),StockLocation::getZoneCode,param.getZoneCodeList())
//                .in(!ObjectUtils.isEmpty(param.getLocationCode()),StockLocation::getLocationCode,param.getLocationCode())
//                .in(!ObjectUtils.isEmpty(param.getSkuQuality()),StockLocation::getSkuQuality,param.getSkuQuality())
//                .gt(StockLocation::getPhysicalQty,new BigDecimal("0.000"))
//        ;
        if (!CollectionUtils.isEmpty(param.getSkuCodePairQueryList())) {
            param.setSkuCodePairQueryList(param.getSkuCodePairQueryList().stream().map(s -> {
                Map<String, String> map = new HashMap();
                map.put("key", s.keySet().stream().findFirst().get());
                map.put("val", s.values().stream().findFirst().get());
                return map;
            }).collect(Collectors.toList()));
        }
        IPage<StockLocation> stockLocationPage = stockLocationMapper.getStatisticsPage(page,
                param.getCargoCodeList(),
                param.getSkuCodeList(),
                param.getZoneCodeList(),
                param.getLocationCode(),
                param.getLocationCodeList(),
                param.getSkuQuality(),
                param.getSkuCodePairQueryList()
        );
        Page<StockLocationDTO> result = ConverterUtil.convertPage(stockLocationPage, StockLocationDTO.class);
        return Result.success(result);
    }

    @Override
    public List<StockLocationDTO> getStatisticsList(LocationStatisticsParam param) {
        if (!CollectionUtils.isEmpty(param.getSkuCodePairQueryList())) {
            param.setSkuCodePairQueryList(param.getSkuCodePairQueryList().stream().map(s -> {
                Map<String, String> map = new HashMap();
                map.put("key", s.keySet().stream().findFirst().get());
                map.put("val", s.values().stream().findFirst().get());
                return map;
            }).collect(Collectors.toList()));
        }
        List<StockLocation> statisticsList = stockLocationMapper.getStatisticsPage(
                param.getCargoCodeList(),
                param.getSkuCodeList(),
                param.getZoneCodeList(),
                param.getLocationCode(),
                param.getLocationCodeList(),
                param.getSkuQuality(),
                param.getSkuCodePairQueryList()
        );
        List<StockLocationDTO> stockLocationDTOList = ConverterUtil.convertList(statisticsList, StockLocationDTO.class);
        return stockLocationDTOList;
    }

    @Override
    public Result<Page<StockLocationDTO>> getStatisticsCargoLotPage(LocationStatisticsLotParam param) {
        Page<StockLocation> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());


//        id, warehouse_code, cargo_code, sku_code, sku_lot_no, sku_quality,
//        SUM(physical_qty) AS physical_qty, SUM(frozen_qty) AS frozen_qty, SUM(occupy_qty) AS occupy_qty,
//        SUM(available_qty) AS available_qty,SUM(wait_shelf_qty) AS wait_shelf_qty,
//                version, deleted, status, created_by, created_time, updated_by, updated_time
//        FROM dt_stock_location

        IPage<StockLocation> stockLocationPage = stockLocationMapper.getStatisticsLotPage(page,
                param.getCargoCodeList(),
                param.getSkuCodeList(),
                param.getSkuLotNoList(),
                param.getSkuQuality()
        );
        LambdaQueryWrapper<StockLocation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .in(!CollectionUtils.isEmpty(param.getCargoCodeList()), StockLocation::getCargoCode, param.getCargoCodeList())
                .in(!CollectionUtils.isEmpty(param.getSkuCodeList()), StockLocation::getSkuCode, param.getSkuCodeList())
                .in(!CollectionUtils.isEmpty(param.getSkuLotNoList()), StockLocation::getSkuLotNo, param.getSkuLotNoList())
                .eq(!StringUtils.isEmpty(param.getSkuQuality()), StockLocation::getSkuQuality, param.getSkuQuality());

        Page<StockLocationDTO> result = ConverterUtil.convertPage(stockLocationPage, StockLocationDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<StockLocationDTO>> queryEffectiveStockBySkuLotNo(StockLocationParam param) {
        LambdaQueryWrapper<StockLocation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(!StringUtils.isEmpty(param.getWarehouseCode()), StockLocation::getWarehouseCode, param.getWarehouseCode());
        queryWrapper.eq(!StringUtils.isEmpty(param.getCargoCode()), StockLocation::getCargoCode, param.getCargoCode());
        queryWrapper.eq(!StringUtils.isEmpty(param.getSkuLotNo()), StockLocation::getSkuLotNo, param.getSkuLotNo());
        queryWrapper.gt(StockLocation::getAvailableQty, BigDecimal.ZERO);
        if (!CollectionUtils.isEmpty(param.getSortParamMap()) && Objects.equals(param.getSortParamMap().getOrDefault("orderBy", ""), "DESC")) {
            queryWrapper.orderByDesc(StockLocation::getOccupyQty);
        }
        if (!CollectionUtils.isEmpty(param.getSortParamMap()) && Objects.equals(param.getSortParamMap().getOrDefault("orderBy", ""), "ASC")) {
            queryWrapper.orderByDesc(StockLocation::getOccupyQty);
        }
        List<StockLocation> stocks = stockLocationService.list(queryWrapper);
        return Result.success(ConverterUtil.convertList(stocks, StockLocationDTO.class));
    }

    @Override
    public Result<List<StockLocationDTO>> queryEffectiveStockByNoSkuLotNo(StockLocationParam param) {
        LambdaQueryWrapper<StockLocation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(!StringUtils.isEmpty(param.getWarehouseCode()), StockLocation::getWarehouseCode, param.getWarehouseCode());
        queryWrapper.eq(!StringUtils.isEmpty(param.getCargoCode()), StockLocation::getCargoCode, param.getCargoCode());
        queryWrapper.eq(!StringUtils.isEmpty(param.getSkuCode()), StockLocation::getSkuCode, param.getSkuCode());
        queryWrapper.in(!CollectionUtils.isEmpty(param.getSkuCodeList()), StockLocation::getSkuCode, param.getSkuCodeList());
        queryWrapper.eq(!StringUtils.isEmpty(param.getSkuQuality()), StockLocation::getSkuQuality, param.getSkuQuality());
        queryWrapper.eq(!StringUtils.isEmpty(param.getLocationType()), StockLocation::getLocationType, param.getLocationType());
        queryWrapper.in(!StringUtils.isEmpty(param.getZoneCodeList()), StockLocation::getZoneCode, param.getZoneCodeList());
        //TODO 以后可以优化 三级库存加上库区类型
        queryWrapper.in(!StringUtils.isEmpty(param.getLocationCodeList()), StockLocation::getLocationCode, param.getLocationCodeList());
        queryWrapper.gt(StockLocation::getAvailableQty, 0);
//        if(!CollectionUtils.isEmpty(param.getSortParamMap()) && Objects.equals(param.getSortParamMap().getOrDefault("orderBy",""),"DESC")){
//            queryWrapper.orderByDesc(StockLocation::getAvailableQty);
//        }
//        if(!CollectionUtils.isEmpty(param.getSortParamMap()) && Objects.equals(param.getSortParamMap().getOrDefault("orderBy",""),"ASC")){
//            queryWrapper.orderByDesc(StockLocation::getAvailableQty);
//        }
        List<StockLocation> stocks = stockLocationService.list(queryWrapper);
        return Result.success(ConverterUtil.convertList(stocks, StockLocationDTO.class));
    }

    @Override
    public void  cleanStockLocation(long updateEnd) {
        stockLocationMapper.cleanStockLocation(updateEnd);
    }
}
