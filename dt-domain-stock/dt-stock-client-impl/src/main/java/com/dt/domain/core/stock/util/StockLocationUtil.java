package com.dt.domain.core.stock.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.core.stock.entity.Stock;
import com.dt.domain.core.stock.entity.StockLocation;
import com.dt.domain.core.stock.param.StockLocationParam;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;

@Component
public class StockLocationUtil extends QueryWrapper<StockLocation, StockLocationParam> {

    @Override
    public LambdaQueryWrapper<StockLocation> getQueryWrapper(StockLocationParam param) {
        LambdaQueryWrapper<StockLocation> lambdaQueryWrapper = super.getQueryWrapper(param);
        lambdaQueryWrapper
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), StockLocation::getWarehouseCode, param.getWarehouseCode())
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), StockLocation::getCargoCode, param.getCargoCode())
                .eq(!ObjectUtils.isEmpty(param.getSkuCode()), StockLocation::getSkuCode, param.getSkuCode())
                .eq(!ObjectUtils.isEmpty(param.getZoneCode()), StockLocation::getZoneCode, param.getZoneCode())
                .eq(!ObjectUtils.isEmpty(param.getZoneType()), StockLocation::getZoneType, param.getZoneType())
                .in(!ObjectUtils.isEmpty(param.getZoneTypeList()), StockLocation::getZoneType, param.getZoneTypeList())
                .eq(!ObjectUtils.isEmpty(param.getLocationCode()), StockLocation::getLocationCode, param.getLocationCode())
                .eq(!ObjectUtils.isEmpty(param.getLocationType()), StockLocation::getLocationType, param.getLocationType())
                .in(!ObjectUtils.isEmpty(param.getLocationTypeList()), StockLocation::getLocationType, param.getLocationTypeList())
                .eq(!ObjectUtils.isEmpty(param.getLocationUseMode()), StockLocation::getLocationUseMode, param.getLocationUseMode())
                .eq(!ObjectUtils.isEmpty(param.getSkuLotNo()), StockLocation::getSkuLotNo, param.getSkuLotNo())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), StockLocation::getStatus, param.getStatus())
                .eq(!ObjectUtils.isEmpty(param.getCreatedBy()), StockLocation::getCreatedBy, param.getCreatedBy())
                .eq(!ObjectUtils.isEmpty(param.getUpdatedBy()), StockLocation::getUpdatedBy, param.getUpdatedBy())
                .eq(!ObjectUtils.isEmpty(param.getSkuQuality()), StockLocation::getSkuQuality, param.getSkuQuality())
                .eq(!ObjectUtils.isEmpty(param.getRealGoods()), StockLocation::getRealGoods, param.getRealGoods())
                .in(!CollectionUtils.isEmpty(param.getCargoCodeList()), StockLocation::getCargoCode, param.getCargoCodeList())
                .in(!CollectionUtils.isEmpty(param.getZoneCodeList()), StockLocation::getZoneCode, param.getZoneCodeList())
                .in(!CollectionUtils.isEmpty(param.getLocationCodeList()), StockLocation::getLocationCode, param.getLocationCodeList())
                .in(!CollectionUtils.isEmpty(param.getSkuLotNoList()), StockLocation::getSkuLotNo, param.getSkuLotNoList())
                .in(!CollectionUtils.isEmpty(param.getSkuCodeList()), StockLocation::getSkuCode, param.getSkuCodeList())
                .in(!CollectionUtils.isEmpty(param.getSkuQualityList()), StockLocation::getSkuQuality, param.getSkuQualityList())
                .gt(!ObjectUtils.isEmpty(param.getUpdatedTimeStart()), StockLocation::getUpdatedTime, param.getUpdatedTimeStart())
                .le(!ObjectUtils.isEmpty(param.getUpdatedTimeEnd()), StockLocation::getUpdatedTime, param.getUpdatedTimeEnd())
                .gt(!ObjectUtils.isEmpty(param.getHasPhysicalQty()) && param.getHasPhysicalQty(), StockLocation::getPhysicalQty, new BigDecimal("0.000"))
                .gt(!ObjectUtils.isEmpty(param.getHasAvailableQty()) && param.getHasAvailableQty(), StockLocation::getAvailableQty, new BigDecimal("0.000"))
                .gt(!ObjectUtils.isEmpty(param.getHasFrozenQty()) && param.getHasFrozenQty(), StockLocation::getFrozenQty, new BigDecimal("0.000"))
                .gt(!ObjectUtils.isEmpty(param.getHasWaitShelfQty()) && param.getHasWaitShelfQty(), StockLocation::getWaitShelfQty, new BigDecimal("0.000"))
                .ge(ObjectUtil.isNotEmpty(param.getLatestUpdateTimeStart()), StockLocation::getLatestUpdateTime,param.getLatestUpdateTimeStart())
                .gt(ObjectUtil.isNotEmpty(param.getLatestUpdateTimeEnd()),StockLocation::getLatestUpdateTime,0L)
                .le(ObjectUtil.isNotEmpty(param.getLatestUpdateTimeEnd()),StockLocation::getLatestUpdateTime,param.getLatestUpdateTimeEnd())
        ;
        lambdaQueryWrapper.gt(ObjectUtil.isNotEmpty(param.getStartId()), StockLocation::getId, param.getStartId());
        lambdaQueryWrapper.lt(ObjectUtil.isNotEmpty(param.getEndId()), StockLocation::getId, param.getEndId());

        if (CollectionUtil.isNotEmpty(param.getSelectColumnList())) {
            lambdaQueryWrapper.select(tableFieldInfo -> param.getSelectColumnList().contains(tableFieldInfo.getColumn().toUpperCase()));
        }
        return lambdaQueryWrapper;

    }


}
