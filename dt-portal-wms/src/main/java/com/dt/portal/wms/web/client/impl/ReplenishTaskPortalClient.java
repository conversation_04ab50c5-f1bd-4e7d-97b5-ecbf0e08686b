package com.dt.portal.wms.web.client.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.enums.replenish.ReplenishTaskTypeEnum;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.IReplenishTaskClient;
import com.dt.platform.wms.dto.replenish.ReplenishRecommendDTO;
import com.dt.platform.wms.dto.replenish.ReplenishTaskNewDTO;
import com.dt.platform.wms.param.receipt.ReplenishTaskBizParam;
import com.dt.platform.wms.param.replenish.ReplenishRecommendParam;
import com.dt.portal.wms.web.client.IReplenishTaskPortalClient;
import com.dt.portal.wms.web.vo.replenish.ReplenishRecommendNewVO;
import com.dt.portal.wms.web.vo.replenish.ReplenishTaskNewVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class ReplenishTaskPortalClient implements IReplenishTaskPortalClient {

    @DubboReference
    private IReplenishTaskClient replenishTaskClient;


    @Override
    @SoulClient(path = "/replenish-task/type", desc = "补货指引")
    public Result<List<IdNameVO>> queryReplenishType() {
        List<IdNameVO> result = Arrays.stream(ReplenishTaskTypeEnum.values()).map((ReplenishTaskTypeEnum s) -> {
            IdNameVO vo = new IdNameVO();
            vo.setName(s.getName());
            vo.setId(s.getType());
            return vo;
        }).collect(Collectors.toList());
        return Result.success(result);
    }

    @Override
    @SoulClient(path = "/replenish-task/getPageNew", desc = "补货指引新")
    public Result<PageVO<ReplenishTaskNewVO>> getPageNew(ReplenishTaskBizParam param) {
        Result<Page<ReplenishTaskNewDTO>> taskClientPage = replenishTaskClient.getPageNew(param);
        return Result.success(ConverterUtil.convertPageVO(taskClientPage.getData(), ReplenishTaskNewVO.class));
    }

    @Override
    @SoulClient(path = "/replenish/recommendNew", desc = "补货指引库位推荐新")
    public Result<List<ReplenishRecommendNewVO>> recommendStockLocationNew(ReplenishRecommendParam replenishRecommendParam) {
        Result<List<ReplenishRecommendDTO>> listResult = replenishTaskClient.recommendStockLocationNew(replenishRecommendParam);
        List<ReplenishRecommendNewVO> replenishRecommendNewVOS = ConverterUtil.convertList(listResult.getData(), ReplenishRecommendNewVO.class);
        if (!CollectionUtils.isEmpty(replenishRecommendNewVOS)) {
            replenishRecommendNewVOS.forEach(a -> {
                if (a.getIsAppoint() != null && a.getIsAppoint()) {
                    a.setIsAppointDesc("是");
                } else {
                    a.setIsAppointDesc("否");
                }
            });
        }
        return Result.success(replenishRecommendNewVOS);
    }
}
