package com.dt.portal.wms.web.client.esShipment.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.dto.pkg.PackAnalysisBillDTO;
import com.dt.domain.bill.param.pkg.PackAnalysisBillParam;
import com.dt.elasticsearch.wms.client.IPackageEsClient;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.*;
import com.dt.platform.wms.dto.base.CarrierBizDTO;
import com.dt.platform.wms.dto.base.WarehouseBizDTO;
import com.dt.platform.wms.dto.cargo.CargoOwnerBizDTO;
import com.dt.platform.wms.dto.pkg.PackAnalysisBizDTO;
import com.dt.platform.wms.dto.sale.SalePlatformBizDTO;
import com.dt.platform.wms.param.cargo.CargoOwnerBizParam;
import com.dt.platform.wms.param.carrier.CarrierBizParam;
import com.dt.platform.wms.param.pkg.PackageBizParam;
import com.dt.platform.wms.param.sale.SalePlatformQueryBizParam;
import com.dt.portal.wms.web.client.IPackagePortClient;
import com.dt.portal.wms.web.client.esShipment.IPackAnalysisPortalClient;
import com.dt.portal.wms.web.vo.pkg.PackageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
@RefreshScope
public class PackAnalysisPortalClientImpl implements IPackAnalysisPortalClient {

    @DubboReference
    private IPackageEsClient packageEsClient;

    @DubboReference
    private IPackageBizClient packageBizClient;

    @DubboReference
    private IWarehouseBizClient iWarehouseBizClient;

    @DubboReference
    private ICargoOwnerBizClient cargoOwnerBizClient;

    @DubboReference
    private ICarrierBizClient iCarrierBizClient;

    @DubboReference
    private ISalePlatformBizClient salePlatformBizClient;

    @DubboReference
    private IAreaBizClient areaBizClient;


    @Resource
    private IPackagePortClient packagePortClient;


    private static final List<String> sortList = Arrays.asList(
            "cargoCode", "businessType", "salePlatform", "packageStruct", "isPre",
            "expOutStockDate_day", "expOutStockDate_hour", "createTime_day",
            "createTime_hour", "payDate_day", "payDate_hour", "outStockDate_day",

            "pickCompleteSkuDate_day", "pickCompleteSkuDate_hour", "checkCompleteDate_day", "checkCompleteDate_hour",

            "outStockDate_hour", "carrierCode",
            "createdOrderCount", "orderCount", "pretreatmentFailOrderCount",
            "pretreatmentCompleteOrderCount", "collectedOrderCount", "checkStartOrderCount", "collectedFailOrderCount",
            "checkCompleteOrderCount", "pickStartOrderCount", "pickCompleteOrderCount", "outOrderCount", "skuQtySum",
            "interceptOrderCount", "cancelOrderCount", "shortageOutOrderCount", "interceptCancelOrderCount"
    );

    private static final List<String> packPageList = Arrays.asList(
            "createdOrderCount", "orderCount", "pretreatmentFailOrderCount",
            "pretreatmentCompleteOrderCount", "collectedOrderCount", "checkStartOrderCount", "collectedFailOrderCount",
            "checkCompleteOrderCount", "pickStartOrderCount", "pickCompleteOrderCount", "outOrderCount", "skuQtySum",
            "interceptOrderCount", "cancelOrderCount", "shortageOutOrderCount", "interceptCancelOrderCount"
    );

    /**
     * 校验创建时间参数
     * 创建时间必填，且跨度不能超过一个自然月
     *
     * @param param 查询参数
     * @return 校验结果，如果有错误返回错误结果，否则返回 null
     */
    private void validateCreateTimeParam(PackAnalysisBillParam param) {

        // 校验创建时间参数
        if (param == null) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        // 校验创建时间参数
        if (param.getCreateTimeStart() == null || param.getCreateTimeEnd() == null) {
            throw new BaseException(BaseBizEnum.TIP, "创建时间范围必填");
        }
        //创建时间
        checkCreateTimeRange(param);

        //字段转换
        if (StringUtils.isNotEmpty(param.getField()) && sortList.contains(param.getField())) {
            param.setSortField(param.getField());
        }
        //字段转换
        if (StringUtils.isNotEmpty(param.getOrder()) && Objects.equals(param.getOrder(), "ascend")) {
            param.setSortOrder("ASC");
        }
        if (StringUtils.isNotEmpty(param.getOrder()) && Objects.equals(param.getOrder(), "descend")) {
            param.setSortOrder("DESC");
        }

        Map<String, List<String>> mapAreaMap = areaBizClient.handAreaCascade(param.getReceiverProvList(), param.getReceiverCityList(), param.getReceiverAreaList());
        if (!CollectionUtils.isEmpty(mapAreaMap)) {
            param.setReceiverProvList(mapAreaMap.getOrDefault("receiverProvList", null));
            param.setReceiverCityList(mapAreaMap.getOrDefault("receiverCityList", null));
            param.setReceiverAreaList(mapAreaMap.getOrDefault("receiverAreaList", null));
        }

    }

    /**
     * 校验创建时间范围
     * 这里可以添加具体的逻辑来检查创建时间范围是否符合业务要求
     *
     * @param param 查询参数
     */
    private void checkCreateTimeRange(PackAnalysisBillParam param) {
        // 计算时间跨度（毫秒）
        long timeSpan = param.getCreateTimeEnd() - param.getCreateTimeStart();
        // 不能超过3天
        long maxMonthSpan = 31L * 24 * 60 * 60 * 1000;

        if (timeSpan <= 0) {
            throw new BaseException(BaseBizEnum.TIP, "创建时间结束时间必须大于开始时间");
        }

        if (timeSpan > maxMonthSpan) {
            throw new BaseException(BaseBizEnum.TIP, "创建时间范围不能超过3天");
        }
    }

    @Override
    @SoulClient(path = "/pack/analysis/page", desc = "包裹分析查询（分页版）")
    public Result<PageVO<PackAnalysisBizDTO>> getPackAnalysisPage(PackAnalysisBillParam param) {
        log.info("出库单分析查询（分页版），参数：{}", param);

        // 校验创建时间参数
        validateCreateTimeParam(param);

        Result<Page<PackAnalysisBillDTO>> packAnalysisPage = packageBizClient.getPackAnalysisPage(param);
        PageVO<PackAnalysisBizDTO> packAnalysisBizDTOPageVO = ConverterUtil.convertPageVO(packAnalysisPage.getData(), PackAnalysisBizDTO.class);
        if (!CollectionUtils.isEmpty(packAnalysisBizDTOPageVO.getDataList())) {
            buildVo(packAnalysisBizDTOPageVO.getDataList());
        }
        return Result.success(packAnalysisBizDTOPageVO);
    }

    /**
     * 构建VO
     *
     * @param dataList
     */
    private void buildVo(List<PackAnalysisBizDTO> dataList) {
        //获取仓库名称
        WarehouseBizDTO warehouseBizDTO = iWarehouseBizClient.queryByCode(CurrentRouteHolder.getWarehouseCode()).getData();
        //获取货主集合
        CargoOwnerBizParam cargoOwnerBizParam = new CargoOwnerBizParam();
        cargoOwnerBizParam.setCodeList(dataList.stream().map(PackAnalysisBizDTO::getCargoCode).distinct().collect(Collectors.toList()));
        List<CargoOwnerBizDTO> cargoOwnerBizDTOList = cargoOwnerBizClient.queryList(cargoOwnerBizParam).getData();
        //获取快递公司集合
        CarrierBizParam carrierBizParam = new CarrierBizParam();
        carrierBizParam.setCodeList(dataList.stream().map(PackAnalysisBizDTO::getCarrierCode).distinct().collect(Collectors.toList()));
        List<CarrierBizDTO> carrierBizDTOList = iCarrierBizClient.getList(carrierBizParam).getData();
        //获取平台集合
        SalePlatformQueryBizParam platformQueryBizParam = new SalePlatformQueryBizParam();
        platformQueryBizParam.setCodeList(dataList.stream().map(PackAnalysisBizDTO::getSalePlatform).distinct().collect(Collectors.toList()));
        List<SalePlatformBizDTO> platformBizDTOList = salePlatformBizClient.getList(platformQueryBizParam).getData();
        final int[] i = {1};
        dataList.forEach(a -> {
            a.setId(i[0]);
            i[0]++;
            if (warehouseBizDTO != null) {
                a.setWarehouseName(warehouseBizDTO.getName());
            }
            if (!CollectionUtils.isEmpty(cargoOwnerBizDTOList)) {
                CargoOwnerBizDTO cargoOwnerBizDTO = cargoOwnerBizDTOList.stream().filter(b -> b.getCode().equals(a.getCargoCode())).findAny().orElse(null);
                a.setCargoName(cargoOwnerBizDTO == null ? "" : cargoOwnerBizDTO.getName());
            }
            if (!CollectionUtils.isEmpty(carrierBizDTOList)) {
                CarrierBizDTO carrierBizDTO = carrierBizDTOList.stream().filter(b -> b.getCode().equals(a.getCarrierCode())).findAny().orElse(null);
                a.setCarrierName(carrierBizDTO == null ? "" : carrierBizDTO.getName());
            }
            if (!CollectionUtils.isEmpty(platformBizDTOList)) {
                SalePlatformBizDTO salePlatformBizDTO = platformBizDTOList.stream().filter(b -> b.getCode().equals(a.getSalePlatform())).findAny().orElse(null);
                a.setSalePlatformName(salePlatformBizDTO == null ? "" : salePlatformBizDTO.getName());
            }
            if (a.getPackageStruct() != null) {
                a.setPackageStructName(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(a.getPackageStruct()).getDesc());
            }
            if (a.getIsPre() != null) {
                a.setIsPreDesc(PackEnum.TYPE.findEnumDesc(a.getIsPre()).getDesc());
            }
            //创建小时
            if (StringUtils.isNotEmpty(a.getCreatedTime_hour())) {
                a.setCreatedTime_hour(a.getCreatedTime_hour() + ":00:00");
            }
            //预计出库时间小时
            if (StringUtils.isNotEmpty(a.getExpOutStockDate_hour())) {
                a.setExpOutStockDate_hour(a.getExpOutStockDate_hour() + ":00:00");
            }

            //拣选完成时间小时
            if (StringUtils.isNotEmpty(a.getPickCompleteSkuDate_hour())) {
                a.setPickCompleteSkuDate_hour(a.getPickCompleteSkuDate_hour() + ":00:00");
            }

            //预计出库时间小时
            if (StringUtils.isNotEmpty(a.getCheckCompleteDate_hour())) {
                a.setCheckCompleteDate_hour(a.getCheckCompleteDate_hour() + ":00:00");
            }
            //出库时间小时
            if (StringUtils.isNotEmpty(a.getOutStockDate_hour())) {
                a.setOutStockDate_hour(a.getOutStockDate_hour() + ":00:00");
            }
            //付款时间小时
            if (StringUtils.isNotEmpty(a.getPayDate_hour())) {
                a.setPayDate_hour(a.getPayDate_hour() + ":00:00");
            }
        });
    }

    @Override
    @SoulClient(path = "/pack/analysis/getPackPage", desc = "包裹查询分析查询（分页版）")
    public Result<PageVO<PackageVO>> getPackAnalysisPageByPack(PackAnalysisBillParam param) {
        if (param == null) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        PackageBizParam packageBizParam = buildPackEsParam(param);

        Result<PageVO<PackageVO>> pageVOResult = packagePortClient.queryPage(packageBizParam);

        return pageVOResult;
    }

    /**
     * 校验分析维度
     * 这里可以添加具体的逻辑来检查分析维度是否符合业务要求
     *
     * @param param 查询参数
     */
    private void checkAnalysisDimensionsByPackPage(PackAnalysisBillParam param) {
        if (StringUtils.isEmpty(param.getColumnKey())) {
            throw new BaseException(BaseBizEnum.TIP, "数量列必要参数不能为空");
        }

        if (!CollectionUtils.isEmpty(param.getAnalysisDimensions())) {
            param.getAnalysisDimensions().forEach(it -> {
                //货主维度
                if (Objects.equals(it, "cargoCode") && StringUtils.isEmpty(param.getCargoCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "货主编码不能为空");
                }
                //业务类型维度
                if (Objects.equals(it, "businessType") && StringUtils.isEmpty(param.getBusinessType())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务类型不能为空");
                }
                //销售平台维度
                if (Objects.equals(it, "salePlatform") && StringUtils.isEmpty(param.getSalePlatform())) {
                    throw new BaseException(BaseBizEnum.TIP, "销售平台不能为空");
                }
                //包裹类型维度
                if (Objects.equals(it, "packageStruct") && StringUtils.isEmpty(param.getPackageStruct())) {
                    throw new BaseException(BaseBizEnum.TIP, "包裹类型不能为空");
                }
                //预计出库时间维度
                if (Objects.equals(it, "expOutStockDate_day") || Objects.equals(it, "expOutStockDate_hour")) {
                    if (param.getExpOutStockDate_day() == null && param.getExpOutStockDate_hour() == null) {
                        throw new BaseException(BaseBizEnum.TIP, "预计出库时间不能为空");
                    }
                }
                //创建时间维度
                if (Objects.equals(it, "createTime_day") || Objects.equals(it, "createTime_hour")) {
                    if (param.getCreatedTime_day() == null && param.getCreatedTime_hour() == null) {
                        throw new BaseException(BaseBizEnum.TIP, "创建时间不能为空");
                    }
                }
                //付款时间维度
                if (Objects.equals(it, "payDate_day") || Objects.equals(it, "payDate_hour")) {
                    if (param.getPayDate_day() == null && param.getPayDate_hour() == null) {
                        throw new BaseException(BaseBizEnum.TIP, "付款时间不能为空");
                    }
                }
                //出库时间维度
                if (Objects.equals(it, "outStockDate_day") || Objects.equals(it, "outStockDate_hour")) {
                    if (param.getOutStockDate_day() == null && param.getOutStockDate_hour() == null) {
                        throw new BaseException(BaseBizEnum.TIP, "出库时间不能为空");
                    }
                }
                //快递公司维度
                if (Objects.equals(it, "carrierCode") && StringUtils.isEmpty(param.getCarrierCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "快递公司编码不能为空");
                }
                //省份维度
                if (Objects.equals(it, "receiverProvName") && StringUtils.isEmpty(param.getReceiverProvName())) {
                    throw new BaseException(BaseBizEnum.TIP, "省份不能为空");
                }
                //城市维度
                if (Objects.equals(it, "receiverCityName") && StringUtils.isEmpty(param.getReceiverCityName())) {
                    throw new BaseException(BaseBizEnum.TIP, "城市不能为空");
                }
                //区/县维度
                if (Objects.equals(it, "receiverAreaName") && StringUtils.isEmpty(param.getReceiverAreaName())) {
                    throw new BaseException(BaseBizEnum.TIP, "区/县不能为空");
                }

            });
        }
    }

    /**
     * 构建出库单分析查询参数
     *
     * @param param
     * @return
     */
    private PackageBizParam buildPackEsParam(PackAnalysisBillParam param) {
        PackageBizParam packageBizParam = ConverterUtil.convert(param, PackageBizParam.class);
        packageBizParam.setReceiverProvName(param.getReceiverProvName());
        packageBizParam.setReceiverCityName(param.getReceiverCityName());
        packageBizParam.setReceiverAreaName(param.getReceiverAreaName());

        //创建时间
        checkCreateTimeRange(param);
        //必要参数
        checkAnalysisDimensionsByPackPage(param);

        //--------------------状态码转换------------------

        //创建状态
        if (Objects.equals(param.getColumnKey(), "createdOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.CREATE_STATUS.getCode());
        }
        //预处理失败
        if (Objects.equals(param.getColumnKey(), "pretreatmentFailOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.PRETREATMENT_FAIL.getCode());
        }
        //预处理完成
        if (Objects.equals(param.getColumnKey(), "pretreatmentCompleteOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
        }
        //汇总失败
        if (Objects.equals(param.getColumnKey(), "collectedFailOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.ASSGIN_STOCK_STATUS.getCode());
        }
        //汇总完成
        if (Objects.equals(param.getColumnKey(), "collectedOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
        }

        //拣选开始
        if (Objects.equals(param.getColumnKey(), "pickStartOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.PICK_BEGIN_STATUS.getCode());
        }
        //拣选完成
        if (Objects.equals(param.getColumnKey(), "pickEndOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode());
        }

        //复核开始
        if (Objects.equals(param.getColumnKey(), "checkStartOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode());
        }
        //复核完成
        if (Objects.equals(param.getColumnKey(), "checkCompleteOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        }

        //缺货出库
        if (Objects.equals(param.getColumnKey(), "interceptCancelOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode());
        }

        //已出库
        if (Objects.equals(param.getColumnKey(), "outOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        }
        //拦截
        if (Objects.equals(param.getColumnKey(), "interceptOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode());
        }
        //取消
        if (Objects.equals(param.getColumnKey(), "cancelOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode());
        }
        //缺货出库
        if (Objects.equals(param.getColumnKey(), "shortageOutOrderCount")) {
            packageBizParam.setStatus(PackEnum.STATUS.UNENOUGH_STOCK_STATUS.getCode());
        }
        //--------------------状态码转换------------------

        //创建时间
        if (param.getCreateTimeStart() != null && param.getCreateTimeEnd() != null) {
            packageBizParam.setCreatedTimeStart(param.getCreateTimeStart());
            packageBizParam.setCreatedTimeEnd(param.getCreateTimeEnd());
        }
        //商品数
        if (param.getSkuQtyMax() != null) {
            packageBizParam.setEndSkuCount(param.getSkuQtyMax());
        }
        if (param.getSkuQtyMin() != null) {
            packageBizParam.setStartSkuCount(param.getSkuQtyMin());
        }
        //订单数
        if (param.getSkuTypeQtyMax() != null) {
            packageBizParam.setEndSkuTypeCount(param.getSkuTypeQtyMax());
        }
        if (param.getSkuTypeQtyMin() != null) {
            packageBizParam.setStartSkuTypeCount(param.getSkuTypeQtyMin());
        }
        //重量
        if (param.getWeightMin() != null) {
            packageBizParam.setWeightStart(BigDecimal.valueOf(param.getWeightMin()));
        }
        if (param.getWeightMax() != null) {
            packageBizParam.setWeightEnd(BigDecimal.valueOf(param.getWeightMax()));
        }
        //-----------------付款时间------------------
        if (param.getPayDateStart() != null) {
            packageBizParam.setStartPayTime(param.getPayDateStart());
        }
        if (param.getPayDateEnd() != null) {
            packageBizParam.setEndPayTime(param.getPayDateEnd());
        }
        //按天
        if (StringUtils.isNotEmpty(param.getPayDate_day())) {
            long time = DateUtil.parse(param.getPayDate_day(), "yyyy-MM-dd").getTime();
            packageBizParam.setStartPayTime(DateUtil.beginOfDay(new Date(time)).getTime());
            packageBizParam.setEndPayTime(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getPayDate_hour())) {
            long time = DateUtil.parse(param.getPayDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageBizParam.setStartPayTime(time);
            packageBizParam.setEndPayTime(time + 3600L * 1000);
        }
        //-----------------付款时间------------------

        //-----------------创建时间------------------
        //按天
        if (StringUtils.isNotEmpty(param.getCreatedTime_day())) {
            long time = DateUtil.parse(param.getCreatedTime_day(), "yyyy-MM-dd").getTime();
            packageBizParam.setCreatedTimeStart(DateUtil.beginOfDay(new Date(time)).getTime());
            packageBizParam.setCreatedTimeEnd(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getCreatedTime_hour())) {
            long time = DateUtil.parse(param.getCreatedTime_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageBizParam.setCreatedTimeStart(time);
            packageBizParam.setCreatedTimeEnd(time + 3600L * 1000);
        }
        //-----------------创建时间------------------

        //-----------------出库时间------------------
        if (param.getOutStockDateStart() != null) {
            packageBizParam.setStartOutStockDate(param.getOutStockDateStart());
        }
        if (param.getOutStockDateEnd() != null) {
            packageBizParam.setEndOutStockDate(param.getOutStockDateEnd());
        }
        //按天
        if (StringUtils.isNotEmpty(param.getOutStockDate_day())) {
            long time = DateUtil.parse(param.getOutStockDate_day(), "yyyy-MM-dd").getTime();
            packageBizParam.setStartOutStockDate(DateUtil.beginOfDay(new Date(time)).getTime());
            packageBizParam.setEndOutStockDate(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getOutStockDate_hour())) {
            long time = DateUtil.parse(param.getOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageBizParam.setStartOutStockDate(time);
            packageBizParam.setEndOutStockDate(time + 3600L * 1000);
        }
        //-----------------出库时间------------------

        //-----------------拣选完成------------------
        if (param.getPickCompleteDateStart() != null) {
            packageBizParam.setPickCompleteSkuDateStart(param.getPickCompleteDateStart());
        }
        if (param.getPickCompleteDateEnd() != null) {
            packageBizParam.setPickCompleteSkuDateEnd(param.getPickCompleteDateEnd());
        }
        //按天
        if (StringUtils.isNotEmpty(param.getPickCompleteSkuDate_day())) {
            long time = DateUtil.parse(param.getPickCompleteSkuDate_day(), "yyyy-MM-dd").getTime();
            packageBizParam.setPickCompleteSkuDateStart(DateUtil.beginOfDay(new Date(time)).getTime());
            packageBizParam.setPickCompleteSkuDateEnd(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getPickCompleteSkuDate_hour())) {
            long time = DateUtil.parse(param.getPickCompleteSkuDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageBizParam.setPickCompleteSkuDateStart(time);
            packageBizParam.setPickCompleteSkuDateEnd(time + 3600L * 1000);
        }
        //-----------------拣选完成------------------

        //-----------------复核完成------------------
        if (param.getCheckCompleteDateStart() != null) {
            packageBizParam.setCheckCompleteDateStart(param.getCheckCompleteDateStart());
        }
        if (param.getCheckCompleteDateEnd() != null) {
            packageBizParam.setCheckCompleteDateEnd(param.getCheckCompleteDateEnd());
        }
        //按天
        if (StringUtils.isNotEmpty(param.getCheckCompleteDate_day())) {
            long time = DateUtil.parse(param.getCheckCompleteDate_day(), "yyyy-MM-dd").getTime();
            packageBizParam.setCheckCompleteDateStart(DateUtil.beginOfDay(new Date(time)).getTime());
            packageBizParam.setCheckCompleteDateEnd(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getCheckCompleteDate_hour())) {
            long time = DateUtil.parse(param.getCheckCompleteDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageBizParam.setCheckCompleteDateStart(time);
            packageBizParam.setCheckCompleteDateEnd(time + 3600L * 1000);
        }
        //-----------------复核完成------------------


        //-----------------预计出库时间------------------
        if (param.getExpOutStockDateStart() != null) {
            packageBizParam.setStartExpOutStockDate(param.getExpOutStockDateStart());
        }
        if (param.getExpOutStockDateEnd() != null) {
            packageBizParam.setEndExpOutStockDate(param.getExpOutStockDateEnd());
        }
        //按天
        if (StringUtils.isNotEmpty(param.getExpOutStockDate_day())) {
            long time = DateUtil.parse(param.getExpOutStockDate_day(), "yyyy-MM-dd").getTime();
            packageBizParam.setStartExpOutStockDate(DateUtil.beginOfDay(new Date(time)).getTime());
            packageBizParam.setEndExpOutStockDate(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getExpOutStockDate_hour())) {
            long time = DateUtil.parse(param.getExpOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageBizParam.setStartExpOutStockDate(time);
            packageBizParam.setEndExpOutStockDate(time + 3600L * 1000);
        }
        //-----------------预计出库时间------------------

        return packageBizParam;
    }


    @Override
    @SoulClient(path = "/pack/analysis/summary", desc = "包裹分析汇总查询")
    public Result<PackAnalysisBizDTO> getPackAnalysisSummary(PackAnalysisBillParam param) {
        log.info("包裹分析汇总查询，参数：{}", param);

        // 校验创建时间参数
        validateCreateTimeParam(param);

        //分组
        param.setAnalysisDimensions(new ArrayList<>());

        Result<Page<PackAnalysisBillDTO>> packAnalysisPage = packageBizClient.getPackAnalysisPage(param);
        PackAnalysisBizDTO packAnalysisBizDTO = new PackAnalysisBizDTO();
        if (!CollectionUtils.isEmpty(packAnalysisPage.getData().getRecords())) {
            packAnalysisBizDTO = ConverterUtil.convert(packAnalysisPage.getData().getRecords().get(0), PackAnalysisBizDTO.class);
        }
        return Result.success(packAnalysisBizDTO);
    }
}
