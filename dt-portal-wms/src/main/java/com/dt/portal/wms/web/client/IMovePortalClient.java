package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.move.*;
import com.dt.portal.wms.web.vo.move.MoveVO;

import java.util.List;

public interface IMovePortalClient {

    /**
     * 获取移位单状态下拉列表
     * @return
     */
    Result<List<IdNameVO>> getStatusList();
    Result<List<IdNameVO>> getAllStatusList();

    /**
     * 新增
     *
     * @param param
     * @return
     */
    Result<Boolean> create(MoveAddParam param);

    /**
     * 修改
     *
     * @param param
     * @return
     */
    Result<Boolean> modify(MoveUpdateParam param);


    /**
     * 修改上架单上架方式
     * @param param
     * @return
     */
    Result<Boolean> modifyOpType(MoveOpTypeParam param);

    /**
     * 移位单分页列表
     * @param param
     * @return
     */
    Result<PageVO<MoveVO>> getPage(MoveBizParam param);

    /**
     * 获取详情
     * @param param
     * @return
     */
    Result<MoveVO> getDetail(CodeParam param);

    /**
     * 完成移位单
     * @param param
     * @return
     */
    Result<Boolean> complete(CodeParam param);

    /**
     * 完成移位单明细
     * @param param
     * @return
     */
    Result<Boolean> completeDetail(MoveDetailCompleteBizParam param);

    /**
     * 完成移位单剩余明细
     * @param param
     */
    Result<Boolean> completeRetainDetail(CodeParam param);
}
