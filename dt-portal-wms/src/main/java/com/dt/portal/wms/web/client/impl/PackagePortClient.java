package com.dt.portal.wms.web.client.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.pkg.PackIsPreEnum;
import com.dt.component.common.enums.pkg.PackageUnitEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.dto.PackageDetailDTO;
import com.dt.domain.bill.param.PackageDetailParam;
import com.dt.elasticsearch.wms.client.IPackageEsClient;
import com.dt.elasticsearch.wms.dto.PackageIndexDTO;
import com.dt.elasticsearch.wms.param.PackageEsParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.*;
import com.dt.platform.wms.dto.base.AllocationRuleBizDTO;
import com.dt.platform.wms.dto.base.TurnoverRuleBizDTO;
import com.dt.platform.wms.dto.base.WarehouseBizDTO;
import com.dt.platform.wms.dto.cargo.CargoOwnerBizDTO;
import com.dt.platform.wms.dto.pkg.PackageBizDTO;
import com.dt.platform.wms.dto.pkg.PackageDetailBizDTO;
import com.dt.platform.wms.dto.pkg.PackageLogBizDTO;
import com.dt.platform.wms.param.IdParam;
import com.dt.platform.wms.param.pkg.*;
import com.dt.portal.wms.web.client.IPackagePortClient;
import com.dt.portal.wms.web.vo.pkg.PackageDetailVO;
import com.dt.portal.wms.web.vo.pkg.PackageLogVO;
import com.dt.portal.wms.web.vo.pkg.PackageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
@RefreshScope
public class PackagePortClient implements IPackagePortClient {

    @DubboReference
    ICarrierBizClient iCarrierBizClient;

    @DubboReference
    ICargoOwnerBizClient iCargoOwnerBizClient;

    @DubboReference
    IWarehouseBizClient iWarehouseBizClient;

    @DubboReference
    IAllocationRuleClient iAllocationRuleClient;

    @DubboReference
    TurnoverRuleClient turnoverRuleClient;

    @DubboReference
    private IPackageBizClient ipackageClient;

    @DubboReference
    private IShipmentOrderBizClient shipmentOrderClient;

    @DubboReference
    private IAreaBizClient areaClient;

    @DubboReference
    IPackageDetailClient packageDetailClient;

    @DubboReference
    private IPackageEsClient packageEsClient;

    @Value("${page-from-es:false}")
    private Boolean pageFromEs;

    @Override
    @SoulClient(path = "/package/status", desc = "包裹状态")
    public Result<List<IdNameVO>> findListStatus() {
        List<IdNameVO> result = Arrays.stream(PackEnum.STATUS.values()).filter(s -> !StringUtils.isEmpty(s.getCode())).map((PackEnum.STATUS s) -> {
            IdNameVO vo = new IdNameVO();
            vo.setName(s.getDesc());
            vo.setId(s.getCode());
            return vo;
        }).collect(Collectors.toList());
        return Result.success(result);
    }

    @Override
    @SoulClient(path = "/package/struct", desc = "包裹结构")
    public Result<List<IdNameVO>> findPackageStruct() {
        List<IdNameVO> result = Arrays.stream(ShipmentOrderEnum.PACKAGE_STRUCT.values()).filter(s -> !StringUtils.isEmpty(s.getCode())).map((ShipmentOrderEnum.PACKAGE_STRUCT s) -> {
            IdNameVO vo = new IdNameVO();
            vo.setName(s.getDesc());
            vo.setId(s.getCode());
            return vo;
        }).collect(Collectors.toList());
        return Result.success(result);
    }


    @Override
    public Result<PageVO<PackageVO>> queryPageFromES(PackageBizParam param) {
        PackageEsParam esParam = ConverterUtil.convert(param, PackageEsParam.class);
        Result<PageVO<PackageIndexDTO>> pageVOResult = packageEsClient.getPage(esParam);

        PageVO<PackageVO> pageVO = new PageVO<>();
        pageVO.setPage(pageVOResult.getData().getPage());

        List<PackageVO> dataList = new ArrayList<>();
        //获取包裹打印次数
        Map<String, Long> packPrintNumMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(pageVOResult.getData().getDataList())) {
            PackageBizParam printPackBizParam = new PackageBizParam();
            printPackBizParam.setPackageCodeList(pageVOResult.getData().getDataList().stream().map(PackageIndexDTO::getPackageCode).distinct().collect(Collectors.toList()));
            Map<String, Long> packPrintNum = ipackageClient.getPackPrintNum(printPackBizParam).getData();
            if (!CollectionUtils.isEmpty(packPrintNum)) {
                packPrintNumMap.putAll(packPrintNum);
            }
        }

        pageVOResult.getData().getDataList().forEach(it -> {
            PackageVO vo = ConverterUtil.convert(it, PackageVO.class);
            vo.setPickSkuDate(ConverterUtil.convertVoTime(it.getPickSkuDate()));
            vo.setCreatedTime(ConverterUtil.convertVoTime(it.getCreatedTime()));
            vo.setId(it.getKey());
            vo.setUpdatedTime(ConverterUtil.convertVoTime(it.getUpdatedTime()));
            vo.setPickCompleteSkuDate(ConverterUtil.convertVoTime(it.getPickCompleteSkuDate()));
            vo.setCheckStartDate(ConverterUtil.convertVoTime(it.getCheckStartDate()));
            vo.setCheckCompleteDate(ConverterUtil.convertVoTime(it.getCheckCompleteDate()));
            vo.setInterceptCancelDate(ConverterUtil.convertVoTime(it.getInterceptCancelDate()));
            vo.setOutStockDate(ConverterUtil.convertVoTime(it.getOutStockDate()));
            vo.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(it.getPackageStruct()).getDesc());
            //解析订单tag
            vo.setOrderTagName("");
            if (!StringUtils.isEmpty(vo.getOrderTag())) {
                Set<OrderTagEnum> orderTagEnumList = OrderTagEnum.NumToEnum(vo.getOrderTag());
                if (!CollectionUtils.isEmpty(orderTagEnumList)) {
                    String orderTagName = orderTagEnumList.stream().map(OrderTagEnum::getDesc).collect(Collectors.joining("|"));
                    vo.setOrderTagName(orderTagName);
                } else {
                    vo.setOrderTagName("");
                }
            }
            if (!CollectionUtils.isEmpty(packPrintNumMap) && packPrintNumMap.containsKey(it.getPackageCode())) {
                vo.setExpressPrintNum(Math.toIntExact(packPrintNumMap.get(it.getPackageCode())));
            }

            dataList.add(vo);
        });
        pageVO.setDataList(dataList);
        return Result.success(pageVO);
    }

    @Override
    @SoulClient(path = "/package/page", desc = "包裹分页查询")
    public Result<PageVO<PackageVO>> queryPage(PackageBizParam param) {
        //todo ---------------------省市区级联 多选
        Map<String, List<String>> mapAreaMap = areaClient.handAreaCascade(param.getReceiverProvList(), param.getReceiverCityList(), param.getReceiverAreaList());
        if (!CollectionUtils.isEmpty(mapAreaMap)) {
            param.setReceiverProvList(mapAreaMap.getOrDefault("receiverProvList", null));
            param.setReceiverCityList(mapAreaMap.getOrDefault("receiverCityList", null));
            param.setReceiverAreaList(mapAreaMap.getOrDefault("receiverAreaList", null));
        }
        //todo ---------------------省市区级联 多选
        if (Boolean.TRUE.equals(pageFromEs)) {
            return queryPageFromES(param);
        }
        log.info("[op:PackagePortClient:queryPage ] param =  {}", JSON.toJSONString(param));
        //订单标记
        if (param != null && !StringUtils.isEmpty(param.getOrderTagList())) {
            param.setOrderTag(OrderTagEnum.queryParamListToInteger(param.getOrderTagList()));
        }
        Result<Page<PackageBizDTO>> pageResult = ipackageClient.queryPage(param);
        Page<PackageBizDTO> result = pageResult.getData();
        PageVO.Page page = new PageVO.Page();
        List<PackageBizDTO> packageBizDTOList = pageResult.getData().getRecords();

        //获取包裹打印次数
        Map<String, Long> packPrintNumMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(packageBizDTOList)) {
            PackageBizParam printPackBizParam = new PackageBizParam();
            printPackBizParam.setPackageCodeList(packageBizDTOList.stream().map(PackageBizDTO::getPackageCode).distinct().collect(Collectors.toList()));
            Map<String, Long> packPrintNum = ipackageClient.getPackPrintNum(printPackBizParam).getData();
            if (!CollectionUtils.isEmpty(packPrintNum)) {
                packPrintNumMap.putAll(packPrintNum);
            }
        }

        List<PackageVO> packageVOList = packageBizDTOList.stream().map((PackageBizDTO packageBizDTO) -> {
            PackageVO packageVO = new PackageVO();
            BeanUtils.copyProperties(packageBizDTO, packageVO);
//            //-----------------------------
//            ShipmentOrderBizParam shipmentOrderParam = new ShipmentOrderBizParam();
//            shipmentOrderParam.setShipmentOrderCode(packageBizDTO.getShipmentOrderCode());
//            ShipmentOrderBizDTO2 shipmentOrderDTO2 = shipmentOrderClient.get(shipmentOrderParam).getData();
//            packageVO.setExpShipTimeDateFormat(ConverterUtil.convertVoTime(shipmentOrderDTO2.getExpShipTime() == null ? 0L : shipmentOrderDTO2.getExpShipTime()));
//            if (!StringUtils.isEmpty(shipmentOrderDTO2.getPreSaleType())) {
//                packageVO.setPreSaleTypeName(ShipmentPreSaleTypeEnum.fromCode(shipmentOrderDTO2.getPreSaleType()).getMessage());
//            } else {
//                packageVO.setPreSaleTypeName("");
//            }
//            List<ShipmentOrderMaterialDTO> shipmentMaterial = shipmentOrderClient.getShipmentMaterial(packageBizDTO.getShipmentOrderCode());
//            if (CollectionUtil.isNotEmpty(shipmentMaterial)) {
//                packageVO.setMaterialUpcCode(shipmentMaterial.stream().map(ShipmentOrderMaterialDTO::getRecPackUpcCode).distinct().collect(Collectors.joining(StrUtil.COMMA)));
//            } else {
//                packageVO.setMaterialUpcCode("");
//            }
//            packageVO.setSaleShop(shipmentOrderDTO2.getSaleShop());
//            packageVO.setSaleShopId(shipmentOrderDTO2.getSaleShopId());
//            packageVO.setReceiverAreaName(shipmentOrderDTO2.getReceiverAreaName());
//            packageVO.setReceiverCityName(shipmentOrderDTO2.getReceiverCityName());
//            packageVO.setReceiverProvName(shipmentOrderDTO2.getReceiverProvName());
//            packageVO.setReceiverAddress(shipmentOrderDTO2.getReceiverAddress());
//            packageVO.setReceiverMan(shipmentOrderDTO2.getReceiverMan());
//            packageVO.setReceiverTel(shipmentOrderDTO2.getReceiverTel());
//            //-----------------------------
            packageVO.setActualPackWeight((packageBizDTO.getActualPackWeight() == null ? BigDecimal.ZERO : packageBizDTO.getActualPackWeight()).setScale(packageBizDTO.getWeightFormat(), RoundingMode.FLOOR).toString());
            packageVO.setWeight((packageBizDTO.getWeight() == null ? BigDecimal.ZERO : packageBizDTO.getWeight()).setScale(packageBizDTO.getWeightFormat(), RoundingMode.FLOOR).toString());
            packageVO.setRealWeight((packageBizDTO.getRealWeight() == null ? BigDecimal.ZERO : packageBizDTO.getRealWeight()).setScale(packageBizDTO.getWeightFormat(), RoundingMode.FLOOR).toString());
            packageVO.setVolumetricWeight((packageBizDTO.getVolumetricWeight() == null ? BigDecimal.ZERO : packageBizDTO.getVolumetricWeight()).setScale(packageBizDTO.getWeightFormat(), RoundingMode.FLOOR).toString());
            BigDecimal packageSkuQty = packageBizDTO.getPackageSkuQty() == null ? BigDecimal.ZERO : packageBizDTO.getPackageSkuQty();
            packageVO.setPackageSkuQty(packageSkuQty.setScale(packageBizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
            packageSkuQty = packageBizDTO.getOutSkuQty() == null ? BigDecimal.ZERO : packageBizDTO.getOutSkuQty();
            packageVO.setOutSkuQty(packageSkuQty.setScale(packageBizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
            packageVO.setCheckCompleteDate(ConverterUtil.convertVoTime(packageBizDTO.getCheckCompleteDate()));
            packageVO.setCheckStartDate(ConverterUtil.convertVoTime(packageBizDTO.getCheckStartDate()));
            packageVO.setInterceptCancelDate(ConverterUtil.convertVoTime(packageBizDTO.getInterceptCancelDate()));
            packageVO.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(packageBizDTO.getPackageStruct()).getDesc());
            packageVO.setOutStockDate(ConverterUtil.convertVoTime(packageBizDTO.getOutStockDate()));
            packageVO.setPickCompleteSkuDate(ConverterUtil.convertVoTime(packageBizDTO.getPickCompleteSkuDate()));
            packageVO.setPickSkuDate(ConverterUtil.convertVoTime(packageBizDTO.getPickSkuDate()));
            packageVO.setCreatedTime(ConverterUtil.convertVoTime(packageBizDTO.getCreatedTime()));
            packageVO.setUpdatedTime(ConverterUtil.convertVoTime(packageBizDTO.getUpdatedTime()));
            if (!org.apache.commons.lang3.StringUtils.isEmpty(packageVO.getSkuQuality())) {
                packageVO.setSkuQualityName(SkuQualityEnum.getEnum(packageVO.getSkuQuality()).getMessage());
            } else {
                packageVO.setSkuQualityName("");
            }

            //解析订单tag
            packageVO.setOrderTagName("");
            if (!StringUtils.isEmpty(packageVO.getOrderTag())) {
                Set<OrderTagEnum> orderTagEnumList = OrderTagEnum.NumToEnum(packageVO.getOrderTag());
                if (!CollectionUtils.isEmpty(orderTagEnumList)) {
                    String orderTagName = orderTagEnumList.stream().map(OrderTagEnum::getDesc).collect(Collectors.joining("|"));
                    packageVO.setOrderTagName(orderTagName);
                } else {
                    packageVO.setOrderTagName("");
                }
            }
            if (!CollectionUtils.isEmpty(packPrintNumMap) && packPrintNumMap.containsKey(packageVO.getPackageCode())) {
                packageVO.setExpressPrintNum(Math.toIntExact(packPrintNumMap.get(packageVO.getPackageCode())));
            }
            return packageVO;
        }).collect(Collectors.toList());
        page.setPageSize(result.getSize());
        page.setCurrentPage(result.getCurrent());
        page.setTotalPage(result.getPages());
        page.setTotalCount(result.getTotal());
        PageVO<PackageVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(packageVOList);
        Result<PageVO<PackageVO>> pageVOResult = new Result<>();
        pageVOResult.setCode(pageResult.getCode());
        pageVOResult.setMessage(pageResult.getMessage());
        pageVOResult.setData(pageVO);
        return pageVOResult;
    }


    @Override
    @SoulClient(path = "/package/report", desc = "包裹视频回告")
    @UCApi(type = AuthTypeEnum.OPEN)
    public Result videoReport(PackageVideoReportBizParam param) {
        if (ObjectUtil.isEmpty(param.getWarehouseCode())) {
            return Result.fail("仓编码不能为空");
        }
        return Result.success(ipackageClient.videoReport(param));
    }

    @Override
    @SoulClient(path = "/package/videoReportBySelf", desc = "包裹视频回告非淘天")
    @UCApi(type = AuthTypeEnum.OPEN)
    public Result<String> videoReportBySelf(PackageVideoReportBySelfBizParam param) {
        if (ObjectUtil.isEmpty(param.getWarehouseCode())) {
            return Result.fail("仓编码不能为空");
        }
        if (ObjectUtil.isEmpty(param.getPackageCode())) {
            return Result.fail("包裹号不能为空");
        }
        if (ObjectUtil.isEmpty(param.getUrl())) {
            return Result.fail("url不能为空");
        }
        return ipackageClient.videoReportBySelf(param);
    }


    @Override
    @SoulClient(path = "/fileUpload/apply", desc = "文件申请上传")
    @UCApi(type = AuthTypeEnum.OPEN)
    public Result<FileUploadApplyResponse> fileUploadApply(FileUploadParam param) {
        return Result.success(ipackageClient.fileUploadApply(param).getData());
    }

    @Override
    @SoulClient(path = "/package/pre/selectItem", desc = "预包包裹类型下拉列表")
    public Result<List<IdNameVO>> preSelectItem() {
        return Result.success(IdNameVO.buildAll(PackEnum.TYPE.class, "code", "desc"));
    }


    @Override
    @SoulClient(path = "/package/queryDetail", desc = "包裹明细")
    public Result<PackageVO> queryDetail(IdParam idParam) {
        log.info("[op:PackagePortClient:queryDetail ] idParam =  {}", JSON.toJSONString(idParam));
        PackageBizParam searchPackageBizParam = new PackageBizParam();
        searchPackageBizParam.setId(idParam.getId());
        PackageBizDTO packageBizDTO = ipackageClient.getDetail(searchPackageBizParam).getData();
        PackageVO pageVo = new PackageVO();
        BeanUtils.copyProperties(packageBizDTO, pageVo);
        CargoOwnerBizDTO cargoOwnerDTO = iCargoOwnerBizClient.queryByCode(packageBizDTO.getCargoCode()).getData();
        if (cargoOwnerDTO != null) {
            pageVo.setCargoName(cargoOwnerDTO.getName());
        } else {
            pageVo.setCargoName("");
        }
        WarehouseBizDTO warehouseDTO = iWarehouseBizClient.queryByCode(packageBizDTO.getWarehouseCode()).getData();
        if (warehouseDTO != null) {
            pageVo.setWarehouseName(warehouseDTO.getName());
        } else {
            pageVo.setWarehouseName("");
        }
        pageVo.setActualPackWeight((packageBizDTO.getActualPackWeight() == null ? BigDecimal.ZERO : packageBizDTO.getActualPackWeight()).setScale(packageBizDTO.getWeightFormat(), RoundingMode.FLOOR).toString());
        pageVo.setWeight((packageBizDTO.getWeight() == null ? BigDecimal.ZERO : packageBizDTO.getWeight()).setScale(packageBizDTO.getWeightFormat(), RoundingMode.FLOOR).toString());
        pageVo.setRealWeight((packageBizDTO.getRealWeight() == null ? BigDecimal.ZERO : packageBizDTO.getRealWeight()).setScale(packageBizDTO.getWeightFormat(), RoundingMode.FLOOR).toString());
        BigDecimal _bigDecimal = packageBizDTO.getPackageSkuQty() == null ? BigDecimal.ZERO : packageBizDTO.getPackageSkuQty();
        pageVo.setPackageSkuQty(_bigDecimal.setScale(packageBizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
        _bigDecimal = packageBizDTO.getOutSkuQty() == null ? BigDecimal.ZERO : packageBizDTO.getOutSkuQty();
        pageVo.setOutSkuQty(_bigDecimal.setScale(packageBizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
        Optional<PackEnum.TYPE> packEnumOptional = Optional.ofNullable(PackEnum.TYPE.findEnumDesc(pageVo.getIsPre()));
        pageVo.setIsPreName(packEnumOptional.isPresent() ? packEnumOptional.get().getDesc() : "");
        pageVo.setIsPreDesc(packEnumOptional.isPresent() ? packEnumOptional.get().getDesc() : "");
        if (!org.apache.commons.lang3.StringUtils.isEmpty(pageVo.getSkuQuality())) {
            pageVo.setSkuQualityName(SkuQualityEnum.getEnum(pageVo.getSkuQuality()).getMessage());
        } else {
            pageVo.setSkuQualityName("");
        }
        List<PackageDetailVO> voList = new ArrayList<>();
        pageVo.setListDetail(voList);

        List<PackageLogVO> voLogList = new ArrayList<PackageLogVO>();
        List<PackageLogBizDTO> logListDetail = packageBizDTO.getLogListDetail();
        logListDetail = logListDetail.stream().sorted(Comparator.comparing(PackageLogBizDTO::getOpDate).reversed()).collect(Collectors.toList());
        for (PackageLogBizDTO dto : logListDetail) {
            PackageLogVO detail = new PackageLogVO();
            BeanUtils.copyProperties(dto, detail);
            detail.setOpDate(ConverterUtil.convertVoTime(dto.getOpDate()));
            detail.setCreatedTime(ConverterUtil.convertVoTime(dto.getCreatedTime()));
            detail.setUpdatedTime(ConverterUtil.convertVoTime(dto.getUpdatedTime()));
            voLogList.add(detail);
        }

        pageVo.setLogListDetail(voLogList);
        return Result.success(pageVo);
    }

    @Override
    @SoulClient(path = "/package/getDetailPage", desc = "包裹明细")
    public Result<PageVO<PackageDetailVO>> getDetailPage(PackageBizParam packageBizParam) {
        if (StringUtils.isEmpty(packageBizParam) || StringUtils.isEmpty(packageBizParam.getPackageCode())
                || StringUtils.isEmpty(packageBizParam.getIsPre())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        Page<PackageDetailDTO> result = packageDetailClient.getPage(ConverterUtil.convert(packageBizParam, PackageDetailParam.class));
        PageVO<PackageDetailVO> page = ConverterUtil.convertPageVO(result, PackageDetailVO.class);
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            List<PackageDetailBizDTO> listDetail = ConverterUtil.convertList(result.getRecords(), PackageDetailBizDTO.class);
            CargoOwnerBizDTO cargoOwnerDTO = iCargoOwnerBizClient.queryByCode(listDetail.get(0).getCargoCode()).getData();
            WarehouseBizDTO warehouseDTO = iWarehouseBizClient.queryByCode(listDetail.get(0).getWarehouseCode()).getData();
            List<PackageDetailVO> voList = new ArrayList<>();
            int i = 0;
            for (PackageDetailBizDTO dto : listDetail) {
                PackageDetailVO detail = new PackageDetailVO();
                BeanUtils.copyProperties(dto, detail);
                detail.setId((long) ++i);
                Optional<PackEnum.STATUS> optional = Optional.ofNullable(PackEnum.STATUS.findEnumDesc(dto.getStatus()));
                detail.setStatusName(optional.isPresent() ? optional.get().getDesc() : "");
                Optional<PackIsPreEnum> packIsPreEnumOptional = Optional.ofNullable(PackIsPreEnum.getEnum(dto.getIsPre()));
                detail.setIsPreName(packIsPreEnumOptional.isPresent() ? packIsPreEnumOptional.get().getMessage() : "");
                detail.setCreatedTime(ConverterUtil.convertVoTime(dto.getCreatedTime()));
                detail.setUpdatedTime(ConverterUtil.convertVoTime(dto.getUpdatedTime()));
                detail.setExpireDateDesc(ConverterUtil.convertVoTime(detail.getExpireDate(), "yyyy-MM-dd"));
                detail.setPackageUnitCode(PackageUnitEnum.fromCode(detail.getPackageUnitCode()).getMessage());
                detail.setWithdrawCompareDateDesc(ConverterUtil.convertVoTime(detail.getWithdrawCompareDate(), "yyyy-MM-dd"));

                detail.setExpireDateStartDesc(ConverterUtil.convertVoTime(detail.getExpireDateStart(), "yyyy-MM-dd"));
                detail.setExpireDateEndDesc(ConverterUtil.convertVoTime(detail.getExpireDateEnd(), "yyyy-MM-dd"));
                detail.setExpireDateShowDesc("");

                if (!StringUtils.isEmpty(detail.getExpireDateDesc())) {
                    detail.setExpireDateShowDesc(detail.getExpireDateDesc() + "至" + detail.getExpireDateDesc());
                }
                //起止都有值
                if (detail.getExpireDateStart() != null
                        && detail.getExpireDateStart() > 0
                        && detail.getExpireDateEnd() != null
                        && detail.getExpireDateEnd() > 0) {
                    detail.setExpireDateShowDesc(detail.getExpireDateStartDesc() + "至" + detail.getExpireDateEndDesc());
                }
                //起有值 止无值
                if (detail.getExpireDateStart() != null
                        && detail.getExpireDateStart() > 0
                        && (detail.getExpireDateEnd() == null
                        || detail.getExpireDateEnd() == 0)) {
                    detail.setExpireDateShowDesc("失效日期:"+detail.getExpireDateStartDesc()+"及以后");
                }
                //止有值 起无值
                if (detail.getExpireDateEnd() != null
                        && detail.getExpireDateEnd() > 0
                        && (detail.getExpireDateStart() == null
                        || detail.getExpireDateStart() == 0)) {
                    detail.setExpireDateShowDesc("失效日期:"+detail.getExpireDateEndDesc()+"及以前");
                }

                //是否赠品
                if (detail.getFreeFlag() == 1) {
                    detail.setSkuCode(detail.getSkuCode().concat("（赠品）"));
                }
                if (cargoOwnerDTO != null) {
                    detail.setCargoName(cargoOwnerDTO.getName());
                } else {
                    detail.setCargoName("");
                }
                if (warehouseDTO != null) {
                    detail.setWarehouseName(warehouseDTO.getName());
                } else {
                    detail.setWarehouseName("");
                }

                if (!StringUtils.isEmpty(detail.getAllocationRuleCode())) {
                    AllocationRuleBizDTO allocationRuleBizDTOResult = iAllocationRuleClient.queryAllocationRuleByCode(detail.getAllocationRuleCode()).getData();
                    if (allocationRuleBizDTOResult != null) {
                        detail.setAllocationRuleCodeName(allocationRuleBizDTOResult.getName());
                    } else {
                        detail.setAllocationRuleCodeName("");
                    }
                } else {
                    detail.setAllocationRuleCodeName("");
                }
                if (!StringUtils.isEmpty(detail.getTurnoverRuleCode())) {
                    TurnoverRuleBizDTO turnoverRuleBizDTO = turnoverRuleClient.queryTurnoverRuleByCode(detail.getTurnoverRuleCode()).getData();
                    if (turnoverRuleBizDTO != null) {
                        detail.setTurnoverRuleCodeName(turnoverRuleBizDTO.getName());
                    } else {
                        detail.setTurnoverRuleCodeName("");
                    }
                } else {
                    detail.setTurnoverRuleCodeName("");
                }
                detail.setInventoryTypeDesc("");
                if (!StringUtils.isEmpty(detail.getInventoryType())) {
                    detail.setInventoryTypeDesc(InventoryTypeEnum.getEnum(detail.getInventoryType()).getMessage());
                }
                voList.add(detail);
            }
            //临时展示
            voList = voList.stream().sorted(Comparator.comparing(PackageDetailVO::getIsPre).thenComparing(PackageDetailVO::getSkuCode)).collect(Collectors.toList());
            if (packageBizParam.getIsPre().equalsIgnoreCase(PackEnum.TYPE.NORMAL.getCode())) {
                voList = voList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
                page.setDataList(voList);
            } else {
                voList = voList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList());
                page.setDataList(voList);
            }
        }
        return Result.success(page);
    }
}
