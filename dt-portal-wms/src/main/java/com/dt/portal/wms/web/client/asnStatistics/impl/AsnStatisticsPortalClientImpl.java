package com.dt.portal.wms.web.client.asnStatistics.impl;

import java.io.Serializable;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboService;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.wms.client.IAsnStatisticsClient;
import com.dt.elasticsearch.wms.dto.AsnStatisticsDTO;
import com.dt.elasticsearch.wms.param.AsnStatisticsParam;
import com.dt.portal.wms.web.client.asnStatistics.IAsnStatisticsPortalClient;
import com.dt.portal.wms.web.vo.asnStatistics.AsnStatisticsVO;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: yousx
 * @Date: 2025/06/10
 * @Description:
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class AsnStatisticsPortalClientImpl implements IAsnStatisticsPortalClient {

    @Resource
    private IAsnStatisticsClient asnStatisticsClient;

    @Override
    public Result<PageVO<AsnStatisticsVO>> getPage(AsnStatisticsParam param) {
        PageVO<AsnStatisticsDTO> page = asnStatisticsClient.getPage(param);
        return null;
    }
}
