package com.dt.portal.wms.web.client.esShipment.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.param.PackageDetainParam;
import com.dt.domain.bill.param.pkg.PackDetainBillParam;
import com.dt.platform.wms.param.pkg.PackageBizParam;
import com.dt.portal.wms.web.client.IPackagePortClient;
import com.dt.portal.wms.web.client.esShipment.IPackDetainPortalClient;
import com.dt.portal.wms.web.vo.pkg.PackageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.util.function.Consumer;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
@RefreshScope
public class PackDetainPortalClientImpl implements IPackDetainPortalClient {


    @Resource
    private IPackagePortClient packagePortClient;


    /**
     * 校验创建时间范围
     * 这里可以添加具体的逻辑来检查创建时间范围是否符合业务要求
     *
     * @param param 查询参数
     */
    private void checkCreateTimeRange(PackDetainBillParam param) {
        // 计算时间跨度（毫秒）
        long timeSpan = param.getCreateTimeEnd() - param.getCreateTimeStart();
        // 不能超过3天
        long maxMonthSpan = 31L * 24 * 60 * 60 * 1000;

        if (timeSpan <= 0) {
            throw new BaseException(BaseBizEnum.TIP, "创建时间结束时间必须大于开始时间");
        }

        if (timeSpan > maxMonthSpan) {
            throw new BaseException(BaseBizEnum.TIP, "创建时间范围不能超过3天");
        }
    }

    /**
     * {@link com.dt.domain.bill.client.IPackageDetainClient#packageDetain(PackageDetainParam)}
     */
    @Override
    @SoulClient(path = "/pack/analysis/getPackDetainPage", desc = "包裹滞留")
    public Result<PageVO<PackageVO>> getPackDetainPageByPack(PackDetainBillParam param) {
        if (param == null) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        PackageBizParam packageBizParam = buildPackEsParam(param);

        return packagePortClient.queryPage(packageBizParam);
    }


    private PackageBizParam buildPackEsParam(PackDetainBillParam param) {
        PackageBizParam packageBizParam = new PackageBizParam();
        BeanUtil.copyProperties(param, packageBizParam);

        //创建时间
        checkCreateTimeRange(param);

        //创建时间
        if (param.getCreateTimeStart() != null && param.getCreateTimeEnd() != null) {
            packageBizParam.setCreatedTimeStart(param.getCreateTimeStart());
            packageBizParam.setCreatedTimeEnd(param.getCreateTimeEnd());
        }

        //-----------------付款时间------------------
        if (param.getPayDateStart() != null) {
            packageBizParam.setStartPayTime(param.getPayDateStart());
        }
        if (param.getPayDateEnd() != null) {
            packageBizParam.setEndPayTime(param.getPayDateEnd());
        }


        //-----------------预计出库时间------------------
        if (param.getExpOutStockDateStart() != null) {
            packageBizParam.setStartExpOutStockDate(param.getExpOutStockDateStart());
        }
        if (param.getExpOutStockDateEnd() != null) {
            packageBizParam.setEndExpOutStockDate(param.getExpOutStockDateEnd());
        }

        processFiled(packageBizParam, param);

        return packageBizParam;
    }

    private void processFiled(PackageBizParam packageBizParam, PackDetainBillParam param) {

        String detainFiled = param.getDetainFiled();

        Consumer<Long> start = packageBizParam::setUpdatedTimeStart;
        Consumer<Long> end = packageBizParam::setUpdatedTimeEnd;
        if (PackEnum.STATUS.CREATE_STATUS.getCode().equalsIgnoreCase(param.getStatus())) {
            start = packageBizParam::setCreatedTimeStart;
            end = packageBizParam::setCreatedTimeEnd;
        }

        DateTime now = DateTime.now();
        switch (detainFiled) {
            case "lt30":
                processFiled(start, end, now, -30, null);
                break;
            case "lt60":
                processFiled(start, end, now, -60, -30);
                break;
            case "lt90":
                processFiled(start, end, now, -90, -60);
                break;
            case "lt120":
                processFiled(start, end, now, -120, -90);
                break;
            case "lt150":
                processFiled(start, end, now, -150, -120);
                break;
            case "lt180":
                processFiled(start, end, now, -180, -150);
                break;
            case "lt210":
                processFiled(start, end, now, -210, -180);
                break;
            case "lt240":
                processFiled(start, end, now, -240, -210);
                break;
            case "lt270":
                processFiled(start, end, now, -270, -240);
                break;
            case "lt300":
                processFiled(start, end, now, -300, -270);
                break;
            case "lt360":
                processFiled(start, end, now, -360, -300);
                break;
            case "lt420":
                processFiled(start, end, now, -420, -360);
                break;
            case "lt480":
                processFiled(start, end, now, -480, -420);
                break;
            case "lt720":
                processFiled(start, end, now, -720, -480);
                break;
            case "lt1440":
                processFiled(start, end, now, -1440, -720);
                break;
            case "lt2880":
                processFiled(start, end, now, -2880, -1440);
                break;
            case "gt2880":
                processFiled(start, end, now, null, -2880);
                break;
        }
    }


    private void processFiled(Consumer<Long> start, Consumer<Long> end, DateTime now, Integer startOffsetMinute, Integer endOffsetMinute) {
        if (null != startOffsetMinute) {
            DateTime startTime = DateUtil.offset(now, DateField.MINUTE, startOffsetMinute);
            start.accept(startTime.getTime());
        }
        if (null != endOffsetMinute) {
            DateTime endTime = DateUtil.offset(now, DateField.MINUTE, endOffsetMinute);
            end.accept(endTime.getTime());
        }
    }

}
