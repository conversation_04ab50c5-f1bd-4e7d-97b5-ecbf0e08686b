package com.dt.portal.wms.web.client.rs.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.component.common.enums.rs.*;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.DateDescUtil;
import com.dt.platform.wms.client.rs.ISalesReturnOrderBizClient;
import com.dt.platform.wms.dto.rs.SalesReturnOrderBizDTO;
import com.dt.platform.wms.dto.rs.SalesReturnOrderDetailBizDTO;
import com.dt.platform.wms.dto.rs.SalesReturnOrderReceiveBizDTO;
import com.dt.platform.wms.param.rs.SalesReturnOrderBizParam;
import com.dt.portal.wms.web.client.rs.ISalesReturnOrderPortalClient;
import com.dt.portal.wms.web.vo.rs.SalesReturnOrderDetailVO;
import com.dt.portal.wms.web.vo.rs.SalesReturnOrderNode;
import com.dt.portal.wms.web.vo.rs.SalesReturnOrderReceiveDetailVO;
import com.dt.portal.wms.web.vo.rs.SalesReturnOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class ISalesReturnOrderPortalClientImpl implements ISalesReturnOrderPortalClient {

    @DubboReference
    private ISalesReturnOrderBizClient salesReturnOrderBizClient;

    @Override
    @SoulClient(path = "/rs/order/getPage", desc = "销退单分页")
    public Result<PageVO<SalesReturnOrderVO>> getPage(SalesReturnOrderBizParam salesReturnOrderBizParam) {
        // 处理查询条件
        if (null != salesReturnOrderBizParam) {
            if (StrUtil.isNotBlank(salesReturnOrderBizParam.getSearchBillNoListStr())) {
                salesReturnOrderBizParam.setSearchBillNoList(Arrays.asList(salesReturnOrderBizParam.getSearchBillNoListStr().split(StrUtil.COMMA)));
            }
            if (StrUtil.isNotBlank(salesReturnOrderBizParam.getSearchCarrierListStr())) {
                salesReturnOrderBizParam.setSearchCarrierList(Arrays.asList(salesReturnOrderBizParam.getSearchCarrierListStr().split(StrUtil.COMMA)));
            }
            if (null != salesReturnOrderBizParam.getSearchBillType()) {
                if (1 == salesReturnOrderBizParam.getSearchBillType()) {
                    salesReturnOrderBizParam.setPoNoList(salesReturnOrderBizParam.getSearchBillNoList());
                } else if (2 == salesReturnOrderBizParam.getSearchBillType()) {
                    salesReturnOrderBizParam.setAfterSalesTrackingNoList(salesReturnOrderBizParam.getSearchBillNoList());
                } else if (3 == salesReturnOrderBizParam.getSearchBillType()) {
                    salesReturnOrderBizParam.setGlobalNoList(salesReturnOrderBizParam.getSearchBillNoList());
                } else if (4 == salesReturnOrderBizParam.getSearchBillType()) {
                    salesReturnOrderBizParam.setAsnIdList(salesReturnOrderBizParam.getSearchBillNoList());
                } else if (5 == salesReturnOrderBizParam.getSearchBillType()) {
                    salesReturnOrderBizParam.setExpressNoList(salesReturnOrderBizParam.getSearchBillNoList());
                } else if (6 == salesReturnOrderBizParam.getSearchBillType()) {
                    salesReturnOrderBizParam.setReverseExpressNoList(salesReturnOrderBizParam.getSearchBillNoList());
                } else if (7 == salesReturnOrderBizParam.getSearchBillType()) {
                    salesReturnOrderBizParam.setLpNoList(salesReturnOrderBizParam.getSearchBillNoList());
                } else if (8 == salesReturnOrderBizParam.getSearchBillType()) {
                    salesReturnOrderBizParam.setMfcNoList(salesReturnOrderBizParam.getSearchBillNoList());
                }
            }
            if (null != salesReturnOrderBizParam.getSearchCarrierType()) {
                if (1 == salesReturnOrderBizParam.getSearchCarrierType()) {
                    salesReturnOrderBizParam.setCarrierCodeList(salesReturnOrderBizParam.getSearchCarrierList());
                } else if (2 == salesReturnOrderBizParam.getSearchCarrierType()) {
                    salesReturnOrderBizParam.setReverseCarrierCodeList(salesReturnOrderBizParam.getSearchCarrierList());
                }
            }
            if (null != salesReturnOrderBizParam.getSearchLocationType()) {
                if (1 == salesReturnOrderBizParam.getSearchLocationType()) {
                    salesReturnOrderBizParam.setLocationCodeList(salesReturnOrderBizParam.getSearchLocationList());
                } else if (2 == salesReturnOrderBizParam.getSearchLocationType()) {
                    salesReturnOrderBizParam.setContainerList(salesReturnOrderBizParam.getSearchLocationList());
                }
            }
        }
        Result<Page<SalesReturnOrderBizDTO>> page = salesReturnOrderBizClient.getPage(salesReturnOrderBizParam);
        PageVO<SalesReturnOrderVO> salesReturnOrderVOPageVO = ConverterUtil.convertPageVO(page.getData(), SalesReturnOrderVO.class);
        for (SalesReturnOrderVO salesReturnOrderVO : salesReturnOrderVOPageVO.getDataList()) {
            salesReturnOrderVO.setStatusDesc(RSOrderStatusEnum.desc(salesReturnOrderVO.getStatus()));
            salesReturnOrderVO.setTaxTypeDesc(TaxTypeEnum.desc(salesReturnOrderVO.getTaxType()));
            salesReturnOrderVO.setReturnTypeDesc(RSReturnTypeEnum.desc(salesReturnOrderVO.getReturnType()));

            salesReturnOrderVO.setDeclarationResultsDesc(RSSuccessEnum.desc(salesReturnOrderVO.getDeclarationResults()));
            salesReturnOrderVO.setInspectionResultDesc(RSInspectionEnum.desc(salesReturnOrderVO.getInspectionResult()));

            salesReturnOrderVO.setOverdueDesc(RSOverdueEnum.desc(salesReturnOrderVO.getOverdue()));
            salesReturnOrderVO.setShelfTypeDesc(RSShelfEnum.desc(salesReturnOrderVO.getShelfType()));
            salesReturnOrderVO.setOutTypeDesc(RSOutEnum.desc(salesReturnOrderVO.getOutType()));
            salesReturnOrderVO.setCreatedTimeDesc(DateDescUtil.normalTimeStr(salesReturnOrderVO.getCreatedTime()));
            salesReturnOrderVO.setHandoverTimeDesc(DateDescUtil.normalTimeStr(salesReturnOrderVO.getHandoverTime()));
            salesReturnOrderVO.setInspectionTimeDesc(DateDescUtil.normalTimeStr(salesReturnOrderVO.getInspectionTime()));
            salesReturnOrderVO.setDeclareTimeDesc(DateDescUtil.normalTimeStr(salesReturnOrderVO.getDeclareTime()));
            if (!RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderVO.getBillSource())) {
                salesReturnOrderVO.setAfterSalesTrackingNo(StrUtil.EMPTY);
                salesReturnOrderVO.setInspectionResultDesc(StrUtil.EMPTY);
            }
            salesReturnOrderVO.setBillSourceDesc(RSBillSourceEnum.desc(salesReturnOrderVO.getBillSource()));
            salesReturnOrderVO.setReturnReasonDesc(RSReturnReasonEnum.desc(salesReturnOrderVO.getReturnReason()));
            salesReturnOrderVO.setReturnAllowEntryDesc(ReturnAllowEntryEnum.desc(salesReturnOrderVO.getReturnAllowEntry()));
            extraInfo(salesReturnOrderVO);
        }
        return Result.success(salesReturnOrderVOPageVO);
    }

    private void extraInfo(SalesReturnOrderVO salesReturnOrderVO) {
        try {
            if (null == salesReturnOrderVO) return;
            if (StrUtil.isBlank(salesReturnOrderVO.getExtraJson())) return;
            JSONObject jsonObject = JSONUtil.parseObj(salesReturnOrderVO.getExtraJson());
            salesReturnOrderVO.setRejectReason(jsonObject.getStr("rejectReason", StrUtil.EMPTY));
            salesReturnOrderVO.setInstructionType(jsonObject.getStr(RSAdditionalOrderEnum.ADDITIONAL_ORDER_KEY, StrUtil.EMPTY));
            salesReturnOrderVO.setInstructionTypeDesc(RSAdditionalOrderEnum.desc(salesReturnOrderVO.getInstructionType()));
            salesReturnOrderVO.setAdditionalOrderDesc(RSAdditionalOrderEnum.desc(salesReturnOrderVO.getInstructionType()));
            if (jsonObject.containsKey("extendProps")) {

            }
            String extendProps = jsonObject.getStr("extendProps");
            JSONObject extend = JSONUtil.parseObj(extendProps);
            if (extend.containsKey("highRiskCustom")) {
                if ("1".equalsIgnoreCase(extend.getStr("highRiskCustom"))) {
                    salesReturnOrderVO.setHighRiskCustomDesc("是");
                } else {
                    salesReturnOrderVO.setHighRiskCustomDesc("否");
                }
            }
            if (extend.containsKey("highRiskPackage")) {
                if ("1".equalsIgnoreCase(extend.getStr("highRiskPackage"))) {
                    salesReturnOrderVO.setHighRiskPackageDesc("是");
                } else {
                    salesReturnOrderVO.setHighRiskPackageDesc("否");
                }
            }
            if (jsonObject.containsKey("rejectImageList")) {
                salesReturnOrderVO.setRejectImageList(jsonObject.getJSONArray("rejectImageList").toList(String.class));
            }
        } catch (Exception ignored) {
        }
    }

    @Override
    @SoulClient(path = "/rs/order/info", desc = "销退单详情")
    public Result<SalesReturnOrderVO> info(SalesReturnOrderBizParam salesReturnOrderBizParam) {
        Result<SalesReturnOrderBizDTO> info = salesReturnOrderBizClient.info(salesReturnOrderBizParam);
        SalesReturnOrderVO convert = ConverterUtil.convert(info.getData(), SalesReturnOrderVO.class);

        extraInfo(convert);

        if (convert == null) return Result.success(null);
        if (StrUtil.isBlank(convert.getExtraJson())) {
            convert.setNodeList(new ArrayList<>());
        } else {
            JSONObject jsonObject = JSONUtil.parseObj(convert.getExtraJson());
            if (jsonObject.containsKey("nodes")) {
                convert.setNodeList(jsonObject.getJSONArray("nodes").toList(SalesReturnOrderNode.class));
            }
        }
        convert.setTaxTypeDesc(TaxTypeEnum.desc(convert.getTaxType()));
        return Result.success(convert);
    }

    @Override
    @SoulClient(path = "/rs/order/detailList", desc = "销退单详情列表")
    public Result<List<SalesReturnOrderDetailVO>> detailList(SalesReturnOrderBizParam salesReturnOrderBizParam) {
        Result<List<SalesReturnOrderDetailBizDTO>> listResult = salesReturnOrderBizClient.detailList(salesReturnOrderBizParam);
        List<SalesReturnOrderDetailVO> salesReturnOrderDetailVOS = ConverterUtil.convertList(listResult.getData(), SalesReturnOrderDetailVO.class);
        for (SalesReturnOrderDetailVO salesReturnOrderDetailVO : salesReturnOrderDetailVOS) {
            if (StrUtil.isNotBlank(salesReturnOrderDetailVO.getSkuQuality())) {
                salesReturnOrderDetailVO.setSkuQualityDesc(SkuQualityEnum.desc(salesReturnOrderDetailVO.getSkuQuality()));
            }else {
                salesReturnOrderDetailVO.setSkuQualityDesc(InventoryTypeEnum.skuQualityDesc(salesReturnOrderDetailVO.getInventoryType()));
            }
            salesReturnOrderDetailVO.setInventoryTypeDesc(InventoryTypeEnum.desc(salesReturnOrderDetailVO.getInventoryType()));
            salesReturnOrderDetailVO.setAllowEntryDesc(RSAllowEntryEnum.desc(salesReturnOrderDetailVO.getAllowEntry()));
            salesReturnOrderDetailVO.setManufDateDesc(DateDescUtil.normalDateStr(salesReturnOrderDetailVO.getManufDate()));
            salesReturnOrderDetailVO.setExpireDateDesc(DateDescUtil.normalDateStr(salesReturnOrderDetailVO.getExpireDate()));
        }
        return Result.success(salesReturnOrderDetailVOS);
    }


    @Override
    @SoulClient(path = "/rs/order/modifyExpress", desc = "修改运单")
    public Result<Boolean> modifyExpress(SalesReturnOrderBizParam salesReturnOrderBizParam) {
        return salesReturnOrderBizClient.modifyExpress(salesReturnOrderBizParam);
    }

    @Override
    @SoulClient(path = "/rs/order/shelf", desc = "上架")
    public Result<Boolean> shelf(SalesReturnOrderBizParam salesReturnOrderBizParam) {
        return salesReturnOrderBizClient.shelf(salesReturnOrderBizParam);
    }

    @Override
    @SoulClient(path = "/rs/order/out", desc = "出库")
    public Result<Boolean> out(SalesReturnOrderBizParam salesReturnOrderBizParam) {
        return salesReturnOrderBizClient.out(salesReturnOrderBizParam);
    }

    @Override
    @SoulClient(path = "/rs/order/statusList", desc = "状态下拉")
    public Result<List<IdNameVO>> statusList() {
        return Result.success(IdNameVO.build(RSOrderStatusEnum.class, "code", "message"));
    }

    @SoulClient(path = "/rs/order/returnAllowEntry", desc = "货主标签下拉")
    @Override
    public Result<List<IdNameVO>> returnAllowEntry() {
        return Result.success(IdNameVO.build(ReturnAllowEntryEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/returnTypeList", desc = "退货类型下拉")
    public Result<List<IdNameVO>> returnTypeList() {
        return Result.success(IdNameVO.build(RSReturnTypeEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/returnReasonList", desc = "退货原因下拉")
    public Result<List<IdNameVO>> returnReasonList() {
        return Result.success(IdNameVO.build(RSReturnReasonEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/billSourceList", desc = "单据来源")
    public Result<List<IdNameVO>> billSourceList() {
        return Result.success(IdNameVO.build(RSBillSourceEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/passList", desc = "是否通过下拉")
    public Result<List<IdNameVO>> passList() {
        return Result.success(IdNameVO.build(RSInspectionEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/secondEntryList", desc = "是否二次入区")
    public Result<List<IdNameVO>> secondEntryList() {
        return Result.success(IdNameVO.build(RSSecondEntryEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/damageTypeList", desc = "次品类型")
    public Result<List<IdNameVO>> damageTypeList() {
        return Result.success(IdNameVO.build(RSDamageTypeEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/successList", desc = "是否成功下拉")
    public Result<List<IdNameVO>> successList() {
        return Result.success(IdNameVO.build(RSSuccessEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/inventoryTypeList", desc = "库存类型")
    public Result<List<IdNameVO>> inventoryTypeList() {
//        return Result.success(IdNameVO.build(InventoryTypeEnum.class, "code", "message"));
        return Result.success(InventoryTypeEnum.getNeedList());
    }

    @Override
    @SoulClient(path = "/rs/order/inventoryTypeListSelectAble", desc = "库存类型【可下拉选择】")
    public Result<List<IdNameVO>> inventoryTypeListSelectAble() {
        List<String> collect = Stream.of(InventoryTypeEnum.BLC, InventoryTypeEnum.CZC, InventoryTypeEnum.CC, InventoryTypeEnum.XQC).map(InventoryTypeEnum::getCode).collect(Collectors.toList());
        return Result.success(IdNameVO.build(InventoryTypeEnum.class, "code", "message").stream()
                .filter(it -> collect.contains(it.getId().toString())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/rs/order/overdueList", desc = "是否超期30天下拉")
    public Result<List<IdNameVO>> overdueList() {
        return Result.success(IdNameVO.build(RSOverdueEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/shelfList", desc = "上架类型下拉")
    public Result<List<IdNameVO>> shelfList() {
        return Result.success(IdNameVO.build(RSShelfEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/outList", desc = "出库类型下拉")
    public Result<List<IdNameVO>> outList() {
        return Result.success(IdNameVO.build(RSOutEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/rs/order/outListSecond", desc = "出库类型下拉")
    public Result<List<IdNameVO>> outListSecond() {
        List<IdNameVO> collect = IdNameVO.build(RSOutEnum.class, "code", "message").stream()
                .filter(it -> !RSOutEnum.BONDED.getCode().equals(it.getId()))
                .filter(it -> !RSOutEnum.ENTRY_INSPECTION.getCode().equals(it.getId()))
                .collect(Collectors.toList());
        return Result.success(collect);
    }

    @Override
    @SoulClient(path = "/rs/order/receiveDetail", desc = "销退单收货明细")
    public Result<List<SalesReturnOrderReceiveDetailVO>> getSalesOrderReceiveDetail(SalesReturnOrderBizParam salesReturnOrderBizParam) {
        if (salesReturnOrderBizParam == null || StringUtils.isEmpty(salesReturnOrderBizParam.getSalesReturnOrderNo())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        Result<List<SalesReturnOrderReceiveBizDTO>> salesOrderReceiveDetail = salesReturnOrderBizClient.getSalesOrderReceiveDetail(salesReturnOrderBizParam);
        if (CollectionUtils.isEmpty(salesOrderReceiveDetail.getData())) {
            return Result.success(new ArrayList<>());
        }
        List<SalesReturnOrderReceiveDetailVO> salesReturnOrderReceiveDetailVOList = new ArrayList<>();
        salesOrderReceiveDetail.getData().forEach(it -> {
            SalesReturnOrderReceiveDetailVO salesReturnOrderReceiveDetailVO = ConverterUtil.convert(it, SalesReturnOrderReceiveDetailVO.class);
            if (null == salesReturnOrderReceiveDetailVO) return;
            salesReturnOrderReceiveDetailVO.setSkuQualityDesc(SkuQualityEnum.getEnum(it.getSkuQuality()).getMessage());
            salesReturnOrderReceiveDetailVO.setManufDateDesc(ConverterUtil.convertVoTime(it.getManufDate(), "yyyy-MM-dd"));
            salesReturnOrderReceiveDetailVO.setExpireDateDesc(ConverterUtil.convertVoTime(it.getExpireDate(), "yyyy-MM-dd"));
            salesReturnOrderReceiveDetailVO.setCheckDateDesc(ConverterUtil.convertVoTime(it.getCheckDate()));
            salesReturnOrderReceiveDetailVO.setSecondEntryDesc(RSSecondEntryEnum.desc(it.getSecondEntry()));
            salesReturnOrderReceiveDetailVO.setSecurityCode(it.getTraceCode());
            salesReturnOrderReceiveDetailVO.setDamageTypeDesc(RSDamageTypeEnum.desc(it.getDamageType()));
            salesReturnOrderReceiveDetailVO.setInventoryTypeDesc(InventoryTypeEnum.desc(it.getInventoryType()));
            salesReturnOrderReceiveDetailVO.setSourceCode(it.getSourceCode());
            salesReturnOrderReceiveDetailVOList.add(salesReturnOrderReceiveDetailVO);
        });
        return Result.success(salesReturnOrderReceiveDetailVOList);
    }
}
