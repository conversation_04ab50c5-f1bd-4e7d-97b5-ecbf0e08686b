package com.dt.portal.wms.web.client.esShipment.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.ShipmentCustomsClearanceStatusEnum;
import com.dt.component.common.enums.bill.ShipmentCustomsClearanceTypeEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.tally.TallyStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.dto.ship.ShipmentAnalysisBillDTO;
import com.dt.domain.bill.param.ship.ShipmentAnalysisBillParam;
import com.dt.elasticsearch.wms.client.IShipmentOrderEsClient;
import com.dt.elasticsearch.wms.dto.ShipmentAnalysisDTO;
import com.dt.elasticsearch.wms.dto.ShipmentIndexDTO;
import com.dt.elasticsearch.wms.param.ShipmentAnalysisParam;
import com.dt.elasticsearch.wms.param.ShipmentEsParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.*;
import com.dt.platform.wms.dto.base.CarrierBizDTO;
import com.dt.platform.wms.dto.base.WarehouseBizDTO;
import com.dt.platform.wms.dto.cargo.CargoOwnerBizDTO;
import com.dt.platform.wms.dto.sale.SalePlatformBizDTO;
import com.dt.platform.wms.dto.shipment.ShipmentOrderBizDTO;
import com.dt.platform.wms.param.cargo.CargoOwnerBizParam;
import com.dt.platform.wms.param.carrier.CarrierBizParam;
import com.dt.platform.wms.param.sale.SalePlatformQueryBizParam;
import com.dt.platform.wms.param.shipment.ShipmentOrderBizParam;
import com.dt.portal.wms.web.client.ISalePlatformPortalClient;
import com.dt.portal.wms.web.client.esShipment.IShipmentAnalysisPortalClient;
import com.dt.portal.wms.web.vo.shipment.ShipmentOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.dt.platform.wms.constant.MaterialCalculateConstant.CALCULATE_MATERIAL_BAR_CODE;

/**
 * 出库单分析Portal客户端实现
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
@RefreshScope
public class ShipmentAnalysisPortalClientImpl implements IShipmentAnalysisPortalClient {

    @DubboReference
    private IShipmentOrderEsClient shipmentOrderEsClient;

    @DubboReference
    private IShipmentOrderBizClient shipmentOrderBizClient;

//    @Value("${shipment.analysis.page.from.es:false}")
//    private Boolean pageFromEs;

    @Value("${page-from-es:false}")
    private Boolean pageFromEs;

    @DubboReference
    private IWarehouseBizClient iWarehouseBizClient;

    @DubboReference
    private ICargoOwnerBizClient cargoOwnerBizClient;

    @DubboReference
    private ICarrierBizClient iCarrierBizClient;

    @DubboReference
    private ISalePlatformBizClient salePlatformBizClient;

    @DubboReference
    private IAreaBizClient areaBizClient;


    private static final List<String> sortList = Arrays.asList(
            "cargoCode", "businessType", "salePlatform", "packageStruct",
            "expOutStockDate_day", "expOutStockDate_hour", "createTime_day",
            "createTime_hour", "payDate_day", "payDate_hour", "outStockDate_day",
            "outStockDate_hour", "carrierCode",
//            "receiverProvName", "receiverCityName", "receiverAreaName",
            "createdOrderCount", "orderCount", "pretreatmentFailOrderCount",
            "pretreatmentCompleteOrderCount", "collectedOrderCount", "checkStartOrderCount",
            "checkCompleteOrderCount", "partialOutOrderCount", "outOrderCount", "skuQtySum",
            "interceptOrderCount", "cancelOrderCount", "shortageOutOrderCount"
    );

//    private static final List<String> shipPageList = Arrays.asList(
//            "createdOrderCount", "orderCount", "pretreatmentFailOrderCount",
//            "pretreatmentCompleteOrderCount", "collectedOrderCount", "checkStartOrderCount",
//            "checkCompleteOrderCount", "partialOutOrderCount", "outOrderCount",
//            "interceptOrderCount", "cancelOrderCount", "shortageOutOrderCount"
//    );

    /**
     * 校验创建时间参数
     * 创建时间必填，且跨度不能超过一个自然月
     *
     * @param param 查询参数
     * @return 校验结果，如果有错误返回错误结果，否则返回 null
     */
    private void validateCreateTimeParam(ShipmentAnalysisParam param) {

        // 校验创建时间参数
        if (param == null) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        // 校验创建时间参数
        if (param.getCreateTimeStart() == null || param.getCreateTimeEnd() == null) {
            throw new BaseException(BaseBizEnum.TIP, "创建时间范围必填");
        }
        //创建时间
        checkCreateTimeRange(param);

        //字段转换
        if (StringUtils.isNotEmpty(param.getField()) && sortList.contains(param.getField())) {
            param.setSortField(param.getField());
        }
        //字段转换
        if (StringUtils.isNotEmpty(param.getOrder()) && Objects.equals(param.getOrder(), "ascend")) {
            param.setSortOrder("ASC");
        }
        if (StringUtils.isNotEmpty(param.getOrder()) && Objects.equals(param.getOrder(), "descend")) {
            param.setSortOrder("DESC");
        }

        Map<String, List<String>> mapAreaMap = areaBizClient.handAreaCascade(param.getReceiverProvList(), param.getReceiverCityList(), param.getReceiverAreaList());
        if (!CollectionUtils.isEmpty(mapAreaMap)) {
            param.setReceiverProvList(mapAreaMap.getOrDefault("receiverProvList", null));
            param.setReceiverCityList(mapAreaMap.getOrDefault("receiverCityList", null));
            param.setReceiverAreaList(mapAreaMap.getOrDefault("receiverAreaList", null));
        }

    }

    /**
     * 校验创建时间范围
     * 这里可以添加具体的逻辑来检查创建时间范围是否符合业务要求
     *
     * @param param 查询参数
     */
    private void checkCreateTimeRange(ShipmentAnalysisParam param) {
        // 计算时间跨度（毫秒）
        long timeSpan = param.getCreateTimeEnd() - param.getCreateTimeStart();
        // 一个自然月的最大毫秒数（按31天计算）
        long maxMonthSpan = 3L * 24 * 60 * 60 * 1000;

        if (timeSpan <= 0) {
            throw new BaseException(BaseBizEnum.TIP, "创建时间结束时间必须大于开始时间");
        }

        if (timeSpan > maxMonthSpan) {
            throw new BaseException(BaseBizEnum.TIP, "创建时间范围不能超过3天");
        }
    }

    @Override
    @SoulClient(path = "/shipment/analysis/page", desc = "出库单分析查询（分页版）")
    public Result<PageVO<ShipmentAnalysisDTO>> getShipmentAnalysisPage(ShipmentAnalysisParam param) {
        log.info("出库单分析查询（分页版），参数：{}", param);

        // 校验创建时间参数
        validateCreateTimeParam(param);

//        RpcContextUtil.setWarehouseCode("DT_JYWMS1230");
//        if (pageFromEs) {
//            Result<PageVO<ShipmentAnalysisDTO>> shipmentAnalysisPage = shipmentOrderEsClient.getShipmentAnalysisPage(param);
//            if (!CollectionUtils.isEmpty(shipmentAnalysisPage.getData().getDataList())) {
//                buildVo(shipmentAnalysisPage.getData().getDataList());
//            }
//            return shipmentAnalysisPage;
//        } else {
        //无分组就是求和
        if (CollectionUtils.isEmpty(param.getAnalysisDimensions())) {
            Result<ShipmentAnalysisBillDTO> shipmentAnalysisSummary = shipmentOrderBizClient.getShipmentAnalysisSummary(ConverterUtil.convert(param, ShipmentAnalysisBillParam.class));
            PageVO<ShipmentAnalysisDTO> pageVO = new PageVO<>();
            pageVO.setDataList(new ArrayList<>());
            if (shipmentAnalysisSummary.getData() != null) {
                if (shipmentAnalysisSummary.getData().getOrderCount() == null || shipmentAnalysisSummary.getData().getOrderCount() == 0) {
                    PageVO.Page page = new PageVO.Page();
                    page.setPageSize(0L);
                    page.setCurrentPage(0L);
                    page.setTotalPage(0L);
                    page.setTotalCount(0L);
                    pageVO.setPage(page);
                    return Result.success();
                }
                //获取仓库名称
                WarehouseBizDTO warehouseBizDTO = iWarehouseBizClient.queryByCode(CurrentRouteHolder.getWarehouseCode()).getData();
                if (warehouseBizDTO != null) {
                    shipmentAnalysisSummary.getData().setWarehouseName(warehouseBizDTO.getName());
                }
                pageVO.setDataList(Collections.singletonList(ConverterUtil.convert(shipmentAnalysisSummary.getData(), ShipmentAnalysisDTO.class)));
            }
            PageVO.Page page = new PageVO.Page();
            page.setPageSize(20L);
            page.setCurrentPage(1L);
            page.setTotalPage(1L);
            page.setTotalCount(1L);
            pageVO.setPage(page);
            return Result.success(pageVO);
        }
        Result<Page<ShipmentAnalysisBillDTO>> shipmentAnalysisPage = shipmentOrderBizClient.getShipmentAnalysisPage(ConverterUtil.convert(param, ShipmentAnalysisBillParam.class));
        PageVO<ShipmentAnalysisDTO> shipmentAnalysisDTOPageVO = ConverterUtil.convertPageVO(shipmentAnalysisPage.getData(), ShipmentAnalysisDTO.class);
        if (!CollectionUtils.isEmpty(shipmentAnalysisDTOPageVO.getDataList())) {
            buildVo(shipmentAnalysisDTOPageVO.getDataList());
        }
        return Result.success(shipmentAnalysisDTOPageVO);
//        }
    }

    @Override
    @SoulClient(path = "/shipment/analysis/getShipPage", desc = "出库单分析查询（分页版）")
    public Result<PageVO<ShipmentOrderVO>> getShipmentAnalysisPageByShip(ShipmentAnalysisParam param) {
        if (param == null) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        if (pageFromEs) {
            ShipmentEsParam shipmentEsParam = buildShipmentEsParam(param);

            Result<PageVO<ShipmentIndexDTO>> pageVOResult = shipmentOrderEsClient.getPage(shipmentEsParam);

            PageVO<ShipmentIndexDTO> pageData = pageVOResult.getData();
            List<ShipmentOrderVO> voList = pageData.getDataList().stream().map(it -> {
                ShipmentOrderVO vo = new ShipmentOrderVO();
                BeanUtils.copyProperties(it, vo);
                vo.setId(it.getKey());
                vo.setSkuQty(it.getSkuQty().toString());
                vo.setExpOutStockDate(ConverterUtil.convertVoTime(it.getExpOutStockDate()));
                vo.setCreatedTime(ConverterUtil.convertVoTime(it.getCreatedTime()));
                vo.setUpdatedTime(ConverterUtil.convertVoTime(it.getUpdatedTime()));
                vo.setNotifyTime(ConverterUtil.convertVoTime(it.getNotifyTime()));
                vo.setSkuQuality(it.getSkuQuality());
                vo.setPickCompleteSkuDate(ConverterUtil.convertVoTime(it.getPickCompleteSkuDate()));
                vo.setFirstPackOutStockDate(ConverterUtil.convertVoTime(it.getFirstPackOutStockDate()));
                vo.setCheckStartDate(ConverterUtil.convertVoTime(it.getCheckStartDate()));
                vo.setPlaceTradeOrderDate(ConverterUtil.convertVoTime(it.getPlaceTradeOrderDate()));
                vo.setCheckCompleteDate(ConverterUtil.convertVoTime(it.getCheckCompleteDate()));
                vo.setInterceptCancelDate(ConverterUtil.convertVoTime(it.getInterceptCancelDate()));
                vo.setOutSkuQty(it.getOutSkuQty().toString());
                vo.setOutStockDate(ConverterUtil.convertVoTime(it.getOutStockDate()));
                vo.setPayDate(ConverterUtil.convertVoTime(it.getPayDate()));
                vo.setExpShipTimeDesc(ConverterUtil.convertVoTime(it.getExpShipTime()));
                vo.setWeight(it.getWeight());
                vo.setPickSkuDate(ConverterUtil.convertVoTime(it.getPickSkuDate()));
                vo.setReceiverArea(it.getReceiverAreaName());
                vo.setReceiverCity(it.getReceiverCityName());
                vo.setReceiverProv(it.getReceiverProvName());
                vo.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(it.getPackageStruct()).getDesc());
                //解析订单tag
                vo.setOrderTagName("");
                if (!org.springframework.util.StringUtils.isEmpty(vo.getOrderTag())) {
                    Set<OrderTagEnum> orderTagEnumList = OrderTagEnum.NumToEnum(vo.getOrderTag());
                    if (!CollectionUtils.isEmpty(orderTagEnumList)) {
                        String orderTagName = orderTagEnumList.stream().map(OrderTagEnum::getDesc).collect(Collectors.joining("|"));
                        vo.setOrderTagName(orderTagName);
                    } else {
                        vo.setOrderTagName("");
                    }
                }
                return vo;
            }).collect(Collectors.toList());
            // 组装VO Page数据
            PageVO.Page page = new PageVO.Page();
            page.setPageSize(pageData.getPage().getPageSize());
            page.setCurrentPage(pageData.getPage().getCurrentPage());
            page.setTotalPage(pageData.getPage().getTotalPage());
            page.setTotalCount(pageData.getPage().getTotalCount());
            PageVO<ShipmentOrderVO> pageVO = new PageVO<>();
            pageVO.setPage(page);
            pageVO.setDataList(voList);
            Result<PageVO<ShipmentOrderVO>> result = new Result<>();
            result.setCode(pageVOResult.getCode());
            result.setMessage(pageVOResult.getMessage());
            result.setData(pageVO);
            return Result.success(pageVO);
        } else {
            ShipmentEsParam shipmentEsParam = buildShipmentEsParam(param);

            ShipmentOrderBizParam shipmentOrderBizParam = ConverterUtil.convert(shipmentEsParam, ShipmentOrderBizParam.class);

            Result<Page<ShipmentOrderBizDTO>> pageVOResult = shipmentOrderBizClient.getPage(shipmentOrderBizParam);

            Page<ShipmentOrderBizDTO> pageData = pageVOResult.getData();

            List<ShipmentOrderVO> voList = new ArrayList<>();

            if (!CollectionUtils.isEmpty(pageData.getRecords())) {
                //获取货主集合
                CargoOwnerBizParam cargoOwnerBizParam = new CargoOwnerBizParam();
                cargoOwnerBizParam.setCodeList(pageData.getRecords().stream().map(ShipmentOrderBizDTO::getCargoCode).distinct().collect(Collectors.toList()));
                List<CargoOwnerBizDTO> cargoOwnerBizDTOList = cargoOwnerBizClient.queryList(cargoOwnerBizParam).getData();
                //获取快递公司集合
                CarrierBizParam carrierBizParam = new CarrierBizParam();
                carrierBizParam.setCodeList(pageData.getRecords().stream().map(ShipmentOrderBizDTO::getCarrierCode).distinct().collect(Collectors.toList()));
                List<CarrierBizDTO> carrierBizDTOList = iCarrierBizClient.getList(carrierBizParam).getData();
                //获取平台集合
                SalePlatformQueryBizParam platformQueryBizParam = new SalePlatformQueryBizParam();
                platformQueryBizParam.setCodeList(pageData.getRecords().stream().map(ShipmentOrderBizDTO::getSalePlatform).distinct().collect(Collectors.toList()));
                List<SalePlatformBizDTO> platformBizDTOList = salePlatformBizClient.getList(platformQueryBizParam).getData();

                voList = pageData.getRecords().stream().map(it -> {
                    ShipmentOrderVO vo = new ShipmentOrderVO();
                    BeanUtils.copyProperties(it, vo);
                    vo.setId(it.getKey());
                    vo.setSkuQty(it.getSkuQty().toString());
                    vo.setExpOutStockDate(ConverterUtil.convertVoTime(it.getExpOutStockDate()));
                    vo.setCreatedTime(ConverterUtil.convertVoTime(it.getCreatedTime()));
                    vo.setUpdatedTime(ConverterUtil.convertVoTime(it.getUpdatedTime()));
                    vo.setNotifyTime(ConverterUtil.convertVoTime(it.getNotifyTime()));
                    vo.setSkuQuality(it.getSkuQuality());
                    vo.setPickCompleteSkuDate(ConverterUtil.convertVoTime(it.getPickCompleteSkuDate()));
                    vo.setFirstPackOutStockDate(ConverterUtil.convertVoTime(it.getFirstPackOutStockDate()));
                    vo.setCheckStartDate(ConverterUtil.convertVoTime(it.getCheckStartDate()));
                    vo.setPlaceTradeOrderDate(ConverterUtil.convertVoTime(it.getPlaceTradeOrderDate()));
                    vo.setCheckCompleteDate(ConverterUtil.convertVoTime(it.getCheckCompleteDate()));
                    vo.setInterceptCancelDate(ConverterUtil.convertVoTime(it.getInterceptCancelDate()));
                    vo.setOutSkuQty(it.getOutSkuQty().toString());
                    vo.setOutStockDate(ConverterUtil.convertVoTime(it.getOutStockDate()));
                    vo.setPayDate(ConverterUtil.convertVoTime(it.getPayDate()));
                    vo.setExpShipTimeDesc(ConverterUtil.convertVoTime(it.getExpShipTime()));
                    vo.setWeight(it.getWeight());
                    vo.setPickSkuDate(ConverterUtil.convertVoTime(it.getPickSkuDate()));
                    vo.setReceiverArea(it.getReceiverAreaName());
                    vo.setReceiverCity(it.getReceiverCityName());
                    vo.setReceiverProv(it.getReceiverProvName());
                    vo.setStatusName(ShipmentOrderEnum.STATUS.findOrderStatus(vo.getStatus()).getDesc());
                    vo.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(it.getPackageStruct()).getDesc());
                    //解析订单tag
                    vo.setOrderTagName("");
                    if (!org.springframework.util.StringUtils.isEmpty(vo.getOrderTag())) {
                        Set<OrderTagEnum> orderTagEnumList = OrderTagEnum.NumToEnum(vo.getOrderTag());
                        if (!CollectionUtils.isEmpty(orderTagEnumList)) {
                            String orderTagName = orderTagEnumList.stream().map(OrderTagEnum::getDesc).collect(Collectors.joining("|"));
                            vo.setOrderTagName(orderTagName);
                        } else {
                            vo.setOrderTagName("");
                        }
                    }

                    if (!CollectionUtils.isEmpty(cargoOwnerBizDTOList)) {
                        CargoOwnerBizDTO cargoOwnerBizDTO = cargoOwnerBizDTOList.stream().filter(b -> b.getCode().equals(vo.getCargoCode())).findAny().orElse(null);
                        vo.setCargoName(cargoOwnerBizDTO == null ? "" : cargoOwnerBizDTO.getName());
                    }
                    if (!CollectionUtils.isEmpty(carrierBizDTOList)) {
                        CarrierBizDTO carrierBizDTO = carrierBizDTOList.stream().filter(b -> b.getCode().equals(vo.getCarrierCode())).findAny().orElse(null);
                        vo.setCarrierName(carrierBizDTO == null ? "" : carrierBizDTO.getName());
                    }
                    if (!CollectionUtils.isEmpty(platformBizDTOList)) {
                        SalePlatformBizDTO salePlatformBizDTO = platformBizDTOList.stream().filter(b -> b.getCode().equals(vo.getSalePlatform())).findAny().orElse(null);
                        vo.setSalePlatformName(salePlatformBizDTO == null ? "" : salePlatformBizDTO.getName());
                    }

                    return vo;
                }).collect(Collectors.toList());
            }
            // 组装VO Page数据
            PageVO.Page page = new PageVO.Page();
            page.setPageSize(pageData.getSize());
            page.setCurrentPage(pageData.getCurrent());
            page.setTotalPage(pageData.getPages());
            page.setTotalCount(pageData.getTotal());
            PageVO<ShipmentOrderVO> pageVO = new PageVO<>();
            pageVO.setPage(page);
            pageVO.setDataList(voList);
            Result<PageVO<ShipmentOrderVO>> result = new Result<>();
            result.setCode(pageVOResult.getCode());
            result.setMessage(pageVOResult.getMessage());
            result.setData(pageVO);
            return Result.success(pageVO);
        }
    }

//    /**
//     * 构建ShipmentOrderBizParam
//     *
//     * @param param 查询参数
//     * @return ShipmentEsParam
//     */
//    private ShipmentOrderBizParam buildShipmentOrderBizParam(ShipmentAnalysisParam param) {
//        ShipmentOrderBizParam shipmentOrderBizParam = ConverterUtil.convert(param, ShipmentOrderBizParam.class);
//        shipmentOrderBizParam.setReceiverProv(param.getReceiverProvName());
//        shipmentOrderBizParam.setReceiverCity(param.getReceiverCityName());
//        shipmentOrderBizParam.setReceiverArea(param.getReceiverAreaName());
//
//        //创建时间
//        checkCreateTimeRange(param);
//        //必要参数
//        checkAnalysisDimensionsByShipPage(param);
//
//        //--------------------状态码转换------------------
//        //创建状态
//        if (Objects.equals(param.getColumnKey(), "createdOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode());
//        }
//        //预处理失败
//        if (Objects.equals(param.getColumnKey(), "pretreatmentFailOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_FAIL_STATUS.getCode());
//        }
//        //预处理完成
//        if (Objects.equals(param.getColumnKey(), "pretreatmentCompleteOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
//        }
//        //已汇总
//        if (Objects.equals(param.getColumnKey(), "collectedOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode());
//        }
//        //复核开始
//        if (Objects.equals(param.getColumnKey(), "checkStartOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode());
//        }
//        //复核完成
//        if (Objects.equals(param.getColumnKey(), "checkCompleteOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode());
//        }
//        //部分出库
//        if (Objects.equals(param.getColumnKey(), "partialOutOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
//        }
//        //已出库
//        if (Objects.equals(param.getColumnKey(), "outOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
//        }
//        //拦截
//        if (Objects.equals(param.getColumnKey(), "interceptOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.INTERCEPT_STATUS.getCode());
//        }
//        //取消
//        if (Objects.equals(param.getColumnKey(), "cancelOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.CANCEL_STATUS.getCode());
//        }
//        //缺货出库
//        if (Objects.equals(param.getColumnKey(), "shortageOutOrderCount")) {
//            shipmentOrderBizParam.setStatus(ShipmentOrderEnum.STATUS.OUT_OF_OUT_STOCK_STATUS.getCode());
//        }
//        //--------------------状态码转换------------------
//
//        //创建时间
//        if (param.getCreateTimeStart() != null && param.getCreateTimeEnd() != null) {
//            shipmentOrderBizParam.setCreatedTimeStart(param.getCreateTimeStart());
//            shipmentOrderBizParam.setCreatedTimeEnd(param.getCreateTimeEnd());
//        }
//        //商品数
//        if (param.getSkuQtyMax() != null) {
//            shipmentOrderBizParam.setEndSkuCount(param.getSkuQtyMax());
//        }
//        if (param.getSkuQtyMin() != null) {
//            shipmentOrderBizParam.setStartSkuCount(param.getSkuQtyMin());
//        }
//        //订单数
//        if (param.getSkuTypeQtyMax() != null) {
//            shipmentOrderBizParam.setEndSkuTypeCount(param.getSkuTypeQtyMax());
//        }
//        if (param.getSkuTypeQtyMin() != null) {
//            shipmentOrderBizParam.setStartSkuTypeCount(param.getSkuTypeQtyMin());
//        }
//        //重量
//        if (param.getWeightMin() != null) {
//            shipmentOrderBizParam.setWeightStart(BigDecimal.valueOf(param.getWeightMin()));
//        }
//        if (param.getWeightMax() != null) {
//            shipmentOrderBizParam.setWeightEnd(BigDecimal.valueOf(param.getWeightMax()));
//        }
//        //-----------------付款时间------------------
//        if (param.getPayDateStart() != null) {
//            shipmentOrderBizParam.setStartPayTime(param.getPayDateStart());
//        }
//        if (param.getPayDateEnd() != null) {
//            shipmentOrderBizParam.setEndPayTime(param.getPayDateEnd());
//        }
//        //按天
//        if (StringUtils.isNotEmpty(param.getPayDate_day())) {
//            long time = DateUtil.parse(param.getPayDate_day(), "yyyy-MM-dd").getTime();
//            shipmentOrderBizParam.setStartPayTime(DateUtil.beginOfDay(new Date(time)).getTime());
//            shipmentOrderBizParam.setEndPayTime(DateUtil.endOfDay(new Date(time)).getTime());
//        }
//        //按小时
//        if (StringUtils.isNotEmpty(param.getPayDate_hour())) {
//            long time = DateUtil.parse(param.getPayDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
//            shipmentOrderBizParam.setStartPayTime(time);
//            shipmentOrderBizParam.setEndPayTime(time + 3600L * 1000);
//        }
//        //-----------------付款时间------------------
//
//        //-----------------创建时间------------------
//        //按天
//        if (StringUtils.isNotEmpty(param.getCreatedTime_day())) {
//            long time = DateUtil.parse(param.getCreatedTime_day(), "yyyy-MM-dd").getTime();
//            shipmentOrderBizParam.setCreatedTimeStart(DateUtil.beginOfDay(new Date(time)).getTime());
//            shipmentOrderBizParam.setCreatedTimeEnd(DateUtil.endOfDay(new Date(time)).getTime());
//        }
//        //按小时
//        if (StringUtils.isNotEmpty(param.getCreatedTime_hour())) {
//            long time = DateUtil.parse(param.getCreatedTime_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
//            shipmentOrderBizParam.setCreatedTimeStart(time);
//            shipmentOrderBizParam.setCreatedTimeEnd(time + 3600L * 1000);
//        }
//        //-----------------创建时间------------------
//
//        //-----------------出库时间------------------
//        if (param.getOutStockDateStart() != null) {
//            shipmentOrderBizParam.setStartOutStockDate(param.getOutStockDateStart());
//        }
//        if (param.getOutStockDateEnd() != null) {
//            shipmentOrderBizParam.setEndOutStockDate(param.getOutStockDateEnd());
//        }
//        //按天
//        if (StringUtils.isNotEmpty(param.getOutStockDate_day())) {
//            long time = DateUtil.parse(param.getOutStockDate_day(), "yyyy-MM-dd").getTime();
//            shipmentOrderBizParam.setStartOutStockDate(DateUtil.beginOfDay(new Date(time)).getTime());
//            shipmentOrderBizParam.setEndOutStockDate(DateUtil.endOfDay(new Date(time)).getTime());
//        }
//        //按小时
//        if (StringUtils.isNotEmpty(param.getOutStockDate_hour())) {
//            long time = DateUtil.parse(param.getOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
//            shipmentOrderBizParam.setStartOutStockDate(time);
//            shipmentOrderBizParam.setEndOutStockDate(time + 3600L * 1000);
//        }
//        //-----------------出库时间------------------
//
//        //-----------------预计出库时间------------------
//        if (param.getExpOutStockDateStart() != null) {
//            shipmentOrderBizParam.setExpShipTimeStart(param.getExpOutStockDateStart());
//        }
//        if (param.getExpOutStockDateEnd() != null) {
//            shipmentOrderBizParam.setExpShipTimeEnd(param.getExpOutStockDateEnd());
//        }
//        //按天
//        if (StringUtils.isNotEmpty(param.getExpOutStockDate_day())) {
//            long time = DateUtil.parse(param.getExpOutStockDate_day(), "yyyy-MM-dd").getTime();
//            shipmentOrderBizParam.setExpShipTimeStart(DateUtil.beginOfDay(new Date(time)).getTime());
//            shipmentOrderBizParam.setExpShipTimeEnd(DateUtil.endOfDay(new Date(time)).getTime());
//        }
//        //按小时
//        if (StringUtils.isNotEmpty(param.getExpOutStockDate_hour())) {
//            long time = DateUtil.parse(param.getExpOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
//            shipmentOrderBizParam.setExpShipTimeStart(time);
//            shipmentOrderBizParam.setExpShipTimeEnd(time + 3600L * 1000);
//        }
//        //-----------------预计出库时间------------------
//
//        return shipmentOrderBizParam;
//    }

    /**
     * 构建出库单分析查询参数
     *
     * @param param
     * @return
     */
    private ShipmentEsParam buildShipmentEsParam(ShipmentAnalysisParam param) {
        ShipmentEsParam shipmentEsParam = ConverterUtil.convert(param, ShipmentEsParam.class);
        shipmentEsParam.setReceiverProvName(param.getReceiverProvName());
        shipmentEsParam.setReceiverCityName(param.getReceiverCityName());
        shipmentEsParam.setReceiverAreaName(param.getReceiverAreaName());

        //创建时间
        checkCreateTimeRange(param);
        //必要参数
        checkAnalysisDimensionsByShipPage(param);

        //--------------------状态码转换------------------
        //创建状态
        if (Objects.equals(param.getColumnKey(), "createdOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode());
        }
        //预处理失败
        if (Objects.equals(param.getColumnKey(), "pretreatmentFailOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_FAIL_STATUS.getCode());
        }
        //预处理完成
        if (Objects.equals(param.getColumnKey(), "pretreatmentCompleteOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        }
        //已汇总
        if (Objects.equals(param.getColumnKey(), "collectedOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode());
        }
        //复核开始
        if (Objects.equals(param.getColumnKey(), "checkStartOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode());
        }
        //复核完成
        if (Objects.equals(param.getColumnKey(), "checkCompleteOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode());
        }
        //部分出库
        if (Objects.equals(param.getColumnKey(), "partialOutOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
        }
        //已出库
        if (Objects.equals(param.getColumnKey(), "outOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
        }
        //拦截
        if (Objects.equals(param.getColumnKey(), "interceptOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.INTERCEPT_STATUS.getCode());
        }
        //取消
        if (Objects.equals(param.getColumnKey(), "cancelOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.CANCEL_STATUS.getCode());
        }
        //缺货出库
        if (Objects.equals(param.getColumnKey(), "shortageOutOrderCount")) {
            shipmentEsParam.setStatus(ShipmentOrderEnum.STATUS.OUT_OF_OUT_STOCK_STATUS.getCode());
        }
        //--------------------状态码转换------------------

        //创建时间
        if (param.getCreateTimeStart() != null && param.getCreateTimeEnd() != null) {
            shipmentEsParam.setCreatedTimeStart(param.getCreateTimeStart());
            shipmentEsParam.setCreatedTimeEnd(param.getCreateTimeEnd());
        }
        //商品数
        if (param.getSkuQtyMax() != null) {
            shipmentEsParam.setEndSkuCount(param.getSkuQtyMax());
        }
        if (param.getSkuQtyMin() != null) {
            shipmentEsParam.setStartSkuCount(param.getSkuQtyMin());
        }
        //订单数
        if (param.getSkuTypeQtyMax() != null) {
            shipmentEsParam.setEndSkuTypeCount(param.getSkuTypeQtyMax());
        }
        if (param.getSkuTypeQtyMin() != null) {
            shipmentEsParam.setStartSkuTypeCount(param.getSkuTypeQtyMin());
        }
        //重量
        if (param.getWeightMin() != null) {
            shipmentEsParam.setWeightStart(BigDecimal.valueOf(param.getWeightMin()));
        }
        if (param.getWeightMax() != null) {
            shipmentEsParam.setWeightEnd(BigDecimal.valueOf(param.getWeightMax()));
        }
        //-----------------付款时间------------------
        if (param.getPayDateStart() != null) {
            shipmentEsParam.setStartPayTime(param.getPayDateStart());
        }
        if (param.getPayDateEnd() != null) {
            shipmentEsParam.setEndPayTime(param.getPayDateEnd());
        }
        //按天
        if (StringUtils.isNotEmpty(param.getPayDate_day())) {
            long time = DateUtil.parse(param.getPayDate_day(), "yyyy-MM-dd").getTime();
            shipmentEsParam.setStartPayTime(DateUtil.beginOfDay(new Date(time)).getTime());
            shipmentEsParam.setEndPayTime(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getPayDate_hour())) {
            long time = DateUtil.parse(param.getPayDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            shipmentEsParam.setStartPayTime(time);
            shipmentEsParam.setEndPayTime(time + 3600L * 1000);
        }
        //-----------------付款时间------------------

        //-----------------创建时间------------------
        //按天
        if (StringUtils.isNotEmpty(param.getCreatedTime_day())) {
            long time = DateUtil.parse(param.getCreatedTime_day(), "yyyy-MM-dd").getTime();
            shipmentEsParam.setCreatedTimeStart(DateUtil.beginOfDay(new Date(time)).getTime());
            shipmentEsParam.setCreatedTimeEnd(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getCreatedTime_hour())) {
            long time = DateUtil.parse(param.getCreatedTime_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            shipmentEsParam.setCreatedTimeStart(time);
            shipmentEsParam.setCreatedTimeEnd(time + 3600L * 1000);
        }
        //-----------------创建时间------------------

        //-----------------出库时间------------------
        if (param.getOutStockDateStart() != null) {
            shipmentEsParam.setStartOutStockDate(param.getOutStockDateStart());
        }
        if (param.getOutStockDateEnd() != null) {
            shipmentEsParam.setEndOutStockDate(param.getOutStockDateEnd());
        }
        //按天
        if (StringUtils.isNotEmpty(param.getOutStockDate_day())) {
            long time = DateUtil.parse(param.getOutStockDate_day(), "yyyy-MM-dd").getTime();
            shipmentEsParam.setStartOutStockDate(DateUtil.beginOfDay(new Date(time)).getTime());
            shipmentEsParam.setEndOutStockDate(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getOutStockDate_hour())) {
            long time = DateUtil.parse(param.getOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            shipmentEsParam.setStartOutStockDate(time);
            shipmentEsParam.setEndOutStockDate(time + 3600L * 1000);
        }
        //-----------------出库时间------------------

        //-----------------预计出库时间------------------
        if (param.getExpOutStockDateStart() != null) {
            shipmentEsParam.setStartExpOutStockDate(param.getExpOutStockDateStart());
        }
        if (param.getExpOutStockDateEnd() != null) {
            shipmentEsParam.setEndExpOutStockDate(param.getExpOutStockDateEnd());
        }
        //按天
        if (StringUtils.isNotEmpty(param.getExpOutStockDate_day())) {
            long time = DateUtil.parse(param.getExpOutStockDate_day(), "yyyy-MM-dd").getTime();
            shipmentEsParam.setStartExpOutStockDate(DateUtil.beginOfDay(new Date(time)).getTime());
            shipmentEsParam.setEndExpOutStockDate(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (StringUtils.isNotEmpty(param.getExpOutStockDate_hour())) {
            long time = DateUtil.parse(param.getExpOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            shipmentEsParam.setStartExpOutStockDate(time);
            shipmentEsParam.setEndExpOutStockDate(time + 3600L * 1000);
        }
        //-----------------预计出库时间------------------

        return shipmentEsParam;
    }

    /**
     * 校验分析维度
     * 这里可以添加具体的逻辑来检查分析维度是否符合业务要求
     *
     * @param param 查询参数
     */
    private void checkAnalysisDimensionsByShipPage(ShipmentAnalysisParam param) {
        if (StringUtils.isEmpty(param.getColumnKey())) {
            throw new BaseException(BaseBizEnum.TIP, "数量列必要参数不能为空");
        }

        if (!CollectionUtils.isEmpty(param.getAnalysisDimensions())) {
            param.getAnalysisDimensions().forEach(it -> {
                //货主维度
                if (Objects.equals(it, "cargoCode") && StringUtils.isEmpty(param.getCargoCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "货主编码不能为空");
                }
                //业务类型维度
                if (Objects.equals(it, "businessType") && StringUtils.isEmpty(param.getBusinessType())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务类型不能为空");
                }
                //销售平台维度
                if (Objects.equals(it, "salePlatform") && StringUtils.isEmpty(param.getSalePlatform())) {
                    throw new BaseException(BaseBizEnum.TIP, "销售平台不能为空");
                }
                //包裹类型维度
                if (Objects.equals(it, "packageStruct") && StringUtils.isEmpty(param.getPackageStruct())) {
                    throw new BaseException(BaseBizEnum.TIP, "包裹类型不能为空");
                }
                //预计出库时间维度
                if (Objects.equals(it, "expOutStockDate_day") || Objects.equals(it, "expOutStockDate_hour")) {
                    if (param.getExpOutStockDate_day() == null && param.getExpOutStockDate_hour() == null) {
                        throw new BaseException(BaseBizEnum.TIP, "预计出库时间不能为空");
                    }
                }
                //创建时间维度
                if (Objects.equals(it, "createTime_day") || Objects.equals(it, "createTime_hour")) {
                    if (param.getCreatedTime_day() == null && param.getCreatedTime_hour() == null) {
                        throw new BaseException(BaseBizEnum.TIP, "创建时间不能为空");
                    }
                }
                //付款时间维度
                if (Objects.equals(it, "payDate_day") || Objects.equals(it, "payDate_hour")) {
                    if (param.getPayDate_day() == null && param.getPayDate_hour() == null) {
                        throw new BaseException(BaseBizEnum.TIP, "付款时间不能为空");
                    }
                }
                //出库时间维度
                if (Objects.equals(it, "outStockDate_day") || Objects.equals(it, "outStockDate_hour")) {
                    if (param.getOutStockDate_day() == null && param.getOutStockDate_hour() == null) {
                        throw new BaseException(BaseBizEnum.TIP, "出库时间不能为空");
                    }
                }
                //快递公司维度
                if (Objects.equals(it, "carrierCode") && StringUtils.isEmpty(param.getCarrierCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "快递公司编码不能为空");
                }
                //省份维度
                if (Objects.equals(it, "receiverProvName") && StringUtils.isEmpty(param.getReceiverProvName())) {
                    throw new BaseException(BaseBizEnum.TIP, "省份不能为空");
                }
                //城市维度
                if (Objects.equals(it, "receiverCityName") && StringUtils.isEmpty(param.getReceiverCityName())) {
                    throw new BaseException(BaseBizEnum.TIP, "城市不能为空");
                }
                //区/县维度
                if (Objects.equals(it, "receiverAreaName") && StringUtils.isEmpty(param.getReceiverAreaName())) {
                    throw new BaseException(BaseBizEnum.TIP, "区/县不能为空");
                }

            });
        }
    }

    /**
     * 构建VO
     *
     * @param dataList
     */
    private void buildVo(List<ShipmentAnalysisDTO> dataList) {
        //获取仓库名称
        WarehouseBizDTO warehouseBizDTO = iWarehouseBizClient.queryByCode(CurrentRouteHolder.getWarehouseCode()).getData();
        //获取货主集合
        CargoOwnerBizParam cargoOwnerBizParam = new CargoOwnerBizParam();
        cargoOwnerBizParam.setCodeList(dataList.stream().map(ShipmentAnalysisDTO::getCargoCode).distinct().collect(Collectors.toList()));
        List<CargoOwnerBizDTO> cargoOwnerBizDTOList = cargoOwnerBizClient.queryList(cargoOwnerBizParam).getData();
        //获取快递公司集合
        CarrierBizParam carrierBizParam = new CarrierBizParam();
        carrierBizParam.setCodeList(dataList.stream().map(ShipmentAnalysisDTO::getCarrierCode).distinct().collect(Collectors.toList()));
        List<CarrierBizDTO> carrierBizDTOList = iCarrierBizClient.getList(carrierBizParam).getData();
        //获取平台集合
        SalePlatformQueryBizParam platformQueryBizParam = new SalePlatformQueryBizParam();
        platformQueryBizParam.setCodeList(dataList.stream().map(ShipmentAnalysisDTO::getSalePlatform).distinct().collect(Collectors.toList()));
        List<SalePlatformBizDTO> platformBizDTOList = salePlatformBizClient.getList(platformQueryBizParam).getData();
        final int[] i = {1};
        dataList.forEach(a -> {
            a.setId(i[0]);
            i[0]++;
            if (warehouseBizDTO != null) {
                a.setWarehouseName(warehouseBizDTO.getName());
            }
            if (!CollectionUtils.isEmpty(cargoOwnerBizDTOList)) {
                CargoOwnerBizDTO cargoOwnerBizDTO = cargoOwnerBizDTOList.stream().filter(b -> b.getCode().equals(a.getCargoCode())).findAny().orElse(null);
                a.setCargoName(cargoOwnerBizDTO == null ? "" : cargoOwnerBizDTO.getName());
            }
            if (!CollectionUtils.isEmpty(carrierBizDTOList)) {
                CarrierBizDTO carrierBizDTO = carrierBizDTOList.stream().filter(b -> b.getCode().equals(a.getCarrierCode())).findAny().orElse(null);
                a.setCarrierName(carrierBizDTO == null ? "" : carrierBizDTO.getName());
            }
            if (!CollectionUtils.isEmpty(platformBizDTOList)) {
                SalePlatformBizDTO salePlatformBizDTO = platformBizDTOList.stream().filter(b -> b.getCode().equals(a.getSalePlatform())).findAny().orElse(null);
                a.setSalePlatformName(salePlatformBizDTO == null ? "" : salePlatformBizDTO.getName());
            }
            if (a.getPackageStruct() != null) {
                a.setPackageStructName(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(a.getPackageStruct()).getDesc());
            }
            //创建小时
            if (StringUtils.isNotEmpty(a.getCreatedTime_hour())) {
                a.setCreatedTime_hour(a.getCreatedTime_hour() + ":00:00");
            }
            //预计出库时间小时
            if (StringUtils.isNotEmpty(a.getExpOutStockDate_hour())) {
                a.setExpOutStockDate_hour(a.getExpOutStockDate_hour() + ":00:00");
            }
            //出库时间小时
            if (StringUtils.isNotEmpty(a.getOutStockDate_hour())) {
                a.setOutStockDate_hour(a.getOutStockDate_hour() + ":00:00");
            }
            //付款时间小时
            if (StringUtils.isNotEmpty(a.getPayDate_hour())) {
                a.setPayDate_hour(a.getPayDate_hour() + ":00:00");
            }
        });
    }

    @Override
    @SoulClient(path = "/shipment/analysis/summary", desc = "出库单分析汇总查询")
    public Result<ShipmentAnalysisDTO> getShipmentAnalysisSummary(ShipmentAnalysisParam param) {
        log.info("出库单分析汇总查询，参数：{}", param);

        // 校验创建时间参数
        validateCreateTimeParam(param);

//        RpcContextUtil.setWarehouseCode("DT_JYWMS1230");
//        if (pageFromEs) {
//            return shipmentOrderEsClient.getShipmentAnalysisSummary(param);
//        } else {
        Result<ShipmentAnalysisBillDTO> shipmentAnalysisSummary = shipmentOrderBizClient.getShipmentAnalysisSummary(ConverterUtil.convert(param, ShipmentAnalysisBillParam.class));
        return Result.success(ConverterUtil.convert(shipmentAnalysisSummary.getData(), ShipmentAnalysisDTO.class));
//        }
    }
}
