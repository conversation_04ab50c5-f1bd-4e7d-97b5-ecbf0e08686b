package com.dt.portal.wms.web.client.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.encrypt.annotation.DataSecurity;
import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.FromSourceEnum;
import com.dt.component.common.enums.NotifyStatusEnum;
import com.dt.component.common.enums.bill.*;
import com.dt.component.common.enums.cargo.CargoOpenEffectEnum;
import com.dt.component.common.enums.pkg.PackageUnitEnum;
import com.dt.component.common.enums.rs.OpAbnormalTypeEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.tally.TallyStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.dto.AllocationOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.domain.bill.dto.ShipmentOrderMaterialDTO;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.elasticsearch.wms.client.IShipmentOrderEsClient;
import com.dt.elasticsearch.wms.dto.ShipmentIndexDTO;
import com.dt.elasticsearch.wms.param.ShipmentEsParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.*;
import com.dt.platform.wms.dto.base.AllocationRuleBizDTO;
import com.dt.platform.wms.dto.base.AreaBizDTO;
import com.dt.platform.wms.dto.base.CarrierBizDTO;
import com.dt.platform.wms.dto.base.TurnoverRuleBizDTO;
import com.dt.platform.wms.dto.box.AnalysisCommitBizDTO;
import com.dt.platform.wms.dto.box.AnalysisErrorBizDTO;
import com.dt.platform.wms.dto.cargo.CargoOwnerBizDTO;
import com.dt.platform.wms.dto.sale.SalePlatformBizDTO;
import com.dt.platform.wms.dto.shipment.ShipmentOrderBizDTO;
import com.dt.platform.wms.dto.shipment.ShipmentOrderBizDTO2;
import com.dt.platform.wms.dto.shipment.ShipmentOrderDetailBizDTO;
import com.dt.platform.wms.dto.shipment.ShipmentOrderLogBizDTO;
import com.dt.platform.wms.dto.sku.SkuLotBizDTO;
import com.dt.platform.wms.form.Shipment.OrderTagOperationParam;
import com.dt.platform.wms.form.Shipment.ShipmentAddForm;
import com.dt.platform.wms.form.Shipment.ShipmentModifyForm;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.IdParam;
import com.dt.platform.wms.param.shipment.*;
import com.dt.platform.wms.param.sku.SkuLotBizParam;
import com.dt.portal.wms.web.client.IShipmentOrderPortClient;
import com.dt.portal.wms.web.vo.box.AnalysisErrorVO;
import com.dt.portal.wms.web.vo.shipment.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dt.platform.wms.constant.MaterialCalculateConstant.CALCULATE_MATERIAL_BAR_CODE;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
@RefreshScope
public class ShipmentOrderPortClient implements IShipmentOrderPortClient {
    @DubboReference
    private IShipmentOrderBizClient shipmentOrderClient;
    @DubboReference
    ICarrierBizClient icarrierBizClient;
    @DubboReference
    ICargoOwnerBizClient iCargoOwnerBizClient;
    @DubboReference
    private IAreaBizClient areaBizClient;
    @DubboReference
    IAllocationRuleClient iAllocationRuleClient;
    @DubboReference
    TurnoverRuleClient turnoverRuleClient;
    @DubboReference
    private ISalePlatformBizClient salePlatformBizClient;

    @DubboReference
    private IAllocationOrderClient allocationOrderClient;

    @DubboReference
    private IDecimalPlaceClient decimalPlaceClient;
    @DubboReference
    private ISkuLotBizClient skuLotBizClient;
    @DubboReference
    private IShipmentOrderDetailBizClient shipmentOrderDetailBizClient;

    @DubboReference
    private IShipmentOrderEsClient shipmentOrderEsClient;

    @Value("${page-from-es:false}")
    private Boolean pageFromEs;

    @Override
    @SoulClient(path = "/shipment/decrypt", desc = "出库单单独解密")
    public Result<ShipmentOrderVO> decryptForShipment(ShipmentOrderBizParam bizParam) {
        if (ObjectUtils.isEmpty(bizParam) || StringUtils.isEmpty(bizParam.getShipmentOrderCode())) {
            throw new BaseException(BaseBizEnum.TIP, "出库单号不能为空");
        }
        Result<ShipmentOrderBizDTO> result = shipmentOrderClient.decryptForShipment(bizParam);
        ShipmentOrderVO vo = ConverterUtil.convert(result.getData(), ShipmentOrderVO.class);
        getProvCityArea(result.getData(), vo);
        return Result.success(vo);
    }

    @Override
    @SoulClient(path = "/preSaleType/item", desc = "预售类型下拉列表")
    public Result<List<IdNameVO>> preSaleTypeItem() {
        List<IdNameVO> idNameVOS = IdNameVO.build(ShipmentPreSaleTypeEnum.class, "code", "message");
        return Result.success(idNameVOS);
    }

    @Override
    @SoulClient(path = "/shipment/status", desc = "订单状态")
    public Result<List<IdNameVO>> findListStatus() {
        List<IdNameVO> result = Arrays.stream(ShipmentOrderEnum.STATUS.values()).map((ShipmentOrderEnum.STATUS s) -> {
            IdNameVO vo = new IdNameVO();
            vo.setName(s.getDesc());
            vo.setId(s.getCode());
            return vo;
        }).collect(Collectors.toList());
        return Result.success(result);
    }

    @Override
    @SoulClient(path = "/shipment/business-type", desc = "业务类型")
    public Result<List<IdNameVO>> findListBusinessType() {
        List<IdNameVO> result =
                Arrays.stream(ShipmentOrderEnum.BUSSINESS_TYPE.values()).map((ShipmentOrderEnum.BUSSINESS_TYPE s) -> {
                    IdNameVO vo = new IdNameVO();
                    vo.setName(s.name());
                    vo.setId(s.name());
                    return vo;
                }).collect(Collectors.toList());
        return Result.success(result);
    }

    @Override
    @SoulClient(path = "/shipment/clearanceStatus", desc = "清关状态")
    public Result<List<IdNameVO>> clearanceStatus() {
        List<IdNameVO> result =
                Arrays.stream(ShipmentCustomsClearanceStatusEnum.values()).map((ShipmentCustomsClearanceStatusEnum s) -> {
                    IdNameVO vo = new IdNameVO();
                    vo.setName(s.getName());
                    vo.setId(s.getCode());
                    return vo;
                }).collect(Collectors.toList());
        return Result.success(result);
    }

    @Override
    @SoulClient(path = "/shipment/clearanceType", desc = "清关类型")
    public Result<List<IdNameVO>> clearanceType() {
        List<IdNameVO> result =
                Arrays.stream(ShipmentCustomsClearanceTypeEnum.values()).map((ShipmentCustomsClearanceTypeEnum s) -> {
                    IdNameVO vo = new IdNameVO();
                    vo.setName(s.getName());
                    vo.setId(s.getCode());
                    return vo;
                }).collect(Collectors.toList());
        return Result.success(result);

    }

    @Override
    @SoulClient(path = "/shipment/order-type", desc = "单据类型")
    public Result<List<IdNameVO>> findListOrderType() {
        List<IdNameVO> result = Arrays.stream(ShipmentOrderEnum.ORDER_TYPE.values())
                .filter(s -> !StringUtils.isEmpty(s.getCode())).map((ShipmentOrderEnum.ORDER_TYPE s) -> {
                    IdNameVO vo = new IdNameVO();
                    vo.setName(s.getDesc());
                    vo.setId(s.getCode());
                    return vo;
                }).collect(Collectors.toList());
        return Result.success(result);
    }

    @Override
    @SoulClient(path = "/shipment/order-notify-status", desc = "订单回传状态")
    public Result<List<IdNameVO>> findListOrderNotifyStatus() {
        List<IdNameVO> result = IdNameVO.build(NotifyStatusEnum.class, "code", "message");
        return Result.success(result);
    }

    @Override
    @SoulClient(path = "/shipment/order-sku-type", desc = "订单品种类型,包裹结构类型")
    public Result<List<IdNameVO>> findListOrderSkuType() {
        List<IdNameVO> result = Arrays.stream(ShipmentOrderEnum.PACKAGE_STRUCT.values())
                .filter(s -> !StringUtils.isEmpty(s.getCode())).map((ShipmentOrderEnum.PACKAGE_STRUCT s) -> {
                    IdNameVO vo = new IdNameVO();
                    vo.setName(s.getDesc());
                    vo.setId(s.getCode());
                    return vo;
                }).collect(Collectors.toList());
        return Result.success(result);
    }

    private ShipmentOrderVO getShipmentOrderVO(ShipmentOrderBizDTO bizDTO) {
        ShipmentOrderVO vo = new ShipmentOrderVO();
        BeanUtils.copyProperties(bizDTO, vo);
        vo.setSkuQty((bizDTO.getSkuQty() == null ? BigDecimal.ZERO : bizDTO.getSkuQty())
                .setScale(bizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
        vo.setOutSkuQty((bizDTO.getOutSkuQty() == null ? BigDecimal.ZERO : bizDTO.getOutSkuQty())
                .setScale(bizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
        vo.setCheckCompleteDate(ConverterUtil.convertVoTime(bizDTO.getCheckCompleteDate()));
        vo.setCheckStartDate(ConverterUtil.convertVoTime(bizDTO.getCheckStartDate()));
        vo.setInterceptCancelDate(ConverterUtil.convertVoTime(bizDTO.getInterceptCancelDate()));
        vo.setOutStockDate(ConverterUtil.convertVoTime(bizDTO.getOutStockDate()));
        vo.setCreatedTime(ConverterUtil.convertVoTime(bizDTO.getCreatedTime()));
        vo.setUpdatedTime(ConverterUtil.convertVoTime(bizDTO.getUpdatedTime()));
        vo.setFirstPackOutStockDate(ConverterUtil.convertVoTime(bizDTO.getFirstPackOutStockDate()));
        vo.setPickCompleteSkuDate(ConverterUtil.convertVoTime(bizDTO.getPickCompleteSkuDate()));
        vo.setPickSkuDate(ConverterUtil.convertVoTime(bizDTO.getPickSkuDate()));
        ShipmentCustomsClearanceStatusEnum.getByCode(bizDTO.getCustomsClearanceStatus()).ifPresent(it -> vo.setCustomsClearanceStatusDesc(it.getName()));
        ShipmentCustomsClearanceTypeEnum.getByCode(bizDTO.getCustomsClearanceType()).ifPresent(it -> vo.setCustomsClearanceTypeDesc(it.getName()));
        if (!StringUtils.isEmpty(vo.getSalePlatform())) {
            CodeParam codeParam = new CodeParam();
            codeParam.setCode(vo.getSalePlatform());
            Result<SalePlatformBizDTO> result = salePlatformBizClient.get(codeParam);
            if (result.getData() != null) {
                vo.setSalePlatformName(result.getData().getName());
            }
        } else {
            vo.setSalePlatformName(vo.getSalePlatform());
        }
        if (bizDTO.getNotifyStatus() != null) {
            NotifyStatusEnum notifyStatusEnum = NotifyStatusEnum.fromCode(bizDTO.getNotifyStatus());
            vo.setNotifyStatusName(notifyStatusEnum.getMessage());
        }
        vo.setNotifyTime(ConverterUtil.convertVoTime(bizDTO.getNotifyTime()));
        vo.setPayDate(ConverterUtil.convertVoTime(bizDTO.getPayDate()));
        vo.setPlaceTradeOrderDate(ConverterUtil.convertVoTime(bizDTO.getPlaceTradeOrderDate()));
        vo.setExpOutStockDate(ConverterUtil.convertVoTime(bizDTO.getExpOutStockDate()));
        vo.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(bizDTO.getPackageStruct()).getDesc());
//        vo.setOrderType(ShipmentOrderEnum.ORDER_TYPE.findEnumDesc(bizDTO.getOrderType()).getDesc());
        vo.setOrderTypeName(ShipmentOrderEnum.ORDER_TYPE.findEnumDesc(bizDTO.getOrderType()).getDesc());
        ShipmentOrderEnum.STATUS status = ShipmentOrderEnum.STATUS.findOrderStatus(bizDTO.getStatus());

        if (!StringUtils.isEmpty(vo.getFromSource())) {
            vo.setFromSourceName(FromSourceEnum.getEnum(vo.getFromSource()).message());
        }

        String statusDesc = status == null ? "" : status.getDesc();
        vo.setStatusName(statusDesc);
        Optional<PretreatmentStatusEnum> pretreatmentStatusEnumOptional = Optional.ofNullable(PretreatmentStatusEnum.getEnum(bizDTO.getPretreatmentStatus()));
        vo.setPretreatmentStatusName(pretreatmentStatusEnumOptional.isPresent() ? pretreatmentStatusEnumOptional.get().getName() : "");
        if (!StringUtils.isEmpty(bizDTO.getCarrierCode())) {
            CarrierBizDTO carrierBizDTO = icarrierBizClient.queryByCode(bizDTO.getCarrierCode()).getData();
            if (carrierBizDTO != null) {
                vo.setCarrierName(carrierBizDTO.getName());
            }
        }
        getProvCityArea(bizDTO, vo);
        if (!StringUtils.isEmpty(bizDTO.getCargoCode())) {
            CargoOwnerBizDTO cargoOwnerBizDTO = iCargoOwnerBizClient.queryByCode(bizDTO.getCargoCode()).getData();
            if (cargoOwnerBizDTO != null) {
                vo.setCargoName(cargoOwnerBizDTO.getName());
            } else {
                vo.setCargoName("");
            }
        } else {
            vo.setCargoName("");
        }
        List<ShipmentOrderMaterialDTO> shipmentMaterial =
                shipmentOrderClient.getShipmentMaterial(bizDTO.getShipmentOrderCode());
        if (CollectionUtil.isNotEmpty(shipmentMaterial)) {
            vo.setMaterialUpcCode(shipmentMaterial.stream().map(ShipmentOrderMaterialDTO::getRecPackUpcCode).distinct()
                    .collect(Collectors.joining(StrUtil.COMMA)));
        } else {
            vo.setMaterialUpcCode("");
        }

        // 预售类型 和 最晚出库时间
        if (bizDTO.getExpShipTime() != null) {
            vo.setExpShipTimeDesc(ConverterUtil.convertVoTime(bizDTO.getExpShipTime()));
        } else {
            vo.setExpShipTimeDesc("");
        }
        if (bizDTO.getPreSaleType() != null) {
            vo.setPreSaleTypeDesc(
                    ShipmentPreSaleTypeEnum.fromCode(bizDTO.getPreSaleType()).getMessage());
        } else {
            vo.setPreSaleTypeDesc("");
        }
        CargoOwnerBizDTO cargoOwnerDTO = iCargoOwnerBizClient.queryByCode(bizDTO.getCargoCode()).getData();
        if (cargoOwnerDTO != null) {
            vo.setOpenEffect(cargoOwnerDTO.getOpenEffect() == null
                    ? CargoOpenEffectEnum.CLOSE.getValue() : cargoOwnerDTO.getOpenEffect());
        } else {
            vo.setOpenEffect(CargoOpenEffectEnum.CLOSE.getValue());
        }
        //解析订单tag
        vo.setOrderTagName("");
        if (!StringUtils.isEmpty(vo.getOrderTag())) {
            Set<OrderTagEnum> orderTagEnumList = OrderTagEnum.NumToEnum(vo.getOrderTag());
            if (!CollectionUtils.isEmpty(orderTagEnumList)) {
                String orderTagName = orderTagEnumList.stream().map(OrderTagEnum::getDesc).collect(Collectors.joining("|"));
                vo.setOrderTagName(orderTagName);
            } else {
                vo.setOrderTagName("");
            }
        }
        //理货按钮 B2B + 已汇总+4pl
        vo.setIsTally(false);
        if (vo.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())
                && vo.getStatus().equalsIgnoreCase(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode())) {
            vo.setIsTally(true);
        }

        if (StrUtil.isNotBlank(vo.getExtraJson())) {
            JSONObject jsonObject = JSONUtil.parseObj(vo.getExtraJson());
            vo.setCalculateMaterialCode(jsonObject.getStr(CALCULATE_MATERIAL_BAR_CODE));
            vo.setOriginalPlatform(jsonObject.getStr("originalPlatform"));
        }

        //出库订单的订单标记上有【淘天】且外部业务类型为【退供出库、报废出库、跨货主调拨出库
        vo.setZeroOutStock(false);
        if (taoTianZeroOutStock(bizDTO.getBusinessType(), bizDTO.getOrderTag(), bizDTO.getExtraJson())) {
            vo.setZeroOutStock(true);
        }
        if (vo.getOrderTag() > 0 && OrderTagEnum.NumToEnum(vo.getOrderTag()).contains(OrderTagEnum.MERGE_CUSTOMS_CLEARANCE)) {
            vo.setMergeCustomsClearanceTip("合并清关");
        }
        vo.setTallyStatusDesc("");
        vo.setTallyStatus("");

        return vo;
    }

    private void getProvCityArea(ShipmentOrderBizDTO bizDTO, ShipmentOrderVO vo) {
        if (!StringUtils.isEmpty(bizDTO.getReceiverArea())) {
            AreaBizDTO areaBizDTO = areaBizClient.get(bizDTO.getReceiverArea()).getData();
            if (areaBizDTO != null) {
                vo.setReceiverArea(areaBizDTO.getAreaName());
            }
        } else {
            vo.setReceiverArea(bizDTO.getReceiverAreaName());
        }

        if (!StringUtils.isEmpty(bizDTO.getReceiverCity())) {
            AreaBizDTO areaBizDTO = areaBizClient.get(bizDTO.getReceiverCity()).getData();
            if (areaBizDTO != null) {
                vo.setReceiverCity(areaBizDTO.getAreaName());
            }
        } else {
            vo.setReceiverCity(bizDTO.getReceiverCityName());
        }
        if (!StringUtils.isEmpty(bizDTO.getReceiverProv())) {
            AreaBizDTO areaBizDTO = areaBizClient.get(bizDTO.getReceiverProv()).getData();
            if (areaBizDTO != null) {
                vo.setReceiverProv(areaBizDTO.getAreaName());
            }
        } else {
            vo.setReceiverProv(bizDTO.getReceiverProvName());
        }
    }


    @Override
    public Result<PageVO<ShipmentOrderVO>> queryPageFromES(ShipmentOrderBizParam param) {
        ShipmentEsParam esParam = new ShipmentEsParam();
        BeanUtils.copyProperties(param, esParam);
        Result<PageVO<ShipmentIndexDTO>> pageVOResult = shipmentOrderEsClient.getPage(esParam);
        PageVO<ShipmentIndexDTO> pageData = pageVOResult.getData();
        List<ShipmentOrderVO> voList = pageData.getDataList().stream().map(it -> {
            ShipmentOrderVO vo = new ShipmentOrderVO();
            BeanUtils.copyProperties(it, vo);
            vo.setId(it.getKey());
            vo.setSkuQty(it.getSkuQty().toString());
            vo.setExpOutStockDate(ConverterUtil.convertVoTime(it.getExpOutStockDate()));
            vo.setCreatedTime(ConverterUtil.convertVoTime(it.getCreatedTime()));
            vo.setUpdatedTime(ConverterUtil.convertVoTime(it.getUpdatedTime()));
            vo.setNotifyTime(ConverterUtil.convertVoTime(it.getNotifyTime()));
            vo.setSkuQuality(it.getSkuQuality());
            vo.setPickCompleteSkuDate(ConverterUtil.convertVoTime(it.getPickCompleteSkuDate()));
            vo.setFirstPackOutStockDate(ConverterUtil.convertVoTime(it.getFirstPackOutStockDate()));
            vo.setCheckStartDate(ConverterUtil.convertVoTime(it.getCheckStartDate()));
            vo.setPlaceTradeOrderDate(ConverterUtil.convertVoTime(it.getPlaceTradeOrderDate()));
            vo.setCheckCompleteDate(ConverterUtil.convertVoTime(it.getCheckCompleteDate()));
            vo.setInterceptCancelDate(ConverterUtil.convertVoTime(it.getInterceptCancelDate()));
            vo.setOutSkuQty(it.getOutSkuQty().toString());
            vo.setOutStockDate(ConverterUtil.convertVoTime(it.getOutStockDate()));
            vo.setPayDate(ConverterUtil.convertVoTime(it.getPayDate()));
            vo.setExpShipTimeDesc(ConverterUtil.convertVoTime(it.getExpShipTime()));
            vo.setWeight(it.getWeight());
            vo.setPickSkuDate(ConverterUtil.convertVoTime(it.getPickSkuDate()));
            vo.setReceiverArea(it.getReceiverAreaName());
            vo.setReceiverCity(it.getReceiverCityName());
            vo.setReceiverProv(it.getReceiverProvName());
            Arrays.stream(ShipmentCustomsClearanceTypeEnum.values())
                    .filter(itt -> itt.getCode().equals(it.getCustomsClearanceType()))
                    .findFirst().ifPresent(itt -> vo.setCustomsClearanceTypeDesc(itt.getName()));
            Arrays.stream(ShipmentCustomsClearanceStatusEnum.values())
                    .filter(itt -> itt.getCode().equals(it.getCustomsClearanceStatus()))
                    .findFirst().ifPresent(itt -> vo.setCustomsClearanceStatusDesc(itt.getName()));
            vo.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(it.getPackageStruct()).getDesc());
            //解析订单tag
            vo.setOrderTagName("");
            if (!StringUtils.isEmpty(vo.getOrderTag())) {
                Set<OrderTagEnum> orderTagEnumList = OrderTagEnum.NumToEnum(vo.getOrderTag());
                if (!CollectionUtils.isEmpty(orderTagEnumList)) {
                    String orderTagName = orderTagEnumList.stream().map(OrderTagEnum::getDesc).collect(Collectors.joining("|"));
                    vo.setOrderTagName(orderTagName);
                } else {
                    vo.setOrderTagName("");
                }
            }
            //理货按钮 B2B + 已汇总+4pl
            vo.setIsTally(false);
            if (vo.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())
                    && vo.getStatus().equalsIgnoreCase(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode())) {
                vo.setIsTally(true);
            }
            if (StrUtil.isNotBlank(vo.getExtraJson())) {
                JSONObject jsonObject = JSONUtil.parseObj(vo.getExtraJson());
                vo.setCalculateMaterialCode(jsonObject.getStr(CALCULATE_MATERIAL_BAR_CODE));
                vo.setOriginalPlatform(jsonObject.getStr("originalPlatform"));
            }
            vo.setSkuQty((it.getSkuQty() == null ? BigDecimal.ZERO : it.getSkuQty())
                    .setScale(0, RoundingMode.FLOOR).toString());
            vo.setOutSkuQty((it.getOutSkuQty() == null ? BigDecimal.ZERO : it.getOutSkuQty())
                    .setScale(0, RoundingMode.FLOOR).toString());
            //出库订单的订单标记上有【淘天】且外部业务类型为【退供出库、报废出库、跨货主调拨出库
            vo.setZeroOutStock(false);
            if (taoTianZeroOutStock(it.getBusinessType(), it.getOrderTag(), it.getExtraJson())) {
                vo.setZeroOutStock(true);
            }
            if (vo.getOrderTag() > 0 && OrderTagEnum.NumToEnum(vo.getOrderTag()).contains(OrderTagEnum.MERGE_CUSTOMS_CLEARANCE)) {
                vo.setMergeCustomsClearanceTip("合并清关");
            }
            //展示收货公司
            if (!StringUtils.isEmpty(vo.getExtraJson()) && JSONUtil.isJson(vo.getExtraJson())) {
                JSONObject jsonObject = JSONUtil.parseObj(vo.getExtraJson());
                if (jsonObject.containsKey("receivingCompany")) {
                    vo.setReceivingCompany(jsonObject.getStr("receivingCompany", ""));
                }
            }

            return vo;
        }).collect(Collectors.toList());
        //展示加上理货状态码
        if (!CollectionUtils.isEmpty(voList)) {
            List<String> shipmentOrderCodeList = voList.stream()
                    .filter(a -> Objects.equals(a.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString()))
                    .map(ShipmentOrderVO::getShipmentOrderCode).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(shipmentOrderCodeList)) {
                Map<String, String> tallyStatusMap = shipmentOrderClient.queryShipTallyStatus(shipmentOrderCodeList);
                voList.forEach(it -> {
                    it.setTallyStatus("");
                    it.setTallyStatusDesc("");
                    if (!CollectionUtils.isEmpty(tallyStatusMap) && tallyStatusMap.containsKey(it.getShipmentOrderCode())) {
                        it.setTallyStatus(tallyStatusMap.get(it.getShipmentOrderCode()));
                        it.setTallyStatusDesc(TallyStatusEnum.desc(tallyStatusMap.get(it.getShipmentOrderCode())));
                    }
                });
            }
        }
        // 组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(pageData.getPage().getPageSize());
        page.setCurrentPage(pageData.getPage().getCurrentPage());
        page.setTotalPage(pageData.getPage().getTotalPage());
        page.setTotalCount(pageData.getPage().getTotalCount());
        PageVO<ShipmentOrderVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);
        Result<PageVO<ShipmentOrderVO>> result = new Result<>();
        result.setCode(pageVOResult.getCode());
        result.setMessage(pageVOResult.getMessage());
        result.setData(pageVO);
        return result;
    }

    /**
     * @param businessType
     * @param orderTag
     * @param extraJson
     * @return boolean
     * <AUTHOR>
     * @describe: 出库订单的订单标记上有【淘天】且外部业务类型为【退供出库、报废出库、跨货主调拨出库
     * <p>
     * ● PTCK=退供出库单
     * ● DBCK=调拨出库
     * ● BAFCK=报废出库
     * ● KHZCK=跨货主调拨出库
     * @date 2024/11/27 13:18
     */
    private boolean taoTianZeroOutStock(String businessType, Integer orderTag, String extraJson) {
        if (Objects.equals(businessType, ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())
                && OrderTagEnum.NumToEnum(orderTag).contains(OrderTagEnum.TAOTAIN)
                && !StringUtils.isEmpty(extraJson)) {
            JSONObject jsonObject = JSONUtil.parseObj(extraJson);
            if (jsonObject.containsKey("orderType") && !StringUtils.isEmpty(jsonObject.getStr("orderType"))) {
                String orderType = jsonObject.getStr("orderType");
                if (Arrays.asList("PTCK", "BAFCK", "KHZCK").contains(orderType)) {
                    return true;
                }
            }

        }
        return false;
    }

    /**
     * 查询出库单
     *
     * @param
     * @return
     */
    @Override
    @SoulClient(path = "/shipment/page", desc = "出库分页")
    public Result<PageVO<ShipmentOrderVO>> queryPage(ShipmentOrderBizParam param) {
        //todo ---------------------省市区级联 多选
        Map<String, List<String>> mapAreaMap = areaBizClient.handAreaCascade(param.getReceiverProvList(), param.getReceiverCityList(), param.getReceiverAreaList());
        if (!CollectionUtils.isEmpty(mapAreaMap)) {
            param.setReceiverProvList(mapAreaMap.getOrDefault("receiverProvList", null));
            param.setReceiverCityList(mapAreaMap.getOrDefault("receiverCityList", null));
            param.setReceiverAreaList(mapAreaMap.getOrDefault("receiverAreaList", null));
        }
        //todo ---------------------省市区级联 多选
        if (Boolean.TRUE.equals(pageFromEs)) {
            return queryPageFromES(param);
        }
        log.info("[op:ShipmentOrderClientImpl:queryPage] param= {}", JSON.toJSONString(param));
        if (param != null && !StringUtils.isEmpty(param.getOrderTagList())) {
            param.setOrderTag(OrderTagEnum.queryParamListToInteger(param.getOrderTagList()));
        }
        Result<Page<ShipmentOrderBizDTO>> pageResult = shipmentOrderClient.getPage(param);
        Page<ShipmentOrderBizDTO> pageBizPage = pageResult.getData();
        List<ShipmentOrderBizDTO> shipmentOrderBizList = pageBizPage.getRecords();
        List<ShipmentOrderVO> voList = new ArrayList<>();
        // 格式化数据
        if (!CollectionUtils.isEmpty(shipmentOrderBizList)) {
            voList = shipmentOrderBizList.stream().flatMap(a -> Stream.of(getShipmentOrderVO(a)))
                    .collect(Collectors.toList());
            //展示加上理货状态码
            if (!CollectionUtils.isEmpty(voList)) {
                List<String> shipmentOrderCodeList = voList.stream()
                        .filter(a -> Objects.equals(a.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString()))
                        .map(ShipmentOrderVO::getShipmentOrderCode).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(shipmentOrderCodeList)) {
                    Map<String, String> tallyStatusMap = shipmentOrderClient.queryShipTallyStatus(shipmentOrderCodeList);
                    voList.forEach(it -> {
                        it.setTallyStatus("");
                        it.setTallyStatusDesc("");
                        if (!CollectionUtils.isEmpty(tallyStatusMap) && tallyStatusMap.containsKey(it.getShipmentOrderCode())) {
                            it.setTallyStatus(tallyStatusMap.get(it.getShipmentOrderCode()));
                            it.setTallyStatusDesc(TallyStatusEnum.desc(tallyStatusMap.get(it.getShipmentOrderCode())));
                        }
                    });
                }
            }

        }
        // 组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(pageBizPage.getSize());
        page.setCurrentPage(pageBizPage.getCurrent());
        page.setTotalPage(pageBizPage.getPages());
        page.setTotalCount(pageBizPage.getTotal());
        PageVO<ShipmentOrderVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);
        Result<PageVO<ShipmentOrderVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);
        return result;
    }

    @Override
    @SoulClient(path = "/shipment/getDetailPage", desc = "出库明细分页")
    public Result<PageVO<ShipmentOrderDetailVO>> getDetailPage(ShipmentOrderBizParam shipmentOrderBizParam) {
        if (StringUtils.isEmpty(shipmentOrderBizParam) || StringUtils.isEmpty(shipmentOrderBizParam.getShipmentOrderCode())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        Result<Page<ShipmentOrderDetailBizDTO>> result = shipmentOrderClient.getPageDetail(shipmentOrderBizParam);
        PageVO<ShipmentOrderDetailVO> page = ConverterUtil.convertPageVO(result.getData(), ShipmentOrderDetailVO.class);

        List<ShipmentOrderDetailVO> shipmentOrderDetailVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(result.getData().getRecords())) {

            List<ShipmentOrderDetailBizDTO> shipmentOrderDetailBizDTOList = result.getData().getRecords();
//            List<ShipmentOrderDetailBizDTO> detailBizDTOArrayList = new ArrayList<>();
//            List<ShipmentOrderDetailBizDTO> detailBizDTOList = shipmentOrderDetailBizDTOList.stream()
//                    .filter(s -> s.getRefSplitDetailId() == null || s.getRefSplitDetailId() <= 0)
//                    .sorted(Comparator.comparing(ShipmentOrderDetailBizDTO::getId, Comparator.naturalOrder()))
//                    .collect(Collectors.toList());
//
//            detailBizDTOList.forEach(detailBizDTO -> {
//                detailBizDTOArrayList.add(detailBizDTO);
//                detailBizDTOArrayList.addAll(shipmentOrderDetailBizDTOList.stream().filter(ss -> ss.getRefSplitDetailId().equals(detailBizDTO.getId()))
//                        .sorted(Comparator.comparing(ShipmentOrderDetailBizDTO::getId, Comparator.naturalOrder()))
//                        .collect(Collectors.toList()));
//            });

            int i = 0;
            for (ShipmentOrderDetailBizDTO shipmentOrderDetailBizDTO : shipmentOrderDetailBizDTOList) {
                ShipmentOrderDetailVO shipmentOrderDetailVO = new ShipmentOrderDetailVO();
                BeanUtils.copyProperties(shipmentOrderDetailBizDTO, shipmentOrderDetailVO);
                if (StrUtil.isNotBlank(shipmentOrderDetailBizDTO.getExtraJson()) && JSONUtil.isJson(shipmentOrderDetailBizDTO.getExtraJson())) {
                    shipmentOrderDetailVO.setRemark(JSONUtil.parseObj(shipmentOrderDetailBizDTO.getExtraJson()).getStr("remark", ""));
                }
                shipmentOrderDetailVO.setIdx("" + (++i));
                ShipmentOrderEnum.STATUS status = ShipmentOrderEnum.STATUS.findOrderStatus(shipmentOrderDetailBizDTO.getStatus());
                String statusDesc = status == null ? "" : status.getDesc();
                shipmentOrderDetailVO.setStatusName(statusDesc);
                shipmentOrderDetailVO.setExpQty((shipmentOrderDetailBizDTO.getExpQty() == null ? BigDecimal.ZERO : shipmentOrderDetailBizDTO.getExpQty())
                        .setScale(0, RoundingMode.FLOOR).toString());
                shipmentOrderDetailVO.setExpSkuQty((shipmentOrderDetailBizDTO.getExpSkuQty() == null ? BigDecimal.ZERO : shipmentOrderDetailBizDTO.getExpSkuQty())
                        .setScale(0, RoundingMode.FLOOR).toString());
                shipmentOrderDetailVO.setAssignQty((shipmentOrderDetailBizDTO.getAssignQty() == null ? BigDecimal.ZERO : shipmentOrderDetailBizDTO.getAssignQty())
                        .setScale(0, RoundingMode.FLOOR).toString());
                shipmentOrderDetailVO.setPickQty((shipmentOrderDetailBizDTO.getPickQty() == null ? BigDecimal.ZERO : shipmentOrderDetailBizDTO.getPickQty())
                        .setScale(0, RoundingMode.FLOOR).toString());
                shipmentOrderDetailVO.setCheckQty((shipmentOrderDetailBizDTO.getCheckQty() == null ? BigDecimal.ZERO : shipmentOrderDetailBizDTO.getCheckQty())
                        .setScale(0, RoundingMode.FLOOR).toString());
                shipmentOrderDetailVO.setOutStockQty((shipmentOrderDetailBizDTO.getOutStockQty() == null ? BigDecimal.ZERO : shipmentOrderDetailBizDTO.getOutStockQty())
                        .setScale(0, RoundingMode.FLOOR).toString());
                shipmentOrderDetailVO.setSkuQuality(SkuQualityEnum.getEnum(shipmentOrderDetailVO.getSkuQuality()).getMessage());
                shipmentOrderDetailVO.setReceiveDate(ConverterUtil.convertVoTime(shipmentOrderDetailBizDTO.getReceiveDate()));
                shipmentOrderDetailVO.setManufDateDesc(ConverterUtil.convertVoTime(shipmentOrderDetailBizDTO.getManufDate(), "yyyy-MM-dd"));
                shipmentOrderDetailVO.setExpireDateDesc(ConverterUtil.convertVoTime(shipmentOrderDetailBizDTO.getExpireDate(), "yyyy-MM-dd"));
                shipmentOrderDetailVO.setWithdrawDateDesc(ConverterUtil.convertVoTime(shipmentOrderDetailBizDTO.getWithdrawDate(), "yyyy-MM-dd"));
                shipmentOrderDetailVO.setCreatedTime(ConverterUtil.convertVoTime(shipmentOrderDetailBizDTO.getCreatedTime()));
                shipmentOrderDetailVO.setUpdatedTime(ConverterUtil.convertVoTime(shipmentOrderDetailBizDTO.getUpdatedTime()));
                shipmentOrderDetailVO.setPackageUnitDesc(PackageUnitEnum.fromCode(shipmentOrderDetailBizDTO.getPackageUnitCode()).getMessage());
                //是否赠品
                if (shipmentOrderDetailBizDTO.getFreeFlag() == 1) {
                    shipmentOrderDetailVO.setSkuCode(shipmentOrderDetailVO.getSkuCode().concat("（赠品）"));
                }
                if (!StringUtils.isEmpty(shipmentOrderDetailVO.getAllocationRuleCode())) {
                    AllocationRuleBizDTO allocationRuleBizDTOResult =
                            iAllocationRuleClient.queryAllocationRuleByCode(shipmentOrderDetailVO.getAllocationRuleCode()).getData();
                    if (allocationRuleBizDTOResult != null) {
                        shipmentOrderDetailVO.setAllocationRuleCodeName(allocationRuleBizDTOResult.getName());
                    } else {
                        shipmentOrderDetailVO.setAllocationRuleCodeName("");
                    }
                } else {
                    shipmentOrderDetailVO.setAllocationRuleCodeName("");
                }
                if (!StringUtils.isEmpty(shipmentOrderDetailVO.getTurnoverRuleCode())) {
                    TurnoverRuleBizDTO turnoverRuleBizDTO =
                            turnoverRuleClient.queryTurnoverRuleByCode(shipmentOrderDetailVO.getTurnoverRuleCode()).getData();
                    if (turnoverRuleBizDTO != null) {
                        shipmentOrderDetailVO.setTurnoverRuleCodeName(turnoverRuleBizDTO.getName());
                    } else {
                        shipmentOrderDetailVO.setTurnoverRuleCodeName("");
                    }
                } else {
                    shipmentOrderDetailVO.setTurnoverRuleCodeName("");
                }
                shipmentOrderDetailVO.setWithdrawCompareDateDesc("");
                if (!StringUtils.isEmpty(shipmentOrderDetailVO.getWithdrawCompareDate())) {
                    shipmentOrderDetailVO.setWithdrawCompareDateDesc(ConverterUtil.convertVoTime(shipmentOrderDetailVO.getWithdrawCompareDate(), "yyyy-MM-dd"));
                }

                shipmentOrderDetailVO.setInventoryTypeDesc("");
                if (!StringUtils.isEmpty(shipmentOrderDetailVO.getInventoryType())) {
                    shipmentOrderDetailVO.setInventoryTypeDesc(InventoryTypeEnum.getEnum(shipmentOrderDetailVO.getInventoryType()).getMessage());
                }

                shipmentOrderDetailVO.setExpireDateStartDesc(ConverterUtil.convertVoTime(shipmentOrderDetailVO.getExpireDateStart(), "yyyy-MM-dd"));
                shipmentOrderDetailVO.setExpireDateEndDesc(ConverterUtil.convertVoTime(shipmentOrderDetailVO.getExpireDateEnd(), "yyyy-MM-dd"));
                shipmentOrderDetailVO.setExpireDateShowDesc("");

                if (!StringUtils.isEmpty(shipmentOrderDetailVO.getExpireDateDesc())) {
                    shipmentOrderDetailVO.setExpireDateShowDesc(shipmentOrderDetailVO.getExpireDateDesc() + "至" + shipmentOrderDetailVO.getExpireDateDesc());
                }
                //起止都有值
                if (shipmentOrderDetailVO.getExpireDateStart() != null
                        && shipmentOrderDetailVO.getExpireDateStart() > 0
                        && shipmentOrderDetailVO.getExpireDateEnd() != null
                        && shipmentOrderDetailVO.getExpireDateEnd() > 0) {
                    shipmentOrderDetailVO.setExpireDateShowDesc(shipmentOrderDetailVO.getExpireDateStartDesc() + "至" + shipmentOrderDetailVO.getExpireDateEndDesc());
                }
                //起有值 止无值
                if (shipmentOrderDetailVO.getExpireDateStart() != null
                        && shipmentOrderDetailVO.getExpireDateStart() > 0
                        && (shipmentOrderDetailVO.getExpireDateEnd() == null
                        || shipmentOrderDetailVO.getExpireDateEnd() == 0)) {
                    shipmentOrderDetailVO.setExpireDateShowDesc("失效日期:" + shipmentOrderDetailVO.getExpireDateStartDesc() + "及以后");
                }
                //止有值 起无值
                if (shipmentOrderDetailVO.getExpireDateEnd() != null
                        && shipmentOrderDetailVO.getExpireDateEnd() > 0
                        && (shipmentOrderDetailVO.getExpireDateStart() == null
                        || shipmentOrderDetailVO.getExpireDateStart() == 0)) {
                    shipmentOrderDetailVO.setExpireDateShowDesc("失效日期:" + shipmentOrderDetailVO.getExpireDateEndDesc() + "及以前");
                }

                shipmentOrderDetailVOList.add(shipmentOrderDetailVO);
            }
        }
        page.setDataList(shipmentOrderDetailVOList);
        return Result.success(page);
    }

//    /**
//     * @param orderTagList
//     * @return java.lang.Integer
//     * @author: WuXian
//     * description:  解析订单tag
//     * create time: 2022/1/5 18:01
//     */
//    private Integer buildOrderTagList(List<Integer> orderTagList) {
//        List<OrderTagEnum> orderTagEnumList = new ArrayList<>();
//        Arrays.stream(OrderTagEnum.values()).forEach(it -> {
//            if (orderTagList.contains(it.getCode())) {
//                orderTagEnumList.add(it);
//            }
//        });
//        Integer type = OrderTagEnum.enumToNum(orderTagEnumList);
//        return type;
//    }

    @Override
    @SoulClient(path = "/shipmentDetail/page", desc = "出库发货明细分页")
    public Result<PageVO<ShipmentOrderSendVO>> queryPage2(ShipmentOrderBizParam param) {
        param.setStatusList(Arrays.asList(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode()));
        log.info("[op:ShipmentOrderClientImpl:queryPage2] param= {}", JSON.toJSONString(param));
        List<String> dataList = shipmentOrderClient.getCodeList(param);

        List<ShipmentOrderSendVO> voList = new ArrayList();
        Page<AllocationOrderDTO> pageBizPage = new Page<>();
        Result<Page<AllocationOrderDTO>> pageResult = new Result<>();

        if (!CollectionUtils.isEmpty(dataList)) {

            List<String> shipmentOrderCodeList = dataList.stream().distinct().collect(Collectors.toList());
            param.setShipmentOrderCodeList(shipmentOrderCodeList);
            pageResult = allocationOrderClient.getShipmentOrderPage(param);
            pageBizPage = pageResult.getData();
            List<AllocationOrderDTO> records = pageBizPage.getRecords();

            for (AllocationOrderDTO dto : records) {
                ShipmentOrderBizParam shipmentOrderBizParam = new ShipmentOrderBizParam();
                shipmentOrderBizParam.setShipmentOrderCode(dto.getShipmentOrderCode());
                shipmentOrderBizParam.setCargoCode(dto.getCargoCode());
                Result<ShipmentOrderBizDTO2> shipmentOrderBizDTO2Result =
                        shipmentOrderClient.get(shipmentOrderBizParam);
                ShipmentOrderBizDTO2 shipmentOrderBizDTO = shipmentOrderBizDTO2Result.getData();

                ShipmentOrderSendVO shipmentOrderSendVO = new ShipmentOrderSendVO();
                shipmentOrderSendVO.setBusinessType(shipmentOrderBizDTO.getBusinessType());
                CargoOwnerBizDTO cargoOwnerBizDTO =
                        iCargoOwnerBizClient.queryByCode(shipmentOrderBizDTO.getCargoCode()).getData();
                if (cargoOwnerBizDTO != null) {
                    shipmentOrderSendVO.setCargoName(cargoOwnerBizDTO.getName());
                }

                shipmentOrderSendVO.setPickSkuDate(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getPickSkuDate()));
                shipmentOrderSendVO
                        .setPickCompleteSkuDate(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getPickCompleteSkuDate()));
                shipmentOrderSendVO
                        .setCheckStartDate(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getCheckStartDate()));
                shipmentOrderSendVO
                        .setCheckCompleteDate(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getCheckCompleteDate()));
                shipmentOrderSendVO.setOutStockDate(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getOutStockDate()));
                shipmentOrderSendVO.setCreatedTime(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getCreatedTime()));
                shipmentOrderSendVO.setShipmentOrderCode(shipmentOrderBizDTO.getShipmentOrderCode());
                shipmentOrderSendVO.setSoNo(shipmentOrderBizDTO.getSoNo());

                if (!StringUtils.isEmpty(shipmentOrderBizDTO.getSalePlatform())
                        && !"other".equals(shipmentOrderBizDTO.getSalePlatform())) {
                    CodeParam codeParam = new CodeParam();
                    codeParam.setCode(shipmentOrderBizDTO.getSalePlatform());
                    SalePlatformBizDTO salePlatformBizDTO = salePlatformBizClient.get(codeParam).getData();
                    if (salePlatformBizDTO != null) {
                        shipmentOrderSendVO.setSalePlatformName(salePlatformBizDTO.getName());
                    }
                } else {
                    shipmentOrderSendVO.setSalePlatformName("其他");
                }

                if (!StringUtils.isEmpty(shipmentOrderBizDTO.getCarrierCode())) {
                    CarrierBizDTO carrierBizDTO =
                            icarrierBizClient.queryByCode(shipmentOrderBizDTO.getCarrierCode()).getData();
                    if (carrierBizDTO != null) {
                        shipmentOrderSendVO.setCarrierName(carrierBizDTO.getName());
                    }
                }
                shipmentOrderSendVO.setCarrierCode(shipmentOrderBizDTO.getCarrierCode());
                shipmentOrderSendVO
                        .setStatus(ShipmentOrderEnum.STATUS.findOrderStatus(shipmentOrderBizDTO.getStatus()).getDesc());
                shipmentOrderSendVO.setSaleShop(shipmentOrderBizDTO.getSaleShop());
                shipmentOrderSendVO.setUpdatedBy(shipmentOrderBizDTO.getUpdatedBy());
                shipmentOrderSendVO.setUpdatedTime(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getUpdatedTime()));

                shipmentOrderSendVO.setReceiverProv(shipmentOrderBizDTO.getReceiverProv());
                shipmentOrderSendVO.setReceiverArea(shipmentOrderBizDTO.getReceiverArea());
                shipmentOrderSendVO.setReceiverCity(shipmentOrderBizDTO.getReceiverCity());
                shipmentOrderSendVO.setReceiverAddress(shipmentOrderBizDTO.getReceiverAddress());
                shipmentOrderSendVO.setReceiverCityName(shipmentOrderBizDTO.getReceiverCityName());
                shipmentOrderSendVO.setReceiverProvName(shipmentOrderBizDTO.getReceiverProvName());
                shipmentOrderSendVO.setReceiverAreaName(shipmentOrderBizDTO.getReceiverAreaName());

                shipmentOrderSendVO.setCargoCode(shipmentOrderBizDTO.getCargoCode());
                shipmentOrderSendVO.setWarehouseCode(shipmentOrderBizDTO.getWarehouseCode());
                shipmentOrderSendVO.setPickQty(dto.getPickQty());
                shipmentOrderSendVO.setExpSkuQty(dto.getExpQty());
                shipmentOrderSendVO.setAssignQty(dto.getRealQty());
                shipmentOrderSendVO.setCheckQty(dto.getPickQty());
                shipmentOrderSendVO.setOutStockQty(dto.getRealQty());
                shipmentOrderSendVO.setSkuLotNo(dto.getSkuLotNo());
                shipmentOrderSendVO.setSkuCode(dto.getSkuCode());
                shipmentOrderSendVO.setLocationCode(dto.getLocationCode());

                // 查询生产日期和失效日期
                SkuLotBizParam skuLotParam = new SkuLotBizParam();
                skuLotParam.setSkuCode(dto.getSkuCode());
                skuLotParam.setSkuLotNo(dto.getSkuLotNo());
                skuLotParam.setCargoCode(dto.getCargoCode());
                SkuLotBizDTO skuLotBizDTO = skuLotBizClient.get(skuLotParam);
                if (skuLotBizDTO != null) {
                    shipmentOrderSendVO
                            .setManufDateFormat(ConverterUtil.convertVoTime(skuLotBizDTO.getManufDate(), "yyyy-MM-dd"));
                    shipmentOrderSendVO
                            .setExpireDateFormat(ConverterUtil.convertVoTime(skuLotBizDTO.getExpireDate(), "yyyy-MM-dd"));
                }

                ShipmentOrderDetailParam shipmentOrderParam = new ShipmentOrderDetailParam();
                shipmentOrderParam.setShipmentOrderCode(shipmentOrderBizDTO.getShipmentOrderCode());
                List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList =
                        shipmentOrderDetailBizClient.getList(shipmentOrderParam).getData();
                ;
                shipmentOrderDetailDTOList.stream().forEach(s -> {
                    if (StringUtils.isEmpty(s.getSkuLotNo())) {
                        if (s.getSkuCode().equals(dto.getSkuCode()) && s.getCargoCode().equals(dto.getCargoCode())) {
                            shipmentOrderSendVO.setSkuName(s.getSkuName());
                            shipmentOrderSendVO.setUpcCode(s.getUpcCode());
                            shipmentOrderSendVO
                                    .setPackageUnitDesc(PackageUnitEnum.fromCode(s.getPackageUnitCode()).getMessage());
                            shipmentOrderSendVO.setSkuQuality(SkuQualityEnum.getEnum(s.getSkuQuality()).getMessage());
                        }
                    } else {
                        if (s.getSkuCode().equals(dto.getSkuCode()) && s.getCargoCode().equals(dto.getCargoCode())
                                && s.getSkuLotNo().equals(dto.getSkuLotNo())) {
                            shipmentOrderSendVO.setSkuName(s.getSkuName());
                            shipmentOrderSendVO.setUpcCode(s.getUpcCode());
                            shipmentOrderSendVO
                                    .setPackageUnitDesc(PackageUnitEnum.fromCode(s.getPackageUnitCode()).getMessage());
                            shipmentOrderSendVO.setSkuQuality(SkuQualityEnum.getEnum(s.getSkuQuality()).getMessage());
                        }
                    }
                });

                voList.add(shipmentOrderSendVO);
            }

            int i = 0;
            for (ShipmentOrderSendVO shipmentOrderSendVO : voList) {
                shipmentOrderSendVO.setIdx("" + (++i));
            }
        } else {
            pageResult.setCode(200);
            pageResult.setMessage("操作成功");
            pageBizPage.setSize(param.getSize());
        }

        // 组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(pageBizPage.getSize());
        page.setCurrentPage(pageBizPage.getCurrent());
        page.setTotalPage(pageBizPage.getPages());
        page.setTotalCount(pageBizPage.getTotal());
        PageVO<ShipmentOrderSendVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);
        Result<PageVO<ShipmentOrderSendVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);
        return result;
    }

    /**
     * 查看明细
     *
     * @param idParam
     * @return
     */
    @Override
    @SoulClient(path = "/shipment/view", desc = "出库明细查看")
    public Result<ShipmentOrderVO> view(IdParam idParam) {

        log.info("[op:ShipmentOrderClientImpl:view] idParam= {}", JSON.toJSONString(idParam));
        ShipmentOrderBizParam searchShipmentOrderParam = new ShipmentOrderBizParam();
        searchShipmentOrderParam.setId(idParam.getId());
        ShipmentOrderBizDTO bizDTO = shipmentOrderClient.getDetail(searchShipmentOrderParam).getData();

        ShipmentOrderVO vo = getShipmentOrderVO(bizDTO);

        List<ShipmentOrderDetailVO> shipmentOrderDetailVOList = new ArrayList<>();
//        List<ShipmentOrderDetailBizDTO> shipmentOrderDetailBizDTOList = bizDTO.getListShipmentOrderDetailBizDTO();
//
//        List<ShipmentOrderDetailBizDTO> detailBizDTOArrayList = new ArrayList<>();
//        List<ShipmentOrderDetailBizDTO> detailBizDTOList =
//                shipmentOrderDetailBizDTOList.stream().filter(s -> s.getRefSplitDetailId() == null || s.getRefSplitDetailId() <= 0)
//                        .sorted(Comparator.comparing(ShipmentOrderDetailBizDTO::getId, Comparator.naturalOrder()))
//                        .collect(Collectors.toList());
//
//        detailBizDTOList.forEach(s -> {
//            detailBizDTOArrayList.add(s);
//            detailBizDTOArrayList.addAll(shipmentOrderDetailBizDTOList.stream().filter(ss -> ss.getRefSplitDetailId().equals(s.getId()))
//                    .sorted(Comparator.comparing(ShipmentOrderDetailBizDTO::getId, Comparator.naturalOrder()))
//                    .collect(Collectors.toList()));
//        });
//
//        int i = 0;
//        for (ShipmentOrderDetailBizDTO bizDTO1 : detailBizDTOArrayList) {
//            ShipmentOrderDetailVO voDetail = new ShipmentOrderDetailVO();
//            BeanUtils.copyProperties(bizDTO1, voDetail);
//            voDetail.setIdx("" + (++i));
//            ShipmentOrderEnum.STATUS status = ShipmentOrderEnum.STATUS.findOrderStatus(bizDTO1.getStatus());
//            String statusDesc = status == null ? "" : status.getDesc();
//            voDetail.setStatusName(statusDesc);
//            voDetail.setExpQty((bizDTO1.getExpQty() == null ? BigDecimal.ZERO : bizDTO1.getExpQty())
//                    .setScale(bizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
//            voDetail.setExpSkuQty((bizDTO1.getExpSkuQty() == null ? BigDecimal.ZERO : bizDTO1.getExpSkuQty())
//                    .setScale(bizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
//            voDetail.setAssignQty((bizDTO1.getAssignQty() == null ? BigDecimal.ZERO : bizDTO1.getAssignQty())
//                    .setScale(bizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
//            voDetail.setPickQty((bizDTO1.getPickQty() == null ? BigDecimal.ZERO : bizDTO1.getPickQty())
//                    .setScale(bizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
//            voDetail.setCheckQty((bizDTO1.getCheckQty() == null ? BigDecimal.ZERO : bizDTO1.getCheckQty())
//                    .setScale(bizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
//            voDetail.setOutStockQty((bizDTO1.getOutStockQty() == null ? BigDecimal.ZERO : bizDTO1.getOutStockQty())
//                    .setScale(bizDTO.getNumberFormat(), RoundingMode.FLOOR).toString());
//            voDetail.setSkuQuality(SkuQualityEnum.getEnum(voDetail.getSkuQuality()).getMessage());
//            voDetail.setReceiveDate(ConverterUtil.convertVoTime(bizDTO1.getReceiveDate()));
//            voDetail.setManufDateDesc(ConverterUtil.convertVoTime(bizDTO1.getManufDate(), "yyyy-MM-dd"));
//            voDetail.setExpireDateDesc(ConverterUtil.convertVoTime(bizDTO1.getExpireDate(), "yyyy-MM-dd"));
//            voDetail.setWithdrawDateDesc(ConverterUtil.convertVoTime(bizDTO1.getWithdrawDate(), "yyyy-MM-dd"));
//            voDetail.setCreatedTime(ConverterUtil.convertVoTime(bizDTO1.getCreatedTime()));
//            voDetail.setUpdatedTime(ConverterUtil.convertVoTime(bizDTO1.getUpdatedTime()));
//            voDetail.setPackageUnitDesc(PackageUnitEnum.fromCode(bizDTO1.getPackageUnitCode()).getMessage());
//            //是否赠品
//            if (bizDTO1.getFreeFlag() == 1) {
//                voDetail.setSkuCode(voDetail.getSkuCode().concat("（赠品）"));
//            }
//            if (!StringUtils.isEmpty(voDetail.getAllocationRuleCode())) {
//                AllocationRuleBizDTO allocationRuleBizDTOResult =
//                        iAllocationRuleClient.queryAllocationRuleByCode(voDetail.getAllocationRuleCode()).getData();
//                if (allocationRuleBizDTOResult != null) {
//                    voDetail.setAllocationRuleCodeName(allocationRuleBizDTOResult.getName());
//                } else {
//                    voDetail.setAllocationRuleCodeName("");
//                }
//            } else {
//                voDetail.setAllocationRuleCodeName("");
//            }
//            if (!StringUtils.isEmpty(voDetail.getTurnoverRuleCode())) {
//                TurnoverRuleBizDTO turnoverRuleBizDTO =
//                        turnoverRuleClient.queryTurnoverRuleByCode(voDetail.getTurnoverRuleCode()).getData();
//                if (turnoverRuleBizDTO != null) {
//                    voDetail.setTurnoverRuleCodeName(turnoverRuleBizDTO.getName());
//                } else {
//                    voDetail.setTurnoverRuleCodeName("");
//                }
//            } else {
//                voDetail.setTurnoverRuleCodeName("");
//            }
//            voDetail.setInventoryTypeDesc("");
//            if (!StringUtils.isEmpty(voDetail.getInventoryType())) {
//                voDetail.setInventoryTypeDesc(InventoryTypeEnum.getEnum(voDetail.getInventoryType()).getMessage());
//            }
//            shipmentOrderDetailVOList.add(voDetail);
//        }

        List<ShipmentOrderLogBizDTO> logList = bizDTO.getListShipmentOrderLogBizDTO();
        if (logList == null) {
            logList = new ArrayList<>();
        }
        logList = logList.stream().sorted(Comparator.comparing(ShipmentOrderLogBizDTO::getOpDate).reversed())
                .collect(Collectors.toList());

        List<ShipmentOrderLogVO> shipmentOrderLogVOList = new ArrayList<>();

        for (ShipmentOrderLogBizDTO bizLog : logList) {
            ShipmentOrderLogVO voLog = new ShipmentOrderLogVO();
            voLog.setOpDate(ConverterUtil.convertVoTime(bizLog.getOpDate()));
            voLog.setCreatedTime(ConverterUtil.convertVoTime(bizLog.getCreatedTime()));
            voLog.setUpdatedTime(ConverterUtil.convertVoTime(bizLog.getUpdatedTime()));
            BeanUtils.copyProperties(bizLog, voLog);
            shipmentOrderLogVOList.add(voLog);
        }
        vo.setListShipmentOrderDetail(shipmentOrderDetailVOList);
        vo.setListShipmentOrderLog(shipmentOrderLogVOList);
        return Result.success(vo);
    }

    @Override
    @SoulClient(path = "/shipment/modifyCarrier", desc = "批量修改")
    public Result<Boolean> modifyCarrier(@Validated ModifyCarrierBizParam param) {
        return Result.success(shipmentOrderClient.modifyCarrier(param).getData());
    }

    @Override
    @SoulClient(path = "/shipment/modifyExpressNo", desc = "修改运单号")
    public Result<Boolean> modifyExpressNo(ModifyExpressNoBizParam param) {
        return Result.success(shipmentOrderClient.modifyExpressNo(param).getData());
    }

    @Override
    @SoulClient(path = "/shipment/outOfStockNotify", desc = "缺货回告")
    public Result<Boolean> outOfStockNotify(OutOfStockNotifyParam param) {
        return shipmentOrderClient.outOfStockNotify(param);
    }

    @Override
    @SoulClient(path = "/shipment/outOfStockNotifyView", desc = "缺货回告")
    public Result<List<ShipmentOrderDetailDTO>> outOfStockNotifyView(OutOfStockNotifyParam param) {
        return shipmentOrderClient.outOfStockNotifyView(param);
    }

    @Override
    @SoulClient(path = "/shipment/getOutOfStockNotifyType", desc = "获取缺货回告的类型")
    public Result<List<IdNameVO>> getOutOfStockNotifyType() {
        List<IdNameVO> idNameVOList = new ArrayList<>();
        IdNameVO idNameVO = new IdNameVO();
        idNameVO.setId(OpAbnormalTypeEnum.T_2002.getCode());
        idNameVO.setName(OpAbnormalTypeEnum.T_2002.getMessage());
        idNameVOList.add(idNameVO);
        IdNameVO idNameVO2 = new IdNameVO();
        idNameVO2.setId(OpAbnormalTypeEnum.T_2003.getCode());
        idNameVO2.setName(OpAbnormalTypeEnum.T_2003.getMessage());
        idNameVOList.add(idNameVO2);
        IdNameVO idNameVO4 = new IdNameVO();
        idNameVO4.setId(OpAbnormalTypeEnum.T_2004.getCode());
        idNameVO4.setName(OpAbnormalTypeEnum.T_2004.getMessage());
        idNameVOList.add(idNameVO4);
        IdNameVO idNameVO8 = new IdNameVO();
        idNameVO8.setId(OpAbnormalTypeEnum.T_2008.getCode());
        idNameVO8.setName(OpAbnormalTypeEnum.T_2008.getMessage());
        idNameVOList.add(idNameVO8);
        return Result.success(idNameVOList);
    }

    @Override
    @SoulClient(path = "/shipment/rollback", desc = "预处理成功回滚")
    public Result<Boolean> rollback(ShipmentOrderBizParam param) {
        return shipmentOrderClient.rollback(param.getShipmentOrderCodeList());
    }

    @Override
    @SoulClient(path = "/shipment/assignSkuLotNo", desc = "创建状态的出库单指定批次")
    public Result<Boolean> assignSkuLotNo(ShipmentOrderBizParam param) {
        return Result.success(shipmentOrderClient.assignSkuLotNo(param));
    }

    @Override
    @SoulClient(path = "/shipment/getShipmentDetailSku", desc = "根据出库单号查询出库单明细")
    public Result<List<ShipmentOrderSkuMsgVO>> getShipmentDetailSkuMsg(ShipmentOrderBizParam param) {
        List<ShipmentOrderDetailDTO> list = shipmentOrderClient.getShipmentDetailSkuMsg(param);
        List<ShipmentOrderSkuMsgVO> oretList = new ArrayList<>();
        for (ShipmentOrderDetailDTO dto : list) {
            ShipmentOrderSkuMsgVO vo = ConverterUtil.convert(dto, ShipmentOrderSkuMsgVO.class);
            vo.setSourceId(dto.getRefSplitDetailId());
            vo.setViewExpSkuQty(dto.getExpSkuQty());
            if (!StringUtils.isEmpty(dto.getSkuQuality())) {
                vo.setSkuQuality(SkuQualityEnum.getEnum(dto.getSkuQuality()).getMessage());
            } else {
                vo.setSkuQuality("");
            }
            oretList.add(vo);
        }
        /**
         * 返回明细按照拆分时候的顺序排序
         */
        List<ShipmentOrderSkuMsgVO> voOretList = new ArrayList<>();
        List<ShipmentOrderSkuMsgVO> _oretList =
                oretList.stream().filter(s -> s.getSourceId() == null || s.getSourceId() <= 0)
                        .sorted(Comparator.comparing(ShipmentOrderSkuMsgVO::getId, Comparator.naturalOrder()))
                        .collect(Collectors.toList());
        _oretList.forEach(s -> {
            voOretList.add(s);
            voOretList.addAll(oretList.stream().filter(ss -> ss.getSourceId().equals(s.getId()))
                    .sorted(Comparator.comparing(ShipmentOrderSkuMsgVO::getId, Comparator.naturalOrder()))
                    .collect(Collectors.toList()));
        });
        return Result.success(voOretList);
    }

    @Override
    @SoulClient(path = "/shipment/xtBatchOut", desc = "销退仓批量出库")
    public Result<Boolean> xtBatchOut(ShipmentOrderBizParam param) {
        if (StringUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getShipmentOrderCodeList())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return shipmentOrderClient.xtBatchOut(param);
    }

    @Override
    @SoulClient(path = "/shipment/oneClickOutBoundByXt", desc = "销退仓批量一键出库")
    public Result<Boolean> oneClickOutBoundByXt(ShipmentOrderBizParam param) {
        if (StringUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getShipmentOrderCodeList())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return shipmentOrderClient.oneClickOutBoundByXt(param);
    }

    @Override
    @SoulClient(path = "/shipment/zeroOutStock", desc = "零出库")
    public Result<Boolean> zeroOutStock(ShipmentOrderBizParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getShipmentOrderCode())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return shipmentOrderClient.zeroOutStock(param);
    }

    @Override
    @SoulClient(path = "/shipment/fixMergeShipOut", desc = "移除合流标记")
    public Result<Boolean> fixMergeShipOut(ShipmentOrderBizParam param) {
        if (StringUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getShipmentOrderCodeList())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        if (param.getShipmentOrderCodeList().size() > 1) {
            throw new BaseException(BaseBizEnum.TIP, "每次只能勾选一个出库单,同交易单号只需要移除一次");
        }
        param.setShipmentOrderCode(param.getShipmentOrderCodeList().get(0));
        return shipmentOrderClient.fixMergeShipOut(param);
    }

    @Override
    @SoulClient(path = "/shipment/oneClickOutBound", desc = "B单一键出库")
    public Result<Boolean> oneClickOutBound(CodeParam param) throws Exception {
        return Result.success(shipmentOrderClient.oneClickOutBound(param));
    }

    @Override
    @SoulClient(path = "/shipment/wmsAddShipment", desc = "新增出库单")
    public Result<Boolean> wmsAddShipment(@Validated ShipmentAddForm form) {
        return shipmentOrderClient.wmsAddShipment(form);
    }

    @Override
    @SoulClient(path = "/shipment/wmsModifyShipment", desc = "修改出库单")
    public Result<Boolean> wmsModifyShipment(@Validated ShipmentModifyForm form) {
        return shipmentOrderClient.wmsModifyShipment(form);
    }

    @Override
    @SoulClient(path = "/shipment/wmsCancelShipment", desc = "取消入库单")
    public Result<String> wmsCancelShipment(@Validated ShipmentCancelParam param) {
        return shipmentOrderClient.wmsCancelShipment(param);
    }

    @Override
    @SoulClient(path = "/shipment/wmsModifyShipmentDetail", desc = "查询修改明细")
    @DataSecurity
    public Result<ShipmentOrderModifyVO> wmsModifyShipmentDetail(@Validated CodeParam param) {
        Result<ShipmentOrderBizDTO> shipmentOrderBizDTOResult = shipmentOrderClient.wmsModifyShipmentDetail(param);
        if (!shipmentOrderBizDTOResult.checkSuccess()) {
            throw new BaseException(BaseBizEnum.TIP, "出库单获取明细异常");
        }
        ShipmentOrderBizDTO shipmentOrderBizDTO = shipmentOrderBizDTOResult.getData();
        ShipmentOrderModifyVO vo = ConverterUtil.convert(shipmentOrderBizDTO, ShipmentOrderModifyVO.class);
        vo.setCreatedTime(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getCreatedTime()));
        vo.setUpdatedTime(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getUpdatedTime()));
        vo.setOrderTypeName(ShipmentOrderEnum.ORDER_TYPE.findEnumDesc(vo.getOrderType()).getDesc());
        if (!StringUtils.isEmpty(vo.getSalePlatform())) {
            CodeParam codeParam = new CodeParam();
            codeParam.setCode(vo.getSalePlatform());
            Result<SalePlatformBizDTO> result = salePlatformBizClient.get(codeParam);
            if (result.getData() != null) {
                vo.setSalePlatformName(result.getData().getName());
            }
        } else {
            vo.setSalePlatformName(vo.getSalePlatform());
        }
        vo.setExpOutStockDateFormat(ConverterUtil.convertVoTime(shipmentOrderBizDTO.getExpOutStockDate(), "yyyy-MM-dd"));

        if (!StringUtils.isEmpty(shipmentOrderBizDTO.getCarrierCode())) {
            CarrierBizDTO carrierBizDTO = icarrierBizClient.queryByCode(shipmentOrderBizDTO.getCarrierCode()).getData();
            if (carrierBizDTO != null) {
                vo.setCarrierName(carrierBizDTO.getName());
            }
        }
        if (!StringUtils.isEmpty(shipmentOrderBizDTO.getReceiverArea())) {
            AreaBizDTO areaBizDTO = areaBizClient.get(shipmentOrderBizDTO.getReceiverArea()).getData();
            if (areaBizDTO != null) {
                vo.setReceiverAreaName(areaBizDTO.getAreaName());
            }
        } else {
            vo.setReceiverAreaName(shipmentOrderBizDTO.getReceiverAreaName());
        }

        if (!StringUtils.isEmpty(shipmentOrderBizDTO.getReceiverCity())) {
            AreaBizDTO areaBizDTO = areaBizClient.get(shipmentOrderBizDTO.getReceiverCity()).getData();
            if (areaBizDTO != null) {
                vo.setReceiverCityName(areaBizDTO.getAreaName());
            }
        } else {
            vo.setReceiverCityName(shipmentOrderBizDTO.getReceiverCityName());
        }
        if (!StringUtils.isEmpty(shipmentOrderBizDTO.getReceiverProv())) {
            AreaBizDTO areaBizDTO = areaBizClient.get(shipmentOrderBizDTO.getReceiverProv()).getData();
            if (areaBizDTO != null) {
                vo.setReceiverProvName(areaBizDTO.getAreaName());
            }
        } else {
            vo.setReceiverProvName(shipmentOrderBizDTO.getReceiverProvName());
        }

        List<ShipmentOrderMaterialDTO> shipmentMaterial = shipmentOrderClient.getShipmentMaterial(shipmentOrderBizDTO.getShipmentOrderCode());
        if (CollectionUtil.isNotEmpty(shipmentMaterial)) {
            vo.setPackageMaterialCode(shipmentMaterial.stream().map(ShipmentOrderMaterialDTO::getRecPackUpcCode).distinct().collect(Collectors.joining(StrUtil.COMMA)));
        } else {
            vo.setPackageMaterialCode("");
        }
        List<ShipmentOrderDetailModifyVO> detailModifyVOList = ConverterUtil.convertList(shipmentOrderBizDTO.getListShipmentOrderDetailBizDTO(), ShipmentOrderDetailModifyVO.class);
        detailModifyVOList.forEach(a -> a.setSkuQualityName(SkuQualityEnum.getEnum(a.getSkuQuality()).getMessage()));
        vo.setDetailModifyVOList(detailModifyVOList);
        return Result.success(vo);
    }

    @Override
    @SoulClient(path = "/shipment/distributionOutBound", desc = "分销出库")
    public Result<Boolean> distributionOutBound(CodeParam param) throws Exception {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = shipmentOrderClient.distributionOutBound(param);
        return result;
    }

    @Override
    @SoulClient(path = "/shipment/orderTag", desc = "订单tag")
    public Result<List<IdNameVO>> getOrderTag() {
        List<IdNameVO> idNameVOS = IdNameVO.build(OrderTagEnum.class, "code", "desc");
        return Result.success(idNameVOS);
    }

    @Override
    @SoulClient(path = "/shipment/wmsAddShipmentTally", desc = "出库单新增理货报告")
    public Result<Boolean> wmsAddShipmentTally(CodeParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = shipmentOrderClient.wmsAddShipmentTally(param);
        return result;
    }

    @Override
    @SoulClient(path = "/shipment/getOrderTagPartToOperation", desc = "出库单获取能操作的订单标记")
    public Result<List<IdNameVO>> getOrderTagPartToOperation() {
        List<IdNameVO> idNameVOS = new ArrayList<>();
        OrderTagEnum.getShipmentOrderTagToOperation().stream().forEachOrdered(a -> {
            IdNameVO idNameVO = new IdNameVO();
            idNameVO.setId(a.getCode());
            idNameVO.setName(a.getDesc());
            idNameVOS.add(idNameVO);
        });
        return Result.success(idNameVOS);
    }

    @Override
    @SoulClient(path = "/shipment/modifyOrderTag", desc = "修改出库单的订单tag")
    public Result<Boolean> modifyOrderTag(OrderTagOperationParam param) throws Exception {
        if (param == null || StringUtils.isEmpty(param.getShipmentOrderCode())) {
            throw new BaseException(BaseBizEnum.TIP, "出库单号不能为空");
        }
        Result<Boolean> result = shipmentOrderClient.modifyOrderTag(param);
        return result;
    }

    @Override
    @SoulClient(path = "/shipment/getOrderTagPartToOperationOccupy", desc = "获取当前订单tag")
    public Result<List<Integer>> getOrderTagPartToOperationOccupy(CodeParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<List<Integer>> result = shipmentOrderClient.getOrderTagPartToOperationOccupy(param);
        return result;
    }

    @Override
    @SoulClient(path = "/shipment/getAnalysisError", desc = "获取解析异常的订单")
    public Result<AnalysisErrorVO> getAnalysisError(CodeParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<AnalysisErrorBizDTO> result = shipmentOrderClient.getAnalysisError(param);
        return Result.success(ConverterUtil.convert(result.getData(), AnalysisErrorVO.class));
    }

    @Override
    @SoulClient(path = "/shipment/commitAnalysisData", desc = "提交解析异常的订单")
    public Result<Boolean> commitAnalysisData(AnalysisCommitBizDTO commitBizDTO) {
        if (StringUtils.isEmpty(commitBizDTO) || StringUtils.isEmpty(commitBizDTO.getBillNo())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = shipmentOrderClient.commitAnalysisData(commitBizDTO);
        return result;
    }

    @Override
    @SoulClient(path = "/shipment/signAndNotify", desc = "签收回传")
    public Result<Object> signAndNotify(ShipmentOrderBizParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getShipmentOrderCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        if (StringUtils.isEmpty(param.getSignNotifyTime()) || param.getSignNotifyTime() <= 0) {
            throw new BaseException(BaseBizEnum.TIP, "仓签收时间为空");
        }
        //签收时间必填，且必须小于等于当前时间
        if (param.getSignNotifyTime() > System.currentTimeMillis()) {
            throw new BaseException(BaseBizEnum.TIP, "签收时间必填，且必须小于等于当前时间");
        }
        Result<Object> result = shipmentOrderClient.signAndNotify(param);
        return result;
    }
}
