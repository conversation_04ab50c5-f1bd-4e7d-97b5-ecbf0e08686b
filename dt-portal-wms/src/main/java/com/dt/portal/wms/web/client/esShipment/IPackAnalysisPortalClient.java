package com.dt.portal.wms.web.client.esShipment;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.param.pkg.PackAnalysisBillParam;
import com.dt.platform.wms.dto.pkg.PackAnalysisBizDTO;
import com.dt.portal.wms.web.vo.pkg.PackageVO;
import com.dt.portal.wms.web.vo.shipment.ShipmentOrderVO;

/**
 * 包裹分析Portal客户端接口
 */
public interface IPackAnalysisPortalClient {


    /**
     * 包裹分析查询（分页版）
     *
     * @param param 查询参数
     * @return 分页分析结果
     */

    Result<PageVO<PackAnalysisBizDTO>> getPackAnalysisPage(PackAnalysisBillParam param);

    /**
     *  获取当前仓库下的包裹分析数据
     * @param param
     * @return
     */
    Result<PageVO<PackageVO>> getPackAnalysisPageByPack(PackAnalysisBillParam param);

    /**
     * 包裹分析汇总查询
     * 对查询条件下的所有数据进行汇总，返回订单数和商品数量的求和
     *
     * @param param 查询参数
     * @return 汇总结果
     */

    Result<PackAnalysisBizDTO> getPackAnalysisSummary(PackAnalysisBillParam param);
}
