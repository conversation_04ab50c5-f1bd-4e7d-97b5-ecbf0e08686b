package com.dt.portal.wms.web.client.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.OpTypeEnum;
import com.dt.component.common.enums.move.MoveStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.IMoveBizClient;
import com.dt.platform.wms.dto.move.MoveBizDTO;
import com.dt.platform.wms.dto.move.MoveDetailBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.move.*;
import com.dt.portal.wms.web.client.IMovePortalClient;
import com.dt.portal.wms.web.vo.move.MoveDetailVO;
import com.dt.portal.wms.web.vo.move.MoveVO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
public class MovePortalClient implements IMovePortalClient {

    @DubboReference
    private IMoveBizClient moveBizClient;

    public static MoveVO getMoveVO(MoveBizDTO moveBiz) {
        MoveVO moveVO = ConverterUtil.convert(moveBiz, MoveVO.class);
        if (!ObjectUtils.isEmpty(moveVO)) {
            moveVO.setStatusName(MoveStatusEnum.getEnum(moveVO.getStatus()).getName());
            moveVO.setOpTypeName(OpTypeEnum.getEnum(moveVO.getOpType()).getName());
            moveVO.setCompleteDate(ConverterUtil.convertVoTime(moveBiz.getCompleteDate()));
            moveVO.setCreatedTime(ConverterUtil.convertVoTime(moveBiz.getCreatedTime()));
            moveVO.setUpdatedTime(ConverterUtil.convertVoTime(moveBiz.getUpdatedTime()));
            List<MoveDetailBizDTO> detailList = moveBiz.getDetailList();
            if (!CollectionUtils.isEmpty(detailList)) {
                List<MoveDetailVO> detailVOList = detailList.stream()
                        .flatMap(a -> {
                            MoveDetailVO detail = ConverterUtil.convert(a, MoveDetailVO.class);
                            if (!ObjectUtils.isEmpty(detail)) {
                                detail.setStatusName(MoveStatusEnum.getEnum(detail.getStatus()).getName());
                                detail.setCreatedTime(ConverterUtil.convertVoTime(a.getCreatedTime()));
                                detail.setUpdatedTime(ConverterUtil.convertVoTime(a.getUpdatedTime()));
                            }
                            return Stream.of(detail);
                        })
                        .collect(Collectors.toList());
                moveVO.setDetailList(detailVOList);
            }
        }
        return moveVO;
    }

    @Override
    @SoulClient(path = "/move/getStatusList", desc = "移位单-获取状态下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getStatusList() {
        return Result.success(IdNameVO.build(MoveStatusEnum.class, "status", "name"));
    }

    @Override
    @SoulClient(path = "/move/getAllStatusList", desc = "移位单-获取状态下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getAllStatusList() {
        return Result.success(IdNameVO.buildAll(MoveStatusEnum.class, "status", "name"));
    }

    @Override
    @SoulClient(path = "/move/create", desc = "移位单管理-修改")
    public Result<Boolean> create(@Validated MoveAddParam param) {
//        return moveBizClient.create(param);
        return moveBizClient.moveAdd(param);
    }

    @Override
    @SoulClient(path = "/move/modify", desc = "移位单管理-修改")
    public Result<Boolean> modify(@Validated MoveUpdateParam param) {
//        MoveBizParam zoneBizParam = ConverterUtil.convert(param, MoveBizParam.class);
//        return moveBizClient.modify(zoneBizParam);
        return moveBizClient.moveUpdate(param);
    }

    @Override
    @SoulClient(path = "/move/modifyOpType", desc = "移位单-修改操作方式")
    public Result<Boolean> modifyOpType(MoveOpTypeParam param) {
        return moveBizClient.modifyOpType(param);
    }

    @Override
    @SoulClient(path = "/move/getDetail", desc = "移位单-获取详情")
    public Result<MoveVO> getDetail(@Validated CodeParam param) {
        MoveBizParam moveBizParam = new MoveBizParam();
        moveBizParam.setCode(param.getCode());

        Result<MoveBizDTO> dtoResult = moveBizClient.getDetail(moveBizParam);

        MoveBizDTO moveBiz = dtoResult.getData();
        return Result.success(getMoveVO(moveBiz));
    }

    @Override
    @SoulClient(path = "/move/getPage", desc = "移位单-获取分页列表")
    public Result<PageVO<MoveVO>> getPage(MoveBizParam param) {

        Result<Page<MoveBizDTO>> pageResult = moveBizClient.getPage(param);

        Page<MoveBizDTO> moveBizPage = pageResult.getData();
        List<MoveBizDTO> moveBizList = moveBizPage.getRecords();
        List<MoveVO> voList = new ArrayList<>();

        //格式化数据
        if (!CollectionUtils.isEmpty(moveBizList)) {
            moveBizList = moveBizPage.getRecords();
            voList = moveBizList.stream()
                    .flatMap(a -> Stream.of(getMoveVO(a)))
                    .collect(Collectors.toList());
        }

        //组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(moveBizPage.getSize());
        page.setCurrentPage(moveBizPage.getCurrent());
        page.setTotalPage(moveBizPage.getPages());
        page.setTotalCount(moveBizPage.getTotal());

        PageVO<MoveVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);

        Result<PageVO<MoveVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);

        return result;
    }

    @Override
    @SoulClient(path = "/move/complete", desc = "移位单-完成移位单")
    public Result<Boolean> complete(@Validated CodeParam param) {
        return moveBizClient.complete(param);
    }

    @Override
    @SoulClient(path = "/move/completeDetail", desc = "移位单-完成明细")
    public Result<Boolean> completeDetail(@Validated MoveDetailCompleteBizParam param) {
        throw new BaseException(BaseBizEnum.TIP, "单条移位不再支持");
    }

    @Override
    @SoulClient(path = "/move/completeRetainDetail", desc = "移位单-完成剩余明细")
    public Result<Boolean> completeRetainDetail(CodeParam param) {
        return moveBizClient.completeRetainDetail(param);
    }
}
