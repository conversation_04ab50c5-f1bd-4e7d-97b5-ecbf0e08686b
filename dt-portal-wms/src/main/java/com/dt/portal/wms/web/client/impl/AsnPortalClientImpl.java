package com.dt.portal.wms.web.client.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.asn.*;
import com.dt.component.common.enums.base.AsnThermostaticStrategyEnum;
import com.dt.component.common.enums.base.ThermostaticEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.param.AsnDetailParam;
import com.dt.domain.bill.param.AsnThermostaticMaintainParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.IAsnBizClient;
import com.dt.platform.wms.dto.asn.AsnBizDTO;
import com.dt.platform.wms.dto.asn.AsnDetailBizDTO;
import com.dt.platform.wms.dto.asn.AsnDetailDataBizDTO;
import com.dt.platform.wms.dto.asn.AsnLogBizDTO;
import com.dt.platform.wms.dto.box.AnalysisCommitBizDTO;
import com.dt.platform.wms.dto.box.AnalysisErrorBizDTO;
import com.dt.platform.wms.form.asn.AsnAddForm;
import com.dt.platform.wms.form.asn.AsnLinkBillParam;
import com.dt.platform.wms.form.asn.AsnModifyForm;
import com.dt.platform.wms.form.asn.AsnOrderTagOperationParam;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.asn.ArrivalBatchParam;
import com.dt.platform.wms.param.asn.AsnBizParam;
import com.dt.platform.wms.param.asn.AsnCancelParam;
import com.dt.platform.wms.param.asn.AsnDetailBizParam;
import com.dt.portal.wms.web.client.IAsnPortalClient;
import com.dt.portal.wms.web.vo.asn.*;
import com.dt.portal.wms.web.vo.box.AnalysisErrorVO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/21 17:01
 */
@DubboService(version = "${dubbo.service.version}")
public class AsnPortalClientImpl implements IAsnPortalClient {

    @DubboReference
    IAsnBizClient iAsnBizClient;

    @Override
    @SoulClient(path = "/asn/page", desc = "ASN分页查询")
    public Result<PageVO<AsnVO>> queryPage(AsnBizParam param) {
        Result<IPage<AsnBizDTO>> result = iAsnBizClient.queryPage(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        PageVO<AsnVO> pageVO = AsnVO.buildPage(result.getData());
        return Result.success(pageVO);
    }

    @Override
    @SoulClient(path = "/asnLog/getPage", desc = "asn操作日志分页获取")
    public Result<PageVO<AsnLogVO>> logPage(AsnBizParam param) {
        Result<IPage<AsnLogBizDTO>> pageResult = iAsnBizClient.logPage(param);
        Page<AsnLogBizDTO> dtoPage = ConverterUtil.convertPage(pageResult.getData(), AsnLogBizDTO.class);
        Page<AsnLogVO> logVOPage = ConverterUtil.convertPage(dtoPage, AsnLogVO.class);
        List<AsnLogVO> asnLogVOS = AsnLogVO.buildVO(dtoPage.getRecords());
        logVOPage.setRecords(asnLogVOS);
        PageVO<AsnLogVO> pageVO = new PageVO<>();
        pageVO.setDataList(logVOPage.getRecords());
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(logVOPage.getSize());
        page.setTotalPage(logVOPage.getTotal());
        page.setTotalCount(logVOPage.getTotal());
        page.setCurrentPage(logVOPage.getCurrent());
        pageVO.setPage(page);
        return Result.success(pageVO);
    }

    @Override
    @SoulClient(path = "/asn/status", desc = "ASN状态码")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> queryStatus() {
        return iAsnBizClient.queryStatus();
    }

    @Override
    @SoulClient(path = "/asn/queryPriority", desc = "优先级")
    public Result<List<IdNameVO>> queryPriority() {
        return Result.success(IdNameVO.build(AsnPriorityEnum.class, "code", "message"));
    }

    @Override
    @SoulClient(path = "/asn/statusAll", desc = "ASN状态码")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> queryStatusAll() {
        return iAsnBizClient.queryStatusAll();
    }

    @Override
    @SoulClient(path = "/asn/xtBatchComplete", desc = "销退仓批量完成收货")
    public Result<Boolean> xtBatchComplete(AsnBizParam param) {
        if (StringUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getAsnIdList())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return iAsnBizClient.xtBatchComplete(param);
    }

    @Override
    @SoulClient(path = "/asn/xtBatchArrival", desc = "销退仓批量到货")
    public Result<Boolean> xtBatchArrival(ArrivalBatchParam param) {
        if (StringUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getAsnIdList())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return iAsnBizClient.xtBatchArrival(param);
    }

    @Override
    @SoulClient(path = "/asn/type", desc = "ASN单据类型")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> queryType() {
        return iAsnBizClient.queryType();
    }

    @Override
    @SoulClient(path = "/asn/typeAll", desc = "ASN单据类型")
    public Result<List<IdNameVO>> queryTypeAll() {
        return iAsnBizClient.queryTypeAll();
    }

    @Override
    @SoulClient(path = "/asn/arrival", desc = "ASN确认到货")
    public Result<Boolean> arrival(@Validated AsnDetailBizParam param) {
        Result<Boolean> result = iAsnBizClient.arrival(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/asn/detail", desc = "ASN明细")
    public Result<AsnDataDetailVO> getDetail(@Validated AsnDetailBizParam param) {
        Result<AsnDetailDataBizDTO> result = iAsnBizClient.getDetail(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(AsnDataDetailVO.buildVO(result.getData()));
    }

    @Override
    @SoulClient(path = "/asn/detailForTally", desc = "ASN明细")
    public Result<AsnDataDetailForTallyVO> getDetailTally(AsnDetailBizParam param) {
        Result<AsnDetailDataBizDTO> result = iAsnBizClient.getDetailForTally(param);

        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        List<AsnDetailForTallyVO> asnDetailForTallyVOS = ConverterUtil.convertList(result.getData().getDetailList(), AsnDetailForTallyVO.class);
        AsnDataDetailForTallyVO asnDataDetailForTallyVO = new AsnDataDetailForTallyVO();
        if (!CollectionUtils.isEmpty(result.getData().getDetailList())) {
            asnDataDetailForTallyVO.setCargoCode(result.getData().getDetailList().get(0).getCargoCode());
        }
        asnDataDetailForTallyVO.setDetails(asnDetailForTallyVOS);
        return Result.success(asnDataDetailForTallyVO);
    }

    @Override
    @SoulClient(path = "/asn/detail/page", desc = "ASN明细分页查询")
    public Result<PageVO<AsnDetailVO>> queryDetailPage(AsnBizParam param) {
        if (param == null || StringUtils.isEmpty(param.getAsnId())) {
            throw new BaseException(BaseBizEnum.TIP, "到货通知单号不能为空");
        }
        Result<IPage<AsnDetailBizDTO>> result = iAsnBizClient.queryDetailPage(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        PageVO<AsnDetailVO> pageVO = AsnDetailVO.buildPage(result.getData());
        return Result.success(pageVO);
    }

    @Override
    @SoulClient(path = "/asn/cancel", desc = "取消ASN")
    public Result<Boolean> cancel(@Validated AsnDetailBizParam param) {
        Result<Boolean> result = iAsnBizClient.cancel(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/asn/linkDeclarationOrderNo", desc = "关联报关单号")
    public Result<Boolean> linkDeclarationOrderNo(AsnBizParam param) {
        if (param == null || StringUtils.isEmpty(param.getAsnId())
                || StringUtils.isEmpty(param.getDeclarationOrderNo())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return iAsnBizClient.linkDeclarationOrderNo(param);
    }

    @Override
    @SoulClient(path = "/asn/complete", desc = "完成Asn")
    public Result<Boolean> complete(@Validated AsnDetailBizParam param) {
        Result<Boolean> result = iAsnBizClient.complete(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/asn/batchComplete", desc = "完成Asn")
    public Result<Boolean> batchComplete(CodeListParam param) {
        Result<Boolean> result = iAsnBizClient.batchComplete(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/asn/batchArrival", desc = "批量到货")
    public Result<Boolean> batchArrival(ArrivalBatchParam param) {
        if (param == null || CollectionUtils.isEmpty(param.getAsnIdList())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = iAsnBizClient.batchArrival(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/asn/cancelReceipt", desc = "取消收货")
    public Result<Boolean> cancelReceipt(@Validated AsnDetailBizParam param) {
        Result<Boolean> result = iAsnBizClient.cancelReceipt(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/asn/wmsAddAsn", desc = "WMS创建入库单")
    public Result<Boolean> wmsAddAsn(@Validated AsnAddForm from) {
        Result<Boolean> result = iAsnBizClient.wmsAddAsn(from);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/asn/wmsModifyAsn", desc = "WMS修改入库单")
    public Result<Boolean> wmsModifyAsn(@Validated AsnModifyForm from) {
        Result<Boolean> result = iAsnBizClient.wmsModifyAsn(from);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/asn/getAsnModifyDetail", desc = "WMS获取编辑入库单")
    public Result<AsnModifyVO> getAsnModifyDetail(@Validated CodeParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "通知单号不能为空");
        }
        Result<AsnDetailDataBizDTO> result = iAsnBizClient.getAsnModifyDetail(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        AsnDetailDataBizDTO dataBizDTO = result.getData();
        //格式化
        AsnModifyVO asnModifyVO = ConverterUtil.convert(dataBizDTO.getAsnDTO(), AsnModifyVO.class);
        asnModifyVO.setTypeName(AsnTypeEnum.fromCode(asnModifyVO.getType()).getMessage());
        asnModifyVO.setStatusName(AsnStatusEnum.fromCode(asnModifyVO.getStatus()).getMessage());
        asnModifyVO.setExpRecDateFormat(ConverterUtil.convertVoTime(dataBizDTO.getAsnDTO().getExpRecDate()));
        asnModifyVO.setCreatedTime(ConverterUtil.convertVoTime(dataBizDTO.getAsnDTO().getCreatedTime()));
        asnModifyVO.setUpdatedTime(ConverterUtil.convertVoTime(dataBizDTO.getAsnDTO().getUpdatedTime()));

        List<AsnModifyDetailVO> detailVOList = ConverterUtil.convertList(dataBizDTO.getDetailList(), AsnModifyDetailVO.class);
        detailVOList.forEach(a -> {
            a.setSkuQualityName(SkuQualityEnum.getEnum(a.getSkuQuality()).getMessage());
        });
        asnModifyVO.setDetailVOList(detailVOList);
        return Result.success(asnModifyVO);
    }

    @Override
    @SoulClient(path = "/asn/wmsCancelAsn", desc = "WMS取消入库单")
    public Result<String> wmsCancelAsn(@Validated AsnCancelParam param) {
        Result<String> result = iAsnBizClient.wmsCancelAsn(param);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/asn/queryCustomsClearance", desc = "获取清关类型")
    public Result<List<IdNameVO>> queryCustomsClearance() {
        return Result.success(IdNameVO.build(AsnCustomsClearanceEnum.class, "code", "message").stream()
                .filter(a -> !StringUtils.isEmpty(a.getId())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/asn/queryCustomsClearanceAll", desc = "获取清关类型全部")
    public Result<List<IdNameVO>> queryCustomsClearanceAll() {
        return Result.success(IdNameVO.buildAll(AsnCustomsClearanceEnum.class, "code", "message").stream()
                .filter(a -> !StringUtils.isEmpty(a.getId())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/asn/queryAsnOrderTag", desc = "获取全部订单标记")
    public Result<List<IdNameVO>> queryAsnOrderTag() {
        return Result.success(IdNameVO.build(AsnOrderTagEnum.class, "code", "desc").stream()
                .filter(a -> !StringUtils.isEmpty(a.getId())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/asn/queryAsnOrderSpecialTag", desc = "获取特定标记")
    public Result<List<IdNameVO>> queryAsnOrderSpecialTag() {
        List<AsnOrderTagEnum> asnOrderTagEnumList = new ArrayList<>();
        asnOrderTagEnumList.add(AsnOrderTagEnum.RECEIVE_BEFORE_REVIEW);
        return Result.success(IdNameVO.build(asnOrderTagEnumList, "code", "desc"));
    }

    @Override
    @SoulClient(path = "/asn/modifyOrderTag", desc = "修改入库单的订单tag")
    public Result<Boolean> modifyOrderTag(AsnOrderTagOperationParam param) throws Exception {
        if (param == null || StringUtils.isEmpty(param.getAsnId())) {
            throw new BaseException(BaseBizEnum.TIP, "单号不能为空");
        }
        Result<Boolean> result = iAsnBizClient.modifyOrderTag(param);
        return result;
    }

    @Override
    @SoulClient(path = "/asn/getOrderTagPartToOperationOccupy", desc = "获取当前订单tag")
    public Result<List<Integer>> getOrderTagPartToOperationOccupy(CodeParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<List<Integer>> result = iAsnBizClient.getOrderTagPartToOperationOccupy(param);
        return result;
    }

    @Override
    @SoulClient(path = "/asn/getCustomsClearanceStatus", desc = "获取清关状态")
    public Result<List<IdNameVO>> getCustomsClearanceStatus() {
        return Result.success(IdNameVO.build(CustomsClearanceStatusEnum.class, "code", "message").stream()
                .filter(a -> !StringUtils.isEmpty(a.getId())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/asn/getCustomsClearanceStatusAll", desc = "获取清关状态全部")
    public Result<List<IdNameVO>> getCustomsClearanceStatusAll() {
        return Result.success(IdNameVO.buildAll(CustomsClearanceStatusEnum.class, "code", "message").stream()
                .filter(a -> !StringUtils.isEmpty(a.getId())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/asn/getDeclarationMethod", desc = "获取申报方式")
    public Result<List<IdNameVO>> getDeclarationMethod() {
        return Result.success(IdNameVO.build(DeclarationMethodEnum.class, "code", "message").stream()
                .filter(a -> !StringUtils.isEmpty(a.getId())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/asn/getDeclarationMethodAll", desc = "获取申报方式全部")
    public Result<List<IdNameVO>> getDeclarationMethodAll() {
        return Result.success(IdNameVO.buildAll(DeclarationMethodEnum.class, "code", "message").stream()
                .filter(a -> !StringUtils.isEmpty(a.getId())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/asn/getAnalysisError", desc = "获取解析异常的数据")
    public Result<AnalysisErrorVO> getAnalysisError(CodeParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "通知单号不能为空");
        }
        Result<AnalysisErrorBizDTO> result = iAsnBizClient.getAnalysisError(param);
        return Result.success(ConverterUtil.convert(result.getData(), AnalysisErrorVO.class));
    }

    @Override
    @SoulClient(path = "/asn/commitAnalysisData", desc = "提交解析异常的数据")
    public Result<Boolean> commitAnalysisData(AnalysisCommitBizDTO commitBizDTO) {
        if (StringUtils.isEmpty(commitBizDTO) || StringUtils.isEmpty(commitBizDTO.getBillNo())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = iAsnBizClient.commitAnalysisData(commitBizDTO);
        return result;
    }

    @Override
    @SoulClient(path = "/asn/genTally", desc = "生成理货报告")
    public Result<Boolean> genTally(CodeParam from) {
        if (StringUtils.isEmpty(from) || StringUtils.isEmpty(from.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = iAsnBizClient.genTally(from);
        return result;
    }

    @Override
    @SoulClient(path = "/asn/releaseStock", desc = "先收后审释放库存")
    public Result<Boolean> releaseStock(CodeParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = iAsnBizClient.releaseStock(param);
        return result;
    }

    @Override
    @SoulClient(path = "/asn/linkBill", desc = "关联单据号")
    public Result<Boolean> linkBill(AsnLinkBillParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getAsnId())
                || StringUtils.isEmpty(param.getLinkAsnId())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = iAsnBizClient.linkBill(param);
        return result;
    }

    @Override
    @SoulClient(path = "/asn/thermostaticDetailPage", desc = "恒温标志明细分页")
    public Result<PageVO<AsnDetailVO>> thermostaticDetailPage(AsnDetailParam asnDetailParam) {
        Result<IPage<AsnDetailBizDTO>> result = iAsnBizClient.thermostaticDetailPag(asnDetailParam);
        if (!result.checkSuccess()) {
            throw new BaseException(BaseBizEnum.REMOTE_CALL_ERROR);
        }
        PageVO<AsnDetailVO> pageVO = ConverterUtil.convertPageVO(result.getData(), AsnDetailVO.class);
        return Result.success(pageVO);
    }

    @Override
    @SoulClient(path = "/asn/thermostaticMaintain", desc = "恒温标志保存")
    public Result<Boolean> thermostaticMaintain(AsnThermostaticMaintainParam param) {
        return iAsnBizClient.thermostaticMaintain(param);
    }

    @Override
    @SoulClient(path = "/asn/zeroReceive", desc = "零理货")
    public Result<String> zeroReceive(AsnBizParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getAsnId())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<String> result = iAsnBizClient.zeroReceive(param);
        return result;
    }


    public static void main(String[] args) {
        AsnDetailParam asnDetailParam = new AsnDetailParam();
        asnDetailParam.setAsnId("aaa");
        asnDetailParam.setUpcCodeList(ListUtil.toList("ddd"));
        asnDetailParam.setCurrentPage(1);
        asnDetailParam.setPageSize(20);
        System.out.println(JSONUtil.toJsonStr(asnDetailParam));
        AsnThermostaticMaintainParam asnThermostaticMaintainParam = new AsnThermostaticMaintainParam();
        asnThermostaticMaintainParam.setAsnId("aaa");
        HashMap<String, String> skuThermostaticStrategy = new HashMap<>();
        skuThermostaticStrategy.put("aaaa", "Y");
        skuThermostaticStrategy.put("bbbb", "N");
        asnThermostaticMaintainParam.setSkuThermostaticStrategy(skuThermostaticStrategy);
        asnThermostaticMaintainParam.setThermostaticStrategy(AsnThermostaticStrategyEnum.PART.getType());
        System.out.println(JSONUtil.toJsonStr(asnThermostaticMaintainParam));
    }

}
