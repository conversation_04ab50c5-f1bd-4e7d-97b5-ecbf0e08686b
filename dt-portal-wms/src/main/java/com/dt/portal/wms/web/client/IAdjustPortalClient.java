package com.dt.portal.wms.web.client;

import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.param.AdjustParam;
import com.dt.platform.wms.dto.adjust.AdjustCreateSkuLotBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.adjust.AdjustBizParam;
import com.dt.platform.wms.param.adjust.AdjustCreateSkuLotParam;
import com.dt.platform.wms.param.adjust.AdjustModifyBizParam;
import com.dt.portal.wms.web.vo.adjust.AdjustVO;

import java.util.List;

public interface IAdjustPortalClient {

    /**
     * 获取移位单状态下拉列表
     *
     * @return
     */
    Result<List<IdNameVO>> getStatusList();

    Result<List<IdNameVO>> getAllStatusList();

    /**
     * 功能描述:  调整单类型接口
     * 创建时间:  2021/2/22 8:28 下午
     *
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.component.common.vo.IdNameVO>>
     * <AUTHOR>
     */
    Result<List<IdNameVO>> getTypeList();

    /**
     * 获取调整原因下拉列表
     *
     * @return
     */
    Result<List<IdNameVO>> getReasonList();

    /**
     * 新增
     *
     * @param param
     * @return
     */
    Result<Boolean> create(AdjustBizParam param);

    /**
     * 修改
     *
     * @param param
     * @return
     */
    Result<Boolean> modify(AdjustModifyBizParam param);

    /**
     * 移位单分页列表
     *
     * @param param
     * @return
     */
    Result<PageVO<AdjustVO>> getPage(AdjustBizParam param);

    /**
     * 获取详情
     *
     * @param param
     * @return
     */
    Result<AdjustVO> getDetail(CodeParam param);

    /**
     * 完成移位单
     *
     * @param param
     * @return
     */
    Result<Boolean> complete(CodeParam param);

    /**
     * 审核移位单
     *
     * @param param
     * @return
     */
    Result<Boolean> audit(CodeParam param);

    /**
     * 取消移位单
     *
     * @param param
     * @return
     */
    Result<Boolean> cancel(CodeParam param);


    /**
     * 调整单-提交审核
     *
     * @param param
     * @return
     */
    Result<Boolean> commitAudit(CodeParam param);

    /**
     * 调整单-仓内审核
     *
     * @param param
     * @return
     */
    Result<Boolean> innerAudit(CodeParam param);

    Result<List<IdNameVO>> getBusinessTypeList();

    Result<List<IdNameVO>> getDetailReasonList();

    Result<List<IdNameVO>> getRPList();

    /**
     * @param adjustCreateSkuLotParam
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.adjust.AdjustCreateSkuLotBizDTO>
     * <AUTHOR>
     * @describe: 调整单获取批次信息
     * @date 2024/6/6 9:26
     */
    Result<AdjustCreateSkuLotBizDTO> querySkuInfoByAdjust(AdjustCreateSkuLotParam adjustCreateSkuLotParam);

    /**
     * @param adjustCreateSkuLotParam
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @describe: 调整单生成批次
     * @date 2024/6/6 9:26
     */
    Result<String> createSkuLotByAdjust(AdjustCreateSkuLotParam adjustCreateSkuLotParam);

    Result<List<IdNameVO>> queryAllTag();

    Result<List<IdNameVO>> queryTag(AdjustParam param);

    Result<Boolean> modifyTag(AdjustParam param);
}
