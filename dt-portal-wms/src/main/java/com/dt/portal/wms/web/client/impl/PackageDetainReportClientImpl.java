package com.dt.portal.wms.web.client.impl;

import com.dt.domain.bill.param.PackageDetainParam;
import com.dt.portal.wms.web.client.IPackageDetainReportClient;
import com.dt.portal.wms.web.vo.pkg.PackageDetainVO;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.Collections;
import java.util.List;

@DubboService
public class PackageDetainReportClientImpl implements IPackageDetainReportClient {

    @Override
    public List<PackageDetainVO> report(PackageDetainParam packageDetainParam) {
        return Collections.emptyList();
    }
}
