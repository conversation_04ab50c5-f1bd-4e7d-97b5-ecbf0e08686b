package com.dt.portal.wms.web.client.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.OpTypeEnum;
import com.dt.component.common.enums.pkg.PackageUnitEnum;
import com.dt.component.common.enums.shelf.ShelfMarkDetailEnum;
import com.dt.component.common.enums.shelf.ShelfMarkEnum;
import com.dt.component.common.enums.shelf.ShelfStatusEnum;
import com.dt.component.common.enums.shelf.ShelfTypeEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.DateDescUtil;
import com.dt.platform.wms.client.ISaleReturnShelfBizClient;
import com.dt.platform.wms.client.IShelfBizClient;
import com.dt.platform.wms.dto.shelf.SaleReturnBatchShelfTipDTO;
import com.dt.platform.wms.dto.shelf.ShelfBizDTO;
import com.dt.platform.wms.dto.shelf.ShelfDetailBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.shelf.ShelfBizParam;
import com.dt.platform.wms.param.shelf.ShelfCompleteParam;
import com.dt.platform.wms.param.shelf.ShelfDetailCompleteBizParam;
import com.dt.platform.wms.param.shelf.ShelfModifyOpTypeParam;
import com.dt.portal.wms.web.client.IShelfPortalClient;
import com.dt.portal.wms.web.vo.shelf.ShelfDetailVO;
import com.dt.portal.wms.web.vo.shelf.ShelfVO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
public class ShelfPortalClient implements IShelfPortalClient {

    @DubboReference
    private IShelfBizClient shelfBizClient;

    @DubboReference(timeout = 90000)
    private ISaleReturnShelfBizClient saleReturnShelfBizClient;

    @Override
    @SoulClient(path = "/shelf/getStatusList", desc = "上架单-获取状态下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getStatusList() {
        return Result.success(IdNameVO.build(ShelfStatusEnum.class, "status", "name"));
    }

    @Override
    @SoulClient(path = "/shelf/getAllStatusList", desc = "上架单-获取状态下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getAllStatusList() {
        return Result.success(IdNameVO.buildAll(ShelfStatusEnum.class, "status", "name"));
    }

    @Override
    @SoulClient(path = "/shelf/getOpTypeList", desc = "上架单-获取上架方式下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getOpTypeList() {
        return Result.success(IdNameVO.build(OpTypeEnum.class, "type", "name"));
    }

    @Override
    @SoulClient(path = "/shelf/getMark", desc = "上架单标记")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getMark() {
        return Result.success(IdNameVO.build(ShelfMarkEnum.class, "code", "desc"));
    }

    @Override
    @SoulClient(path = "/shelf/getAllOpTypeList", desc = "上架单-获取上架方式下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getAllOpTypeList() {
        return Result.success(IdNameVO.buildAll(OpTypeEnum.class, "type", "name"));
    }


    @Override
    @SoulClient(path = "/shelf/getShelfTypeList", desc = "上架单-获取上架单类型下拉列表")
    public Result<List<IdNameVO>> getShelfTypeList() {
        return Result.success(IdNameVO.build(ShelfTypeEnum.class, "type", "name"));
    }

    @Override
    @SoulClient(path = "/shelf/getAllShelfTypeList", desc = "上架单-获取上架单类型下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getAllShelfTypeList() {
        return Result.success(IdNameVO.buildAll(ShelfTypeEnum.class, "type", "name"));
    }

    @Override
    @SoulClient(path = "/shelf/modifyShelfOpType", desc = "上架单-获取详情")
    public Result<Boolean> modifyShelfOpType(ShelfModifyOpTypeParam param) {
        return shelfBizClient.modifyShelfOpType(param);
    }

    @Override
    @SoulClient(path = "/shelf/getDetail", desc = "上架单-获取详情")
    public Result<ShelfVO> getDetail(@Validated CodeParam param) {
        ShelfBizParam shelfBizParam = new ShelfBizParam();
        shelfBizParam.setCode(param.getCode());

        Result<ShelfBizDTO> dtoResult = shelfBizClient.getDetail(shelfBizParam);

        ShelfBizDTO shelfBiz = dtoResult.getData();
        ShelfVO shelfVO = getShelfVO(shelfBiz);
        if (ShelfMarkEnum.NumToEnum(shelfVO.getMark()).contains(ShelfMarkEnum.HOLD)) {
            shelfVO.setHold(true);
        }
        return Result.success(shelfVO);
    }

    @Override
    @SoulClient(path = "/shelf/getPage", desc = "上架单-获取分页列表")
    public Result<PageVO<ShelfVO>> getPage(ShelfBizParam param) {

        Result<Page<ShelfBizDTO>> pageResult = shelfBizClient.getPage(param);
        Page<ShelfBizDTO> shelfBizPage = pageResult.getData();
        List<ShelfBizDTO> shelfBizList = shelfBizPage.getRecords();
        List<ShelfVO> voList = new ArrayList<>();
        //格式化数据
        if (!CollectionUtils.isEmpty(shelfBizList)) {
            voList = shelfBizList.stream()
                    .flatMap(a -> Stream.of(getShelfVO(a)))
                    .collect(Collectors.toList());
        }
        //组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(shelfBizPage.getSize());
        page.setCurrentPage(shelfBizPage.getCurrent());
        page.setTotalPage(shelfBizPage.getPages());
        page.setTotalCount(shelfBizPage.getTotal());

        PageVO<ShelfVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);

        Result<PageVO<ShelfVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);

        return result;
    }

//    @Override
//    @SoulClient(path = "/shelf/complete", desc = "上架单-完成上架单")
//    public Result<Boolean> complete(@Validated CodeParam param) {
//        return shelfBizClient.complete(param);
//    }


    @Override
    @SoulClient(path = "/shelf/completeWholeShelf", desc = "上架单-完成上架单")
    public Result<Boolean> completeWholeShelf(@Validated ShelfCompleteParam param) {
        return shelfBizClient.completeWholeShelf(param);
    }

    @Override
    @SoulClient(path = "/shelf/completeDetail", desc = "上架单-完成明细")
    public Result<Boolean> completeDetail(@Validated ShelfDetailCompleteBizParam param) {
        return shelfBizClient.completeDetail(param);
    }

    @Override
    @SoulClient(path = "/shelf/commitTaoTianShelf", desc = "上架单-申请")
    public Result<Boolean> commitTaoTianShelf(ShelfBizParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return shelfBizClient.commitTaoTianShelf(param);
    }
    @Override
    @SoulClient(path = "/shelf/commitTaoTianShelfCheckSku", desc = "上架单-校验商品")
    public Result<Boolean> commitTaoTianShelfCheckSku(ShelfBizParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return shelfBizClient.commitTaoTianShelfCheckSku(param);
    }

    private ShelfVO getShelfVO(ShelfBizDTO shelfBiz) {
        ShelfVO shelfVO = ConverterUtil.convert(shelfBiz, ShelfVO.class);
        if (!ObjectUtils.isEmpty(shelfVO)) {
            shelfVO.setTypeName(ShelfTypeEnum.getEnum(shelfVO.getType()).getName());
            shelfVO.setStatusName(ShelfStatusEnum.getEnum(shelfVO.getStatus()).getName());
            shelfVO.setPackageUnitName(PackageUnitEnum.fromCode(shelfBiz.getPackageUnitCode()).getMessage());
            shelfVO.setOpTypeName(StringUtils.isEmpty(shelfVO.getOpType()) ? "" : OpTypeEnum.getEnum(shelfVO.getOpType()).getName());
            shelfVO.setSkuQualityName(SkuQualityEnum.getEnum(shelfVO.getSkuQuality()).getMessage());
            shelfVO.setPrintStatusName(StringUtils.isEmpty(shelfVO.getPrintStatus()) ? "" : "已打印");
            shelfVO.setCreatedTime(ConverterUtil.convertVoTime(shelfBiz.getCreatedTime()));
            shelfVO.setUpdatedTime(ConverterUtil.convertVoTime(shelfBiz.getUpdatedTime()));
            shelfVO.setPrintDate(ConverterUtil.convertVoTime(shelfBiz.getPrintDate()));
            shelfVO.setCompleteDate(ConverterUtil.convertVoTime(shelfBiz.getCompleteDate()));

            Set<ShelfMarkEnum> markEnums = ShelfMarkEnum.NumToEnum(shelfVO.getMark());
            shelfVO.setMarkDesc("");
            if (!CollectionUtils.isEmpty(markEnums)) {
                shelfVO.setMarkDesc(markEnums.stream().map(ShelfMarkEnum::getDesc).collect(Collectors.joining("|")));
            }

            List<ShelfDetailBizDTO> detailList = shelfBiz.getDetailList();
            if (!CollectionUtils.isEmpty(detailList)) {
                List<ShelfDetailVO> detailVOList = detailList.stream()
                        .flatMap(a -> {
                            ShelfDetailVO detail = ConverterUtil.convert(a, ShelfDetailVO.class);
                            if (!ObjectUtils.isEmpty(detail)) {
                                detail.setWarehouseName(shelfVO.getWarehouseName());
                                detail.setCargoName(shelfVO.getCargoName());
                                detail.setStatusName(ShelfStatusEnum.getEnum(detail.getStatus()).getName());
                                detail.setCreatedTime(ConverterUtil.convertVoTime(a.getCreatedTime()));
                                detail.setUpdatedTime(ConverterUtil.convertVoTime(a.getUpdatedTime()));
                                detail.setReceiveDate(DateDescUtil.normalDateStr(a.getReceiveDate()));
                                detail.setExpireDate(DateDescUtil.normalDateStr(a.getExpireDate()));
                                detail.setManufDate(DateDescUtil.normalDateStr(a.getManufDate()));
                                detail.setWithdrawDate(ConverterUtil.convertVoTime(a.getWithdrawDate(), a.getWithdrawDateFormat()));
                                detail.setSkuQualityName(SkuQualityEnum.getEnum(detail.getSkuQuality()).getMessage());
                                detail.setMarkDesc("");
                                detail.setInventoryType(a.getInventoryType());
                                detail.setInventoryTypeDesc(InventoryTypeEnum.desc(a.getInventoryType()));
                                Set<ShelfMarkDetailEnum> detailEnums = ShelfMarkDetailEnum.NumToEnum(detail.getMark());
                                if (!CollectionUtils.isEmpty(detailEnums)) {
                                    detail.setMarkDesc(detailEnums.stream().map(ShelfMarkDetailEnum::getDesc).collect(Collectors.joining("|")));
                                }
                            }
                            return Stream.of(detail);
                        })
                        .collect(Collectors.toList());
                shelfVO.setDetailList(detailVOList);
            }
        }
        return shelfVO;
    }

    @Override
    @SoulClient(path = "/shelf/saleReturnBatchShelf", desc = "上架单-销退上架")
    public Result<Boolean> saleReturnBatchShelf(ShelfBizParam shelfBizParam) {
        return saleReturnShelfBizClient.saleReturnShelf(shelfBizParam);
    }

    @Override
    @SoulClient(path = "/shelf/saleReturnBatchShelfTip", desc = "上架单-销退上架提示")
    public Result<List<SaleReturnBatchShelfTipDTO>> saleReturnBatchShelfTip(ShelfBizParam shelfBizParam) {
        return saleReturnShelfBizClient.saleReturnShelfTip(shelfBizParam);
    }

}
