package com.dt.portal.wms.web.client.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.*;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.transfer.TransferTagEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.param.AdjustParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.IAdjustBizClient;
import com.dt.platform.wms.client.ICargoConfigBizClient;
import com.dt.platform.wms.dto.adjust.AdjustBizDTO;
import com.dt.platform.wms.dto.adjust.AdjustCreateSkuLotBizDTO;
import com.dt.platform.wms.dto.adjust.AdjustDetailBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.adjust.AdjustBizParam;
import com.dt.platform.wms.param.adjust.AdjustCreateSkuLotParam;
import com.dt.platform.wms.param.adjust.AdjustModifyBizParam;
import com.dt.portal.wms.web.client.IAdjustPortalClient;
import com.dt.portal.wms.web.vo.adjust.AdjustDetailVO;
import com.dt.portal.wms.web.vo.adjust.AdjustVO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
public class AdjustPortalClient implements IAdjustPortalClient {

    @DubboReference
    private IAdjustBizClient adjustBizClient;

    @DubboReference
    private ICargoConfigBizClient cargoConfigBizClient;

    public static AdjustVO getAdjustVO(AdjustBizDTO adjustBiz) {
        AdjustVO adjustVO = ConverterUtil.convert(adjustBiz, AdjustVO.class);
        if (!ObjectUtils.isEmpty(adjustVO)) {
            Set<AdjustTagEnum> transferTagEnums = AdjustTagEnum.NumToEnum(adjustBiz.getTag());
            adjustVO.setTagName(transferTagEnums.stream().map(AdjustTagEnum::getDesc).collect(Collectors.joining("|")));
            adjustVO.setStatusName(AdjustStatusEnum.getEnum(adjustVO.getStatus()).getName());
            adjustVO.setReasonName(AdjustReasonEnum.getEnum(adjustVO.getReason()).getName());
            adjustVO.setTypeName(AdjustTypeEnum.getEnum(adjustVO.getType()).getName());
            adjustVO.setCheckerDate(ConverterUtil.convertVoTime(adjustBiz.getCheckerDate()));
            adjustVO.setCompleteDate(ConverterUtil.convertVoTime(adjustBiz.getCompleteDate()));
            adjustVO.setCreatedTime(ConverterUtil.convertVoTime(adjustBiz.getCreatedTime()));
            adjustVO.setUpdatedTime(ConverterUtil.convertVoTime(adjustBiz.getUpdatedTime()));
            if (!StringUtils.isEmpty(adjustVO.getBusinessType())) {
                adjustVO.setBusinessTypeDesc(AdjustBusinessTypeEnum.getEnum(adjustVO.getBusinessType()).getName());
            } else {
                adjustVO.setBusinessTypeDesc("");
            }
            List<AdjustDetailBizDTO> detailList = adjustBiz.getDetailList();
            if (!CollectionUtils.isEmpty(detailList)) {
                List<AdjustDetailVO> detailVOList = detailList.stream()
                        .flatMap(a -> {
                            AdjustDetailVO detail = ConverterUtil.convert(a, AdjustDetailVO.class);
                            if (!ObjectUtils.isEmpty(detail)) {
                                detail.setInventoryTypeDesc(InventoryTypeEnum.desc(detail.getInventoryType()));
                                detail.setStatusName(AdjustStatusEnum.getEnum(detail.getStatus()).getName());
                                detail.setCreatedTime(ConverterUtil.convertVoTime(a.getCreatedTime()));
                                detail.setUpdatedTime(ConverterUtil.convertVoTime(a.getUpdatedTime()));

                                detail.setReasonDesc("");
                                detail.setRpDesc("");
                                if (!StringUtils.isEmpty(a.getReason())) {
                                    detail.setReasonDesc(AdjustDetailReasonEnum.getEnum(a.getReason()).getName());
                                }
                                if (!StringUtils.isEmpty(a.getRp())) {
                                    detail.setRpDesc(AdjustDetailRPEnum.getEnum(a.getRp()).getName());
                                }
                            }
                            return Stream.of(detail);
                        })
                        .collect(Collectors.toList());
                adjustVO.setDetailList(detailVOList);
            }
        }
        return adjustVO;
    }

    @Override
    @SoulClient(path = "/adjust/getStatusList", desc = "调整单-获取状态下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getStatusList() {
        List<IdNameVO> list = IdNameVO.build(AdjustStatusEnum.class, "status", "name");
        List<String> statusList = Arrays.asList(AdjustStatusEnum.AUDIT_FAILURE.getStatus(), AdjustStatusEnum.STATUS_WAIT.getStatus(), AdjustStatusEnum.STATUS_CHECKED.getStatus());
        list.removeIf(vo -> statusList.contains((String) vo.getId()));
        return Result.success(list);
    }

    @Override
    @SoulClient(path = "/adjust/getAllStatusList", desc = "调整单-获取状态下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getAllStatusList() {
        List<IdNameVO> list = IdNameVO.buildAll(AdjustStatusEnum.class, "status", "name");
        List<String> statusList = Arrays.asList(AdjustStatusEnum.AUDIT_FAILURE.getStatus(), AdjustStatusEnum.STATUS_WAIT.getStatus(), AdjustStatusEnum.STATUS_CHECKED.getStatus());
        list.removeIf(vo -> statusList.contains((String) vo.getId()));
        return Result.success(list);
    }

    @Override
    @SoulClient(path = "/adjust/getTypeList", desc = "调整单-获取类型接口")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getTypeList() {
        return Result.success(IdNameVO.build(AdjustTypeEnum.class, "status", "name"));
    }

    @Override
    @SoulClient(path = "/adjust/getReasonList", desc = "调整单-获取调整原因下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getReasonList() {
        return Result.success(IdNameVO.build(AdjustReasonEnum.class, "code", "name"));
    }

    @Override
    @SoulClient(path = "/adjust/create", desc = "调整单管理-新增")
    public Result<Boolean> create(@Validated AdjustBizParam param) {
        return adjustBizClient.createV2(param);
    }

    @Override
    @SoulClient(path = "/adjust/modify", desc = "调整单管理-修改")
    public Result<Boolean> modify(@Validated AdjustModifyBizParam param) {
        AdjustBizParam zoneBizParam = ConverterUtil.convert(param, AdjustBizParam.class);
        return adjustBizClient.modify(zoneBizParam);
    }

    @Override
    @SoulClient(path = "/adjust/getDetail", desc = "调整单-获取详情")
    public Result<AdjustVO> getDetail(@Validated CodeParam param) {
        AdjustBizParam adjustBizParam = new AdjustBizParam();
        adjustBizParam.setCode(param.getCode());

        Result<AdjustBizDTO> dtoResult = adjustBizClient.getDetail(adjustBizParam);

        AdjustBizDTO adjustBiz = dtoResult.getData();
        return Result.success(getAdjustVO(adjustBiz));
    }

    @Override
    @SoulClient(path = "/adjust/getPage", desc = "调整单-获取分页列表")
    public Result<PageVO<AdjustVO>> getPage(AdjustBizParam param) {

        Result<Page<AdjustBizDTO>> pageResult = adjustBizClient.getPage(param);

        Page<AdjustBizDTO> adjustBizPage = pageResult.getData();
        List<AdjustBizDTO> adjustBizList = adjustBizPage.getRecords();
        List<AdjustVO> voList = new ArrayList<>();

        //格式化数据
        if (!CollectionUtils.isEmpty(adjustBizList)) {
            adjustBizList = adjustBizPage.getRecords();
            voList = adjustBizList.stream()
                    .flatMap(a -> Stream.of(getAdjustVO(a)))
                    .collect(Collectors.toList());
        }

        //组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(adjustBizPage.getSize());
        page.setCurrentPage(adjustBizPage.getCurrent());
        page.setTotalPage(adjustBizPage.getPages());
        page.setTotalCount(adjustBizPage.getTotal());

        PageVO<AdjustVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);

        Result<PageVO<AdjustVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);

        return result;
    }

    @Override
    @SoulClient(path = "/adjust/complete", desc = "调整单-完成调整单")
    public Result<Boolean> complete(@Validated CodeParam param) {
        return adjustBizClient.complete(param);
    }

    @Override
    @SoulClient(path = "/adjust/audit", desc = "调整单-审核调整单")
    @Deprecated
    public Result<Boolean> audit(CodeParam param) {
        return adjustBizClient.audit(param);
    }

    @Override
    @SoulClient(path = "/adjust/cancel", desc = "调整单-取消调整单")
    public Result<Boolean> cancel(CodeParam param) {
        return adjustBizClient.cancel(param);
    }


    @Override
    @SoulClient(path = "/adjust/commitAudit", desc = "调整单-提交审核")
    public Result<Boolean> commitAudit(CodeParam param) {
        if (StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return adjustBizClient.commitAudit(param);
    }

    @Override
    @SoulClient(path = "/adjust/innerAudit", desc = "调整单-仓内审核")
    public Result<Boolean> innerAudit(CodeParam param) {
        if (StringUtils.isEmpty(param.getCode()) || StringUtils.isEmpty(param.getRemark()) || StringUtils.isEmpty(param.getPass())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        if (param.getRemark().length() >= 200) {
            throw new BaseException(BaseBizEnum.TIP, "审核说明过长");
        }
        return adjustBizClient.innerAudit(param);
    }

    @Override
    @SoulClient(path = "/adjust/getBusinessTypeList", desc = "调整单-业务场景")
    public Result<List<IdNameVO>> getBusinessTypeList() {
        return Result.success(IdNameVO.build(AdjustBusinessTypeEnum.class, "code", "name"));
    }

    @Override
    @SoulClient(path = "/adjust/getDetailReasonList", desc = "调整单-明细原因")
    public Result<List<IdNameVO>> getDetailReasonList() {
        return Result.success(IdNameVO.build(AdjustDetailReasonEnum.class, "code", "name"));
    }

    @Override
    @SoulClient(path = "/adjust/getRPList", desc = "调整单-明细责任方")
    public Result<List<IdNameVO>> getRPList() {
        return Result.success(IdNameVO.build(AdjustDetailRPEnum.class, "code", "name"));
    }

    @Override
    @SoulClient(path = "/adjust/querySkuInfoByAdjust", desc = "调整单-获取商品批次规则信息")
    public Result<AdjustCreateSkuLotBizDTO> querySkuInfoByAdjust(AdjustCreateSkuLotParam adjustCreateSkuLotParam) {
        if (null == adjustCreateSkuLotParam || StringUtils.isEmpty(adjustCreateSkuLotParam.getUpcCode()) || StringUtils.isEmpty(adjustCreateSkuLotParam.getCargoCode())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return adjustBizClient.querySkuInfoByAdjust(adjustCreateSkuLotParam);
    }

    @Override
    @SoulClient(path = "/adjust/createSkuLotByAdjust", desc = "调整单-生成批次")
    public Result<String> createSkuLotByAdjust(AdjustCreateSkuLotParam adjustCreateSkuLotParam) {
        if (null == adjustCreateSkuLotParam || StringUtils.isEmpty(adjustCreateSkuLotParam.getSkuCode()) || StringUtils.isEmpty(adjustCreateSkuLotParam.getCargoCode())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        return adjustBizClient.createSkuLotByAdjust(adjustCreateSkuLotParam);
    }

    @Override
    @SoulClient(path = "/adjust/queryAllTag", desc = "调整单所有标记")
    public Result<List<IdNameVO>> queryAllTag() {
        return Result.success(IdNameVO.build(AdjustTagEnum.class, "code", "desc"));
    }

    @Override
    @SoulClient(path = "/adjust/queryTag", desc = "当前调整单标记")
    public Result<List<IdNameVO>> queryTag(AdjustParam param) {
        return adjustBizClient.queryTag(param);
    }

    @Override
    @SoulClient(path = "/adjust/modifyTag", desc = "修改调整单标记")
    public Result<Boolean> modifyTag(AdjustParam param) {
        return adjustBizClient.modifyTag(param);
    }
}
