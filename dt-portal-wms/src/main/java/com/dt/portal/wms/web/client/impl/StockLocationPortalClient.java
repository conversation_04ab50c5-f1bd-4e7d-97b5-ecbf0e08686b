package com.dt.portal.wms.web.client.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.LocationTypeEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.pre.SkuIsPreEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.base.dto.SkuLotSourceExtraJsonDTO;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.DateDescUtil;
import com.dt.platform.wms.client.ISkuLotBizClient;
import com.dt.platform.wms.client.IStockLocationBizClient;
import com.dt.platform.wms.dto.lot.SkuLotSourceBizDTO;
import com.dt.platform.wms.dto.stock.StockLocationBizDTO;
import com.dt.platform.wms.dto.stock.StockLocationWithLotBizDTO;
import com.dt.platform.wms.dto.stock.StockStatisticBizDTO;
import com.dt.platform.wms.param.IdParam;
import com.dt.platform.wms.param.sku.SkuLotBizParam;
import com.dt.platform.wms.param.stock.StockLocationBizParam;
import com.dt.platform.wms.param.stock.StockLocationSkuLotParam;
import com.dt.platform.wms.param.stock.StockLocationStatisticsBizParam;
import com.dt.platform.wms.param.stock.StockLocationWithLotBizParam;
import com.dt.portal.wms.web.client.IStockLocationPortalClient;
import com.dt.portal.wms.web.vo.stock.StockLocationVO;
import com.dt.portal.wms.web.vo.stock.StockLocationWithLotVO;
import com.dt.portal.wms.web.vo.stock.StockStatisticVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class StockLocationPortalClient implements IStockLocationPortalClient {

    @DubboReference
    private IStockLocationBizClient stockLocationBizClient;
    @DubboReference
    private ISkuLotBizClient skuLotBizClient;

    @Override
    @SoulClient(path = "/stockLocation/getDetail", desc = "三级库存管理-获取详情")
    public Result<StockLocationVO> getDetail(@Validated IdParam param) {
        StockLocationBizParam stockBizParam = new StockLocationBizParam();
        stockBizParam.setId(param.getId());

        Result<StockLocationBizDTO> dtoResult = stockLocationBizClient.getDetail(stockBizParam);

        StockLocationBizDTO stockBiz = dtoResult.getData();

        return Result.success(getStockLocationVO(stockBiz));
    }

    @Override
    @SoulClient(path = "/stockLocation/getSkuLotList", desc = "三级库存管理-获取详情")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getSkuLotList(@Validated StockLocationSkuLotParam param) {
        return stockLocationBizClient.getSkuLotList(param);
    }

    @Override
    @SoulClient(path = "/stockLocation/getPage", desc = "三级库存管理-获取详情")
    public Result<PageVO<StockLocationVO>> getPage(StockLocationBizParam param) {
        Result<Page<StockLocationBizDTO>> pageResult = stockLocationBizClient.getPage(param);

        Page<StockLocationBizDTO> stockBizPage = pageResult.getData();
        List<StockLocationBizDTO> stockBizList = stockBizPage.getRecords();
        List<StockLocationVO> voList = new ArrayList<>();

        // 格式化数据
        if (!CollectionUtils.isEmpty(stockBizList)) {
            stockBizList = stockBizPage.getRecords();
            voList = stockBizList.stream().flatMap(a -> Stream.of(getStockLocationVO(a)))
                    .peek(stockLocationVO -> stockLocationVO.setLatestUpdateTimeDesc(DateDescUtil.normalTimeStr(stockLocationVO.getLatestUpdateTime())))
                    .collect(Collectors.toList());
        }

        // 组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(stockBizPage.getSize());
        page.setCurrentPage(stockBizPage.getCurrent());
        page.setTotalPage(stockBizPage.getPages());
        page.setTotalCount(stockBizPage.getTotal());

        PageVO<StockLocationVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);

        Result<PageVO<StockLocationVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);

        return result;
    }

    @Override
    @SoulClient(path = "/stockLocation/getStatistic", desc = "库存汇总")
    public Result<StockStatisticVO> getStatistic(StockLocationBizParam param) {
        Result<StockStatisticBizDTO> statistic = stockLocationBizClient.getStatistic(param);
        StockStatisticVO stockStatisticVO = ConverterUtil.convert(statistic.getData(), StockStatisticVO.class);
        return Result.success(stockStatisticVO);
    }

    @Override
    @SoulClient(path = "/stockLocation/getStatisticsPage", desc = "货主货位商品")
    public Result<PageVO<StockLocationVO>> getStatisticsPage(StockLocationStatisticsBizParam param) {

        Result<Page<StockLocationBizDTO>> pageResult = stockLocationBizClient.getStatisticsPage(param);

        Page<StockLocationBizDTO> stockBizPage = pageResult.getData();
        List<StockLocationBizDTO> stockBizList = stockBizPage.getRecords();
        List<StockLocationVO> voList = new ArrayList<>();

        // 格式化数据
        if (!CollectionUtils.isEmpty(stockBizList)) {
            stockBizList = stockBizPage.getRecords();
            voList = stockBizList.stream().flatMap(a -> Stream.of(getStockLocationVO(a))).collect(Collectors.toList());
        }

        // 组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(stockBizPage.getSize());
        page.setCurrentPage(stockBizPage.getCurrent());
        page.setTotalPage(stockBizPage.getPages());
        page.setTotalCount(stockBizPage.getTotal());

        PageVO<StockLocationVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);

        Result<PageVO<StockLocationVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);

        return result;
    }

    @Override
    @SoulClient(path = "/stockLocation/getLocationWithLotPage", desc = "货主货位批次商品")
    public Result<PageVO<StockLocationWithLotVO>> getLocationWithLotPage(StockLocationWithLotBizParam param) {

        Result<Page<StockLocationWithLotBizDTO>> pageResult = stockLocationBizClient.getLocationWithLotPage(param);

        Page<StockLocationWithLotBizDTO> stockBizPage = pageResult.getData();
        List<StockLocationWithLotBizDTO> stockBizList = stockBizPage.getRecords();
        List<StockLocationWithLotVO> voList = new ArrayList<>();

        // 格式化数据
        if (!CollectionUtils.isEmpty(stockBizList)) {
            stockBizList = stockBizPage.getRecords();
            voList = stockBizList.stream().flatMap(a -> Stream.of(getStockLocationWithLotVO(a)))
                    .peek(stockLocationWithLotVO -> stockLocationWithLotVO.setLatestUpdateTimeDesc(DateDescUtil.normalTimeStr(stockLocationWithLotVO.getLatestUpdateTime())))
                    .collect(Collectors.toList());
        }

        // 组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(stockBizPage.getSize());
        page.setCurrentPage(stockBizPage.getCurrent());
        page.setTotalPage(stockBizPage.getPages());
        page.setTotalCount(stockBizPage.getTotal());

        PageVO<StockLocationWithLotVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);

        Result<PageVO<StockLocationWithLotVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);

        return result;
    }

    @Override
    @SoulClient(path = "/stockLocation/getLocationWithLotPageStatistic", desc = "货主货位批次商品")
    public Result<StockStatisticVO> getLocationWithLotPageStatistic(StockLocationWithLotBizParam param) {
        Result<StockStatisticBizDTO> locationWithLotPageStatistic = stockLocationBizClient.getLocationWithLotPageStatistic(param);
        StockStatisticVO stockStatisticVO = ConverterUtil.convert(locationWithLotPageStatistic.getData(), StockStatisticVO.class);
        return Result.success(stockStatisticVO);
    }

    @Override
    @SoulClient(path = "/stockLocation/getStatisticsCargoLotPage", desc = "货主货位批次商品")
    public Result<PageVO<StockLocationWithLotVO>> getStatisticsCargoLotPage(SkuLotBizParam param) {

        Result<Page<StockLocationWithLotBizDTO>> pageResult = stockLocationBizClient.getStatisticsCargoLotPage(param);

        Page<StockLocationWithLotBizDTO> stockBizPage = pageResult.getData();
        List<StockLocationWithLotBizDTO> stockBizList = stockBizPage.getRecords();
        List<StockLocationWithLotVO> voList = new ArrayList<>();

        // 格式化数据
        if (!CollectionUtils.isEmpty(stockBizList)) {
            stockBizList = stockBizPage.getRecords();
            voList = stockBizList.stream().flatMap(a -> Stream.of(getStockLotVO(a))).collect(Collectors.toList());
        }

        // 组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(stockBizPage.getSize());
        page.setCurrentPage(stockBizPage.getCurrent());
        page.setTotalPage(stockBizPage.getPages());
        page.setTotalCount(stockBizPage.getTotal());

        PageVO<StockLocationWithLotVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);

        Result<PageVO<StockLocationWithLotVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);

        return result;
    }

    @Override
    @SoulClient(path = "/lot/getSkuLotSource", desc = "批次溯源信息")
    public Result<SkuLotSourceBizDTO> getSkuLotSource(StockLocationWithLotBizParam param) {
        if (param == null || StringUtils.isEmpty(param.getSkuLotNo())) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        Result<SkuLotSourceBizDTO> skuLotSourceVOResult = stockLocationBizClient.getSkuLotSource(param);

        return skuLotSourceVOResult;
    }

    private StockLocationWithLotVO getStockLotVO(StockLocationWithLotBizDTO a) {
        StockLocationWithLotVO stockVO = ConverterUtil.convert(a, StockLocationWithLotVO.class);
        if (!ObjectUtils.isEmpty(stockVO)) {
            stockVO.setSkuQualityName(SkuQualityEnum.getEnum(stockVO.getSkuQuality()).getMessage());
            stockVO.setCreatedTime(ConverterUtil.convertVoTime(a.getCreatedTime()));
            stockVO.setUpdatedTime(ConverterUtil.convertVoTime(a.getUpdatedTime()));
            stockVO.setExpireDateDesc(ConverterUtil.convertVoTime(a.getExpireDate(), a.getExpireDateFormat()));
            stockVO.setManufDateDesc(ConverterUtil.convertVoTime(a.getManufDate(), a.getManufDateFormat()));
            stockVO.setReceiveDateDesc(ConverterUtil.convertVoTime(a.getReceiveDate(), a.getReceiveDateFormat()));
            stockVO.setWithdrawDateDesc(ConverterUtil.convertVoTime(a.getWithdrawDate(), a.getWithdrawDateFormat()));

            stockVO.setInventoryTypeDesc("");
            if (!StringUtils.isEmpty(stockVO.getInventoryType())) {
                stockVO.setInventoryTypeDesc(InventoryTypeEnum.getEnum(stockVO.getInventoryType()).getMessage());
            }

            stockVO.setIsPreDesc(SkuIsPreEnum.getEnum(stockVO.getIsPre()).getMessage());
        }
        return stockVO;
    }

    private StockLocationWithLotVO getStockLocationWithLotVO(StockLocationWithLotBizDTO a) {
        StockLocationWithLotVO stockVO = ConverterUtil.convert(a, StockLocationWithLotVO.class);
        if (!ObjectUtils.isEmpty(stockVO)) {
            stockVO.setZoneTypeName(ZoneTypeEnum.getEnum(stockVO.getZoneType()).getName());
            Arrays.stream(LocationTypeEnum.values()).sequential().filter(locationTypeEnum -> locationTypeEnum.getType().equalsIgnoreCase(stockVO.getLocationType()))
                            .findFirst().ifPresent(locationTypeEnum -> stockVO.setLocationTypeName(locationTypeEnum.getName()));

            stockVO.setSkuQualityName(SkuQualityEnum.getEnum(stockVO.getSkuQuality()).getMessage());
            stockVO.setCreatedTime(ConverterUtil.convertVoTime(a.getCreatedTime()));
            stockVO.setUpdatedTime(ConverterUtil.convertVoTime(a.getUpdatedTime()));

            stockVO.setExpireDateDesc(ConverterUtil.convertVoTime(a.getExpireDate(), a.getExpireDateFormat()));
            stockVO.setManufDateDesc(ConverterUtil.convertVoTime(a.getManufDate(), a.getManufDateFormat()));
            stockVO.setReceiveDateDesc(ConverterUtil.convertVoTime(a.getReceiveDate(), a.getReceiveDateFormat()));
            stockVO.setWithdrawDateDesc(ConverterUtil.convertVoTime(a.getWithdrawDate(), a.getWithdrawDateFormat()));

            stockVO.setZoneType(a.getZoneType());
            if (!StringUtils.isEmpty(a.getZoneType())) {
                stockVO.setZoneTypeName(ZoneTypeEnum.getEnum(a.getZoneType()).getName());
            }
            if (StrUtil.isNotBlank(a.getIsPre())) {
                stockVO.setIsPreDesc(SkuIsPreEnum.getEnum(a.getIsPre()).getMessage());
            }
        }
        return stockVO;
    }

    private StockLocationVO getStockLocationVO(StockLocationBizDTO a) {
        StockLocationVO stockVO = ConverterUtil.convert(a, StockLocationVO.class);
        if (!ObjectUtils.isEmpty(stockVO)) {
            stockVO.setZoneTypeName(ZoneTypeEnum.getEnum(stockVO.getZoneType()).getName());
            stockVO.setLocationTypeName(LocationTypeEnum.getEnum(stockVO.getLocationType()).getName());
            stockVO.setSkuQualityName(SkuQualityEnum.getEnum(stockVO.getSkuQuality()).getMessage());
            stockVO.setCreatedTime(ConverterUtil.convertVoTime(a.getCreatedTime()));
            stockVO.setUpdatedTime(ConverterUtil.convertVoTime(a.getUpdatedTime()));
            stockVO.setIsPreDesc(SkuIsPreEnum.desc(a.getIsPre()));
        }
        return stockVO;
    }
}
