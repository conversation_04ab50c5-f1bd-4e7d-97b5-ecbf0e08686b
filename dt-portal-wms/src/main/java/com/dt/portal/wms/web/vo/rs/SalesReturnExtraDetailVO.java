package com.dt.portal.wms.web.vo.rs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SalesReturnExtraDetailVO implements Serializable {

    @ApiModelProperty(value = "多货单号")
    private String salesReturnExtraOrderNo;

    /**
     * 商品代码
     */
    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    /**
     * 商品条码
     */
    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 货主名称
     */
    @ApiModelProperty(value = "货主名称")
    private String cargoName;
    private String workbenchCode;

    /**
     * 实收正品数量
     */
    @ApiModelProperty(value = "实收正品数量")
    private BigDecimal avlQty;

    /**
     * 实收次品数量
     */
    @ApiModelProperty(value = "实收次品数量")
    private BigDecimal damageQty;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Long manufDate;
    private String manufDateDesc;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private Long expireDate;
    private String expireDateDesc;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal qty;
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;
    @ApiModelProperty(value = "商品属性")
    private String skuQualityDesc;
}
