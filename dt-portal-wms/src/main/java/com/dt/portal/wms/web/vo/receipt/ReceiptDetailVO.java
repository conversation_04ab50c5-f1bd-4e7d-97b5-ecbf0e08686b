package com.dt.portal.wms.web.vo.receipt;

import cn.hutool.core.date.DateUtil;
import com.dt.component.common.enums.rec.ReceiptStatusEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.dto.receipt.ReceiptDetailBizDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/29 15:00
 */
@Data
public class ReceiptDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收货作业批次")
    private String recId;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;
    private String warehouseName;

    @ApiModelProperty(value = "上游行号")
    private String extNo;

    @ApiModelProperty(value = "行号")
    private String lineSeq;

    @ApiModelProperty(value = "到货通知单号")
    private String asnId;

    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    @ApiModelProperty(value = "容器号")
    private String contCode;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "总体积(m³)")
    private String volume;

    @ApiModelProperty(value = "总毛重(KG)")
    private String grossWeight;

    @ApiModelProperty(value = "总净重(KG)")
    private String netWeight;

    @ApiModelProperty(value = "商品数量")
    private String skuQty;

    @ApiModelProperty(value = "批次ID 不允许为空，默认标准规则(批次规则档案)")
    private String skuLotNo;

    @ApiModelProperty(value = "入库日期")
    private String receiveDate;

    @ApiModelProperty(value = "生产日期")
    private String manufDate;

    @ApiModelProperty(value = "生产日期")
    private String expireDate;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;
    private String skuQualityName;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "批次属性(单据号)")
    private String externalLinkBillNo;

    @ApiModelProperty(value = "禁售日期")
    private String withdrawDate;

    @ApiModelProperty(value = "收货库位")
    private String locationCode;
    private String targetLocationCode;

    @ApiModelProperty(value = "状态码")
    private String status;
    private String statusName;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private String createdTime;
    @ApiModelProperty("更新时间")
    private String updatedTime;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;
    private String inventoryTypeDesc;

    @ApiModelProperty(value = "外部批次编码")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;

    public static List<ReceiptDetailVO> buildVO(List<ReceiptDetailBizDTO> dtos) {
        if (dtos == null) {
            return null;
        }
        List<ReceiptDetailVO> vos = dtos.stream().map(entity -> {
            ReceiptDetailVO vo = ConverterUtil.convert(entity, ReceiptDetailVO.class);
            vo.setStatusName(ReceiptStatusEnum.fromCode(entity.getStatus()).getMessage());

            vo.setSkuQualityName(SkuQualityEnum.getEnum(entity.getSkuQuality()).getMessage());

            if (!StringUtils.isEmpty(entity.getCreatedTime()) && entity.getCreatedTime() != 0) {
                vo.setCreatedTime(DateUtil.format(new Date(entity.getCreatedTime()), "yyyy-MM-dd HH:mm:ss"));
            } else {
                vo.setCreatedTime("");
            }
            if (!StringUtils.isEmpty(entity.getCreatedTime()) && entity.getCreatedTime() != 0) {
                vo.setUpdatedTime(DateUtil.format(new Date(entity.getUpdatedTime()), "yyyy-MM-dd HH:mm:ss"));
            } else {
                vo.setUpdatedTime("");
            }

            vo.setInventoryTypeDesc("");
            if (!StringUtils.isEmpty(entity.getInventoryType())) {
                vo.setInventoryTypeDesc(InventoryTypeEnum.getEnum(entity.getInventoryType()).getMessage());
            }

            vo.setGrossWeight(entity.getGrossWeight().setScale(entity.getWeightFormat(), RoundingMode.FLOOR).toString());
            vo.setNetWeight(entity.getNetWeight().setScale(entity.getWeightFormat(), RoundingMode.FLOOR).toString());
            vo.setVolume(entity.getVolume().setScale(entity.getVolumeFormat(), RoundingMode.FLOOR).toString());
            vo.setSkuQty(entity.getSkuQty().setScale(entity.getNumberFormat(), RoundingMode.FLOOR).toString());

            vo.setReceiveDate(ConverterUtil.convertVoTime(entity.getReceiveDate(), entity.getReceiveDateFormat()));
            vo.setExpireDate(ConverterUtil.convertVoTime(entity.getExpireDate(), entity.getExpireDateFormat()));
            vo.setManufDate(ConverterUtil.convertVoTime(entity.getManufDate(), entity.getManufDateFormat()));
            vo.setWithdrawDate(ConverterUtil.convertVoTime(entity.getWithdrawDate(), entity.getWithdrawDateFormat()));

            return vo;
        }).collect(Collectors.toList());
        return vos;
    }

}
