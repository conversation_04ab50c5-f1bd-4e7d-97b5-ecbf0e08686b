package com.dt.portal.wms.web.client;

import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.param.TransferParam;
import com.dt.platform.wms.param.transfer.*;
import com.dt.portal.wms.web.vo.transfer.TransferVO;

import java.util.List;

/**
 * Created by nobody on 2020/12/28 19:01
 */
public interface ITransferPortalClient {

    /**
     * 新增库存转移主单
     * @param param
     * @return
     */
    Result<String> create(TransferAddBizParam param);

    /**
     * 转移单状态列表
     * @return
     */
    Result<List<IdNameVO>> getStatus();

    /**
     * 转移单状态列表
     * 多了一个全部
     * @return
     */
    Result<List<IdNameVO>> getAllStatus();

    /**
     * 转移原因下拉
     * @return
     */
    Result<List<IdNameVO>> getReasons();

    /**
     * 新增或修改明细
     * @param param
     * @return
     */
    Result<Boolean> saveDetail(TransferUpdateBizParam param);

    Result<Boolean> maintainLocation(TransferUpdateBizParam param);

    /**
     * 转移单审核
     * @param param
     * @return
     */
    Result<Boolean> examine(TransferExamineBizParam param);

    /**
     * 转移单提交审核
     * @param param
     * @return
     */
    Result<Boolean> commitAudit(TransferExamineBizParam param);

    /**
     * 转移单仓内审核
     * @param param
     * @return
     */
    Result<Boolean> innerAudit(TransferExamineBizParam param);

    /**
     * 转移单确认转移
     * @param param
     * @return
     */
    Result<Boolean> confirm(TransferConfirmBizParam param);

    /**
     * 转移单取消
     * @param param
     * @return
     */
    Result<Boolean> cancel(TransferCancelBizParam param);

    /**
     * 转移单分页
     * @param transferParam
     * @return
     */
    Result<PageVO<TransferVO>> page(TransferParam transferParam);

    /**
     * 转移单详情
     * @param transferParam
     * @return
     */
    Result<TransferVO> detail(TransferParam transferParam);

    Result<List<IdNameVO>> getBusinessTypeList();
    Result<List<IdNameVO>> getDetailReasonList();
    Result<List<IdNameVO>> getRPList();

    Result<List<IdNameVO>> queryTransferOrderTag();

    Result<List<IdNameVO>> queryTag(TransferParam param);

    Result<Boolean> modifyTag(TransferParam param);
}
