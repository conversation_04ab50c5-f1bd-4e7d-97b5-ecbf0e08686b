package com.dt.portal.wms.web.client.rs;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.dto.rs.SaleReturnDataCenterDeliveryOrderDTO;
import com.dt.platform.wms.dto.rs.SalesReturnExtraScanUpcBizDTO;
import com.dt.platform.wms.param.rs.SalesReturnExtraBizParam;
import com.dt.portal.wms.web.vo.rs.SalesReturnExtraVO;

import java.util.List;

public interface ISalesReturnExtraPortalClient {
    /**
     * @param salesReturnExtraBizParam
     * @return com.dt.component.common.result.Result<com.dt.component.common.vo.PageVO < com.dt.platform.wms.dto.rs.SalesReturnExtraBizDTO>>
     * <AUTHOR>
     * @describe: 分页
     * @date 2024/1/25 10:52
     */
    Result<PageVO<SalesReturnExtraVO>> getPage(SalesReturnExtraBizParam salesReturnExtraBizParam);

    /**
     * @param salesReturnExtraBizParam
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.rs.SalesReturnExtraBizDTO>
     * <AUTHOR>
     * @describe: 详情
     * @date 2024/1/25 10:53
     */
    Result<SalesReturnExtraVO> getDetail(SalesReturnExtraBizParam salesReturnExtraBizParam);

    Result<Boolean> back(SalesReturnExtraBizParam salesReturnExtraBizParam);

    /**
     * @param
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.component.common.vo.IdNameVO>>
     * <AUTHOR>
     * @describe: 状态码
     * @date 2024/1/25 10:54
     */
    Result<List<IdNameVO>> querySalesReturnExtraStatus();
    /**
     * @param
     * @return com.dt.component.common.result.Result<java.util.List<com.dt.component.common.vo.IdNameVO>>
     * <AUTHOR>
     * @describe:
     * @date 2024/1/26 11:03
     */
    Result<List<IdNameVO>> querySalesReturnDamage();

    /**
     * @param salesReturnExtraBizParam
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 执行报废
     * @date 2024/1/25 10:56
     */
    Result<Boolean> executeDumpExtra(SalesReturnExtraBizParam salesReturnExtraBizParam) throws Exception;

    /**
     * @param salesReturnExtraBizParam
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 执行退回
     * @date 2024/1/25 10:56
     */
    Result<Boolean> executeReturnExtra(SalesReturnExtraBizParam salesReturnExtraBizParam) throws Exception;

    /**
     * @param salesReturnExtraBizParam
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 多货扫描质检台
     * @date 2024/1/25 10:56
     */
    Result<Boolean> scanWorkbenchByExtra(SalesReturnExtraBizParam salesReturnExtraBizParam);

    /**
     * @param salesReturnExtraBizParam
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 多货扫描运单号
     * @date 2024/1/25 10:56
     */
    Result<SaleReturnDataCenterDeliveryOrderDTO> scanExpressByExtra(SalesReturnExtraBizParam salesReturnExtraBizParam);

    /**
     * @param salesReturnExtraBizParam
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 多货扫描条码
     * @date 2024/1/25 10:56
     */
    Result<List<SalesReturnExtraScanUpcBizDTO>> scanUpcByExtra(SalesReturnExtraBizParam salesReturnExtraBizParam);

    /**
     * @param salesReturnExtraBizParam
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 完成多货
     * @date 2024/1/25 10:56
     */
    Result<String> completeExtra(SalesReturnExtraBizParam salesReturnExtraBizParam) throws Exception;

}
