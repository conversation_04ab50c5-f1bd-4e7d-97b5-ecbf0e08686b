package com.dt.portal.wms.web.client.esShipment;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.param.pkg.PackAnalysisBillParam;
import com.dt.domain.bill.param.pkg.PackDetainBillParam;
import com.dt.portal.wms.web.vo.pkg.PackageVO;

public interface IPackDetainPortalClient {

    /**
     * 包裹滞留报告
     */
    Result<PageVO<PackageVO>> getPackDetainPageByPack(PackDetainBillParam param);
}
