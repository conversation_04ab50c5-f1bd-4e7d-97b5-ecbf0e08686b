package com.dt.portal.wms.web.client.impl;

import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.IB2BPackageCheckBenchBizClient;
import com.dt.platform.wms.dto.check.b2b.*;
import com.dt.platform.wms.param.base.WorkBenchDetailBizParam;
import com.dt.platform.wms.param.check.b2b.*;
import com.dt.portal.wms.web.client.IB2BWorkBenchClient;
import com.dt.portal.wms.web.vo.check.b2b.B2BPackBackResultVO;
import com.dt.portal.wms.web.vo.check.b2b.B2BScanUpcCodeBackVO;
import com.dt.portal.wms.web.vo.check.b2b.B2BSubmitSkuBackVO;
import com.dt.portal.wms.web.vo.pkg.B2BScanBillNoBackVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class B2BWorkBenchClient implements IB2BWorkBenchClient {


    @DubboReference
    IB2BPackageCheckBenchBizClient iB2BPackageCheckBenchBizClient;

    @Override
    @SoulClient(path = "/b2b/checkWorkBench", desc = "检查质检台")
    public Result<Boolean> checkWorkBench(@Validated WorkBenchDetailBizParam param) {
        return Result.success(iB2BPackageCheckBenchBizClient.checkWorkBench(param));
    }

    @Override
    @SoulClient(path = "/b2b/packMaterial", desc = "检查包材")
    public Result<Boolean> checkPackMaterial(@Validated B2BScanMaterialParam param) {
        return iB2BPackageCheckBenchBizClient.checkPackMaterial(param);
    }

    @Override
    @SoulClient(path = "/b2b/checkPackMaterialNew", desc = "检查包材")
    public Result<Boolean> checkPackMaterialNew(B2BScanMaterialParamNew param) {
        if (StringUtils.isEmpty(param)
                || StringUtils.isEmpty(param.getPackageCode())
                || StringUtils.isEmpty(param.getPackageMaterialCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        return iB2BPackageCheckBenchBizClient.checkPackMaterialNew(param);
    }

    @Override
    @SoulClient(path = "/b2b/submitPackage", desc = "提交包裹")
    public Result<B2BPackBackResultVO> submitPackage(@Validated B2BSubmitPackageParam param) {
        Result<B2BPackBackResultBizDTO> result = iB2BPackageCheckBenchBizClient.submitPackage(param);
        return Result.success(ConverterUtil.convert(result.getData(), B2BPackBackResultVO.class));
    }

    @Override
    @SoulClient(path = "/b2b/submitPackageNew", desc = "提交包裹")
    public Result<B2BPackBackResultBizDTO> submitPackageNew(B2BSubmitPackageParamNew param) {
        if (StringUtils.isEmpty(param)
                || StringUtils.isEmpty(param.getPackageCode())
                || StringUtils.isEmpty(param.getPackageMaterialCode())
                || StringUtils.isEmpty(param.getCheckPackageCode())
                || StringUtils.isEmpty(param.getRealWeight())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<B2BPackBackResultBizDTO> result = iB2BPackageCheckBenchBizClient.submitPackageNew(param);
        return result;
    }

    @Override
    @SoulClient(path = "/b2b/removePackSkuQty", desc = "反扫")
    public Result<Boolean> removePackSkuQty(RemovePackSkuQtyParam param) {
        return Result.success(iB2BPackageCheckBenchBizClient.removePackSkuQty(param).getData());
    }

    @Override
    @SoulClient(path = "/b2b/scanBillNo", desc = "扫码包裹号或快递单号")
    public Result<B2BScanBillNoBackVO> scanExpressNoOrPackageCode(@Validated B2BScanBillNoParam param) {
        Result<B2BScanBillNoBackBizDTO> result = iB2BPackageCheckBenchBizClient.scanExpressNoOrPackageCode(param);
        return Result.success(ConverterUtil.convert(result.getData(), B2BScanBillNoBackVO.class));
    }

    @Override
    @SoulClient(path = "/b2b/scanBillNoNew", desc = "扫码包裹号或快递单号")
    public Result<B2BScanBillNoBackBizDTO> scanBillNoNew(B2BScanBillNoParam param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "单号不能为空");
        }
        Result<B2BScanBillNoBackBizDTO> result = iB2BPackageCheckBenchBizClient.scanBillNoNew(param);
        return result;
    }


    @Override
    @SoulClient(path = "/b2b/scanUpcCode", desc = "扫码商品条码")
    public Result<B2BScanUpcCodeBackVO> scanUpcCode(@Validated B2BScanSkuParam param) {
        Result<B2BScanUpcCodeBackBizDTO> result = iB2BPackageCheckBenchBizClient.scanSkuUpcCode(param);
        return Result.success(ConverterUtil.convert(result.getData(), B2BScanUpcCodeBackVO.class));
    }

    @Override
    @SoulClient(path = "/b2b/scanUpcCodeNew", desc = "扫码商品条码新")
    public Result<B2BScanUpcCodeBackBizDTO> scanUpcCodeNew(B2BScanSkuParam param) {
        if (StringUtils.isEmpty(param)
                || StringUtils.isEmpty(param.getPackageCode())
                || StringUtils.isEmpty(param.getUpcCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<B2BScanUpcCodeBackBizDTO> result = iB2BPackageCheckBenchBizClient.scanUpcCodeNew(param);
        return result;
    }

    @Override
    @SoulClient(path = "/b2b/submitSkuAndCheckSku", desc = "提交SKU")
    public Result<B2BSubmitSkuBackVO> submitSkuAndCheckSku(@Validated B2BSubmitSkuParam param) {
        return Result.success(ConverterUtil.convert(iB2BPackageCheckBenchBizClient.submitSkuAndCheckSku(param).getData(), B2BSubmitSkuBackVO.class));
    }

    @Override
    @SoulClient(path = "/b2b/submitSkuAndCheckSkuNew", desc = "提交SKU新")
    public Result<B2BSubmitSkuBackBizNewDTO> submitSkuAndCheckSkuNew(B2BSubmitParamNew b2BSubmitParam) {
        if (StringUtils.isEmpty(b2BSubmitParam)
                || CollectionUtils.isEmpty(b2BSubmitParam.getSkuParamList())
                || StringUtils.isEmpty(b2BSubmitParam.getPackageCode())
                || StringUtils.isEmpty(b2BSubmitParam.getWorkBenchCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        for (B2BSubmitSkuParamNew entity : b2BSubmitParam.getSkuParamList()) {
            if (StringUtils.isEmpty(entity.getQty())
                    || StringUtils.isEmpty(entity.getSkuCode())
                    || entity.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
            }
            if (StringUtils.isEmpty(entity.getExpireDate())) {
                entity.setExpireDate(0L);
            }
            if (StringUtils.isEmpty(entity.getProductionNo())) {
                entity.setProductionNo("");
            }
            if (StringUtils.isEmpty(entity.getValidityCode())) {
                entity.setValidityCode("");
            }
        }
        if (b2BSubmitParam.getSkuParamList().stream().map(B2BSubmitSkuParamNew::getSkuCode).distinct().count() > 1) {
            throw new BaseException(BaseBizEnum.TIP, "商品提交不允许有多个商品");
        }

        Result<B2BSubmitSkuBackBizNewDTO> b2BSubmitSkuBackBizDTO = iB2BPackageCheckBenchBizClient.submitSkuAndCheckSkuNew(b2BSubmitParam);
        return b2BSubmitSkuBackBizDTO;
    }

}
