package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.param.CodeEnabledParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.base.ZoneBizModifyParam;
import com.dt.platform.wms.param.base.ZoneBizParam;
import com.dt.portal.wms.web.vo.base.ZoneVO;

import java.util.List;

/**
 * <p>
 * 库区 管理 网关接口
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-14
 */
public interface IZonePortalClient {

    /**
     * 获取存储区库区下拉列表
     * @return
     */
    Result<List<IdNameVO>> getStoreZoneList();
    /**
     * 获取库区类型列表
     *
     * @return
     */
    Result<List<IdNameVO>> getZoneTypeList();
    Result<List<IdNameVO>> getAllZoneTypeList();

    /**
     * 获取库区状态下拉列表
     * @return
     */
    Result<List<IdNameVO>> getStatusList();
    Result<List<IdNameVO>> getAllStatusList();

    /**
     * 获取存放类型下拉列表
     * @return
     */
    Result<List<IdNameVO>> getStorageRuleList();
    Result<List<IdNameVO>> getAllStorageRuleList();

    Result<List<IdNameVO>> getAllZoneList();

    /**
     * 获取所有拣选区的编码
     * @return
     */
    Result<List<IdNameVO>> getPickZoneList();

    /**
     * 查询
     * @return
     */
    Result<List<IdNameVO>> getZonePickAndStorageList();
    Result<List<IdNameVO>> getZonePickAndStorageEffectiveList();

    /**
     * 新增库区
     *
     * @param param
     * @return
     */
    Result<Boolean> create(ZoneBizParam param);

    /**
     * 修改库区
     *
     * @param param
     * @return
     */
    Result<Boolean> modify(ZoneBizModifyParam param);


    /**
     * 修改库区
     *
     * @param param
     * @return
     */
    Result<Boolean> enable(CodeEnabledParam param);

    /**
     * 获取库区
     *
     * @param param
     * @return
     */
    Result<ZoneVO> getDetail(CodeParam param);


    /**
     * 库区分页列表
     *
     * @param param
     * @return
     */
    Result<PageVO<ZoneVO>> getPage(ZoneBizParam param);
    /**
     * @param
     * @return com.dt.component.common.result.Result<java.util.List<com.dt.component.common.vo.IdNameVO>>
     * <AUTHOR>
     * @describe:
     * @date 2022/12/15 9:50
     */
    Result<List<IdNameVO>> getPickSkuZoneList();
    /**
     * @param
     * @return com.dt.component.common.result.Result<java.util.List<com.dt.component.common.vo.IdNameVO>>
     * <AUTHOR>
     * @describe:
     * @date 2023/4/20 14:23
     */
    Result<List<IdNameVO>> getPhysicalPartitionList();

}
