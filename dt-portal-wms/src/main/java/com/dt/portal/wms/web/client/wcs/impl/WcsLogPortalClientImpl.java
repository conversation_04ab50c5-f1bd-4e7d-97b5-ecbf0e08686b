package com.dt.portal.wms.web.client.wcs.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.client.wcs.IWcsLogBizClient;
import com.dt.portal.wms.web.client.wcs.IWcsLogPortalClient;
import com.dt.portal.wms.web.client.wcs.LogDTO;
import com.dt.portal.wms.web.client.wcs.WcsLogParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

/**
 * <p>
 * WCS 相关日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
@RefreshScope
public class WcsLogPortalClientImpl implements IWcsLogPortalClient {

    @DubboReference
    private IWcsLogBizClient wcsLogBizClient;

    @UCApi(type = AuthTypeEnum.OPEN)
    @Override
    @SoulClient(path = "/wcs/log", desc = "PLC设备信息单条新增")
    public Result<PageVO<LogDTO>> log(WcsLogParam wcsLogParam) {
        if (CollectionUtil.isEmpty(wcsLogParam.getLogSubjectList())) {
            return Result.failWithMessage("请输入运单号");
        }
        try {
            String post = HttpUtil.post(wcsLogBizClient.urlMap().get(CurrentRouteHolder.getWarehouseCode()) + "/package/logs", JSONUtil.toJsonStr(wcsLogParam));
            List<LogDTO> logDTOList = JSONUtil.parseObj(post).getJSONObject("data").getJSONArray("dataList").toList(LogDTO.class);
            JSONObject jsonObject = JSONUtil.parseObj(post).getJSONObject("data");
            PageVO<LogDTO> pageVO = new PageVO<>();
            BeanUtil.copyProperties(jsonObject, pageVO);
            pageVO.setDataList(logDTOList);
            return Result.success(pageVO);
        } catch (Exception exception) {
            return Result.failWithMessage("调用WCS查询日志异常");
        }
    }

    public static void main(String[] args) {
        WcsLogParam wcsLogParam = new WcsLogParam();
        String post = HttpUtil.post("http://192.168.20.65:32301/package/logs", JSONUtil.toJsonStr(wcsLogParam));
        System.out.println(post);
        Result<PageVO<LogDTO>> result = new Result<>();
        BeanUtil.copyProperties(JSONUtil.parse(post), result);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }
}
