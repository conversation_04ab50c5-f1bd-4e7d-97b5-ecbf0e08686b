package com.dt.portal.wms.web.controller;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.wms.dto.ShipmentAnalysisDTO;
import com.dt.elasticsearch.wms.param.ShipmentAnalysisParam;
import com.dt.portal.wms.web.client.esShipment.IShipmentAnalysisPortalClient;
import com.dt.portal.wms.web.vo.shipment.ShipmentOrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 出库单分析控制器
 */
@Slf4j
@RestController
@RequestMapping("/shipment/analysis")
@Api(tags = "出库单分析")
@Profile({"local", "dev", "test"})
public class ShipmentAnalysisController {

    @Resource
    private IShipmentAnalysisPortalClient shipmentAnalysisPortalClient;


    @PostMapping("/page")
    @ApiOperation("出库单分析查询（分页版）")
    public Result<PageVO<ShipmentAnalysisDTO>> getShipmentAnalysisPage(@RequestBody ShipmentAnalysisParam param) {
        log.info("出库单分析查询（分页版），参数：{}", param);
        return shipmentAnalysisPortalClient.getShipmentAnalysisPage(param);
    }

    @PostMapping("/summary")
    @ApiOperation("出库单分析汇总查询")
    public Result<ShipmentAnalysisDTO> getShipmentAnalysisSummary(@RequestBody ShipmentAnalysisParam param) {
        log.info("出库单分析汇总查询，参数：{}", param);
        return shipmentAnalysisPortalClient.getShipmentAnalysisSummary(param);
    }

    @PostMapping("/getShipPage")
    @ApiOperation("取当前仓库下的出库单分析数据")
    public Result<PageVO<ShipmentOrderVO>> getShipmentAnalysisPageByShip(@RequestBody ShipmentAnalysisParam param) {
        log.info("取当前仓库下的出库单分析数据，参数：{}", param);
        return shipmentAnalysisPortalClient.getShipmentAnalysisPageByShip(param);
    }
}
