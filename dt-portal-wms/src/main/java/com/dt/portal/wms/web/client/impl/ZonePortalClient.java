package com.dt.portal.wms.web.client.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.enums.base.ZoneStatusEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.stock.StorageRuleEnum;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.IZoneBizClient;
import com.dt.platform.wms.dto.base.ZoneBizDTO;
import com.dt.platform.wms.param.CodeEnabledParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.base.ZoneBizModifyParam;
import com.dt.platform.wms.param.base.ZoneBizParam;
import com.dt.portal.wms.web.client.IZonePortalClient;
import com.dt.portal.wms.web.vo.base.ZoneVO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
public class ZonePortalClient implements IZonePortalClient {


    @DubboReference
    private IZoneBizClient zoneBizClient;

    @Override
    @SoulClient(path = "/zone/getStoreZoneList", desc = "库区管理-获取存储区下拉列表")
    public Result<List<IdNameVO>> getStoreZoneList() {
        return Result.success(zoneBizClient.getStoreZoneList().getData());
    }

    @Override
    @SoulClient(path = "/zone/getZoneTypeList", desc = "库区管理-获取库区类型下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getZoneTypeList() {
        return Result.success(IdNameVO.build(ZoneTypeEnum.class, "type", "name"));
    }

    @Override
    @SoulClient(path = "/zone/getAllZoneTypeList", desc = "库区管理-获取库区类型下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getAllZoneTypeList() {
        return Result.success(IdNameVO.buildAll(ZoneTypeEnum.class, "type", "name"));
    }

    @Override
    @SoulClient(path = "/zone/getStatusList", desc = "库区管理-获取库区状态下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getStatusList() {
        return Result.success(IdNameVO.build(ZoneStatusEnum.class, "status", "name"));
    }

    @Override
    @SoulClient(path = "/zone/getAllStatusList", desc = "库区管理-获取库区状态下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getAllStatusList() {
        return Result.success(IdNameVO.buildAll(ZoneStatusEnum.class, "status", "name"));
    }

    @Override
    @SoulClient(path = "/zone/getStorageRuleList", desc = "库区管理-获取存放规则下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getStorageRuleList() {
        return Result.success(IdNameVO.build(StorageRuleEnum.class, "rule", "name"));
    }

    @Override
    @SoulClient(path = "/zone/getAllStorageRuleList", desc = "库区管理-获取存放规则下拉列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<List<IdNameVO>> getAllStorageRuleList() {
        return Result.success(IdNameVO.buildAll(StorageRuleEnum.class, "rule", "name"));
    }

    @Override
    @SoulClient(path = "/zone/getAllZoneList", desc = "库区管理-获取存放规则下拉列表")
    public Result<List<IdNameVO>> getAllZoneList() {
        return zoneBizClient.getAllZoneList();
    }

    @Override
    @SoulClient(path = "/zone/getPickZoneList", desc = "库区管理-获取拣选区列表")
    public Result<List<IdNameVO>> getPickZoneList() {
        return zoneBizClient.getPickZoneList();
    }

    @Override
    @SoulClient(path = "/zone/getZonePickAndStorage", desc = "库区管理-获取存放规则下拉列表")
    public Result<List<IdNameVO>> getZonePickAndStorageList() {
        return zoneBizClient.getZonePickAndStorageList();
    }

    @Override
    @SoulClient(path = "/zone/getZonePickAndStorageEffectiveList", desc = "库区管理-获取存放规则下拉列表")
    public Result<List<IdNameVO>> getZonePickAndStorageEffectiveList() {
        return zoneBizClient.getZonePickAndStorageEffectiveList();
    }

    @Override
    @SoulClient(path = "/zone/create", desc = "库区管理-修改")
    public Result<Boolean> create(@Validated ZoneBizParam param) {
        return zoneBizClient.create(param);
    }

    @Override
    @SoulClient(path = "/zone/modify", desc = "库区管理-修改")
    public Result<Boolean> modify(@Validated ZoneBizModifyParam param) {
        ZoneBizParam zoneBizParam = ConverterUtil.convert(param, ZoneBizParam.class);
        return zoneBizClient.modify(zoneBizParam);
    }

    @Override
    @SoulClient(path = "/zone/enable", desc = "库区管理-启用禁用")
    public Result<Boolean> enable(@Validated CodeEnabledParam param) {
        ZoneBizParam zoneBizParam = ConverterUtil.convert(param, ZoneBizParam.class);
        return zoneBizClient.enable(zoneBizParam);
    }

    @Override
    @SoulClient(path = "/zone/getDetail", desc = "库区管理-获取详情")
    public Result<ZoneVO> getDetail(@Validated CodeParam param) {
        ZoneBizParam zoneBizParam = new ZoneBizParam();
        zoneBizParam.setCode(param.getCode());
        Result<ZoneBizDTO> dtoResult = zoneBizClient.getDetail(zoneBizParam);
        ZoneBizDTO zoneBiz = dtoResult.getData();
        return Result.success(getZoneVO(zoneBiz));
    }

    @Override
    @SoulClient(path = "/zone/getPage", desc = "库区管理-获取分页列表")
    public Result<PageVO<ZoneVO>> getPage(ZoneBizParam param) {

        Result<Page<ZoneBizDTO>> pageResult = zoneBizClient.getPage(param);

        Page<ZoneBizDTO> zoneBizPage = pageResult.getData();
        List<ZoneBizDTO> zoneBizList = zoneBizPage.getRecords();
        List<ZoneVO> voList = new ArrayList<>();
        //格式化数据
        if (!CollectionUtils.isEmpty(zoneBizList)) {
            zoneBizList = zoneBizPage.getRecords();
            voList = zoneBizList.stream().flatMap(a -> {
                ZoneVO zoneVO = getZoneVO(a);
                return Stream.of(zoneVO);
            }).collect(Collectors.toList());
        }
        //组装VO Page数据
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(zoneBizPage.getSize());
        page.setCurrentPage(zoneBizPage.getCurrent());
        page.setTotalPage(zoneBizPage.getPages());
        page.setTotalCount(zoneBizPage.getTotal());

        PageVO<ZoneVO> pageVO = new PageVO<>();
        pageVO.setPage(page);
        pageVO.setDataList(voList);

        Result<PageVO<ZoneVO>> result = new Result<>();
        result.setCode(pageResult.getCode());
        result.setMessage(pageResult.getMessage());
        result.setData(pageVO);

        return result;
    }

    @Override
    @SoulClient(path = "/zone/getPickSkuZoneList", desc = "获取拣货去的库区编码(不包含存储区)")
    public Result<List<IdNameVO>> getPickSkuZoneList() {
        return zoneBizClient.getPickSkuZoneList();
    }

    @Override
    @SoulClient(path = "/zone/getPhysicalPartitionList", desc = "获取当前仓库物理防火分区")
    public Result<List<IdNameVO>> getPhysicalPartitionList() {
        return zoneBizClient.getPhysicalPartitionList();
    }

    private ZoneVO getZoneVO(ZoneBizDTO a) {
        ZoneVO zoneVO = ConverterUtil.convert(a, ZoneVO.class);
        if (!ObjectUtils.isEmpty(zoneVO)) {
            zoneVO.setTypeName(ZoneTypeEnum.getEnum(a.getType()).getName());
            zoneVO.setSkuQualityName(SkuQualityEnum.getEnum(a.getSkuQuality()).getMessage());
            zoneVO.setStatusName(ZoneStatusEnum.getEnum(a.getStatus()).getName());
            zoneVO.setStorageRuleName(StorageRuleEnum.getEnum(a.getStorageRule()).getName());
            zoneVO.setCreatedTime(ConverterUtil.convertVoTime(a.getCreatedTime()));
            zoneVO.setUpdatedTime(ConverterUtil.convertVoTime(a.getUpdatedTime()));
        }
        return zoneVO;
    }
}
