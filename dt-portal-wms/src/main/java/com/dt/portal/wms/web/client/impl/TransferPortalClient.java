package com.dt.portal.wms.web.client.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.transfer.*;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.dto.TransferDTO;
import com.dt.domain.bill.param.TransferParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.ITransferBizClient;
import com.dt.platform.wms.param.transfer.*;
import com.dt.portal.wms.web.client.ITransferPortalClient;
import com.dt.portal.wms.web.vo.transfer.TransferDetailVO;
import com.dt.portal.wms.web.vo.transfer.TransferVO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by nobody on 2020/12/28 19:05
 */
@DubboService
public class TransferPortalClient implements ITransferPortalClient {

    @DubboReference
    private ITransferBizClient transferBizClient;

    @Override
    @SoulClient(path = "/transfer/create", desc = "新增转移单")
    public Result<String> create(TransferAddBizParam param) {
        param.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        return transferBizClient.addTransfer(param);
    }

    @Override
    @SoulClient(path = "/transfer/statuses", desc = "转移状态")
    public Result<List<IdNameVO>> getStatus() {
        List<IdNameVO> list = IdNameVO.build(TransferStatusEnum.class, "code", "name");
        List<String> statusList = Arrays.asList(TransferStatusEnum.UNDER_REVIEW.getCode(), TransferStatusEnum.AUDIT_FAILURE.getCode(), TransferStatusEnum.EXAMINED.getCode());
        list.removeIf(vo -> statusList.contains((String) vo.getId()));
        return Result.success(list);
    }

    @Override
    @SoulClient(path = "/transfer/allStatuses", desc = "转移状态")
    public Result<List<IdNameVO>> getAllStatus() {
        List<IdNameVO> list = IdNameVO.buildAll(TransferStatusEnum.class, "code", "name");
        List<String> statusList = Arrays.asList(TransferStatusEnum.UNDER_REVIEW.getCode(), TransferStatusEnum.AUDIT_FAILURE.getCode(), TransferStatusEnum.EXAMINED.getCode());
        list.removeIf(vo -> statusList.contains((String) vo.getId()));
        return Result.success(list);
    }

    @Override
    @SoulClient(path = "/transfer/reasons", desc = "转移原因")
    public Result<List<IdNameVO>> getReasons() {
        List<IdNameVO> result = Arrays.stream(TransferReasonEnum.values()).filter(s -> !StringUtils.isEmpty(s.getCode())).map((TransferReasonEnum s) -> {
            IdNameVO vo = new IdNameVO();
            vo.setName(s.getName());
            vo.setId(s.getCode());
            return vo;
        }).collect(Collectors.toList());
        return Result.success(result);
    }

    @Override
    @SoulClient(path = "/transfer/saveDetail", desc = "保存转移单明细")
    public Result<Boolean> saveDetail(TransferUpdateBizParam param) {
        param.setFlag(TransferUpdateBizParam.FlagEnum.UPDATE);
        return transferBizClient.updateTransferDetail(param);
    }
    
    @Override
    @SoulClient(path = "/transfer/maintainLocation", desc = "编辑目标库位")
    public Result<Boolean> maintainLocation(TransferUpdateBizParam param) {
        return transferBizClient.maintainLocation(param);
    }

    @Override
    @SoulClient(path = "/transfer/examine", desc = "转移单审核")
    @Deprecated
    public Result<Boolean> examine(TransferExamineBizParam param) {
        return transferBizClient.submit(param);
    }

    @Override
    @SoulClient(path = "/transfer/commitAudit",desc = "转移单提交审核")
    public Result<Boolean> commitAudit(TransferExamineBizParam param) {
        if (StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return transferBizClient.commitAudit(param);
    }

    @Override
    @SoulClient(path = "/transfer/innerAudit",desc = "转移单仓内审核")
    public Result<Boolean> innerAudit(TransferExamineBizParam param) {
        if (StringUtils.isEmpty(param.getCode()) || StringUtils.isEmpty(param.getPass()) || StringUtils.isEmpty(param.getRemark())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        if (param.getRemark().length()>=200) {
            throw new BaseException(BaseBizEnum.TIP,"审核说明过长");
        }
        return transferBizClient.innerAudit(param);
    }


    @Override
    @SoulClient(path = "/transfer/confirm", desc = "转移单确认转移")
    public Result<Boolean> confirm(TransferConfirmBizParam param) {
        return transferBizClient.confirm(param);
    }

    @Override
    @SoulClient(path = "/transfer/cancel", desc = "转移单取消")
    public Result<Boolean> cancel(TransferCancelBizParam param) {
        return transferBizClient.cancel(param);
    }

    @Override
    @SoulClient(path = "/transfer/page", desc = "转移单分页")
    public Result<PageVO<TransferVO>> page(TransferParam transferParam) {
        Result<Page<TransferDTO>> page = transferBizClient.page(transferParam);
        PageVO<TransferVO> transferVOPageVO = ConverterUtil.convertPageVO(page.getData(), TransferVO.class);

        transferVOPageVO.getDataList().forEach(transferVO -> {
            transferVO.setStatusName(TransferStatusEnum.getEnum(transferVO.getStatus()).getName());
            transferVO.setReasonName(TransferReasonEnum.getEnum(transferVO.getReason()).getName());
            transferVO.setBusinessTypeDesc("");
            if(!StringUtils.isEmpty(transferVO.getBusinessType())){
                transferVO.setBusinessTypeDesc(TransferBusinessTypeEnum.getEnum(transferVO.getBusinessType()).getName());
            }
            Optional<TransferDTO> first = page.getData().getRecords().stream().filter(transferDTO -> transferDTO.getCode().equals(transferVO.getCode())).findFirst();
            first.ifPresent(transferDTO -> {
                transferVO.setCompleteDate(ConverterUtil.convertVoTime(transferDTO.getCompleteDate()));
                transferVO.setCreatedTime(ConverterUtil.convertVoTime(transferDTO.getCreatedTime()));
                transferVO.setUpdatedTime(ConverterUtil.convertVoTime(transferDTO.getUpdatedTime()));
                Set<TransferTagEnum> transferTagEnums = TransferTagEnum.NumToEnum(transferDTO.getOrderTag());
                transferVO.setOrderTagName(transferTagEnums.stream().map(TransferTagEnum::getDesc).collect(Collectors.joining("|")));
            });
        });
        return Result.success(transferVOPageVO);
    }

    @Override
    @SoulClient(path = "/transfer/detail", desc = "转移单详情")
    public Result<TransferVO> detail(TransferParam transferParam) {
        Result<TransferDTO> detail = transferBizClient.detail(transferParam);
        TransferVO transferVO = ConverterUtil.convert(detail.getData(), TransferVO.class);
        assert transferVO != null;
        transferVO.setReasonName(TransferReasonEnum.getEnum(transferVO.getReason()).getName());
        transferVO.setStatusName(TransferStatusEnum.getEnum(transferVO.getStatus()).getName());
        transferVO.setTransferDetailVOList(new ArrayList<>());
        transferVO.setCreatedTime(ConverterUtil.convertVoTime(detail.getData().getCreatedTime()));
        transferVO.setUpdatedTime(ConverterUtil.convertVoTime(detail.getData().getUpdatedTime()));
        transferVO.setCompleteDate(ConverterUtil.convertVoTime(detail.getData().getCompleteDate()));
        transferVO.setBusinessTypeDesc("");
        if(!StringUtils.isEmpty(transferVO.getBusinessType())){
            transferVO.setBusinessTypeDesc(TransferBusinessTypeEnum.getEnum(transferVO.getBusinessType()).getName());
        }
        detail.getData().getTransferDetailDTOList().forEach(transferDetailDTO -> {
            TransferDetailVO convert = ConverterUtil.convert(transferDetailDTO, TransferDetailVO.class);
            assert convert != null;
            // 兼容前端命名要求
            convert.setSkuCode(convert.getOriginSkuCode());
            convert.setSkuLotNo(convert.getOriginSkuLotNo());
            convert.setLocationCode(convert.getOriginLoactionCode());
            convert.setAvailableQty(convert.getOriginQty());
            convert.setReceiveDate(convert.getOriginReceiveDate());
            convert.setManufDate(convert.getOriginManufDate());
            convert.setExpireDate(convert.getOriginExpireDate());
            convert.setSkuQuality(convert.getOriginSkuQuality());
            convert.setSkuQualityName(SkuQualityEnum.getEnum(convert.getSkuQuality()).getMessage());
            convert.setProductionNo(convert.getOriginProductionNo());
            convert.setWithdrawDate(convert.getOriginWithdrawDate());
            convert.setOriginReceiveDate(ConverterUtil.convertVoTime(transferDetailDTO.getOriginReceiveDate(), transferDetailDTO.getOriginReceiveDateFormat()));
            convert.setOriginExpireDate(ConverterUtil.convertVoTime(transferDetailDTO.getOriginExpireDate(), transferDetailDTO.getOriginExpireDateFormat()));
            convert.setOriginManufDate(ConverterUtil.convertVoTime(transferDetailDTO.getOriginManufDate(), transferDetailDTO.getOriginManufDateFormat()));
            convert.setOriginWithdrawDate(ConverterUtil.convertVoTime(transferDetailDTO.getOriginWithdrawDate(), transferDetailDTO.getOriginWithdrawDateFormat()));
            convert.setOriginInventoryTypeDesc(InventoryTypeEnum.desc(convert.getOriginInventoryType()));
            convert.setTargetInventoryTypeDesc(InventoryTypeEnum.desc(convert.getTargetInventoryType()));
            
            if (StrUtil.isNotBlank(transferDetailDTO.getTargetReceiveDateFormat())) {
                convert.setTargetReceiveDateDesc(ConverterUtil.convertVoTime(transferDetailDTO.getTargetReceiveDate(), transferDetailDTO.getTargetReceiveDateFormat()));
            } else {
                convert.setTargetReceiveDateDesc(ConverterUtil.convertVoTime(transferDetailDTO.getTargetReceiveDate()));
            }

            if (StrUtil.isNotBlank(transferDetailDTO.getTargetManufDateFormat())) {
                convert.setTargetManufDateDesc(ConverterUtil.convertVoTime(transferDetailDTO.getTargetManufDate(), transferDetailDTO.getTargetManufDateFormat()));
            } else {
                convert.setTargetManufDateDesc(ConverterUtil.convertVoTime(transferDetailDTO.getTargetManufDate()));
            }

            if (StrUtil.isNotBlank(transferDetailDTO.getTargetExpireDateFormat())) {
                convert.setTargetExpireDateDesc(ConverterUtil.convertVoTime(transferDetailDTO.getTargetExpireDate(), transferDetailDTO.getTargetExpireDateFormat()));
            } else {
                convert.setTargetExpireDateDesc(ConverterUtil.convertVoTime(transferDetailDTO.getTargetExpireDate()));
            }

            if (StrUtil.isNotBlank(transferDetailDTO.getTargetWithdrawDateFormat())) {
                convert.setTargetWithdrawDateDesc(ConverterUtil.convertVoTime(transferDetailDTO.getTargetWithdrawDate(), transferDetailDTO.getTargetWithdrawDateFormat()));
            } else {
                convert.setTargetWithdrawDateDesc(ConverterUtil.convertVoTime(transferDetailDTO.getTargetWithdrawDate()));
            }

            convert.setTargetSkuQualityName(SkuQualityEnum.getEnum(transferDetailDTO.getTargetSkuQuality()).getMessage());
            if (convert.getDateFlag().equals(TransferDetailDateFlagEnum.MANUF.getCode())) {
                convert.setTargetManufOrExpireDate(convert.getTargetManufDate());
            } else if (convert.getDateFlag().equals(TransferDetailDateFlagEnum.EXPIRE.getCode())) {
                convert.setTargetManufOrExpireDate(convert.getTargetExpireDate());
            }
            convert.setReasonDesc("");
            convert.setRpDesc("");
            if (!StringUtils.isEmpty(convert.getReason())) {
                convert.setReasonDesc(TransferDetailReasonEnum.getEnum(convert.getReason()).getName());
            }
            if (!StringUtils.isEmpty(convert.getRp())) {
                convert.setRpDesc(TransferDetailRPEnum.getEnum(convert.getRp()).getName());
            }
            convert.setTargetManufOrExpireDateDesc(ConverterUtil.convertVoTime(convert.getTargetManufOrExpireDate()));
            transferVO.getTransferDetailVOList().add(convert);
        });
        return Result.success(transferVO);
    }

    @Override
    @SoulClient(path = "/transfer/getBusinessTypeList", desc = "转移单-业务场景")
    public Result<List<IdNameVO>> getBusinessTypeList() {
        return Result.success(IdNameVO.build(TransferBusinessTypeEnum.class, "code", "name"));
    }

    @Override
    @SoulClient(path = "/transfer/getDetailReasonList", desc = "转移单-明细原因")
    public Result<List<IdNameVO>> getDetailReasonList() {
        return Result.success(IdNameVO.build(TransferDetailReasonEnum.class, "code", "name"));
    }

    @Override
    @SoulClient(path = "/transfer/getRPList", desc = "转移单-明细责任方")
    public Result<List<IdNameVO>> getRPList() {
        return Result.success(IdNameVO.build(TransferDetailRPEnum.class, "code", "name"));
    }

    @Override
    @SoulClient(path = "/transfer/queryTransferOrderTag", desc = "获取全部订单标记")
    public Result<List<IdNameVO>> queryTransferOrderTag() {
        return Result.success(IdNameVO.build(TransferTagEnum.class, "code", "desc").stream()
                .filter(a -> !StringUtils.isEmpty(a.getId())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/transfer/queryTag", desc = "当前转移单标记")
    public Result<List<IdNameVO>> queryTag(TransferParam param) {
        return transferBizClient.queryTag(param);
    }

    @Override
    @SoulClient(path = "/transfer/modifyTag", desc = "修改转移单标记")
    public Result<Boolean> modifyTag(TransferParam param) {
        return transferBizClient.modifyTag(param);
    }
}
