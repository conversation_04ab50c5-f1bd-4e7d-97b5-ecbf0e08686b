package com.dt.elasticsearch.wms.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.PretreatmentStatusEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.bill.ShipmentPreSaleTypeEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.base.dto.CarrierDTO;
import com.dt.domain.base.dto.SalePlatformDTO;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderMaterialDTO;
import com.dt.elasticsearch.wms.biz.IPackageEsBizClient;
import com.dt.elasticsearch.wms.config.IndexNameConfig;
import com.dt.elasticsearch.wms.integration.domain.PackageIndex;
import com.dt.elasticsearch.wms.integration.domain.PickInfo;
import com.dt.elasticsearch.wms.integration.pkg.IRemotePackageEsClient;
import com.dt.elasticsearch.wms.integration.shipment.IRemoteShipmentEsClient;
import com.dt.platform.utils.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.DeleteQuery;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/9 10:11
 */
@Service
@Slf4j
public class PackageEsBizClientImpl implements IPackageEsBizClient {

    @Resource
    private IndexNameConfig nameConfig;

    @Resource
    private IRemotePackageEsClient remotePackageEsClient;

    @Resource
    private IRemoteShipmentEsClient remoteShipmentEsClient;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Value("${elasticsearch.package.index-name}")
    private String packageOrderIndexName;

    @Override
    public void remove(PackageDTO packageDTO) {
        String id = packageDTO.getPackageCode() + ":" + packageDTO.getWarehouseCode();
        this.remove(id);
    }

    private void remove(String id) {
        DeleteQuery deleteQuery = new DeleteQuery();
        deleteQuery.setQuery(QueryBuilders.idsQuery().addIds(id));
        deleteQuery.setIndex(packageOrderIndexName + "*");
        deleteQuery.setType("_doc");
        elasticsearchRestTemplate.delete(deleteQuery);
    }

    @Override
    public void initData(List<PackageDTO> packageDTOList) {
        List<PackageIndex> docList = new ArrayList<>();
        packageDTOList.forEach(packageDTO -> {
            RpcContextUtil.setWarehouseCode(packageDTO.getWarehouseCode());
            PackageIndex index = indexForPackage(packageDTO);
            indexForCreate(index);
            indexForPick(index);
            docList.add(index);
        });
        if (CollectionUtils.isEmpty(docList)) {
            return;
        }
        BulkRequest request = new BulkRequest();
        docList.forEach(doc -> {
            String indexName = routingIndex(doc);
            IndexRequest indexRequest = new IndexRequest(indexName, "_doc", doc.getIndexId());
            indexRequest.source(JSONUtil.toJsonStr(doc), XContentType.JSON);
            request.add(indexRequest);
        });
        try {
            bulkIndex(request, docList);
        } catch (Exception e) {
            log.error("包裹批量插入数据异常", e);
            throw new BaseException(BaseBizEnum.TIP, "批量ES同步包裹数据异常");
        }
    }

    @Override
    public void handleDataBatch(List<PackageDTO> packageDTOList) {
        List<PackageIndex> packageDocs = new ArrayList<>();
        packageDTOList.forEach(packageDTO -> {
            RpcContextUtil.setWarehouseCode(packageDTO.getWarehouseCode());
            PackageIndex index = indexForPackage(packageDTO);
            indexForCreate(index);
            indexForPick(index);
            packageDocs.add(index);
        });
        // 2. 插入
        if (CollectionUtils.isEmpty(packageDocs)) {
            return;
        }
        try {
            packageDocs.forEach(doc -> {
                String packageIndexName = routingIndex(doc);

                IndexQuery indexQuery = new IndexQueryBuilder()
                        .withIndexName(packageIndexName)
                        .withObject(doc)
                        .withId(doc.getIndexId())
                        .withType("_doc")
                        .build();
                elasticsearchRestTemplate.index(indexQuery);
            });
        } catch (Exception e) {
            log.error("消费失败:{}", e.getMessage(), e);
            throw e;
        }

    }

    private String routingIndex(PackageIndex doc) {
        Long createdTime = doc.getCreatedTime();
        String format = DateUtil.format(new Date(createdTime), "yyyyMM");
        return packageOrderIndexName + "_" + format;
    }

    /**
     * 构建唯一文档
     *
     * @param doc
     * @return
     */
    private PackageIndex buildUniqueDoc(PackageIndex doc) {
        SearchRequest searchRequest = new SearchRequest(packageOrderIndexName + "*");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.idsQuery(doc.getIndexId()));
        searchSourceBuilder.sort("esUpdatedTime", SortOrder.DESC);
        searchRequest.source(searchSourceBuilder);
        try {
            Boolean needDelete = Boolean.FALSE;
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits hits = searchResponse.getHits();
            PackageIndex oldDoc = null;
            if (hits.getTotalHits() >= 1L) {
                SearchHit hit = hits.getAt(0);
                String indexName = hit.getIndex();
                // 不在一个索引中
                if (!indexName.equals(nameConfig.getPackageIndexName())) {
                    oldDoc = JSONUtil.toBean(hit.getSourceAsString(), PackageIndex.class);
                    remove(doc.getIndexId());
                    needDelete = Boolean.TRUE;
                }
            }

            if (Boolean.TRUE.equals(needDelete)
                    && !ObjectUtils.isEmpty(oldDoc)) {
                // 将新的doc中不为空的值 拷贝到旧的doc上
                BeanUtil.copyProperties(doc, oldDoc, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                return oldDoc;
            } else {
                return doc;
            }
        } catch (IOException e) {
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        }
    }

    /**
     * 批量插入到ES中 批量插入失败时 单条重试
     *
     * @throws IOException
     */
    private void bulkIndex(BulkRequest request, List<PackageIndex> packageDocs) throws Exception {
        BulkResponse responses = restHighLevelClient.bulk(request, RequestOptions.DEFAULT);
        if (responses.hasFailures()) {
            log.error("批量ES同步包裹数据发生丢失，单条重试插入");
            for (BulkItemResponse item : responses.getItems()) {
                if (item.isFailed()) {
                    String packageCode = item.getId().split(":")[0];
                    PackageIndex doc = packageDocs.stream().filter(it -> it.getShipmentOrderCode().equals(packageCode)).findFirst().orElse(null);
                    if (null != doc) {
                        IndexQuery query = new IndexQueryBuilder().withId(doc.getIndexId())
                                .withIndexName(nameConfig.getPackageIndexName()).withObject(doc).withType("_doc").build();
                        elasticsearchRestTemplate.index(query);
                    }
                }
            }
        }
    }


    private PackageIndex indexForPackage(PackageDTO packageDTO) {
        PackageIndex doc = ConverterUtil.convert(packageDTO, PackageIndex.class);
        assert doc != null;
        doc.setIndexId(packageDTO.getPackageCode() + ":" + packageDTO.getWarehouseCode());
        doc.setKey(packageDTO.getId());
        doc.setEsUpdatedTime(System.currentTimeMillis());
        // 编码 时间 数量 格式转换
        Integer weightFormat = remotePackageEsClient.getWeightFormat(packageDTO.getWarehouseCode(), packageDTO.getCargoCode());
        Integer numberFormat = remotePackageEsClient.getNumberFormat(packageDTO.getWarehouseCode(), packageDTO.getCargoCode());
        BigDecimal convertDecimal = packageDTO.getOutSkuQty() == null ? BigDecimal.ZERO : packageDTO.getOutSkuQty();
        doc.setActualPackWeight((packageDTO.getActualPackWeight() == null ? BigDecimal.ZERO : packageDTO.getActualPackWeight()).setScale(weightFormat, RoundingMode.FLOOR).toString());
        doc.setWeight((packageDTO.getWeight() == null ? BigDecimal.ZERO : packageDTO.getWeight()).setScale(weightFormat, RoundingMode.FLOOR).toString());
        doc.setRealWeight((packageDTO.getRealWeight() == null ? BigDecimal.ZERO : packageDTO.getRealWeight()).setScale(weightFormat, RoundingMode.FLOOR).toString());
        doc.setOutSkuQty(convertDecimal.setScale(numberFormat, RoundingMode.FLOOR).toString());
        doc.setCheckCompleteDate(packageDTO.getCheckCompleteDate());
        doc.setCheckStartDate(packageDTO.getCheckStartDate());
        doc.setInterceptCancelDate(packageDTO.getInterceptCancelDate());
        doc.setOutStockDate(packageDTO.getOutStockDate());
        doc.setPickCompleteSkuDate(packageDTO.getPickCompleteSkuDate());
        doc.setPickSkuDate(packageDTO.getPickSkuDate());
        doc.setCreatedTime(packageDTO.getCreatedTime());
        doc.setUpdatedTime(packageDTO.getUpdatedTime());
        doc.setPackageStruct(packageDTO.getPackageStruct());
        doc.setInterceptCancelDate(packageDTO.getInterceptCancelDate());
        doc.setPackageStructName(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(packageDTO.getPackageStruct()).getDesc());
        convertDecimal = packageDTO.getPackageSkuQty() == null ? BigDecimal.ZERO : packageDTO.getPackageSkuQty();
        doc.setPackageSkuQty(convertDecimal.setScale(numberFormat, RoundingMode.FLOOR).toString());
        Optional<PackEnum.STATUS> optional = Optional.ofNullable(PackEnum.STATUS.findEnumDesc(packageDTO.getStatus()));
        doc.setStatusName(optional.isPresent() ? optional.get().getDesc() : "");
        if (!StringUtils.isEmpty(packageDTO.getPretreatmentStatus())) {
            Optional<PretreatmentStatusEnum> pretreatmentStatusEnumOptional = Optional.ofNullable(PretreatmentStatusEnum.getEnum(packageDTO.getPretreatmentStatus()));
            doc.setPretreatmentStatusName(pretreatmentStatusEnumOptional.isPresent() ? pretreatmentStatusEnumOptional.get().getName() : "");
        }
        Optional<PackEnum.TYPE> packEnumOptional = Optional.ofNullable(PackEnum.TYPE.findEnumDesc(packageDTO.getIsPre()));
        doc.setIsPreName(packEnumOptional.isPresent() ? packEnumOptional.get().getDesc() : "");
        doc.setIsPreDesc(packEnumOptional.isPresent() ? packEnumOptional.get().getDesc() : "");
        Optional<SkuQualityEnum> skuQualityEnumOptional = Optional.ofNullable(SkuQualityEnum.getEnum(packageDTO.getSkuQuality()));
        doc.setSkuQualityName(skuQualityEnumOptional.isPresent() ? skuQualityEnumOptional.get().getMessage() : "");
        doc.setVolumetricWeight(packageDTO.getVolumetricWeight().setScale(weightFormat, RoundingMode.FLOOR).toString());
        if (doc.getOrderTag() != null && doc.getOrderTag() > 0) {
            List<Integer> orderTagList = OrderTagEnum.NumToEnum(doc.getOrderTag()).stream().map(OrderTagEnum::getCode).collect(Collectors.toList());
            doc.setOrderTagList(orderTagList);
        }
        return doc;
    }

    private void indexForCreateForInit(List<PackageIndex> docList) {
        docList.forEach(index -> {
            RpcContextUtil.setWarehouseCode(index.getWarehouseCode());
            // 固定信息查询
            ShipmentOrderDTO shipmentOrderDTO = remotePackageEsClient.getShipmentByCode(index.getShipmentOrderCode());
            if (ObjectUtils.isEmpty(shipmentOrderDTO)) {
                return;
            }
            index.setExpShipTimeDateFormat(ConverterUtil.convertVoTime(shipmentOrderDTO.getExpShipTime() == null ? 0L : shipmentOrderDTO.getExpShipTime()));
            index.setPreSaleType(shipmentOrderDTO.getPreSaleType());
            if (!ObjectUtils.isEmpty(shipmentOrderDTO.getPreSaleType())) {
                index.setPreSaleTypeName(ShipmentPreSaleTypeEnum.fromCode(shipmentOrderDTO.getPreSaleType()).getMessage());
            } else {
                index.setPreSaleTypeName("");
            }
            List<ShipmentOrderMaterialDTO> shipmentMaterial = remotePackageEsClient.getShipmentMaterial(index.getShipmentOrderCode());
            index.setMaterialUpcCodeList(shipmentMaterial.stream().map(ShipmentOrderMaterialDTO::getRecPackUpcCode).distinct().collect(Collectors.toList()));
            index.setSaleShop(shipmentOrderDTO.getSaleShop());
            index.setSaleShopId(shipmentOrderDTO.getSaleShopId());
            index.setReceiverProv(shipmentOrderDTO.getReceiverProv());
            index.setReceiverArea(shipmentOrderDTO.getReceiverArea());
            index.setReceiverCity(shipmentOrderDTO.getReceiverCity());
            index.setReceiverAreaName(shipmentOrderDTO.getReceiverAreaName());
            index.setReceiverCityName(shipmentOrderDTO.getReceiverCityName());
            index.setReceiverProvName(shipmentOrderDTO.getReceiverProvName());
            index.setReceiverAddress(shipmentOrderDTO.getReceiverAddress());
            index.setReceiverMan(shipmentOrderDTO.getReceiverMan());
            index.setReceiverTel(shipmentOrderDTO.getReceiverTel());
            index.setOrderType(shipmentOrderDTO.getOrderType());
            index.setRemark(shipmentOrderDTO.getRemark());
            index.setExpOutStockDate(shipmentOrderDTO.getExpOutStockDate());
            index.setExpShipTime(shipmentOrderDTO.getExpShipTime());
            index.setPayDate(shipmentOrderDTO.getPayDate());
            index.setOrdCreatedTime(shipmentOrderDTO.getCreatedTime());
            CarrierDTO carrier = remotePackageEsClient.getCarrier(index.getCarrierCode());
            if (!ObjectUtils.isEmpty(carrier)) {
                index.setCarrierName(carrier.getName());
            }
            if (!StringUtils.isEmpty(index.getSalePlatform())) {
                SalePlatformDTO salePlatform = remotePackageEsClient.getSalePlatform(index.getSalePlatform());
                if (!ObjectUtils.isEmpty(salePlatform)) {
                    index.setSalePlatformName(salePlatform.getName());
                }
            }
            index.setCargoName(remotePackageEsClient.getCargoOwner(index.getCargoCode()).getName());
            // 获取skuCodeList 和 upcCodeList
            index.setSkuCodeList(remotePackageEsClient.getSkuCodeList(index.getPackageCode()));
            index.setUpcCodeList(remotePackageEsClient.getUpcCodeList(index.getPackageCode()));

            // 指定包材
            if (CollectionUtil.isNotEmpty(shipmentMaterial)) {
                index.setMaterialUpcCode(shipmentMaterial.stream().map(ShipmentOrderMaterialDTO::getRecPackUpcCode).distinct()
                        .collect(Collectors.joining(StrUtil.COMMA)));
            } else {
                index.setMaterialUpcCode("");
            }
        });
    }

    private void indexForCreate(PackageIndex index) {
        // 固定信息查询
        ShipmentOrderDTO shipmentOrderDTO = remotePackageEsClient.getShipmentByCode(index.getShipmentOrderCode());
        if (ObjectUtils.isEmpty(shipmentOrderDTO)) {
            return;
        }
        index.setExpShipTimeDateFormat(ConverterUtil.convertVoTime(shipmentOrderDTO.getExpShipTime() == null ? 0L : shipmentOrderDTO.getExpShipTime()));
        index.setPreSaleType(shipmentOrderDTO.getPreSaleType());
        if (!ObjectUtils.isEmpty(shipmentOrderDTO.getPreSaleType())) {
            index.setPreSaleTypeName(ShipmentPreSaleTypeEnum.fromCode(shipmentOrderDTO.getPreSaleType()).getMessage());
        } else {
            index.setPreSaleTypeName("");
        }
        List<ShipmentOrderMaterialDTO> shipmentMaterial = remotePackageEsClient.getShipmentMaterial(index.getShipmentOrderCode());
        index.setMaterialUpcCodeList(shipmentMaterial.stream().map(ShipmentOrderMaterialDTO::getRecPackUpcCode).distinct().collect(Collectors.toList()));
        index.setSaleShop(shipmentOrderDTO.getSaleShop());
        index.setSaleShopId(shipmentOrderDTO.getSaleShopId());
        index.setReceiverProv(shipmentOrderDTO.getReceiverProv());
        index.setReceiverArea(shipmentOrderDTO.getReceiverArea());
        index.setReceiverCity(shipmentOrderDTO.getReceiverCity());
        index.setReceiverAreaName(shipmentOrderDTO.getReceiverAreaName());
        index.setReceiverCityName(shipmentOrderDTO.getReceiverCityName());
        index.setReceiverProvName(shipmentOrderDTO.getReceiverProvName());
        index.setReceiverAddress(shipmentOrderDTO.getReceiverAddress());
        index.setReceiverMan(shipmentOrderDTO.getReceiverMan());
        index.setReceiverTel(shipmentOrderDTO.getReceiverTel());
        index.setOrderType(shipmentOrderDTO.getOrderType());
        index.setRemark(shipmentOrderDTO.getRemark());
        index.setExpOutStockDate(shipmentOrderDTO.getExpOutStockDate());
        index.setExpShipTime(shipmentOrderDTO.getExpShipTime());
        index.setPayDate(shipmentOrderDTO.getPayDate());
        index.setOrdCreatedTime(shipmentOrderDTO.getCreatedTime());
        CarrierDTO carrier = remotePackageEsClient.getCarrier(index.getCarrierCode());
        if (!ObjectUtils.isEmpty(carrier)) {
            index.setCarrierName(carrier.getName());
        }
        if (!StringUtils.isEmpty(index.getSalePlatform())) {
            SalePlatformDTO salePlatform = remotePackageEsClient.getSalePlatform(index.getSalePlatform());
            if (!ObjectUtils.isEmpty(salePlatform)) {
                index.setSalePlatformName(salePlatform.getName());
            }
        }
        index.setCargoName(remotePackageEsClient.getCargoOwner(index.getCargoCode()).getName());
        // 获取skuCodeList 和 upcCodeList
        index.setSkuCodeList(remotePackageEsClient.getSkuCodeList(index.getPackageCode()));
        index.setUpcCodeList(remotePackageEsClient.getUpcCodeList(index.getPackageCode()));

        // 指定包材
        if (CollectionUtil.isNotEmpty(shipmentMaterial)) {
            index.setMaterialUpcCode(shipmentMaterial.stream().map(ShipmentOrderMaterialDTO::getRecPackUpcCode).distinct()
                    .collect(Collectors.joining(StrUtil.COMMA)));
        } else {
            index.setMaterialUpcCode("");
        }
    }


    private void indexForPick(PackageIndex index) {
        RpcContextUtil.setWarehouseCode(index.getWarehouseCode());
        PickInfo pickInfo = remotePackageEsClient.getPickInfo(index.getPackageCode());
        index.setExpressPrintNum(pickInfo.getExpressPrintNum());
        if (!CollectionUtils.isEmpty(pickInfo.getPickCodeList())) {
            index.setPickCode(pickInfo.getPickCodeList().get(0));
        }
    }

}
