package com.dt.elasticsearch.wms.mq;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.dt.component.canal.mq.AbstractCanalMQService;
import com.dt.component.common.enums.Deleted;
import com.dt.component.common.enums.stock.OperationTypeEnum;
import com.dt.domain.core.stock.dto.StockSerialDTO;
import com.dt.elasticsearch.wms.biz.IStockOccupyViewBiz;
import com.dt.elasticsearch.wms.config.IndexNameConfig;
import com.dt.elasticsearch.wms.integration.domain.StockOccupyViewIndex;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RocketMQMessageListener(topic = "dt_wms_stock_serial_topic", consumerGroup = "StockOccupyView", consumeMode = ConsumeMode.ORDERLY)
public class StockSerialConsumerForOccupyView extends AbstractCanalMQService<StockSerialDTO> implements RocketMQListener<FlatMessage> {

    @Autowired
    private IStockOccupyViewBiz stockOccupyViewBiz;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private IndexNameConfig nameConfig;

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }

    @Override
    public void insert(StockSerialDTO stockSerialDTO) {
        handleMessage(stockSerialDTO);
    }

    @Override
    public void update(StockSerialDTO before, StockSerialDTO after) {
        handleMessage(after);
    }

    @Override
    public void delete(StockSerialDTO stockSerialDTO) {
        stockSerialDTO.setDeleted(Deleted.DELETE.getCode());
        handleMessage(stockSerialDTO);
    }

    private void handleMessage(StockSerialDTO stockSerialDTO) {
        if (DateUtil.between(DateTime.now(),DateUtil.date(stockSerialDTO.getCreatedTime()), DateUnit.DAY) > 100) {
            log.info("skip old data {}", DateUtil.date(stockSerialDTO.getCreatedTime()).toDateStr());
            return;
        }
        try {
            if(stockSerialDTO != null && StringUtils.isEmpty(stockSerialDTO.getOperationType())){
                log.info("StockOccupyView OperationType empty:{}",JSONUtil.toJsonStr(stockSerialDTO));
                return;
            }
            log.info("{} {} {} {} {}",stockSerialDTO.getGlobalNo(),stockSerialDTO.getBillNo(),stockSerialDTO.getSkuCode(),stockSerialDTO.getStockLevel(),stockSerialDTO.getTransactionSerialNo());
            String globalNo = globalNo(stockSerialDTO);
            if (StrUtil.isBlank(globalNo)) return;
            StockOccupyViewIndex stockOccupyViewIndex = null;
            GetRequest getRequest = new GetRequest(nameConfig.getStockOccupyViewIndexName());
            getRequest.id(globalNo);
            GetResponse documentFields = restHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
            if (documentFields.isExists()) {
                stockOccupyViewIndex = JSONUtil.parseObj(documentFields.getSource()).toBean(StockOccupyViewIndex.class);
            }
            if (stockOccupyViewIndex == null) {
                stockOccupyViewIndex = new StockOccupyViewIndex();
                stockOccupyViewIndex.setIndexId(globalNo);
            }
            if (CollectionUtil.isEmpty(stockOccupyViewIndex.getStockSerialDTOList())) {
                stockOccupyViewIndex.setStockSerialDTOList(new ArrayList<>());
            }
            List<StockSerialDTO> stockSerialDTOList = stockOccupyViewIndex.getStockSerialDTOList().stream()
                    .filter(it -> !it.getId().equals(stockSerialDTO.getId()))
                    .collect(Collectors.toList());
            stockSerialDTOList.add(stockSerialDTO);
            stockOccupyViewIndex.setWarehouseCode(stockSerialDTO.getWarehouseCode());
            stockOccupyViewIndex.setCargoCode(stockSerialDTO.getCargoCode());
            stockOccupyViewIndex.setStockSerialDTOList(stockSerialDTOList);
            stockOccupyViewIndex.setLastModifyTime(System.currentTimeMillis());
            stockOccupyViewBiz.refresh(stockOccupyViewIndex);
            IndexRequest indexRequest = new IndexRequest(nameConfig.getStockOccupyViewIndexName(), "_doc", globalNo);
            indexRequest.source(JSONUtil.toJsonStr(stockOccupyViewIndex), XContentType.JSON);
            IndexResponse indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            if (!indexResponse.getResult().equals(DocWriteResponse.Result.CREATED) && !indexResponse.getResult().equals(DocWriteResponse.Result.UPDATED)) {
                throw new RuntimeException("index fail");
            }
        } catch (ElasticsearchStatusException exception) {
            if (exception.getDetailedMessage().contains("index_not_found_exception")) {
                try {
                    CreateIndexRequest createIndexRequest = new CreateIndexRequest(nameConfig.getStockOccupyViewIndexName());
                    createIndexRequest.settings(Settings.builder().put("number_of_shards","1").put("number_of_replicas","2").build());
                    restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
                } catch (IOException e) {
                    throw new RuntimeException("create index fail");
                }
            }
            throw new RuntimeException(exception);
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            throw new RuntimeException(exception);
        }
    }

    private String globalNo(StockSerialDTO stockSerialDTO) {
        OperationTypeEnum operationTypeEnum = OperationTypeEnum.getEnum(stockSerialDTO.getOperationType());
        switch (operationTypeEnum) {
            case OPERATION_UPSTREAM_OCCUPY:
            case OPERATION_UPSTREAM_RELEASE:
            case OPERATION_SHIPMENT:
            case OPERATION_INTERCEPT:
            case OPERATION_CANCEL_SHIPMENT:
            case OPERATION_STOCK:
            case OPERATION_CONVERGE:
            case OPERATION_CANCEL_ALLOCATION:
                return stockSerialDTO.getGlobalNo();
        }
        return StrUtil.EMPTY;
    }
}
