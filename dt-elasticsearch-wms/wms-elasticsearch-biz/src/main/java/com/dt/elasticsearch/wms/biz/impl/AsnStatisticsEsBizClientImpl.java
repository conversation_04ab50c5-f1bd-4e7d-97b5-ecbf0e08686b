package com.dt.elasticsearch.wms.biz.impl;


import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.stereotype.Service;

import com.dt.component.common.enums.asn.AsnStatusEnum;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.client.IAsnDetailBillClient;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.domain.bill.dto.AsnDetailDTO;
import com.dt.domain.bill.param.AsnDetailParam;
import com.dt.elasticsearch.wms.biz.IAsnStatisticsEsBizClient;
import com.dt.elasticsearch.wms.config.IndexNameConfig;
import com.dt.elasticsearch.wms.integration.domain.AsnStatisticsIndex;

import cn.hutool.core.date.DateUtil;

/**
 * @Author: yousx
 * @Date: 2025/06/09
 * @Description:
 */
@Service
public class AsnStatisticsEsBizClientImpl implements IAsnStatisticsEsBizClient {

    @DubboReference
    private IAsnDetailBillClient asnDetailBillClient;
    @DubboReference
    private IndexNameConfig nameConfig;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Override
    public void save(AsnDTO asnDTO) {
        if (!Objects.equals(AsnStatusEnum.COMPLETE.getCode(), asnDTO.getStatus())) {
            return;
        }
        AsnDetailParam param = new AsnDetailParam();
        param.setAsnId(asnDTO.getAsnId());
        Result<List<AsnDetailDTO>> result = asnDetailBillClient.getList(param);
        List<AsnDetailDTO> detailList = result.getData();
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        BigDecimal totalVolume = BigDecimal.ZERO;
        BigDecimal totalGrossWeight = BigDecimal.ZERO;
        BigDecimal totalNetWeight = BigDecimal.ZERO;
        for (AsnDetailDTO asnDetailBizDTO : detailList) {
            if (Objects.nonNull(asnDetailBizDTO.getRecSkuQty())) {
                if (Objects.nonNull(asnDTO.getVolume())) {
                    totalVolume = totalVolume.add(asnDTO.getVolume().multiply(asnDetailBizDTO.getRecSkuQty()));
                }
                if (Objects.nonNull(asnDTO.getGrossWeight())) {
                    totalGrossWeight = totalGrossWeight.add(asnDTO.getGrossWeight().multiply(asnDetailBizDTO.getRecSkuQty()));
                }
                if (Objects.nonNull(asnDTO.getNetWeight())) {
                    totalNetWeight = totalNetWeight.add(asnDTO.getNetWeight().multiply(asnDetailBizDTO.getRecSkuQty()));
                }
            }
        }
        AsnStatisticsIndex asnStatisticsIndex = new AsnStatisticsIndex();
        asnStatisticsIndex.setType(asnDTO.getType());
        asnStatisticsIndex.setIndexId(asnDTO.getWarehouseCode() + ":" + asnDTO.getAsnId());
        asnStatisticsIndex.setCargoCode(asnDTO.getCargoCode());
        asnStatisticsIndex.setVolume(totalVolume);
        asnStatisticsIndex.setGrossWeight(totalGrossWeight);
        asnStatisticsIndex.setNetWeight(totalNetWeight);
        asnStatisticsIndex.setRecSkuQty(asnDTO.getRecSkuQty());
        asnStatisticsIndex.setRecSkuType(asnDTO.getRecSkuType());
        asnStatisticsIndex.setRecDateEnd(asnDTO.getRecDateEnd());
        if (Objects.nonNull(asnDTO.getRecDateStart())) {
            asnStatisticsIndex.setStatDate(DateUtil.format(new Date(asnDTO.getRecDateEnd()), "yyyy-MM-dd"));
        }

        IndexQuery query = new IndexQueryBuilder().withId(asnStatisticsIndex.getIndexId())
                .withIndexName(nameConfig.getAsnStatisticsIndexName())
                .withObject(asnStatisticsIndex).withType("_doc").build();
        elasticsearchRestTemplate.index(query);
    }
}
