package com.dt.elasticsearch.wms.mq;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.dt.component.canal.mq.AbstractCanalMQService;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.elasticsearch.wms.biz.IAsnStatisticsEsBizClient;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: yousx
 * @Date: 2025/06/04
 * @Description:
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "dt_wms_asn_topic", consumerGroup = "asn_order_statistics")
public class AsnStatisticsConsumer extends AbstractCanalMQService<AsnDTO> implements RocketMQListener<FlatMessage> {

    @Resource
    private IAsnStatisticsEsBizClient asnStatisticsEsBizClient;


    @Override
    protected void insert(AsnDTO asnDTO) {
        log.info("asn insert {}", JSONUtil.toJsonStr(asnDTO));
        asnStatisticsEsBizClient.save(asnDTO);
    }

    @Override
    protected void update(AsnDTO before, AsnDTO after) {
        log.info("asn update {}", JSONUtil.toJsonStr(after));
        asnStatisticsEsBizClient.save(after);
    }

    @Override
    protected void delete(AsnDTO asnDTO) {

    }

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }
}
