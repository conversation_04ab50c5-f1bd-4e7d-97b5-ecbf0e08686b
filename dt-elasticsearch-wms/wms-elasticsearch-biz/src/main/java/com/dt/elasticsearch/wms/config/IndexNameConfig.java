package com.dt.elasticsearch.wms.config;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 索引名称配置 按月创建索引
 */
@Component
public class IndexNameConfig {
    @Value("${elasticsearch.shipment.index-name}")
    private String shipmentOrderIndexName;

    @Value("${elasticsearch.package.index-name}")
    private String packageIndexName;

    @Value("${elasticsearch.log.index-name}")
    private String shipmentOrderLogIndexName;

    @Value("${elasticsearch.stock.lot.index-name:dt_stock_lot}")
    private String stockLotIndexName;

    @Value("${elasticsearch.stock.occupy.view.index-name:dt_stock_occupy_view}")
    private String stockOccupyViewIndexName;

    @Value("${elasticsearch.asn.statistics.index-name:dt_asn_statistics}")
    private String asnStatisticsIndexName;
    
    public String getShipmentOrderIndexName() {
        return shipmentOrderIndexName + "_" + new SimpleDateFormat("yyyyMM").format(new Date());
    }

    public String getPackageIndexName() {
        return packageIndexName + "_" + new SimpleDateFormat("yyyyMM").format(new Date());
    }

    public String getShipmentOrderLogIndexName() {
        return shipmentOrderLogIndexName + "_" + new SimpleDateFormat("yyyyMM").format(new Date());
    }
    
    public String getStockLotIndexName() {
        return stockLotIndexName;
    }


    public String getStockOccupyViewIndexName() {
        return stockOccupyViewIndexName;
    }

    public String getAsnStatisticsIndexName() {
        return asnStatisticsIndexName;
    }
}
