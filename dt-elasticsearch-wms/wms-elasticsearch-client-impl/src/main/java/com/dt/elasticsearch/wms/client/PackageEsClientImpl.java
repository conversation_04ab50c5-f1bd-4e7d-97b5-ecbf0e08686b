package com.dt.elasticsearch.wms.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.wms.dto.PackageExportDTO;
import com.dt.elasticsearch.wms.dto.PackageIndexDTO;
import com.dt.elasticsearch.wms.dto.PackageScrollResultDTO;
import com.dt.elasticsearch.wms.integration.domain.PackageIndex;
import com.dt.elasticsearch.wms.param.PackageEsParam;
import com.dt.platform.utils.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/9 11:35
 */
@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class PackageEsClientImpl implements IPackageEsClient {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Value("${elasticsearch.package.index-name}")
    private String packageIndexName;

    @Override
    public Result<PageVO<PackageIndexDTO>> getPage(PackageEsParam param) {
        QueryBuilder query = getQuery(param);
        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(query)
                // 这里使用配置获取索引名称 相当于 {indexName}*
                .withIndices(packageIndexName + "*")
                .withTypes("_doc")
                .withSort(SortBuilders.fieldSort("createdTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(param.getCurrent() == 0 ? 0 : param.getCurrent() - 1, param.getSize())).build();
        AggregatedPage<PackageIndex> result = elasticsearchRestTemplate.queryForPage(searchQuery, PackageIndex.class);
        List<PackageIndex> content = result.getContent();
        List<PackageIndexDTO> dataList = content.stream().map(it -> ConverterUtil.convert(it, PackageIndexDTO.class)).collect(Collectors.toList());
        PageVO<PackageIndexDTO> pageVO = new PageVO<>();
        PageVO.Page page = new PageVO.Page();
        page.setCurrentPage(Long.valueOf(param.getCurrent()));
        page.setPageSize((long) result.getTotalPages());
        page.setTotalCount(result.getTotalElements());
        page.setPageSize(Long.valueOf(param.getSize()));
        page.setTotalPage((long) result.getTotalPages());
        pageVO.setPage(page);
        pageVO.setDataList(dataList);
        return Result.success(pageVO);
    }

    @Override
    public Result<PackageScrollResultDTO> getByScroll(PackageEsParam param) {
        PackageScrollResultDTO resultDTO = new PackageScrollResultDTO();
        QueryBuilder query = getQuery(param);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(query)
                .withIndices(packageIndexName + "*")
                .withTypes("_doc")
                .withPageable(PageRequest.of(0, 5000))
                .build();
        long scrollTimeInMills = 5 * 60 * 1000;
        ScrolledPage<PackageIndex> indices;
        String scrollId = param.getScrollId();
        try {
            if (StringUtils.isEmpty(scrollId)) {
                indices = elasticsearchRestTemplate.startScroll(scrollTimeInMills, searchQuery, PackageIndex.class);
                scrollId = indices.getScrollId();
                resultDTO.setDataList(buildExportDTO(indices.getContent()));
                resultDTO.setScrollId(scrollId);
            } else {
                indices = elasticsearchRestTemplate.continueScroll(scrollId, scrollTimeInMills, PackageIndex.class);
                resultDTO.setDataList(buildExportDTO(indices.getContent()));
                resultDTO.setScrollId(indices.getScrollId());
            }
            if (!indices.hasContent()) {
                elasticsearchRestTemplate.clearScroll(scrollId);
            }
        } catch (Exception e) {
            if (!StringUtils.isEmpty(scrollId)) {
                elasticsearchRestTemplate.clearScroll(scrollId);
            }
        }
        return Result.success(resultDTO);
    }

    private List<PackageExportDTO> buildExportDTO(List<PackageIndex> indexList) {
        List<PackageExportDTO> dataList = new ArrayList<>();
        indexList.forEach(index -> {
            PackageExportDTO packageExportDTO = ConverterUtil.convert(index, PackageExportDTO.class);
            assert packageExportDTO != null;
            packageExportDTO.setPackageMaterial(index.getMaterialUpcCode());
            packageExportDTO.setExpShipTimeDate(index.getExpShipTimeDateFormat());
            packageExportDTO.setOrdCreateTime(ConverterUtil.convertVoTime(index.getCreatedTime()));
            packageExportDTO.setPackageType(index.getIsPreDesc());
            packageExportDTO.setTradeType(ShipmentOrderEnum.ORDER_TYPE.findEnumDesc(index.getOrderType()).getDesc());
            packageExportDTO.setSalePlatform(index.getSalePlatformName());
            packageExportDTO.setExpSkuQty(new BigDecimal(index.getPackageSkuQty()));
            packageExportDTO.setOutSkuQty(new BigDecimal(index.getOutSkuQty()));
            packageExportDTO.setOutStockDate(ConverterUtil.convertVoTime(index.getOutStockDate()));
            packageExportDTO.setPickCompleteSkuDate(ConverterUtil.convertVoTime(index.getPickCompleteSkuDate()));
            packageExportDTO.setCheckStartDate(ConverterUtil.convertVoTime(index.getCheckStartDate()));
            packageExportDTO.setCheckCompleteDate(ConverterUtil.convertVoTime(index.getCheckCompleteDate()));
            packageExportDTO.setInterceptCancelDate(ConverterUtil.convertVoTime(index.getInterceptCancelDate()));
            dataList.add(packageExportDTO);
        });
        return dataList;
    }

    /**
     * 获取分页查询 query
     *
     * @param param
     * @return
     */
    private QueryBuilder getQuery(PackageEsParam param) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        // 仓库编码
        TermQueryBuilder warehouseCodeQuery = QueryBuilders.termQuery("warehouseCode.keyword", CurrentRouteHolder.getWarehouseCode());
        boolQuery.must(warehouseCodeQuery);
        //如果来源是包裹日汇总页面，查询总包数据时，不查询已取消的包裹数
        if (!ObjectUtils.isEmpty(param.getSummarySelect()) && param.getSummarySelect()) {
            TermQueryBuilder statusQuery = QueryBuilders.termQuery("status.keyword", PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode());
            boolQuery.mustNot(statusQuery);
        }
        // 货主编码
        if (StrUtil.isNotEmpty(param.getCargoCode())) {
            TermQueryBuilder cargoCodeQuery = QueryBuilders.termQuery("cargoCode.keyword", param.getCargoCode());
            boolQuery.must(cargoCodeQuery);
        }
        if (CollectionUtil.isNotEmpty(param.getCargoCodeList())) {
            TermsQueryBuilder cargoCodeQuery = QueryBuilders.termsQuery("cargoCode.keyword", param.getCargoCodeList());
            boolQuery.must(cargoCodeQuery);
        }
        // 出库单号
        if (CollectionUtil.isNotEmpty(param.getShipmentOrderCodeList())) {
            TermsQueryBuilder shipmentOrderCodeQuery = QueryBuilders.termsQuery("shipmentOrderCode.keyword", param.getShipmentOrderCodeList());
            boolQuery.must(shipmentOrderCodeQuery);
        }
        //上游单号
        if (CollectionUtil.isNotEmpty(param.getPoNoList())) {
            TermsQueryBuilder poNoQuery = QueryBuilders.termsQuery("poNo.keyword", param.getPoNoList());
            boolQuery.must(poNoQuery);
        }
        //ERP单号
        if (CollectionUtil.isNotEmpty(param.getSoNoList())) {
            TermsQueryBuilder soNoQuery = QueryBuilders.termsQuery("soNo.keyword", param.getSoNoList());
            boolQuery.must(soNoQuery);
        }
        // 包裹号
        if (CollectionUtil.isNotEmpty(param.getPackageCodeList())) {
            TermsQueryBuilder packageCodeQuery = QueryBuilders.termsQuery("packageCode.keyword", param.getPackageCodeList());
            boolQuery.must(packageCodeQuery);
        }
        // 拣选单号
        if (StringUtils.isNotBlank(param.getPickCode())) {
            TermsQueryBuilder pickCodeQuery = QueryBuilders.termsQuery("pickCode.keyword", param.getPickCode().toUpperCase());
            boolQuery.must(pickCodeQuery);
        }
        // 包裹状态
        if (StrUtil.isNotEmpty(param.getStatus())) {
            TermQueryBuilder statusQuery = QueryBuilders.termQuery("status.keyword", param.getStatus());
            boolQuery.must(statusQuery);
        }
        // 包裹状态
        if (CollectionUtil.isNotEmpty(param.getStatusList())) {
            TermsQueryBuilder statusQuery = QueryBuilders.termsQuery("status.keyword", param.getStatusList());
            boolQuery.must(statusQuery);
        }
        // 单据类型
        if (StringUtils.isNotBlank(param.getOrderType())) {
            TermsQueryBuilder orderTypeQuery = QueryBuilders.termsQuery("orderType.keyword", param.getOrderType());
            boolQuery.must(orderTypeQuery);
        }

        // 业务类型
        if (StringUtils.isNotBlank(param.getBusinessType())) {
            TermsQueryBuilder businessTypeQuery = QueryBuilders.termsQuery("businessType.keyword", param.getBusinessType());
            boolQuery.must(businessTypeQuery);
        }
        // 预售类型
        if (ObjectUtils.isNotEmpty(param.getPreSaleType())) {
            TermQueryBuilder preSaleTypeQuery = QueryBuilders.termQuery("preSaleType", param.getPreSaleType());
            boolQuery.must(preSaleTypeQuery);
        }
        // 快递公司
        if (StrUtil.isNotEmpty(param.getCarrierCode())) {
            TermQueryBuilder carrierCodeQuery = QueryBuilders.termQuery("carrierCode.keyword", param.getCarrierCode());
            boolQuery.must(carrierCodeQuery);
        }
        if (CollectionUtil.isNotEmpty(param.getCarrierCodeList())) {
            TermsQueryBuilder carrierCodeQuery = QueryBuilders.termsQuery("carrierCode.keyword", param.getCarrierCodeList());
            boolQuery.must(carrierCodeQuery);
        }
        // 快递单号
        if (CollectionUtil.isNotEmpty(param.getExpressNoList())) {
            TermsQueryBuilder expressNoQuery = QueryBuilders.termsQuery("expressNo.keyword", param.getExpressNoList());
            boolQuery.must(expressNoQuery);
        }
        // 商品代码
        if (CollectionUtil.isNotEmpty(param.getSkuCodeList())) {
            TermsQueryBuilder skuCodeQuery = QueryBuilders.termsQuery("skuCodeList.keyword", param.getSkuCodeList());
            boolQuery.must(skuCodeQuery);
        }
        // 商品条码
        if (CollectionUtil.isNotEmpty(param.getUpcCodeList())) {
            TermsQueryBuilder upcCodeQuery = QueryBuilders.termsQuery("upcCodeList.keyword", param.getUpcCodeList());
            boolQuery.must(upcCodeQuery);
        }
        // 交易单号
        if (CollectionUtil.isNotEmpty(param.getTradeNoList())) {
            TermsQueryBuilder tradeNoQuery = QueryBuilders.termsQuery("tradeNo.keyword", param.getTradeNoList());
            boolQuery.must(tradeNoQuery);
        }
        // 销售平台
        if (StringUtils.isNotBlank(param.getSalePlatform())) {
            TermsQueryBuilder salePlatformQuery = QueryBuilders.termsQuery("salePlatform.keyword", param.getSalePlatform());
            boolQuery.must(salePlatformQuery);
        }
        // 销售店铺
        if (StringUtils.isNotBlank(param.getSaleShop())) {
            TermsQueryBuilder saleShopQuery = QueryBuilders.termsQuery("saleShop.keyword", param.getSaleShop());
            boolQuery.must(saleShopQuery);
        }
        // 指定包材
        if (StringUtils.isNotBlank(param.getMaterialUpcCode())) {
            TermsQueryBuilder materialUpcCodeQuery = QueryBuilders.termsQuery("materialUpcCodeList.keyword", param.getMaterialUpcCode());
            boolQuery.must(materialUpcCodeQuery);
        }
        // 收货人姓名
        if (StringUtils.isNotBlank(param.getReceiverMan())) {
            TermsQueryBuilder receiverManQuery = QueryBuilders.termsQuery("receiverMan.keyword", param.getReceiverMan());
            boolQuery.must(receiverManQuery);
        }
        // 收货人电话
        if (StringUtils.isNotBlank(param.getReceiverTel())) {
            TermsQueryBuilder receiverTelQuery = QueryBuilders.termsQuery("receiverTel.keyword", param.getReceiverTel());
            boolQuery.must(receiverTelQuery);
        }
        // 收货省份
        if (StringUtils.isNotBlank(param.getReceiverArea())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverArea.keyword", param.getReceiverArea());
            boolQuery.must(receiverQuery);
        }
        if (StringUtils.isNotBlank(param.getReceiverAreaName())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverAreaName.keyword", param.getReceiverAreaName());
            boolQuery.must(receiverQuery);
        }
        if (StringUtils.isNotBlank(param.getReceiverCity())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverCity.keyword", param.getReceiverCity());
            boolQuery.must(receiverQuery);
        }
        if (StringUtils.isNotBlank(param.getReceiverCityName())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverCityName.keyword", param.getReceiverCityName());
            boolQuery.must(receiverQuery);
        }
        if (StringUtils.isNotBlank(param.getReceiverProv())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverProv.keyword", param.getReceiverProv());
            boolQuery.must(receiverQuery);
        }
        if (StringUtils.isNotBlank(param.getReceiverProvName())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverProvName.keyword", param.getReceiverProvName());
            boolQuery.must(receiverQuery);
        }

        //省市区多个
        if (CollectionUtil.isNotEmpty(param.getReceiverProvList())) {
            TermsQueryBuilder receiverProvQueryList = QueryBuilders.termsQuery("receiverProv.keyword", param.getReceiverProvList());
            boolQuery.must(receiverProvQueryList);
        }
        if (CollectionUtil.isNotEmpty(param.getReceiverCityList())) {
            TermsQueryBuilder receiverCityQueryList = QueryBuilders.termsQuery("receiverCity.keyword", param.getReceiverCityList());
            boolQuery.must(receiverCityQueryList);
        }
        if (CollectionUtil.isNotEmpty(param.getReceiverAreaList())) {
            TermsQueryBuilder receiverAreaQueryList = QueryBuilders.termsQuery("receiverArea.keyword", param.getReceiverAreaList());
            boolQuery.must(receiverAreaQueryList);
        }

        // 包裹品种类型
        if (CollectionUtil.isNotEmpty(param.getPackageStructList())) {
            TermsQueryBuilder packageStructQuery = QueryBuilders.termsQuery("packageStruct.keyword", param.getPackageStructList());
            boolQuery.must(packageStructQuery);
        }
        // 预计出库时间
        RangeQueryBuilder expOutStockDate = QueryBuilders.rangeQuery("expOutStockDate");
        if (ObjectUtils.isNotEmpty(param.getStartExpOutStockDate())) {
            expOutStockDate.from(param.getStartExpOutStockDate());
            boolQuery.must(expOutStockDate);
        }
        if (ObjectUtils.isNotEmpty(param.getEndExpOutStockDate())) {
            if (ObjectUtils.isEmpty(param.getStartExpOutStockDate())) {
                expOutStockDate.from(1);
            }
            expOutStockDate.to(param.getEndExpOutStockDate());
            boolQuery.must(expOutStockDate);
        }

        // 付款时间
        RangeQueryBuilder payDate = QueryBuilders.rangeQuery("payDate");
        if (ObjectUtils.isNotEmpty(param.getStartPayTime())) {
            payDate.from(param.getStartPayTime());
            boolQuery.must(payDate);
        }
        if (ObjectUtils.isNotEmpty(param.getEndPayTime())) {
            if (ObjectUtils.isEmpty(param.getEndPayTime())) {
                payDate.from(1);
            }
            payDate.to(param.getEndPayTime());
            boolQuery.must(payDate);
        }
        // 包裹类型
        if (StringUtils.isNotBlank(param.getIsPre())) {
            TermQueryBuilder isPre = QueryBuilders.termQuery("isPre.keyword", param.getIsPre());
            boolQuery.must(isPre);
        }
        // 包裹创建时间
        RangeQueryBuilder ordCreatedTime = QueryBuilders.rangeQuery("ordCreatedTime");
        if (ObjectUtils.isNotEmpty(param.getOrdCreatedTimeStart())) {
            ordCreatedTime.from(param.getOrdCreatedTimeStart());
            boolQuery.must(ordCreatedTime);
        }
        if (ObjectUtils.isNotEmpty(param.getOrdCreatedTimeEnd())) {
            if (ObjectUtils.isEmpty(param.getOrdCreatedTimeStart())) {
                ordCreatedTime.from(1);
            }
            ordCreatedTime.to(param.getOrdCreatedTimeEnd());
            boolQuery.must(ordCreatedTime);
        }

        // 出库日期
        RangeQueryBuilder outStockDate = QueryBuilders.rangeQuery("outStockDate");
        if (ObjectUtils.isNotEmpty(param.getStartOutStockDate())) {
            outStockDate.from(param.getStartOutStockDate());
            boolQuery.must(outStockDate);
        }
        if (ObjectUtils.isNotEmpty(param.getEndOutStockDate())) {
            if (ObjectUtils.isEmpty(param.getStartOutStockDate())) {
                outStockDate.from(1);
            }
            outStockDate.to(param.getEndOutStockDate());
            boolQuery.must(outStockDate);
        }

        // 创建时间
        RangeQueryBuilder createdTime = QueryBuilders.rangeQuery("createdTime");
        if (ObjectUtils.isNotEmpty(param.getCreatedTimeStart())) {
            createdTime.from(param.getCreatedTimeStart());
            boolQuery.must(createdTime);
        }
        if (ObjectUtils.isNotEmpty(param.getCreatedTimeEnd())) {
            if (ObjectUtils.isEmpty(param.getCreatedTimeStart())) {
                createdTime.from(1);
            }
            createdTime.to(param.getCreatedTimeEnd());
            boolQuery.must(createdTime);
        }

        // 最晚出库时间
        RangeQueryBuilder expShipTime = QueryBuilders.rangeQuery("expShipTime");
        if (ObjectUtils.isNotEmpty(param.getExpShipTimeStart())) {
            expShipTime.from(param.getExpShipTimeStart());
            boolQuery.must(expShipTime);
        }
        if (ObjectUtils.isNotEmpty(param.getExpShipTimeEnd())) {
            if (ObjectUtils.isEmpty(param.getExpShipTimeStart())) {
                expShipTime.from(1);
            }
            expShipTime.to(param.getExpShipTimeEnd());
            boolQuery.must(expShipTime);
        }

        // 网点名称
        TermsQueryBuilder expressBranchName = QueryBuilders.termsQuery("expressBranchName.keyword", param.getExpressBranchName());
        if (StringUtils.isNotEmpty(param.getExpressBranchName())) {
            boolQuery.must(expressBranchName);
        }

        // 拣选完成时间
        RangeQueryBuilder pickCompleteSkuDate = QueryBuilders.rangeQuery("pickCompleteSkuDate");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getPickCompleteSkuDateStart())) {
            pickCompleteSkuDate.from(param.getPickCompleteSkuDateStart());
            boolQuery.must(pickCompleteSkuDate);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getPickCompleteSkuDateEnd())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getPickCompleteSkuDateStart())) {
                pickCompleteSkuDate.from(1);
            }
            pickCompleteSkuDate.to(param.getPickCompleteSkuDateEnd());
            boolQuery.must(pickCompleteSkuDate);
        }

        // 复核完成时间
        RangeQueryBuilder checkCompleteDate = QueryBuilders.rangeQuery("checkCompleteDate");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getCheckCompleteDateStart())) {
            checkCompleteDate.from(param.getCheckCompleteDateStart());
            boolQuery.must(checkCompleteDate);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getCheckCompleteDateEnd())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getCheckCompleteDateStart())) {
                checkCompleteDate.from(1);
            }
            checkCompleteDate.to(param.getCheckCompleteDateEnd());
            boolQuery.must(checkCompleteDate);
        }

        // 订单标记
        if (!CollectionUtils.isEmpty(param.getOrderTagList())) {
            param.getOrderTagList().forEach(it -> {
                boolQuery.must(QueryBuilders.matchQuery("orderTagList", it));
            });
        }

        // 品种数
        RangeQueryBuilder skuTypeQty = QueryBuilders.rangeQuery("skuTypeQty");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartSkuTypeCount())) {
            skuTypeQty.from(param.getStartSkuTypeCount());
            boolQuery.must(skuTypeQty);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getEndSkuTypeCount())) {
            skuTypeQty.to(param.getEndSkuTypeCount());
            boolQuery.must(skuTypeQty);
        }
        // 商品数
        RangeQueryBuilder skuQty = QueryBuilders.rangeQuery("packageSkuQty");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartSkuCount())) {
            skuQty.from(param.getStartSkuCount());
            boolQuery.must(skuQty);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getEndSkuCount())) {
            skuQty.to(param.getEndSkuCount());
            boolQuery.must(skuQty);
        }
//        // 付款时间
//        RangeQueryBuilder payDate = QueryBuilders.rangeQuery("payDate");
//        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartPayTime())) {
//            payDate.from(param.getStartPayTime());
//            boolQuery.must(payDate);
//        }
//        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getEndPayTime())) {
//            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartPayTime())) {
//                payDate.from(1);
//            }
//            payDate.to(param.getEndPayTime());
//            boolQuery.must(payDate);
//        }
        // 理论重量
        RangeQueryBuilder weight = QueryBuilders.rangeQuery("weight");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getWeightStart())) {
            weight.from(param.getWeightStart());
            boolQuery.must(weight);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getWeightEnd())) {
            weight.to(param.getWeightEnd());
            boolQuery.must(weight);
        }

        return boolQuery;
    }

}
