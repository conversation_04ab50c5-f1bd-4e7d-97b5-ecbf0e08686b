package com.dt.elasticsearch.wms.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.es.EsDateTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IndexVO;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.wms.config.IndexNameConfig;
import com.dt.elasticsearch.wms.dto.ShipmentAnalysisDTO;
import com.dt.elasticsearch.wms.dto.ShipmentExportDTO;
import com.dt.elasticsearch.wms.dto.ShipmentIndexDTO;
import com.dt.elasticsearch.wms.dto.ShipmentScrollResultDTO;
import com.dt.elasticsearch.wms.integration.domain.OrderStatisticsLogIndex;
import com.dt.elasticsearch.wms.integration.domain.ShipmentIndex;
import com.dt.elasticsearch.wms.param.ShipmentAnalysisParam;
import com.dt.elasticsearch.wms.param.ShipmentEsParam;
import com.dt.elasticsearch.wms.param.ShipmentOrderEsParam;
import com.dt.platform.utils.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.AbstractAggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.ExtendedBounds;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.dt.platform.wms.constant.MaterialCalculateConstant.CALCULATE_MATERIAL_BAR_CODE;

/**
 * <AUTHOR>
 */
@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class ShipmentOrderEsClientImpl implements IShipmentOrderEsClient {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Value("${elasticsearch.shipment.index-name}")
    private String shipmentOrderIndexName;

    @Value("${elasticsearch.log.index-name}")
    private String shipmentOrderLogIndexName;

    @Resource
    private IndexNameConfig nameConfig;

    @Override
    public IndexVO getData(ShipmentOrderEsParam param) {
        // 判断索引是否存在 如果不存在就创建一个空的索引 防止下面查询报错
        if (!elasticsearchRestTemplate.indexExists(nameConfig.getShipmentOrderLogIndexName())) {
            elasticsearchRestTemplate.createIndex(nameConfig.getShipmentOrderLogIndexName(), OrderStatisticsLogIndex.class);
        }

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        // WMS
        queryBuilder.must(QueryBuilders.termsQuery("sysType.keyword", "WMS"));
        // 状态为创建的出库单
        queryBuilder.must(QueryBuilders.termsQuery("status.keyword", param.getStatus()));
        // 仓库编码
        if (!StringUtils.isEmpty(param.getWarehouseCode())) {
            queryBuilder.must(QueryBuilders.termsQuery("warehouseCode.keyword", param.getWarehouseCode()));
        } else {
            queryBuilder.must(QueryBuilders.termsQuery("warehouseCode.keyword", CurrentRouteHolder.getWarehouseCode()));
        }
        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("timestamp");
        if (!ObjectUtils.isEmpty(param.getCreatedTimeStart())) {
            rangeQueryBuilder.from(param.getCreatedTimeStart(), true);
        }
        if (!ObjectUtils.isEmpty(param.getCreatedTimeEnd())) {
            rangeQueryBuilder.to(param.getCreatedTimeEnd(), true);
        }
        queryBuilder.must(rangeQueryBuilder);
        // 根据时间段分割
        if (StringUtils.isEmpty(param.getType())) {
            param.setType(EsDateTypeEnum.EVERY_HOUR.code());
        }
        Date toady = new Date();
        if (ObjectUtils.isEmpty(param.getCreatedTimeStart())) {
            param.setCreatedTimeStart(DateUtil.beginOfDay(toady).getTime());
        }
        // 大于当前时间
        if (ObjectUtils.isEmpty(param.getCreatedTimeEnd()) || param.getCreatedTimeEnd().compareTo(System.currentTimeMillis()) > 0) {
            param.setCreatedTimeEnd(System.currentTimeMillis());
        }
        DateHistogramAggregationBuilder dateHistogramAggregationBuilder;

        switch (EsDateTypeEnum.fromCode(param.getType())) {
            case EVERY_HOUR: {
                dateHistogramAggregationBuilder = AggregationBuilders.dateHistogram("dateAgg").field("timestamp")
                        .dateHistogramInterval(DateHistogramInterval.hours(1)).minDocCount(0L);
            }
            break;
            case EVERY_FIVE_MINUTE: {
                dateHistogramAggregationBuilder = AggregationBuilders.dateHistogram("dateAgg").field("timestamp")
                        .dateHistogramInterval(DateHistogramInterval.minutes(5)).minDocCount(0L);
            }
            break;
            default:
                throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }

        dateHistogramAggregationBuilder
                .extendedBounds(new ExtendedBounds(param.getCreatedTimeStart(), param.getCreatedTimeEnd()));
        dateHistogramAggregationBuilder.timeZone(DateTimeZone.forTimeZone(TimeZone.getDefault()));
        SearchQuery build = new NativeSearchQueryBuilder().withQuery(queryBuilder)
                .withIndices(shipmentOrderLogIndexName + "*")
                .addAggregation(dateHistogramAggregationBuilder).build();
        // 聚合查询
        AggregatedPage<OrderStatisticsLogIndex> indices =
                elasticsearchRestTemplate.queryForPage(build, OrderStatisticsLogIndex.class);
        Aggregations aggregations = indices.getAggregations();
        Histogram terms = aggregations.get("dateAgg");
        List<String> xData = new ArrayList<>();
        List<Integer> yData = new ArrayList<>();
        for (Histogram.Bucket bucket : terms.getBuckets()) {
            DateTime key = (DateTime) bucket.getKey();
            long time = key.toDate().getTime();
            xData.add(ConverterUtil.convertVoTime(time));
            long value = bucket.getDocCount();
            yData.add((int) value);
        }
        return IndexVO.buildVO(xData, yData);
    }

    @Override
    public IndexVO getOrderData(ShipmentOrderEsParam param) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        // 时间段查询
        RangeQueryBuilder createdTimeRangeQuery = QueryBuilders.rangeQuery("createdTime");
        boolean flag = false;
        if (!ObjectUtils.isEmpty(param.getCreatedTimeStart())) {
            flag = true;
            createdTimeRangeQuery.from(param.getCreatedTimeStart());
        }
        if (!ObjectUtils.isEmpty(param.getCreatedTimeEnd())) {
            flag = true;
            createdTimeRangeQuery.to(param.getCreatedTimeEnd());
        }
        if (flag) {
            queryBuilder.must(createdTimeRangeQuery);
        }
        queryBuilder.must(QueryBuilders.termsQuery("warehouseCode.keyword", CurrentRouteHolder.getWarehouseCode()));
        // 不同状态计数
        TermsAggregationBuilder builder = AggregationBuilders.terms("statusGroup").field("status.keyword").subAggregation(AggregationBuilders.count("count").field("shipmentOrderCode.keyword"));
        // 这里使用配置获取索引名称 相当于 {indexName}*
        SearchQuery build = new NativeSearchQueryBuilder().withIndices(shipmentOrderIndexName + "*")
                .withQuery(queryBuilder)
                .addAggregation(builder)
                .build();
        AggregatedPage<ShipmentIndex> page = elasticsearchRestTemplate.queryForPage(build, ShipmentIndex.class);
        // 总数
        List<String> xData = new ArrayList<>();
        List<Integer> yData = new ArrayList<>();
        long total = page.getTotalElements();
        xData.add("all");
        yData.add((int) total);
        Aggregations aggregations = page.getAggregations();
        Terms terms = aggregations.get("statusGroup");

        for (Terms.Bucket bucket : terms.getBuckets()) {
            xData.add(bucket.getKeyAsString());
            yData.add((int) bucket.getDocCount());
        }
        return IndexVO.buildVO(xData, yData);
    }

    @Override
    public Result<PageVO<ShipmentIndexDTO>> getPage(ShipmentEsParam param) {
        QueryBuilder query = getQuery(param);

        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(query)
                .withIndices(shipmentOrderIndexName + "*")
                .withTypes("_doc")
                .withSort(SortBuilders.fieldSort("createdTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(param.getCurrent() == 0 ? 0 : param.getCurrent() - 1, param.getSize())).build();
        AggregatedPage<ShipmentIndex> result = elasticsearchRestTemplate.queryForPage(searchQuery, ShipmentIndex.class);
        List<ShipmentIndex> content = result.getContent();
        List<ShipmentIndexDTO> dataList = content.stream().map(it -> ConverterUtil.convert(it, ShipmentIndexDTO.class)).collect(Collectors.toList());
        PageVO<ShipmentIndexDTO> pageVO = new PageVO<>();
        PageVO.Page page = new PageVO.Page();
        page.setCurrentPage(Long.valueOf(param.getCurrent()));
        page.setPageSize((long) result.getTotalPages());
        page.setTotalCount(result.getTotalElements());
        page.setPageSize(Long.valueOf(param.getSize()));
        page.setTotalPage((long) result.getTotalPages());
        pageVO.setPage(page);
        pageVO.setDataList(dataList);
        return Result.success(pageVO);
    }

    @Override
    public Result<ShipmentScrollResultDTO> getByScroll(ShipmentEsParam param) {
        ShipmentScrollResultDTO resultDTO = new ShipmentScrollResultDTO();
        QueryBuilder query = getQuery(param);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(query)
                .withIndices(shipmentOrderIndexName + "*")
                .withTypes("_doc")
                .withPageable(PageRequest.of(0, 5000))
                .build();
        long scrollTimeInMills = 5 * 60 * 1000;
        ScrolledPage<ShipmentIndex> indices;
        String scrollId = param.getScrollId();
        try {
            if (org.apache.commons.lang3.StringUtils.isEmpty(scrollId)) {
                indices = elasticsearchRestTemplate.startScroll(scrollTimeInMills, searchQuery, ShipmentIndex.class);
                scrollId = indices.getScrollId();
                resultDTO.setDataList(buildExportDTO(indices.getContent()));
                resultDTO.setScrollId(scrollId);
            } else {
                indices = elasticsearchRestTemplate.continueScroll(scrollId, scrollTimeInMills, ShipmentIndex.class);
                resultDTO.setDataList(buildExportDTO(indices.getContent()));
                resultDTO.setScrollId(indices.getScrollId());
            }
            if (!indices.hasContent()) {
                elasticsearchRestTemplate.clearScroll(scrollId);
            }
        } catch (Exception e) {
            if (!StringUtils.isEmpty(scrollId)) {
                elasticsearchRestTemplate.clearScroll(scrollId);
            }
        }
        return Result.success(resultDTO);
    }

//    @Override
//    public Result<List<ShipmentAnalysisDTO>> getShipmentAnalysis(ShipmentAnalysisParam param) {
//        log.info("开始执行出库单分析查询，参数: {}", param);
//
////        // 检查维度数量
////        if (param.getDimensions() != null && param.getDimensions().size() > 5) {
////            log.warn("查询维度过多，可能导致查询性能下降或超时。当前维度数: {}", param.getDimensions().size());
////            // 限制维度数量，只取前5个
////            param.setDimensions(param.getDimensions().subList(0, 5));
////            log.info("已限制维度数量为5个，实际使用的维度: {}", param.getDimensions());
////        }
//
//        try {
//            // 构建查询条件
//            BoolQueryBuilder queryBuilder = buildAnalysisQuery(param);
//            log.info("构建的查询条件: {}", queryBuilder);
//
//            // 构建聚合查询
//            NativeSearchQueryBuilder searchQueryBuilder = new NativeSearchQueryBuilder()
//                    .withQuery(queryBuilder)
//                    .withIndices(shipmentOrderIndexName + "*")
//                    .withTypes("_doc")
//                    .withPageable(PageRequest.of(0, 1)); // 设置最小页面大小，实际不需要返回文档
//
//            // 添加分组聚合
//            addGroupAggregations(searchQueryBuilder, param.getDimensions());
//
//            // 生成Kibana可执行的查询语句
//            NativeSearchQuery searchQuery = searchQueryBuilder.build();
////            String kibanaQuery = generateKibanaQuery(searchQuery, shipmentOrderIndexName + "*");
////            log.info("Kibana可执行的查询语句: \n{}", kibanaQuery);
//
//            // 执行查询
//            log.info("开始执行查询...");
//            AggregatedPage<ShipmentIndex> result = elasticsearchRestTemplate.queryForPage(searchQuery, ShipmentIndex.class);
//            log.info("查询执行完成，总文档数: {}", result.getTotalElements());
//
//            // 检查是否有聚合结果
//            if (result.getAggregations() == null) {
//                log.warn("查询没有返回聚合结果");
//                return Result.success(new ArrayList<>());
//            }
//
//            // 解析聚合结果
//            log.info("开始解析聚合结果...");
//            List<ShipmentAnalysisDTO> analysisList = parseAggregationResults(result.getAggregations(), param.getDimensions());
//            log.info("解析完成，结果数量: {}", analysisList.size());
//
//            // 检查结果是否为空
//            if (analysisList.isEmpty()) {
//                log.warn("查询结果为空，可能是因为没有符合条件的数据或者维度设置不正确");
//
//                // 尝试执行一个简单的查询来检查是否有数据
//                BoolQueryBuilder simpleQuery = QueryBuilders.boolQuery();
//                if (CollectionUtil.isNotEmpty(param.getCargoCodeList())) {
//                    simpleQuery.must(QueryBuilders.termsQuery("cargoCode.keyword", param.getCargoCodeList()));
//                }
//                simpleQuery.must(QueryBuilders.termsQuery("warehouseCode.keyword", CurrentRouteHolder.getWarehouseCode()));
//
//                NativeSearchQuery countQuery = new NativeSearchQueryBuilder()
//                        .withQuery(simpleQuery)
//                        .withIndices(shipmentOrderIndexName + "*")
//                        .withTypes("_doc")
//                        .withPageable(PageRequest.of(0, 1))
//                        .build();
//
//                long count = elasticsearchRestTemplate.count(countQuery, ShipmentIndex.class);
//                log.info("简单查询结果，符合条件的文档数: {}", count);
//
//                if (count == 0) {
//                    log.warn("没有找到符合条件的数据，请检查查询条件是否正确");
//                } else {
//                    log.warn("有符合条件的数据，但聚合结果为空，可能是维度设置不正确或聚合层级过深");
//                }
//            }
//
//            return Result.success(analysisList);
//        } catch (Exception e) {
//            log.error("执行出库单分析查询异常", e);
//            // 返回空结果并包含错误信息
//            return Result.failWithMessage("执行查询异常: " + e.getMessage());
//        }
//    }

//    @Override
//    public Result<ShipmentAnalysisDTO> getShipmentAnalysisSummary(ShipmentAnalysisParam param) {
//        log.info("开始执行出库单分析汇总查询，参数: {}", param);
//
//        try {
//            // 构建查询条件
//            BoolQueryBuilder queryBuilder = buildAnalysisQuery(param);
//            log.info("查询条件: {}", queryBuilder);
//
//            // 构建简单的查询，检查是否有数据
//            NativeSearchQuery countQuery = new NativeSearchQueryBuilder()
//                    .withQuery(queryBuilder)
//                    .withIndices(shipmentOrderIndexName + "*")
//                    .withTypes("_doc")
//                    .withPageable(PageRequest.of(0, 1))
//                    .build();
//
//            long count = elasticsearchRestTemplate.count(countQuery, ShipmentIndex.class);
//            log.info("符合条件的文档数: {}", count);
//
//            if (count == 0) {
//                log.warn("没有找到符合条件的数据");
//                // 返回空的汇总结果
//                ShipmentAnalysisDTO emptyResult = new ShipmentAnalysisDTO();
//                emptyResult.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//                emptyResult.setWarehouseName("汇总");
//                emptyResult.setOrderCount(0);
//                emptyResult.setSkuQtySum(BigDecimal.ZERO);
//                setDefaultStatusCounts(emptyResult);
//                return Result.success(emptyResult);
//            }
//
//            // 构建聚合查询，只使用计数和求和聚合
//            NativeSearchQueryBuilder searchQueryBuilder = new NativeSearchQueryBuilder()
//                    .withQuery(queryBuilder)
//                    .withIndices(shipmentOrderIndexName + "*")
//                    .withTypes("_doc")
//                    .withPageable(PageRequest.of(0, 1));
//
//            // 添加计数聚合
//            searchQueryBuilder.addAggregation(AggregationBuilders.count("order_count").field("shipmentOrderCode.keyword"));
//
//            // 添加求和聚合
//            searchQueryBuilder.addAggregation(AggregationBuilders.sum("sku_qty_sum").field("skuQty"));
//
//            // 添加各状态订单数的过滤聚合
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("created_order_count", QueryBuilders.termQuery("status.keyword", "10")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("pretreatment_fail_order_count", QueryBuilders.termQuery("status.keyword", "15")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("pretreatment_complete_order_count", QueryBuilders.termQuery("status.keyword", "20")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("collected_order_count", QueryBuilders.termQuery("status.keyword", "25")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("check_start_order_count", QueryBuilders.termQuery("status.keyword", "30")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("check_complete_order_count", QueryBuilders.termQuery("status.keyword", "35")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("partial_out_order_count", QueryBuilders.termQuery("status.keyword", "45")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("out_order_count", QueryBuilders.termQuery("status.keyword", "50")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("intercept_order_count", QueryBuilders.termQuery("status.keyword", "55")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("cancel_order_count", QueryBuilders.termQuery("status.keyword", "60")));
//            searchQueryBuilder.addAggregation(AggregationBuilders.filter("shortage_out_order_count", QueryBuilders.termQuery("status.keyword", "65")));
//
//            // 生成Kibana可执行的查询语句
//            NativeSearchQuery searchQuery = searchQueryBuilder.build();
//            String kibanaQuery = generateKibanaQuery(searchQuery, shipmentOrderIndexName + "*");
//            log.info("Kibana可执行的查询语句: \n{}", kibanaQuery);
//
//            // 执行查询
//            log.info("开始执行聚合查询...");
//            AggregatedPage<ShipmentIndex> result = elasticsearchRestTemplate.queryForPage(searchQuery, ShipmentIndex.class);
//            log.info("聚合查询执行完成");
//
//            // 检查是否有聚合结果
//            if (result.getAggregations() == null) {
//                log.warn("查询没有返回聚合结果");
//                // 返回空的汇总结果
//                ShipmentAnalysisDTO emptyResult = new ShipmentAnalysisDTO();
//                emptyResult.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//                emptyResult.setWarehouseName("汇总");
//                emptyResult.setOrderCount(0);
//                emptyResult.setSkuQtySum(BigDecimal.ZERO);
//                setDefaultStatusCounts(emptyResult);
//                return Result.success(emptyResult);
//            }
//
//            // 解析聚合结果
//            ShipmentAnalysisDTO summaryDTO = new ShipmentAnalysisDTO();
//            summaryDTO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//            summaryDTO.setWarehouseName("汇总");
//
//            // 设置维度值
//            Map<String, String> dimensionValues = new HashMap<>();
//            dimensionValues.put("summary", "汇总");
//            summaryDTO.setDimensionValues(dimensionValues);
//
//            // 获取订单总数
//            org.elasticsearch.search.aggregations.metrics.valuecount.ValueCount orderCount = result.getAggregations().get("order_count");
//            if (orderCount != null) {
//                summaryDTO.setOrderCount((int) orderCount.getValue());
//            } else {
//                summaryDTO.setOrderCount(0);
//            }
//
//            // 获取商品数量总和
//            Sum skuQtySum = result.getAggregations().get("sku_qty_sum");
//            if (skuQtySum != null) {
//                summaryDTO.setSkuQtySum(BigDecimal.valueOf(skuQtySum.getValue()));
//            } else {
//                summaryDTO.setSkuQtySum(BigDecimal.ZERO);
//            }
//
//            // 获取各状态订单数
//            org.elasticsearch.search.aggregations.bucket.filter.Filter createdOrderCount = result.getAggregations().get("created_order_count");
//            summaryDTO.setCreatedOrderCount(createdOrderCount != null ? (int) createdOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter pretreatmentFailOrderCount = result.getAggregations().get("pretreatment_fail_order_count");
//            summaryDTO.setPretreatmentFailOrderCount(pretreatmentFailOrderCount != null ? (int) pretreatmentFailOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter pretreatmentCompleteOrderCount = result.getAggregations().get("pretreatment_complete_order_count");
//            summaryDTO.setPretreatmentCompleteOrderCount(pretreatmentCompleteOrderCount != null ? (int) pretreatmentCompleteOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter collectedOrderCount = result.getAggregations().get("collected_order_count");
//            summaryDTO.setCollectedOrderCount(collectedOrderCount != null ? (int) collectedOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter checkStartOrderCount = result.getAggregations().get("check_start_order_count");
//            summaryDTO.setCheckStartOrderCount(checkStartOrderCount != null ? (int) checkStartOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter checkCompleteOrderCount = result.getAggregations().get("check_complete_order_count");
//            summaryDTO.setCheckCompleteOrderCount(checkCompleteOrderCount != null ? (int) checkCompleteOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter partialOutOrderCount = result.getAggregations().get("partial_out_order_count");
//            summaryDTO.setPartialOutOrderCount(partialOutOrderCount != null ? (int) partialOutOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter outOrderCount = result.getAggregations().get("out_order_count");
//            summaryDTO.setOutOrderCount(outOrderCount != null ? (int) outOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter interceptOrderCount = result.getAggregations().get("intercept_order_count");
//            summaryDTO.setInterceptOrderCount(interceptOrderCount != null ? (int) interceptOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter cancelOrderCount = result.getAggregations().get("cancel_order_count");
//            summaryDTO.setCancelOrderCount(cancelOrderCount != null ? (int) cancelOrderCount.getDocCount() : 0);
//
//            org.elasticsearch.search.aggregations.bucket.filter.Filter shortageOutOrderCount = result.getAggregations().get("shortage_out_order_count");
//            summaryDTO.setShortageOutOrderCount(shortageOutOrderCount != null ? (int) shortageOutOrderCount.getDocCount() : 0);
//
//            log.info("汇总结果: 订单总数={}, 商品数量={}", summaryDTO.getOrderCount(), summaryDTO.getSkuQtySum());
//
//            return Result.success(summaryDTO);
//        } catch (Exception e) {
//            log.error("执行出库单分析汇总查询异常", e);
//            return Result.failWithMessage("执行查询异常: " + e.getMessage());
//        }
//    }
//
//    @Override
//    public Result<PageVO<ShipmentAnalysisDTO>> getShipmentAnalysisPage(ShipmentAnalysisParam param) {
//        log.info("开始执行出库单分析查询（分页版），参数: {}", param);
//
//        try {
//
//            // 构建查询条件
//            BoolQueryBuilder queryBuilder = buildAnalysisQuery(param);
//
//            // 构建聚合查询
//            NativeSearchQueryBuilder searchQueryBuilder = new NativeSearchQueryBuilder()
//                    .withQuery(queryBuilder)
//                    .withIndices(shipmentOrderIndexName + "*")
//                    .withTypes("_doc")
//                    .withPageable(PageRequest.of(0, 1)); // 设置最小页面大小，实际不需要返回文档
//
//            // 添加分组聚合
//            addGroupAggregations(searchQueryBuilder, param.getAnalysisDimensions());
//
//            // 生成Kibana可执行的查询语句
//            NativeSearchQuery searchQuery = searchQueryBuilder.build();
////            String kibanaQuery = generateKibanaQuery(searchQuery, shipmentOrderIndexName + "*");
////            log.info("Kibana可执行的查询语句: \n{}", kibanaQuery);
//
//            // 执行查询
//            AggregatedPage<ShipmentIndex> result = elasticsearchRestTemplate.queryForPage(searchQuery, ShipmentIndex.class);
//
//            // 检查是否有聚合结果
//            if (result.getAggregations() == null) {
//                log.warn("查询没有返回聚合结果");
//                return Result.success(new PageVO<>());
//            }
//
//            // 解析聚合结果
//            List<ShipmentAnalysisDTO> allAnalysisList = parseAggregationResults(result.getAggregations(), param.getAnalysisDimensions());
//            log.info("解析完成，总结果数量: {}", allAnalysisList.size());
//
//
////            // 生成汇总行
////            ShipmentAnalysisDTO summaryDTO = createSummaryRow(allAnalysisList);
////            log.info("生成汇总行，订单总数: {}, 商品件数: {}",
////                    summaryDTO.getOrderCount(), summaryDTO.getSkuQtySum());
//
//            // 对结果进行排序（可以根据需求自定义排序规则）
//            sortAnalysisResults(allAnalysisList, param);
//
//            // 分页处理
//            int current = param.getCurrent() == 0 ? 1 : param.getCurrent();
//            int size = param.getSize() == 0 ? 10 : param.getSize();
//            int fromIndex = (current - 1) * size;
//            int toIndex = Math.min(fromIndex + size, allAnalysisList.size());
//
//            // 如果起始索引超出范围，返回空列表
//            List<ShipmentAnalysisDTO> pagedList;
//            if (fromIndex >= allAnalysisList.size()) {
//                pagedList = new ArrayList<>();
//            } else {
//                pagedList = allAnalysisList.subList(fromIndex, toIndex);
//            }
//
//            // 在第一页的开头添加汇总行
//            List<ShipmentAnalysisDTO> resultList = new ArrayList<>();
////            if (current == 1) {
////                resultList.add(summaryDTO);
////            }
//            resultList.addAll(pagedList);
//            if (!CollectionUtils.isEmpty(resultList)) {
//                for (ShipmentAnalysisDTO analysisDTO : resultList) {
//                    // 将维度值平铺到DTO对象中
//                    setDimensionValuesToDTO(analysisDTO, analysisDTO.getDimensionValues());
//                }
//            }
//
//            // 构建分页结果
//            PageVO<ShipmentAnalysisDTO> pageVO = new PageVO<>();
//            PageVO.Page page = new PageVO.Page();
//            page.setCurrentPage((long) current);
//            page.setPageSize((long) size);
//            page.setTotalCount((long) allAnalysisList.size()); // +1 表示汇总行
//            page.setTotalPage((long) Math.ceil((double) (allAnalysisList.size()) / size));
//            pageVO.setPage(page);
//            pageVO.setDataList(resultList);
//
//            return Result.success(pageVO);
//        } catch (Exception e) {
//            log.error("执行出库单分析查询（分页版）异常", e);
//            return Result.failWithMessage("执行查询异常: " + e.getMessage());
//        }
//    }

//    /**
//     * 对分析结果进行排序
//     *
//     * @param analysisList 分析结果列表
//     * @param param        查询参数
//     */
//    private void sortAnalysisResults(List<ShipmentAnalysisDTO> analysisList, ShipmentAnalysisParam param) {
//        if (CollectionUtil.isEmpty(analysisList)) {
//            return;
//        }
//
//        // 获取排序字段和排序方式
//        String sortField = param.getSortField();
//        String sortOrder = param.getSortOrder();
//
//        // 默认排序字段为订单总数
//        if (StrUtil.isEmpty(sortField)) {
//            sortField = "orderCount";
//        }
//
//        // 默认排序方式为降序
//        boolean isAsc = "ASC".equalsIgnoreCase(sortOrder);
//
//        log.info("对结果进行排序，排序字段: {}, 排序方式: {}", sortField, isAsc ? "升序" : "降序");
//
//        // 根据指定字段和排序方式进行排序
//        final String finalSortField = sortField;
//        final boolean finalIsAsc = isAsc;
//
//        analysisList.sort((o1, o2) -> {
//            int compare = 0;
//
//            // 根据指定字段进行排序
//            switch (finalSortField) {
//                case "orderCount":
//                    // 按订单总数排序
//                    compare = Integer.compare(
//                            o1.getOrderCount() != null ? o1.getOrderCount() : 0,
//                            o2.getOrderCount() != null ? o2.getOrderCount() : 0);
//                    break;
//                case "skuQtySum":
//                    // 按商品件数排序
//                    if (o1.getSkuQtySum() != null && o2.getSkuQtySum() != null) {
//                        compare = o1.getSkuQtySum().compareTo(o2.getSkuQtySum());
//                    }
//                    break;
//                case "createdOrderCount":
//                    // 按创建状态订单数排序
//                    compare = Integer.compare(
//                            o1.getCreatedOrderCount() != null ? o1.getCreatedOrderCount() : 0,
//                            o2.getCreatedOrderCount() != null ? o2.getCreatedOrderCount() : 0);
//                    break;
//                case "outOrderCount":
//                    // 按已出库订单数排序
//                    compare = Integer.compare(
//                            o1.getOutOrderCount() != null ? o1.getOutOrderCount() : 0,
//                            o2.getOutOrderCount() != null ? o2.getOutOrderCount() : 0);
//                    break;
//                default:
//                    // 如果是维度字段，按维度值排序
//                    if (param.getAnalysisDimensions() != null && param.getAnalysisDimensions().contains(finalSortField)) {
//                        String value1 = o1.getDimensionValues().get(finalSortField);
//                        String value2 = o2.getDimensionValues().get(finalSortField);
//                        if (value1 != null && value2 != null) {
//                            compare = value1.compareTo(value2);
//                        }
//                    } else {
//                        // 默认按订单总数排序
//                        compare = Integer.compare(
//                                o1.getOrderCount() != null ? o1.getOrderCount() : 0,
//                                o2.getOrderCount() != null ? o2.getOrderCount() : 0);
//                    }
//                    break;
//            }
//
//            // 如果是降序，则反转比较结果
//            return finalIsAsc ? compare : -compare;
//        });
//    }
//
//    /**
//     * 生成Kibana可执行的查询语句
//     *
//     * @param searchQuery 查询对象
//     * @param indexName   索引名称
//     * @return Kibana可执行的查询语句
//     */
//    private String generateKibanaQuery(NativeSearchQuery searchQuery, String indexName) {
//        StringBuilder kibanaQuery = new StringBuilder();
//        kibanaQuery.append("GET ").append(indexName).append("/_search\n");
//        kibanaQuery.append("{\n");
//
//        // 添加查询条件
//        kibanaQuery.append("  \"query\": ");
//        kibanaQuery.append(searchQuery.getQuery().toString().replace("\n", "\n  "));
//        kibanaQuery.append(",\n");
//
//        // 添加聚合
//        if (searchQuery.getAggregations() != null && !searchQuery.getAggregations().isEmpty()) {
//            kibanaQuery.append("  \"aggs\": {\n");
//            int i = 0;
//            for (AbstractAggregationBuilder<?> agg : searchQuery.getAggregations()) {
//                if (i > 0) {
//                    kibanaQuery.append(",\n");
//                }
//                kibanaQuery.append("    \"").append(agg.getName()).append("\": ");
//                kibanaQuery.append(agg.toString().replace("\n", "\n    "));
//                i++;
//            }
//            kibanaQuery.append("\n  }\n");
//        }
//
//        // 添加大小设置
//        kibanaQuery.append("  \"size\": 0\n");  // Kibana查询中可以设置为0，表示不返回文档
//
//        kibanaQuery.append("}");
//        return kibanaQuery.toString();
//    }
//
//    /**
//     * 构建分析查询条件
//     *
//     * @param param 查询参数
//     * @return 查询条件
//     */
//    private BoolQueryBuilder buildAnalysisQuery(ShipmentAnalysisParam param) {
//        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//
//        // 仓库编码
//        TermsQueryBuilder warehouseCodeQuery = QueryBuilders.termsQuery("warehouseCode.keyword", CurrentRouteHolder.getWarehouseCode());
//        boolQuery.must(warehouseCodeQuery);
//
//        // 货主编码
//        if (CollectionUtil.isNotEmpty(param.getCargoCodeList())) {
//            TermsQueryBuilder cargoCodeQuery = QueryBuilders.termsQuery("cargoCode.keyword", param.getCargoCodeList());
//            boolQuery.must(cargoCodeQuery);
//        }
//
//        // 业务类型
//        if (StrUtil.isNotEmpty(param.getBusinessType())) {
//            TermQueryBuilder businessTypeQuery = QueryBuilders.termQuery("businessType.keyword", param.getBusinessType());
//            boolQuery.must(businessTypeQuery);
//        }
//
//        // 销售平台
//        if (StrUtil.isNotEmpty(param.getSalePlatform())) {
//            TermQueryBuilder salePlatformQuery = QueryBuilders.termQuery("salePlatform.keyword", param.getSalePlatform());
//            boolQuery.must(salePlatformQuery);
//        }
//
//        // 创建时间
//        if (param.getCreateTimeStart() != null || param.getCreateTimeEnd() != null) {
//            RangeQueryBuilder createTimeQuery = QueryBuilders.rangeQuery("createdTime");
//            if (param.getCreateTimeStart() != null) {
//                createTimeQuery.from(param.getCreateTimeStart());
//            }
//            if (param.getCreateTimeEnd() != null) {
//                createTimeQuery.to(param.getCreateTimeEnd());
//            }
//            boolQuery.must(createTimeQuery);
//        }
//
//        // 付款时间
//        if (param.getPayDateStart() != null || param.getPayDateEnd() != null) {
//            RangeQueryBuilder payDateQuery = QueryBuilders.rangeQuery("payDate");
//            if (param.getPayDateStart() != null) {
//                payDateQuery.from(param.getPayDateStart());
//            }
//            if (param.getPayDateEnd() != null) {
//                payDateQuery.to(param.getPayDateEnd());
//            }
//            boolQuery.must(payDateQuery);
//        }
//
//        // 预计出库时间
//        if (param.getExpOutStockDateStart() != null || param.getExpOutStockDateEnd() != null) {
//            RangeQueryBuilder expOutStockDateQuery = QueryBuilders.rangeQuery("expOutStockDate");
//            if (param.getExpOutStockDateStart() != null) {
//                expOutStockDateQuery.from(param.getExpOutStockDateStart());
//            }
//            if (param.getExpOutStockDateEnd() != null) {
//                expOutStockDateQuery.to(param.getExpOutStockDateEnd());
//            }
//            boolQuery.must(expOutStockDateQuery);
//        }
//
//        // 出库时间
//        if (param.getOutStockDateStart() != null || param.getOutStockDateEnd() != null) {
//            RangeQueryBuilder outStockDateQuery = QueryBuilders.rangeQuery("outStockDate");
//            if (param.getOutStockDateStart() != null) {
//                outStockDateQuery.from(param.getOutStockDateStart());
//            }
//            if (param.getOutStockDateEnd() != null) {
//                outStockDateQuery.to(param.getOutStockDateEnd());
//            }
//            boolQuery.must(outStockDateQuery);
//        }
//
//        // 订单品种类型
//        if (CollectionUtil.isNotEmpty(param.getPackageStructList())) {
//            TermsQueryBuilder packageStructQuery = QueryBuilders.termsQuery("packageStruct.keyword", param.getPackageStructList());
//            boolQuery.must(packageStructQuery);
//        }
//
//        // 品种数
//        if (param.getSkuTypeQtyMin() != null || param.getSkuTypeQtyMax() != null) {
//            RangeQueryBuilder skuTypeQtyQuery = QueryBuilders.rangeQuery("skuTypeQty");
//            if (param.getSkuTypeQtyMin() != null) {
//                skuTypeQtyQuery.from(param.getSkuTypeQtyMin());
//            }
//            if (param.getSkuTypeQtyMax() != null) {
//                skuTypeQtyQuery.to(param.getSkuTypeQtyMax());
//            }
//            boolQuery.must(skuTypeQtyQuery);
//        }
//
//        // 商品数
//        if (param.getSkuQtyMin() != null || param.getSkuQtyMax() != null) {
//            RangeQueryBuilder skuQtyQuery = QueryBuilders.rangeQuery("skuQty");
//            if (param.getSkuQtyMin() != null) {
//                skuQtyQuery.from(param.getSkuQtyMin());
//            }
//            if (param.getSkuQtyMax() != null) {
//                skuQtyQuery.to(param.getSkuQtyMax());
//            }
//            boolQuery.must(skuQtyQuery);
//        }
//
//        // 理论重量
//        if (param.getWeightMin() != null || param.getWeightMax() != null) {
//            RangeQueryBuilder weightQuery = QueryBuilders.rangeQuery("weight");
//            if (param.getWeightMin() != null) {
//                weightQuery.from(param.getWeightMin());
//            }
//            if (param.getWeightMax() != null) {
//                weightQuery.to(param.getWeightMax());
//            }
//            boolQuery.must(weightQuery);
//        }
//
//        // 快递公司编码
//        if (StrUtil.isNotEmpty(param.getCarrierCode())) {
//            TermQueryBuilder carrierCodeQuery = QueryBuilders.termQuery("carrierCode.keyword", param.getCarrierCode());
//            boolQuery.must(carrierCodeQuery);
//        }
//
//        // 收货省份
//        if (CollectionUtil.isNotEmpty(param.getReceiverProvNameList())) {
//            TermsQueryBuilder receiverProvNameQuery = QueryBuilders.termsQuery("receiverProvName.keyword", param.getReceiverProvNameList());
//            boolQuery.must(receiverProvNameQuery);
//        }
//
//        // 收货城市
//        if (CollectionUtil.isNotEmpty(param.getReceiverCityNameList())) {
//            TermsQueryBuilder receiverCityNameQuery = QueryBuilders.termsQuery("receiverCityName.keyword", param.getReceiverCityNameList());
//            boolQuery.must(receiverCityNameQuery);
//        }
//
//        // 收货区县
//        if (CollectionUtil.isNotEmpty(param.getReceiverAreaNameList())) {
//            TermsQueryBuilder receiverAreaNameQuery = QueryBuilders.termsQuery("receiverAreaName.keyword", param.getReceiverAreaNameList());
//            boolQuery.must(receiverAreaNameQuery);
//        }
//
//        return boolQuery;
//    }
//
//    /**
//     * 添加分组聚合
//     *
//     * @param searchQueryBuilder 查询构建器
//     * @param dimensions         分析维度列表
//     */
//    private void addGroupAggregations(NativeSearchQueryBuilder searchQueryBuilder, List<String> dimensions) {
//        if (CollectionUtil.isEmpty(dimensions)) {
//            return;
//        }
//
//        // 构建嵌套聚合
//        TermsAggregationBuilder rootAgg = null;
//        TermsAggregationBuilder currentAgg = null;
//
//        for (int i = 0; i < dimensions.size(); i++) {
//            String dimension = dimensions.get(i);
//            String aggName = "group_by_" + dimension;
//
//            // 根据维度类型确定字段名称
//            String fieldName = getFieldNameByDimension(dimension);
//
//            TermsAggregationBuilder termsAgg = AggregationBuilders.terms(aggName).field(fieldName);
//
//            // 添加子聚合
//            if (i == 0) {
//                rootAgg = termsAgg;
//                currentAgg = rootAgg;
//            } else {
//                currentAgg.subAggregation(termsAgg);
//                currentAgg = termsAgg;
//            }
//        }
//
//        // 添加度量聚合（计数和求和）
//        if (currentAgg != null) {
//            // 订单总数计数
//            currentAgg.subAggregation(AggregationBuilders.count("order_count").field("shipmentOrderCode.keyword"));
//
//            // 商品数量求和
//            currentAgg.subAggregation(AggregationBuilders.sum("sku_qty_sum").field("skuQty"));
//
//            // 各状态订单数计数
//            //创建
//            currentAgg.subAggregation(AggregationBuilders.filter("created_order_count", QueryBuilders.termQuery("status.keyword", "10")));
//            //预处理失败
//            currentAgg.subAggregation(AggregationBuilders.filter("pretreatment_fail_order_count", QueryBuilders.termQuery("status.keyword", "15")));
//            //预处理完成
//            currentAgg.subAggregation(AggregationBuilders.filter("pretreatment_complete_order_count", QueryBuilders.termQuery("status.keyword", "20")));
//            //已汇总
//            currentAgg.subAggregation(AggregationBuilders.filter("collected_order_count", QueryBuilders.termQuery("status.keyword", "25")));
//            //复核开始
//            currentAgg.subAggregation(AggregationBuilders.filter("check_start_order_count", QueryBuilders.termQuery("status.keyword", "30")));
//            //复核完成
//            currentAgg.subAggregation(AggregationBuilders.filter("check_complete_order_count", QueryBuilders.termQuery("status.keyword", "35")));
//            //部分出库
//            currentAgg.subAggregation(AggregationBuilders.filter("partial_out_order_count", QueryBuilders.termQuery("status.keyword", "45")));
//            //出库
//            currentAgg.subAggregation(AggregationBuilders.filter("out_order_count", QueryBuilders.termQuery("status.keyword", "50")));
//            //拦截
//            currentAgg.subAggregation(AggregationBuilders.filter("intercept_order_count", QueryBuilders.termQuery("status.keyword", "55")));
//            //取消
//            currentAgg.subAggregation(AggregationBuilders.filter("cancel_order_count", QueryBuilders.termQuery("status.keyword", "60")));
//            //缺货
//            currentAgg.subAggregation(AggregationBuilders.filter("shortage_out_order_count", QueryBuilders.termQuery("status.keyword", "40")));
//        }
//
//        if (rootAgg != null) {
//            searchQueryBuilder.addAggregation(rootAgg);
//        }
//    }

//    /**
//     * 根据维度获取字段名称
//     *
//     * @param dimension 维度
//     * @return 字段名称
//     */
//    private String getFieldNameByDimension(String dimension) {
//        // 处理时间类型的维度
//        if (dimension.endsWith("_day") || dimension.endsWith("_hour")) {
//            // 时间类型的维度直接使用原始字段，在聚合结果解析时进行格式化
//            return dimension.substring(0, dimension.lastIndexOf("_"));
//        }
//
//        // 其他维度使用keyword类型进行精确匹配
//        return dimension + ".keyword";
//    }
//
//    /**
//     * 解析聚合结果
//     *
//     * @param aggregations 聚合结果
//     * @param dimensions   分析维度列表
//     * @return 分析结果列表
//     */
//    private List<ShipmentAnalysisDTO> parseAggregationResults(Aggregations aggregations, List<String> dimensions) {
//        if (CollectionUtil.isEmpty(dimensions)) {
//            return new ArrayList<>();
//        }
//
//        List<ShipmentAnalysisDTO> resultList = new ArrayList<>();
//        String rootAggName = "group_by_" + dimensions.get(0);
//        Terms rootTerms = aggregations.get(rootAggName);
//
//        // 递归解析聚合结果
//        parseNestedAggregations(rootTerms, dimensions, 0, new HashMap<>(), resultList);
//
//        return resultList;
//    }
//
//    /**
//     * 递归解析嵌套聚合结果
//     *
//     * @param terms           当前层级的terms聚合
//     * @param dimensions      分析维度列表
//     * @param level           当前层级
//     * @param dimensionValues 当前维度值映射
//     * @param resultList      结果列表
//     */
//    private void parseNestedAggregations(Terms terms, List<String> dimensions, int level, Map<String, String> dimensionValues,
//                                         List<ShipmentAnalysisDTO> resultList) {
//        String currentDimension = dimensions.get(level);
//
//        for (Terms.Bucket bucket : terms.getBuckets()) {
//            String bucketKey = bucket.getKeyAsString();
//
//            // 处理时间类型的维度值
//            String formattedValue = formatDimensionValue(currentDimension, bucketKey);
//
//            // 复制当前维度值映射，防止影响上层调用
//            Map<String, String> currentDimensionValues = new HashMap<>(dimensionValues);
//            currentDimensionValues.put(currentDimension, formattedValue);
//
//            if (level < dimensions.size() - 1) {
//                // 如果还有下一层维度，继续递归解析
//                String nextAggName = "group_by_" + dimensions.get(level + 1);
//                Terms nextTerms = bucket.getAggregations().get(nextAggName);
//                parseNestedAggregations(nextTerms, dimensions, level + 1, currentDimensionValues, resultList);
//            } else {
//                // 到达最后一层，构建结果对象
//                ShipmentAnalysisDTO analysisDTO = new ShipmentAnalysisDTO();
//                analysisDTO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//                analysisDTO.setDimensionValues(currentDimensionValues);
//
//
//                // 订单总数
//                org.elasticsearch.search.aggregations.metrics.valuecount.ValueCount orderCount = bucket.getAggregations().get("order_count");
//                analysisDTO.setOrderCount((int) orderCount.getValue());
//
//                // 商品数量总和
//                Sum skuQtySum = bucket.getAggregations().get("sku_qty_sum");
//                analysisDTO.setSkuQtySum(BigDecimal.valueOf(skuQtySum.getValue()));
//
//                // 各状态订单数
//                //创建
//                org.elasticsearch.search.aggregations.bucket.filter.Filter createdOrderCount = bucket.getAggregations().get("created_order_count");
//                analysisDTO.setCreatedOrderCount((int) createdOrderCount.getDocCount());
//                //预处理失败
//                org.elasticsearch.search.aggregations.bucket.filter.Filter pretreatmentFailOrderCount = bucket.getAggregations().get("pretreatment_fail_order_count");
//                analysisDTO.setPretreatmentFailOrderCount((int) pretreatmentFailOrderCount.getDocCount());
//                //预处理完成
//                org.elasticsearch.search.aggregations.bucket.filter.Filter pretreatmentCompleteOrderCount = bucket.getAggregations().get("pretreatment_complete_order_count");
//                analysisDTO.setPretreatmentCompleteOrderCount((int) pretreatmentCompleteOrderCount.getDocCount());
//                //已汇总
//                org.elasticsearch.search.aggregations.bucket.filter.Filter collectedOrderCount = bucket.getAggregations().get("collected_order_count");
//                analysisDTO.setCollectedOrderCount((int) collectedOrderCount.getDocCount());
//                //复核开始
//                org.elasticsearch.search.aggregations.bucket.filter.Filter checkStartOrderCount = bucket.getAggregations().get("check_start_order_count");
//                analysisDTO.setCheckStartOrderCount((int) checkStartOrderCount.getDocCount());
//                //复核完成
//                org.elasticsearch.search.aggregations.bucket.filter.Filter checkCompleteOrderCount = bucket.getAggregations().get("check_complete_order_count");
//                analysisDTO.setCheckCompleteOrderCount((int) checkCompleteOrderCount.getDocCount());
//                //部分出库
//                org.elasticsearch.search.aggregations.bucket.filter.Filter partialOutOrderCount = bucket.getAggregations().get("partial_out_order_count");
//                analysisDTO.setPartialOutOrderCount((int) partialOutOrderCount.getDocCount());
//                //已出库
//                org.elasticsearch.search.aggregations.bucket.filter.Filter outOrderCount = bucket.getAggregations().get("out_order_count");
//                analysisDTO.setOutOrderCount((int) outOrderCount.getDocCount());
//                //拦截
//                org.elasticsearch.search.aggregations.bucket.filter.Filter interceptOrderCount = bucket.getAggregations().get("intercept_order_count");
//                analysisDTO.setInterceptOrderCount((int) interceptOrderCount.getDocCount());
//                //取消
//                org.elasticsearch.search.aggregations.bucket.filter.Filter cancelOrderCount = bucket.getAggregations().get("cancel_order_count");
//                analysisDTO.setCancelOrderCount((int) cancelOrderCount.getDocCount());
//                //缺货出库
//                org.elasticsearch.search.aggregations.bucket.filter.Filter shortageOutOrderCount = bucket.getAggregations().get("shortage_out_order_count");
//                analysisDTO.setShortageOutOrderCount((int) shortageOutOrderCount.getDocCount());
//                analysisDTO.setWarehouseName("");
//                resultList.add(analysisDTO);
//            }
//        }
//    }
//
//    /**
//     * 创建汇总行
//     *
//     * @param analysisList 分析结果列表
//     * @return 汇总行对象
//     */
//    private ShipmentAnalysisDTO createSummaryRow(List<ShipmentAnalysisDTO> analysisList) {
//        ShipmentAnalysisDTO summaryDTO = new ShipmentAnalysisDTO();
//        summaryDTO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//        summaryDTO.setWarehouseName("汇总");
//
//        // 初始化所有维度字段为空字符串
//        summaryDTO.setCargoCode("汇总");
//        summaryDTO.setBusinessType("");
//        summaryDTO.setSalePlatform("");
//        summaryDTO.setPackageStruct("");
//        summaryDTO.setExpOutStockDate_day("");
//        summaryDTO.setExpOutStockDate_hour("");
//        summaryDTO.setCreatedTime_day("");
//        summaryDTO.setCreatedTime_hour("");
//        summaryDTO.setPayDate_day("");
//        summaryDTO.setPayDate_hour("");
//        summaryDTO.setOutStockDate_day("");
//        summaryDTO.setOutStockDate_hour("");
//        summaryDTO.setCarrierCode("");
//        summaryDTO.setReceiverProvName("");
//        summaryDTO.setReceiverCityName("");
//        summaryDTO.setReceiverAreaName("");
//
//        // 初始化维度值映射
//        Map<String, String> dimensionValues = new HashMap<>();
//        dimensionValues.put("cargoCode", "汇总");
//        summaryDTO.setDimensionValues(dimensionValues);
//
//        // 如果列表为空，返回初始化的汇总对象
//        if (CollectionUtil.isEmpty(analysisList)) {
//            summaryDTO.setOrderCount(0);
//            summaryDTO.setSkuQtySum(BigDecimal.ZERO);
//            summaryDTO.setCreatedOrderCount(0);
//            summaryDTO.setPretreatmentFailOrderCount(0);
//            summaryDTO.setPretreatmentCompleteOrderCount(0);
//            summaryDTO.setCollectedOrderCount(0);
//            summaryDTO.setCheckStartOrderCount(0);
//            summaryDTO.setCheckCompleteOrderCount(0);
//            summaryDTO.setPartialOutOrderCount(0);
//            summaryDTO.setOutOrderCount(0);
//            summaryDTO.setInterceptOrderCount(0);
//            summaryDTO.setCancelOrderCount(0);
//            summaryDTO.setShortageOutOrderCount(0);
//            return summaryDTO;
//        }
//
//        // 计算汇总数据
//        int orderCount = 0;
//        BigDecimal skuQtySum = BigDecimal.ZERO;
//        int createdOrderCount = 0;
//        int pretreatmentFailOrderCount = 0;
//        int pretreatmentCompleteOrderCount = 0;
//        int collectedOrderCount = 0;
//        int checkStartOrderCount = 0;
//        int checkCompleteOrderCount = 0;
//        int partialOutOrderCount = 0;
//        int outOrderCount = 0;
//        int interceptOrderCount = 0;
//        int cancelOrderCount = 0;
//        int shortageOutOrderCount = 0;
//
//        for (ShipmentAnalysisDTO dto : analysisList) {
//            orderCount += dto.getOrderCount() != null ? dto.getOrderCount() : 0;
//            skuQtySum = skuQtySum.add(dto.getSkuQtySum() != null ? dto.getSkuQtySum() : BigDecimal.ZERO);
//            createdOrderCount += dto.getCreatedOrderCount() != null ? dto.getCreatedOrderCount() : 0;
//            pretreatmentFailOrderCount += dto.getPretreatmentFailOrderCount() != null ? dto.getPretreatmentFailOrderCount() : 0;
//            pretreatmentCompleteOrderCount += dto.getPretreatmentCompleteOrderCount() != null ? dto.getPretreatmentCompleteOrderCount() : 0;
//            collectedOrderCount += dto.getCollectedOrderCount() != null ? dto.getCollectedOrderCount() : 0;
//            checkStartOrderCount += dto.getCheckStartOrderCount() != null ? dto.getCheckStartOrderCount() : 0;
//            checkCompleteOrderCount += dto.getCheckCompleteOrderCount() != null ? dto.getCheckCompleteOrderCount() : 0;
//            partialOutOrderCount += dto.getPartialOutOrderCount() != null ? dto.getPartialOutOrderCount() : 0;
//            outOrderCount += dto.getOutOrderCount() != null ? dto.getOutOrderCount() : 0;
//            interceptOrderCount += dto.getInterceptOrderCount() != null ? dto.getInterceptOrderCount() : 0;
//            cancelOrderCount += dto.getCancelOrderCount() != null ? dto.getCancelOrderCount() : 0;
//            shortageOutOrderCount += dto.getShortageOutOrderCount() != null ? dto.getShortageOutOrderCount() : 0;
//        }
//
//        // 设置汇总数据
//        summaryDTO.setOrderCount(orderCount);
//        summaryDTO.setSkuQtySum(skuQtySum);
//        summaryDTO.setCreatedOrderCount(createdOrderCount);
//        summaryDTO.setPretreatmentFailOrderCount(pretreatmentFailOrderCount);
//        summaryDTO.setPretreatmentCompleteOrderCount(pretreatmentCompleteOrderCount);
//        summaryDTO.setCollectedOrderCount(collectedOrderCount);
//        summaryDTO.setCheckStartOrderCount(checkStartOrderCount);
//        summaryDTO.setCheckCompleteOrderCount(checkCompleteOrderCount);
//        summaryDTO.setPartialOutOrderCount(partialOutOrderCount);
//        summaryDTO.setOutOrderCount(outOrderCount);
//        summaryDTO.setInterceptOrderCount(interceptOrderCount);
//        summaryDTO.setCancelOrderCount(cancelOrderCount);
//        summaryDTO.setShortageOutOrderCount(shortageOutOrderCount);
//
//        return summaryDTO;
//    }
//
//    /**
//     * 将维度值平铺到DTO对象中
//     *
//     * @param dto             分析结果DTO
//     * @param dimensionValues 维度值映射
//     */
//    private void setDimensionValuesToDTO(ShipmentAnalysisDTO dto, Map<String, String> dimensionValues) {
//        // 初始化所有维度字段为空字符串
//        dto.setCargoCode("");
//        dto.setBusinessType("");
//        dto.setSalePlatform("");
//        dto.setPackageStruct("");
//        dto.setExpOutStockDate_day("");
//        dto.setExpOutStockDate_hour("");
//        dto.setCreatedTime_day("");
//        dto.setCreatedTime_hour("");
//        dto.setPayDate_day("");
//        dto.setPayDate_hour("");
//        dto.setOutStockDate_day("");
//        dto.setOutStockDate_hour("");
//        dto.setCarrierCode("");
//        dto.setReceiverProvName("");
//        dto.setReceiverCityName("");
//        dto.setReceiverAreaName("");
//
//        // 将维度值设置到对应的字段
//        if (dimensionValues != null) {
//            for (Map.Entry<String, String> entry : dimensionValues.entrySet()) {
//                String dimension = entry.getKey();
//                String value = entry.getValue() != null ? entry.getValue() : "";
//
//                switch (dimension) {
//                    case "cargoCode":
//                        dto.setCargoCode(value);
//                        break;
//                    case "businessType":
//                        dto.setBusinessType(value);
//                        break;
//                    case "salePlatform":
//                        dto.setSalePlatform(value);
//                        break;
//                    case "packageStruct":
//                        dto.setPackageStruct(value);
//                        break;
//                    case "expOutStockDate_day":
//                        dto.setExpOutStockDate_day(value);
//                        break;
//                    case "expOutStockDate_hour":
//                        dto.setExpOutStockDate_hour(value);
//                        break;
//                    case "createdTime_day":
//                        dto.setCreatedTime_day(value);
//                        break;
//                    case "createdTime_hour":
//                        dto.setCreatedTime_hour(value);
//                        break;
//                    case "payDate_day":
//                        dto.setPayDate_day(value);
//                        break;
//                    case "payDate_hour":
//                        dto.setPayDate_hour(value);
//                        break;
//                    case "outStockDate_day":
//                        dto.setOutStockDate_day(value);
//                        break;
//                    case "outStockDate_hour":
//                        dto.setOutStockDate_hour(value);
//                        break;
//                    case "carrierCode":
//                        dto.setCarrierCode(value);
//                        break;
//                    case "receiverProvName":
//                        dto.setReceiverProvName(value);
//                        break;
//                    case "receiverCityName":
//                        dto.setReceiverCityName(value);
//                        break;
//                    case "receiverAreaName":
//                        dto.setReceiverAreaName(value);
//                        break;
//                    default:
//                        // 其他维度字段不处理
//                        break;
//                }
//            }
//        }
//    }

//    /**
//     * 格式化维度值，主要处理时间类型的维度
//     *
//     * @param dimension 维度
//     * @param value     原始值
//     * @return 格式化后的值
//     */
//    private String formatDimensionValue(String dimension, String value) {
//        if (dimension.endsWith("_day")) {
//            // 将时间戳格式化为年月日
//            try {
//                long timestamp = Long.parseLong(value);
//                return DateUtil.format(new Date(timestamp), "yyyy-MM-dd");
//            } catch (Exception e) {
//                return value;
//            }
//        } else if (dimension.endsWith("_hour")) {
//            // 将时间戳格式化为年月日时
//            try {
//                long timestamp = Long.parseLong(value);
//                return DateUtil.format(new Date(timestamp), "yyyy-MM-dd HH");
//            } catch (Exception e) {
//                return value;
//            }
//        }
//
//        return value;
//    }

    private List<ShipmentExportDTO> buildExportDTO(List<ShipmentIndex> indexList) {
        List<ShipmentExportDTO> dataList = new ArrayList<>();
        indexList.forEach(index -> {
            ShipmentExportDTO exportDTO = ConverterUtil.convert(index, ShipmentExportDTO.class);
            assert exportDTO != null;
            exportDTO.setOrderTag("");
            if (!StringUtils.isEmpty(index.getOrderTag())) {
                Set<OrderTagEnum> orderTagEnumList = OrderTagEnum.NumToEnum(index.getOrderTag());
                if (!CollectionUtils.isEmpty(orderTagEnumList)) {
                    String orderTagName = orderTagEnumList.stream().map(OrderTagEnum::getDesc).collect(Collectors.joining("|"));
                    exportDTO.setOrderTag(orderTagName);
                } else {
                    exportDTO.setOrderTag("");
                }
            }
            exportDTO.setPreSaleType(index.getPreSaleTypeDesc());
            exportDTO.setExpShipTime(ConverterUtil.convertVoTime(index.getExpShipTime()));
            exportDTO.setReceiverCity(index.getReceiverCityName());
            exportDTO.setReceiverArea(index.getReceiverAreaName());
            exportDTO.setReceiverProv(index.getReceiverProvName());
            exportDTO.setSalePlatform(index.getSalePlatformName());
            if (StrUtil.isNotBlank(index.getExtraJson())) {
                exportDTO.setCalculateMaterialCode(JSONUtil.parseObj(index.getExtraJson()).getStr(CALCULATE_MATERIAL_BAR_CODE));
            }
            exportDTO.setSkuQty(index.getSkuQty().toString());
            exportDTO.setOutSkuQty(index.getOutSkuQty().toString());
            exportDTO.setPayDate(ConverterUtil.convertVoTime(index.getPayDate()));
            exportDTO.setCreatedTime(ConverterUtil.convertVoTime(index.getCreatedTime()));
            exportDTO.setPickSkuDate(ConverterUtil.convertVoTime(index.getPickSkuDate()));
            exportDTO.setCheckStartDate(ConverterUtil.convertVoTime(index.getCheckStartDate()));
            exportDTO.setCheckCompleteDate(ConverterUtil.convertVoTime(index.getCheckCompleteDate()));
            exportDTO.setFirstPackOutStockDate(ConverterUtil.convertVoTime(index.getFirstPackOutStockDate()));
            exportDTO.setOutStockDate(ConverterUtil.convertVoTime(index.getOutStockDate()));
            exportDTO.setInterceptCancelDate(ConverterUtil.convertVoTime(index.getInterceptCancelDate()));
            dataList.add(exportDTO);
        });
        return dataList;
    }

    /**
     * 获取分页查询 query
     *
     * @param param
     * @return
     */
    private QueryBuilder getQuery(ShipmentEsParam param) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        // 仓库编码
        TermsQueryBuilder warehouseCodeQuery = QueryBuilders.termsQuery("warehouseCode.keyword", CurrentRouteHolder.getWarehouseCode());
        boolQuery.must(warehouseCodeQuery);
        // 货主编码
        if (StrUtil.isNotEmpty(param.getCargoCode())) {
            TermQueryBuilder cargoCodeQuery = QueryBuilders.termQuery("cargoCode.keyword", param.getCargoCode());
            boolQuery.must(cargoCodeQuery);
        }
        if (CollectionUtil.isNotEmpty(param.getCargoCodeList())) {
            TermsQueryBuilder cargoCodeQuery = QueryBuilders.termsQuery("cargoCode.keyword", param.getCargoCodeList());
            boolQuery.must(cargoCodeQuery);
        }
        // 出库单号
        if (CollectionUtil.isNotEmpty(param.getShipmentOrderCodeList())) {
            TermsQueryBuilder shipmentOrderCodeQuery = QueryBuilders.termsQuery("shipmentOrderCode.keyword", param.getShipmentOrderCodeList());
            boolQuery.must(shipmentOrderCodeQuery);
        }
        // 清关状态
        if (CollectionUtil.isNotEmpty(param.getCustomsClearanceStatusList())) {
            TermsQueryBuilder customsClearanceStatus = QueryBuilders.termsQuery("customsClearanceStatus.keyword", param.getCustomsClearanceStatusList());
            boolQuery.must(customsClearanceStatus);
        }
        // 清关类型
        if (StrUtil.isNotBlank(param.getCustomsClearanceType())) {
            TermQueryBuilder customsClearanceType = QueryBuilders.termQuery("customsClearanceType.keyword", param.getCustomsClearanceType());
            boolQuery.must(customsClearanceType);
        }
        //上游单号
        if (CollectionUtil.isNotEmpty(param.getPoNoList())) {
            TermsQueryBuilder poNoQuery = QueryBuilders.termsQuery("poNo.keyword", param.getPoNoList());
            boolQuery.must(poNoQuery);
        }
        //全局单号
        if (CollectionUtil.isNotEmpty(param.getGlobalNoList())) {
            TermsQueryBuilder poNoQuery = QueryBuilders.termsQuery("globalNo.keyword", param.getGlobalNoList());
            boolQuery.must(poNoQuery);
        }
        //ERP单号
        if (CollectionUtil.isNotEmpty(param.getSoNoList())) {
            TermsQueryBuilder soNoQuery = QueryBuilders.termsQuery("soNo.keyword", param.getSoNoList());
            boolQuery.must(soNoQuery);
        }
        // 包裹号
        if (CollectionUtil.isNotEmpty(param.getPackageCodeList())) {
            TermsQueryBuilder packageCodeQuery = QueryBuilders.termsQuery("packageCodeList.keyword", param.getPackageCodeList());
            boolQuery.must(packageCodeQuery);
        }

        // 拣选单号
        if (CollectionUtil.isNotEmpty(param.getPickCodeList())) {
            TermsQueryBuilder pickCodeQuery = QueryBuilders.termsQuery("pickCodeList.keyword", param.getPickCodeList().stream().map(String::toUpperCase).collect(Collectors.toList()));
            boolQuery.must(pickCodeQuery);
        }
        // 出库单状态
        if (StrUtil.isNotEmpty(param.getStatus())) {
            TermQueryBuilder statusQuery = QueryBuilders.termQuery("status.keyword", param.getStatus());
            boolQuery.must(statusQuery);
        }
        // 出库单状态
        if (CollectionUtil.isNotEmpty(param.getStatusList())) {
            TermsQueryBuilder statusQuery = QueryBuilders.termsQuery("status.keyword", param.getStatusList());
            boolQuery.must(statusQuery);
        }

        // 出库单理货报告状态
        if (CollectionUtil.isNotEmpty(param.getTallyStatusList())) {
            TermsQueryBuilder tallyStatusQuery = QueryBuilders.termsQuery("tallyStatus.keyword", param.getTallyStatusList());
            boolQuery.must(tallyStatusQuery);
        }

        // 单据类型
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getOrderType())) {
            TermsQueryBuilder orderTypeQuery = QueryBuilders.termsQuery("orderType.keyword", param.getOrderType());
            boolQuery.must(orderTypeQuery);
        }
        // 业务类型
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getBusinessType())) {
            TermsQueryBuilder businessTypeQuery = QueryBuilders.termsQuery("businessType.keyword", param.getBusinessType());
            boolQuery.must(businessTypeQuery);
        }
        // 预售类型
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getPreSaleType())) {
            TermQueryBuilder preSaleTypeQuery = QueryBuilders.termQuery("preSaleType", param.getPreSaleType());
            boolQuery.must(preSaleTypeQuery);
        }
        // 快递公司
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getCarrierCode())) {
            TermsQueryBuilder carrierCodeQuery = QueryBuilders.termsQuery("carrierCode.keyword", param.getCarrierCode());
            boolQuery.must(carrierCodeQuery);
        }
        // 快递单号
        if (CollectionUtil.isNotEmpty(param.getExpressNoList())) {
            TermsQueryBuilder expressNoQuery = QueryBuilders.termsQuery("expressNoList.keyword", param.getExpressNoList());
            boolQuery.must(expressNoQuery);
        }
        // 商品代码
        if (CollectionUtil.isNotEmpty(param.getSkuCodeList())) {
            TermsQueryBuilder skuCodeQuery = QueryBuilders.termsQuery("skuCodeList.keyword", param.getSkuCodeList());
            boolQuery.must(skuCodeQuery);
        }
        // 商品条码
        if (CollectionUtil.isNotEmpty(param.getUpcCodeList())) {
            TermsQueryBuilder upcCodeQuery = QueryBuilders.termsQuery("upcCodeList.keyword", param.getUpcCodeList());
            boolQuery.must(upcCodeQuery);
        }
        // 交易单号
        if (CollectionUtil.isNotEmpty(param.getTradeNoList())) {
            TermsQueryBuilder tradeNoQuery = QueryBuilders.termsQuery("tradeNo.keyword", param.getTradeNoList());
            boolQuery.must(tradeNoQuery);
        }
        // 销售平台
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getSalePlatform())) {
            TermsQueryBuilder salePlatformQuery = QueryBuilders.termsQuery("salePlatform.keyword", param.getSalePlatform());
            boolQuery.must(salePlatformQuery);
        }
        // 销售店铺
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getSaleShop())) {
            TermsQueryBuilder saleShopQuery = QueryBuilders.termsQuery("saleShop.keyword", param.getSaleShop());
            boolQuery.must(saleShopQuery);
        }
        // 指定包材
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getMaterialUpcCode())) {
            TermsQueryBuilder materialUpcCodeQuery = QueryBuilders.termsQuery("materialUpcCode.keyword", param.getMaterialUpcCode());
            boolQuery.must(materialUpcCodeQuery);
        }
        // 收货人姓名
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getReceiverMan())) {
            TermsQueryBuilder receiverManQuery = QueryBuilders.termsQuery("receiverMan.keyword", param.getReceiverMan());
            boolQuery.must(receiverManQuery);
        }
        // 收货人电话
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getReceiverTel())) {
            TermsQueryBuilder receiverTelQuery = QueryBuilders.termsQuery("receiverTel.keyword", param.getReceiverTel());
            boolQuery.must(receiverTelQuery);
        }
        // 收货省份
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getReceiverArea())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverArea.keyword", param.getReceiverArea());
            boolQuery.must(receiverQuery);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getReceiverCity())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverCity.keyword", param.getReceiverCity());
            boolQuery.must(receiverQuery);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getReceiverProv())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverProv.keyword", param.getReceiverProv());
            boolQuery.must(receiverQuery);
        }

        // 收货省份
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getReceiverAreaName())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverAreaName.keyword", param.getReceiverAreaName());
            boolQuery.must(receiverQuery);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getReceiverCityName())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverCityName.keyword", param.getReceiverCityName());
            boolQuery.must(receiverQuery);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getReceiverProvName())) {
            TermsQueryBuilder receiverQuery = QueryBuilders.termsQuery("receiverProvName.keyword", param.getReceiverProvName());
            boolQuery.must(receiverQuery);
        }
        //省市区多个
        if (CollectionUtil.isNotEmpty(param.getReceiverProvList())) {
            TermsQueryBuilder receiverProvQueryList = QueryBuilders.termsQuery("receiverProv.keyword", param.getReceiverProvList());
            boolQuery.must(receiverProvQueryList);
        }
        if (CollectionUtil.isNotEmpty(param.getReceiverCityList())) {
            TermsQueryBuilder receiverCityQueryList = QueryBuilders.termsQuery("receiverCity.keyword", param.getReceiverCityList());
            boolQuery.must(receiverCityQueryList);
        }
        if (CollectionUtil.isNotEmpty(param.getReceiverAreaList())) {
            TermsQueryBuilder receiverAreaQueryList = QueryBuilders.termsQuery("receiverArea.keyword", param.getReceiverAreaList());
            boolQuery.must(receiverAreaQueryList);
        }


        // 包裹品种类型
        if (CollectionUtil.isNotEmpty(param.getPackageStructList())) {
            TermsQueryBuilder packageStructQuery = QueryBuilders.termsQuery("packageStruct.keyword", param.getPackageStructList());
            boolQuery.must(packageStructQuery);
        }
        // 预计出库时间
        RangeQueryBuilder expOutStockDate = QueryBuilders.rangeQuery("expOutStockDate");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartExpOutStockDate())) {
            expOutStockDate.from(param.getStartExpOutStockDate());
            boolQuery.must(expOutStockDate);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getEndExpOutStockDate())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartExpOutStockDate())) {
                expOutStockDate.from(1);
            }
            expOutStockDate.to(param.getEndExpOutStockDate());
            boolQuery.must(expOutStockDate);
        }

        // 出库日期
        RangeQueryBuilder outStockDate = QueryBuilders.rangeQuery("outStockDate");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartOutStockDate())) {
            outStockDate.from(param.getStartOutStockDate());
            boolQuery.must(outStockDate);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getEndOutStockDate())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartOutStockDate())) {
                outStockDate.from(1);
            }
            outStockDate.to(param.getEndOutStockDate());
            boolQuery.must(outStockDate);
        }

        // 创建时间
        RangeQueryBuilder createdTime = QueryBuilders.rangeQuery("createdTime");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getCreatedTimeStart())) {
            createdTime.from(param.getCreatedTimeStart());
            boolQuery.must(createdTime);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getCreatedTimeEnd())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getCreatedTimeStart())) {
                createdTime.from(1);
            }
            createdTime.to(param.getCreatedTimeEnd());
            boolQuery.must(createdTime);
        }

        // 最晚出库时间
        RangeQueryBuilder expShipTime = QueryBuilders.rangeQuery("expShipTime");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getExpShipTimeStart())) {
            expShipTime.from(param.getExpShipTimeStart());
            boolQuery.must(expShipTime);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getExpShipTimeEnd())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getExpShipTimeStart())) {
                expShipTime.from(1);
            }
            expShipTime.to(param.getExpShipTimeEnd());
            boolQuery.must(expShipTime);
        }
        // 单据来源
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getFromSource())) {
            TermsQueryBuilder fromSource = QueryBuilders.termsQuery("fromSource.keyword", param.getFromSource());
            boolQuery.must(fromSource);
        }
        // 回传状态
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getNotifyStatus())) {
            TermQueryBuilder notifyStatus = QueryBuilders.termQuery("notifyStatus", param.getNotifyStatus());
            boolQuery.must(notifyStatus);
        }
        // 品种数
        RangeQueryBuilder skuTypeQty = QueryBuilders.rangeQuery("skuTypeQty");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartSkuTypeCount())) {
            skuTypeQty.from(param.getStartSkuTypeCount());
            boolQuery.must(skuTypeQty);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getEndSkuTypeCount())) {
            skuTypeQty.to(param.getEndSkuTypeCount());
            boolQuery.must(skuTypeQty);
        }
        // 商品数
        RangeQueryBuilder skuQty = QueryBuilders.rangeQuery("skuQty");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartSkuCount())) {
            skuQty.from(param.getStartSkuCount());
            boolQuery.must(skuQty);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getEndSkuCount())) {
            skuQty.to(param.getEndSkuCount());
            boolQuery.must(skuQty);
        }
        // 付款时间
        RangeQueryBuilder payDate = QueryBuilders.rangeQuery("payDate");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartPayTime())) {
            payDate.from(param.getStartPayTime());
            boolQuery.must(payDate);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getEndPayTime())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartPayTime())) {
                payDate.from(1);
            }
            payDate.to(param.getEndPayTime());
            boolQuery.must(payDate);
        }
        // 交易下单时间
        RangeQueryBuilder placeTradeOrderDate = QueryBuilders.rangeQuery("placeTradeOrderDate");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartTradeOrderDate())) {
            placeTradeOrderDate.from(param.getStartTradeOrderDate());
            boolQuery.must(placeTradeOrderDate);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getEndTradeOrderDate())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getStartTradeOrderDate())) {
                placeTradeOrderDate.from(1);
            }
            placeTradeOrderDate.to(param.getEndTradeOrderDate());
            boolQuery.must(placeTradeOrderDate);
        }
        // 理论重量
        RangeQueryBuilder weight = QueryBuilders.rangeQuery("weight");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getWeightStart())) {
            weight.from(param.getWeightStart());
            boolQuery.must(weight);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getWeightEnd())) {
            weight.to(param.getWeightEnd());
            boolQuery.must(weight);
        }
        // 订单标记
        if (!CollectionUtils.isEmpty(param.getOrderTagList())) {
            param.getOrderTagList().forEach(it -> {
                boolQuery.must(QueryBuilders.matchQuery("orderTagList", it));
            });
        }

        // 订单标记 不包含
        if (!CollectionUtils.isEmpty(param.getOrderTagNoContainList())) {
            param.getOrderTagNoContainList().forEach(it -> {
                boolQuery.mustNot(QueryBuilders.matchQuery("orderTagList", it));
            });
        }

        // 订单标记 不包含
        if (!CollectionUtils.isEmpty(param.getOrderTagNoContainList())) {
            param.getOrderTagNoContainList().forEach(it -> {
                boolQuery.mustNot(QueryBuilders.matchQuery("orderTagList", it));
            });
        }

        // 拣选完成时间
        RangeQueryBuilder pickCompleteSkuDate = QueryBuilders.rangeQuery("pickCompleteSkuDate");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getPickCompleteSkuDateStart())) {
            pickCompleteSkuDate.from(param.getPickCompleteSkuDateStart());
            boolQuery.must(pickCompleteSkuDate);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getPickCompleteSkuDateEnd())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getPickCompleteSkuDateStart())) {
                pickCompleteSkuDate.from(1);
            }
            pickCompleteSkuDate.to(param.getPickCompleteSkuDateEnd());
            boolQuery.must(pickCompleteSkuDate);
        }

        // 复核完成时间
        RangeQueryBuilder checkCompleteDate = QueryBuilders.rangeQuery("checkCompleteDate");
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getCheckCompleteDateStart())) {
            checkCompleteDate.from(param.getCheckCompleteDateStart());
            boolQuery.must(checkCompleteDate);
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getCheckCompleteDateEnd())) {
            if (!org.apache.commons.lang3.ObjectUtils.isNotEmpty(param.getCheckCompleteDateStart())) {
                checkCompleteDate.from(1);
            }
            checkCompleteDate.to(param.getCheckCompleteDateEnd());
            boolQuery.must(checkCompleteDate);
        }

        return boolQuery;

    }

    /**
     * 设置默认值
     *
     * @param dto 分析结果DTO
     */
    private void setDefaultValues(ShipmentAnalysisDTO dto) {
        dto.setOrderCount(0);
        dto.setSkuQtySum(BigDecimal.ZERO);
        setDefaultStatusCounts(dto);
    }

    /**
     * 设置默认状态计数
     *
     * @param dto 分析结果DTO
     */
    private void setDefaultStatusCounts(ShipmentAnalysisDTO dto) {
        dto.setCreatedOrderCount(0);
        dto.setPretreatmentFailOrderCount(0);
        dto.setPretreatmentCompleteOrderCount(0);
        dto.setCollectedOrderCount(0);
        dto.setCheckStartOrderCount(0);
        dto.setCheckCompleteOrderCount(0);
        dto.setPartialOutOrderCount(0);
        dto.setOutOrderCount(0);
        dto.setInterceptOrderCount(0);
        dto.setCancelOrderCount(0);
        dto.setShortageOutOrderCount(0);
    }

}
