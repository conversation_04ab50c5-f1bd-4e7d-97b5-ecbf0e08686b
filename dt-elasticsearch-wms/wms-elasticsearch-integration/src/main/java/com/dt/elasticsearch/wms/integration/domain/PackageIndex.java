package com.dt.elasticsearch.wms.integration.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/9 9:18
 */
@Data
@Document(indexName = "${@indexNameConfig.packageIndexName}",type = "_doc")
public class PackageIndex  implements java.io.Serializable  {

    @Field(ignoreFields = "scrollId")
    private String scrollId;

    @Id
    private String indexId;

    private Long key;

    private List<String> skuCodeList;

    private List<String> upcCodeList;

    @ApiModelProperty(value = "包裹号")
    @Field(type = FieldType.Keyword)
    private String packageCode;

    @ApiModelProperty(value = "出库单号")
    @Field(type = FieldType.Keyword)
    private String shipmentOrderCode;

    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    @Field(type = FieldType.Keyword)
    private String poNo;

    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    @Field(type = FieldType.Keyword)
    private String soNo;

    @ApiModelProperty(value = "货主编码")
    @Field(type = FieldType.Keyword)
    private String cargoCode;
    @Field(type = FieldType.Keyword)
    private String cargoName;

    @ApiModelProperty(value = "商品正/次品属性")
    @Field(type = FieldType.Keyword)
    private String skuQuality;
    @ApiModelProperty(value = "商品正/次品属性")
    @Field(type = FieldType.Keyword)
    private String skuQualityName;
    @ApiModelProperty(value = "仓库编码")
    @Field(type = FieldType.Keyword)
    private String warehouseCode;
    @Field(type = FieldType.Keyword)
    private String warehouseName;

    @ApiModelProperty(value = "包裹状态")
    @Field(type = FieldType.Keyword)
    private String status;
    @Field(type = FieldType.Keyword)
    private String statusName;

    @ApiModelProperty(value = "快递公司编码")
    @Field(type = FieldType.Keyword)
    private String carrierCode;
    @Field(type = FieldType.Keyword)
    private String carrierName;

    @ApiModelProperty(value = "快递单号")
    @Field(type = FieldType.Keyword)
    private String expressNo;

    @ApiModelProperty(value = "销售店铺ID")
    @Field(type = FieldType.Keyword)
    private String saleShopId;

    @ApiModelProperty(value = "销售店铺")
    @Field(type = FieldType.Keyword)
    private String saleShop;
    @ApiModelProperty(value = "销售平台")
    @Field(type = FieldType.Keyword)
    private String salePlatform;
    @ApiModelProperty(value = "销售平台")
    @Field(type = FieldType.Keyword)
    private String salePlatformName;

    @ApiModelProperty(value = "包裹结构")
    @Field(type = FieldType.Keyword)
    private String packageStruct;
    @Field(type = FieldType.Keyword)
    private String packageStructName;

    @ApiModelProperty(value = "拣货开始时间")
    @Field(type = FieldType.Keyword)
    private Long pickSkuDate;

    @ApiModelProperty(value = "拣货完成时间")
    @Field(type = FieldType.Keyword)
    private Long pickCompleteSkuDate;

    @ApiModelProperty(value = "复核开始时间")
    @Field(type = FieldType.Keyword)
    private Long checkStartDate;

    @ApiModelProperty(value = "复核完成时间")
    @Field(type = FieldType.Keyword)
    private Long checkCompleteDate;

    @ApiModelProperty(value = "出库时间")
    @Field(type = FieldType.Keyword)
    private Long outStockDate;

    @ApiModelProperty(value = "拦截取消时间")
    @Field(type = FieldType.Keyword)
    private Long interceptCancelDate;

    @ApiModelProperty(value = "商品品种数")
    private Integer skuTypeQty;

    @ApiModelProperty(value = "包裹商品数量")
    @Field(type = FieldType.Keyword)
    private String packageSkuQty;

    @ApiModelProperty(value = "出库商品数量")
    @Field(type = FieldType.Keyword)
    private String outSkuQty;

    @ApiModelProperty(value = "推荐包材条码")
    @Field(type = FieldType.Keyword)
    private String recPackUpc;

    @ApiModelProperty(value = "实际使用包材条码")
    @Field(type = FieldType.Keyword)
    private String actualPackUpc;

    @ApiModelProperty(value = "操作人")
    @Field(type = FieldType.Keyword)
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    @Field(type = FieldType.Keyword)
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @Field(type = FieldType.Keyword)
    private Long createdTime;

    @ApiModelProperty(value = "业务类型")
    @Field(type = FieldType.Keyword)
    private String businessType;
    @ApiModelProperty(value = "包材重量")
    @Field(type = FieldType.Keyword)
    private String actualPackWeight;
    @ApiModelProperty(value = "理论重量")
    @Field(type = FieldType.Keyword)
    private String weight;
    @ApiModelProperty(value = "实际重量")
    @Field(type = FieldType.Keyword)
    private String realWeight;


    @ApiModelProperty(value = "包材数量")
    private Integer actualPackNum;

    @ApiModelProperty(value = "更新时间")
    @Field(type = FieldType.Keyword)
    private Long updatedTime;
    @ApiModelProperty(value = "交易单号")
    @Field(type = FieldType.Keyword)
    private String tradeNo;

    @ApiModelProperty(value = "收货省份")
    @Field(type = FieldType.Keyword)
    private String receiverProv;
    @ApiModelProperty(value = "收货市")
    @Field(type = FieldType.Keyword)
    private String receiverCity;
    @ApiModelProperty(value = "收货区")
    @Field(type = FieldType.Keyword)
    private String receiverArea;
    @Field(type = FieldType.Keyword)
    private String receiverProvName;
    @Field(type = FieldType.Keyword)
    private String receiverCityName;
    @Field(type = FieldType.Keyword)
    private String receiverAreaName;
    @ApiModelProperty(value = "收货人姓名")
    @Field(type = FieldType.Keyword)
    private String receiverMan;

    @ApiModelProperty(value = "联系电话")
    @Field(type = FieldType.Keyword)
    private String receiverTel;

    @ApiModelProperty(value = "收获地址")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;

    @ApiModelProperty(value = "包材")
    @Field(type = FieldType.Keyword)
    private List<String> materialUpcCodeList;

    @ApiModelProperty
    private String pickCode;

    @ApiModelProperty(value = "面单打印次数")
    private Integer expressPrintNum;


    @ApiModelProperty(value = "订单履约时间")
    @Field(type = FieldType.Keyword)
    private String expShipTimeDateFormat;

    @ApiModelProperty(value = "预售类型")
    private Integer preSaleType;
    @Field(type = FieldType.Keyword)
    private String preSaleTypeName;

    @ApiModelProperty(value = "预处理状态")
    @Field(type = FieldType.Keyword)
    private String pretreatmentStatus;
    @Field(type = FieldType.Keyword)
    private String pretreatmentStatusName;

    @ApiModelProperty(value = "是否预包包裹 1:普通包裹 2:预包包裹  枚举：PackEnum.TYPE")
    @Field(type = FieldType.Keyword)
    private String isPre;
    @Field(type = FieldType.Keyword)
    private String isPreName;
    @Field(type = FieldType.Keyword)
    private String isPreDesc;

    @ApiModelProperty(value = "快递网点编码")
    @Field(type = FieldType.Keyword)
    private String expressBranch;

    @ApiModelProperty(value = "快递网点编码名称")
    @Field(type = FieldType.Keyword)
    private String expressBranchName;

    @ApiModelProperty(value = "快递账号")
    @Field(type = FieldType.Keyword)
    private String expressAccount;

    /**
     * 同步ES时间
     */
    private Long esUpdatedTime;

    private String volumetricWeight;

    private BigDecimal volume;

    private Integer orderTag;
    private List<Integer> orderTagList;
    
    private String extraJson;

    private Integer is4pl;

    private String backFlag;

    private String materialUpcCode;

    private String orderType;

    private Long ordCreatedTime;

    private Long expOutStockDate;

    private Long expShipTime;

    private Long payDate;

    private String remark;
}