package com.dt.elasticsearch.wms.integration.domain;

import java.io.Serializable;
import java.math.BigDecimal;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: yousx
 * @Date: 2025/06/09
 * @Description:
 */
@Data
@Document(indexName = "${@indexNameConfig.asnStatisticsIndexName}", type = "_doc")
public class AsnStatisticsIndex implements Serializable {

    @Id
    private String indexId;

    /**
     * 统计时间
     */
    @Field(type = FieldType.Keyword)
    private String statDate;

    /**
     * 收货完成时间
     */
    private Long recDateEnd;

    @Field(type = FieldType.Keyword)
    private String type;

    /**
     * 货主编码
     */
    @Field(type = FieldType.Keyword)
    private String cargoCode;

    @ApiModelProperty(value = "实收数量 默认:0")
    private BigDecimal recSkuQty;

    @ApiModelProperty(value = "实收品种数 默认:0")
    private Integer recSkuType;

    @ApiModelProperty(value = "总体积(m³) 默认0、保留三位小数")
    private BigDecimal volume;

    @ApiModelProperty(value = "总毛重(KG) 默认0、保留三位小数")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "总净重(KG) 默认0、保留三位小数")
    private BigDecimal netWeight;
}
