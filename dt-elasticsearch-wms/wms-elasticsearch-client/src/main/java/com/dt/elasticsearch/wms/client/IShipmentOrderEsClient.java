package com.dt.elasticsearch.wms.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IndexVO;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.wms.dto.ShipmentAnalysisDTO;
import com.dt.elasticsearch.wms.dto.ShipmentIndexDTO;
import com.dt.elasticsearch.wms.dto.ShipmentScrollResultDTO;
import com.dt.elasticsearch.wms.param.ShipmentAnalysisParam;
import com.dt.elasticsearch.wms.param.ShipmentEsParam;
import com.dt.elasticsearch.wms.param.ShipmentOrderEsParam;

import java.util.List;

/**
 * 出库单
 *
 * <AUTHOR>
 */
public interface IShipmentOrderEsClient {
    /**
     * 出库折线图数据
     *
     * @param param
     * @return
     */
    IndexVO getData(ShipmentOrderEsParam param);

    /**
     * 出库订单需发总量与实发总量统计图 条形统计图
     * @param param
     * @return
     */
    IndexVO getOrderData(ShipmentOrderEsParam param);

    /**
     * 出库单分页查询
     * @param param
     * @return
     */
    Result<PageVO<ShipmentIndexDTO>> getPage(ShipmentEsParam param);

    Result<ShipmentScrollResultDTO> getByScroll(ShipmentEsParam param);

//    /**
//     * 出库单分析查询
//     * 支持按多个维度分组统计订单数量和商品数量
//     *
//     * @param param 查询参数
//     * @return 分析结果列表
//     */
//    Result<List<ShipmentAnalysisDTO>> getShipmentAnalysis(ShipmentAnalysisParam param);

//    /**
//     * 出库单分析查询（分页版）
//     * 支持按多个维度分组统计订单数量和商品数量，并以分页方式返回结果
//     *
//     * @param param 查询参数
//     * @return 分页分析结果
//     */
//    Result<PageVO<ShipmentAnalysisDTO>> getShipmentAnalysisPage(ShipmentAnalysisParam param);
//
//    /**
//     * 出库单分析汇总查询
//     * 对查询条件下的所有数据进行汇总，返回订单数和商品数量的求和
//     *
//     * @param param 查询参数
//     * @return 汇总结果
//     */
//    Result<ShipmentAnalysisDTO> getShipmentAnalysisSummary(ShipmentAnalysisParam param);
}
