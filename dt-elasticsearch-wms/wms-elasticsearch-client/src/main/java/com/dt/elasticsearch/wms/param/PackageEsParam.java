package com.dt.elasticsearch.wms.param;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/9 13:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PackageEsParam extends BaseSearchParam  implements java.io.Serializable  {

    private String scrollId;

    private String warehouseCode;

    @ApiModelProperty(value = "包裹号")
    private List<String> packageCodeList;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;
    @ApiModelProperty(value = "出库单号列表")
    private List<String> shipmentOrderCodeList;
    @ApiModelProperty(value = "客户端号")
    private List<String> soNoList;

    @ApiModelProperty(value = "客户单号")
    private String poNo;
    private List<String> poNoList;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    @ApiModelProperty(value = "包裹状态")
    private String status;
    private List<String> statusList;

    @ApiModelProperty(value = "预处理状态")
    private String pretreatmentStatus;
    private List<String> pretreatmentStatusList;

    @ApiModelProperty(value = "单据类型")
    private String orderType;

    @ApiModelProperty(value = "拣选单号")
    private String pickCode;
    @ApiModelProperty(value = "拣选单号")
    private List<String> pickCodeList;

    @ApiModelProperty(value = "交易单号")
    private List<String> tradeNoList;

    @ApiModelProperty(value = "商品代码")
    private List<String> skuCodeList;

    @ApiModelProperty(value = "商品条码")
    private List<String> upcCodeList;

    @ApiModelProperty(value = "包裹结构")
    private List<String> packageStructList;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    @ApiModelProperty(value = "快递公司编码")
    private List<String> carrierCodeList;

    @ApiModelProperty(value = "快递单号")
    private List<String> expressNoList;


    @ApiModelProperty(value = "销售平台")
    private String salePlatform;

    @ApiModelProperty(value = "销售店铺")
    private String saleShop;

    @ApiModelProperty(value = "订单品种类型")
    private List<String> orderSkuTypeList;


//    @ApiModelProperty(value = "创建开始时间")
//    private Long startCreateTime;
//
//    @ApiModelProperty(value = "创建结束时间")
//    private Long endCreateTime;

    @ApiModelProperty(value = "订单标记")
    private Integer orderTag;
    private List<Integer> orderTagList;


    @ApiModelProperty(value = "出库开始时间")
    private Long startOutStockDate;

    @ApiModelProperty(value = "出库结束时间")
    private Long endOutStockDate;


    @ApiModelProperty(value = "预计出库时间开始时间")
    private Long startExpOutStockDate;

    @ApiModelProperty(value = "预计出库时间结束时间")
    private Long endExpOutStockDate;

    @ApiModelProperty(value = "收货人姓名")
    private String receiverMan;
    @ApiModelProperty(value = "联系电话")
    private String receiverTel;
    @ApiModelProperty(value = "收货省份")
    private String receiverProv;
    private String receiverProvName;
    @ApiModelProperty(value = "收货市")
    private String receiverCity;
    private String receiverCityName;
    @ApiModelProperty(value = "收货区")
    private String receiverArea;
    private String receiverAreaName;

    @ApiModelProperty(value = "日汇总页面查询")
    private Boolean summarySelect;

    @ApiModelProperty(value = "包材")
    private String materialUpcCode;


    @ApiModelProperty(value = "订单创建开始时间")
    @Getter
    @Setter
    private Long ordCreatedTimeStart;

    @ApiModelProperty(value = "订单创建结束时间")
    @Getter
    @Setter
    private Long ordCreatedTimeEnd;

    @ApiModelProperty(value = "预售类型")
    private Integer preSaleType;

    @ApiModelProperty(value = "发货超时时间")
    private Long expShipTimeStart;
    private Long expShipTimeEnd;
    @ApiModelProperty(value = "是否预包包裹 1:普通包裹 2:预包包裹  枚举：PackEnum.TYPE")
    private String isPre;

    @ApiModelProperty(value = "快递网点编码")
    private String expressBranch;
    private List<String> expressBranchList;

    @ApiModelProperty(value = "快递网点编码名称")
    private String expressBranchName;

    @ApiModelProperty(value = "快递账号")
    private String expressAccount;
    private List<String> expressAccountList;

    @ApiModelProperty(value = "复核完成查询条件")
    private Long checkCompleteDateStart;
    private Long checkCompleteDateEnd;
    @ApiModelProperty(value = "拣选完成查询条件")
    private Long pickCompleteSkuDateStart;
    private Long pickCompleteSkuDateEnd;

    //省
    private List<String> receiverProvList;
    //市
    private List<String> receiverCityList;
    //区
    private List<String> receiverAreaList;

    //----
    @ApiModelProperty(value = "品种数(开始)")
    private Integer startSkuTypeCount;

    @ApiModelProperty(value = "品种数(结束)")
    private Integer endSkuTypeCount;

    @ApiModelProperty(value = "商品数(开始)")
    private Integer startSkuCount;

    @ApiModelProperty(value = "商品数(结束)")
    private Integer endSkuCount;

    @ApiModelProperty(value = "理论重量")
    private BigDecimal weightStart;
    private BigDecimal weightEnd;

    @ApiModelProperty(value = "付款时间(开始)")
    private Long startPayTime;

    @ApiModelProperty(value = "付款时间(结束)")
    private Long endPayTime;
    //----
}