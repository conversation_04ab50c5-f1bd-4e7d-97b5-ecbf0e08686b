package com.dt.elasticsearch.wms.param;

import java.awt.Dimension;
import java.io.Serializable;

import com.dt.component.common.param.BaseQueryParam;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: yousx
 * @Date: 2025/06/10
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AsnStatisticsParam extends BaseQueryParam implements Serializable {

    /**
     * 收货完成时间
     */
    @ApiModelProperty("收货完成时间")
    private Long recDateStart;
    private Long recDateEnd;

    /**
     * 业务类型 字典组:ASN_TYPE
     */
    @ApiModelProperty(value = "业务类型 字典组:ASN_TYPE")
    private String type;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 分析维度
     */
    @ApiModelProperty(value = "分析维度")
    private String analysisDimension;
}
