package com.dt.wms.job.sku;

import cn.hutool.json.JSONUtil;
import com.dt.domain.base.param.SkuParam;
import com.dt.platform.wms.client.ISkuBizClient;
import com.dt.wms.job.config.DefaultWarehouseCodeConfig;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class SkuItemCronTab {

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @DubboReference
    ISkuBizClient skuBizClient;

    @XxlJob("skuItemCronTabJobHandler")
    public ReturnT<String> skuItemCronTabJobHandler(String param) throws Exception {
        //TODO 只能按货主维度检查 check = true 仅仅用于检查,不做更新.货主和商品代码传参只做更新指定的货主和商品
        //{"check":true,"cargoCode":"xxx","skuCode":"xxxx"}
        XxlJobLogger.log(param + "-skuItemCronTabJobHandler 任务开始。");
        List<String> warehouseCodesList = defaultWarehouseCodeConfig.getWarehouseCodeList();
        if (warehouseCodesList.isEmpty()) {
            log.error("该定时任务没有设置仓库编号!");
            return ReturnT.SUCCESS;
        }
        //param = cargoCode#skuCode
        warehouseCodesList.parallelStream().forEach(warehouseCode -> {
            try {
                skuBizClient.skuItemToUpdate(param, warehouseCode);
            } catch (Exception e) {

            }
        });
        XxlJobLogger.log(param + "-skuItemCronTabJobHandler 任务结束。");
        return ReturnT.SUCCESS;
    }

    @XxlJob("skuItemCallBackTaoTianCronTabJobHandler")
    public ReturnT<String> skuItemCallBackTaoTianCronTabJobHandler(String param) throws Exception {
        XxlJobLogger.log(param + "-skuItemCallBackTaoTianCronTabJobHandler 任务开始。");
        List<String> warehouseCodesList = defaultWarehouseCodeConfig.getWarehouseCodeList();
        if (warehouseCodesList.isEmpty()) {
            log.error("该定时任务没有设置仓库编号!");
            return ReturnT.SUCCESS;
        }
        //param = cargoCode#skuCode
        warehouseCodesList.parallelStream().forEach(warehouseCode -> {
            try {
                skuBizClient.skuCallBackTaoTian(param, warehouseCode);
            } catch (Exception e) {

            }
        });
        XxlJobLogger.log(param + "-skuItemCallBackTaoTianCronTabJobHandler 任务结束。");
        return ReturnT.SUCCESS;
    }


    @XxlJob("skuTaoTianOverTimeWarmCronTabJobHandler")
    public ReturnT<String> skuTaoTianOverTimeWarmCronTabJobHandler(String param) throws Exception {
        XxlJobLogger.log(param + "-skuTaoTianOverTimeWarmCronTabJobHandler 任务开始。");
        List<String> warehouseCodesList = defaultWarehouseCodeConfig.getWarehouseCodeList();
        if (warehouseCodesList.isEmpty()) {
            log.error("该定时任务没有设置仓库编号!");
            return ReturnT.SUCCESS;
        }
        warehouseCodesList.parallelStream().forEach(warehouseCode -> {
            try {
                skuBizClient.skuTaoTianOverTimeWarm(param, warehouseCode);
            } catch (Exception e) {

            }
        });
        XxlJobLogger.log(param + "-skuTaoTianOverTimeWarmCronTabJobHandler 任务结束。");
        return ReturnT.SUCCESS;
    }

//    /**
//     * 淘天商品比对
//     *
//     * @param param
//     * @return
//     * @throws Exception
//     */
//    @XxlJob("skuTaoTianCompareCronTabJobHandler")
//    public ReturnT<String> skuTaoTianCompareCronTabJobHandler(String param) throws Exception {
//        XxlJobLogger.log(param + "-skuTaoTianCompareCronTabJobHandler 任务开始。");
//        List<String> warehouseCodesList = defaultWarehouseCodeConfig.getWarehouseCodeList();
//        if (warehouseCodesList.isEmpty()) {
//            log.error("该定时任务没有设置仓库编号!");
//            return ReturnT.SUCCESS;
//        }
//        warehouseCodesList.parallelStream().forEach(warehouseCode -> {
//            try {
//                skuBizClient.skuTaoTianCompare(param, warehouseCode);
//            } catch (Exception e) {
//
//            }
//        });
//        XxlJobLogger.log(param + "-skuTaoTianCompareCronTabJobHandler 任务结束。");
//        return ReturnT.SUCCESS;
//    }


    @XxlJob("skuSyncERPCronTabJobHandler")
    public ReturnT<String> skuSyncERPCronTabJobHandler(String param) throws Exception {
        //TODO 支持仓库(必传)，货主(支持单个和数组) 商品(支持单个和数组)
        XxlJobLogger.log(param + "-skuSyncERPCronTabJobHandler 任务开始。");
        List<String> warehouseCodesList = defaultWarehouseCodeConfig.getSyncSkuWarehouseCodeList();
        if (warehouseCodesList.isEmpty()) {
            log.error("该定时任务没有设置仓库编号!");
            return ReturnT.SUCCESS;
        }
        if (!StringUtils.isEmpty(param)) {
            if (!JSONUtil.isJson(param)) {
                log.error("该定时任务参数非JSON!");
                return ReturnT.SUCCESS;
            }
            SkuParam skuParam = JSONUtil.toBean(param, SkuParam.class);
            if (CollectionUtils.isEmpty(skuParam.getWarehouseCodeList())) {
                log.error("该定时任务仓库必传");
                return ReturnT.SUCCESS;
            }
            //param = cargoCode#skuCode
            skuParam.getWarehouseCodeList().parallelStream().forEach(warehouseCode -> {
                if (warehouseCodesList.contains(warehouseCode)) {
                    try {
                        skuBizClient.dataSyncWms(skuParam, warehouseCode);
                    } catch (Exception e) {

                    }
                } else {
                    log.error("仓库编码不存在仓库列表中");
                }
            });
        } else {
            warehouseCodesList.parallelStream().forEach(warehouseCode -> {
                try {
                    SkuParam skuParam = new SkuParam();
                    skuParam.setWarehouseCode(warehouseCode);
                    skuBizClient.dataSyncWms(skuParam, warehouseCode);
                } catch (Exception e) {

                }
            });
        }
        XxlJobLogger.log(param + "-skuSyncERPCronTabJobHandler 任务结束。");
        return ReturnT.SUCCESS;
    }


}
