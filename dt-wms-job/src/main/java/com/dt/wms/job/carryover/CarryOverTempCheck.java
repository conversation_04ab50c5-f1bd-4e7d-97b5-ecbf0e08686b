package com.dt.wms.job.carryover;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.client.IAsnBillClient;
import com.dt.domain.bill.client.IReceiptDetailClient;
import com.dt.domain.bill.client.IShelfClient;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.domain.bill.dto.ReceiptDetailDTO;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.param.AsnParam;
import com.dt.domain.bill.param.ReceiptDetailParam;
import com.dt.domain.bill.param.ShelfParam;
import com.dt.wms.job.config.DefaultWarehouseCodeConfig;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Component
public class CarryOverTempCheck {

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @DubboReference
    private IShelfClient shelfClient;

    @DubboReference
    private IReceiptDetailClient receiptDetailClient;

    @DubboReference
    private IAsnBillClient asnBillClient;

    // 需要处理的运单
    private String expressNos = "464152413913512\n" +
            "464161727812724\n" +
            "464164289598000\n" +
            "464165967137918\n" +
            "464168966710066\n" +
            "464155138312817\n" +
            "464161619443128\n" +
            "464157404803385\n" +
            "312640414008411\n" +
            "464164996280445\n" +
            "312641144788730\n" +
            "428150430685886\n" +
            "464150488964429\n" +
            "464152424280355\n" +
            "464146351110236\n" +
            "464154951915040\n" +
            "464149980934885\n" +
            "464155003071846\n" +
            "312637026842080\n" +
            "464170942544359\n" +
            "464164813410767\n" +
            "464167040052102\n" +
            "464147195352508\n" +
            "464149957037384\n" +
            "464162432404979\n" +
            "464158643213910\n" +
            "464162481656576\n" +
            "464161681712261\n" +
            "464172182599325\n" +
            "464164263795023\n" +
            "464152462256516\n" +
            "464160331897296\n" +
            "464167538553194\n" +
            "464173154023836\n" +
            "464161615867568\n" +
            "464146529865119\n" +
            "464158812240673\n" +
            "464173471154578\n" +
            "312640929131592\n" +
            "464148558892555\n" +
            "464149956512218\n" +
            "464167195835669\n" +
            "464162904190472\n" +
            "464163968772980\n" +
            "464161713986074\n" +
            "464164234982594\n" +
            "464173463500049\n" +
            "464164996282809\n" +
            "464164025176751\n" +
            "464167301379653\n" +
            "464161706927479\n" +
            "464164999021950\n" +
            "464164679219306\n" +
            "464161404203134\n" +
            "464160170843397\n" +
            "464168271980727\n" +
            "464152471188508\n" +
            "464167042556850\n" +
            "464148362705183\n" +
            "464167670134540\n" +
            "464167057988451\n" +
            "464158393148569\n" +
            "464152421826029\n" +
            "464155259663959\n" +
            "464147702291772\n" +
            "464150827990033\n" +
            "464154035673713\n" +
            "312632975663912\n" +
            "464158870864182\n" +
            "312638171889841\n" +
            "464173867581212\n" +
            "464160144786487\n" +
            "464167060220896\n" +
            "464173823679898\n" +
            "312635046472530\n" +
            "464146350898399\n" +
            "464161840130818\n" +
            "464142863600024\n" +
            "464164040650711\n" +
            "464164026245125\n" +
            "464173073448659\n" +
            "464150569420349\n" +
            "464173070220799\n" +
            "464173212842424\n" +
            "464158456680160\n" +
            "464167633174553\n" +
            "464173114669258\n" +
            "464161588330336\n" +
            "464162493661763\n" +
            "464152428325791";


    @XxlJob("CarryOverTempCheck")
    public ReturnT<String> carryOverTempCheck(String param) {
        if (StrUtil.isEmpty(param)) return ReturnT.SUCCESS;
        RpcContextUtil.setWarehouseCode(param);
        List<String> expressNoList = Arrays.stream(expressNos.split("\n")).collect(Collectors.toList());

        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setCompleteDateStart(1740414271000L);
        List<ShelfDTO> list = shelfClient.getList(shelfParam).getData();
        for (ShelfDTO shelfDTO : list) {
            try {
                if (StrUtil.isBlank(shelfDTO.getBillNo())) continue;
                ReceiptDetailParam receiptDetailParam = new ReceiptDetailParam();
                receiptDetailParam.setRecId(shelfDTO.getBillNo());
                List<ReceiptDetailDTO> receiptDetailDTOList = receiptDetailClient.getList(receiptDetailParam).getData();
                if (CollectionUtil.isEmpty(receiptDetailDTOList)) continue;

                for (ReceiptDetailDTO receiptDetailDTO : receiptDetailDTOList) {
                    if (StrUtil.isBlank(receiptDetailDTO.getAsnId())) continue;
                    AsnParam asnParam = new AsnParam();
                    asnParam.setAsnId(receiptDetailDTO.getAsnId());
                    AsnDTO asnDTO = asnBillClient.get(asnParam).getData();
                    if (null == asnDTO) continue;
                    String logisticsNo = JSONUtil.parseObj(asnDTO.getExtraJson()).getStr("logisticsNo", "none");
                    if (expressNoList.contains(logisticsNo)) {
                        XxlJobLogger.log("上架单需要处理 " + shelfDTO.getCode());
                    }
                }
            } catch (Exception exception) {
                XxlJobLogger.log("CarryOverTempCheck error" + exception.getMessage());
            }
        }

        return ReturnT.SUCCESS;
    }


}
