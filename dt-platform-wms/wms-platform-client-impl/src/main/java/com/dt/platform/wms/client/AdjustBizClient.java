package com.dt.platform.wms.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.AuditEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.base.*;
import com.dt.component.common.enums.bill.*;
import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.enums.cargo.CargoConfigStatusEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.message.LargeMessageOperationTypeEnum;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.component.common.enums.sku.SkuLifeCtrlEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.sku.SkuStatusEnum;
import com.dt.component.common.enums.wms.WmsAdjustBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.exceptions.WmsBizException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.log.AdjustLogDTO;
import com.dt.domain.base.param.*;
import com.dt.domain.bill.dto.AdjustDTO;
import com.dt.domain.bill.dto.AdjustDetailDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.related.RelatedBillDTO;
import com.dt.domain.bill.dto.rs.OpExceptionDTO;
import com.dt.domain.bill.dto.rs.OpExceptionDetailDTO;
import com.dt.domain.bill.param.AdjustDetailParam;
import com.dt.domain.bill.param.AdjustParam;
import com.dt.domain.bill.param.related.RelatedBillParam;
import com.dt.domain.bill.param.rs.OpExceptionDetailParam;
import com.dt.domain.bill.param.rs.OpExceptionParam;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.biz.ICargoConfigBiz;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.param.SkuLotCheckAndFormatParam;
import com.dt.platform.wms.dto.adjust.AdjustBizDTO;
import com.dt.platform.wms.dto.adjust.AdjustCreateSkuLotBizDTO;
import com.dt.platform.wms.dto.adjust.AdjustDetailBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.log.IRemoteAdjustLogClient;
import com.dt.platform.wms.integration.mercury.IRemoteMercuryClient;
import com.dt.platform.wms.integration.related.IRemoteRelatedBillClient;
import com.dt.platform.wms.integration.rs.IRemoteOpExceptionClient;
import com.dt.platform.wms.integration.rs.IRemoteOpExceptionDetailClient;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.adjust.AdjustBizParam;
import com.dt.platform.wms.param.adjust.AdjustCreateSkuLotParam;
import com.dt.platform.wms.transaction.IAdjustGtsService;
import com.dt.platform.wms.transaction.bo.AdjustBO;
import com.dt.platform.wms.transaction.bo.AdjustCompleteBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dt.platform.utils.ConverterUtil.convertList;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class AdjustBizClient implements IAdjustBizClient {

    @Resource
    private IRemoteAdjustClient remoteAdjustClient;

    @Resource
    private IRemoteAdjustDetailClient remoteAdjustDetailClient;

    @Resource
    private IAdjustGtsService adjustContextService;

    @Resource
    private IRemoteMercuryClient remoteMercuryClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteCargoConfigClient remoteCargoConfigClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    IRemoteLotRuleClient remoteLotRuleClient;

    @Resource
    ISkuLotBiz skuLotBiz;

    @Resource
    private IRemoteLocationClient remoteLocationClient;

    @Resource
    private IRemoteZoneClient remoteZoneClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteSeqRuleClient iRemoteSeqRuleClient;

    @Resource
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    private IRemoteAdjustLogClient remoteAdjustLogClient;

    @Resource
    private ICargoConfigBiz cargoConfigBiz;

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    private IRemoteLockSupportClient remoteLockSupportClient;

    @Resource
    private IRemoteRelatedBillClient remoteRelatedBillClient;

    @Resource
    private IRemoteOpExceptionClient remoteOpExceptionClient;

    @Resource
    private IRemoteOpExceptionDetailClient remoteOpExceptionDetailClient;

    @Resource
    WmsOtherConfig wmsOtherConfig;

    @Override
    public Result<Boolean> createFromOPException(AdjustBizParam param) {
        if (null == param) throw ExceptionUtil.DATA_ERROR;
        if (StrUtil.isBlank(param.getBillNo())) throw ExceptionUtil.exceptionWithMessage("异常登记号必填");
        return remoteLockSupportClient.execute(() -> {
            RelatedBillParam relatedBillParam = new RelatedBillParam();
            relatedBillParam.setBillNo(param.getBillNo());
            relatedBillParam.setType(RelatedBillTypeEnum.BILL_TYPE_OP_EXCEPTION_ADJUST.getType());
            List<RelatedBillDTO> list = remoteRelatedBillClient.getList(relatedBillParam);
            if (CollectionUtil.isNotEmpty(list)) return Result.success(true);
            return create(param);
        }, ListUtil.toList(param.getBillNo()));
    }


    @Override
    public Result<Boolean> createV2(AdjustBizParam param) {
        if (StrUtil.isNotBlank(param.getBillNo())) {
            return remoteLockSupportClient.execute(() -> {
                RelatedBillParam relatedBillParam = new RelatedBillParam();
                relatedBillParam.setBillNo(param.getBillNo());
                relatedBillParam.setType(RelatedBillTypeEnum.BILL_TYPE_OP_EXCEPTION_ADJUST.getType());
                List<RelatedBillDTO> list = remoteRelatedBillClient.getList(relatedBillParam);
                if (CollectionUtil.isNotEmpty(list))
                    throw ExceptionUtil.exceptionWithMessage("该异常登记已经存在对应的调整单");
                return create(param);
            }, ListUtil.toList(param.getBillNo()));
        }

        return create(param);
    }

    @Override
    public Result<Boolean> create(AdjustBizParam param) {


        AdjustParam bizParam = ConverterUtil.convert(param, AdjustParam.class);
        if (ObjectUtils.isEmpty(bizParam) || ObjectUtils.isEmpty(bizParam.getDetailList())) {
            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<AdjustDetailDTO> adjustDetailList =
                ConverterUtil.convertList(param.getDetailList(), AdjustDetailDTO.class);
        bizParam.setDetailList(adjustDetailList);
        for (AdjustDetailDTO adjustDetail : bizParam.getDetailList()) {
            adjustDetail.setWarehouseCode(param.getWarehouseCode());
            adjustDetail.setCargoCode(param.getCargoCode());
        }
        List<String> skuLotNoList =
                adjustDetailList.stream().flatMap(a -> Stream.of(a.getSkuLotNo())).collect(Collectors.toList());
        List<String> cargoCodeList =
                adjustDetailList.stream().flatMap(a -> Stream.of(a.getCargoCode())).collect(Collectors.toList());
        List<String> skuCodeList =
                adjustDetailList.stream().flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toList());
        List<String> locationCodeList =
                adjustDetailList.stream().flatMap(a -> Stream.of(a.getLocationCode())).collect(Collectors.toList());
        // 批次查询
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(param.getCargoCode());
        skuLotParam.setSkuCodeList(skuCodeList);
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> skuLotList = remoteSkuLotClient.getList(skuLotParam);

        // 货主一致性校验
        if (skuLotList.stream().map(SkuLotDTO::getCargoCode).distinct().count() > 1) {
            throw new BaseException(BaseBizEnum.TIP, "暂不支持同时调整多货主库存");
        }
        if (!skuLotList.get(0).getCargoCode().equals(param.getCargoCode())) {
            throw new BaseException(BaseBizEnum.TIP, "该货主无此商品");
        }

        List<String> skuQualityList =
                skuLotList.stream().flatMap(a -> Stream.of(a.getSkuQuality())).collect(Collectors.toList());
        // 查询三级库存
        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setCargoCodeList(cargoCodeList);
        stockLocationParam.setSkuCodeList(skuCodeList);
        stockLocationParam.setSkuLotNoList(skuLotNoList);
        stockLocationParam.setLocationCodeList(locationCodeList);
        stockLocationParam.setSkuQualityList(skuQualityList);
        List<StockLocationDTO> stockLocationList = remoteStockLocationClient.getList(stockLocationParam);

        bizParam.setStatus(AdjustStatusEnum.CREATED.getStatus());
        // bizParam.setCode(UidUtils.getCacheSerialNo("AD-"));
        bizParam.setCode(iRemoteSeqRuleClient.findSequence(SeqEnum.ADJUST_CODE_000001));
        bizParam.setDetailList(adjustDetailList);
        List<String> errorInfoList = new ArrayList<>();

        List<String> allowMoveZoneTypeList = new ArrayList<>();
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PICK.getType());
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_STORE.getType());

        for (AdjustDetailDTO adjustDetail : bizParam.getDetailList()) {
            // 处理库位编码【避免大小写问题】
            if (StrUtil.isNotBlank(adjustDetail.getLocationCode())) {
                LocationParam checkLocationParam = new LocationParam();
                checkLocationParam.setCode(adjustDetail.getLocationCode());
                LocationDTO locationDTO = remoteLocationClient.get(checkLocationParam);
                if (null != locationDTO) {
                    adjustDetail.setLocationCode(locationDTO.getCode());
                }
            }
            SkuLotDTO skuLot = skuLotList.stream().filter(a -> a.getSkuCode().equals(a.getSkuCode()))
                    .filter(a -> a.getCode().equals(adjustDetail.getSkuLotNo())).findAny().orElse(null);
            if (ObjectUtils.isEmpty(skuLot)) {
                errorInfoList
                        .add(String.format("商品：%s 批次：%s不存在", adjustDetail.getSkuCode(), adjustDetail.getSkuLotNo()));
            }
            StockLocationDTO stockLocation =
                    stockLocationList.stream().filter(a -> a.getCargoCode().equals(adjustDetail.getCargoCode()))
                            .filter(a -> a.getSkuCode().equals(adjustDetail.getSkuCode()))
                            .filter(a -> a.getSkuLotNo().equals(adjustDetail.getSkuLotNo()))
                            .filter(a -> a.getSkuQuality().equals(skuLot.getSkuQuality()))
                            .filter(a -> a.getLocationCode().equals(adjustDetail.getLocationCode())).findAny().orElse(null);
            //TODO ADD 2024-06-07 无库存，允许调增
            if (AdjustTypeEnum.SUBTRACT.getStatus().equals(param.getType())) {
                if (ObjectUtils.isEmpty(stockLocation)) {
                    errorInfoList.add(String.format("商品：%s 批次：%s 库位：%s 库存信息不存在", adjustDetail.getSkuCode(),
                            adjustDetail.getSkuLotNo(), adjustDetail.getLocationCode()));
                }
            }

            if (AdjustTypeEnum.SUBTRACT.getStatus().equals(param.getType())
                    && adjustDetail.getAdjustQty().compareTo(BigDecimal.ZERO) > 0) {
                if (!ObjectUtils.isEmpty(stockLocation)
                        && stockLocation.getAvailableQty().compareTo(adjustDetail.getAdjustQty().abs()) < 0) {
                    errorInfoList.add(String.format("商品：%s 批次：%s 库位：%s 库存不足", adjustDetail.getSkuCode(),
                            adjustDetail.getSkuLotNo(), adjustDetail.getLocationCode()));
                }
            }
            if (!ObjectUtils.isEmpty(stockLocation) && !allowMoveZoneTypeList.contains(stockLocation.getZoneType())) {
                errorInfoList.add(String.format("商品：%s 批次：%s 库位：%s 非拣选存储区不允许调整", adjustDetail.getSkuCode(),
                        adjustDetail.getSkuLotNo(), adjustDetail.getLocationCode()));
            }
            adjustDetail.setAvailableQty(
                    ObjectUtils.isEmpty(stockLocation) ? BigDecimal.ZERO : stockLocation.getAvailableQty());
            adjustDetail.setTargetQty(adjustDetail.getAvailableQty().add(adjustDetail.getAdjustQty()));
            if (AdjustTypeEnum.SUBTRACT.getStatus().equalsIgnoreCase(param.getType())) {
                adjustDetail.setTargetQty(adjustDetail.getAvailableQty().subtract(adjustDetail.getAdjustQty()));
            }
            adjustDetail.setAdjustCode(bizParam.getCode());
            adjustDetail.setStatus(AdjustStatusEnum.CREATED.getStatus());
        }
        if (!CollectionUtils.isEmpty(errorInfoList)) {
            throw new BaseException(WmsAdjustBizEnum.ADJUST_BIZ_CREATE_ERROR, String.join("\n", errorInfoList));
        }
        //调整字节信息校验
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList()) && defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList().contains(CurrentRouteHolder.getWarehouseCode())) {
            checkSpecial(bizParam);
        }
        // 异常登记场景校验
        opExceptionCheck(param);

        Boolean result = remoteAdjustClient.save(bizParam);
        // 添加日志
        AdjustLogDTO logDTO = new AdjustLogDTO();
        logDTO.setOpContent("创建调整单:" + bizParam.getCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setAdjustCode(bizParam.getCode());
        logDTO.setCargoCode(bizParam.getCargoCode());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_INSERT.getType());
        remoteAdjustLogClient.save(logDTO);
        return Result.success(result);
    }

    private void opExceptionCheck(AdjustBizParam param) {
        if (AdjustBusinessTypeEnum.OP_EXCEPTION.getCode().equalsIgnoreCase(param.getBusinessType()) && StrUtil.isNotBlank(param.getBillNo())) {
            OpExceptionParam opExceptionParam = new OpExceptionParam();
            opExceptionParam.setAbnormalOrderNo(param.getBillNo());
            OpExceptionDTO opExceptionDTO = remoteOpExceptionClient.get(opExceptionParam);
            if (null == opExceptionDTO) {
                throw ExceptionUtil.exceptionWithMessage("异常登记不存在");
            }
            OpExceptionDetailParam opExceptionDetailParam = new OpExceptionDetailParam();
            opExceptionDetailParam.setAbnormalOrderNo(param.getBillNo());
            List<OpExceptionDetailDTO> list = remoteOpExceptionDetailClient.getList(opExceptionDetailParam);
            if (CollectionUtil.isEmpty(list)) {
                throw ExceptionUtil.exceptionWithMessage("异常登记明细不存在");
            }
            BigDecimal opExceptionQty = list.stream().map(OpExceptionDetailDTO::getExpSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal adjustQty = param.getDetailList().stream().map(AdjustDetailBizDTO::getAdjustQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (adjustQty.compareTo(opExceptionQty) != 0) {
                throw ExceptionUtil.exceptionWithMessage("提交的调整总数与异常登记数量不符，请检查SKU和对应的调整数量后重新提交");
            }

        }

    }

    /**
     * @param bizParam
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2023/2/15 9:44
     */
    private void checkSpecial(AdjustParam bizParam) {
        //业务场景
        if (StringUtils.isEmpty(bizParam.getBusinessType())) {
            throw new BaseException(BaseBizEnum.TIP, "当前仓库,业务场景不能为空");
        }
        bizParam.getDetailList().forEach(it -> {
            if (StringUtils.isEmpty(it.getReason())) {
                throw new BaseException(BaseBizEnum.TIP, "当前仓库,明细盘点原因不能为空");
            }
            if (StringUtils.isEmpty(it.getRemark())) {
                throw new BaseException(BaseBizEnum.TIP, "当前仓库,明细备注不能为空");
            }
            if (StringUtils.isEmpty(it.getRp())) {
                throw new BaseException(BaseBizEnum.TIP, "当前仓库,明细责任方不能为空");
            }
            if (!StringUtils.isEmpty(it.getRemark()) && it.getRemark().length() > 200) {
                throw new BaseException(BaseBizEnum.TIP, "明细备注不能超过200个字符");
            }
        });

    }

    @Override
    public Result<Boolean> modify(AdjustBizParam param) {
        AdjustParam bizParam = ConverterUtil.convert(param, AdjustParam.class);
        if (ObjectUtils.isEmpty(bizParam) || ObjectUtils.isEmpty(bizParam.getDetailList())
                || StringUtils.isEmpty(param.getCode())) {
            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        AdjustParam searchParam = new AdjustParam();
        searchParam.setCode(param.getCode());
        AdjustDTO adjustDTO = remoteAdjustClient.get(searchParam);
        if (adjustDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "调整单不存在");
        }
        List<AdjustDetailDTO> adjustDetailList =
                ConverterUtil.convertList(param.getDetailList(), AdjustDetailDTO.class);
        bizParam.setDetailList(adjustDetailList);
        for (AdjustDetailDTO adjustDetail : bizParam.getDetailList()) {
            adjustDetail.setWarehouseCode(param.getWarehouseCode());
            adjustDetail.setCargoCode(param.getCargoCode());
            adjustDetail.setStatus(adjustDTO.getStatus());
        }
        List<String> skuLotNoList =
                adjustDetailList.stream().flatMap(a -> Stream.of(a.getSkuLotNo())).collect(Collectors.toList());
        List<String> cargoCodeList =
                adjustDetailList.stream().flatMap(a -> Stream.of(a.getCargoCode())).collect(Collectors.toList());
        List<String> skuCodeList =
                adjustDetailList.stream().flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toList());
        List<String> locationCodeList =
                adjustDetailList.stream().flatMap(a -> Stream.of(a.getLocationCode())).collect(Collectors.toList());
        // 批次查询
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(param.getCargoCode());
        skuLotParam.setSkuCodeList(skuCodeList);
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> skuLotList = remoteSkuLotClient.getList(skuLotParam);

        // 货主一致性校验
        if (skuLotList.stream().map(SkuLotDTO::getCargoCode).distinct().count() > 1) {
            throw new BaseException(BaseBizEnum.TIP, "暂不支持同时调整多货主库存");
        }
        if (!skuLotList.get(0).getCargoCode().equals(param.getCargoCode())) {
            throw new BaseException(BaseBizEnum.TIP, "该货主无此商品");
        }
        //异常登记调整 修改返回异常登记关联单号
        if (AdjustBusinessTypeEnum.OP_EXCEPTION.getCode().equalsIgnoreCase(param.getBusinessType()) && StrUtil.isBlank(param.getBillNo())) {
            RelatedBillParam relatedBillParam = new RelatedBillParam();
            relatedBillParam.setRelatedNo(adjustDTO.getCode());
            relatedBillParam.setType(RelatedBillTypeEnum.BILL_TYPE_OP_EXCEPTION_ADJUST.getType());
            List<RelatedBillDTO> relatedBillDTOList = remoteRelatedBillClient.getList(relatedBillParam);
            if (CollectionUtil.isNotEmpty(relatedBillDTOList)) {
                param.setBillNo(relatedBillDTOList.get(0).getBillNo());
            }
        }
        // 异常登记校验
        opExceptionCheck(param);

        List<String> skuQualityList =
                skuLotList.stream().flatMap(a -> Stream.of(a.getSkuQuality())).collect(Collectors.toList());
        // 查询三级库存
        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setCargoCodeList(cargoCodeList);
        stockLocationParam.setSkuCodeList(skuCodeList);
        stockLocationParam.setSkuLotNoList(skuLotNoList);
        stockLocationParam.setLocationCodeList(locationCodeList);
        stockLocationParam.setSkuQualityList(skuQualityList);
        List<StockLocationDTO> stockLocationList = remoteStockLocationClient.getList(stockLocationParam);
        if (!adjustDTO.getStatus().equals(AdjustStatusEnum.IN_REJECT.getStatus())
                && !adjustDTO.getStatus().equals(AdjustStatusEnum.ERP_REJECT.getStatus())) {
            adjustDTO.setStatus(AdjustStatusEnum.CREATED.getStatus());
        }
        bizParam.setDetailList(adjustDetailList);
        List<String> errorInfoList = new ArrayList<>();
        BigDecimal zero = new BigDecimal("0.000");
        for (AdjustDetailDTO adjustDetail : bizParam.getDetailList()) {
            SkuLotDTO skuLot = skuLotList.stream().filter(a -> a.getSkuCode().equals(a.getSkuCode()))
                    .filter(a -> a.getCode().equals(adjustDetail.getSkuLotNo())).findAny().orElse(null);
            if (ObjectUtils.isEmpty(skuLot)) {
                errorInfoList
                        .add(String.format("商品：%s 批次：%s不存在", adjustDetail.getSkuCode(), adjustDetail.getSkuLotNo()));
            }
            StockLocationDTO stockLocation =
                    stockLocationList.stream().filter(a -> a.getCargoCode().equals(adjustDetail.getCargoCode()))
                            .filter(a -> a.getSkuCode().equals(adjustDetail.getSkuCode()))
                            .filter(a -> a.getSkuLotNo().equals(adjustDetail.getSkuLotNo()))
                            .filter(a -> a.getSkuQuality().equals(skuLot.getSkuQuality()))
                            .filter(a -> a.getLocationCode().equals(adjustDetail.getLocationCode())).findAny().orElse(null);
            //TODO ADD 2024-06-07 无库存，允许调增
            if (AdjustTypeEnum.SUBTRACT.getStatus().equals(param.getType())) {
                if (ObjectUtils.isEmpty(stockLocation)) {
                    errorInfoList.add(String.format("商品：%s 批次：%s 库位：%s 库存信息不存在", adjustDetail.getSkuCode(),
                            adjustDetail.getSkuLotNo(), adjustDetail.getLocationCode()));
                }
            }

            if (AdjustTypeEnum.SUBTRACT.getStatus().equals(param.getType())
                    && adjustDetail.getAdjustQty().compareTo(zero) > 0) {
                if (!ObjectUtils.isEmpty(stockLocation)
                        && stockLocation.getAvailableQty().compareTo(adjustDetail.getAdjustQty().abs()) < 0) {
                    errorInfoList.add(String.format("商品：%s 批次：%s 库位：%s 库存不足", adjustDetail.getSkuCode(),
                            adjustDetail.getSkuLotNo(), adjustDetail.getLocationCode()));
                }
            }
            adjustDetail.setAvailableQty(stockLocation == null ? BigDecimal.ZERO : stockLocation.getAvailableQty());
            adjustDetail.setAdjustCode(bizParam.getCode());
            // 仓内驳回 上游驳回 再次编辑 状态不变
            if (!adjustDetail.getStatus().equals(AdjustStatusEnum.IN_REJECT.getStatus())
                    && !adjustDetail.getStatus().equals(AdjustStatusEnum.ERP_REJECT.getStatus())) {
                adjustDetail.setStatus(AdjustStatusEnum.CREATED.getStatus());
            }
            adjustDetail.setTargetQty(adjustDetail.getAvailableQty().add(adjustDetail.getAdjustQty()));
            if (AdjustTypeEnum.SUBTRACT.getStatus().equalsIgnoreCase(adjustDTO.getType())) {
                adjustDetail.setTargetQty(adjustDetail.getAvailableQty().subtract(adjustDetail.getAdjustQty()));
            }
        }
        if (!CollectionUtils.isEmpty(errorInfoList)) {
            throw new BaseException(WmsAdjustBizEnum.ADJUST_BIZ_CREATE_ERROR, String.join("\n", errorInfoList));
        }
        //调整字节信息校验
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList()) && defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList().contains(CurrentRouteHolder.getWarehouseCode())) {
            checkSpecial(bizParam);
        }
        Boolean result = remoteAdjustClient.modify(bizParam);
        // 添加日志
        AdjustLogDTO logDTO = new AdjustLogDTO();
        logDTO.setOpContent("编辑调整单:" + bizParam.getCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setAdjustCode(bizParam.getCode());
        logDTO.setCargoCode(bizParam.getCargoCode());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
        remoteAdjustLogClient.save(logDTO);
        return Result.success(result);
    }

    @Override
    public Result<AdjustBizDTO> getDetail(AdjustBizParam param) {
        AdjustParam bizParam = ConverterUtil.convert(param, AdjustParam.class);
        AdjustDTO result = remoteAdjustClient.getDetail(bizParam);
        AdjustBizDTO adjustBiz = ConverterUtil.convert(result, AdjustBizDTO.class);
        if (!ObjectUtils.isEmpty(adjustBiz)) {
            WarehouseDTO warehouse = remoteWarehouseClient.queryByCode(adjustBiz.getWarehouseCode());
            CargoOwnerDTO cargoOwner = remoteCargoOwnerClient.queryByCode(adjustBiz.getCargoCode());
            adjustBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
            adjustBiz.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
            adjustBiz.setDetailList(convertList(result.getDetailList(), AdjustDetailBizDTO.class));
            if (!CollectionUtils.isEmpty(adjustBiz.getDetailList())) {
                // 批次属性增加展示
                SkuLotParam skuLotParam = new SkuLotParam();
                skuLotParam.setCodeList(adjustBiz.getDetailList().stream().map(AdjustDetailBizDTO::getSkuLotNo)
                        .distinct().collect(Collectors.toList()));
                List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
                Map<String, SkuLotDTO> listMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(skuLotDTOList)) {
                    listMap = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity()));
                }
                for (AdjustDetailBizDTO shelfDetailBiz : adjustBiz.getDetailList()) {
                    SkuLotDTO skuLotDTO = listMap.getOrDefault(shelfDetailBiz.getSkuLotNo(), null);
                    if (skuLotDTO != null) {
                        shelfDetailBiz.setExternalSkuLotNo(skuLotDTO.getExternalSkuLotNo());
                        shelfDetailBiz.setProductionNo(skuLotDTO.getProductionNo());

                        shelfDetailBiz.setManufDate(skuLotDTO.getManufDate());
                        shelfDetailBiz.setManufDateFormat(
                                ConverterUtil.convertVoTime(skuLotDTO.getManufDate(), skuLotDTO.getManufDateFormat()));

                        shelfDetailBiz.setExpireDate(skuLotDTO.getExpireDate());
                        shelfDetailBiz.setExpireDateFormat(
                                ConverterUtil.convertVoTime(skuLotDTO.getExpireDate(), skuLotDTO.getExpireDateFormat()));

                        shelfDetailBiz.setWithdrawDate(skuLotDTO.getWithdrawDate());
                        shelfDetailBiz.setWithdrawDateFormat(ConverterUtil.convertVoTime(skuLotDTO.getWithdrawDate(),
                                skuLotDTO.getWithdrawDateFormat()));

                        shelfDetailBiz.setReceiveDate(skuLotDTO.getReceiveDate());
                        shelfDetailBiz.setReceiveDateFormat(
                                ConverterUtil.convertVoTime(skuLotDTO.getReceiveDate(), skuLotDTO.getReceiveDateFormat()));

                        shelfDetailBiz.setSkuQuality(skuLotDTO.getSkuQuality());
                        shelfDetailBiz
                                .setSkuQualityName(SkuQualityEnum.getEnum(skuLotDTO.getSkuQuality()).getMessage());
                        shelfDetailBiz.setInventoryType(skuLotDTO.getInventoryType());
                        shelfDetailBiz.setValidityCode(skuLotDTO.getValidityCode());
                        shelfDetailBiz.setPalletCode(skuLotDTO.getPalletCode());
                        shelfDetailBiz.setBoxCode(skuLotDTO.getBoxCode());
                    }
                    shelfDetailBiz.setWarehouseName(warehouse.getName());
                    shelfDetailBiz.setCargoName(cargoOwner.getName());
                }
            }
        }
        return Result.success(adjustBiz);
    }

    @Override
    public Result<Page<AdjustBizDTO>> getPage(AdjustBizParam param) {
        if (CollectionUtil.isNotEmpty(param.getTagList())) {
            param.setTag(AdjustTagEnum.queryParamListToInteger(param.getTagList()));
        }

        // 格式化参数
        formatParam(param);

        AdjustParam bizParam = ConverterUtil.convert(param, AdjustParam.class);
        Page<AdjustDTO> page = remoteAdjustClient.getPage(bizParam);
        List<AdjustDTO> resultList = page.getRecords();
        List<AdjustBizDTO> resultBizList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultList)) {
            List<String> warehouseCodeList = page.getRecords().stream().flatMap(a -> Stream.of(a.getWarehouseCode()))
                    .distinct().collect(Collectors.toList());
            WarehouseParam warehouseParam = new WarehouseParam();
            warehouseParam.setCodeList(warehouseCodeList);
            List<WarehouseDTO> warehouseList = remoteWarehouseClient.queryList(warehouseParam);

            List<String> cargoCodeList = page.getRecords().stream().flatMap(a -> Stream.of(a.getCargoCode())).distinct()
                    .collect(Collectors.toList());
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(cargoCodeList);
            List<CargoOwnerDTO> cargoOwnerList = remoteCargoOwnerClient.queryList(cargoOwnerParam);

            CargoConfigParam cargoConfigParam = new CargoConfigParam();
            cargoConfigParam.setCargoCodeList(cargoCodeList);
            cargoConfigParam.setPropKey(CargoConfigParamEnum.OUT_STOCK_CHECK.getCode());
            cargoConfigParam.setStatus(CargoConfigStatusEnum.ENABLE.getValue());
            List<CargoConfigDTO> cargoConfigDTOList = remoteCargoConfigClient.getList(cargoConfigParam);

            resultBizList = resultList.stream().flatMap(a -> {
                WarehouseDTO warehouse =
                        warehouseList.stream().filter(b -> b.getCode().equals(a.getWarehouseCode())).findAny().orElse(null);
                CargoOwnerDTO cargoOwner =
                        cargoOwnerList.stream().filter(b -> b.getCode().equals(a.getCargoCode())).findAny().orElse(null);
                AdjustBizDTO adjustBiz = ConverterUtil.convert(a, AdjustBizDTO.class);
                if (!ObjectUtils.isEmpty(adjustBiz)) {
                    adjustBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
                    adjustBiz.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
                    adjustBiz.setDetailList(convertList(adjustBiz.getDetailList(), AdjustDetailBizDTO.class));
                }

                if (cargoConfigBiz.stockNeedUpstreamCheck(cargoConfigDTOList.stream()
                        .filter(cargoConfigDTO -> cargoConfigDTO.getCargoCode().equalsIgnoreCase(a.getCargoCode()))
                        .findAny().orElse(null))) {
                    adjustBiz.setNeedERPCheck("YES");
                } else {
                    adjustBiz.setNeedERPCheck("NO");
                }

                return Stream.of(adjustBiz);
            }).collect(Collectors.toList());
        }
        Page<AdjustBizDTO> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(resultBizList);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> complete(CodeParam param) {
        AdjustParam adjustParam = new AdjustParam();
        adjustParam.setCode(param.getCode());
        AdjustDTO adjust = remoteAdjustClient.get(adjustParam);
        if (!AdjustStatusEnum.ERP_CHECKED.getStatus().equals(adjust.getStatus())
                && !AdjustStatusEnum.IN_CHECKED.getStatus().equals(adjust.getStatus())) {
            throw new WmsBizException(WmsAdjustBizEnum.ADJUST_BIZ_STATUS_PROTECTED);
        }
        AdjustDetailParam adjustDetailParam = new AdjustDetailParam();
        adjustDetailParam.setAdjustCode(adjust.getCode());
        List<AdjustDetailDTO> detailList = remoteAdjustDetailClient.getList(adjustDetailParam);
        for (AdjustDetailDTO detail : detailList) {
            if (!AdjustStatusEnum.ERP_CHECKED.getStatus().equals(detail.getStatus())
                    && !AdjustStatusEnum.IN_CHECKED.getStatus().equals(adjust.getStatus())) {
                throw new WmsBizException(WmsAdjustBizEnum.ADJUST_BIZ_STATUS_PROTECTED);
            }
            detail.setStatus(AdjustStatusEnum.STATUS_COMPLETED.getStatus());
            if (adjust.getType().equals(AdjustTypeEnum.ADD.getStatus())) {
                detail.setTargetQty(detail.getAvailableQty().add(detail.getAdjustQty()));
            } else if (adjust.getType().equals(AdjustTypeEnum.SUBTRACT.getStatus())) {
                detail.setTargetQty(detail.getAvailableQty().subtract(detail.getAdjustQty()));
            }
        }
        adjust.setOpBy(CurrentUserHolder.getUserName());
        adjust.setCompleteDate(System.currentTimeMillis());

        AdjustCompleteBO adjustComplete = new AdjustCompleteBO();

        adjustComplete.setNeedERPCheck(cargoConfigBiz.stockNeedUpstreamCheck(adjust.getCargoCode()));
        // 此判断要置于库存校验参数判断下方
        if (adjust.getStatus().equals(AdjustStatusEnum.ERP_CHECKED.getStatus())) {
            adjustComplete.setNeedERPCheck(true);
        } else if (adjust.getStatus().equals(AdjustStatusEnum.IN_CHECKED.getStatus())) {
            adjustComplete.setNeedERPCheck(false);
        }
        // 改变状态要在判断之后
        adjust.setStatus(AdjustStatusEnum.STATUS_COMPLETED.getStatus());

        adjustComplete.setWarehouseCode(adjust.getWarehouseCode());
        adjustComplete.setAdjust(adjust);
        //        设置行号
        for (int i = 0; i < detailList.size(); i++) {
            detailList.get(i).setLineSeq(String.valueOf(i + 1));
        }
        adjustComplete.setAdjustDetailList(detailList);

        List<MessageMqDTO> messageMqDTOList = remoteAdjustClient.taoTianMessageMqDTO(adjust, MessageTypeEnum.OPERATION_ADJUST_COMPLETE_CALLBACK);
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(adjust.getCargoCode());
        if (CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO) && !AdjustTagEnum.NumToEnum(adjust.getTag()).contains(AdjustTagEnum.NO_CALLBACK)) {
            remoteMercuryClient.commitTaoTianAdjustCheckSku(adjust, detailList);
        }

        adjustComplete.setMessageMqDTOList(messageMqDTOList);
        Boolean result = adjustContextService.doComplete(adjustComplete);
        if (!result) {
            throw new WmsBizException(BaseBizEnum.DATA_ERROR);
        }

        // 盘点完成回告
        remoteAdjustClient.callback(messageMqDTOList);

        // 添加日志
        AdjustLogDTO logDTO = new AdjustLogDTO();
        logDTO.setOpContent("确认完成:" + adjust.getCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setAdjustCode(adjust.getCode());
        logDTO.setCargoCode(adjust.getCargoCode());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
        remoteAdjustLogClient.save(logDTO);
        return Result.success(true);

    }

    @Override
    public Result<Boolean> audit(CodeParam param) {
        AdjustParam adjustParam = new AdjustParam();
        adjustParam.setCode(param.getCode());
        AdjustDTO adjust = remoteAdjustClient.get(adjustParam);
        if (!AdjustStatusEnum.CREATED.getStatus().equals(adjust.getStatus())
                && !AdjustStatusEnum.AUDIT_FAILURE.getStatus().equals(adjust.getStatus())) {
            throw new WmsBizException(WmsAdjustBizEnum.ADJUST_BIZ_STATUS_PROTECTED);
        }

        AdjustDetailParam adjustDetailParam = new AdjustDetailParam();
        adjustDetailParam.setAdjustCode(adjust.getCode());
        List<AdjustDetailDTO> detailList = remoteAdjustDetailClient.getList(adjustDetailParam);

        if (!cargoConfigBiz.stockNeedUpstreamCheck(adjust.getCargoCode())) {
            return auditInner(param, adjust, detailList);
        }

        for (AdjustDetailDTO detail : detailList) {
            if (!AdjustStatusEnum.CREATED.getStatus().equals(detail.getStatus())
                    && !AdjustStatusEnum.AUDIT_FAILURE.getStatus().equals(detail.getStatus())) {
                throw new WmsBizException(WmsAdjustBizEnum.ADJUST_BIZ_STATUS_PROTECTED);
            }
            detail.setStatus(AdjustStatusEnum.STATUS_WAIT.getStatus());
        }
        adjust.setChecker(CurrentUserHolder.getUserName());
        adjust.setCheckerDate(System.currentTimeMillis());
        adjust.setStatus(AdjustStatusEnum.STATUS_WAIT.getStatus());

        AdjustParam modifyAdjustParam = ConverterUtil.convert(adjust, AdjustParam.class);
        modifyAdjustParam.setDetailList(detailList);

        // Boolean result = remoteAdjustClient.modify(modifyAdjustParam);
        // if (!result) {
        // throw new WmsBizException(BaseBizEnum.DATA_ERROR);
        // }
        // 调用erp的库存调整

        // 调减 需要锁定库存， 调整不需要。
        AdjustBO adjustBO = new AdjustBO();
        adjustBO.setAdjust(adjust);
        adjustBO.setAdjustDetailList(detailList);
        Boolean submit = adjustContextService.submitToERPForApproval(adjustBO);
        // 提交上游审核日志
        AdjustLogDTO logDTO = new AdjustLogDTO();
        logDTO.setAdjustCode(adjust.getCode());
        logDTO.setCargoCode(adjust.getCargoCode());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        logDTO.setOpContent("提交上游审核:" + adjust.getCode());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_INSERT.getType());
        remoteAdjustLogClient.save(logDTO);
        return Result.success(submit);
    }

    /**
     * 内部审核
     *
     * @param param
     * @return
     */
    public Result<Boolean> auditInner(CodeParam param, AdjustDTO adjust, List<AdjustDetailDTO> detailList) {
        if (StrUtil.isBlank(param.getRemark())) {
            throw new BaseException(BaseBizEnum.TIP, "审核说明必填");
        }
        if (StrUtil.isBlank(param.getPass())) {
            throw new BaseException(BaseBizEnum.TIP, "是否审核通过必填");
        }
        adjust.setRemark(param.getRemark());
        adjust.setCheckerDate(System.currentTimeMillis());
        adjust.setChecker(CurrentUserHolder.getUserName());
        // 添加审核日志
        AdjustLogDTO logDTO = new AdjustLogDTO();
        logDTO.setAdjustCode(adjust.getCode());
        logDTO.setCargoCode(adjust.getCargoCode());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        if (AuditEnum.PASS.getCode().equals(param.getPass())) {
            logDTO.setOpContent("仓内审核通过:" + param.getCode() + ",审核说明:" + param.getRemark());
            adjust.setStatus(AdjustStatusEnum.IN_CHECKED.getStatus());
            //是否需要上游审核
            if (cargoConfigBiz.stockNeedUpstreamCheck(adjust.getCargoCode())) {
                logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_INSERT.getType());
                remoteAdjustLogClient.save(logDTO);
                for (AdjustDetailDTO detail : detailList) {
                    if (!AdjustStatusEnum.IN_AUDIT.getStatus().equals(detail.getStatus())) {
                        throw new WmsBizException(WmsAdjustBizEnum.ADJUST_BIZ_STATUS_PROTECTED);
                    }
                    detail.setStatus(AdjustStatusEnum.ERP_AUDIT.getStatus());
                }
                adjust.setChecker(CurrentUserHolder.getUserName());
                adjust.setCheckerDate(System.currentTimeMillis());
                adjust.setStatus(AdjustStatusEnum.ERP_AUDIT.getStatus());

                AdjustParam modifyAdjustParam = ConverterUtil.convert(adjust, AdjustParam.class);
                modifyAdjustParam.setDetailList(detailList);

                // 调减 需要锁定库存， 调整不需要。
                AdjustBO adjustBO = new AdjustBO();
                adjustBO.setAdjust(adjust);
                adjustBO.setAdjustDetailList(detailList);
                Boolean submit = adjustContextService.submitToERPForApproval(adjustBO);
                // 提交上游审核日志
                AdjustLogDTO adjustLogDTO = new AdjustLogDTO();
                adjustLogDTO.setAdjustCode(adjust.getCode());
                adjustLogDTO.setCargoCode(adjust.getCargoCode());
                adjustLogDTO.setOpBy(CurrentUserHolder.getUserName());
                adjustLogDTO.setOpDate(System.currentTimeMillis());
                adjustLogDTO.setOpRemark("报文:" + JSON.toJSONString(param));
                adjustLogDTO.setOpContent("提交上游审核:" + adjust.getCode());
                adjustLogDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_INSERT.getType());
                remoteAdjustLogClient.save(adjustLogDTO);
                return Result.success();
            }
        } else {
            logDTO.setOpContent("仓内审核驳回:" + param.getCode() + ",审核说明:" + param.getRemark());
            adjust.setStatus(AdjustStatusEnum.IN_REJECT.getStatus());
        }
        detailList.forEach(adjustDetailDTO -> adjustDetailDTO.setStatus(adjust.getStatus()));

        AdjustBO adjustBO = new AdjustBO();
        adjustBO.setAdjust(adjust);
        adjustBO.setAdjustDetailList(detailList);
        Boolean approval = adjustContextService.innerApproval(adjustBO);
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_INSERT.getType());
        remoteAdjustLogClient.save(logDTO);

        return Result.success(approval);
    }

    @Override
    public Result<Boolean> cancel(CodeParam param) {
        AdjustParam adjustParam = new AdjustParam();
        adjustParam.setCode(param.getCode());
        AdjustDTO adjust = remoteAdjustClient.get(adjustParam);
        if (!AdjustStatusEnum.CREATED.getStatus().equals(adjust.getStatus())
                && !AdjustStatusEnum.IN_REJECT.getStatus().equals(adjust.getStatus())
                && !AdjustStatusEnum.ERP_REJECT.getStatus().equals(adjust.getStatus())) {
            throw new WmsBizException(WmsAdjustBizEnum.ADJUST_BIZ_STATUS_PROTECTED);
        }
        AdjustDetailParam adjustDetailParam = new AdjustDetailParam();
        adjustDetailParam.setAdjustCode(adjust.getCode());
        List<AdjustDetailDTO> detailList = remoteAdjustDetailClient.getList(adjustDetailParam);
        for (AdjustDetailDTO detail : detailList) {
            if (!AdjustStatusEnum.CREATED.getStatus().equals(detail.getStatus())
                    && !AdjustStatusEnum.IN_REJECT.getStatus().equals(detail.getStatus())
                    && !AdjustStatusEnum.ERP_REJECT.getStatus().equals(adjust.getStatus())) {
                throw new WmsBizException(WmsAdjustBizEnum.ADJUST_BIZ_STATUS_PROTECTED);
            }
            detail.setStatus(AdjustStatusEnum.STATUS_CANCELED.getStatus());
        }

        AdjustParam modifyAdjustParam = ConverterUtil.convert(adjust, AdjustParam.class);
        modifyAdjustParam.setDetailList(detailList);

        if (!cargoConfigBiz.stockNeedUpstreamCheck(adjust.getCargoCode())) {
            modifyAdjustParam.setIsErpCancel(false);
        }
        // 如果状态是上游审核驳回 则需要回传上游
        if (adjust.getStatus().equalsIgnoreCase(AdjustStatusEnum.ERP_REJECT.getStatus())) {
            modifyAdjustParam.setIsErpCancel(true);
        } else if (adjust.getStatus().equalsIgnoreCase(AdjustStatusEnum.IN_REJECT.getStatus())) {
            modifyAdjustParam.setIsErpCancel(false);
        }

        adjustContextService.releaseStockIfNeed(adjust);

        modifyAdjustParam.setStatus(AdjustStatusEnum.STATUS_CANCELED.getStatus());
        adjustContextService.cancel(modifyAdjustParam);

        // 添加日志
        AdjustLogDTO logDTO = new AdjustLogDTO();
        logDTO.setOpContent("取消调整单:" + adjust.getCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setAdjustCode(adjust.getCode());
        logDTO.setCargoCode(adjust.getCargoCode());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
        remoteAdjustLogClient.save(logDTO);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> commitAudit(CodeParam param) {
        AdjustParam searchParam = new AdjustParam();
        searchParam.setCode(param.getCode());
        AdjustDTO adjustDTO = remoteAdjustClient.getDetail(searchParam);
        adjustDTO.getDetailList().forEach(it -> {
            it.setStatus(AdjustStatusEnum.IN_AUDIT.getStatus());
        });
        adjustDTO.setStatus(AdjustStatusEnum.IN_AUDIT.getStatus());
        // 添加日志
        AdjustLogDTO logDTO = new AdjustLogDTO();
        logDTO.setOpContent("调整单提交审核:" + adjustDTO.getCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setAdjustCode(adjustDTO.getCode());
        logDTO.setCargoCode(adjustDTO.getCargoCode());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
        remoteAdjustLogClient.save(logDTO);

        return Result.success(remoteAdjustClient.modify(ConverterUtil.convert(adjustDTO, AdjustParam.class)));
    }

    @Override
    public Result<Boolean> innerAudit(CodeParam param) {
        AdjustParam adjustParam = new AdjustParam();
        adjustParam.setCode(param.getCode());
        AdjustDTO adjust = remoteAdjustClient.get(adjustParam);
        if (!AdjustStatusEnum.IN_AUDIT.getStatus().equals(adjust.getStatus())) {
            throw new WmsBizException(WmsAdjustBizEnum.ADJUST_BIZ_STATUS_PROTECTED);
        }

        AdjustDetailParam adjustDetailParam = new AdjustDetailParam();
        adjustDetailParam.setAdjustCode(adjust.getCode());
        List<AdjustDetailDTO> detailList = remoteAdjustDetailClient.getList(adjustDetailParam);

        return auditInner(param, adjust, detailList);
    }

    @Override
    public Result<AdjustCreateSkuLotBizDTO> querySkuInfoByAdjust(AdjustCreateSkuLotParam adjustCreateSkuLotParam) {
        log.info("querySkuInfoByAdjust:{}", JSONUtil.toJsonStr(adjustCreateSkuLotParam));
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setUpcCode(adjustCreateSkuLotParam.getUpcCode());
        skuUpcParam.setCargoCode(adjustCreateSkuLotParam.getCargoCode());
        SkuUpcDTO skuUpcDTO = remoteSkuClient.getSkuUpc(skuUpcParam);
        if (skuUpcDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "商品UPC未找到有效商品,请核查输入商品条码");
        }
        if (!Objects.equals(skuUpcDTO.getStatus(), SkuStatusEnum.STATUS_ENABLED.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "商品UPC未找到有效商品,请核查输入商品条码");
        }
        //拉取sku
        SkuDTO skuDTO = remoteSkuClient.querySkuByCode(adjustCreateSkuLotParam.getCargoCode(), skuUpcDTO.getSkuCode());
        if (skuDTO == null || !Objects.equals(skuDTO.getStatus(), SkuStatusEnum.STATUS_ENABLED.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "商品信息异常,请核查商品");
        }
        AdjustCreateSkuLotBizDTO adjustCreateSkuLotBizDTO = new AdjustCreateSkuLotBizDTO();
        adjustCreateSkuLotBizDTO.setCargoCode(adjustCreateSkuLotParam.getCargoCode());
        adjustCreateSkuLotBizDTO.setSkuCode(skuDTO.getCode());
        adjustCreateSkuLotBizDTO.setUpcCode(adjustCreateSkuLotParam.getUpcCode());
        adjustCreateSkuLotBizDTO.setSkuName(skuDTO.getName());
        adjustCreateSkuLotBizDTO.setLifeCycle(skuDTO.getLifeCycle());
        adjustCreateSkuLotBizDTO.setWithdrawCycle(skuDTO.getWithdrawCycle());
        adjustCreateSkuLotBizDTO.setRejectCycle(skuDTO.getRejectCycle());
        adjustCreateSkuLotBizDTO.setIsLifeMgt(Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode()));
        //设置Sku的商品批次属性规则
        LotRuleDTO lotRuleDTO = remoteLotRuleClient.querySkuLotRule(skuDTO.getLotRuleCode());
        if (StringUtils.isEmpty(lotRuleDTO) || CollectionUtils.isEmpty(lotRuleDTO.getRuleDetailList())
                || !Objects.equals(lotRuleDTO.getStatus(), LotRuleStatusEnum.ENABLE.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "请维护,商品批次默认规则");
        }
        //组装批次属性规则
        adjustCreateSkuLotBizDTO.setPropList(skuLotBiz.buildPopAdjustSku(lotRuleDTO.getRuleDetailList(), adjustCreateSkuLotBizDTO.getIsLifeMgt(), true, ""));
        //CW货主
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(skuDTO.getCargoCode());
        adjustCreateSkuLotBizDTO.setCwCargo(false);
        if (cargoOwnerDTO != null && CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.CW_CARGO)) {
            adjustCreateSkuLotBizDTO.setCwCargo(true);
        }
        return Result.success(adjustCreateSkuLotBizDTO);
    }


    @Override
    public Result<String> createSkuLotByAdjust(AdjustCreateSkuLotParam adjustCreateSkuLotParam) {
        log.info("createSkuLotByAdjust:{}", JSONUtil.toJsonStr(adjustCreateSkuLotParam));
        if (StringUtils.isEmpty(adjustCreateSkuLotParam.getSkuQuality())) {
            throw new BaseException(BaseBizEnum.TIP, "正次品属性不能为空");
        }
        if (StringUtils.isEmpty(adjustCreateSkuLotParam.getInventoryType())) {
            throw new BaseException(BaseBizEnum.TIP, "残次等级不能为空");
        }
        if (StringUtils.isEmpty(adjustCreateSkuLotParam.getLocationCode())) {
            throw new BaseException(BaseBizEnum.TIP, "库位编码不能为空");
        }
        //库位编码：
        //只能填写当前仓库下存在的【启用】状态的，【库位类型】为【拣选位/存储位】的库位编码
        //库位上的【商品属性】 和当前弹窗内填写的【商品属性】需一致
        LocationParam locationParam = new LocationParam();
        locationParam.setCode(adjustCreateSkuLotParam.getLocationCode());
        LocationDTO locationDTO = remoteLocationClient.get(locationParam);
        if (locationDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "库位不存在,请核查");
        }
        if (!Objects.equals(locationDTO.getStatus(), LocationStatusEnum.STATUS_ENABLED.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "库位非有效状态,请核查");
        }
        if (!Arrays.asList(LocationTypeEnum.LOCATION_TYPE_STORE.getType(), LocationTypeEnum.LOCATION_TYPE_PICK.getType()).contains(locationDTO.getType())) {
            throw new BaseException(BaseBizEnum.TIP, "【库位类型】请选择【拣选位/存储位】的库位编码,请核查");
        }
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCode(locationDTO.getZoneCode());
        ZoneDTO zoneDTO = remoteZoneClient.get(zoneParam);
        if (zoneDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "库区不存在,请核查");
        }
        if (!Objects.equals(zoneDTO.getStatus(), ZoneStatusEnum.STATUS_ENABLED.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "库区非有效状态,请核查");
        }
        if (!Objects.equals(adjustCreateSkuLotParam.getSkuQuality(), zoneDTO.getSkuQuality())) {
            throw new BaseException(BaseBizEnum.TIP, "库区的商品属性和提交的不一致,请核查");
        }
        //入库关联号： 最长60个字符，支持英文+数字（忽略大小写）
        if (!StringUtils.isEmpty(adjustCreateSkuLotParam.getExternalLinkBillNo())) {
            boolean matches = Pattern.matches("^[a-zA-Z0-9]{1,60}$", adjustCreateSkuLotParam.getExternalLinkBillNo());
            if (!matches) {
                throw new BaseException(BaseBizEnum.TIP, "入库关联号:最长60个字符,支持英文+数字,请核查");
            }
        }
        //生产批次号： 最长60个字符
        if (!StringUtils.isEmpty(adjustCreateSkuLotParam.getProductionNo())
                && adjustCreateSkuLotParam.getProductionNo().length() > 60) {
            throw new BaseException(BaseBizEnum.TIP, "生产批次号:最长60个字符,请核查");
        }
        //暗码： 英文大小写、数字、中划线； 长度30
        if (!StringUtils.isEmpty(adjustCreateSkuLotParam.getValidityCode())) {
            boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", adjustCreateSkuLotParam.getValidityCode());
            if (!matches) {
                throw new BaseException(BaseBizEnum.TIP, "暗码:英文大小写、数字、中划线,长度30,请核查");
            }
        }
        //托盘： 英文大小写、数字、中划线； 长度30
        if (!StringUtils.isEmpty(adjustCreateSkuLotParam.getPalletCode())) {
            boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", adjustCreateSkuLotParam.getPalletCode());
            if (!matches) {
                throw new BaseException(BaseBizEnum.TIP, "托盘:英文大小写、数字、中划线,长度30,请核查");
            }
        }
        //箱码： 英文大小写、数字、中划线； 长度30
        if (!StringUtils.isEmpty(adjustCreateSkuLotParam.getBoxCode())) {
            boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", adjustCreateSkuLotParam.getBoxCode());
            if (!matches) {
                throw new BaseException(BaseBizEnum.TIP, "箱码:英文大小写、数字、中划线,长度30,请核查");
            }
        }

        //拉取sku
        SkuDTO skuDTO = remoteSkuClient.querySkuByCode(adjustCreateSkuLotParam.getCargoCode(), adjustCreateSkuLotParam.getSkuCode());
        if (skuDTO == null || !Objects.equals(skuDTO.getStatus(), SkuStatusEnum.STATUS_ENABLED.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "商品信息异常,请核查商品");
        }
        //校验效期码
        if(!StringUtils.isEmpty(adjustCreateSkuLotParam.getValidityCode())&& !wmsOtherConfig.isValidValidityCode(adjustCreateSkuLotParam.getValidityCode())){
            throw new BaseException(BaseBizEnum.TIP, wmsOtherConfig.getValidValidityRuleMsg());
        }
        //不校验禁收
        SkuLotDTO skuLotDTO = skuLotBiz.findAndFormatSkuLot(ConverterUtil.convert(adjustCreateSkuLotParam, SkuLotCheckAndFormatParam.class), skuDTO, false);
        if (StringUtils.isEmpty(skuLotDTO.getId())) {
            Boolean saveBatch = remoteSkuLotClient.saveBatch(Arrays.asList(skuLotDTO));
            if (!saveBatch) {
                throw new BaseException(BaseBizEnum.TIP, "生成货品批次异常");
            }
        }
        return Result.success(skuLotDTO.getCode());
    }

    private void formatParam(AdjustBizParam param) {

        if (!StringUtils.isEmpty(param.getCode())) {
            if (!CollectionUtils.isEmpty(param.getCodeList())) {
                param.getCodeList().add(param.getCode());
            } else {
                param.setCodeList(new ArrayList<>());
                param.getCodeList().add(param.getCode());
            }
        }
        if (!StringUtils.isEmpty(param.getCargoCode())) {
            if (!CollectionUtils.isEmpty(param.getCargoCodeList())) {
                param.getCargoCodeList().add(param.getCargoCode());
            } else {
                param.setCargoCodeList(new ArrayList<>());
                param.getCargoCodeList().add(param.getCargoCode());
            }
        }
        if (!StringUtils.isEmpty(param.getStatus())) {
            if (!CollectionUtils.isEmpty(param.getStatusList())) {
                param.getStatusList().add(param.getStatus());
            } else {
                param.setStatusList(new ArrayList<>());
                param.getStatusList().add(param.getStatus());
            }
        }

        if (!StringUtils.isEmpty(param.getSkuCode())) {
            if (!CollectionUtils.isEmpty(param.getSkuCodeList())) {
                param.getSkuCodeList().add(param.getSkuCode());
            } else {
                param.setSkuCodeList(new ArrayList<>());
                param.getSkuCodeList().add(param.getSkuCode());
            }
        }
        if (!StringUtils.isEmpty(param.getZoneCode())) {
            if (!CollectionUtils.isEmpty(param.getZoneCodeList())) {
                param.getZoneCodeList().add(param.getZoneCode());
            } else {
                param.setZoneCodeList(new ArrayList<>());
                param.getZoneCodeList().add(param.getZoneCode());
            }
        }
        if (!StringUtils.isEmpty(param.getLocationCode())) {
            param.setZoneCode(null);
            param.setZoneCodeList(new ArrayList<>());
            if (!CollectionUtils.isEmpty(param.getLocationCodeList())) {
                param.getLocationCodeList().add(param.getLocationCode());
            } else {
                param.setLocationCodeList(new ArrayList<>());
                param.getLocationCodeList().add(param.getLocationCode());
            }
        }
        if (!StringUtils.isEmpty(param.getSkuUpcCode()) || !CollectionUtils.isEmpty(param.getSkuUpcCodeList())) {
            if (!CollectionUtils.isEmpty(param.getSkuUpcCodeList())) {
                param.getSkuUpcCodeList().add(param.getSkuUpcCode());
            } else {
                param.setSkuUpcCodeList(new ArrayList<>());
                param.getSkuUpcCodeList().add(param.getSkuUpcCode());
            }
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCodeList(param.getSkuUpcCodeList());
            List<SkuUpcDTO> upcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            if (!CollectionUtils.isEmpty(upcList)) {
                param.setSkuCodeList(
                        upcList.stream().flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toList()));
            } else {
                param.setSkuCodeList(new ArrayList<>());
                param.getSkuCodeList().add("");
            }
        }
        // 库库位
        if (!ObjectUtils.isEmpty(param.getZoneCode()) || !CollectionUtils.isEmpty(param.getZoneCodeList())) {
            LocationParam locationParam = new LocationParam();
            locationParam.setZoneCode(param.getZoneCode());
            locationParam.setZoneCodeList(param.getZoneCodeList());
            List<LocationDTO> locationList = remoteLocationClient.getList(locationParam);
            if (!CollectionUtils.isEmpty(locationList)) {
                List<String> codeList =
                        locationList.stream().flatMap(a -> Stream.of(a.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(param.getLocationCodeList())) {
                    param.setLocationCodeList(codeList);
                } else {
                    param.getLocationCodeList().addAll(codeList);
                }
            } else {
                param.setLocationCodeList(new ArrayList<>());
                param.getLocationCodeList().add("");
            }
        }
        // 库位
        if (!ObjectUtils.isEmpty(param.getLocationCode()) || !CollectionUtils.isEmpty(param.getLocationCodeList())) {
            LocationParam locationParam = new LocationParam();
            locationParam.setCode(param.getLocationCode());
            locationParam.setCodeList(param.getLocationCodeList());
            List<LocationDTO> locationList = remoteLocationClient.getList(locationParam);
            if (!CollectionUtils.isEmpty(locationList)) {
                List<String> codeList =
                        locationList.stream().flatMap(a -> Stream.of(a.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(param.getLocationCodeList())) {
                    param.setLocationCodeList(codeList);
                } else {
                    param.getLocationCodeList().addAll(codeList);
                }
            } else {
                param.setLocationCodeList(new ArrayList<>());
                param.getLocationCodeList().add("");
            }
        }

        if (!ObjectUtils.isEmpty(param.getSkuLotNo()) || !CollectionUtils.isEmpty(param.getSkuCodeList())
                || !CollectionUtils.isEmpty(param.getLocationCodeList())) {
            AdjustDetailParam adjustDetailParam = new AdjustDetailParam();
            adjustDetailParam.setSkuLotNo(param.getSkuLotNo());
            adjustDetailParam.setSkuCodeList(param.getSkuCodeList());
            adjustDetailParam.setLocationCodeList(param.getLocationCodeList());
            List<AdjustDetailDTO> adjustDetailList = remoteAdjustDetailClient.getList(adjustDetailParam);
            if (!CollectionUtils.isEmpty(adjustDetailList)) {
                List<String> codeList =
                        adjustDetailList.stream().flatMap(a -> Stream.of(a.getAdjustCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(param.getCodeList())) {
                    param.setCodeList(codeList);
                } else {
                    param.getCodeList().addAll(codeList);
                }
            } else {
                param.setCodeList(new ArrayList<>());
                param.getCodeList().add("");
            }
        }
    }

    @Override
    public Result<List<IdNameVO>> queryTag(AdjustParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        AdjustDTO adjustDTO = remoteAdjustClient.get(param);
        if (adjustDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "调整单不存在");
        }
        Set<AdjustTagEnum> adjustTagEnums = AdjustTagEnum.NumToEnum(adjustDTO.getTag());
        return Result.success(IdNameVO.build(new ArrayList<>(adjustTagEnums), "code", "desc"));
    }

    @Override
    public Result<Boolean> modifyTag(AdjustParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        AdjustDTO adjustDTO = remoteAdjustClient.get(param);
        if (adjustDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "调整单不存在");
        }
        if (!AdjustStatusEnum.CREATED.getStatus().equalsIgnoreCase(adjustDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "非创建状态不允许打标");
        }
        if (CollectionUtils.isEmpty(param.getTagList())) {
            adjustDTO.setTag(0);
        } else {
            adjustDTO.setTag(AdjustTagEnum.queryParamListToInteger(param.getTagList()));
        }
        Result<Boolean> success = Result.success(remoteAdjustClient.modify(ConverterUtil.convert(adjustDTO, AdjustParam.class)));
        // 添加日志
        AdjustLogDTO logDTO = new AdjustLogDTO();
        String content = StrUtil.join(StrUtil.EMPTY, "修改标记为：", AdjustTagEnum.NumToEnum(adjustDTO.getTag()).stream().map(AdjustTagEnum::getDesc).collect(Collectors.joining("|")));
        logDTO.setOpContent(content);
        logDTO.setOpRemark(content);
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setAdjustCode(adjustDTO.getCode());
        logDTO.setCargoCode(adjustDTO.getCargoCode());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_INSERT.getType());
        remoteAdjustLogClient.save(logDTO);

        return success;

    }

}
