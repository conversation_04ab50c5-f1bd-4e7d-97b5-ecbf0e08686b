package com.dt.platform.wms.client.rs;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.business.oms.common.BO.LogisticsReturnScene;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.component.common.enums.pre.BillLogTypeEnum;
import com.dt.component.common.enums.rs.*;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.bill.client.rs.bo.SalesReturnHandoverBO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.rs.*;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.domain.bill.param.rs.SalesReturnHandoverDetailParam;
import com.dt.domain.bill.param.rs.SalesReturnHandoverParam;
import com.dt.domain.bill.param.rs.SalesReturnOrderParam;
import com.dt.platform.utils.CompareUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.dto.rs.SalesReturnHandoverBizDTO;
import com.dt.platform.wms.dto.rs.SalesReturnHandoverDetailBizDTO;
import com.dt.platform.wms.integration.IRemoteLockSupportClient;
import com.dt.platform.wms.integration.IRemoteSeqRuleClient;
import com.dt.platform.wms.integration.IRemoteShipmentOrderClient;
import com.dt.platform.wms.integration.impl.CallUnderOtherContext;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.message.IRemoteMessageMqClient;
import com.dt.platform.wms.integration.oms.IRemoteOmsLogisticClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnHandoverClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnHandoverDetailClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import com.dt.platform.wms.integration.warehouseManage.IRemoteLogicWarehouseClient;
import com.dt.platform.wms.param.rs.SalesReturnHandoverBizParam;
import com.dt.platform.wms.param.rs.SalesReturnHandoverDetailBizParam;
import com.dt.tms.rpc.waybill.client.ITmsExpressClient;
import com.dt.tms.rpc.waybill.result.code.RpcTmsCodeItemResVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class SalesReturnHandoverBizClientImpl implements ISalesReturnHandoverBizClient {

    @DubboReference
    private ITmsExpressClient tmsExpressClient;

    @Resource
    private IRemoteMessageMqClient remoteMessageMqClient;

    @Resource
    private IRemoteSalesReturnHandoverClient remoteSalesReturnHandoverClient;

    @Resource
    private IRemoteSalesReturnHandoverDetailClient remoteSalesReturnHandoverDetailClient;

    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Resource
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    private IRemoteBillLogClient remoteBillLogClient;

    @Resource
    private IRemoteLockSupportClient remoteLockSupportClient;

    @Resource
    private IRemoteOmsLogisticClient remoteOmsLogisticClient;

    @Resource
    private IRemoteLogicWarehouseClient remoteLogicWarehouseClient;

    @Override
    public Result<Page<SalesReturnHandoverBizDTO>> getPage(SalesReturnHandoverBizParam param) {
        if (null == param) throw ExceptionUtil.ARG_ERROR;
        processExpressParam(param);
        Page<SalesReturnHandoverDTO> page = remoteSalesReturnHandoverClient.getPage(ConverterUtil.convert(param, SalesReturnHandoverParam.class));
        Page<SalesReturnHandoverBizDTO> salesReturnHandoverBizDTOPage = ConverterUtil.convertPage(page, SalesReturnHandoverBizDTO.class);
        return Result.success(salesReturnHandoverBizDTOPage);
    }

    @Override
    public Result<Page<SalesReturnHandoverDetailBizDTO>> detailPage(SalesReturnHandoverBizParam param) {
        SalesReturnHandoverDTO salesReturnHandoverDTO = get(param);

        SalesReturnHandoverDetailParam convert = new SalesReturnHandoverDetailParam();
        BeanUtil.copyProperties(param, convert);
        convert.setId(null);
        convert.setHandoverNo(salesReturnHandoverDTO.getHandoverNo());
        Page<SalesReturnHandoverDetailDTO> page = remoteSalesReturnHandoverDetailClient.getPage(convert);

        Page<SalesReturnHandoverDetailBizDTO> salesReturnHandoverDetailBizDTOPage = ConverterUtil.convertPage(page, SalesReturnHandoverDetailBizDTO.class);
        List<SalesReturnHandoverDetailBizDTO> records = salesReturnHandoverDetailBizDTOPage.getRecords();
        records.parallelStream()
                .peek(salesReturnHandoverDetailBizDTO -> salesReturnHandoverDetailBizDTO.setReturnTypeDesc(RSReturnTypeEnum.desc(salesReturnHandoverDetailBizDTO.getReturnType())))
                .filter(salesReturnHandoverDetailBizDTO -> StrUtil.isNotBlank(salesReturnHandoverDetailBizDTO.getSalesReturnOrderNo()))
                .forEach(salesReturnHandoverDetailBizDTO -> {
                    RpcContextUtil.setWarehouseCode(salesReturnHandoverDTO.getWarehouseCode());
                    List<String> extraExpressNoList = remoteSalesReturnOrderClient.getExtraExpressNoList(salesReturnHandoverDetailBizDTO.getExpressNo());
                    SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
                    salesReturnOrderParam.setReverseExpressNoList(extraExpressNoList);
                    List<SalesReturnOrderDTO> salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(salesReturnOrderParam);
                    for (SalesReturnOrderDTO salesReturnOrderDTO : salesReturnOrderDTOList) {
                        salesReturnHandoverDetailBizDTO.setWarehouseCode(salesReturnHandoverDTO.getWarehouseCode());
                        salesReturnHandoverDetailBizDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
                        salesReturnHandoverDetailBizDTO.setIsTaoTian(RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource()));
                    }
                });

        return Result.success(salesReturnHandoverDetailBizDTOPage);
    }

    @Override
    public Result<SalesReturnHandoverBizDTO> create(SalesReturnHandoverBizParam param) {
        if (null == param) throw ExceptionUtil.ARG_ERROR;
        if (StrUtil.isBlank(param.getCarrierCode())) throw ExceptionUtil.exceptionWithMessage("快递公司必填");
        List<RpcTmsCodeItemResVO> data = tmsExpressClient.queryCodes().getData();
        String sequence = remoteSeqRuleClient.findSequence(SeqEnum.ST_HANDOVER_000001);
        SalesReturnHandoverDTO salesReturnHandoverDTO = new SalesReturnHandoverDTO();
        salesReturnHandoverDTO.setHandoverNo(sequence);
        salesReturnHandoverDTO.setHandoverParty(param.getHandoverParty());
        salesReturnHandoverDTO.setCarrierCode(param.getCarrierCode());
        salesReturnHandoverDTO.setStatus(RSHandoverStatusEnum.DOING.getCode());
        data.stream().filter(rpcTmsCodeItemResVO -> rpcTmsCodeItemResVO.getCode().equalsIgnoreCase(param.getCarrierCode()))
                .findFirst().ifPresent(rpcTmsCodeItemResVO -> salesReturnHandoverDTO.setCarrierName(rpcTmsCodeItemResVO.getTitle()));

        remoteSalesReturnHandoverClient.save(salesReturnHandoverDTO);
        return Result.success(ConverterUtil.convert(salesReturnHandoverDTO, SalesReturnHandoverBizDTO.class));
    }

    @Override
    public Result<Object> confirmHandover(SalesReturnHandoverBizParam param) {
        SalesReturnHandoverDTO salesReturnHandoverDTO = get(param);
        if (!RSHandoverStatusEnum.DOING.getCode().equals(salesReturnHandoverDTO.getStatus())) {
            throw ExceptionUtil.exceptionWithMessage("交接单状态不是交接中");
        }
        salesReturnHandoverDTO.setHandoverTime(System.currentTimeMillis());
        salesReturnHandoverDTO.setStatus(RSHandoverStatusEnum.DONE.getCode());

        SalesReturnHandoverDetailParam detailParam = new SalesReturnHandoverDetailParam();
        detailParam.setHandoverNo(salesReturnHandoverDTO.getHandoverNo());
        List<SalesReturnHandoverDetailDTO> detailDTOList = remoteSalesReturnHandoverDetailClient.getList(detailParam);

        List<LogisticsReturnScene> logisticsReturnScenes = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(detailDTOList)) {
            logisticsReturnScenes.addAll(remoteOmsLogisticClient.logisticInfoList(detailDTOList.stream().map(SalesReturnHandoverDetailDTO::getExpressNo).distinct().collect(Collectors.toList())));

        }

        SalesReturnHandoverBO salesReturnHandoverBO = new SalesReturnHandoverBO();
        List<BillLogDTO> billLogDTOList = new ArrayList<>();
        List<SalesReturnOrderDTO> salesReturnOrderDTOList = new ArrayList<>();
        List<MessageMqDTO> messageMqDTOList = new ArrayList<>();
        //移除销退单号
        List<SalesReturnHandoverDetailDTO> forUpdateRemoveSalesNo = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(detailDTOList)) {
            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            salesReturnOrderParam.setReverseExpressNoList(detailDTOList.stream().flatMap(it -> remoteSalesReturnOrderClient.getExtraExpressNoList(it.getExpressNo()).stream()).collect(Collectors.toList()));
            salesReturnOrderParam.setStatus(RSOrderStatusEnum.REGISTERED.getCode());
            salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(salesReturnOrderParam);

            for (SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO : detailDTOList) {
                List<String> extraExpressNoList = remoteSalesReturnOrderClient.getExtraExpressNoList(salesReturnHandoverDetailDTO.getExpressNo());

                List<SalesReturnOrderDTO> collect = salesReturnOrderDTOList.stream()
                        .filter(salesReturnOrderDTO -> extraExpressNoList.stream().anyMatch(expressNo -> CompareUtil.normalEquals(salesReturnOrderDTO.getReverseExpressNo(), expressNo)))
                        .collect(Collectors.toList());

                // 需要拉单
                if (CollectionUtil.isEmpty(collect)) {
                    if (RSReturnRequireEnum.T.getCode().equalsIgnoreCase(salesReturnHandoverDetailDTO.getReturnRequire())) {
                        List<MessageMqDTO> pullMessage = remoteSalesReturnOrderClient.messageMqList(salesReturnHandoverDTO.getWarehouseCode(), salesReturnHandoverDetailDTO.getExpressNo(), MessageTypeEnum.OPERATION_SALE_RETURN_PULL);
                        messageMqDTOList.addAll(pullMessage);
                    }

                    // 添加交接明细时有销退单，确认交接时没有【销退单状态变更了】
                    if (StrUtil.isNotBlank(salesReturnHandoverDetailDTO.getSalesReturnOrderNo())) {
                        forUpdateRemoveSalesNo.add(salesReturnHandoverDetailDTO);
                    }

                }


                for (SalesReturnOrderDTO salesReturnOrderDTO : collect) {
                    salesReturnOrderDTO.setStatus(RSOrderStatusEnum.HANDOVER.getCode());
                    salesReturnOrderDTO.setHandoverTime(System.currentTimeMillis());
                    remoteSalesReturnOrderClient.buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
                    // 交接回告
                    List<MessageMqDTO> handoverMessageDTO = remoteSalesReturnOrderClient.messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_ARRIVE_CALLBACK);
                    messageMqDTOList.addAll(handoverMessageDTO);
                    if (RSRejectEnum.YES.getCode().equals(salesReturnHandoverDetailDTO.getReject())) {
                        salesReturnOrderDTO.setStatus(RSOrderStatusEnum.REJECT.getCode());
                        remoteSalesReturnOrderClient.buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
                        List<MessageMqDTO> rejectMessageDTO = remoteSalesReturnOrderClient.messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_REJECT_CALLBACK);
                        messageMqDTOList.addAll(rejectMessageDTO);
                        List<MessageMqDTO> rejectNotifyOrigin = remoteSalesReturnOrderClient.messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN);
                        messageMqDTOList.addAll(rejectNotifyOrigin);

                        BillLogDTO rejectBillLogDTO = remoteSalesReturnOrderClient.billLogDTO(salesReturnOrderDTO, "已拒收", "", CurrentUserHolder.getUserName());
                        billLogDTOList.add(rejectBillLogDTO);
                    }
                    BillLogDTO handoverBillLogDTO = remoteSalesReturnOrderClient.billLogDTO(salesReturnOrderDTO, "交接完成", StrUtil.join(StrUtil.EMPTY, "交接单号：", salesReturnHandoverDTO.getHandoverNo(),
                            "，是否破损：", RSDamageEnum.desc(salesReturnHandoverDetailDTO.getDamage()), "，是否拒收：",
                            RSRejectEnum.desc(salesReturnHandoverDetailDTO.getReject())), CurrentUserHolder.getUserName());
                    billLogDTOList.add(handoverBillLogDTO);
                }

                returnType(salesReturnHandoverDetailDTO, collect, () -> logisticsReturnScenes.stream()
                        .filter(logisticsReturnScene -> logisticsReturnScene.getExpressNo().equalsIgnoreCase(salesReturnHandoverDetailDTO.getExpressNo()))
                        .collect(Collectors.toList()));
                salesReturnHandoverDetailDTO.setSalesReturnOrderNo(collect.stream().map(SalesReturnOrderDTO::getSalesReturnOrderNo).distinct().collect(Collectors.joining(StrUtil.COMMA)));

                // 交接时判断上游下发的附加指令
                remoteSalesReturnOrderClient.handoverCheckAdditionalOrder(salesReturnHandoverDetailDTO.getAdditionalOrder(), RSRejectEnum.fromInt(salesReturnHandoverDetailDTO.getReject()));
            }
        }
        salesReturnHandoverBO.setSalesReturnOrderDTOList(salesReturnOrderDTOList);
        salesReturnHandoverBO.setMessageMqDTOList(messageMqDTOList);
        salesReturnHandoverBO.setSalesReturnHandoverDTOList(ListUtil.toList(salesReturnHandoverDTO));
        if (!CollectionUtils.isEmpty(forUpdateRemoveSalesNo)) {
            forUpdateRemoveSalesNo.forEach(a -> a.setSalesReturnOrderNo(""));
        }
        salesReturnHandoverBO.setSalesReturnHandoverDetailDTOList(detailDTOList);


        Boolean save = remoteSalesReturnHandoverClient.persist(salesReturnHandoverBO);

        remoteSalesReturnOrderClient.callback(messageMqDTOList);

        // 日志
        if (CollectionUtil.isNotEmpty(billLogDTOList)) {
            remoteBillLogClient.saveBatch(billLogDTOList);
        }

        Result<Object> success = Result.success();
        if (CollectionUtil.isNotEmpty(forUpdateRemoveSalesNo)) {
            success.setData("交接成功，运单号" + forUpdateRemoveSalesNo.stream().map(SalesReturnHandoverDetailDTO::getExpressNo).collect(Collectors.joining(StrUtil.COMMA)) + "对应的销退单状态已变更，系统已自动更新数据");
        }
        return success;
    }

    @Override
    public Result<Boolean> cancel(SalesReturnHandoverBizParam param) {
        SalesReturnHandoverDTO salesReturnHandoverDTO = get(param);
        SalesReturnHandoverDetailParam detailParam = new SalesReturnHandoverDetailParam();
        detailParam.setHandoverNo(salesReturnHandoverDTO.getHandoverNo());
        List<SalesReturnHandoverDetailDTO> list = remoteSalesReturnHandoverDetailClient.getList(detailParam);
        if (CollectionUtil.isNotEmpty(list)) {
            throw ExceptionUtil.exceptionWithMessage("该交接单中存在交接明细，不可取消");
        }
        salesReturnHandoverDTO.setStatus(RSHandoverStatusEnum.CANCEL.getCode());
        Boolean modify = remoteSalesReturnHandoverClient.modify(salesReturnHandoverDTO);
        return Result.success(modify);
    }

    @Override
    public Result<SalesReturnHandoverBizDTO> info(SalesReturnHandoverBizParam param) {
        SalesReturnHandoverDTO salesReturnHandoverDTO = get(param);
        SalesReturnHandoverBizDTO convert = ConverterUtil.convert(salesReturnHandoverDTO, SalesReturnHandoverBizDTO.class);
        return Result.success(convert);
    }

    @Override
    public Result<Boolean> addDetail(SalesReturnHandoverDetailBizParam param) {
        return remoteLockSupportClient.execute(() -> {
            SalesReturnHandoverDTO salesReturnHandoverDTO = get(ConverterUtil.convert(param, SalesReturnHandoverBizParam.class));
            if (!RSHandoverStatusEnum.DOING.getCode().equals(salesReturnHandoverDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("交接单状态不是交接中");
            }

            if (StrUtil.isBlank(param.getExpressNo())) throw ExceptionUtil.exceptionWithMessage("运单号必填");
            param.setExpressNo(param.getExpressNo().trim());
            if (ObjectUtil.isEmpty(param.getDamage())) throw ExceptionUtil.exceptionWithMessage("是否破损必填");
            if (RSDamageEnum.notContain(param.getDamage()))
                throw ExceptionUtil.exceptionWithMessage("是否破损取值错误");
            if (ObjectUtil.isEmpty(param.getReject())) throw ExceptionUtil.exceptionWithMessage("是否拒收必填");
            if (RSRejectEnum.notContain(param.getReject()))
                throw ExceptionUtil.exceptionWithMessage("是否拒收取值错误");
            if (RSRejectEnum.YES.getCode().equals(param.getReject())) {
                if (StrUtil.isBlank(param.getRejectReason()))
                    throw ExceptionUtil.exceptionWithMessage("拒收时拒收原因必填");
                if (CollectionUtil.isEmpty(param.getRejectImageList()))
                    throw ExceptionUtil.exceptionWithMessage("拒收时拒收附件必填");
                if (CollectionUtil.isNotEmpty(param.getRejectImageList()) && param.getRejectImageList().size() > 10)
                    throw ExceptionUtil.exceptionWithMessage("拒收附件最多上传10张");
            }
            if (RSRejectEnum.NO.getCode().equals(param.getReject())) {
                param.setRejectReason(StrUtil.EMPTY);
                param.setRejectImageList(new ArrayList<>());
            }


            SalesReturnHandoverDetailParam detailParam = new SalesReturnHandoverDetailParam();
            detailParam.setExpressNoList(remoteSalesReturnOrderClient.getExtraExpressNoList(param.getExpressNo()));
            SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO = remoteSalesReturnHandoverDetailClient.get(detailParam);
            if (null != salesReturnHandoverDetailDTO) {
                if (!salesReturnHandoverDetailDTO.getHandoverNo().equalsIgnoreCase(salesReturnHandoverDTO.getHandoverNo())) {
                    SalesReturnHandoverParam salesReturnHandoverParam = new SalesReturnHandoverParam();
                    salesReturnHandoverParam.setHandoverNo(salesReturnHandoverDetailDTO.getHandoverNo());
                    SalesReturnHandoverDTO other = remoteSalesReturnHandoverClient.get(salesReturnHandoverParam);
                    if (other.getStatus().equals(RSHandoverStatusEnum.DONE.getCode())) {
                        throw ExceptionUtil.exceptionWithMessage("该包裹已完成交接，请勿重复交接");
                    }
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "运单已在交接单", other.getHandoverNo(), "中，请确认"));
                } else {
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "运单号", param.getExpressNo(), "已存在，请勿重复交接"));
                }
            }

            detailParam = new SalesReturnHandoverDetailParam();
            detailParam.setHandoverNo(salesReturnHandoverDTO.getHandoverNo());
            List<SalesReturnHandoverDetailDTO> detailDTOList = remoteSalesReturnHandoverDetailClient.getList(detailParam);

            salesReturnHandoverDetailDTO = new SalesReturnHandoverDetailDTO();
            salesReturnHandoverDetailDTO.setHandoverNo(salesReturnHandoverDTO.getHandoverNo());
            salesReturnHandoverDetailDTO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
            salesReturnHandoverDetailDTO.setExpressNo(param.getExpressNo());
            salesReturnHandoverDetailDTO.setReject(param.getReject());
            salesReturnHandoverDetailDTO.setDamage(param.getDamage());

            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            List<String> expressNOList = remoteSalesReturnOrderClient.getExtraExpressNoList(param.getExpressNo());
            salesReturnOrderParam.setReverseExpressNoList(expressNOList);
            salesReturnOrderParam.setStatus(RSOrderStatusEnum.CREATED.getCode());
            List<SalesReturnOrderDTO> salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(salesReturnOrderParam);

            Holder<List<LogisticsReturnScene>> holder = new Holder<>();
            // 交接时判断上游下发的附件指令
            if (CollectionUtil.isNotEmpty(salesReturnOrderDTOList)) {
                SalesReturnOrderDTO salesReturnOrderDTO = salesReturnOrderDTOList.get(0);
                if (salesReturnOrderDTO.hasWarehouseRejectInstruction()) {
                    salesReturnHandoverDetailDTO.setReject(RSRejectEnum.YES.getCode());
                    salesReturnHandoverDetailDTO.setAdditionalOrder(RSAdditionalOrderEnum.WAREHOUSE_REJECT.getCode());
                }
                if (salesReturnOrderDTO.hasWarehouseReceiveInstruction()) {
                    salesReturnHandoverDetailDTO.setAdditionalOrder(RSAdditionalOrderEnum.WAREHOUSE_RECEIVE.getCode());
                    salesReturnHandoverDetailDTO.setReject(RSRejectEnum.NO.getCode());
                }
            }

            String expressNo = salesReturnHandoverDetailDTO.getExpressNo();
            Supplier<List<LogisticsReturnScene>> listSupplier = () -> remoteOmsLogisticClient.logisticInfoListNoFilter(expressNo);
            returnType(salesReturnHandoverDetailDTO, salesReturnOrderDTOList, listSupplier);

            detailDTOList.add(salesReturnHandoverDetailDTO);

            // 统计字段
            maintainStatistics(salesReturnHandoverDTO, detailDTOList);

            SalesReturnHandoverBO salesReturnHandoverBO = new SalesReturnHandoverBO();
            salesReturnHandoverBO.setSalesReturnHandoverDTOList(ListUtil.toList(salesReturnHandoverDTO));
            if (CollectionUtil.isNotEmpty(salesReturnOrderDTOList)) {
                for (SalesReturnOrderDTO salesReturnOrderDTO : salesReturnOrderDTOList) {
                    // 处理节点信息
                    salesReturnOrderDTO.setStatus(RSOrderStatusEnum.REGISTERED.getCode());
                    remoteSalesReturnOrderClient.buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
                    // 保存交接拒收相关附件
                    SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
                    salesReturnOrderExtraDTO.setRejectReason(param.getRejectReason());
                    salesReturnOrderExtraDTO.setRejectImageList(param.getRejectImageList());
                    salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
                }
                salesReturnHandoverDetailDTO.setSalesReturnOrderNo(salesReturnOrderDTOList.stream().map(SalesReturnOrderDTO::getSalesReturnOrderNo).limit(5).collect(Collectors.joining(StrUtil.COMMA)));
                salesReturnHandoverBO.setSalesReturnOrderDTOList(salesReturnOrderDTOList);
            }

            // 维护商家退回要求
            returnRequire(salesReturnHandoverDetailDTO);

            SalesReturnHandoverDetailExtraDTO salesReturnHandoverDetailExtraDTO = salesReturnHandoverDetailDTO.salesReturnHandoverDetailExtraDTO();
            salesReturnHandoverDetailExtraDTO.setRejectImageList(param.getRejectImageList());
            salesReturnHandoverDetailExtraDTO.setRejectReason(param.getRejectReason());
            salesReturnHandoverDetailDTO.salesReturnHandoverDetailExtraDTO(salesReturnHandoverDetailExtraDTO);
            salesReturnHandoverBO.setSalesReturnHandoverDetailDTOList(ListUtil.toList(salesReturnHandoverDetailDTO));
            remoteSalesReturnHandoverClient.persist(salesReturnHandoverBO);

            // 日志
            List<BillLogDTO> billLogDTOList = new ArrayList<>();
            for (SalesReturnOrderDTO salesReturnOrderDTO : salesReturnOrderDTOList) {
                BillLogDTO billLogDTO = new BillLogDTO();
                billLogDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
                billLogDTO.setBillType(BillLogTypeEnum.RS_SALES_ORDER.getType());
                billLogDTO.setOpBy(CurrentUserHolder.getUserName());
                billLogDTO.setOpDate(System.currentTimeMillis());
                billLogDTO.setOpContent("到仓登记");
                billLogDTO.setOpRemark("运单号：" + expressNo);
                billLogDTOList.add(billLogDTO);
            }
            if (CollectionUtil.isNotEmpty(billLogDTOList)) {
                remoteBillLogClient.saveBatch(billLogDTOList);
            }

            return Result.success(true);
        }, ListUtil.toList(StrUtil.join(StrUtil.COLON, CurrentRouteHolder.getWarehouseCode(), param.getHandoverNo())));
    }

    private void returnType(SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO,
                            List<SalesReturnOrderDTO> salesReturnOrderDTOList,
                            Supplier<List<LogisticsReturnScene>> logisticsReturnSceneList) {
        if (CollectionUtil.isNotEmpty(salesReturnOrderDTOList)) {
            // 匹配到一个销退单直接取销退单上的退货类型
            if (salesReturnOrderDTOList.size() == 1) {
                salesReturnHandoverDetailDTO.setReturnType(salesReturnOrderDTOList.get(0).getReturnType());
            } else {
                // 匹配到多个销退单，如果销退单退货类型一致取销退单上的退回类型，如果不一致不维护
                if (salesReturnOrderDTOList.stream().map(SalesReturnOrderDTO::getReturnType).distinct().count() == 1) {
                    salesReturnHandoverDetailDTO.setReturnType(salesReturnOrderDTOList.get(0).getReturnType());
                }
            }
        } else {
            // 查询正向订单
            List<LogisticsReturnScene> collection = logisticsReturnSceneList.get();
            if (CollectionUtil.isNotEmpty(collection)) {
                List<LogisticsReturnScene> ttkj = collection.stream().filter(it -> it.getAppCode().equalsIgnoreCase("TTKJ")).collect(Collectors.toList());
                // 不是淘天的不展示
                if (CollectionUtil.isNotEmpty(ttkj)) {
                    // 只会有一个
                    LogisticsReturnScene logisticsReturnScene = ttkj.get(0);
                    if (logisticsReturnScene.getIsRejected()) {
                        salesReturnHandoverDetailDTO.setReturnType(RSReturnTypeEnum.CUSTOMER_REJECT.getCode());
                    } else if (logisticsReturnScene.getIsIntercepted()) {
                        salesReturnHandoverDetailDTO.setReturnType(RSReturnTypeEnum.TMS_CUT.getCode());
                    } else {
                        salesReturnHandoverDetailDTO.setReturnType(RSReturnTypeEnum.CUSTOMER_REJECT.getCode());
                    }
                }
            } else {
                // 没有查询到正向出库单展示客退
                salesReturnHandoverDetailDTO.setReturnType(RSReturnTypeEnum.CUSTOMER_RETURNS.getCode());
            }
        }
    }

    /**
     * 维护商家退回要求
     */
    private void returnRequire(SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO) {
        List<LogisticsReturnScene> logisticsReturnScenes = remoteOmsLogisticClient.logisticInfoList(salesReturnHandoverDetailDTO.getExpressNo());
        if (CollectionUtil.isNotEmpty(logisticsReturnScenes)) {
            returnRequire(salesReturnHandoverDetailDTO, logisticsReturnScenes, ListUtil.toList(salesReturnHandoverDetailDTO.getExpressNo()));
        } else {
            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            salesReturnOrderParam.setNoStatus(RSOrderStatusEnum.CANCEL.getCode());
            salesReturnOrderParam.setReverseExpressNoList(remoteSalesReturnOrderClient.getExtraExpressNoList(salesReturnHandoverDetailDTO.getExpressNo()));
            List<SalesReturnOrderDTO> salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(salesReturnOrderParam);
            if (CollectionUtil.isNotEmpty(salesReturnOrderDTOList)) {
                SalesReturnOrderDTO salesReturnOrderDTO = salesReturnOrderDTOList.get(0);
                String expressNo = salesReturnOrderDTO.getExpressNo();
                returnRequire(salesReturnHandoverDetailDTO, remoteOmsLogisticClient.logisticInfoList(expressNo)
                                .stream().filter(logisticsReturnScene -> logisticsReturnScene.getAppCode().equalsIgnoreCase("TTKJ"))
                                .collect(Collectors.toList())
                        , salesReturnOrderDTOList.stream().map(SalesReturnOrderDTO::getExpressNo).collect(Collectors.toList()));
            }
        }
    }

    private void returnRequire(SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO, List<LogisticsReturnScene> logisticsReturnSceneList, List<String> deliveryExpressNo) {
        if (CollectionUtil.isNotEmpty(logisticsReturnSceneList)) {
            LogisticsReturnScene logisticsReturnScene = logisticsReturnSceneList.get(0);
            logisticsReturnScene.getWarehouseCode();
            WarehouseDTO warehouseDTO = remoteLogicWarehouseClient.entityWarehouseCode(logisticsReturnScene.getWarehouseCode());
            String code = warehouseDTO.getCode();

            CallUnderOtherContext.execute(() -> {
                ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
                shipmentOrderParam.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
                shipmentOrderParam.setExpressNoList(deliveryExpressNo);
                List<ShipmentOrderDTO> shipmentOrderDTOList = remoteShipmentOrderClient.getList(shipmentOrderParam);
                if (CollectionUtil.isNotEmpty(shipmentOrderDTOList)) {
                    String collect = shipmentOrderDTOList.stream().map(ShipmentOrderDTO::getTaotianPrintShipReturnInfo).distinct()
                            .sorted()
                            .collect(Collectors.joining(StrUtil.EMPTY));
                    salesReturnHandoverDetailDTO.setReturnRequire(collect);
                }
                return null;
            }, code);
        }
    }

    @Override
    public Result<Boolean> modifyDetail(SalesReturnHandoverDetailBizParam param) {
        SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO = get(param);
        SalesReturnHandoverBizParam salesReturnHandoverBizParam = new SalesReturnHandoverBizParam();
        salesReturnHandoverBizParam.setHandoverNo(salesReturnHandoverDetailDTO.getHandoverNo());
        SalesReturnHandoverDTO salesReturnHandoverDTO = get(salesReturnHandoverBizParam);
        if (!RSHandoverStatusEnum.DOING.getCode().equals(salesReturnHandoverDTO.getStatus())) {
            throw ExceptionUtil.exceptionWithMessage("交接单状态不是交接中");
        }

        if (ObjectUtil.isEmpty(param.getDamage())) throw ExceptionUtil.exceptionWithMessage("是否破损必填");
        if (RSDamageEnum.notContain(param.getDamage())) throw ExceptionUtil.exceptionWithMessage("是否破损取值错误");
        if (ObjectUtil.isEmpty(param.getReject())) throw ExceptionUtil.exceptionWithMessage("是否拒收必填");
        if (RSRejectEnum.notContain(param.getReject())) throw ExceptionUtil.exceptionWithMessage("是否拒收取值错误");
        if (RSRejectEnum.YES.getCode().equals(param.getReject())) {
            if (StrUtil.isBlank(param.getRejectReason()))
                throw ExceptionUtil.exceptionWithMessage("拒收时拒收原因必填");
            if (CollectionUtil.isEmpty(param.getRejectImageList()))
                throw ExceptionUtil.exceptionWithMessage("拒收时拒收附件必填");
            if (CollectionUtil.isNotEmpty(param.getRejectImageList()) && param.getRejectImageList().size() > 10)
                throw ExceptionUtil.exceptionWithMessage("拒收附件最多上传10张");
        }
        if (RSRejectEnum.NO.getCode().equals(param.getReject())) {
            param.setRejectReason(StrUtil.EMPTY);
            param.setRejectImageList(new ArrayList<>());
        }

        SalesReturnHandoverDetailParam detailParam = new SalesReturnHandoverDetailParam();
        detailParam.setHandoverNo(salesReturnHandoverDTO.getHandoverNo());
        List<SalesReturnHandoverDetailDTO> detailDTOList = remoteSalesReturnHandoverDetailClient.getList(detailParam);
        for (SalesReturnHandoverDetailDTO returnHandoverDetailDTO : detailDTOList) {
            if (returnHandoverDetailDTO.getId().equals(salesReturnHandoverDetailDTO.getId())) {
                returnHandoverDetailDTO.setDamage(param.getDamage());
                returnHandoverDetailDTO.setReject(param.getReject());
            }
        }
        maintainStatistics(salesReturnHandoverDTO, detailDTOList);

        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        List<String> expressNOList = remoteSalesReturnOrderClient.getExtraExpressNoList(salesReturnHandoverDetailDTO.getExpressNo());
        salesReturnOrderParam.setReverseExpressNoList(expressNOList);
        salesReturnOrderParam.setNoStatus(RSOrderStatusEnum.CANCEL.getCode());
        List<SalesReturnOrderDTO> salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(salesReturnOrderParam);
        if (CollectionUtil.isNotEmpty(salesReturnOrderDTOList)) {
            for (SalesReturnOrderDTO salesReturnOrderDTO : salesReturnOrderDTOList) {
                // 保存交接拒收相关附件
                SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
                salesReturnOrderExtraDTO.setRejectReason(param.getRejectReason());
                salesReturnOrderExtraDTO.setRejectImageList(param.getRejectImageList());
                salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
            }
        }

        SalesReturnHandoverDetailExtraDTO salesReturnHandoverDetailExtraDTO = salesReturnHandoverDetailDTO.salesReturnHandoverDetailExtraDTO();
        salesReturnHandoverDetailExtraDTO.setRejectImageList(param.getRejectImageList());
        salesReturnHandoverDetailExtraDTO.setRejectReason(param.getRejectReason());
        salesReturnHandoverDetailDTO.salesReturnHandoverDetailExtraDTO(salesReturnHandoverDetailExtraDTO);

        salesReturnHandoverDetailDTO.setDamage(param.getDamage());
        salesReturnHandoverDetailDTO.setReject(param.getReject());
        SalesReturnHandoverBO salesReturnHandoverBO = new SalesReturnHandoverBO();
        salesReturnHandoverBO.setSalesReturnHandoverDTOList(ListUtil.toList(salesReturnHandoverDTO));
        salesReturnHandoverBO.setSalesReturnHandoverDetailDTOList(ListUtil.toList(salesReturnHandoverDetailDTO));
        salesReturnHandoverBO.setSalesReturnOrderDTOList(salesReturnOrderDTOList);
        remoteSalesReturnHandoverClient.persist(salesReturnHandoverBO);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> delDetail(SalesReturnHandoverDetailBizParam param) {
        SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO = get(param);
        SalesReturnHandoverBizParam salesReturnHandoverBizParam = new SalesReturnHandoverBizParam();
        salesReturnHandoverBizParam.setHandoverNo(salesReturnHandoverDetailDTO.getHandoverNo());
        SalesReturnHandoverDTO salesReturnHandoverDTO = get(salesReturnHandoverBizParam);
        if (!RSHandoverStatusEnum.DOING.getCode().equals(salesReturnHandoverDTO.getStatus())) {
            throw ExceptionUtil.exceptionWithMessage("交接单状态不是交接中");
        }

        SalesReturnHandoverDetailParam detailParam = new SalesReturnHandoverDetailParam();
        detailParam.setHandoverNo(salesReturnHandoverDetailDTO.getHandoverNo());
        List<SalesReturnHandoverDetailDTO> detailDTOList = remoteSalesReturnHandoverDetailClient.getList(detailParam);
        detailDTOList = detailDTOList.stream().filter(it -> !it.getId().equals(salesReturnHandoverDetailDTO.getId())).collect(Collectors.toList());

        maintainStatistics(salesReturnHandoverDTO, detailDTOList);

        SalesReturnHandoverBO salesReturnHandoverBO = new SalesReturnHandoverBO();
        salesReturnHandoverBO.setSalesReturnHandoverDTOList(ListUtil.toList(salesReturnHandoverDTO));
        salesReturnHandoverBO.setRemoveDetailIdList(ListUtil.toList(param.getId()));

        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setReverseExpressNoList(remoteSalesReturnOrderClient.getExtraExpressNoList(salesReturnHandoverDetailDTO.getExpressNo()));
        List<SalesReturnOrderDTO> salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(salesReturnOrderParam);

        List<BillLogDTO> billLogDTOList = new ArrayList<>();
        List<SalesReturnOrderDTO> salesReturnOrderDTOListUpdate = new ArrayList<>();
        for (SalesReturnOrderDTO salesReturnOrderDTO : salesReturnOrderDTOList) {
            if (!RSOrderStatusEnum.REGISTERED.getCode().equals(salesReturnOrderDTO.getStatus())) {
                continue;
            }
            remoteSalesReturnOrderClient.removeNode(salesReturnOrderDTO, RSOrderStatusEnum.REGISTERED);
            salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CREATED.getCode());
            SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
            salesReturnOrderExtraDTO.setRejectReason("");
            salesReturnOrderExtraDTO.setRejectImageList(new ArrayList<>());
            salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
            salesReturnOrderDTOListUpdate.add(salesReturnOrderDTO);

            billLogDTOList.add(remoteSalesReturnOrderClient.billLogDTO(salesReturnOrderDTO, "删除到仓登记", "交接单号：" + salesReturnHandoverDetailDTO.getHandoverNo(), CurrentUserHolder.getUserName()));
        }
        salesReturnHandoverBO.setSalesReturnOrderDTOList(salesReturnOrderDTOListUpdate);
        remoteSalesReturnHandoverClient.persist(salesReturnHandoverBO);

        if (CollectionUtil.isNotEmpty(billLogDTOList)) {
            remoteBillLogClient.saveBatch(billLogDTOList);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> taoTianPull(SalesReturnHandoverBizParam param) {
        if (StrUtil.isBlank(param.getCargoCode())) throw ExceptionUtil.exceptionWithMessage("货主编码不能为空");
        if (CollectionUtil.isEmpty(param.getExpressNoList()))
            throw ExceptionUtil.exceptionWithMessage("运单号不能为空");
        List<MessageMqDTO> pullMessageList = param.getExpressNoList().stream().flatMap(it -> {
            List<MessageMqDTO> messageMqDTOList = remoteSalesReturnOrderClient.messageMqList(CurrentRouteHolder.getWarehouseCode(), it, MessageTypeEnum.OPERATION_SALE_RETURN_PULL);
            messageMqDTOList.forEach(messageMqDTO -> messageMqDTO.setCargoCode(param.getCargoCode()));
            return messageMqDTOList.stream();
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(pullMessageList)) {
            remoteMessageMqClient.saveBatch(pullMessageList);
            remoteSalesReturnOrderClient.callback(pullMessageList);
        }
        return Result.success();
    }

    private SalesReturnHandoverDTO get(SalesReturnHandoverBizParam param) {
        if (null == param || (null == param.getId() && StrUtil.isBlank(param.getHandoverNo())))
            throw ExceptionUtil.ARG_ERROR;
        SalesReturnHandoverParam salesReturnHandoverParam = new SalesReturnHandoverParam();
        salesReturnHandoverParam.setId(param.getId());
        salesReturnHandoverParam.setHandoverNo(param.getHandoverNo());
        SalesReturnHandoverDTO salesReturnHandoverDTO = remoteSalesReturnHandoverClient.get(salesReturnHandoverParam);
        if (null == salesReturnHandoverDTO) throw ExceptionUtil.exceptionWithMessage("交接单不存在");
        return salesReturnHandoverDTO;
    }

    private SalesReturnHandoverDetailDTO get(SalesReturnHandoverDetailBizParam param) {
        if (null == param || null == param.getId()) throw ExceptionUtil.ARG_ERROR;
        SalesReturnHandoverDetailParam detailParam = new SalesReturnHandoverDetailParam();
        detailParam.setId(param.getId());
        SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO = remoteSalesReturnHandoverDetailClient.get(detailParam);
        if (null != salesReturnHandoverDetailDTO) return salesReturnHandoverDetailDTO;
        throw ExceptionUtil.exceptionWithMessage("交接明细不存在");
    }

    private void maintainStatistics(SalesReturnHandoverDTO salesReturnHandoverDTO, List<SalesReturnHandoverDetailDTO> detailDTOList) {
        long damageCount = detailDTOList.stream().filter(it -> RSDamageEnum.YES.getCode().equals(it.getDamage())).count();
        long rejectCount = detailDTOList.stream().filter(it -> RSRejectEnum.YES.getCode().equals(it.getReject())).count();
        salesReturnHandoverDTO.setPackageCount(detailDTOList.size());
        salesReturnHandoverDTO.setDamageCount(((int) damageCount));
        if (damageCount > 0) {
            salesReturnHandoverDTO.setDamage(RSDamageEnum.YES.getCode());
        } else {
            salesReturnHandoverDTO.setDamage(RSDamageEnum.NO.getCode());
        }
        salesReturnHandoverDTO.setRejectCount(((int) rejectCount));
        if (rejectCount > 0) {
            salesReturnHandoverDTO.setReject(RSRejectEnum.YES.getCode());
        } else {
            salesReturnHandoverDTO.setReject(RSRejectEnum.NO.getCode());
        }
    }

    private void processExpressParam(SalesReturnHandoverBizParam param) {
        if (StrUtil.isNotBlank(param.getExpressNo())) {
            if (CollectionUtil.isNotEmpty(param.getExpressNoList())) {
                param.setExpressNoList(ListUtil.toList(param.getExpressNo()));
            } else {
                if (!param.getExpressNoList().contains(param.getExpressNo())) {
                    param.setId(-1L);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(param.getExpressNoList())) {
            SalesReturnHandoverDetailParam detailParam = new SalesReturnHandoverDetailParam();
            detailParam.setExpressNoList(param.getExpressNoList());
            List<SalesReturnHandoverDetailDTO> list = remoteSalesReturnHandoverDetailClient.getList(detailParam);
            List<String> collect = list.stream().map(SalesReturnHandoverDetailDTO::getHandoverNo).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect)) {
                param.setId(-1L);
            } else {
                if (CollectionUtil.isEmpty(param.getHandoverNoList())) {
                    param.setHandoverNoList(collect);
                } else {
                    Collection<String> intersection = CollectionUtil.intersection(collect, param.getHandoverNoList());
                    if (CollectionUtil.isEmpty(intersection)) {
                        param.setId(-1L);
                    } else {
                        param.setHandoverNoList(new ArrayList<>(intersection));
                    }
                }
            }
        }
    }

    private List<String> expressNOLikeHandle(String expressNO) {
        if (StrUtil.isBlank(expressNO)) return ListUtil.toList(expressNO);
        expressNO = expressNO.toUpperCase();
        List<String> expressNoList = new ArrayList<>();
        expressNoList.add(expressNO);
        expressNoList.add("T" + expressNO);
        expressNoList.add("R02T" + expressNO);
        if (expressNO.startsWith("T")) {
            expressNoList.add(expressNO.substring(1));
        }
        if (expressNO.startsWith("R02T")) {
            expressNoList.add(expressNO.substring(4));
        }

        return expressNoList;
    }
}
