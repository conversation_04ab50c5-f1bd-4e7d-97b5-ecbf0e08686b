package com.dt.platform.wms.client;

import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@DubboService
public class SupportWarehouseClientImpl implements ISupportWarehouseClient {

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Override
    public List<String> supportWarehouseList() {
        return defaultWarehouseCodeConfig.getWarehouseCodeList();
    }
}
