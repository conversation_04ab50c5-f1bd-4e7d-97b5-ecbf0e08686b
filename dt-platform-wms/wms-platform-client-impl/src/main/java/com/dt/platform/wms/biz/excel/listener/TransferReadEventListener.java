package com.dt.platform.wms.biz.excel.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.LocationTypeEnum;
import com.dt.component.common.enums.cargo.CargoOwnerStatusEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.transfer.TransferBusinessTypeEnum;
import com.dt.component.common.enums.transfer.TransferDetailRPEnum;
import com.dt.component.common.enums.transfer.TransferDetailReasonEnum;
import com.dt.component.common.enums.transfer.TransferReasonEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.LocationDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.param.LocationParam;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.excel.DefaultReadEventListener;
import com.dt.platform.wms.biz.excel.bo.ExcelImportTransferBO;
import com.dt.platform.wms.client.ITransferBizClient;
import com.dt.platform.wms.integration.IRemoteCargoOwnerClient;
import com.dt.platform.wms.integration.IRemoteLocationClient;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.param.rec.CheckSkuLotParam;
import com.dt.platform.wms.param.transfer.TransferDetailUpdateBizParam;
import com.dt.platform.wms.param.transfer.TransferUpdateBizParam;
import lombok.Data;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/2/16 18:16
 */
@Data
@Component("transferReadEventListener")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class TransferReadEventListener extends DefaultReadEventListener<ExcelImportTransferBO>    {

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteLocationClient remoteLocationClient;

    @Resource
    private ITransferBizClient transferBizClient;

    @Resource
    private ISkuLotBiz skuLotBiz;

    @Resource
    DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Override
    public void checkExcel(ExcelImportTransferBO excelImportTransferBO, AnalysisContext context) {
        excelImportTransferBO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        // trim
        excelImportTransferBO.setCargoCode(excelImportTransferBO.getCargoCode().trim());
        excelImportTransferBO.setReasonName(excelImportTransferBO.getReasonName().trim());
        excelImportTransferBO.setNote(excelImportTransferBO.getNote().trim());

        if (StrUtil.isBlank(excelImportTransferBO.getCargoCode())) {
            errorInfoList.add(getExcelErrorInfo("*货主编码", "货主编码必填"));
        } else if (excelImportTransferBO.getCargoCode().contains(" ")) {
            errorInfoList.add(getExcelErrorInfo("*货主编码", "货主编码不能包含空格"));
        }
        if (StrUtil.isBlank(excelImportTransferBO.getReasonName())) {
            errorInfoList.add(getExcelErrorInfo("*转移原因", "转移原因必填"));
        } else if (excelImportTransferBO.getReasonName().contains(" ")) {
            errorInfoList.add(getExcelErrorInfo("*转移原因", "转移原因不能包含空格"));
        }
        if (StrUtil.isBlank(excelImportTransferBO.getNote())) {
            errorInfoList.add(getExcelErrorInfo("*转移描述", "转移描述必填"));
        } else if (excelImportTransferBO.getNote().contains(" ")) {
            errorInfoList.add(getExcelErrorInfo("*转移描述", "转移描述不能包含空格"));
        }
        if (StrUtil.isBlank(excelImportTransferBO.getOriginSkuCode())) {
            errorInfoList.add(getExcelErrorInfo("*来源商品代码", "来源商品代码必填"));
        }
        if (StrUtil.isBlank(excelImportTransferBO.getOriginLocationCode())) {
            errorInfoList.add(getExcelErrorInfo("*来源库位", "来源库位必填"));
        }
        if (StrUtil.isBlank(excelImportTransferBO.getOriginSkuLotNo())) {
            errorInfoList.add(getExcelErrorInfo("*来源批次ID", "来源批次ID必填"));
        }
        if (StrUtil.isBlank(excelImportTransferBO.getTargetSkuQualityName())) {
            errorInfoList.add(getExcelErrorInfo("*目标商品属性", "目标商品属性必填"));
        }
        if (StrUtil.isBlank(excelImportTransferBO.getTargetInventoryTypeName())) {
            errorInfoList.add(getExcelErrorInfo("*目标残次等级", "目标残次等级必填"));
        }
        if (StrUtil.isBlank(excelImportTransferBO.getTargetLocationCode())) {
            errorInfoList.add(getExcelErrorInfo("*目标库位", "目标库位必填"));
        }
        if (ObjectUtil.isEmpty(excelImportTransferBO.getTransferAmount())) {
            errorInfoList.add(getExcelErrorInfo("*转移数量", "转移数量必填"));
        } else if (excelImportTransferBO.getTransferAmount().compareTo(BigDecimal.ZERO) <= 0) {
            errorInfoList.add(getExcelErrorInfo("*转移数量", "转移数量必须大于零"));
        } else if (excelImportTransferBO.getTransferAmount().stripTrailingZeros().scale() > 0) {
            errorInfoList.add(getExcelErrorInfo("*转移数量", "转移数量必须是整数"));
        }
        //业务场景
        if (!StringUtils.isEmpty(excelImportTransferBO.getBusinessTypeDesc()) && Arrays.stream(TransferBusinessTypeEnum.values()).noneMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferBO.getBusinessTypeDesc()))) {
            errorInfoList.add(getExcelErrorInfo("业务场景", "转移单的业务场景填写错误,请核查"));
        }
        if (!StringUtils.isEmpty(excelImportTransferBO.getBusinessTypeDesc()) && Arrays.stream(TransferBusinessTypeEnum.values()).anyMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferBO.getBusinessTypeDesc()))) {
            Arrays.stream(TransferBusinessTypeEnum.values())
                    .filter(it -> it.getName().equalsIgnoreCase(excelImportTransferBO.getBusinessTypeDesc()))
                    .findFirst().ifPresent(a -> excelImportTransferBO.setBusinessType(a.getCode()));
        }
        //明细原因
        if (!StringUtils.isEmpty(excelImportTransferBO.getDetailReasonDesc()) && Arrays.stream(TransferDetailReasonEnum.values()).noneMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferBO.getDetailReasonDesc()))) {
            errorInfoList.add(getExcelErrorInfo("调整原因", "转移单的调整原因填写错误,请核查"));
        }
        if (!StringUtils.isEmpty(excelImportTransferBO.getDetailReasonDesc()) && Arrays.stream(TransferDetailReasonEnum.values()).anyMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferBO.getDetailReasonDesc()))) {
            Arrays.stream(TransferDetailReasonEnum.values())
                    .filter(it -> it.getName().equalsIgnoreCase(excelImportTransferBO.getDetailReasonDesc()))
                    .findFirst().ifPresent(a -> excelImportTransferBO.setDetailReason(a.getCode()));
        }
        //明细责任方
        if (!StringUtils.isEmpty(excelImportTransferBO.getRpDesc()) && Arrays.stream(TransferDetailRPEnum.values()).noneMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferBO.getRpDesc()))) {
            errorInfoList.add(getExcelErrorInfo("责任方", "转移单的责任方填写错误,请核查"));
        }
        if (!StringUtils.isEmpty(excelImportTransferBO.getRpDesc()) && Arrays.stream(TransferDetailRPEnum.values()).anyMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferBO.getRpDesc()))) {
            Arrays.stream(TransferDetailRPEnum.values())
                    .filter(it -> it.getName().equalsIgnoreCase(excelImportTransferBO.getRpDesc()))
                    .findFirst().ifPresent(a -> excelImportTransferBO.setRp(a.getCode()));
        }
        //调整字节信息校验
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList()) && defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList().contains(CurrentRouteHolder.getWarehouseCode())) {
            //业务场景
            if (StringUtils.isEmpty(excelImportTransferBO.getBusinessTypeDesc())) {
                errorInfoList.add(getExcelErrorInfo("业务场景", "当前仓库,转移单的业务场景必填"));
            }
            //调整原因
            if (StringUtils.isEmpty(excelImportTransferBO.getDetailReasonDesc())) {
                errorInfoList.add(getExcelErrorInfo("调整原因", "当前仓库,转移单的调整原因必填"));
            }
            //备注
            if (StringUtils.isEmpty(excelImportTransferBO.getRemark())) {
                errorInfoList.add(getExcelErrorInfo("备注", "当前仓库,转移单的明细备注必填"));
            }
            //责任人
            if (StringUtils.isEmpty(excelImportTransferBO.getRpDesc())) {
                errorInfoList.add(getExcelErrorInfo("责任方", "当前仓库,转移单的责任方必填"));
            }
        }

        if (CollectionUtil.isNotEmpty(errorInfoList)) {
            for (String errorInfo : errorInfoList) {
                callBackPublicService(false, uid, context.readRowHolder().getRowIndex(), excelImportTransferBO, errorInfo);
            }
        }
    }

    private void resetData(ExcelImportTransferBO excelImportTransferBO, AnalysisContext context) {
        List<String> errorMessage = new ArrayList<>();

        if (StrUtil.isNotBlank(excelImportTransferBO.getReasonName())) {
            try {
                TransferReasonEnum transferReasonEnum = TransferReasonEnum.fromName(excelImportTransferBO.getReasonName());
                excelImportTransferBO.setReason(transferReasonEnum.getCode());
            } catch (Exception exception) {
                errorMessage.add(getExcelErrorInfo("*转移原因", "转移原因错误"));
            }
        }

        if (StrUtil.isNotBlank(excelImportTransferBO.getTargetSkuQualityName())) {
            try {
                SkuQualityEnum skuQualityEnum = SkuQualityEnum.getEnumByMessage(excelImportTransferBO.getTargetSkuQualityName());
                excelImportTransferBO.setTargetSkuQuality(skuQualityEnum.getLevel());
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("*目标商品属性", "正残属性错误"));
            }
        }

        if (StrUtil.isNotBlank(excelImportTransferBO.getTargetInventoryTypeName())) {
            try {
                InventoryTypeEnum inventoryTypeEnum = InventoryTypeEnum.getEnumByMessage(excelImportTransferBO.getTargetInventoryTypeName());
                excelImportTransferBO.setTargetInventoryType(inventoryTypeEnum.getCode());
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("*目标残次等级", "目标残次等级错误"));
            }
        }

        if (ObjectUtil.isNotEmpty(excelImportTransferBO.getTargetProductDateTimeStr())) {
            try {
                excelImportTransferBO.setTargetProductDateTime(parseDate(excelImportTransferBO.getTargetProductDateTimeStr()));
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("目标生产日期", "目标生产日期格式错误"));
            }
        }
        if (ObjectUtil.isNotEmpty(excelImportTransferBO.getTargetExpireDateTimeStr())) {
            try {
                excelImportTransferBO.setTargetExpireDateTime(parseDate(excelImportTransferBO.getTargetExpireDateTimeStr()));
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("目标失效日期", "目标失效日期格式错误"));
            }
        }
        if (ObjectUtil.isNotEmpty(excelImportTransferBO.getTargetReceiveDateTimeStr())) {
            try {
                excelImportTransferBO.setTargetReceiveDateTime(parseDate(excelImportTransferBO.getTargetReceiveDateTimeStr()));
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("目标入库日期", "目标入库日期格式错误"));
            }
        }

//        if (ObjectUtil.isNotEmpty(excelImportTransferBO.getTargetValidityCode())) {
//            boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", excelImportTransferBO.getTargetValidityCode());
//            if (!matches) {
//                errorMessage.add(getExcelErrorInfo("目标效期暗码", "目标效期暗码:英文大小写、数字、中划线,长度30,请核查"));
//            }
//        }
        if (ObjectUtil.isNotEmpty(excelImportTransferBO.getTargetBoxCode())) {
            boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", excelImportTransferBO.getTargetBoxCode());
            if (!matches) {
                errorMessage.add(getExcelErrorInfo("目标箱码", "目标箱码:英文大小写、数字、中划线,长度30,请核查"));
            }
        }
        if (ObjectUtil.isNotEmpty(excelImportTransferBO.getTargetPalletCode())) {
            boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", excelImportTransferBO.getTargetPalletCode());
            if (!matches) {
                errorMessage.add(getExcelErrorInfo("目标托盘号", "目标托盘号:英文大小写、数字、中划线,长度30,请核查"));
            }
        }


        if (CollectionUtil.isNotEmpty(errorMessage)) {
            for (String errorInfo : errorMessage) {
                callBackPublicService(false, uid, context.readRowHolder().getRowIndex(), excelImportTransferBO, errorInfo);
            }
        }
        errorInfoList.addAll(errorMessage);
    }

    @Override
    public void invoke(ExcelImportTransferBO excelImportTransferBO, AnalysisContext context) {
        //跳过 0 、 1 数据
        if (context.readRowHolder().getRowIndex() <= 1) {
            return;
        }

        checkExcel(excelImportTransferBO, context);
        resetData(excelImportTransferBO, context);

        // 这里只读取，全部读完统一校验
        dataList.add(excelImportTransferBO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

        if (CollectionUtil.isEmpty(dataList)) return;
        if (basicCheckFail()) return;

        String cargoCode = dataList.get(0).getCargoCode();
        List<String> skuCodeList = dataList.stream().map(ExcelImportTransferBO::getOriginSkuCode).distinct().collect(Collectors.toList());
        List<String> skuLotNoList = dataList.stream().map(ExcelImportTransferBO::getOriginSkuLotNo).distinct().collect(Collectors.toList());
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(cargoCode);
        List<String> locationCodeList = dataList.stream().flatMap(excelImportTransferBO -> Stream.of(excelImportTransferBO.getOriginLocationCode(), excelImportTransferBO.getTargetLocationCode()))
                .distinct()
                .collect(Collectors.toList());

        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(locationCodeList);
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);

        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(dataList.get(0).getCargoCode());
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);

        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(cargoCode);
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);

        if (checkCargoReasonAndNoteFail()) return;
        if (checkCargoExistFail(cargoOwnerDTO)) return;
        if (checkSkuFail(skuDTOList, locationDTOList, skuLotDTOList)) return;

        // save
        TransferUpdateBizParam transferUpdateBizParam = new TransferUpdateBizParam();
        transferUpdateBizParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        transferUpdateBizParam.setCargoCode(cargoCode);
        transferUpdateBizParam.setFlag(TransferUpdateBizParam.FlagEnum.IMPORT_ADD);
        transferUpdateBizParam.setReason(dataList.get(0).getReason());
        transferUpdateBizParam.setNote(dataList.get(0).getNote());
        transferUpdateBizParam.setBusinessType(dataList.get(0).getBusinessType());
        ArrayList<TransferDetailUpdateBizParam> detailList = new ArrayList<>();
        for (ExcelImportTransferBO excelImportTransferBO : dataList) {
            TransferDetailUpdateBizParam transferDetailUpdateBizParam = new TransferDetailUpdateBizParam();
            transferDetailUpdateBizParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
            transferDetailUpdateBizParam.setCargoCode(cargoCode);
            transferDetailUpdateBizParam.setSkuCode(excelImportTransferBO.getOriginSkuCode());
            transferDetailUpdateBizParam.setSkuLotNo(excelImportTransferBO.getOriginSkuLotNo());
            transferDetailUpdateBizParam.setLocationCode(excelImportTransferBO.getOriginLocationCode());
            transferDetailUpdateBizParam.setTargetLocationCode(excelImportTransferBO.getTargetLocationCode());
            transferDetailUpdateBizParam.setTargetSkuCode(excelImportTransferBO.getOriginSkuCode());
            skuLotDTOList.stream()
                    .filter(skuLotDTO -> skuLotDTO.getCode().equals(excelImportTransferBO.getOriginSkuLotNo()))
                    .findFirst()
                    .ifPresent(skuLotDTO -> {
                        transferDetailUpdateBizParam.setManufDate(skuLotDTO.getManufDate());
                        transferDetailUpdateBizParam.setExpireDate(skuLotDTO.getExpireDate());
                        transferDetailUpdateBizParam.setSkuQuality(skuLotDTO.getSkuQuality());
                        transferDetailUpdateBizParam.setProductionNo(skuLotDTO.getProductionNo());
                        transferDetailUpdateBizParam.setWithdrawDate(skuLotDTO.getWithdrawDate());
                        transferDetailUpdateBizParam.setTargetReceiveDate(skuLotDTO.getReceiveDate());
                    });
            transferDetailUpdateBizParam.setTargetManufDate(excelImportTransferBO.getTargetProductDateTime());
            transferDetailUpdateBizParam.setTargetExpireDate(excelImportTransferBO.getTargetExpireDateTime());
            transferDetailUpdateBizParam.setTargetSkuQuality(excelImportTransferBO.getTargetSkuQuality());
            transferDetailUpdateBizParam.setTargetProductionNo(excelImportTransferBO.getTargetProductNo());
            transferDetailUpdateBizParam.setChangeQty(excelImportTransferBO.getTransferAmount());
            transferDetailUpdateBizParam.setTargetReceiveDate(excelImportTransferBO.getTargetReceiveDateTime());
            transferDetailUpdateBizParam.setTargetExternalLinkBillNo(excelImportTransferBO.getTargetExternalLinkBillNo());
            transferDetailUpdateBizParam.setTargetInventoryType(excelImportTransferBO.getTargetInventoryType());

            transferDetailUpdateBizParam.setTargetValidityCode(excelImportTransferBO.getTargetValidityCode());
            transferDetailUpdateBizParam.setTargetPalletCode(excelImportTransferBO.getTargetPalletCode());
            transferDetailUpdateBizParam.setTargetBoxCode(excelImportTransferBO.getTargetBoxCode());

            transferDetailUpdateBizParam.setReason(excelImportTransferBO.getDetailReason());
            transferDetailUpdateBizParam.setRemark(excelImportTransferBO.getRemark());
            transferDetailUpdateBizParam.setRp(excelImportTransferBO.getRp());
            detailList.add(transferDetailUpdateBizParam);
        }
        transferUpdateBizParam.setTransferDetailUpdateBizParamList(detailList);
        try {
            transferBizClient.updateTransferDetail(transferUpdateBizParam);
            IntStream.range(3, dataList.size() + 3).forEach(it -> {
                callBackPublicService(true, uid, it, dataList.get(it - 3), "");
            });
        } catch (Exception e) {
            callBackPublicService(false, uid, 0, null, e.getMessage());
        }
    }

    /**
     * 基础校验
     *
     * @return
     */
    private boolean basicCheckFail() {
        return CollectionUtil.isNotEmpty(errorInfoList);
    }

    /**
     * 货主编码、转移原因、转移描述必须完全一致
     *
     * @return
     */
    private boolean checkCargoReasonAndNoteFail() {
        long count = dataList.stream()
                .map(it -> StrUtil.join(StrUtil.COLON, it.getCargoCode(), it.getReason(), it.getNote(), it.getBusinessTypeDesc()))
                .distinct()
                .count();
        if (count > 1) {
            callBackPublicService(false, uid, 0, null, getExcelErrorInfo("业务校验", "货主编码、转移原因、转移描述、业务场景必须完全一致"));
            return true;
        }
        return false;
    }

    /**
     * 货主存在性校验
     *
     * @return
     */
    private boolean checkCargoExistFail(CargoOwnerDTO cargoOwnerDTO) {
        if (ObjectUtil.isEmpty(cargoOwnerDTO)) {
            callBackPublicService(false, uid, 0, null, getExcelErrorInfo("业务校验", "货主不存在"));
            return true;
        }
        if (CargoOwnerStatusEnum.DISABLE.getValue().equals(cargoOwnerDTO.getStatus())) {
            callBackPublicService(false, uid, 0, null, getExcelErrorInfo("业务校验", "货主禁用"));
            return true;
        }
        return false;
    }

    /**
     * 业务校验
     *
     * @param skuDTOList
     * @param locationDTOList
     * @param skuLotDTOList
     * @return
     */
    private boolean checkSkuFail(List<SkuDTO> skuDTOList, List<LocationDTO> locationDTOList, List<SkuLotDTO> skuLotDTOList) {
        int errorCount = 0;
        int lineNo = 0;
        for (ExcelImportTransferBO excelImportTransferBO : dataList) {
            lineNo++;
            Optional<SkuDTO> optional = skuDTOList.stream().filter(skuDTO -> skuDTO.getCode().equals(excelImportTransferBO.getOriginSkuCode()))
                    .findFirst();
            // 校验商品批次属性
            if (optional.isPresent()) {
                SkuDTO skuDTO = optional.get();
                CheckSkuLotParam checkSkuLotParam = new CheckSkuLotParam();
                checkSkuLotParam.setSkuCode(skuDTO.getCode());
                checkSkuLotParam.setSkuQuality(excelImportTransferBO.getTargetSkuQuality());
                checkSkuLotParam.setExpireDate(excelImportTransferBO.getTargetExpireDateTime());
                checkSkuLotParam.setManufDate(excelImportTransferBO.getTargetProductDateTime());
                checkSkuLotParam.setProductionNo(excelImportTransferBO.getTargetProductNo());
                checkSkuLotParam.setReceiveDate(excelImportTransferBO.getTargetReceiveDateTime());
                checkSkuLotParam.setExternalLinkBillNo(excelImportTransferBO.getTargetExternalLinkBillNo());

                checkSkuLotParam.setInventoryType(excelImportTransferBO.getTargetInventoryType());
                checkSkuLotParam.setPalletCode(excelImportTransferBO.getTargetPalletCode());
                checkSkuLotParam.setBoxCode(excelImportTransferBO.getTargetBoxCode());
                checkSkuLotParam.setValidityCode(excelImportTransferBO.getTargetValidityCode());
                try {
                    skuLotBiz.verificationSkuLotParam(checkSkuLotParam, skuDTO);
                } catch (Exception e) {
                    errorCount++;
                    callBackPublicService(false, uid, lineNo, excelImportTransferBO, e.getMessage());
                }
            } else {
                // 商品不存在
                errorCount++;
                callBackPublicService(false, uid, lineNo, excelImportTransferBO, "商品不存在");
            }
            // 来源库位
            Optional<LocationDTO> locationDTOOptional = locationDTOList.stream().filter(locationDTO -> locationDTO.getCode().equals(excelImportTransferBO.getOriginLocationCode()))
                    .findFirst();
            if (locationDTOOptional.isPresent()) {
                LocationDTO locationDTO = locationDTOOptional.get();
                if (!locationDTO.getType().equals(LocationTypeEnum.LOCATION_TYPE_STORE.getType()) && !locationDTO.getType().equals(LocationTypeEnum.LOCATION_TYPE_PICK.getType())) {
                    errorCount++;
                    callBackPublicService(false, uid, lineNo, excelImportTransferBO, "来源库位必须是拣选位或者存储位");
                }
            } else {
                errorCount++;
                callBackPublicService(false, uid, lineNo, excelImportTransferBO, "来源库位不存在");
            }

            // 目标库位
            Optional<LocationDTO> targetLocationOptional = locationDTOList.stream().filter(locationDTO -> locationDTO.getCode().equals(excelImportTransferBO.getTargetLocationCode()))
                    .findFirst();
            if (targetLocationOptional.isPresent()) {
                LocationDTO locationDTO = targetLocationOptional.get();
                if (!locationDTO.getType().equals(LocationTypeEnum.LOCATION_TYPE_STORE.getType()) && !locationDTO.getType().equals(LocationTypeEnum.LOCATION_TYPE_PICK.getType())) {
                    errorCount++;
                    callBackPublicService(false, uid, lineNo, excelImportTransferBO, "目标库位必须是拣选位或者存储位");
                }
            } else {
                errorCount++;
                callBackPublicService(false, uid, lineNo, excelImportTransferBO, "目标库位库位不存在");
            }

            // 批次信息校验
            if (skuLotDTOList.stream().noneMatch(skuLotDTO -> skuLotDTO.getCode().equals(excelImportTransferBO.getOriginSkuLotNo()) && skuLotDTO.getSkuCode().equals(excelImportTransferBO.getOriginSkuCode()))) {
                errorCount++;
                callBackPublicService(false, uid, lineNo, excelImportTransferBO, "来源批次与商品不匹配");
            }
        }
        return errorCount > 0;
    }

    public static long parseDate(String dateTimeStr) {
        String dateStr = DateUtil.parseDate(dateTimeStr).toDateStr();
        return DateUtil.parseDate(dateStr).getTime();
    }
}