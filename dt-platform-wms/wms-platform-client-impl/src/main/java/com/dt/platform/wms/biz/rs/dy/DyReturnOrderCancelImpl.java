package com.dt.platform.wms.biz.rs.dy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.danding.business.client.rpc.config.facade.OwnerRpcFacade;
import com.danding.business.client.rpc.config.result.WarehouseInfoResult;
import com.danding.core.tenant.SimpleTenantHelper;
import com.dt.component.common.enums.rs.RSOrderStatusEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.domain.bill.param.rs.SalesReturnOrderParam;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import com.dt.platform.wms.rs.dy.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@DubboService
public class DyReturnOrderCancelImpl implements IDyReturnOrderCancel {

    public static final Result<String> SYSTEM_ERROR = new Result<>(100003, "系统错误", "系统错误");

    @Resource
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @DubboReference
    private OwnerRpcFacade ownerRpcFacade;

    private static final List<Integer> ALLOW_CANCEL_STATUS_LIST = ListUtil.toList(RSOrderStatusEnum.CREATED.getCode(),
            RSOrderStatusEnum.REGISTERED.getCode(),
            RSOrderStatusEnum.HANDOVER.getCode(),
            RSOrderStatusEnum.CHECKING.getCode()
    );

    @Override
    public Result<String> cancel(DyReturnOrderCancelRequest request) {
        try {
            SimpleTenantHelper.setTenantId(request.getTenantId());
            WarehouseInfoResult data = ownerRpcFacade.getUserIdByCode(request.getCargoCode()).getData();
            if (null == data) return Result.fail("实体仓不存在");
            RpcContextUtil.setWarehouseCode(data.getWarehouseCode());

            SalesReturnOrderParam param = new SalesReturnOrderParam();
            param.setSalesReturnOrderNo(request.getSalesReturnOrderNo());
            List<SalesReturnOrderDTO> salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(param);
            if (CollectionUtil.isEmpty(salesReturnOrderDTOList)) {
                return new Result<>(6004, "销售退货单不存在", "销售退货单不存在");
            }

            if (salesReturnOrderDTOList.stream().allMatch(it -> RSOrderStatusEnum.CANCEL.getCode().equals(it.getStatus()))) {
                return Result.success("业务处理成功");
            }

            SalesReturnOrderDTO salesReturnOrderDTO = salesReturnOrderDTOList.stream()
                    .filter(dto -> !RSOrderStatusEnum.CANCEL.getCode().equals(dto.getStatus()))
                    .findFirst().orElse(null);
            if (null == salesReturnOrderDTO) {
                return Result.success("业务处理成功");
            }

            if (ALLOW_CANCEL_STATUS_LIST.contains(salesReturnOrderDTO.getStatus())) {
                salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CANCEL.getCode());
                remoteSalesReturnOrderClient.modify(salesReturnOrderDTO);
                return Result.success("业务处理成功");
            }

            return Result.fail("取消失败");
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            return SYSTEM_ERROR;
        }

    }
}
