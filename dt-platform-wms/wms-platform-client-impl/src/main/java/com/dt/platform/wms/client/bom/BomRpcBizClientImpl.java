package com.dt.platform.wms.client.bom;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.bom.BomOriginEnum;
import com.dt.component.common.enums.bom.BomPlanBusinessTypeEnum;
import com.dt.component.common.enums.bom.BomPlanStatusEnum;
import com.dt.component.common.enums.bom.BomTypeEnum;
import com.dt.component.common.enums.pre.BillLogTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.bill.dto.bom.*;
import com.dt.domain.bill.param.bom.BomPlanParam;
import com.dt.domain.bill.param.bom.BomPlanStockDetailParam;
import com.dt.domain.bill.param.bom.BomSkuDetailParam;
import com.dt.domain.bill.param.bom.BomSkuParam;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.integration.IRemoteCargoOwnerClient;
import com.dt.platform.wms.integration.IRemoteSeqRuleClient;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.integration.bom.*;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.rpc.client.bom.IBomRpcClient;
import com.dt.platform.wms.rpc.dto.BomPlanStockDetailRpcDTO;
import com.dt.platform.wms.rpc.dto.BomRpcDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class BomRpcBizClientImpl implements IBomRpcClient {

    @Resource
    private IRemoteBomIssueClient remoteBomIssuedClient;
    @Autowired
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;
    @Autowired
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;
    @Autowired
    private IRemoteBomPlanClient remoteBomPlanClient;
    @Autowired
    private IRemoteBomPlanStockDetailClient remoteBomPlanStockDetailClient;
    @Autowired
    private IRemoteSkuLotClient remoteSkuLotClient;
    @Autowired
    private IRemoteBomSkuClient remoteBomSkuClient;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;
    @Resource
    private IRemoteSkuClient remoteSkuClient;
    @Resource
    private IRemoteBillLogClient remoteBillLogClient;
    @Resource
    private IRemoteBomSkuDetailClient remoteBomSkuDetailClient;


    @Override
    public Result<Boolean> assemblyIssue(BomRpcDTO bomRpcDTO) {
        String originData = JSONUtil.toJsonStr(bomRpcDTO);
        log.info("assemblyIssue param:{}", originData);
        if (null == bomRpcDTO) return Result.fail(-1, "参数不正确", false);
        if (StrUtil.isBlank(bomRpcDTO.getWarehouseCode())) return Result.fail(-1, "参数不正确", false);

        if (!defaultWarehouseCodeConfig.getWarehouseCodeList().contains(bomRpcDTO.getWarehouseCode())) {
            return Result.fail(-1, "参数不正确", false);
        }
        RpcContextUtil.setWarehouseCode(bomRpcDTO.getWarehouseCode());

        if (CollectionUtil.isEmpty(bomRpcDTO.getOrderDetailFromList())) return Result.fail(-1, "参数不正确", false);
        if (StrUtil.isBlank(bomRpcDTO.getCargoCode())) return Result.fail(-1, "参数不正确", false);

        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(bomRpcDTO.getCargoCode());
        if (cargoOwnerDTO == null) {
            return Result.fail(-1, "参数不正确", false);
        }
        if (StrUtil.isBlank(bomRpcDTO.getExtJson())) return Result.fail(-1, "参数不正确", false);
        String extJson = bomRpcDTO.getExtJson();
        JSONObject jsonObject = JSONUtil.parseObj(extJson);
        String logicWarehouseCode = jsonObject.getStr("logicWarehouseCode");
        if (StrUtil.isBlank(logicWarehouseCode)) return Result.fail(-1, "参数不正确", false);

        if (StrUtil.isBlank(bomRpcDTO.getOrderNo())) return Result.fail(-1, "参数不正确", false);
        if (StrUtil.isBlank(bomRpcDTO.getOrderType())) return Result.fail(-1, "参数不正确", false);
        if (bomRpcDTO.getParentQty() == null || bomRpcDTO.getParentQty().compareTo(BigDecimal.ZERO) <= 0)
            return Result.fail(-1, "参数不正确", false);

        List<BomRpcDTO.BomDetail> orderDetailFromList = bomRpcDTO.getOrderDetailFromList();
        List<String> lineNoList = orderDetailFromList.stream().map(BomRpcDTO.BomDetail::getLineNo)
                .distinct().collect(Collectors.toList());
        if (lineNoList.size() != orderDetailFromList.size()) {
            return Result.fail(-1, "行号重复", false);
        }
        List<String> skuList = orderDetailFromList.stream().map(BomRpcDTO.BomDetail::getSku)
                .distinct().collect(Collectors.toList());
        if (skuList.size() != orderDetailFromList.size()) {
            return Result.fail(-1, "子商品重复", false);
        }
        for (BomRpcDTO.BomDetail bomDetail : orderDetailFromList) {
            if (StrUtil.isBlank(bomDetail.getSku())) return Result.fail(-1, "参数不正确", false);
            if (bomDetail.getPlanQuantity() == null || bomRpcDTO.getParentQty().compareTo(BigDecimal.ZERO) <= 0)
                return Result.fail(-1, "参数不正确", false);

            // 验证能否均分
            if (bomDetail.getPlanQuantity().remainder(bomRpcDTO.getParentQty()).compareTo(BigDecimal.ZERO) != 0) {
                return Result.fail(-1, "子项商品数量不成正比", false);
            }
        }
        RLock lock = redissonClient.getLock("dt_wms_one_assembly_issue_lock:" + bomRpcDTO.getOrderNo());
        RLock bomLock = redissonClient.getLock("dt_wms_one_assembly_issue_bom_lock:" + bomRpcDTO.getParentSku());
        try {
            boolean tryLock = lock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!tryLock) {
                return Result.fail(-1, "请稍后再试", false);
            }
            boolean bomTryLock = bomLock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!bomTryLock) {
                return Result.fail(-1, "请稍后再试", false);
            }
            BomIssueDTO bomIssueDTO = BeanUtil.copyProperties(bomRpcDTO, BomIssueDTO.class);


            RpcContextUtil.setWarehouseCode(bomIssueDTO.getWarehouseCode());
            bomIssueDTO.setPlanCode(remoteSeqRuleClient.findSequence(SeqEnum.BOM_PLAN_000001));
            BomPlanBusinessTypeEnum businessTypeEnum = BomPlanBusinessTypeEnum.fromCode(bomIssueDTO.getOrderType());
            if (businessTypeEnum == null) {
                return Result.fail(-1, "业务类型错误", false);
            }
            BomPlanParam bomPlanParam = new BomPlanParam();
            bomPlanParam.setCargoCode(bomIssueDTO.getCargoCode());
            bomPlanParam.setOutNo(bomIssueDTO.getOrderNo());
            BomPlanDTO bomPlanDTO = remoteBomPlanClient.get(bomPlanParam);
            if (bomPlanDTO != null) {
                return Result.fail(-1, "外部单号已存在", false);
            }

            SkuParam param = new SkuParam();
            param.setCargoCode(bomIssueDTO.getCargoCode());
            param.setCode(bomIssueDTO.getParentSku());
            param.setWarehouseCode(bomIssueDTO.getWarehouseCode());
            SkuDTO skuDTO = remoteSkuClient.get(param);
            if (skuDTO == null) {
                return Result.fail(-1, String.format("成品商品不存在:%s", bomIssueDTO.getParentSku()), false);
            }
            //商品名称，条码，taxType
            List<SkuUpcDTO> skuUpcList = skuDTO.getSkuUpcList();
            if (!CollectionUtils.isEmpty(skuUpcList)) {
                List<SkuUpcDTO> collect = skuUpcList.stream()
                        .filter(upc -> Objects.equals(SkuUpcDefaultEnum.YES.getStatus(), upc.getIsDefault()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    bomIssueDTO.setUpcCode(collect.get(0).getUpcCode());
                }
            }
            bomIssueDTO.setBomName(skuDTO.getName());
            bomIssueDTO.setTaxType(skuDTO.getType());

            SkuParam childGoodsParam = new SkuParam();
            childGoodsParam.setWarehouseCode(bomIssueDTO.getWarehouseCode());
            childGoodsParam.setCargoCode(bomIssueDTO.getCargoCode());
            List<String> list = bomIssueDTO.getOrderDetailFromList().stream().map(BomIssueDTO.BomDetail::getSku).collect(Collectors.toList());
            Set<String> childSkuSet = new HashSet<>(list);
            if (childSkuSet.size() != list.size()) {
                return Result.fail(-1, "子商品重复", false);
            }
            childGoodsParam.setCodeList(list);
            List<SkuDTO> childList = remoteSkuClient.getList(childGoodsParam);
            Map<String, SkuDTO> skuDTOMap = childList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity(), (k1, k2) -> k1));
            for (BomIssueDTO.BomDetail bomDetail : bomIssueDTO.getOrderDetailFromList()) {
                SkuDTO sku = skuDTOMap.get(bomDetail.getSku());
                if (sku == null) {
                    return Result.fail(-1, String.format("子商品不存在:%s", bomDetail.getSku()), false);
                }
                SkuUpcParam upcParam = new SkuUpcParam();
                upcParam.setSkuCode(bomDetail.getSku());
                upcParam.setWarehouseCode(bomIssueDTO.getWarehouseCode());
                upcParam.setCargoCode(bomIssueDTO.getCargoCode());
                List<SkuUpcDTO> childSkuUpcList = remoteSkuClient.getSkuUpcList(upcParam);
                if (!CollectionUtils.isEmpty(childSkuUpcList)) {
                    List<SkuUpcDTO> collect = childSkuUpcList.stream()
                            .filter(upc -> Objects.equals(SkuUpcDefaultEnum.YES.getStatus(), upc.getIsDefault()))
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)) {
                        bomDetail.setUpcCode(collect.get(0).getUpcCode());
                    }
                }
                bomDetail.setName(sku.getName());
            }

            BomSkuParam query = new BomSkuParam()
                    .setBomSkuCode(bomIssueDTO.getParentSku())
                    .setCargoCode(bomIssueDTO.getCargoCode());
            BomSkuDTO bomSkuDTO = remoteBomSkuClient.get(query);
            boolean newBom = false;
            if (bomSkuDTO != null) {
                // 校验bom的组成商品是否和数据库中的一样，且比例是否正确
                BomSkuDetailParam skuDetailParam = new BomSkuDetailParam();
                skuDetailParam.setBomSkuCode(bomIssueDTO.getParentSku());
                skuDetailParam.setCargoCode(bomIssueDTO.getCargoCode());
                List<BomSkuDetailDTO> dbBomDetails = remoteBomSkuDetailClient.getList(skuDetailParam);

                List<String> dbChildSku = dbBomDetails.stream().map(BomSkuDetailDTO::getChildSkuCode).collect(Collectors.toList());
                List<String> paramChildSku = bomIssueDTO.getOrderDetailFromList().stream().map(BomIssueDTO.BomDetail::getSku).collect(Collectors.toList());
                // 如果两个sku不是完全相等则报错
                if (!(CollUtil.containsAll(dbChildSku, paramChildSku) && CollUtil.containsAll(paramChildSku, dbChildSku))) {
                    return Result.fail(-1, "bom组成商品不一致", false);
                }
                // 若sku种类都相等则验证比例
                Map<String, BomIssueDTO.BomDetail> collect = bomIssueDTO.getOrderDetailFromList().stream().collect(Collectors.toMap(BomIssueDTO.BomDetail::getSku, Function.identity(), (k1, k2) -> k1));
                for (BomSkuDetailDTO bomSkuDetailDTO : dbBomDetails) {
                    BomIssueDTO.BomDetail bomDetail = collect.get(bomSkuDetailDTO.getChildSkuCode());
                    if (bomSkuDetailDTO.getSkuQty().compareTo(bomDetail.getPlanQuantity().divide(bomIssueDTO.getParentQty(), 0, RoundingMode.HALF_UP)) != 0) {
                        return Result.fail(-1, "bom组成商品比例不一致", false);
                    }
                }
            } else {
                newBom = true;
                addBom(bomIssueDTO);
            }
            addPlan(bomIssueDTO);


            Result<Boolean> issue = remoteBomIssuedClient.issue(bomIssueDTO);

            if (issue.checkSuccess()) {
                BillLogDTO logDTO = new BillLogDTO();
                logDTO.setCargoCode(bomIssueDTO.getCargoCode())
                        .setWarehouseCode(bomIssueDTO.getWarehouseCode())
                        .setOpContent("erp下发")
                        .setOpRemark("erp下发:" + originData)
                        .setOpBy("erp")
                        .setOpDate(System.currentTimeMillis())
                        .setBillType(BillLogTypeEnum.BOM_SKU.getType());
                // 新增bom
                if (newBom) {
                    logDTO.setBillType(BillLogTypeEnum.BOM_SKU.getType());
                    logDTO.setBillNo(bomIssueDTO.getParentSku());
                    remoteBillLogClient.save(logDTO);
                }
                logDTO.setBillNo(bomIssueDTO.getPlanCode());
                logDTO.setBillType(BillLogTypeEnum.BOM_PLAN.getType());
                remoteBillLogClient.save(logDTO);
            } else {
                return Result.fail(-1, "下发失败", false);
            }
            return Result.success(true);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
            if (bomLock.isLocked()) {
                if (bomLock.isHeldByCurrentThread()) {
                    bomLock.unlock();
                }
            }
        }
    }

    private void addBom(BomIssueDTO bomIssueDTO) {

        // 新增bom
        List<String> skuJoinStr = bomIssueDTO.getOrderDetailFromList().stream().map(bomDetail -> {
            return String.format("%s%s", bomDetail.getSku(), bomDetail.getPlanQuantity().setScale(0, BigDecimal.ROUND_HALF_UP));
        }).sorted(Comparator.comparing(String::new)).collect(Collectors.toList());

        BomSkuDTO bom = new BomSkuDTO();
        bom.setCargoCode(bomIssueDTO.getCargoCode());
        bom.setBomSkuCode(bomIssueDTO.getParentSku());
        bom.setBomUpcCode(bomIssueDTO.getUpcCode());
        bom.setBomSkuName(bomIssueDTO.getBomName());
        bom.setBomSkuStructure(SecureUtil.md5(skuJoinStr.stream().collect(Collectors.joining("#"))));
        bom.setBomType(BomTypeEnum.FINISHED_PRODUCT.getCode());
        bom.setTaxType(bomIssueDTO.getTaxType());
        bom.setRemark(bomIssueDTO.getRemark());
        bom.setWarehouseCode(bomIssueDTO.getWarehouseCode());

        bomIssueDTO.setBomSkuDTO(bom);

        List<BomSkuDetailDTO> saveDetail = new ArrayList<>();
        for (BomIssueDTO.BomDetail bomDetail : bomIssueDTO.getOrderDetailFromList()) {
            BomSkuDetailDTO bomSkuDetail = new BomSkuDetailDTO();
            bomSkuDetail.setWarehouseCode(bomIssueDTO.getWarehouseCode());
            bomSkuDetail.setCargoCode(bomIssueDTO.getCargoCode());
            bomSkuDetail.setBomSkuCode(bomIssueDTO.getParentSku());
            bomSkuDetail.setChildSkuCode(bomDetail.getSku());
            bomSkuDetail.setChildUpcCode(bomDetail.getUpcCode());
            bomSkuDetail.setChildSkuName(bomDetail.getName());
            bomSkuDetail.setSkuQty(bomDetail.getPlanQuantity().divide(bomIssueDTO.getParentQty(), 0, RoundingMode.HALF_UP));
            saveDetail.add(bomSkuDetail);
        }
        bomIssueDTO.setBomSkuDetailDTOList(saveDetail);
    }

    private void addPlan(BomIssueDTO bomIssueDTO) {
        // 生产计划
        BomPlanDTO bomPlan = new BomPlanDTO();
        bomPlan.setCargoCode(bomIssueDTO.getCargoCode());
        bomPlan.setBomPlanCode(bomIssueDTO.getPlanCode());
        bomPlan.setOutNo(bomIssueDTO.getOrderNo());
        bomPlan.setBusinessType(BomPlanBusinessTypeEnum.fromCode(bomIssueDTO.getOrderType()).getCode());
        bomPlan.setBomSkuCode(bomIssueDTO.getParentSku());
        bomPlan.setBomUpcCode(bomIssueDTO.getUpcCode());
        bomPlan.setBomSkuName(bomIssueDTO.getBomName());
        bomPlan.setBomSkuQty(bomIssueDTO.getParentQty());
        bomPlan.setCompleteSkuQty(new BigDecimal("0"));
        if (StringUtils.isEmpty(bomIssueDTO.getOrigSystem())) {
            bomPlan.setOriginFrom(BomOriginEnum.JIND.getCode());
        } else {
            bomPlan.setOriginFrom(bomIssueDTO.getOrigSystem());
        }
        bomPlan.setStatus(BomPlanStatusEnum.CREATED.getCode());
        bomPlan.setRemark(bomIssueDTO.getRemark());
        bomPlan.setWarehouseCode(bomIssueDTO.getWarehouseCode());
        bomPlan.setExtraJson(bomIssueDTO.getExtJson());

        bomIssueDTO.setBomPlanDTO(bomPlan);

        List<BomPlanDetailDTO> bomPlanDetailList = new ArrayList<>();

        for (BomIssueDTO.BomDetail bomDetail : bomIssueDTO.getOrderDetailFromList()) {
            BomPlanDetailDTO bomPlanDetail = new BomPlanDetailDTO();
            bomPlanDetail.setCargoCode(bomIssueDTO.getCargoCode());
            bomPlanDetail.setLineNo(bomDetail.getLineNo());
            bomPlanDetail.setBomPlanCode(bomIssueDTO.getPlanCode());
            bomPlanDetail.setBomSkuCode(bomIssueDTO.getParentSku());
            bomPlanDetail.setChildSkuCode(bomDetail.getSku());
            bomPlanDetail.setChildUpcCode(bomDetail.getUpcCode());
            bomPlanDetail.setChildSkuName(bomDetail.getName());
            bomPlanDetail.setExpireDate(bomDetail.getExpDate());
            bomPlanDetail.setSkuQty(bomDetail.getPlanQuantity());
            bomPlanDetail.setWarehouseCode(bomIssueDTO.getWarehouseCode());
            bomPlanDetail.setExtraJson(bomDetail.getExtJson());
            bomPlanDetailList.add(bomPlanDetail);
        }
        bomIssueDTO.setBomPlanDetailDTOS(bomPlanDetailList);
    }

    @Override
    public Result<BomPlanStockDetailRpcDTO> getBomPlanStockDetail(String planCode) {

        // todo 计划单

        if (StrUtil.isBlank(planCode)) throw ExceptionUtil.ARG_ERROR;

        BomPlanStockDetailRpcDTO bomPlanStockDetailDTO = new BomPlanStockDetailRpcDTO();


        List<BomPlanStockDetailRpcDTO.BomPlanDetailDTO> bomPlanDetailDTOList = new ArrayList<>();
        bomPlanStockDetailDTO.setBomPlanDetailDTOList(bomPlanDetailDTOList);

        BomPlanParam param = new BomPlanParam();
        param.setBomPlanCode(planCode);
        BomPlanDTO bomPlanDTO = remoteBomPlanClient.get(param);
        if (bomPlanDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "作业单据不存在,请核查:" + planCode);
        }

        BomPlanStockDetailParam detailParam = new BomPlanStockDetailParam();
        detailParam.setBomPlanCode(planCode);
        List<BomPlanStockDetailDTO> list = remoteBomPlanStockDetailClient.getList(detailParam);

        if (CollectionUtil.isEmpty(list)) {
            throw new BaseException(BaseBizEnum.TIP, "作业单据库存操作记录不存在,请核查:" + planCode);
        }

        bomPlanStockDetailDTO.setCargoCode(bomPlanStockDetailDTO.getCargoCode());

        String extraJson = bomPlanDTO.getExtraJson();
        if (StrUtil.isNotBlank(extraJson)) {
            JSONObject jsonObject = JSONUtil.parseObj(extraJson);
            String logicWarehouseCode = jsonObject.getStr("logicWarehouseCode");
            bomPlanStockDetailDTO.setLogicWarehouseCode(logicWarehouseCode);
        }


        // 主商品记录按bom最小单元编号去重
        Map<String, BomPlanStockDetailDTO> collect = list.stream().collect(Collectors.toMap(BomPlanStockDetailDTO::getBomMinUnit, Function.identity(), (v1, v2) -> v1));

        List<String> bomLotList = list.stream()
                .map(BomPlanStockDetailDTO::getBomSkuLotNo)
                .filter(StrUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        Map<String, SkuLotDTO> lotDTOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(bomLotList)) {
            SkuLotParam skuLotParam = new SkuLotParam();
            skuLotParam.setCodeList(bomLotList);
            lotDTOMap = remoteSkuLotClient.skuLotMap(bomLotList);
        }

        for (Map.Entry<String, BomPlanStockDetailDTO> entry : collect.entrySet()) {

            BomPlanStockDetailDTO value = entry.getValue();
            BomPlanStockDetailRpcDTO.BomPlanDetailDTO detail = new BomPlanStockDetailRpcDTO.BomPlanDetailDTO();
            detail.setSkuCode(value.getBomSkuCode());
            detail.setSkuLotNo(value.getBomSkuLotNo());
            detail.setUpdateNum(value.getCompleteQty());
            detail.setCreateTime(value.getCreatedTime());
            detail.setUpdateTime(value.getUpdatedTime());
            if (BomPlanBusinessTypeEnum.ZZ.getCode().equals(bomPlanDTO.getBusinessType())) {
                detail.setOperateType(1);
            } else if (BomPlanBusinessTypeEnum.CX.getCode().equals(bomPlanDTO.getBusinessType())) {
                detail.setOperateType(2);
            }

            if (StrUtil.isBlank(value.getBomSkuLotNo())) {
                SkuLotDTO skuLotDTO = lotDTOMap.get(value.getBomSkuLotNo());
                if (skuLotDTO != null) {
                    detail.setProductDate(skuLotDTO.getManufDate());
                    detail.setExpireDate(skuLotDTO.getExpireDate());
                    detail.setInventoryType(SkuQualityEnum.SKU_QUALITY_AVL.getLevel().equals(skuLotDTO.getSkuQuality()) ? 1 : 2);
                }
            }
            bomPlanDetailDTOList.add(detail);
        }

        // 子商品组装
        List<String> childLotList = list.stream()
                .map(BomPlanStockDetailDTO::getSkuLotNo)
                .filter(StrUtil::isNotBlank)
                .distinct().collect(Collectors.toList());

        Map<String, SkuLotDTO> childLotDTOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(bomLotList)) {
            SkuLotParam skuLotParam = new SkuLotParam();
            skuLotParam.setCodeList(childLotList);
            childLotDTOMap = remoteSkuLotClient.skuLotMap(bomLotList);
        }

        for (BomPlanStockDetailDTO planStockDetailDTO : list) {
            BomPlanStockDetailRpcDTO.BomPlanDetailDTO detail = new BomPlanStockDetailRpcDTO.BomPlanDetailDTO();

            detail.setSkuCode(planStockDetailDTO.getChildSkuCode());
            detail.setSkuLotNo(planStockDetailDTO.getSkuLotNo());
//            detail.setPlanSkuNum();
            detail.setUpdateNum(planStockDetailDTO.getSkuQty());
            detail.setCreateTime(planStockDetailDTO.getCreatedTime());
            detail.setUpdateTime(planStockDetailDTO.getUpdatedTime());

            if (StrUtil.isBlank(planStockDetailDTO.getSkuLotNo())) {
                SkuLotDTO skuLotDTO = childLotDTOMap.get(planStockDetailDTO.getSkuLotNo());
                if (skuLotDTO != null) {
                    detail.setProductDate(skuLotDTO.getManufDate());
                    detail.setExpireDate(skuLotDTO.getExpireDate());
                    detail.setInventoryType(SkuQualityEnum.SKU_QUALITY_AVL.getLevel().equals(skuLotDTO.getSkuQuality()) ? 1 : 2);
                }
            }
            if (BomPlanBusinessTypeEnum.ZZ.getCode().equals(bomPlanDTO.getBusinessType())) {
                detail.setOperateType(2);
            } else if (BomPlanBusinessTypeEnum.CX.getCode().equals(bomPlanDTO.getBusinessType())) {
                detail.setOperateType(1);
            }
            bomPlanDetailDTOList.add(detail);
        }
        return Result.success(bomPlanStockDetailDTO);
    }
}
