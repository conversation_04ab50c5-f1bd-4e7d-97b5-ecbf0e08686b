
package com.dt.platform.wms.biz.rs;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.danding.business.oms.common.BO.ChannelOrderV2DTO;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.component.common.enums.rs.RSBillSourceEnum;
import com.dt.component.common.enums.rs.RSOrderStatusEnum;
import com.dt.component.common.enums.rs.RSReturnTypeEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.bill.client.rs.bo.SalesReturnOrderBO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDetailDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderExtraDTO;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.domain.bill.param.rs.SalesReturnOrderParam;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.biz.taotian.*;
import com.dt.platform.wms.biz.taotian.TTReturnOrderCreateRequest.OrderLine;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.impl.CallOtherSupport;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.oms.IRemoteOmsOrderClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import com.dt.platform.wms.integration.warehouseManage.IRemoteLogicWarehouseClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class TTReturnOrderBizImpl implements TTReturnOrderBiz {

    private final List<Integer> allowCancelStatus = Stream.of(RSOrderStatusEnum.CREATED, RSOrderStatusEnum.REGISTERED, RSOrderStatusEnum.HANDOVER, RSOrderStatusEnum.CHECKING).map(RSOrderStatusEnum::getCode)
            .collect(Collectors.toList());


    @Resource
    private IRemoteLockSupportClient remoteLockSupportClient;

    @Autowired
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Autowired
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Autowired
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Autowired
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Autowired
    private IRemoteSkuClient remoteSkuClient;

    @Autowired
    private IRemoteBillLogClient remoteBillLogClient;

    @Resource
    private IRemoteLogicWarehouseClient remoteLogicWarehouseClient;

    @Autowired
    private IRemoteOmsOrderClient remoteOmsOrderClient;

    @Autowired
    private RemoteTenantHelper remoteTenantHelper;

    @Override
    public String returnOrderCreate(String requestStr) {
        log.info("returnOrderCreate param {}", requestStr);
        TTReturnOrderCreateRequest request = JSONUtil.toBean(requestStr, TTReturnOrderCreateRequest.class);
        TTReturnOrderCreateResponse response = returnOrderCreateInner(request);
        log.info("returnOrderCreate result {}", response);
        return JSONUtil.toJsonStr(response);
    }

    @Override
    public String returnOrderAdditional(String requestStr) {
        log.info("returnOrderAdditional param {}", requestStr);
        TTAdditionalInstructionReq req = JSONUtil.toBean(requestStr, TTAdditionalInstructionReq.class);
        TTAdditionalInstructionResp response = returnOrderAdditionalInner(req);
        log.info("returnOrderAdditional result {}", response);
        return JSONUtil.toJsonStr(response);
    }

    @Override
    public String returnOrderHold(String requestStr) {
        log.info("returnOrderHold param {}", requestStr);
        TTReturnOrderHoldRequest req = JSONUtil.toBean(requestStr, TTReturnOrderHoldRequest.class);
        TTReturnOrderHoldResponse ttReturnOrderHoldResponse = returnOrderHoldInner(req);
        log.info("returnOrderHold result {}", ttReturnOrderHoldResponse);
        return JSONUtil.toJsonStr(ttReturnOrderHoldResponse);
    }

    @Override
    public String returnOrderCancel(String requestStr) {
        log.info("returnOrderCancel param {}", requestStr);
        TTOrderCancelRequest req = JSONUtil.toBean(requestStr, TTOrderCancelRequest.class);
        TTOrderCancelResponse response = returnOrderCancelInner(req);
        log.info("returnOrderCancel result {}", response);
        return JSONUtil.toJsonStr(response);
    }

    private TTOrderCancelResponse returnOrderCancelInner(TTOrderCancelRequest request) {
        return remoteLockSupportClient.execute(() -> {
            RpcContextUtil.setWarehouseCode(request.getWarehouseCode());
            TTOrderCancelResponse ttOrderCancelResponse = new TTOrderCancelResponse();
            ttOrderCancelResponse.setCode("success");

            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            salesReturnOrderParam.setSalesReturnOrderNo(request.getOrderCode());
            salesReturnOrderParam.setCargoCode(request.getOwnerCode());
            SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
            if (null == salesReturnOrderDTO) {
                ttOrderCancelResponse.setCode("WMS_RETRY_ORDER_NOT_EXIST_ERROR");
                ttOrderCancelResponse.setMessage("销退单不存在");
                return ttOrderCancelResponse;
            }
            if (RSOrderStatusEnum.CANCEL.getCode().equals(salesReturnOrderDTO.getStatus())) {
                return ttOrderCancelResponse;
            }

            if (allowCancelStatus.contains(salesReturnOrderDTO.getStatus())) {
                salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CANCEL.getCode());
                remoteSalesReturnOrderClient.buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
                remoteSalesReturnOrderClient.modify(salesReturnOrderDTO);
                BillLogDTO billLogDTO = remoteSalesReturnOrderClient.billLogDTO(salesReturnOrderDTO, "已取消", "淘天取消", "系统");
                remoteBillLogClient.save(billLogDTO);
            } else {
                ttOrderCancelResponse.setCode("WMS_RETRY_STATUS_VALIDATION_FAIL");
                ttOrderCancelResponse.setMessage("销退单无法取消");
                return ttOrderCancelResponse;
            }

            return ttOrderCancelResponse;
        }, ListUtil.toList(request.getOrderCode()));
    }

    private TTAdditionalInstructionResp returnOrderAdditionalInner(TTAdditionalInstructionReq request) {
        return remoteLockSupportClient.execute(() -> {
            RpcContextUtil.setWarehouseCode(request.getWarehouseCode());
            TTAdditionalInstructionResp ttAdditionalInstructionResp = new TTAdditionalInstructionResp();
            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            salesReturnOrderParam.setSalesReturnOrderNo(request.getOrderCode());
            salesReturnOrderParam.setCargoCode(request.getOwnerCode());

            SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
            if (null == salesReturnOrderDTO) throw ExceptionUtil.exceptionWithMessage("销退单不存在");

            SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();

            if ("RETURN_ON_SHELF_HOLD".equalsIgnoreCase(request.getInstructionType())) {
                salesReturnOrderExtraDTO.setHold(request.getInstructionType());
            } else {
                salesReturnOrderExtraDTO.setInstructionType(request.getInstructionType());
            }
            salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
            String content = StrUtil.join(StrUtil.COLON, "销退附件指令", request.getInstructionType());
            remoteSalesReturnOrderClient.modify(salesReturnOrderDTO);
            BillLogDTO billLogDTO = remoteSalesReturnOrderClient.billLogDTO(salesReturnOrderDTO, content, content, "系统");
            remoteBillLogClient.save(billLogDTO);
            return ttAdditionalInstructionResp;
        }, ListUtil.toList(request.getOrderCode()));
    }

    private TTReturnOrderHoldResponse returnOrderHoldInner(TTReturnOrderHoldRequest request) {
        TTReturnOrderHoldResponse ttReturnOrderHoldResponse = new TTReturnOrderHoldResponse();
        ttReturnOrderHoldResponse.setFlag("success");
        if (null == request) {
            log.error("request is null");
            return ttReturnOrderHoldResponse;
        }
        if (null == request.getReturnOrder()) {
            log.error("return order is null");
            return ttReturnOrderHoldResponse;
        }
        return remoteLockSupportClient.execute(() -> {
            RpcContextUtil.setWarehouseCode(request.getReturnOrder().getWarehouseCode());

            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            salesReturnOrderParam.setSalesReturnOrderNo(request.getReturnOrder().getReturnOrderCode());
            SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
            if (null == salesReturnOrderDTO) {
                log.error("return order not found");
                return ttReturnOrderHoldResponse;
            }

            SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
            salesReturnOrderExtraDTO.setHold(request.getReturnOrder().getOperateType());
            salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
            String content = StrUtil.join(StrUtil.COLON, "HOLD单指令下发", request.getReturnOrder().getOperateType());
            remoteSalesReturnOrderClient.modify(salesReturnOrderDTO);
            BillLogDTO billLogDTO = remoteSalesReturnOrderClient.billLogDTO(salesReturnOrderDTO, content, content, "系统");
            remoteBillLogClient.save(billLogDTO);
            return ttReturnOrderHoldResponse;
        }, ListUtil.toList(request.getReturnOrder().getReturnOrderCode()));
    }

    private TTReturnOrderCreateResponse returnOrderCreateInner(TTReturnOrderCreateRequest request) {
        if (null == request) throw ExceptionUtil.exceptionWithMessage("参数不能为空");
        TTReturnOrderCreateRequest.ReturnOrder returnOrder = request.getReturnOrder();
        List<OrderLine> orderLineList = request.getOrderLines();
        if (null == returnOrder) throw ExceptionUtil.exceptionWithMessage("销退单信息不能为空");

        // 淘天给过来的是退货仓编码
        String returnWarehouseCode = returnOrder.getWarehouseCode();
        String returnCargoCode = returnOrder.getOwnerCode();

        String preDeliveryOrderCode = returnOrder.getPreDeliveryOrderCode();
        if (StrUtil.isBlank(returnOrder.getReturnOrderCode()))
            throw ExceptionUtil.exceptionWithMessage("销退单号不能为空");
        if (StrUtil.isBlank(returnWarehouseCode))
            throw ExceptionUtil.exceptionWithMessage("仓库编码不能为空");
        if (StrUtil.isBlank(preDeliveryOrderCode))
            throw ExceptionUtil.exceptionWithMessage("正向单号信息不能为空");
        if (StrUtil.isBlank(returnCargoCode))
            throw ExceptionUtil.exceptionWithMessage("退货仓货主编码不能为空");
        if (CollectionUtil.isEmpty(orderLineList))
            throw ExceptionUtil.exceptionWithMessage("销退单详情不能为空");

        String returnOrderCode = returnOrder.getReturnOrderCode();
        return remoteLockSupportClient.execute(() -> {
            TTReturnOrderCreateResponse ttReturnOrderCreateResponse = new TTReturnOrderCreateResponse();
            ttReturnOrderCreateResponse.setReturnOrderId(returnOrderCode);
            ttReturnOrderCreateResponse.setFlag("success");

            // 仓库信息也要从退货仓查
            RpcContextUtil.setWarehouseCode(returnWarehouseCode);
            WarehouseDTO returnWarehouse = remoteWarehouseClient.queryByCode(returnWarehouseCode);
            if (null == returnWarehouse) throw ExceptionUtil.exceptionWithMessage("实体仓信息不存在");

            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            salesReturnOrderParam.setSalesReturnOrderNo(returnOrderCode);
            List<SalesReturnOrderDTO> salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(salesReturnOrderParam);
            if (CollectionUtil.isNotEmpty(salesReturnOrderDTOList)) {
                return ttReturnOrderCreateResponse;
            }

            // 拿到渠道订单信息
            remoteTenantHelper.setTenantId(returnWarehouseCode, returnCargoCode);
            Optional<ChannelOrderV2DTO> channelOrderDTOOptional = remoteOmsOrderClient.channelOrder(preDeliveryOrderCode);
            if (!channelOrderDTOOptional.isPresent()) {
                throw ExceptionUtil.exceptionWithMessage("渠道发货信息不存在");
            }

            // 渠道订单中仓库编码是云仓编码，需要调用erp获取实体仓编码
            ChannelOrderV2DTO channelOrderDTO = channelOrderDTOOptional.get();

            WarehouseDTO shipmentWarehouse = remoteLogicWarehouseClient.entityWarehouseCode(channelOrderDTO.getWarehouseCode());

            // 实际发货的实体仓编码
            String entityWarehouseCode = shipmentWarehouse.getCode();
            String entityWarehouseName = shipmentWarehouse.getName();

            // 退货货主跟正向货主不一样
            CargoOwnerDTO returnCargoOwnerDTO = remoteCargoOwnerClient.queryByCode(returnCargoCode);
            if (null == returnCargoOwnerDTO) throw ExceptionUtil.exceptionWithMessage("货主不存在");

            RpcContextUtil.setWarehouseCode(entityWarehouseCode);
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
            shipmentOrderParam.setPoNo(preDeliveryOrderCode);
            ShipmentOrderDTO shipmentOrderDTO = remoteShipmentOrderClient.get(shipmentOrderParam);
            if (null == shipmentOrderDTO) throw ExceptionUtil.exceptionWithMessage("发货信息不存在");
            String cargoCode = shipmentOrderDTO.getCargoCode(); // 正向货主编码

            // 切换到退货仓执行
            RpcContextUtil.setWarehouseCode(returnWarehouseCode);

            // 查询商品信息
            List<String> skuCodeList = orderLineList.stream().map(OrderLine::getItemCode).distinct().collect(Collectors.toList());
            SkuParam skuParam = new SkuParam();
            skuParam.setCargoCode(returnCargoCode);
            skuParam.setCodeList(skuCodeList);
            List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
            Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity()));

            // 条码信息
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setCargoCode(returnCargoCode);
            skuUpcParam.setSkuCodeList(skuCodeList);
            skuUpcParam.setIsDefault(SkuUpcDefaultEnum.YES.getStatus());
            List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            Map<String, SkuUpcDTO> skuUpcDTOMap = skuUpcList.stream().collect(Collectors.toMap(SkuUpcDTO::getSkuCode, Function.identity(), BinaryOperator.maxBy(Comparator.comparing(SkuUpcDTO::getId))));

            salesReturnOrderDTOList = new ArrayList<>();
            SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
            SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
            salesReturnOrderDTO.setSalesReturnOrderNo(returnOrderCode);
            salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CREATED.getCode());
            salesReturnOrderDTO.setCargoCode(returnCargoCode);
            salesReturnOrderExtraDTO.setShipmentCargoCode(cargoCode);
            salesReturnOrderDTO.setCargoName(returnCargoOwnerDTO.getName());
            salesReturnOrderDTO.setSaleShopId(shipmentOrderDTO.internalShopId());
            salesReturnOrderDTO.setShopNick(shipmentOrderDTO.getSaleShop());
            salesReturnOrderDTO.setWarehouseCode(returnWarehouseCode);
            salesReturnOrderDTO.setRealWarehouseCode(entityWarehouseCode);
            salesReturnOrderDTO.setRealWarehouseName(entityWarehouseName);
            salesReturnOrderDTO.setPoNo(preDeliveryOrderCode);
            salesReturnOrderDTO.setAfterSalesTrackingNo(returnOrderCode);
            salesReturnOrderDTO.setGlobalNo(channelOrderDTO.getGlobalSystemSn());
            salesReturnOrderDTO.setExpressNo(shipmentOrderDTO.getExpressNo());
            salesReturnOrderDTO.setCarrierCode(shipmentOrderDTO.getCarrierCode());
            salesReturnOrderDTO.setCarrierName(shipmentOrderDTO.getCarrierName());
            salesReturnOrderDTO.setReverseExpressNo(returnOrder.getExpressCode());
            salesReturnOrderDTO.setReverseCarrierCode(returnOrder.getLogisticsCode());
            salesReturnOrderDTO.setReverseCarrierName(returnOrder.getLogisticsName());
            salesReturnOrderDTO.setTaxType(shipmentOrderDTO.getTaxType());
            salesReturnOrderDTO.setLogicWarehouseCode(StrUtil.EMPTY);
            salesReturnOrderDTO.setClearanceTime(channelOrderDTO.getDeclareSuccessTime().getTime());
            if ("CUSTOMER_REFUSE".equalsIgnoreCase(returnOrder.getExtendProps().get("reverseOrderBizType").toString())) {
                salesReturnOrderDTO.setReturnType(RSReturnTypeEnum.CUSTOMER_REJECT.getCode());
            }
            if ("TMS_CUT".equalsIgnoreCase(returnOrder.getExtendProps().get("reverseOrderBizType").toString())) {
                salesReturnOrderDTO.setReturnType(RSReturnTypeEnum.TMS_CUT.getCode());
            }
            if ("CUSTOMER_RETURN".equalsIgnoreCase(returnOrder.getExtendProps().get("reverseOrderBizType").toString())) {
                salesReturnOrderDTO.setReturnType(RSReturnTypeEnum.CUSTOMER_RETURNS.getCode());
            }
            String source = CallOtherSupport.execute(it -> remoteLogicWarehouseClient.getTaoTianSalePlatformNameByOwner(it), returnCargoCode, "调用ERP查询货主对应平台");
            RSBillSourceEnum sourceEnum = null;
            if ("TMGJZY".equals(source)) {
                sourceEnum = RSBillSourceEnum.MALL_DIRECT;
            }
            if ("PTTMKLDJK".equals(source)) {
                sourceEnum = RSBillSourceEnum.MALL_PLATFORM;
            }
            if (null == sourceEnum) {
                throw ExceptionUtil.exceptionWithMessage("无法确定销售平台信息");
            } else {
                salesReturnOrderDTO.setBillSource(sourceEnum.getCode());
            }

            String returnReason = orderLineList.stream()
                    .map(OrderLine::getExtendProps)
                    .filter(ObjectUtil::isNotNull)
                    .map(it -> it.get("returnReason"))
                    .filter(ObjectUtil::isNotNull)
                    .map(Object::toString)
                    .filter(StrUtil::isNotBlank)
                    .findFirst().orElse(StrUtil.EMPTY);

            salesReturnOrderDTO.setReturnReason(returnReason);
            salesReturnOrderExtraDTO.setOrderType(returnOrder.getOrderType());
            salesReturnOrderExtraDTO.setSenderInfo(JSONUtil.toJsonStr(returnOrder.getSenderInfo()));
            salesReturnOrderExtraDTO.setExtendProps(JSONUtil.toJsonStr(returnOrder.getExtendProps()));
            salesReturnOrderExtraDTO.setPreDeliveryOrderCode(preDeliveryOrderCode);
            JSONObject extendProps = JSONUtil.parseObj(returnOrder.getExtendProps());
            String highRiskPackage = extendProps.getStr("highRiskPackage", "");
            String highRiskCustom = extendProps.getStr("highRiskCustom", "");
            String fulfilOutBizCode = extendProps.getStr("fulfilOutBizCode", "");
            String fulfilOrderCode = extendProps.getStr("fulfilOrderCode", "");
            salesReturnOrderExtraDTO.setHighRiskPackage(highRiskPackage);
            salesReturnOrderExtraDTO.setHighRiskCustom(highRiskCustom);
            salesReturnOrderExtraDTO.setFulfilOutBizCode(fulfilOutBizCode);
            salesReturnOrderDTO.setLpNo(fulfilOutBizCode);
            salesReturnOrderDTO.setMfcNo(fulfilOrderCode);

            List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList = new ArrayList<>();
            for (OrderLine orderLine : orderLineList) {
                SalesReturnOrderDetailDTO salesReturnOrderDetailDTO = new SalesReturnOrderDetailDTO();
                salesReturnOrderDetailDTO.setSalesReturnOrderNo(returnOrderCode);
                salesReturnOrderDetailDTO.setSkuCode(orderLine.getItemCode());
                salesReturnOrderDetailDTO.setLineSeq(orderLine.getOrderLineNo());
                Optional.ofNullable(skuDTOMap.get(orderLine.getItemCode())).ifPresent(skuDTO -> salesReturnOrderDetailDTO.setSkuName(skuDTO.getName()));
                Optional.ofNullable(skuUpcDTOMap.get(orderLine.getItemCode())).ifPresent(skuUpcDTO -> salesReturnOrderDetailDTO.setUpcCode(skuUpcDTO.getUpcCode()));
                salesReturnOrderDetailDTO.setExpectQty(BigDecimal.valueOf(orderLine.getPlanQty()));
                salesReturnOrderDetailDTO.setInventoryType(orderLine.getInventoryType());
                JSONObject jsonObject = JSONUtil.parseObj(orderLine.getExtendProps());
                String allowToEntryBonded = jsonObject.getStr("allowToEntryBonded", "");
                salesReturnOrderDetailDTO.setAllowEntry(allowToEntryBonded);
                salesReturnOrderDetailDTOList.add(salesReturnOrderDetailDTO);
            }

            salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
            salesReturnOrderDTOList.add(salesReturnOrderDTO);

            // 保存节点信息
            remoteSalesReturnOrderClient.buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());

            // 创建回告本地消息
            List<MessageMqDTO> messageMqDTOList = remoteSalesReturnOrderClient.messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_CREATE_CALLBACK);

            SalesReturnOrderBO salesReturnOrderBO = new SalesReturnOrderBO();
            salesReturnOrderBO.setSalesReturnOrderDTOList(salesReturnOrderDTOList);
            salesReturnOrderBO.setSalesReturnOrderDetailDTOList(salesReturnOrderDetailDTOList);
            salesReturnOrderBO.setMessageMqDTOList(messageMqDTOList);
            remoteSalesReturnOrderClient.save(salesReturnOrderBO);

            // 创建回告
            remoteSalesReturnOrderClient.callback(messageMqDTOList);

            // 日志
            BillLogDTO logDTO = remoteSalesReturnOrderClient.billLogDTO(salesReturnOrderDTO, "销退单创建成功", "销退单号：" + salesReturnOrderDTO.getSalesReturnOrderNo(), "系统");
            remoteBillLogClient.save(logDTO);
            return ttReturnOrderCreateResponse;
        }, ListUtil.toList(returnOrderCode));

    }
}
