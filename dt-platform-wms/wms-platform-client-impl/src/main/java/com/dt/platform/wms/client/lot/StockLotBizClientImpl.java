package com.dt.platform.wms.client.lot;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.core.stock.dto.StockStatisticDTO;
import com.dt.domain.stock.dto.lot.StockLotDTO;
import com.dt.domain.stock.param.lot.StockLotParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.LambdaHelpUtils;
import com.dt.platform.wms.dto.lot.StockLotBizDTO;
import com.dt.platform.wms.dto.stock.StockStatisticBizDTO;
import com.dt.platform.wms.integration.IRemoteCargoOwnerClient;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.integration.IRemoteWarehouseClient;
import com.dt.platform.wms.integration.lot.IRemoteStockLotClient;
import com.dt.platform.wms.param.lot.StockLotBizParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 批次库存 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class StockLotBizClientImpl implements IStockLotBizClient {

    @Resource
    private IRemoteStockLotClient remoteStockLotClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;


    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Override
    public Result<Boolean> save(StockLotBizDTO stockLotBizDTO) {
        StockLotDTO stockLotDTO = ConverterUtil.convert(stockLotBizDTO, StockLotDTO.class);
        Boolean result = remoteStockLotClient.save(stockLotDTO);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> saveBatch(List<StockLotBizDTO> stockLotBizDTOList) {
        List<StockLotDTO> stockLotDTOList = ConverterUtil.convertList(stockLotBizDTOList, StockLotDTO.class);
        Boolean result = remoteStockLotClient.saveBatch(stockLotDTOList);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> modify(StockLotBizDTO stockLotBizDTO) {
        StockLotDTO stockLotDTO = ConverterUtil.convert(stockLotBizDTO, StockLotDTO.class);
        Boolean result = remoteStockLotClient.modify(stockLotDTO);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> modifyBatch(List<StockLotBizDTO> stockLotBizDTOList) {
        List<StockLotDTO> stockLotDTOList = ConverterUtil.convertList(stockLotBizDTOList, StockLotDTO.class);
        Boolean result = remoteStockLotClient.modifyBatch(stockLotDTOList);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> checkExits(StockLotBizParam param) {
        StockLotParam stockLotParam = ConverterUtil.convert(param, StockLotParam.class);
        Boolean result = remoteStockLotClient.checkExits(stockLotParam);
        return Result.success(result);
    }

    @Override
    public Result<StockLotBizDTO> get(StockLotBizParam param) {
        StockLotParam stockLotParam = ConverterUtil.convert(param, StockLotParam.class);
        StockLotDTO stockLotDTO = remoteStockLotClient.get(stockLotParam);
        StockLotBizDTO result = ConverterUtil.convert(stockLotDTO, StockLotBizDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<StockLotBizDTO>> getList(StockLotBizParam param) {
        StockLotParam stockLotParam = ConverterUtil.convert(param, StockLotParam.class);
        List<StockLotDTO> list = remoteStockLotClient.getList(stockLotParam);
        List<StockLotBizDTO> result = ConverterUtil.convertList(list, StockLotBizDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<StockLotBizDTO>> getPage(StockLotBizParam stockLotBizParam) {
        StockLotParam stockLotParam = ConverterUtil.convert(stockLotBizParam, StockLotParam.class);
        if (stockLotBizParam.getExpireDateStart() != null && stockLotBizParam.getExpireDateStart() > 0) {
            stockLotParam.setExpireDateStartGE(stockLotBizParam.getExpireDateStart());
        }
        if (stockLotBizParam.getExpireDateEnd() != null && stockLotBizParam.getExpireDateEnd() > 0) {
            stockLotParam.setExpireDateEndLE(stockLotBizParam.getExpireDateEnd());
        }
        if (!CollectionUtils.isEmpty(stockLotBizParam.getUpcCodeList())) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCodeList(stockLotBizParam.getUpcCodeList());
            List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            if (CollectionUtils.isEmpty(skuUpcList)) {
                return Result.success(new Page<>());
            } else {
                if (CollectionUtils.isEmpty(stockLotBizParam.getSkuCodeList())) {
                    stockLotParam.setSkuCodeList(skuUpcList.stream().map(SkuUpcDTO::getSkuCode).distinct().collect(Collectors.toList()));
                } else {
                    List<String> skuCodeList = stockLotParam.getSkuCodeList();
                    skuCodeList.retainAll(skuUpcList.stream().map(SkuUpcDTO::getSkuCode).collect(Collectors.toList()));
                    if (CollectionUtils.isEmpty(skuCodeList)) {
                        return Result.success(new Page<>());
                    } else {
                        stockLotParam.setSkuCodeList(skuCodeList);
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(stockLotBizParam.getValidityCodeList())
                || !StringUtils.isEmpty(stockLotBizParam.getValidityCode())) {
            SkuLotParam skuLotParam = new SkuLotParam();
            skuLotParam.setValidityCode(stockLotBizParam.getValidityCode());
            skuLotParam.setValidityCodeList(stockLotBizParam.getValidityCodeList());

            skuLotParam.setCargoCodeList(stockLotBizParam.getCargoCodeList());
            skuLotParam.setSkuCodeList(stockLotBizParam.getSkuCodeList());
            skuLotParam.setCodeList(stockLotBizParam.getSkuLotNoList());
            skuLotParam.setExternalSkuLotNoList(stockLotBizParam.getExternalSkuLotNoList());

            List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getAppointMultipleParam(skuLotParam, LambdaHelpUtils.convertToFieldNameList(SkuLotDTO::getCode));
            if (CollectionUtils.isEmpty(skuLotDTOList)) {
                stockLotParam.setSkuLotNo("-----------------------");
            } else {
                stockLotParam.setSkuLotNoList(skuLotDTOList.stream().map(SkuLotDTO::getCode).collect(Collectors.toList()));
            }
        }

        stockLotParam.setHasPhysicalQty(true);
        Page<StockLotDTO> page = remoteStockLotClient.getPage(stockLotParam);
        Page<StockLotBizDTO> result = ConverterUtil.convertPage(page, StockLotBizDTO.class);
        if (result != null && !CollectionUtils.isEmpty(result.getRecords())) {
            //货主
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(result.getRecords().stream().map(StockLotBizDTO::getCargoCode).distinct().collect(Collectors.toList()));
            List<CargoOwnerDTO> allCargoOwner = remoteCargoOwnerClient.getAllCargoOwner(cargoOwnerParam);
            //仓库
            WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(CurrentRouteHolder.getWarehouseCode());
            //批次
            SkuLotParam skuLotParam = new SkuLotParam();
            skuLotParam.setCodeList(result.getRecords().stream().map(StockLotBizDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
            List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
            //批次
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setSkuCodeList(result.getRecords().stream().map(StockLotBizDTO::getSkuCode).distinct().collect(Collectors.toList()));
            List<SkuUpcDTO> skuUpcDTOList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            //商品
            SkuParam skuParam = new SkuParam();
            skuParam.setCodeList(result.getRecords().stream().map(StockLotBizDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
            List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
            result.getRecords().forEach(stockLotBizDTO -> {
                stockLotBizDTO.setWarehouseName(warehouseDTO != null ? warehouseDTO.getName() : "");
                allCargoOwner.stream()
                        .filter(a -> a.getCode().equalsIgnoreCase(stockLotBizDTO.getCargoCode()))
                        .findFirst().ifPresent(a -> stockLotBizDTO.setCargoName(a.getName()));

                skuLotDTOList.stream()
                        .filter(a -> a.getCode().equalsIgnoreCase(stockLotBizDTO.getSkuLotNo()))
                        .findFirst().ifPresent(a -> {
                            stockLotBizDTO.setExternalSkuLotNo(a.getExternalSkuLotNo());
                            stockLotBizDTO.setReceiveDate(a.getReceiveDate());
                            stockLotBizDTO.setManufDate(a.getManufDate());
                            stockLotBizDTO.setExpireDate(a.getExpireDate());
                            stockLotBizDTO.setProductionNo(a.getProductionNo());
                            stockLotBizDTO.setExternalLinkBillNo(a.getExternalLinkBillNo());

                            stockLotBizDTO.setPalletCode(a.getPalletCode());
                            stockLotBizDTO.setValidityCode(a.getValidityCode());
                            stockLotBizDTO.setBoxCode(a.getBoxCode());
                            stockLotBizDTO.setInventoryType(a.getInventoryType());
                            stockLotBizDTO.setExtraJson(a.getExtraJson());
                            stockLotBizDTO.setInventoryTypeDesc(InventoryTypeEnum.desc(a.getInventoryType()));
                        });

                skuDTOList.stream()
                        .filter(a -> a.getCode().equalsIgnoreCase(stockLotBizDTO.getSkuCode()))
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(stockLotBizDTO.getCargoCode()))
                        .findFirst().ifPresent(a -> stockLotBizDTO.setSkuName(a.getName()));

                skuUpcDTOList.stream()
                        .filter(a -> a.getSkuCode().equalsIgnoreCase(stockLotBizDTO.getSkuCode()))
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(stockLotBizDTO.getCargoCode()))
                        .filter(a -> a.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus()))
                        .findFirst().ifPresent(a -> stockLotBizDTO.setUpcCode(a.getUpcCode()));
            });
        }
        return Result.success(result);
    }

    @Override
    public Result<StockStatisticBizDTO> getStatistic(StockLotBizParam param) {
        StockLotParam stockLotParam = new StockLotParam();
        BeanUtil.copyProperties(param, stockLotParam);
        if (param.getExpireDateStart() != null && param.getExpireDateStart() > 0) {
            stockLotParam.setExpireDateStartGE(param.getExpireDateStart());
        }
        if (param.getExpireDateEnd() != null && param.getExpireDateEnd() > 0) {
            stockLotParam.setExpireDateEndLE(param.getExpireDateEnd());
        }
        if (!CollectionUtils.isEmpty(param.getUpcCodeList())) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCodeList(param.getUpcCodeList());
            List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            if (CollectionUtils.isEmpty(skuUpcList)) {
                param.setId(-1L);
            } else {
                if (CollectionUtils.isEmpty(param.getSkuCodeList())) {
                    stockLotParam.setSkuCodeList(skuUpcList.stream().map(SkuUpcDTO::getSkuCode).distinct().collect(Collectors.toList()));
                } else {
                    List<String> skuCodeList = stockLotParam.getSkuCodeList();
                    skuCodeList.retainAll(skuUpcList.stream().map(SkuUpcDTO::getSkuCode).collect(Collectors.toList()));
                    if (CollectionUtils.isEmpty(skuCodeList)) {
                        param.setId(-1L);
                    } else {
                        stockLotParam.setSkuCodeList(skuCodeList);
                    }
                }
            }
        }

        StockStatisticDTO statistic = remoteStockLotClient.getStatistic(stockLotParam);
        return Result.success(ConverterUtil.convert(statistic, StockStatisticBizDTO.class));
    }

    @Override
    public Result<Boolean> remove(StockLotBizParam param) {
        StockLotParam stockLotParam = ConverterUtil.convert(param, StockLotParam.class);
        Boolean result = remoteStockLotClient.remove(stockLotParam);
        return Result.success(result);
    }
}
