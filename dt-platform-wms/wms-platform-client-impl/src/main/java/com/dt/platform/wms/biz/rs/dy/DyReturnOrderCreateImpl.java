package com.dt.platform.wms.biz.rs.dy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.danding.business.client.rpc.config.facade.OwnerRpcFacade;
import com.danding.business.client.rpc.config.result.WarehouseInfoResult;
import com.danding.core.tenant.SimpleTenantHelper;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.component.common.enums.rs.RSBillSourceEnum;
import com.dt.component.common.enums.rs.RSOrderStatusEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.bill.client.rs.bo.SalesReturnOrderBO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDetailDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderExtraDTO;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.domain.bill.param.rs.SalesReturnOrderParam;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import com.dt.platform.wms.rs.dy.DyReturnOrderCreateRequest;
import com.dt.platform.wms.rs.dy.IDyReturnOrderCreate;
import com.dt.platform.wms.rs.dy.OrderDetail;
import com.dt.platform.wms.rs.dy.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Slf4j
@DubboService
public class DyReturnOrderCreateImpl implements IDyReturnOrderCreate {

    public static final Result<String> SYSTEM_ERROR = new Result<>(100003, "系统错误", "系统错误");
    public static final Result<String> PARAM_ERROR = new Result<>(100002, "参数错误", "参数错误");

    @Resource
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @DubboReference
    private OwnerRpcFacade ownerRpcFacade;

    @Resource
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private IRemoteLockSupportClient remoteLockSupportClient;

    @Override
    public Result<String> create(DyReturnOrderCreateRequest request) {
        return remoteLockSupportClient.execute(() -> {
            try {
                log.info("dy return order create {}", JSONUtil.toJsonStr(request));
                SimpleTenantHelper.setTenantId(request.getTenantId());
                WarehouseInfoResult data = ownerRpcFacade.getUserIdByCode(request.getCargoCode()).getData();
                if (null == data) throw new RuntimeException("实体仓不存在");
                RpcContextUtil.setWarehouseCode(data.getWarehouseCode());

                WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(data.getWarehouseCode());
                if (null == warehouseDTO) throw new RuntimeException("仓库不存在");

                CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(request.getCargoCode());
                if (null == cargoOwnerDTO) throw new RuntimeException("货主不存在");

                ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
                shipmentOrderParam.setPoNo(request.getPoNo());
                List<ShipmentOrderDTO> list = remoteShipmentOrderClient.getList(shipmentOrderParam);
                if (list.isEmpty()) {
                    throw new RuntimeException("正向发货单不存在");
                }
                ShipmentOrderDTO shipmentOrderDTO = list.stream().max(Comparator.comparing(ShipmentOrderDTO::getId)).get();

                if (CollectionUtil.isEmpty(request.getOrderDetailList())) return PARAM_ERROR;
                if (StrUtil.isBlank(request.getSalesReturnOrderNo())) return PARAM_ERROR;

                SalesReturnOrderParam param = new SalesReturnOrderParam();
                param.setSalesReturnOrderNo(request.getSalesReturnOrderNo());
                List<SalesReturnOrderDTO> oldList = remoteSalesReturnOrderClient.getList(param);
                if (CollectionUtil.isNotEmpty(oldList) && oldList.stream().anyMatch(salesReturnOrderDTO -> !RSOrderStatusEnum.CANCEL.getCode().equals(salesReturnOrderDTO.getStatus()))) {
                    return Result.success("业务处理成功【销退单已存在】");
                }

                SalesReturnOrderBO salesReturnOrderBO = new SalesReturnOrderBO();
                SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
                salesReturnOrderDTO.setSalesReturnOrderNo(request.getSalesReturnOrderNo());
                salesReturnOrderDTO.setCargoCode(request.getCargoCode());
                salesReturnOrderDTO.setCargoName(cargoOwnerDTO.getName());
                salesReturnOrderDTO.setWarehouseCode(data.getWarehouseCode());
                salesReturnOrderDTO.setRealWarehouseName(data.getWarehouseName());
                salesReturnOrderDTO.setRealWarehouseCode(data.getWarehouseCode());
                salesReturnOrderDTO.setPoNo(request.getPoNo());
                salesReturnOrderDTO.setGlobalNo(shipmentOrderDTO.getGlobalNo());
                salesReturnOrderDTO.setExpressNo(request.getExpressNo());
                salesReturnOrderDTO.setCarrierName(request.getCarrierName());
                salesReturnOrderDTO.setReverseExpressNo(request.getReverseExpressNo());
                salesReturnOrderDTO.setReverseCarrierName(request.getReverseCarrierName());
                salesReturnOrderDTO.setTaxType(TaxTypeEnum.TYPE_DUTY_TAX.getCode());
                salesReturnOrderDTO.setReturnType(request.getReturnType());
                salesReturnOrderDTO.setBillSource(RSBillSourceEnum.DY_MARKET.getCode());
                salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CREATED.getCode());
                SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = new SalesReturnOrderExtraDTO();
                salesReturnOrderExtraDTO.setDyMarketWarehouseCode(request.getWarehouseCode());
                salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
                salesReturnOrderBO.setSalesReturnOrderDTOList(ListUtil.toList(salesReturnOrderDTO));
                ArrayList<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList = new ArrayList<>();

                for (OrderDetail orderDetail : request.getOrderDetailList()) {
                    SalesReturnOrderDetailDTO orderDetailDTO = new SalesReturnOrderDetailDTO();
                    orderDetailDTO.setLineSeq(orderDetail.getLineSeq());
                    orderDetailDTO.setSalesReturnOrderNo(request.getSalesReturnOrderNo());
                    orderDetailDTO.setSkuCode(orderDetail.getSkuCode());
                    orderDetailDTO.setExpectQty(orderDetail.getExpectQty());
                    orderDetailDTO.setSkuQuality(orderDetail.getSkuQuality());
                    salesReturnOrderDetailDTOList.add(orderDetailDTO);
                }

                remoteSkuClient.rich(salesReturnOrderDetailDTOList, SalesReturnOrderDetailDTO::getSkuCode, salesReturnOrderDetailDTO -> request.getCargoCode(), (salesReturnOrderDetailDTO, skuDTO) -> salesReturnOrderDetailDTO.setSkuName(skuDTO.getName()));
                remoteSkuClient.richUpc(salesReturnOrderDetailDTOList, SalesReturnOrderDetailDTO::getSkuCode, salesReturnOrderDetailDTO -> request.getCargoCode(), (salesReturnOrderDetailDTO, skuUpcDTO) -> salesReturnOrderDetailDTO.setUpcCode(skuUpcDTO.getUpcCode()));

                salesReturnOrderBO.setSalesReturnOrderDetailDTOList(salesReturnOrderDetailDTOList);
                remoteSalesReturnOrderClient.save(salesReturnOrderBO);
                log.info("dy return order create success {}", JSONUtil.toJsonStr(request));
                return Result.success("业务处理成功");
            } catch (Exception exception) {
                log.error(exception.getMessage(), exception);
                return SYSTEM_ERROR;
            }
        }, ListUtil.toList(request.getSalesReturnOrderNo()));
    }
}
