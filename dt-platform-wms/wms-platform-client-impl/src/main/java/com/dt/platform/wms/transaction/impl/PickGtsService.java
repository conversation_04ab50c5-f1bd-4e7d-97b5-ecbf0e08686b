package com.dt.platform.wms.transaction.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.bill.MessageMqStatusEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.stock.OperationTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.msg.StockOperationMessage;
import com.dt.domain.bill.bo.CancelAllocationBillBO;
import com.dt.domain.bill.bo.PickCancelBillBO;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.PickDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.param.message.MessageMqParam;
import com.dt.platform.wms.biz.stock.biz.StockOperationContext;
import com.dt.platform.wms.biz.stock.biz.StockOperationHandler;
import com.dt.platform.wms.integration.IRemoteBillContextClient;
import com.dt.platform.wms.integration.IRemoteMessageClient;
import com.dt.platform.wms.integration.message.IRemoteMessageMqClient;
import com.dt.platform.wms.transaction.IPickGtsService;
import com.dt.platform.wms.transaction.bo.AllocationCancelBO;
import com.dt.platform.wms.transaction.bo.PickCancelBO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PickGtsService implements IPickGtsService {

    @Resource
    private StockOperationHandler stockOperationHandler;

    @Resource
    private IRemoteBillContextClient remoteBillContextClient;

    @Resource
    private IRemoteMessageClient remoteMessageClient;

    @Resource
    private IRemoteMessageMqClient remoteMessageMqClient;

    @Override
//    @GlobalTransactional(timeoutMills = 300000, rollbackFor = Exception.class, name = "gts-cancel-allocation")
    public Boolean cancelAllocations(AllocationCancelBO param) {
        CancelAllocationBillBO cancelAllocationBillBO = new CancelAllocationBillBO();
        cancelAllocationBillBO.setPickDTO(param.getPickDTO());
        cancelAllocationBillBO.setPickDetailDTOList(param.getPickDetailDTOList());
        cancelAllocationBillBO.setShipmentOrderDTOList(param.getShipmentOrderDTOList());
        cancelAllocationBillBO.setShipmentOrderDetailDTOList(param.getShipmentOrderDetailDTOList());
        cancelAllocationBillBO.setShipmentOrderLogDTOList(param.getShipmentOrderLogDTOList());
        cancelAllocationBillBO.setPackageLogDTOList(param.getPackageLogDTOList());
        cancelAllocationBillBO.setPackageDetailDTOList(param.getPackageDetailDTOList());
        cancelAllocationBillBO.setPackageDTOList(param.getPackageDTOList());
        //补偿消息
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(cancelAllocationBillBO.getPickDTO().getWarehouseCode());
        messageMqDTO.setCargoCode(cancelAllocationBillBO.getPickDTO().getCargoCode());
        messageMqDTO.setBillNo(cancelAllocationBillBO.getPickDTO().getPickCode());
        messageMqDTO.setOperationType(OperationTypeEnum.OPERATION_CANCEL_ALLOCATION.getType());
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_PICK.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());

        cancelAllocationBillBO.setMessageMqDTO(messageMqDTO);

        //上游占用三级库存,不需要处理库存
        if (param.getShipmentOrderDTOList().stream().allMatch(a -> OrderTagEnum.NumToEnum(a.getOrderTag()).contains(OrderTagEnum.OCCUPY_LOCATION_STOCK))) {
            cancelAllocationBillBO.setMessageMqDTO(null);
        }
        //预包包裹不需要处理核销
        if (param.getPackageDTOList().stream().allMatch(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode()))) {
            cancelAllocationBillBO.setMessageMqDTO(null);
        }
        Boolean commit = remoteBillContextClient.cancelAllocation(cancelAllocationBillBO);

        //上游占用三级库存,不需要处理库存
        if (param.getShipmentOrderDTOList().stream().anyMatch(a -> !OrderTagEnum.NumToEnum(a.getOrderTag()).contains(OrderTagEnum.OCCUPY_LOCATION_STOCK))) {
            // 库存操作
            StockOperationMessage stockOperationMessage = new StockOperationMessage();
            stockOperationMessage.setWarehouseCode(param.getPickDTO().getWarehouseCode());
            stockOperationMessage.setCargoCode(param.getPickDTO().getCargoCode());
            stockOperationMessage.setParentBillNo(param.getPickDTO().getPickCode());
            stockOperationMessage.setOperationType(OperationTypeEnum.OPERATION_CANCEL_ALLOCATION.getType());
            stockOperationMessage.setBillType(BillTypeEnum.BILL_TYPE_PICK.getType());
            remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
        }

//        // 修改拣选单明细
//        PickDetailBatchParam pickDetailBatchParam = new PickDetailBatchParam();
//        pickDetailBatchParam.setPickDetailDTOList(param.getPickDetailDTOList());
//        remotePickDetailClient.modifyBatch(pickDetailBatchParam);
//        // 修改拣选单
//        remotePickClient.update(param.getPickDTO());
//
//        // 修改包裹明细
//        PackageDetailBatchParam packageDetailBatchParam = new PackageDetailBatchParam();
//        packageDetailBatchParam.setDetailList(param.getPackageDetailDTOList());
//        remotePackageDetailClient.modifyBatch(packageDetailBatchParam);
//        // 修改包裹
//        PackageBatchParam packageBatchParam = new PackageBatchParam();
//        packageBatchParam.setPackageList(param.getPackageDTOList());
//        remotePackageClient.modifyBatch(packageBatchParam);
//        // 出库单明细
//        ShipmentOrderDetailBatchParam shipmentOrderDetailBatchParam = new ShipmentOrderDetailBatchParam();
//        shipmentOrderDetailBatchParam.setDetailList(param.getShipmentOrderDetailDTOList());
//        remoteShipmentDetailClient.batchModify(shipmentOrderDetailBatchParam);
//        // 修改出库单
//        remoteShipmentOrderClient.modifyBatch(param.getShipmentOrderDTOList());
//
//        // 出库单操作日志
//        remoteShipmentOrderClient.saveShipmentLogList(param.getShipmentOrderLogDTOList());
//        // 包裹操作日志
//        remotePackageClient.savePackLogList(param.getPackageLogDTOList());
        return commit;
    }

    /**
     * 取消分配核销记录
     *
     * @param param
     */
    private void cancelAllocationStockTransaction(AllocationCancelBO param) {
        List<String> packageCode = param.getPackageDTOList().stream().map(PackageDTO::getPackageCode).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(packageCode)) {
            throw new BaseException(BaseBizEnum.TIP, "取消分配没有对应包裹");
        }
        stockOperationHandler.process(StockOperationContext.builder()
                .warehouseCode(param.getPickDTO().getWarehouseCode())
                .cargoCode(param.getPickDTO().getCargoCode())
                .operationType(OperationTypeEnum.OPERATION_CANCEL_ALLOCATION.getType())
                .parentBillNo(param.getPickDTO().getPickCode())
                .cancelAllocationPackageList(param.getPackageDTOList())
                .build());

    }

    @Override
//    @GlobalTransactional(timeoutMills = 300000, rollbackFor = Exception.class, name = "gts-pick-cancel-context-commit")
    public Boolean cancelPicking(PickCancelBO pickCancelBO) {
        PickCancelBillBO pickCancelBillBO = new PickCancelBillBO();
        pickCancelBillBO.setPickDTO(pickCancelBO.getPickDTO());
        pickCancelBillBO.setPickDetailDTOList(pickCancelBO.getPickDetailDTOList());
        pickCancelBillBO.setShipmentOrderDetailDTOList(pickCancelBO.getShipmentOrderDetailDTOList());
        pickCancelBillBO.setPackageLogDTOList(pickCancelBO.getPackageLogDTOList());
        pickCancelBillBO.setPackageDetailDTOList(pickCancelBO.getPackageDetailDTOList());
        pickCancelBillBO.setPackageDTOList(pickCancelBO.getPackageDTOList());
        //补偿消息
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(pickCancelBillBO.getPickDTO().getWarehouseCode());
        messageMqDTO.setCargoCode(pickCancelBillBO.getPickDTO().getCargoCode());
        messageMqDTO.setBillNo(pickCancelBillBO.getPickDTO().getPickCode());
        messageMqDTO.setOperationType(OperationTypeEnum.OPERATION_CANCEL_PICK.getType());
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_PICK.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setBatchSerialNo(getLastTimeUUID(pickCancelBO.getPickDTO()));
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        pickCancelBillBO.setMessageMqDTO(messageMqDTO);
        Boolean commit = remoteBillContextClient.cancelPicking(pickCancelBillBO);

        // 库存消息
        StockOperationMessage stockOperationMessage = new StockOperationMessage();
        stockOperationMessage.setWarehouseCode(pickCancelBillBO.getPickDTO().getWarehouseCode());
        stockOperationMessage.setCargoCode(pickCancelBillBO.getPickDTO().getCargoCode());
        stockOperationMessage.setParentBillNo(pickCancelBillBO.getPickDTO().getPickCode());
        stockOperationMessage.setOperationType(OperationTypeEnum.OPERATION_CANCEL_PICK.getType());
        stockOperationMessage.setBillType(BillTypeEnum.BILL_TYPE_PICK.getType());
        stockOperationMessage.setBatchSerialNo(messageMqDTO.getBatchSerialNo());
        remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);

//        //回滚拣选单状态到已创建
//        remotePickClient.update(pickCancelBO.getPickDTO());
//        PickBatchParam pickBatchParam = new PickBatchParam();
//        //回滚拣选单详情的数据状态
//        pickBatchParam.setPickDetailList(pickCancelBO.getPickDetailDTOList());
//        remotePickClient.modifyPickDetailBatch(pickBatchParam);
//        //回滚包裹状态为已汇总
//        PackageBatchParam packageBatchParam = new PackageBatchParam();
//        packageBatchParam.setPackageList(pickCancelBO.getPackageDTOList());
//        remotePackageClient.modifyBatch(packageBatchParam);
//        //回滚包裹明细的拣选数量
//        PackageDetailBatchParam packageDetailBatchParam = new PackageDetailBatchParam();
//        packageDetailBatchParam.setDetailList(pickCancelBO.getPackageDetailDTOList());
//        remotePackageDetailClient.modifyBatch(packageDetailBatchParam);
//        //回滚出库单明细的拣选数量
//        ShipmentOrderDetailBatchParam shipmentOrderDetailBatchParam = new ShipmentOrderDetailBatchParam();
//        shipmentOrderDetailBatchParam.setDetailList(pickCancelBO.getShipmentOrderDetailDTOList());
//        remoteShipmentDetailClient.batchModify(shipmentOrderDetailBatchParam);
//        //添加记录日志
//        remotePackageClient.savePackLogList(pickCancelBO.getPackageLogDTOList());
        return Boolean.TRUE;
    }

    /**
     * @param pickDTO
     * @return java.lang.String
     * <AUTHOR>
     * @describe: 获取交回的messagMqDTO
     * @date 2023/8/9 14:04
     */
    private String getLastTimeUUID(PickDTO pickDTO) {
        MessageMqParam messageMqParam = new MessageMqParam();
        messageMqParam.setBillNo(pickDTO.getPickCode());
        messageMqParam.setOperationType(OperationTypeEnum.OPERATION_PICK_RETURN.getType());
        List<MessageMqDTO> messageMqDTOList = remoteMessageMqClient.getList(messageMqParam);
        if (!CollectionUtil.isEmpty(messageMqDTOList)) {
            MessageMqDTO messageMqDTO = messageMqDTOList.stream().sorted(Comparator.comparing(MessageMqDTO::getId, Comparator.reverseOrder())).findFirst().orElse(null);
            return messageMqDTO.getBatchSerialNo();
        }
        return "";
    }

    /**
     * 取消拣货核销记录
     */
    private void cancelPickStockTransaction(PickCancelBO param, MessageMqDTO messageMqDTO) {

    }


}
