package com.dt.platform.wms.biz;

import com.dt.component.common.enums.pkg.PackIsPreEnum;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.bill.dto.CollectWaveAnalysisDTO;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.PackageDetailDTO;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.platform.wms.biz.stock.biz.bo.AllocationStockBO;
import com.dt.platform.wms.dto.wave.PreAnalysisSkuLotAndStockDTO;
import com.dt.platform.wms.dto.wave.SkuLotAndStockDTO;
import com.dt.platform.wms.dto.wave.StockOccupyAnalysisDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/22 9:23
 */
public interface ISkuStockAndLotBiz {
    /**
     * create by: wuxian
     * description:  汇单库存排序  stockLocationDTOList（单商品库存）  taskCode 此次任务ID
     * create time: 2021/6/24 13:19
     *
     * @param taskCode
     * @param warehouseCode
     * @param stockLocationDTOList
     * @param turnoverRuleCode
     * @param allocationRuleCode
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     */
    List<SkuLotAndStockDTO> sortListStockLotAndLocation(String taskCode, String warehouseCode, List<SkuLotAndStockDTO> stockLocationDTOList, String turnoverRuleCode, String allocationRuleCode);

    /**
     * create by: wuxian
     * description:  汇单库存排序  stockLocationDTOList（单商品库存）  taskCode 此次任务ID
     * create time: 2021/6/24 13:19
     *
     * @param stockLocationDTOList
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     */
    List<SkuLotAndStockDTO> sortListPreStockLotAndLocation(List<SkuLotAndStockDTO> stockLocationDTOList, PackIsPreEnum preEnum);

    /**
     * create by: wuxian
     * description:  汇单库存数据组装
     * create time: 2021/6/24 13:19
     *
     * @param skuLotDTOList
     * @param stockLocationDTOList
     * @param skuDTOList
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     */
    List<SkuLotAndStockDTO> buildSkuLotAndStock(List<SkuLotDTO> skuLotDTOList, List<StockLocationDTO> stockLocationDTOList, List<SkuDTO> skuDTOList);

    /**
     * create by: wuxian
     * description:  汇单获取指定库区的库存
     * create time: 2021/6/24 13:20
     *
     * @param zoneCodeList
     * @param skuCodeList
     * @param cargoCodeList
     * @param zoneTypeList
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     */
    List<SkuLotAndStockDTO> getSkuLotAndStockByZoneAnalysis(List<String> zoneCodeList, List<String> skuCodeList, List<String> cargoCodeList, List<String> zoneTypeList);


    /**
     * @param zoneCodeList
     * @param skuCode
     * @param cargoCode
     * @return java.util.List<com.dt.platform.wms.dto.wave.PreAnalysisSkuLotAndStockDTO>
     * @author: WuXian
     * description: 预包计划获取商品的所有有效库存和周转规则排序
     * create time: 2021/8/24 13:39
     */
    List<PreAnalysisSkuLotAndStockDTO> prePackageAnalysisStock(List<String> zoneCodeList, String skuCode, String cargoCode);

    /**
     * create by: wuxian
     * description:  获取当前货主是否开启存储区拣货
     * create time: 2021/6/24 13:20
     *
     * @param cargoCodeList
     * @return java.util.List<java.lang.String>
     */
    List<String> getStorageCargoCodeList(List<String> cargoCodeList);

//    /**
//     * @param skuCodeList
//     * @param cargoCodeList
//     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
//     * @author: WuXian
//     * description:  补货指引获取库存数据 （拣选库存）过滤禁售
//     * create time: 2021/7/5 10:51
//     */
//    List<SkuLotAndStockDTO> getSkuLotAndStockFromReplenishTaskForPick(List<String> skuCodeList, List<String> cargoCodeList);

    /**
     * @param cargoCodeList
     * @param skuCodeList
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     * @author: WuXian
     * description: 获取补货库存(存储区)过滤禁售
     * create time: 2021/7/6 14:00
     */
    List<SkuLotAndStockDTO> getSkuLotAndStockReplenishFromStorage(List<String> cargoCodeList, List<String> skuCodeList);

    /**
     * @param stockDTOList
     * @param packageDTOTempList
     * @param waveFrontAnalysisDTOList
     * @param packageDetailDTOList
     * @return java.util.List<java.lang.String>
     * @author: WuXian
     * description: 汇单前置分析
     * create time: 2021/12/30 15:50
     */
    List<String> waveCollectAnalysisData(List<SkuLotAndStockDTO> stockDTOList, List<PackageDTO> packageDTOTempList, List<CollectWaveAnalysisDTO> waveFrontAnalysisDTOList, List<PackageDetailDTO> packageDetailDTOList);

    /**
     * @param skuCodeList
     * @param cargoCodeList
     * @param locationTypeList
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     * <AUTHOR>
     * @describe: 获取拣选区的库存-不过滤禁售
     * @date 2022/10/27 15:10
     */
    List<SkuLotAndStockDTO> getSkuLotAndStockFromReplenishTaskForPickB2B(List<String> skuCodeList, List<String> cargoCodeList, List<String> locationTypeList);

    /**
     * @param stockOccupyAnalysisDTO
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/12/5 11:30
     */
    List<SkuLotAndStockDTO> analysisExternalOccupyLocationStock(StockOccupyAnalysisDTO stockOccupyAnalysisDTO);

    /**
     * @param globalNo
     * @param cargoCode
     * @param skuQuality
     * @param orderType
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     * <AUTHOR>
     * @describe: 上游占用三级库存，生成分配单
     * @date 2022/12/8 14:57
     */
    List<SkuLotAndStockDTO> getOccupyStockLocationFromTransaction(String globalNo, String cargoCode, String skuQuality, String orderType);
    /**
     * @param billNoList
     * @param cargoCode
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     * <AUTHOR>
     * @describe: 预包前占获取库存记录
     * @date 2025/4/10 11:04
     */
    List<SkuLotAndStockDTO> getOccupyStockLocationFromTransactionByPre(List<String> billNoList, String cargoCode);

    /**
     * @param warehouseCode
     * @param batchId
     * @param turnoverRuleCode
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @describe:
     * @date 2022/12/8 15:29
     */
    List<String> addTurnoverRuleSort(String warehouseCode, String batchId, String turnoverRuleCode);

    /**
     * @param warehouseCode
     * @param batchId
     * @param allocationCode
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @describe:
     * @date 2022/12/8 15:29
     */
    List<String> addAllocationRuleSort(String warehouseCode, String batchId, String allocationCode);

    /**
     * @param analysisPackList
     * @param skuCodeList
     * @param packageDetailDTOList
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2022/12/14 17:24
     */
    void analysisPackData(List<CollectWaveAnalysisDTO> analysisPackList, List<String> skuCodeList, List<PackageDetailDTO> packageDetailDTOList);

    /**
     * @param packageDTO
     * @param packageDetailDTOList
     * @return java.util.List<com.dt.platform.wms.biz.stock.biz.bo.AllocationStockBO>
     * <AUTHOR>
     * @describe: 预包前占占用三级库存
     * Result code=-999  库存不满足  -1 表示参数校验异常
     * @date 2025/4/8 9:20
     */
    Result<List<AllocationStockBO>> analysisOccupyPrePackData(PackageDTO packageDTO, List<PackageDetailDTO> packageDetailDTOList);
}
