package com.dt.platform.wms.biz.manager;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.allocation.AllocationIsPreEnum;
import com.dt.component.common.enums.allocation.AllocationOrderEnum;
import com.dt.component.common.enums.base.LocationTypeEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.bill.*;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.collect.CollectLockOperateTypeEnum;
import com.dt.component.common.enums.collect.CollectLockStatusEnum;
import com.dt.component.common.enums.collect.CollectWaveOrderTagEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pick.PickRuleStatusEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.pkg.PackIsPreEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.stock.OperationTypeEnum;
import com.dt.component.common.enums.stock.RealOrUnrealGoodsEnum;
import com.dt.component.common.enums.stock.StockLevelEnum;
import com.dt.component.common.enums.stock.StockTransactionStatusEnum;
import com.dt.component.common.enums.wave.CollectWaveTaskEnum;
import com.dt.component.common.enums.wave.CollectWaveTaskIsPreEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.bill.bo.CollectSubmitBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.collect.CollectLockTaskDTO;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.collect.CollectLockTaskParam;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.dto.StockTransactionDTO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.domain.core.stock.param.StockTransactionParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.LambdaHelpUtils;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.utils.sort.ListSortUtils;
import com.dt.platform.wms.biz.ISkuStockAndLotBiz;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.stock.biz.StockOperationContext;
import com.dt.platform.wms.biz.stock.biz.StockOperationHandler;
import com.dt.platform.wms.biz.stock.biz.bo.AllocationStockBO;
import com.dt.platform.wms.dto.wave.SkuLotAndStockDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.collect.IRemoteCollectLockTaskClient;
import com.dt.platform.wms.integration.pre.IRemotePrePackageSkuDetailClient;
import com.dt.platform.wms.integration.pre.IRemotePreSkuLotClient;
import com.dt.platform.wms.integration.stock.IRemoteStockTransactionClient;
import com.dt.platform.wms.transaction.IPickGtsService;
import com.dt.platform.wms.transaction.IWaveBillGtsService;
import com.dt.platform.wms.transaction.bo.CollectSyncOrderBO;
import com.dt.platform.wms.transaction.bo.CollectSyncOrderBuildNewBO;
import com.dt.platform.wms.transaction.bo.CollectWaveAbnormalBillBO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.RedissonMultiLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 同步汇成拣选单,分配库存
 *
 * <AUTHOR>
 * @date 2020/11/30 15:15
 */
@Component
@Slf4j
public class CollectWaveStockSyncManager {

    @Resource
    IRemotePickRuleClient remotePickRuleClient;

    @Resource
    IRemotePickClient remotePickClient;

    @Resource
    IPickGtsService pickContextService;

    @Resource
    IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    IRemotePackageClient remotePackageClient;

    @Resource
    IRemotePackageDetailClient remotePackageDetailClient;

    @Resource
    IRemoteLocationClient remoteLocationClient;

    @Resource
    IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    IWaveBillGtsService waveBillGtsService;

    @Resource
    ISkuStockAndLotBiz skuStockAndLotBiz;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private IRemoteCollectWaveTaskClient remoteCollectWaveTaskClient;

    @Resource
    IRemoteICollectBillClient remoteICollectBillClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    IRemotePreSkuLotClient remotePreSkuLotClient;

    @Resource
    IRemoteCollectLockTaskClient remoteCollectLockTaskClient;

    @Resource
    IRemotePrePackageSkuDetailClient remotePrePackageSkuDetailClient;

    @Resource
    IRemoteStockTransactionClient remoteStockTransactionClient;

    @Resource
    StockOperationHandler stockOperationHandler;

    @Resource
    DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    WmsOtherConfig wmsOtherConfig;

    @Resource
    CwAllOccupyStockLocationManager cwAllOccupyStockLocationManager;


    /**
     * create by: WuXian
     * description:  同步汇单
     * create time: 2021/6/24 13:02
     *
     * @param collectWaveTaskDTO
     * @return void
     */
    public void allocationStockSyncPickTask(CollectWaveTaskDTO collectWaveTaskDTO, List<String> satisfyPackCodeList) {
        //波次任务执行时间超过15分钟，打印日志
        Long consumeTime = (System.currentTimeMillis() - collectWaveTaskDTO.getCreatedTime()) / 1000;
        if (consumeTime > 900) {
            log.info("allocationStockSyncPick-run-timeout:warehouseCode:{} time/s:{}", collectWaveTaskDTO.getWarehouseCode(), consumeTime);
            if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                String message = String.format("[流程:汇单任务超时]仓库:%s,耗时:%s(s)", collectWaveTaskDTO.getWarehouseCode(), consumeTime);
                WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
            }
        }
        RpcContextUtil.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        PackageParam packageParam = new PackageParam();
        packageParam.setWaveCode(collectWaveTaskDTO.getWaveCode());
        packageParam.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        packageParam.setCargoCode(collectWaveTaskDTO.getCargoCode());
        packageParam.setCarrierCode(collectWaveTaskDTO.getCarrierCode());
        packageParam.setSalePlatform(collectWaveTaskDTO.getSalePlatform());
        packageParam.setExpressBranchNotBlank(collectWaveTaskDTO.getExpressBranch());
        packageParam.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
        packageParam.setCollectStatus(CollectStatusEnum.CREATE_STATUS.getCode());
        Boolean checkExits = remotePackageClient.checkExits(packageParam);
        if (!checkExits && !collectWaveTaskDTO.getStatus().equalsIgnoreCase(CollectWaveTaskEnum.SUCCESS_STATUS.getCode())) {
            collectWaveTaskDTO.setStatus(CollectStatusEnum.COLLECT_STATUS.getCode());
            remoteCollectWaveTaskClient.modifyWaveTask(collectWaveTaskDTO);
            log.info("allocationStockSyncPickTask-end:{}", collectWaveTaskDTO.getWaveCode() + collectWaveTaskDTO.getId());
            return;
        }
        log.info("allocationStockSyncPick-normal-start warehouseCode:{} waveCode:{} cargoCode:{}", collectWaveTaskDTO.getWarehouseCode(), collectWaveTaskDTO.getWaveCode(), collectWaveTaskDTO.getCargoCode());
        //查询是否有任务在操作
        Boolean checkLockTaskExist = checkLockTask(collectWaveTaskDTO.getCargoCode(), collectWaveTaskDTO.getWarehouseCode());
        if (checkLockTaskExist) {
            log.info("checkLockTaskExist:{}", JSONUtil.toJsonStr(collectWaveTaskDTO));
            return;
        }
        //设置CW货主
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(collectWaveTaskDTO.getCargoCode());
        collectWaveTaskDTO.setCwCargo(false);
        if (cargoOwnerDTO != null
                && cargoOwnerDTO.getCargoTag() > 0
                && CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.CW_CARGO)) {
            collectWaveTaskDTO.setCwCargo(true);
        }

        //查询拣选规则
        PickRuleDTO pickRuleDTO = remotePickRuleClient.queryDefaultPickRule(collectWaveTaskDTO.getPickRuleCode());
        if (pickRuleDTO == null || CollectionUtils.isEmpty(pickRuleDTO.getListDetail())) {
            throw new BaseException(BaseBizEnum.TIP, "拣选策略不能为空");
        }
        List<PickRuleDetailDTO> pickRuleList = pickRuleDTO.getListDetail().stream().filter(entity -> Objects.equals(entity.getStatus(), PickRuleStatusEnum.ENABLE.getValue())).sorted(Comparator.comparing(PickRuleDetailDTO::getPriority)).collect(Collectors.toList());
        if (pickRuleList.stream().noneMatch(a -> a.getOrderMinQty() == 1L)) {
            log.info("pickRuleMinOrderNoOne:{}", JSONUtil.toJsonStr(collectWaveTaskDTO));
        }
        int pickRuleSize = pickRuleList.size();
        int currentCount = 0;
        Boolean pickRuleLast = false;
        //排序
        for (PickRuleDetailDTO pickRuleEntity : pickRuleList) {
            currentCount++;
            if (currentCount == pickRuleSize) {
                pickRuleLast = true;
            }
            List<String> structs;
            switch (PickEnum.PickOrderTypeEnum.matchOpCode(pickRuleEntity.getPickOrderType())) {
                //----------------------------秒杀单不考虑篮子-------------------------
                case SPIKE:
                    structs = Arrays.asList(pickRuleEntity.getPackageStruct().split(","));
                    if (!StringUtils.isEmpty(pickRuleEntity.getPackageStruct()) && !CollectionUtils.isEmpty(structs)) {
                        structs = structs.stream().sorted(Comparator.comparing(String::new)).collect(Collectors.toList());
                        for (String entity : structs) {
                            pickRuleEntity.setPackageStruct(entity);
                            //合流汇单
                            if (collectWaveTaskDTO.getOrderTag() > 0
                                    && CollectWaveOrderTagEnum.NumToEnum(collectWaveTaskDTO.getOrderTag()).contains(CollectWaveOrderTagEnum.ORDER_MERG)
                                    && wmsOtherConfig.getOpenTaoTianMerge()) {
                                spikePickOrderByMerge(pickRuleEntity, collectWaveTaskDTO, satisfyPackCodeList, pickRuleLast);
                            } else {
                                spikePickOrder(pickRuleEntity, collectWaveTaskDTO, satisfyPackCodeList);
                            }
                        }
                    }
                    break;
                default:
                    //----------------------------杂单(多品)---------------------------------
                    //合流汇单
                    if (collectWaveTaskDTO.getOrderTag() > 0
                            && CollectWaveOrderTagEnum.NumToEnum(collectWaveTaskDTO.getOrderTag()).contains(CollectWaveOrderTagEnum.ORDER_MERG)
                            && wmsOtherConfig.getOpenTaoTianMerge()) {
                        miscellaneousMMPickOrderByMerge(pickRuleEntity, collectWaveTaskDTO, satisfyPackCodeList, pickRuleLast);
                    } else {
                        miscellaneousMMPickOrder(pickRuleEntity, collectWaveTaskDTO, satisfyPackCodeList);
                    }
                    break;
            }
        }
        log.info("allocationStockSyncPick-end end:{}", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 查询汇单任务是否还有订单
     *
     * @param collectWaveTaskDTO
     * @return
     */
    private PackageParam buildQueryCollectStartParam(CollectWaveTaskDTO collectWaveTaskDTO) {
        PackageParam packageParam = new PackageParam();
        packageParam.setWaveCode(collectWaveTaskDTO.getWaveCode());
        packageParam.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        packageParam.setCargoCode(collectWaveTaskDTO.getCargoCode());
        packageParam.setCarrierCode(collectWaveTaskDTO.getCarrierCode());
        packageParam.setSalePlatform(collectWaveTaskDTO.getSalePlatform());
        packageParam.setExpressBranchNotBlank(collectWaveTaskDTO.getExpressBranch());
        packageParam.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
        packageParam.setCollectStatus(CollectStatusEnum.CREATE_STATUS.getCode());
        return packageParam;
    }

    private void miscellaneousMMPickOrderByMerge(PickRuleDetailDTO pickRuleEntity, CollectWaveTaskDTO collectWaveTaskDTO, List<String> satisfyPackCodeList, Boolean pickRuleLast) {
        //查询指定结构的包裹（分配库存和汇成拣选单） ----包裹结构新增明细sku的比如 ：sku1,10;sku2:15
        log.info("allocationStockSyncPick miscellaneousMMPickOrder pickRuleEntity:{} collectWaveTaskDTO:{}", JSONUtil.toJsonStr(pickRuleEntity), JSONUtil.toJsonStr(collectWaveTaskDTO));
        List<PackageDTO> packageDTOList = getMiscellaneousPackList(collectWaveTaskDTO, pickRuleEntity.getPackageStruct(), satisfyPackCodeList);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            return;
        }
        //TODO 正次品不能并发，因为锁都是针对商品级别，没有针对商品正次品级别
        //正品汇单
        List<PackageDTO> avlPackageList = packageDTOList.stream().filter(packageDTO -> Objects.equals(packageDTO.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel())).collect(Collectors.toList());
        //次品汇单
        List<PackageDTO> damagePackageList = packageDTOList.stream().filter(packageDTO -> Objects.equals(packageDTO.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())).collect(Collectors.toList());
        //最后一个任务结束collectTask
        if (!CollectionUtils.isEmpty(avlPackageList)) {
            miscellaneousToPickByMerge(pickRuleEntity, collectWaveTaskDTO, avlPackageList, pickRuleLast);
        }
        if (!CollectionUtils.isEmpty(damagePackageList)) {
            miscellaneousToPickByMerge(pickRuleEntity, collectWaveTaskDTO, damagePackageList, pickRuleLast);
        }
    }

    private void spikePickOrderByMerge(PickRuleDetailDTO pickRuleEntity, CollectWaveTaskDTO collectWaveTaskDTO, List<String> satisfyPackCodeList, Boolean pickRuleLast) {
        //分析包裹结构 where warehouseCode,cargoCode,carrierCode,salePlatform
        log.info("allocationStockSyncPick spikePickOrder pickRuleEntity:{} collectWaveTaskDTO:{}", JSONUtil.toJsonStr(pickRuleEntity), JSONUtil.toJsonStr(collectWaveTaskDTO));
        //秒杀组装参数
        CollectWaveBillParam param = buildBillSpikeParam(collectWaveTaskDTO, pickRuleEntity.getPackageStruct());
        if (!CollectionUtils.isEmpty(satisfyPackCodeList)) {
            param.setPackageCodeList(satisfyPackCodeList);
        }
        //正次品分开汇单 秒杀分组
        List<CollectWaveDTO> collectWaveDTOList = remotePackageClient.querySpikeCollectWave(param);
        if (CollectionUtils.isEmpty(collectWaveDTOList)) {
            return;
        }
        for (CollectWaveDTO entity : collectWaveDTOList) {
            //满足下限或者尾波
            if (entity.getNum() < pickRuleEntity.getOrderMinQty() && !Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                log.info("collect-wave-minQty:{}", collectWaveTaskDTO.getId() + collectWaveTaskDTO.getWaveCode());
                continue;
            }
            //查询指定结构的包裹（分配库存和汇成拣选单） ----包裹结构新增明细sku的比如 ：sku1,10;sku2:15
            List<PackageDTO> originPackageList = getSpikePackList(collectWaveTaskDTO.getWaveCode(), entity, satisfyPackCodeList);
            if (CollectionUtils.isEmpty(originPackageList)) {
                continue;
            }
            //淘天合流单二次筛选
            originPackageList = checkTaoTianPackList(originPackageList, pickRuleLast);
            if (CollectionUtils.isEmpty(originPackageList)) {
                continue;
            }
            //过滤正次品
            originPackageList = originPackageList.stream().filter(a -> a.getSkuQuality().equalsIgnoreCase(entity.getSkuQuality())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(originPackageList)) {
                continue;
            }
            //查询所有包裹明细
            List<PackageDetailDTO> originPackageDetailList = getPackDetailList(entity.getWarehouseCode(), entity.getCargoCode(), originPackageList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(originPackageDetailList)) {
                continue;
            }
            //获取开启存储区的货主
            List<String> storageCargoCodeList = skuStockAndLotBiz.getStorageCargoCodeList(Arrays.asList(collectWaveTaskDTO.getCargoCode()));
            //货主是否开启存储位拣货 只考虑B2B
            Boolean isStoragePick = false;
            if (!CollectionUtils.isEmpty(storageCargoCodeList) && collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
                isStoragePick = true;
            }
            Boolean isOccupyFinance = true;
            if (collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
                //获取出库单类型，盘点是否是代采出库模式，库存只需要供应链金融库存
                isOccupyFinance = getShipmentOrderType(originPackageList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
            }
            //获取所有包裹商品的库存数据
            List<SkuLotAndStockDTO> originStockLocationList = findStockLotAndLocation(collectWaveTaskDTO, isStoragePick, isOccupyFinance, originPackageDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList()));
            //无库存数据都是异常单
            if (CollectionUtils.isEmpty(originStockLocationList) && Objects.equals(collectWaveTaskDTO.getIsPre(), CollectWaveTaskIsPreEnum.NORMAL.getCode())) {
                log.info("stockLocationDTOS_Insufficient_error:package_code:{} sku_code:{}", JSONUtil.toJsonStr(originPackageList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList())), JSONUtil.toJsonStr(originPackageDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList())));
                buildAbnormalOrderAll(originPackageList, collectWaveTaskDTO, originPackageDetailList, isStoragePick);
                continue;
            }
            int packSize = originPackageList.size();
            //初始化对象（TODO 内存对象）
            CollectSyncOrderBuildNewBO orderBuildNewBO = new CollectSyncOrderBuildNewBO();
            //加锁 -- 与拦截单互斥
            RedissonMultiLock lock = addLockPackage(collectWaveTaskDTO.getWarehouseCode(), originPackageList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
            try {
                lock.lock(60 * 5, TimeUnit.SECONDS);
                //包裹计数
                int num = 0;
                Boolean isContinue = false;
                //合流单库存是否充足
                Map<String, Boolean> authMap = new HashMap<>();
                //合流单计数
                Map<String, Integer> mergeSatisfyCurrentPick = new HashMap<>();
                //按交易单号分组
                Map<String, List<PackageDTO>> packMap = originPackageList.stream().collect(Collectors.groupingBy(it -> StrUtil.join("#", it.getCargoCode(), it.getTradeNo())));
                Map<String, List<PackageDetailDTO>> packDetailMap = originPackageDetailList.stream().collect(Collectors.groupingBy(PackageDetailDTO::getPackageCode));
                for (PackageDTO packageDTO : originPackageList) {
                    String cargoCodeAndTradeNo = StrUtil.join("#", packageDTO.getCargoCode(), packageDTO.getTradeNo());
                    if (!authMap.containsKey(cargoCodeAndTradeNo)) {
                        //check 同一个运单号库存是否够用 否移除当前交易单号的所有包裹
                        authMap.put(cargoCodeAndTradeNo, checkMergeStockAuth(collectWaveTaskDTO, ObjectUtil.cloneByStream(originStockLocationList), ObjectUtil.cloneByStream(packMap.get(cargoCodeAndTradeNo)), ObjectUtil.cloneByStream(packDetailMap)));
                        Integer currentNum = pickRuleEntity.getOrderMaxQty().intValue() - num;
                        if (currentNum >= packageDTO.getMergeNum()) {
                            mergeSatisfyCurrentPick.put(cargoCodeAndTradeNo, packageDTO.getMergeNum());
                        }
                    }
                    //当前拣选单,剩余数量+合流单的拆包数 大于拣选单的上限 不进入当前拣选单
                    if (packSize > 0 && !mergeSatisfyCurrentPick.containsKey(cargoCodeAndTradeNo)) {
                        packSize--;
                        if (packSize != 0) {
                            continue;
                        }
                    }
                    if (authMap.containsKey(cargoCodeAndTradeNo) && authMap.get(cargoCodeAndTradeNo)) {
                        if (num == 0) {
                            //尾波
                            if (packSize < pickRuleEntity.getOrderMinQty() && Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                                isContinue = true;
                            }
                            //非尾波
                            if (packSize >= pickRuleEntity.getOrderMinQty() && !Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                                isContinue = true;
                            }
                            orderBuildNewBO = new CollectSyncOrderBuildNewBO();
                            orderBuildNewBO.setCommitPick(buildPickDTO(collectWaveTaskDTO, pickRuleEntity));
                        }
                        if (!isContinue) {
                            break;
                        }
                        if (packSize > 0) {
                            //获取包裹明细
                            List<PackageDetailDTO> packageDetailDTOList = originPackageDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(packageDetailDTOList)) {
                                throw new BaseException(BaseBizEnum.TIP, "包裹明细不存在");
                            }
                            //包裹明细扣减库存数据
                            Boolean reduceStock = getReduceStock(collectWaveTaskDTO, orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getAllocationStockBOList(), packageDTO, packageDetailDTOList, originStockLocationList, isStoragePick);

                            if (reduceStock) {
                                if (Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.PRE.getCode())) {
                                    packageDTO.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
                                    packageDTO.setListDetail(packageDetailDTOList);
                                }
                                orderBuildNewBO.getCommitPackage().add(packageDTO);
                                num++;
                                orderBuildNewBO.getCommitPick().setExpressBranchName(packageDTO.getExpressBranchName());
                                orderBuildNewBO.getCommitPick().setExpressBranch(packageDTO.getExpressBranch());
                            }
                        }
                        if (packSize > 0) {
                            packSize--;
                        }
                        //处理尾波
                        Boolean isComplete = false;
                        if (Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                            //处理尾波  尾波打破下限
                            isComplete = packSize < pickRuleEntity.getOrderMinQty() && packSize == 0;
                        } else {
                            //处理非尾波
                            isComplete = (num == pickRuleEntity.getOrderMaxQty()) || (packSize == 0 && num >= pickRuleEntity.getOrderMinQty());
                        }
                        //完成
                        if (isComplete) {
                            //预包需要补充分配记录
                            if (orderBuildNewBO.getCommitPackage().stream().anyMatch(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode()))) {
                                List<PackageDTO> prePackList = orderBuildNewBO.getCommitPackage().stream().filter(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode())).collect(Collectors.toList());
                                addPrePackAllocation(prePackList, orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getCommitPick(), collectWaveTaskDTO);
                            }
                            //库区编码
                            orderBuildNewBO.getCommitPick().setPickZoneAll(orderBuildNewBO.getCommitAllocation().stream().map(AllocationOrderDTO::getZoneCode).distinct().collect(Collectors.joining(",")));
                            String zoneCode = orderBuildNewBO.getCommitPick().getPickZoneAll();
                            if (!StringUtils.isEmpty(zoneCode) && zoneCode.length() > 500) {
                                zoneCode = zoneCode.substring(0, 500);
                            }
                            orderBuildNewBO.getCommitPick().setPickZoneAll(zoneCode);
                            //拣选策略为RF拣货,分配记录的拣货数量为0
                            if (orderBuildNewBO.getCommitPick().getWorkType().equalsIgnoreCase(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode())) {
                                orderBuildNewBO.getCommitAllocation().forEach(a -> {
                                    //预包商品对应子商品不需要单独按商品拣货
                                    if (!a.getIsPre().equalsIgnoreCase(AllocationIsPreEnum.PRE_CHILD.getCode())) {
                                        a.setPickQty(BigDecimal.ZERO);
                                        a.setSplitQty(BigDecimal.ZERO);
                                    }
                                });
                            }
                            //回写出库单分配数据
                            allocationShipmentOrderNew(orderBuildNewBO.getCommitPackage(), orderBuildNewBO.getCommitShipment(), orderBuildNewBO.getAllocationStockBOList());
                            //完成状态下，没有包裹
                            if (CollectionUtils.isEmpty(orderBuildNewBO.getCommitPackage())) {
                                break;
                            }
                            //重置
                            isContinue = false;
                            num = 0;

                            checkSpike(orderBuildNewBO.getCommitPackage());
                            //拣选单包裹明细-蓝号-重新排序
                            sortBasketNoPickDetail(orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getCommitPackage());
                            //提交数据
//                            System.out.println(orderBuildNewBO.getCommitPick().getPackageQty() + "," + orderBuildNewBO.getCommitPackage().size());
                            commitSyncOrderBO(collectWaveTaskDTO, orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitPackage(), orderBuildNewBO.getCommitShipment(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getAllocationStockBOList());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("汇单【秒杀-合流】分布式连锁失败！错误信息", e);
                throw new BaseException(BaseBizEnum.TIP, "汇单【秒杀-合流】分布式连锁失败");
            } finally {
                lock.unlock();
            }
        }
    }

    /**
     * @param prePackList
     * @param commitAllocation
     * @return void
     * <AUTHOR>
     * @describe: 预包前占补充分配记录
     * @date 2025/4/10 10:58
     */
    private void addPrePackAllocation(List<PackageDTO> prePackList, List<AllocationOrderDTO> commitAllocation, PickDTO commitPick, CollectWaveTaskDTO collectWaveTaskDTO) {
        List<SkuLotAndStockDTO> skuLotAndStockDTOList = skuStockAndLotBiz.getOccupyStockLocationFromTransactionByPre(prePackList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList()), commitPick.getCargoCode());
        if (CollectionUtils.isEmpty(skuLotAndStockDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "汇单预包前占核销库存数据异常");
        }
        for (PackageDTO packageDTO : prePackList) {

            //添加拣选单明细
            if (CollectionUtils.isEmpty(commitPick.getDetailList())) {
                List<PickDetailDTO> detailList = new ArrayList<>();
                detailList.add(buildPickDetail(packageDTO, commitPick, 1, collectWaveTaskDTO.getCreatedBy()));
                commitPick.setDetailList(detailList);
                commitPick.setPackageQty(1L);
                commitPick.setQty(packageDTO.getPackageSkuQty());
            } else {
                List<PickDetailDTO> detailList = commitPick.getDetailList();
                if (detailList.stream().noneMatch(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode()))) {
                    detailList.add(buildPickDetail(packageDTO, commitPick, detailList.size() + 1, collectWaveTaskDTO.getCreatedBy()));
                    commitPick.setDetailList(detailList);
                    commitPick.setPackageQty(commitPick.getPackageQty() + 1L);
                    commitPick.setQty(commitPick.getQty().add(packageDTO.getPackageSkuQty()));
                }
            }
            collectWaveTaskDTO.setNum(collectWaveTaskDTO.getNum() + 1L);

            packageDTO.getListDetail().stream()
                    .filter(a -> Objects.equals(a.getIsPre(), PackIsPreEnum.NORMAL.getCode()))
                    .forEach(it -> {
                        it.setAssignQty(it.getExpQty());
                        it.setStatus(packageDTO.getStatus());
                    });

            //获取分配的明细
            List<PackageDetailDTO> packageDetailDTOList = packageDTO.getListDetail().stream()
                    .filter(a -> !Objects.equals(a.getIsPre(), PackIsPreEnum.NORMAL.getCode()))
                    .sorted(Comparator.comparing(PackageDetailDTO::getSkuCode, Comparator.reverseOrder())
                            .thenComparing(PackageDetailDTO::getSkuCode, Comparator.reverseOrder())
                            .thenComparing(PackageDetailDTO::getSkuLotNo, Comparator.reverseOrder())
                            .thenComparing(PackageDetailDTO::getExternalSkuLotNo, Comparator.reverseOrder())
                            .thenComparing(PackageDetailDTO::getExpireDate, Comparator.reverseOrder())
                            .thenComparing(PackageDetailDTO::getExpireDateStart, Comparator.reverseOrder())
                            .thenComparing(PackageDetailDTO::getExpireDateEnd, Comparator.reverseOrder())
                            .thenComparing(PackageDetailDTO::getWithdrawCompareDate, Comparator.reverseOrder())
                    ).collect(Collectors.toList());
            //获取的
            for (PackageDetailDTO entity : packageDetailDTOList) {
                //获取当前的商品批次库存 前占忽略禁售 当前报告的数据
                //指定属性过滤
                List<SkuLotAndStockDTO> skuLotAndStockDTOOrigin = skuLotAndStockDTOList.stream()
                        .filter(a -> Objects.equals(a.getCargoCode(), entity.getCargoCode()))
                        .filter(a -> Objects.equals(a.getPackageCode(), entity.getPackageCode()))
                        .filter(a -> Objects.equals(a.getSkuCode(), entity.getSkuCode()))
                        .filter(a -> Objects.equals(a.getSkuQuality(), entity.getSkuQuality()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(skuLotAndStockDTOOrigin) && !StringUtils.isEmpty(entity.getSkuLotNo())) {
                    skuLotAndStockDTOOrigin = skuLotAndStockDTOOrigin.stream().filter(a -> Objects.equals(a.getSkuLotNo(), entity.getSkuLotNo())).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(skuLotAndStockDTOOrigin) && !StringUtils.isEmpty(entity.getExternalSkuLotNo())) {
                    skuLotAndStockDTOOrigin = skuLotAndStockDTOOrigin.stream().filter(a -> Objects.equals(a.getExternalSkuLotNo(), entity.getExternalSkuLotNo())).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(skuLotAndStockDTOOrigin) && !StringUtils.isEmpty(entity.getExpireDate()) && entity.getExpireDate() > 0L) {
                    skuLotAndStockDTOOrigin = skuLotAndStockDTOOrigin.stream().filter(a -> Objects.equals(a.getExpireDate(), entity.getExpireDate())).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(skuLotAndStockDTOOrigin) && !StringUtils.isEmpty(entity.getExpireDateStart()) && entity.getExpireDateStart() > 0L) {
                    skuLotAndStockDTOOrigin = skuLotAndStockDTOOrigin.stream().filter(a -> a.getExpireDate() >= entity.getExpireDateStart()).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(skuLotAndStockDTOOrigin) && !StringUtils.isEmpty(entity.getExpireDateEnd()) && entity.getExpireDateEnd() > 0L) {
                    skuLotAndStockDTOOrigin = skuLotAndStockDTOOrigin.stream().filter(a -> a.getExpireDate() <= entity.getExpireDateEnd()).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(skuLotAndStockDTOOrigin) && !StringUtils.isEmpty(entity.getLocationCode())) {
                    skuLotAndStockDTOOrigin = skuLotAndStockDTOOrigin.stream().filter(a -> Objects.equals(a.getLocationCode(), entity.getLocationCode())).collect(Collectors.toList());
                }
                //TODO 目前只考虑淘天 B2B考虑这个残次等级--非退货仓
                //TODO 淘天全面支持残次等级汇单 add 2025-03-19
                if (remoteWarehouseClient.getTaoTianWarehouse(entity.getWarehouseCode())) {
                    if (!CollectionUtils.isEmpty(skuLotAndStockDTOOrigin) && !StringUtils.isEmpty(entity.getInventoryType())) {
                        skuLotAndStockDTOOrigin = skuLotAndStockDTOOrigin.stream().filter(a -> Objects.equals(a.getInventoryType(), entity.getInventoryType())).collect(Collectors.toList());
                    }
                }
                //获取当前商品库存最大数据
                BigDecimal actualQty = skuLotAndStockDTOOrigin.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (actualQty.compareTo(entity.getExpQty()) >= 0) {
                    BigDecimal planQty = entity.getSkuQty();
                    for (SkuLotAndStockDTO stockDTO : skuLotAndStockDTOOrigin) {
                        if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                        if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = planQty;
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                            //组装分配单
                            AllocationIsPreEnum allocationIsPreEnum = AllocationIsPreEnum.PRE;
                            if (Objects.equals(entity.getIsPre(), PackIsPreEnum.PRE.getCode())) {
                                allocationIsPreEnum = AllocationIsPreEnum.PRE;
                            }
                            if (Objects.equals(entity.getIsPre(), PackIsPreEnum.PRE_CHILD.getCode())) {
                                allocationIsPreEnum = AllocationIsPreEnum.PRE_CHILD;
                            }
                            AllocationOrderDTO allocationOrderDTO = buildAllocationOrderDTO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), collectWaveTaskDTO.getWaveCode(), entity, stockDTO, finalPlanQty, collectWaveTaskDTO.getCreatedBy(), allocationIsPreEnum);
                            commitAllocation.add(allocationOrderDTO);
                            planQty = BigDecimal.ZERO;
                            break;
                        } else {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            planQty = planQty.subtract(finalPlanQty);
                            stockDTO.setAvailableQty(BigDecimal.ZERO);
                            //
                            AllocationIsPreEnum allocationIsPreEnum = AllocationIsPreEnum.PRE;
                            if (Objects.equals(entity.getIsPre(), PackIsPreEnum.PRE.getCode())) {
                                allocationIsPreEnum = AllocationIsPreEnum.PRE;
                            }
                            if (Objects.equals(entity.getIsPre(), PackIsPreEnum.PRE_CHILD.getCode())) {
                                allocationIsPreEnum = AllocationIsPreEnum.PRE_CHILD;
                            }
                            AllocationOrderDTO allocationOrderDTO = buildAllocationOrderDTO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), collectWaveTaskDTO.getWaveCode(), entity, stockDTO, finalPlanQty, collectWaveTaskDTO.getCreatedBy(), allocationIsPreEnum);
                            commitAllocation.add(allocationOrderDTO);
                        }
                    }
                } else {
                    log.info("pre_stock_Insufficient_error:package_code:{} sku_code:{}", entity.getPackageCode(), entity.getSkuCode());
                    throw new BaseException(BaseBizEnum.TIP, "pre_stock_Insufficient_error: {} {}", entity.getPackageCode(), entity.getSkuCode());
                }
            }
        }
//        System.out.println(JSONUtil.toJsonStr(commitAllocation));
    }

    /**
     * @param commitPackageDTOList
     * @param commitShipmentOrderDTOList
     * @param commitStockList
     * @return void
     * <AUTHOR>
     * @describe: 拣选单维度回写出库单数据
     * @date 2025/4/10 10:50
     */
    private void allocationShipmentOrderNew(List<PackageDTO> commitPackageDTOList, List<ShipmentOrderDTO> commitShipmentOrderDTOList, List<AllocationStockBO> commitStockList) {

        //获取所有出库单和出库单明细
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setShipmentOrderCodeList(commitPackageDTOList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
        List<ShipmentOrderDTO> originOrderShipDTOList = remoteShipmentOrderClient.getList(shipmentOrderParam);
        if (CollectionUtils.isEmpty(originOrderShipDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "出库订单为空");
        }
        ShipmentOrderDetailParam shipmentOrderDetailParam = new ShipmentOrderDetailParam();
        shipmentOrderDetailParam.setShipmentOrderCodeList(originOrderShipDTOList.stream().map(ShipmentOrderDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
        List<ShipmentOrderDetailDTO> originOrderShipDetailDTOList = remoteShipmentOrderClient.getDetailList(shipmentOrderDetailParam);
        if (CollectionUtils.isEmpty(originOrderShipDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "出库订单明细为空");
        }

        for (PackageDTO packageDTO : commitPackageDTOList) {
            ShipmentOrderDTO shipmentOrderDTO;
            if (CollectionUtils.isEmpty(commitShipmentOrderDTOList) || commitShipmentOrderDTOList.stream().noneMatch(a -> a.getShipmentOrderCode().equals(packageDTO.getShipmentOrderCode()))) {
                shipmentOrderDTO = originOrderShipDTOList.stream().filter(a -> Objects.equals(a.getShipmentOrderCode(), packageDTO.getShipmentOrderCode())).findFirst().orElse(null);
                if (shipmentOrderDTO == null || Objects.equals(shipmentOrderDTO.getStatus(), ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "创建的订单不允许汇单");
                }
                if (Objects.equals(shipmentOrderDTO.getStatus(), ShipmentOrderEnum.STATUS.PREPARE_HANDLER_STATUS.getCode()) || Objects.equals(shipmentOrderDTO.getStatus(), ShipmentOrderEnum.STATUS.PREPARE_HANDLER_FAIL_STATUS.getCode())) {
                    shipmentOrderDTO.setStatus(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode());
                    shipmentOrderDTO.setCollectTime(System.currentTimeMillis());
                }
            } else {
                shipmentOrderDTO = commitShipmentOrderDTOList.stream().filter(a -> a.getShipmentOrderCode().equals(packageDTO.getShipmentOrderCode())).findFirst().orElse(null);
            }
            //普通包裹需要补充全局单号
            if (Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.NORMAL.getCode())) {
                commitStockList.forEach(it -> {
                    if (it.getShipmentOrderCode().equals(shipmentOrderDTO.getShipmentOrderCode())) {
                        it.setGlobalNo(shipmentOrderDTO.getGlobalNo());
                    }
                });
            }
            List<PackageDetailDTO> packageDetailDTOList = packageDTO.getListDetail();
            //回写出库单数据
            List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList;
            if (CollectionUtils.isEmpty(shipmentOrderDTO.getListShipmentOrderDetailDTO())) {
                shipmentOrderDetailDTOList = originOrderShipDetailDTOList.stream().filter(a -> Objects.equals(a.getShipmentOrderCode(), packageDTO.getShipmentOrderCode())).collect(Collectors.toList());
            } else {
                shipmentOrderDetailDTOList = shipmentOrderDTO.getListShipmentOrderDetailDTO();
            }
            if (CollectionUtils.isEmpty(shipmentOrderDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("出库单明细异常%s,", packageDTO.getShipmentOrderCode()));
            }
            // TODO 预包处理 使用原始商品回写
            packageDetailDTOList = packageDetailDTOList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());

            for (PackageDetailDTO entity : packageDetailDTOList) {
                shipmentOrderDetailDTOList.forEach(a -> {
                    if (Objects.equals(a.getId(), entity.getPUid())) {
                        a.setAssignQty(a.getAssignQty().add(entity.getAssignQty()));
                        //TODO add 2021-04-12 分配数量不能大于计划数量
                        if (a.getAssignQty().compareTo(a.getExpSkuQty()) > 0) {
                            throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s商品:%s分配数量不能大于计划数量", a.getShipmentOrderCode(), a.getSkuCode()));
                        }
                        if (Objects.equals(shipmentOrderDTO.getStatus(), ShipmentOrderEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())) {
                            a.setStatus(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode());
                        }
                    }
                });
            }
            shipmentOrderDTO.setListShipmentOrderDetailDTO(shipmentOrderDetailDTOList);
            Iterator<ShipmentOrderDTO> is = commitShipmentOrderDTOList.iterator();
            while (is.hasNext()) {
                ShipmentOrderDTO orderDTO = is.next();
                if (shipmentOrderDTO.getShipmentOrderCode().equals(orderDTO.getShipmentOrderCode())) {
                    is.remove();
                }
            }

            commitShipmentOrderDTOList.add(shipmentOrderDTO);
        }
    }

    /**
     * @param cargoCode
     * @param warehouseCode
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 汇单库存异步查询是否有任务进行
     * @date 2023/6/19 14:39
     */
    private Boolean checkLockTask(String cargoCode, String warehouseCode) {
        CollectLockTaskParam collectLockTaskParam = new CollectLockTaskParam();
        collectLockTaskParam.setCargoCode(cargoCode);
        collectLockTaskParam.setOperateType(CollectLockOperateTypeEnum.PICK_BILL.getCode());
        //只查询往前推7天的波次任务
        Long startTime = System.currentTimeMillis() - CommonConstantUtil.DAY_MILLISECONDS * 10;
        collectLockTaskParam.setCreatedTimeStart(startTime);
        collectLockTaskParam.setStatusList(Arrays.asList(CollectLockStatusEnum.STOCK_DONE.getCode(), CollectLockStatusEnum.BILL_FAIL.getCode()));
        List<CollectLockTaskDTO> collectLockTaskDTOList = remoteCollectLockTaskClient.getList(collectLockTaskParam);
        if (!CollectionUtils.isEmpty(collectLockTaskDTOList)) {
            CollectLockTaskDTO collectLockTaskDTO = collectLockTaskDTOList.stream().sorted(Comparator.comparing(CollectLockTaskDTO::getBillNo, Comparator.naturalOrder())).findFirst().orElse(null);
            log.info("checkLockTask collectLockTaskDTO:{}", JSONUtil.toJsonStr(collectLockTaskDTO));
            if (collectLockTaskDTO == null) {
                return false;
            }
            CollectLockStatusEnum lockStatusEnum = CollectLockStatusEnum.fromCode(collectLockTaskDTO.getStatus());
            switch (lockStatusEnum) {
                //TODO 目前这个状态不会出现
                /**
                 * TODO 库内占用三级库存 (同仓同货主)
                 * 场景1: 库存失败,不需要记录任务(理论上不会出现)
                 * 场景2: 库存成功,单据失败,记录任务(状态单据失败,需要回滚库存)
                 * 场景3: 库存成功,单据成功,记录任务(状态单据失败,需要操作，理论上不会出现)
                 * 场景4: 库存失败,不需要记录任务(理论上不会出现)
                 * TODO ERP占用三级库存 (同仓不同货主)
                 * 场景1:库存不操作,单据失败,记录任务(状态单据完成)
                 *
                 */
                //查询有无库存操作  拣选单无库存作业数据 可直接取消
                case CREATE: //(理论上不会出现)
                    break;
                case STOCK_DONE: //(理论上不会出现)
                    //查询拣选是否存在---存在可结束任务
                    PickDTO pickDTO = remotePickClient.queryPickByPickCode(collectLockTaskDTO.getBillNo());
                    if (pickDTO != null) {
                        collectLockTaskDTO.setRemark("自动扫描结束【库存操作完成(拣选单存在)->完成】");
                        collectLockTaskDTO.setStatus(CollectLockStatusEnum.COMPLETE.getCode());
                        remoteCollectLockTaskClient.modify(collectLockTaskDTO);
                        return false;
                    } else {
                        StockTransactionParam stockTransactionParam = new StockTransactionParam();
                        stockTransactionParam.setParentBillNo(collectLockTaskDTO.getBillNo());
                        List<StockTransactionDTO> stockTransactionDTONewList = remoteStockTransactionClient.getList(stockTransactionParam);
                        //无核销或核销全部取消
                        if (!CollectionUtils.isEmpty(stockTransactionDTONewList) && !stockTransactionDTONewList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                            //TODO 取消库存
                            stockOperationHandler.process(StockOperationContext.builder().warehouseCode(warehouseCode).cargoCode(cargoCode).operationType(OperationTypeEnum.OPERATION_CANCEL_ALLOCATION.getType()).parentBillNo(collectLockTaskDTO.getBillNo()).build());
                        }
                        collectLockTaskDTO.setRemark("自动扫描结束【库存操作完成->库存取消->完成】");
                        collectLockTaskDTO.setStatus(CollectLockStatusEnum.COMPLETE.getCode());
                        remoteCollectLockTaskClient.modify(collectLockTaskDTO);
                        if (collectLockTaskDTOList.size() == 1) {
                            return false;
                        }
                    }
                    break;
                case BILL_FAIL:
                    //拣选单失败去取消拣选单对应的库存
                    StockTransactionParam stockTransactionParam = new StockTransactionParam();
                    stockTransactionParam.setParentBillNo(collectLockTaskDTO.getBillNo());
                    List<StockTransactionDTO> stockTransactionDTONewList = remoteStockTransactionClient.getList(stockTransactionParam);
                    //无核销或核销全部取消
                    if (!CollectionUtils.isEmpty(stockTransactionDTONewList) && !stockTransactionDTONewList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                        //TODO 取消库存
                        stockOperationHandler.process(StockOperationContext.builder().warehouseCode(warehouseCode).cargoCode(cargoCode).operationType(OperationTypeEnum.OPERATION_CANCEL_ALLOCATION.getType()).parentBillNo(collectLockTaskDTO.getBillNo()).build());
                    }
                    collectLockTaskDTO.setRemark("自动扫描结束【单据操作失败->取消库存->完成】");
                    collectLockTaskDTO.setStatus(CollectLockStatusEnum.COMPLETE.getCode());
                    remoteCollectLockTaskClient.modify(collectLockTaskDTO);
                    if (collectLockTaskDTOList.size() == 1) {
                        return false;
                    }
                    break;
                default:
                    log.info("lockStatusEnum-default:{}", JSONUtil.toJsonStr(collectLockTaskDTO));
                    break;
            }
            return true;
        } else {
            return false;
        }
    }


    /**
     * create by: WuXian
     * description:  波次汇单提交任务
     * create time: 2021/6/24 13:03
     *
     * @param collectSubmitBO
     * @return void
     */
    @Async
    public void submitCollectWaveBillBO(CollectSubmitBO collectSubmitBO) {
        log.info("submitCollectWaveBillBO w-c:{} size:{}", collectSubmitBO.getCollectWaveTaskDTO().getWarehouseCode() + collectSubmitBO.getCollectWaveTaskDTO().getCargoCode(), collectSubmitBO.getPackageDTOS().size());
        RpcContextUtil.setWarehouseCode(collectSubmitBO.getCollectWaveTaskDTO().getWarehouseCode());//将仓库编号存放进dubbo上下文
        //减少提交报文(todo 淘天拓展字段超大)
        collectSubmitBO.getPackageDTOS().forEach(it -> it.setExtraJson(null));
        remoteICollectBillClient.submitCollectWaveBillBO(collectSubmitBO);
    }

    /**
     * create by: WuXian
     * description:  杂单(多品)汇单
     * create time: 2021/6/24 13:03
     *
     * @param pickRuleEntity
     * @param collectWaveTaskDTO
     * @return void
     */
    private void miscellaneousMMPickOrder(PickRuleDetailDTO pickRuleEntity, CollectWaveTaskDTO collectWaveTaskDTO, List<String> satisfyPackCodeList) {
        //查询指定结构的包裹（分配库存和汇成拣选单） ----包裹结构新增明细sku的比如 ：sku1,10;sku2:15
        log.info("allocationStockSyncPick miscellaneousMMPickOrder pickRuleEntity:{} collectWaveTaskDTO:{}", JSONUtil.toJsonStr(pickRuleEntity), JSONUtil.toJsonStr(collectWaveTaskDTO));
        List<PackageDTO> packageDTOList = getMiscellaneousPackList(collectWaveTaskDTO, pickRuleEntity.getPackageStruct(), satisfyPackCodeList);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            return;
        }
        //TODO 正次品不能并发，因为锁都是针对商品级别，没有针对商品正次品级别
        //正品汇单
        List<PackageDTO> avlPackageList = packageDTOList.stream().filter(a1 -> Objects.equals(a1.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel())).collect(Collectors.toList());
        //次品汇单
        List<PackageDTO> damagePackageList = packageDTOList.stream().filter(a1 -> Objects.equals(a1.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())).collect(Collectors.toList());
        //最后一个任务结束collectTask
        if (!CollectionUtils.isEmpty(avlPackageList)) {
            miscellaneousToPick(pickRuleEntity, collectWaveTaskDTO, avlPackageList);
        }
        if (!CollectionUtils.isEmpty(damagePackageList)) {
            miscellaneousToPick(pickRuleEntity, collectWaveTaskDTO, damagePackageList);
        }
    }

    /**
     * @param pickRuleEntity
     * @param collectWaveTaskDTO
     * @param originPackageList
     * @return void
     * <AUTHOR>
     * @describe: 合流汇单
     * @date 2024/4/13 14:38
     */
    private void miscellaneousToPickByMerge(PickRuleDetailDTO pickRuleEntity, CollectWaveTaskDTO collectWaveTaskDTO, List<PackageDTO> originPackageList, Boolean pickRuleLast) {
        //满足订单下限
        if (originPackageList.size() < pickRuleEntity.getOrderMinQty() && !Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
            log.info("collect-wave-minQty:{}", collectWaveTaskDTO.getId() + collectWaveTaskDTO.getWaveCode());
            return;
        }
        //淘天合流单二次筛选
        originPackageList = checkTaoTianPackList(originPackageList, pickRuleLast);
        if (CollectionUtils.isEmpty(originPackageList)) {
            return;
        }
        //查询所有包裹明细
        List<PackageDetailDTO> originPackageDetailList = getPackDetailList(collectWaveTaskDTO.getWarehouseCode(), collectWaveTaskDTO.getCargoCode(), originPackageList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(originPackageDetailList)) {
            return;
        }
        //获取开启存储区的货主
        List<String> storageCargoCodeList = skuStockAndLotBiz.getStorageCargoCodeList(Arrays.asList(collectWaveTaskDTO.getCargoCode()));
        //货主是否开启存储位拣货 只考虑B2B
        Boolean isStoragePick = false;
        if (!CollectionUtils.isEmpty(storageCargoCodeList) && collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            isStoragePick = true;
        }
        //上游已占用库存,不需要检查库存是否充足
        Boolean isCheckStock = true;
        Boolean isOccupyFinance = false;
        if (collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            if (originPackageList.stream().anyMatch(a -> OrderTagEnum.NumToEnum(a.getOrderTag()).contains(OrderTagEnum.OCCUPY_LOCATION_STOCK))) {
                isCheckStock = false;
            }
            //获取出库单类型，盘点是否是代采出库模式，库存只需要供应链金融库存
            isOccupyFinance = getShipmentOrderType(originPackageList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
        }
        //获取所有包裹商品的库存数据
        List<SkuLotAndStockDTO> originStockLocationList = findStockLotAndLocation(collectWaveTaskDTO, isStoragePick, isOccupyFinance, originPackageDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(originStockLocationList) && isCheckStock && Objects.equals(collectWaveTaskDTO.getIsPre(), CollectWaveTaskIsPreEnum.NORMAL.getCode())) {
            log.info("stockLocationDTOS_Insufficient_error:package_code:{} sku_code:{}", JSONUtil.toJsonStr(originPackageList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList())), JSONUtil.toJsonStr(originPackageDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList())));
            //组建异常单
            buildAbnormalOrderAll(originPackageList, collectWaveTaskDTO, originPackageDetailList, isStoragePick);
            return;
        }
        int packSize = originPackageList.size();
        //初始化对象（TODO 内存对象）
        CollectSyncOrderBuildNewBO orderBuildNewBO = new CollectSyncOrderBuildNewBO();
        //加锁 与拦截单互斥
        RedissonMultiLock lock = addLockPackage(collectWaveTaskDTO.getWarehouseCode(), originPackageList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
        try {
            lock.lock(60 * 5, TimeUnit.SECONDS);
            //包裹计数
            int num = 0;
            Boolean isContinue = false;
            //合流单库存是否充足
            Map<String, Boolean> authMap = new HashMap<>();
            //合流单计数
            Map<String, Integer> mergeSatisfyCurrentPick = new HashMap<>();
            //按交易单号分组
            Map<String, List<PackageDTO>> packMap = originPackageList.stream().collect(Collectors.groupingBy(it -> StrUtil.join("#", it.getCargoCode(), it.getTradeNo())));
            Map<String, List<PackageDetailDTO>> packDetailMap = originPackageDetailList.stream().collect(Collectors.groupingBy(PackageDetailDTO::getPackageCode));
            //同一批交易单号的一起出路
            //----------------------TODO 内层循环  循环同一个交易单号-----------------------------
            for (PackageDTO packageDTO : originPackageList) {
                String cargoCodeAndTradeNo = StrUtil.join("#", packageDTO.getCargoCode(), packageDTO.getTradeNo());
                if (!authMap.containsKey(cargoCodeAndTradeNo)) {
                    //check 同一个运单号库存是否够用 否移除当前交易单号的所有包裹
                    authMap.put(cargoCodeAndTradeNo, checkMergeStockAuth(collectWaveTaskDTO, ObjectUtil.cloneByStream(originStockLocationList), ObjectUtil.cloneByStream(packMap.get(cargoCodeAndTradeNo)), ObjectUtil.cloneByStream(packDetailMap)));
                    Integer currentNum = pickRuleEntity.getOrderMaxQty().intValue() - num;
                    if (currentNum >= packageDTO.getMergeNum()) {
                        mergeSatisfyCurrentPick.put(cargoCodeAndTradeNo, packageDTO.getMergeNum());
                    }
                }
                //当前拣选单,剩余数量+合流单的拆包数 大于拣选单的上限 不进入当前拣选单
                if (packSize > 0 && !mergeSatisfyCurrentPick.containsKey(cargoCodeAndTradeNo)) {
                    packSize--;
                    if (packSize != 0) {
                        continue;
                    }
                }
                if (authMap.containsKey(cargoCodeAndTradeNo) && authMap.get(cargoCodeAndTradeNo)) {
                    if (num == 0) {
                        //尾波
                        if (packSize < pickRuleEntity.getOrderMinQty() && Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                            isContinue = true;
                        }
                        //非尾波
                        if (packSize >= pickRuleEntity.getOrderMinQty() && !Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                            isContinue = true;
                        }
                        //创建拣选单
                        orderBuildNewBO = new CollectSyncOrderBuildNewBO();
                        orderBuildNewBO.setCommitPick(buildPickDTO(collectWaveTaskDTO, pickRuleEntity));
                    }
                    if (!isContinue) {
                        break;
                    }
                    if (packSize > 0) {
                        //获取包裹明细
                        List<PackageDetailDTO> packageDetailDTOList = packDetailMap.get(packageDTO.getPackageCode());
                        if (CollectionUtils.isEmpty(packageDetailDTOList)) {
                            throw new BaseException(BaseBizEnum.TIP, "包裹明细不存在");
                        }
                        //包裹明细扣减库存数据
                        Boolean reduceStock = getReduceStock(collectWaveTaskDTO, orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getAllocationStockBOList(), packageDTO, packageDetailDTOList, originStockLocationList, isStoragePick);

                        if (reduceStock) {
                            if (Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.PRE.getCode())) {
                                packageDTO.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
                                packageDTO.setListDetail(packageDetailDTOList);
                            }
                            orderBuildNewBO.getCommitPackage().add(packageDTO);
                            num++;
                            orderBuildNewBO.getCommitPick().setExpressBranchName(packageDTO.getExpressBranchName());
                            orderBuildNewBO.getCommitPick().setExpressBranch(packageDTO.getExpressBranch());
                        }
                    }
                    if (packSize > 0) {
                        packSize--;
                    }
                    Boolean isComplete;
                    if (Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                        //处理尾波 尾波打破下限
                        isComplete = packSize < pickRuleEntity.getOrderMinQty() && packSize == 0;
                    } else {
                        //处理非尾波
                        isComplete = (num == pickRuleEntity.getOrderMaxQty()) || (packSize == 0 && num >= pickRuleEntity.getOrderMinQty());
                    }
                    if (isComplete) {
                        //预包需要补充分配记录
                        if (orderBuildNewBO.getCommitPackage().stream().anyMatch(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode()))) {
                            List<PackageDTO> prePackList = orderBuildNewBO.getCommitPackage().stream().filter(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode())).collect(Collectors.toList());
                            addPrePackAllocation(prePackList, orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getCommitPick(), collectWaveTaskDTO);
                        }
                        //库区编码
                        orderBuildNewBO.getCommitPick().setPickZoneAll(orderBuildNewBO.getCommitAllocation().stream().map(AllocationOrderDTO::getZoneCode).distinct().collect(Collectors.joining(",")));
                        String zoneCode = orderBuildNewBO.getCommitPick().getPickZoneAll();
                        if (!StringUtils.isEmpty(zoneCode) && zoneCode.length() > 500) {
                            zoneCode = zoneCode.substring(0, 500);
                        }
                        orderBuildNewBO.getCommitPick().setPickZoneAll(zoneCode);
                        //拣选策略为RF拣货,分配记录的拣货数量为0
                        if (orderBuildNewBO.getCommitPick().getWorkType().equalsIgnoreCase(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode())) {
                            orderBuildNewBO.getCommitAllocation().forEach(a -> {
                                //预包商品对应子商品不需要单独按商品拣货
                                if (!a.getIsPre().equalsIgnoreCase(AllocationIsPreEnum.PRE_CHILD.getCode())) {
                                    a.setPickQty(BigDecimal.ZERO);
                                    a.setSplitQty(BigDecimal.ZERO);
                                }
                            });
                        }
                        //回写出库单分配数据
                        allocationShipmentOrderNew(orderBuildNewBO.getCommitPackage(), orderBuildNewBO.getCommitShipment(), orderBuildNewBO.getAllocationStockBOList());
                        //完成状态下，没有包裹
                        if (CollectionUtils.isEmpty(orderBuildNewBO.getCommitPackage())) {
                            break;
                        }
                        //重置
                        isContinue = false;
                        num = 0;
                        //拣选单包裹明细-蓝号-重新排序
                        sortBasketNoPickDetail(orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getCommitPackage());
                        //提交数据
//                        System.out.println(orderBuildNewBO.getCommitPick().getPackageQty() + "," + orderBuildNewBO.getCommitPackage().size());
                        commitSyncOrderBO(collectWaveTaskDTO, orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitPackage(), orderBuildNewBO.getCommitShipment(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getAllocationStockBOList());
                    }
                }
            }
            //----------------------TODO 内层循环  循环同一个交易单号-----------------------------
        } catch (Exception e) {
            log.error("【miscellaneousToPick-merge】汇单分布式连锁失败-合流！错误信息", e);
            throw new BaseException(BaseBizEnum.TIP, "汇单【杂单-合流】分布式连锁失败");
        } finally {
            lock.unlock();
        }
    }

    /**
     * @param collectWaveTaskDTO
     * @param cloneByStream
     * @param packageDTOList
     * @param packDetailMap
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 合流汇单，判定同一个交易单号合流是否满足
     * @date 2024/4/13 15:03
     */
    private Boolean checkMergeStockAuth(CollectWaveTaskDTO collectWaveTaskDTO, List<SkuLotAndStockDTO> cloneByStream, List<PackageDTO> packageDTOList, Map<String, List<PackageDetailDTO>> packDetailMap) {
        //全预包包裹 //预包前占库存，不需要分析
        if (packageDTOList.stream().allMatch(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode()))) {
            return true;
        }
        //合并所有的包裹明细
        List<PackageDetailDTO> packageDetailDTOList = new ArrayList<>();
        for (PackageDTO entity : packageDTOList) {
            //预包前占库存，不需要分析
            if (Objects.equals(entity.getIsPre(), PackEnum.TYPE.NORMAL.getCode())) {
                packageDetailDTOList.addAll(packDetailMap.get(entity.getPackageCode()));
            }
        }
        List<PackageDetailDTO> packageDetailDTOCollectList = packageDetailDTOList.stream().sorted(Comparator.comparing(PackageDetailDTO::getSkuLotNo).reversed()).collect(Collectors.toList());
        for (PackageDetailDTO entity : packageDetailDTOCollectList) {
            //获取当前的商品批次库存 过滤禁售
            List<SkuLotAndStockDTO> skuLotAndStockDTOS = getPackDetailStock(collectWaveTaskDTO.getWaveCode(), entity, cloneByStream, false, packageDTOList.get(0).getBusinessType(), false);
            //获取当前商品库存最大数据
            BigDecimal actualQty = skuLotAndStockDTOS.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (actualQty.compareTo(entity.getExpQty()) >= 0) {
                BigDecimal planQty = entity.getSkuQty();
                for (SkuLotAndStockDTO stockDTO : skuLotAndStockDTOS) {
                    if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = planQty;
                        entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                        planQty = BigDecimal.ZERO;
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                        planQty = planQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);
                    }
                }
            } else {
                log.info("stock_Insufficient_error checkMergeStockAuth :package_code:{} sku_code:{}", entity.getPackageCode(), entity.getSkuCode());
                //TODO add 异常单
                // add 异常单【合流拆单当前不满足汇单】
                String tip = "";
                if (packageDTOList.stream().map(PackageDTO::getSkuQuality).distinct().count() > 1) {
                    tip = "同交易单号,正次品包裹无法合流";
                }
                for (PackageDTO packageDTO : packageDTOList) {
                    AbnormalOrderDTO abnormalOrder = new AbnormalOrderDTO();
                    //订单的业务类型
                    abnormalOrder.setOrderBusinessType(packageDTO.getBusinessType());
                    abnormalOrder.setWarehouseCode(packageDTO.getWarehouseCode());
                    abnormalOrder.setCargoCode(packageDTO.getCargoCode());
                    abnormalOrder.setBillNo(packageDTO.getPackageCode());
                    abnormalOrder.setBillType(AbnormalOrderEnum.typeEnum.PACKAGE.getValue());
                    abnormalOrder.setBusinessType(AbnormalOrderEnum.bussinessTypeEnum.THREE_LEVEL_STOCK_TYPE.getCode());
                    abnormalOrder.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
                    abnormalOrder.setRetryCount(0);
                    abnormalOrder.setPoNo(packageDTO.getPoNo());
                    abnormalOrder.setSoNo(packageDTO.getSoNo());
                    abnormalOrder.setCarrierCode(packageDTO.getCarrierCode());
                    abnormalOrder.setErrorStatus("合流汇单【库存】" + tip);
                    abnormalOrder.setCreatedBy(packageDTO.getUpdatedBy());
                    abnormalOrder.setCreatedTime(System.currentTimeMillis());
                    abnormalOrder.setErrorMsg("当前交易单号:" + packageDTO.getTradeNo() + "合流汇单库存不满足");
                    CollectWaveAbnormalBillBO collectWaveAbnormalBillBO = new CollectWaveAbnormalBillBO();
                    //添加异常单
                    packageDTO.setStatus(PackEnum.STATUS.ASSGIN_STOCK_STATUS.getCode());
                    collectWaveAbnormalBillBO.setAbnormalOrderDTO(abnormalOrder);
                    collectWaveAbnormalBillBO.setPackageDTO(packageDTO);
                    waveBillGtsService.submitCollectAbnormalOrder(collectWaveAbnormalBillBO);
                }
                return false;
            }
        }
        return true;
    }


    /**
     * create by: WuXian
     * description:  杂单汇单分配
     * create time: 2021/6/24 13:04
     *
     * @param pickRuleEntity
     * @param collectWaveTaskDTO
     * @param originPackageList
     * @return void
     */
    private void miscellaneousToPick(PickRuleDetailDTO pickRuleEntity, CollectWaveTaskDTO collectWaveTaskDTO, List<PackageDTO> originPackageList) {
        //满足订单下限
        if (originPackageList.size() < pickRuleEntity.getOrderMinQty() && !Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
            log.info("collect-wave-minQty:{}", collectWaveTaskDTO.getId() + collectWaveTaskDTO.getWaveCode());
            return;
        }
        //查询所有包裹明细
        List<PackageDetailDTO> originPackageDetailList = getPackDetailList(collectWaveTaskDTO.getWarehouseCode(), collectWaveTaskDTO.getCargoCode(), originPackageList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(originPackageDetailList)) {
            return;
        }
        //获取开启存储区的货主
        List<String> storageCargoCodeList = skuStockAndLotBiz.getStorageCargoCodeList(Arrays.asList(collectWaveTaskDTO.getCargoCode()));
        //货主是否开启存储位拣货 只考虑B2B
        Boolean isStoragePick = false;
        if (!CollectionUtils.isEmpty(storageCargoCodeList) && collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            isStoragePick = true;
        }
        //上游已占用库存,不需要检查库存是否充足
        Boolean isCheckStock = true;
        Boolean isOccupyFinance = false;
        if (collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            if (originPackageList.stream().anyMatch(a -> OrderTagEnum.NumToEnum(a.getOrderTag()).contains(OrderTagEnum.OCCUPY_LOCATION_STOCK))) {
                isCheckStock = false;
            }
            //获取出库单类型，盘点是否是代采出库模式，库存只需要供应链金融库存
            isOccupyFinance = getShipmentOrderType(originPackageList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
        }
        //获取所有包裹商品的库存数据
        List<SkuLotAndStockDTO> originStockLocationList = findStockLotAndLocation(collectWaveTaskDTO, isStoragePick, isOccupyFinance, originPackageDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(originStockLocationList) && isCheckStock && Objects.equals(collectWaveTaskDTO.getIsPre(), CollectWaveTaskIsPreEnum.NORMAL.getCode())) {
            log.info("stockLocationDTOS_Insufficient_error:package_code:{} sku_code:{}", JSONUtil.toJsonStr(originPackageList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList())), JSONUtil.toJsonStr(originPackageDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList())));
            //组建异常单
            buildAbnormalOrderAll(originPackageList, collectWaveTaskDTO, originPackageDetailList, isStoragePick);
            return;
        }
        int packSize = originPackageList.size();

        //初始化对象（TODO 内存对象）
        CollectSyncOrderBuildNewBO orderBuildNewBO = new CollectSyncOrderBuildNewBO();

        //加锁 与拦截单互斥
        RedissonMultiLock lock = addLockPackage(collectWaveTaskDTO.getWarehouseCode(), originPackageList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
        try {
            lock.lock(60 * 5, TimeUnit.SECONDS);
            //包裹计数
            int num = 0;
            Boolean isContinue = false;
            for (PackageDTO packageDTO : originPackageList) {
                if (num == 0) {
                    //尾波
                    if (packSize < pickRuleEntity.getOrderMinQty() && Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                        isContinue = true;
                    }
                    //非尾波
                    if (packSize >= pickRuleEntity.getOrderMinQty() && !Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                        isContinue = true;
                    }
                    orderBuildNewBO = new CollectSyncOrderBuildNewBO();
                    orderBuildNewBO.setCommitPick(buildPickDTO(collectWaveTaskDTO, pickRuleEntity));
                }
                if (!isContinue) {
                    break;
                }
                //获取包裹明细
                List<PackageDetailDTO> packageDetailDTOList = originPackageDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(packageDetailDTOList)) {
                    continue;
                }
                //包裹明细扣减库存数据
                Boolean reduceStock = getReduceStock(collectWaveTaskDTO, orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getAllocationStockBOList(), packageDTO, packageDetailDTOList, originStockLocationList, isStoragePick);

                if (reduceStock) {
                    if (Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.PRE.getCode())) {
                        packageDTO.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
                        packageDTO.setListDetail(packageDetailDTOList);
                    }
                    orderBuildNewBO.getCommitPackage().add(packageDTO);
                    num++;
                    orderBuildNewBO.getCommitPick().setExpressBranchName(packageDTO.getExpressBranchName());
                    orderBuildNewBO.getCommitPick().setExpressBranch(packageDTO.getExpressBranch());
                }
                packSize--;
                Boolean isComplete;
                if (Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                    //处理尾波 尾波打破下限
                    isComplete = packSize < pickRuleEntity.getOrderMinQty() && packSize == 0;
                } else {
                    //处理非尾波
                    isComplete = (num == pickRuleEntity.getOrderMaxQty()) || (packSize == 0 && num >= pickRuleEntity.getOrderMinQty());
                }
                if (isComplete) {
                    //预包需要补充分配记录
                    if (orderBuildNewBO.getCommitPackage().stream().anyMatch(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode()))) {
                        List<PackageDTO> prePackList = orderBuildNewBO.getCommitPackage().stream().filter(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode())).collect(Collectors.toList());
                        addPrePackAllocation(prePackList, orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getCommitPick(), collectWaveTaskDTO);
                    }
                    //库区编码
                    orderBuildNewBO.getCommitPick().setPickZoneAll(orderBuildNewBO.getCommitAllocation().stream().map(AllocationOrderDTO::getZoneCode).distinct().collect(Collectors.joining(",")));
                    String zoneCode = orderBuildNewBO.getCommitPick().getPickZoneAll();
                    if (!StringUtils.isEmpty(zoneCode) && zoneCode.length() > 500) {
                        zoneCode = zoneCode.substring(0, 500);
                    }
                    orderBuildNewBO.getCommitPick().setPickZoneAll(zoneCode);
                    //拣选策略为RF拣货,分配记录的拣货数量为0
                    if (orderBuildNewBO.getCommitPick().getWorkType().equalsIgnoreCase(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode())) {
                        orderBuildNewBO.getCommitAllocation().forEach(a -> {
                            //预包商品对应子商品不需要单独按商品拣货
                            if (!a.getIsPre().equalsIgnoreCase(AllocationIsPreEnum.PRE_CHILD.getCode())) {
                                a.setPickQty(BigDecimal.ZERO);
                                a.setSplitQty(BigDecimal.ZERO);
                            }
                        });
                    }
                    //回写出库单分配数据
                    allocationShipmentOrderNew(orderBuildNewBO.getCommitPackage(), orderBuildNewBO.getCommitShipment(), orderBuildNewBO.getAllocationStockBOList());

                    //完成状态下，没有包裹
                    if (CollectionUtils.isEmpty(orderBuildNewBO.getCommitPackage())) {
                        break;
                    }
                    //重置
                    isContinue = false;
                    num = 0;
                    //拣选单包裹明细-蓝号-重新排序
                    sortBasketNoPickDetail(orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getCommitPackage());
                    //提交数据
                    commitSyncOrderBO(collectWaveTaskDTO, orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitPackage(), orderBuildNewBO.getCommitShipment(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getAllocationStockBOList());
                }
            }

        } catch (Exception e) {
            log.error("【miscellaneousToPick】汇单分布式连锁失败！错误信息", e);
            throw new BaseException(BaseBizEnum.TIP, "汇单【杂单】分布式连锁失败");
        } finally {
            lock.unlock();
        }
    }

    /**
     * @param originPackageList
     * @param pickRuleLast
     * @return java.util.List<com.dt.domain.bill.dto.PackageDTO>
     * <AUTHOR>
     * @describe: 淘天二次处理（秒杀单不移除）
     * @date 2024/4/13 11:16
     */
    private List<PackageDTO> checkTaoTianPackList(List<PackageDTO> originPackageList, Boolean pickRuleLast) {
        List<PackageDTO> noSatisfyMergePackList = new ArrayList<>();
        List<PackageDTO> satisfyMergePackList = new ArrayList<>();
        //获取所有交易单号
        Map<String, List<PackageDTO>> packMap = originPackageList.stream().collect(Collectors.groupingBy(it -> StrUtil.join("#", it.getCargoCode(), it.getTradeNo())));
        Map<String, String> msgMap = new HashMap<>();
//        log.info("checkTaoTianPackList:{} {}", pickRuleLast, JSONUtil.toJsonStr(packMap));
        for (Map.Entry<String, List<PackageDTO>> packTradeNo : packMap.entrySet()) {
            String tradeNoAndCargoCode = packTradeNo.getKey();
            List<PackageDTO> packageDTOTemp = packTradeNo.getValue();
            Integer mergeNum = packageDTOTemp.get(0).getMergeNum();
            log.info("checkTaoTianPackList-mergeNum:{} size:{}", mergeNum, packageDTOTemp.size());
            if (mergeNum == packageDTOTemp.size()) {
                satisfyMergePackList.addAll(packageDTOTemp);
            } else {
                noSatisfyMergePackList.addAll(packageDTOTemp);
                msgMap.put(tradeNoAndCargoCode, "合流单交易单号:" + packageDTOTemp.get(0).getTradeNo() + ",总包裹数:" + mergeNum + ",当前包裹数:" + packageDTOTemp.size());
            }
        }
        if (!CollectionUtils.isEmpty(noSatisfyMergePackList) && pickRuleLast) {
            // add 异常单【合流拆单当前不满足汇单】
            for (PackageDTO packageDTO : noSatisfyMergePackList) {
                AbnormalOrderDTO abnormalOrder = new AbnormalOrderDTO();
                //订单的业务类型
                abnormalOrder.setOrderBusinessType(packageDTO.getBusinessType());
                abnormalOrder.setWarehouseCode(packageDTO.getWarehouseCode());
                abnormalOrder.setCargoCode(packageDTO.getCargoCode());
                abnormalOrder.setBillNo(packageDTO.getPackageCode());
                abnormalOrder.setBillType(AbnormalOrderEnum.typeEnum.PACKAGE.getValue());
                abnormalOrder.setBusinessType(AbnormalOrderEnum.bussinessTypeEnum.THREE_LEVEL_STOCK_TYPE.getCode());
                abnormalOrder.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
                abnormalOrder.setRetryCount(0);
                abnormalOrder.setPoNo(packageDTO.getPoNo());
                abnormalOrder.setSoNo(packageDTO.getSoNo());
                abnormalOrder.setCarrierCode(packageDTO.getCarrierCode());
                abnormalOrder.setErrorStatus("合流汇单");
                abnormalOrder.setCreatedBy(packageDTO.getUpdatedBy());
                abnormalOrder.setCreatedTime(System.currentTimeMillis());
                abnormalOrder.setErrorMsg(msgMap.getOrDefault(StrUtil.join("#", packageDTO.getCargoCode(), packageDTO.getTradeNo()), "当前交易单号合流汇单不满足"));
                CollectWaveAbnormalBillBO collectWaveAbnormalBillBO = new CollectWaveAbnormalBillBO();
                //添加异常单
                packageDTO.setStatus(PackEnum.STATUS.ASSGIN_STOCK_STATUS.getCode());
                collectWaveAbnormalBillBO.setAbnormalOrderDTO(abnormalOrder);
                collectWaveAbnormalBillBO.setPackageDTO(packageDTO);
                waveBillGtsService.submitCollectAbnormalOrder(collectWaveAbnormalBillBO);
            }
        }
        satisfyMergePackList = satisfyMergePackList.stream().sorted(Comparator.comparing(PackageDTO::getTradeNo, Comparator.naturalOrder())).collect(Collectors.toList());
        return satisfyMergePackList;
    }

    /**
     * @param shipmentOrderCodeList
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe:
     * @date 2023/7/6 9:56
     */
    private Boolean getShipmentOrderType(List<String> shipmentOrderCodeList) {
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setShipmentOrderCodeList(shipmentOrderCodeList);
        List<ShipmentOrderDTO> shipmentOrderDTOList = remoteShipmentOrderClient.getAppointMultipleParam(shipmentOrderParam, LambdaHelpUtils.convertToFieldNameList(ShipmentOrderDTO::getShipmentOrderCode, ShipmentOrderDTO::getOrderType));
        if (!CollectionUtils.isEmpty(shipmentOrderDTOList) && shipmentOrderDTOList.stream().anyMatch(a -> a.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.PURCHASE_REDEEM_OUT.getCode()))) {
            return true;
        }
        return false;
    }

    /**
     * @param commitPick
     * @param commitPackageList
     * @param commitShipmentList
     * @param commitAllocationList
     * @param commitStockList
     * @return void
     * @author: WuXian
     * description:  汇单提交数据
     * create time: 2021/9/10 16:16
     */
    private void commitSyncOrderBO(CollectWaveTaskDTO collectWaveTaskDTO, PickDTO commitPick, List<PackageDTO> commitPackageList, List<ShipmentOrderDTO> commitShipmentList, List<AllocationOrderDTO> commitAllocationList, List<AllocationStockBO> commitStockList) throws Exception {
        CollectSyncOrderBO syncOrderBO = new CollectSyncOrderBO();
        syncOrderBO.setCommitPick(commitPick);
        syncOrderBO.setCommitPackage(commitPackageList);
        syncOrderBO.setCommitShipment(commitShipmentList);
        if (CollectionUtils.isEmpty(commitAllocationList)) {
            throw new BaseException(BaseBizEnum.TIP, "分配记录不能为空:" + collectWaveTaskDTO.getWaveCode() + commitPick.getPickCode() + commitPackageList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
        }
        syncOrderBO.setCommitAllocation(commitAllocationList);
        syncOrderBO.setAllocationStockBOList(commitStockList);
        List<PackageLogDTO> packageLogDTOList = new ArrayList<>();
        for (PackageDTO packageDTO : commitPackageList) {
            PackageLogDTO packageLogDTO = new PackageLogDTO();
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setCargoCode(packageDTO.getCargoCode());
            packageLogDTO.setPackageCode(packageDTO.getPackageCode());
            packageLogDTO.setWarehouseCode(packageDTO.getWarehouseCode());
            packageLogDTO.setOpBy(collectWaveTaskDTO.getCreatedBy());
            packageLogDTO.setCreatedBy(collectWaveTaskDTO.getCreatedBy());
            String opContent = String.format("包裹已汇总,单号:%s", packageDTO.getPackageCode());
            if (!StringUtils.isEmpty(collectWaveTaskDTO.getZoneCode())) {
                String zoneCode = collectWaveTaskDTO.getZoneCode();
                if (zoneCode.length() > 100) {
                    zoneCode = zoneCode.substring(0, 100);
                }
                opContent = opContent + ",指定库区汇单:" + zoneCode;
            }
            packageLogDTO.setOpContent(opContent);
            packageLogDTOList.add(packageLogDTO);
        }
        syncOrderBO.setPackageLogDTOList(packageLogDTOList);
        List<ShipmentOrderLogDTO> shipmentOrderLogDTOList = new ArrayList<>();
        for (ShipmentOrderDTO entity : commitShipmentList) {
            ShipmentOrderLogDTO shipmentOrderLogDTO = new ShipmentOrderLogDTO();
            shipmentOrderLogDTO.setOpDate(System.currentTimeMillis());
            shipmentOrderLogDTO.setCargoCode(entity.getCargoCode());
            shipmentOrderLogDTO.setWarehouseCode(entity.getWarehouseCode());
            shipmentOrderLogDTO.setShipmentOrderCode(entity.getShipmentOrderCode());
            shipmentOrderLogDTO.setOpBy(collectWaveTaskDTO.getCreatedBy());
            shipmentOrderLogDTO.setOpRemark("");
            shipmentOrderLogDTO.setCreatedBy(collectWaveTaskDTO.getCreatedBy());
            String opContent = String.format("包裹已汇总,单号:%s", entity.getShipmentOrderCode());
            if (!StringUtils.isEmpty(collectWaveTaskDTO.getZoneCode())) {
                String zoneCode = collectWaveTaskDTO.getZoneCode();
                if (zoneCode.length() > 100) {
                    zoneCode = zoneCode.substring(0, 100);
                }
                opContent = opContent + ",指定库区汇单:" + zoneCode;
            }
            shipmentOrderLogDTO.setOpContent(opContent);
            shipmentOrderLogDTOList.add(shipmentOrderLogDTO);
        }
        syncOrderBO.setShipmentOrderLogDTOList(shipmentOrderLogDTOList);
//        System.out.println(JSONUtil.toJsonStr(syncOrderBO));
        log.info("submitCollectSyncPickAndStock:{}", JSONUtil.toJsonStr(syncOrderBO));
//        //提交数据---submit
        waveBillGtsService.submitCollectSyncPickAndStock(syncOrderBO);
    }

    /**
     * @param packageDTOList
     * @param collectWaveTaskDTO
     * @param packageDetailDTOList
     * @param isStoragePick
     * @return void
     * @author: WuXian
     * description:  全部异常单
     * create time: 2021/8/17 16:17
     */
    private void buildAbnormalOrderAll(List<PackageDTO> packageDTOList, CollectWaveTaskDTO collectWaveTaskDTO, List<PackageDetailDTO> packageDetailDTOList, Boolean isStoragePick) {
        for (PackageDTO packageDTO : packageDTOList) {
            AbnormalOrderDTO abnormalOrder = new AbnormalOrderDTO();
            //订单的业务类型
            abnormalOrder.setOrderBusinessType(packageDTO.getBusinessType());
            abnormalOrder.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
            abnormalOrder.setCargoCode(collectWaveTaskDTO.getCargoCode());
            abnormalOrder.setBillNo(packageDTO.getPackageCode());
            abnormalOrder.setBillType(AbnormalOrderEnum.typeEnum.PACKAGE.getValue());
            abnormalOrder.setBusinessType(AbnormalOrderEnum.bussinessTypeEnum.THREE_LEVEL_STOCK_TYPE.getCode());
            abnormalOrder.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
            abnormalOrder.setRetryCount(0);
            abnormalOrder.setPoNo(packageDTO.getPoNo());
            abnormalOrder.setSoNo(packageDTO.getSoNo());
            abnormalOrder.setCarrierCode(packageDTO.getCarrierCode());
            abnormalOrder.setErrorStatus("无库存");
            abnormalOrder.setCreatedBy(packageDTO.getUpdatedBy());
            abnormalOrder.setCreatedTime(System.currentTimeMillis());
            List<String> skuCodeList = packageDetailDTOList.stream().filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode())).map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
            String skuCodeMsg = String.join(";", skuCodeList.stream().sorted().collect(Collectors.toList()));
            String message = String.format("商品【%s】拣货区库存不足", skuCodeMsg);
            if (!StringUtils.isEmpty(message) && message.length() > 2048) {
                message = message.substring(0, 2048);
            }
            abnormalOrder.setErrorMsg(message);
            CollectWaveAbnormalBillBO collectWaveAbnormalBillBO = new CollectWaveAbnormalBillBO();
            //添加异常单
            packageDTO.setStatus(PackEnum.STATUS.ASSGIN_STOCK_STATUS.getCode());
            collectWaveAbnormalBillBO.setAbnormalOrderDTO(abnormalOrder);
            collectWaveAbnormalBillBO.setPackageDTO(packageDTO);
            waveBillGtsService.submitCollectAbnormalOrder(collectWaveAbnormalBillBO);
        }
    }


    /**
     * create by: WuXian
     * description:  拣选单蓝号二次排序
     * create time: 2021/6/24 13:05
     *
     * @param commitPick
     * @param commitAllocation
     * @param commitPackageList
     * @return void
     */
    private void sortBasketNoPickDetail(PickDTO commitPick, List<AllocationOrderDTO> commitAllocation, List<PackageDTO> commitPackageList) {
        List<String> packCodeList;
        //淘天 合流拣选单,使用 TODO 2024-04-12
        if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode()) && commitPick.getOrderTag() != null && commitPick.getOrderTag() > 0 && PickOrderTagEnum.NumToEnum(commitPick.getOrderTag()).contains(PickOrderTagEnum.ORDER_MERG)) {
            packCodeList = commitPackageList.stream().sorted(Comparator.comparing(PackageDTO::getTradeNo)).map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList());
        } else {
            packCodeList = commitAllocation.stream().sorted(Comparator.comparing(AllocationOrderDTO::getPickSeq)).map(AllocationOrderDTO::getPackageCode).distinct().collect(Collectors.toList());
        }
        List<PickDetailDTO> pickDetailDTOList = commitPick.getDetailList();
        AtomicInteger i = new AtomicInteger(1);
        for (String entity : packCodeList) {
            pickDetailDTOList.forEach(a -> {
                if (entity.equals(a.getPackageCode())) {
                    a.setBasketNo(String.valueOf(i.get()));
                    i.getAndIncrement();
                }
            });
        }
        commitPick.setDetailList(pickDetailDTOList);
    }


    /**
     * create by: WuXian
     * description:  杂单获取包裹
     * create time: 2021/6/24 13:05
     *
     * @param collectWaveTaskDTO
     * @param packageStruct
     * @return java.util.List<com.dt.domain.bill.dto.PackageDTO>
     */
    private List<PackageDTO> getMiscellaneousPackList(CollectWaveTaskDTO collectWaveTaskDTO, String packageStruct, List<String> satisfyPackCodeList) {
        //店铺目前未加入强制分组,在生成汇单任务未加入店铺
        PackageParam packageParam = new PackageParam();
        packageParam.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        packageParam.setCargoCode(collectWaveTaskDTO.getCargoCode());
        packageParam.setCarrierCode(collectWaveTaskDTO.getCarrierCode());
        packageParam.setWaveCode(collectWaveTaskDTO.getWaveCode());
        packageParam.setSalePlatformT(collectWaveTaskDTO.getSalePlatform());
        packageParam.setSaleShopId(collectWaveTaskDTO.getSaleShopId());
        packageParam.setExpressBranchNotBlank(collectWaveTaskDTO.getExpressBranch());
        packageParam.setPackageStructList(Arrays.asList(packageStruct.split(",")));
        packageParam.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
        if (!CollectionUtils.isEmpty(satisfyPackCodeList)) {
            packageParam.setPackageCodeList(satisfyPackCodeList);
        }
        List<PackageDTO> packageDTOList = remotePackageClient.getListByPage(packageParam);
        return packageDTOList;
    }

    /**
     * create by: WuXian
     * description:  秒杀单分配
     * create time: 2021/6/24 13:06
     *
     * @param pickRuleEntity
     * @param collectWaveTaskDTO
     * @return void
     */
    private void spikePickOrder(PickRuleDetailDTO pickRuleEntity, CollectWaveTaskDTO collectWaveTaskDTO, List<String> satisfyPackCodeList) {
        //分析包裹结构 where warehouseCode,cargoCode,carrierCode,salePlatform
        log.info("allocationStockSyncPick spikePickOrder pickRuleEntity:{} collectWaveTaskDTO:{}", JSONUtil.toJsonStr(pickRuleEntity), JSONUtil.toJsonStr(collectWaveTaskDTO));
        //秒杀组装参数
        CollectWaveBillParam param = buildBillSpikeParam(collectWaveTaskDTO, pickRuleEntity.getPackageStruct());
        if (!CollectionUtils.isEmpty(satisfyPackCodeList)) {
            param.setPackageCodeList(satisfyPackCodeList);
        }
        //正次品分开汇单 秒杀分组
        List<CollectWaveDTO> collectWaveDTOList = remotePackageClient.querySpikeCollectWave(param);
        if (CollectionUtils.isEmpty(collectWaveDTOList)) {
            return;
        }
        for (CollectWaveDTO entity : collectWaveDTOList) {
            //满足下限或者尾波
            if (entity.getNum() < pickRuleEntity.getOrderMinQty() && !Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                log.info("collect-wave-minQty:{}", collectWaveTaskDTO.getId() + collectWaveTaskDTO.getWaveCode());
                continue;
            }
            //查询指定结构的包裹（分配库存和汇成拣选单） ----包裹结构新增明细sku的比如 ：sku1,10;sku2:15
            List<PackageDTO> originPackageList = getSpikePackList(collectWaveTaskDTO.getWaveCode(), entity, satisfyPackCodeList);
            if (CollectionUtils.isEmpty(originPackageList)) {
                continue;
            }
            //过滤正次品
            originPackageList = originPackageList.stream().filter(a -> a.getSkuQuality().equalsIgnoreCase(entity.getSkuQuality())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(originPackageList)) {
                continue;
            }
            //查询所有包裹明细
            List<PackageDetailDTO> originPackageDetailList = getPackDetailList(entity.getWarehouseCode(), entity.getCargoCode(), originPackageList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(originPackageDetailList)) {
                continue;
            }
            //获取开启存储区的货主
            List<String> storageCargoCodeList = skuStockAndLotBiz.getStorageCargoCodeList(Arrays.asList(collectWaveTaskDTO.getCargoCode()));
            //货主是否开启存储位拣货 只考虑B2B
            Boolean isStoragePick = false;
            if (!CollectionUtils.isEmpty(storageCargoCodeList) && collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
                isStoragePick = true;
            }
            Boolean isOccupyFinance = true;
            if (collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
                //获取出库单类型，盘点是否是代采出库模式，库存只需要供应链金融库存
                isOccupyFinance = getShipmentOrderType(originPackageList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
            }
            //获取所有包裹商品的库存数据
            List<SkuLotAndStockDTO> originStockLocationList = findStockLotAndLocation(collectWaveTaskDTO, isStoragePick, isOccupyFinance, originPackageDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList()));
            //无库存数据都是异常单
            if (CollectionUtils.isEmpty(originStockLocationList) && Objects.equals(collectWaveTaskDTO.getIsPre(), CollectWaveTaskIsPreEnum.NORMAL.getCode())) {
                log.info("stockLocationDTOS_Insufficient_error:package_code:{} sku_code:{}", JSONUtil.toJsonStr(originPackageList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList())), JSONUtil.toJsonStr(originPackageDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList())));
                buildAbnormalOrderAll(originPackageList, collectWaveTaskDTO, originPackageDetailList, isStoragePick);
                continue;
            }
            int packSize = originPackageList.size();
            //初始化对象（TODO 内存对象）
            CollectSyncOrderBuildNewBO orderBuildNewBO = new CollectSyncOrderBuildNewBO();
            //加锁 -- 与拦截单互斥
            RedissonMultiLock lock = addLockPackage(collectWaveTaskDTO.getWarehouseCode(), originPackageList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
            try {
                lock.lock(60 * 5, TimeUnit.SECONDS);
                //包裹计数
                int num = 0;
                Boolean isContinue = false;
                for (PackageDTO packageDTO : originPackageList) {
                    if (num == 0) {
                        //尾波
                        if (packSize < pickRuleEntity.getOrderMinQty() && Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                            isContinue = true;
                        }
                        //非尾波
                        if (packSize >= pickRuleEntity.getOrderMinQty() && !Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                            isContinue = true;
                        }
                        orderBuildNewBO = new CollectSyncOrderBuildNewBO();
                        orderBuildNewBO.setCommitPick(buildPickDTO(collectWaveTaskDTO, pickRuleEntity));
                    }
                    if (!isContinue) {
                        break;
                    }
                    //获取包裹明细
                    List<PackageDetailDTO> packageDetailDTOList = originPackageDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(packageDetailDTOList)) {
                        continue;
                    }
                    //包裹明细扣减库存数据
                    Boolean reduceStock = getReduceStock(collectWaveTaskDTO, orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getAllocationStockBOList(), packageDTO, packageDetailDTOList, originStockLocationList, isStoragePick);
                    if (reduceStock) {
                        if (Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.PRE.getCode())) {
                            packageDTO.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
                            packageDTO.setListDetail(packageDetailDTOList);
                        }
                        orderBuildNewBO.getCommitPackage().add(packageDTO);
                        num++;
                        orderBuildNewBO.getCommitPick().setExpressBranchName(packageDTO.getExpressBranchName());
                        orderBuildNewBO.getCommitPick().setExpressBranch(packageDTO.getExpressBranch());
                    }
                    packSize--;
                    //处理尾波
                    Boolean isComplete = false;
                    if (Objects.equals(collectWaveTaskDTO.getLastWave(), "true")) {
                        //处理尾波  尾波打破下限
                        isComplete = packSize < pickRuleEntity.getOrderMinQty() && packSize == 0;
                    } else {
                        //处理非尾波
                        isComplete = (num == pickRuleEntity.getOrderMaxQty()) || (packSize == 0 && num >= pickRuleEntity.getOrderMinQty());
                    }
                    if (isComplete) {
                        //预包需要补充分配记录
                        if (orderBuildNewBO.getCommitPackage().stream().anyMatch(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode()))) {
                            List<PackageDTO> prePackList = orderBuildNewBO.getCommitPackage().stream().filter(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode())).collect(Collectors.toList());
                            addPrePackAllocation(prePackList, orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getCommitPick(), collectWaveTaskDTO);
                        }
                        //库区编码
                        orderBuildNewBO.getCommitPick().setPickZoneAll(orderBuildNewBO.getCommitAllocation().stream().map(AllocationOrderDTO::getZoneCode).distinct().collect(Collectors.joining(",")));
                        String zoneCode = orderBuildNewBO.getCommitPick().getPickZoneAll();
                        if (!StringUtils.isEmpty(zoneCode) && zoneCode.length() > 500) {
                            zoneCode = zoneCode.substring(0, 500);
                        }
                        orderBuildNewBO.getCommitPick().setPickZoneAll(zoneCode);
                        //拣选策略为RF拣货,分配记录的拣货数量为0
                        if (orderBuildNewBO.getCommitPick().getWorkType().equalsIgnoreCase(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode())) {
                            orderBuildNewBO.getCommitAllocation().forEach(a -> {
                                //预包商品对应子商品不需要单独按商品拣货
                                if (!a.getIsPre().equalsIgnoreCase(AllocationIsPreEnum.PRE_CHILD.getCode())) {
                                    a.setPickQty(BigDecimal.ZERO);
                                    a.setSplitQty(BigDecimal.ZERO);
                                }
                            });
                        }
                        //回写出库单分配数据
                        allocationShipmentOrderNew(orderBuildNewBO.getCommitPackage(), orderBuildNewBO.getCommitShipment(), orderBuildNewBO.getAllocationStockBOList());
                        //完成状态下，没有包裹
                        if (CollectionUtils.isEmpty(orderBuildNewBO.getCommitPackage())) {
                            break;
                        }
                        //重置
                        isContinue = false;
                        num = 0;
                        checkSpike(orderBuildNewBO.getCommitPackage());
                        //拣选单包裹明细-蓝号-重新排序
                        sortBasketNoPickDetail(orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getCommitPackage());
                        //提交数据
                        commitSyncOrderBO(collectWaveTaskDTO, orderBuildNewBO.getCommitPick(), orderBuildNewBO.getCommitPackage(), orderBuildNewBO.getCommitShipment(), orderBuildNewBO.getCommitAllocation(), orderBuildNewBO.getAllocationStockBOList());
                    }
                }
            } catch (Exception e) {
                log.error("汇单【秒杀】分布式连锁失败！错误信息", e);
                throw new BaseException(BaseBizEnum.TIP, "汇单【秒杀】分布式连锁失败");
            } finally {
                lock.unlock();
            }
        }
    }

    /**
     * @param commitPackageList
     * @return void
     * <AUTHOR>
     * @describe: 秒杀保护
     * @date 2022/8/23 17:22
     */
    private void checkSpike(List<PackageDTO> commitPackageList) {
        if (!CollectionUtils.isEmpty(commitPackageList)) {
            Long count = commitPackageList.stream().map(packageDTO -> {
                return StrUtil.join("#", packageDTO.getCargoCode().toUpperCase(), packageDTO.getSalePlatform().toUpperCase(), packageDTO.getCarrierCode().toUpperCase(), packageDTO.getSpikeAnalysisSku());
            }).distinct().count();
            if (count > 1) {
                log.error("checkSpike commitPackageList:{}", JSONUtil.toJsonStr(commitPackageList));
                throw new BaseException(BaseBizEnum.TIP, "秒杀包裹结构不一致");
            }
        }
    }


    /**
     * @param collectWaveTaskDTO
     * @param commitPick
     * @param commitAllocationList
     * @param commitStockList
     * @param packageDTO
     * @param packageDetailDTOList
     * @param originStockLocationList
     * @param isStoragePick
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  汇单扣减库存核心
     * create time: 2021/9/9 11:14
     */
    private Boolean getReduceStock(CollectWaveTaskDTO collectWaveTaskDTO, PickDTO commitPick, List<AllocationOrderDTO> commitAllocationList, List<AllocationStockBO> commitStockList, PackageDTO packageDTO, List<PackageDetailDTO> packageDetailDTOList, List<SkuLotAndStockDTO> originStockLocationList, Boolean isStoragePick) {
        Boolean reduceStock = false;
        //TODO 2025-04-09 预包改前占
//        //预包包裹扣减库存
        if (Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.PRE.getCode())) {
            // TODO 预包前占库存
            return true;
        }
        //非预包扣减库存
        packageDetailDTOList = packageDetailDTOList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
        List<SkuLotAndStockDTO> skuLotAndStockDTOListNormal;
        List<String> locationTypeStoragePickList = Arrays.asList(LocationTypeEnum.LOCATION_TYPE_PICK.getType(), LocationTypeEnum.LOCATION_TYPE_STORE.getType());
        //B2B存储区拣货
        if (packageDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            Boolean isOccupyFinance = getShipmentOrderType(Arrays.asList(packageDTO.getShipmentOrderCode()));
            if (isOccupyFinance) {
                skuLotAndStockDTOListNormal = originStockLocationList.stream().filter(a -> Objects.equals(a.getLocationType(), LocationTypeEnum.LOCATION_TYPE_FINANCE.getType())).collect(Collectors.toList());
            } else {
                //存储位拣货
                if (isStoragePick) {
                    skuLotAndStockDTOListNormal = originStockLocationList.stream().filter(a -> locationTypeStoragePickList.contains(a.getLocationType())).collect(Collectors.toList());
                    //供应链金融
                } else {
                    //普通拣选位拣货
                    skuLotAndStockDTOListNormal = originStockLocationList.stream().filter(a -> Objects.equals(a.getLocationType(), LocationTypeEnum.LOCATION_TYPE_PICK.getType())).collect(Collectors.toList());
                }
            }
        } else {
            skuLotAndStockDTOListNormal = originStockLocationList.stream().filter(a -> a.getLocationType().equalsIgnoreCase(LocationTypeEnum.LOCATION_TYPE_PICK.getType())).collect(Collectors.toList());
        }
        //CW业务单独走
        if (collectWaveTaskDTO.getCwCargo() != null && collectWaveTaskDTO.getCwCargo()) {
            reduceStock = reduceCWOriginStockByPackDetail(collectWaveTaskDTO, commitPick, commitAllocationList, commitStockList, packageDTO, packageDetailDTOList, skuLotAndStockDTOListNormal, AllocationIsPreEnum.NORMAL, isStoragePick);
        } else {
            reduceStock = reduceOriginStockByPackDetail(collectWaveTaskDTO, commitPick, commitAllocationList, commitStockList, packageDTO, packageDetailDTOList, skuLotAndStockDTOListNormal, AllocationIsPreEnum.NORMAL, isStoragePick);
        }
        return reduceStock;
    }

    /**
     * @param collectWaveTaskDTO
     * @param commitPick
     * @param commitAllocationList
     * @param commitStockList
     * @param packageDTO
     * @param packageDetailDTOList
     * @param originStockLocationDTOList
     * @param allocationIsPreEnum
     * @param isStoragePick
     * @return
     * @description CW扣减库存
     */
    private Boolean reduceCWOriginStockByPackDetail(CollectWaveTaskDTO collectWaveTaskDTO, PickDTO commitPick, List<AllocationOrderDTO> commitAllocationList, List<AllocationStockBO> commitStockList, PackageDTO packageDTO, List<PackageDetailDTO> packageDetailDTOList, List<SkuLotAndStockDTO> originStockLocationDTOList, AllocationIsPreEnum allocationIsPreEnum, Boolean isStoragePick) {
        List<AllocationOrderDTO> allocationOrderDTOList = new ArrayList<>();
        List<AllocationStockBO> stockCollectBOList = new ArrayList<>();

        //是否校验禁售 true 是  false 非 是销毁出库 不校验禁售
        Boolean isIgnorePeriod = false;

        //订单标记忽略效期
        if (OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).stream().anyMatch(a -> a.getCode().equals(OrderTagEnum.IGNORE_VALIDITY_PERIOD.getCode()))) {
            isIgnorePeriod = true;
        }
        //是否销毁出库 true 是  false 非 是销毁出库 不校验禁售
        if (packageDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            Boolean isDestroy = getShipmentIsDestroy(packageDTO.getShipmentOrderCode());
            if (isDestroy) {
                isIgnorePeriod = true;
            }
        }
        //处理上游占用三级库存的包裹 获取已经分配的库存
        Boolean isOccupyStock = false;
        if (OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).contains(OrderTagEnum.OCCUPY_LOCATION_STOCK)) {
            isOccupyStock = true;
        }
        //CW需要指定批次编码或外部批次编码
        if (packageDetailDTOList.stream().anyMatch(a -> StringUtils.isEmpty(a.getSkuLotNo()) && StringUtils.isEmpty(a.getExternalSkuLotNo())) || isOccupyStock) {
            String errorCWMsg = packageDetailDTOList.stream().filter(a -> StringUtils.isEmpty(a.getSkuLotNo()) && StringUtils.isEmpty(a.getExternalSkuLotNo())).map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.joining(","));
            errorCWMsg = "CW业务需要全部指定批次或外部批次编码才能进行汇单,请核查,商品编码" + errorCWMsg;
            if (!StringUtils.isEmpty(errorCWMsg) && errorCWMsg.length() > 2048) {
                errorCWMsg = errorCWMsg.substring(0, 2048);
            }
            if (isOccupyStock) {
                errorCWMsg = "CW业务,上游占用三级库存,库内不好做整库位或托盘，箱码二次分配,请核查";
            }
            buildAbnormalOrder(packageDTO, collectWaveTaskDTO, errorCWMsg, isStoragePick);
            return false;
        }

        //获取当前包裹所有的商品
        List<String> skuCodeList = packageDetailDTOList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList());

        List<SkuLotAndStockDTO> skuLotAndStockDTOList = originStockLocationDTOList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDTO.getCargoCode())).filter(a -> skuCodeList.contains(a.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDTO.getSkuQuality())).collect(Collectors.toList());

        //无库存 生成异常单
        if (CollectionUtils.isEmpty(skuLotAndStockDTOList)) {
            String skuCodeMsg = String.join(";", skuCodeList.stream().sorted().collect(Collectors.toList()));
            String message = String.format("商品【%s】拣选区分配库存不足", skuCodeMsg);
            if (!StringUtils.isEmpty(message) && message.length() > 2048) {
                message = message.substring(0, 2048);
            }
            buildAbnormalOrder(packageDTO, collectWaveTaskDTO, message, isStoragePick);
            return false;
        }
        //库存数据深复制 用于分配
        List<SkuLotAndStockDTO> skuLotAndStockDTOTempList = ObjectUtil.cloneByStream(skuLotAndStockDTOList);
        if (Objects.equals(packageDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            skuLotAndStockDTOTempList = skuLotAndStockDTOTempList.stream().filter(a -> !a.getLocationType().equalsIgnoreCase(LocationTypeEnum.LOCATION_TYPE_FINANCE.getType())).collect(Collectors.toList());
        }
        //库存数据深复制 用于异常单计算
        List<SkuLotAndStockDTO> skuLotAndStockDTOAbnormalOrderTempList = ObjectUtil.cloneByStream(skuLotAndStockDTOList);

        if (Objects.equals(packageDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            skuLotAndStockDTOAbnormalOrderTempList = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> !a.getLocationType().equalsIgnoreCase(LocationTypeEnum.LOCATION_TYPE_FINANCE.getType())).collect(Collectors.toList());
        }
        //深复制包裹明细用于异常单
        List<PackageDetailDTO> packageDetailDTOAbnormalOrderTempList = ObjectUtil.cloneByStream(packageDetailDTOList);
        //指定批次先扣减 库存分配取计划数量
        packageDetailDTOList = packageDetailDTOList.stream().sorted(Comparator.comparing(PackageDetailDTO::getSkuLotNo).reversed()).collect(Collectors.toList());

        //WMS-汇单
        //1. 同一库位的库存需要一次性占用完，不能按散件占一部分
        //2. 同一商品同一批次多库位有货，根据出库数量计算库位占用优先级

        //CW业务逻辑 二次分组 内部批次+外部批次编码+库位分组 (忽略失效日期和生产批次号)
//        List<PackageDetailDTO> copyPackDetailList = ObjectUtil.cloneByStream(packageDetailDTOList);
        Map<String, List<PackageDetailDTO>> cwPackDetailMap = packageDetailDTOList.stream()
                .collect(Collectors.groupingBy(it -> StrUtil.join("##",
                        it.getSkuCode(), it.getSkuLotNo(), it.getExternalSkuLotNo(), it.getLocationCode())));

        for (Map.Entry<String, List<PackageDetailDTO>> entityMap : cwPackDetailMap.entrySet()) {
            PackageDetailDTO packageDetailDTO = buildCWPackDetail(entityMap.getValue());
            //获取当前的商品批次库存 过滤禁售
            List<SkuLotAndStockDTO> skuLotAndStockDTOS = getPackDetailStock(collectWaveTaskDTO.getWaveCode(), packageDetailDTO, skuLotAndStockDTOTempList, isIgnorePeriod, packageDTO.getBusinessType(), false);
            //获取当前商品库存最大数据
            BigDecimal actualQty = skuLotAndStockDTOS.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (actualQty.compareTo(packageDetailDTO.getExpQty()) >= 0) {
                //需要实现单库位的全占(当前数量全占)
                BigDecimal allocationQty = packageDetailDTO.getSkuQty();
                //托盘和箱码的计算
                List<SkuLotAndStockDTO> boxAndPallectStockDTOList = skuLotAndStockDTOS.stream()
                        .filter(a -> !StringUtils.isEmpty(a.getPalletCode()) || !StringUtils.isEmpty(a.getBoxCode())).collect(Collectors.toList());

                //无托盘和箱码的计算
                List<SkuLotAndStockDTO> noBoxAndPallectStockDTOList = skuLotAndStockDTOS.stream()
                        .filter(a -> StringUtils.isEmpty(a.getPalletCode()) && StringUtils.isEmpty(a.getBoxCode())).collect(Collectors.toList());
                //可以满足的箱码和托盘数据
                List<SkuLotAndStockDTO> needBoxAndPallectSkuLotAndStockDTOList = cwAllOccupyStockLocationManager.cwAllOccupyStockLocationNew(allocationQty, boxAndPallectStockDTOList);

                log.info("needBoxAndPallectSkuLotAndStockDTOList:{} {}", packageDTO.getPackageCode(), JSONUtil.toJsonStr(needBoxAndPallectSkuLotAndStockDTOList));

                BigDecimal needBoxAndPallectQty = needBoxAndPallectSkuLotAndStockDTOList.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal noBoxAndPallectQty = noBoxAndPallectStockDTOList.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);

                //库存不足
                if (CollectionUtils.isEmpty(noBoxAndPallectStockDTOList) && CollectionUtils.isEmpty(needBoxAndPallectSkuLotAndStockDTOList)
                        || needBoxAndPallectQty.add(noBoxAndPallectQty).compareTo(allocationQty) < 0) {
                    String errorCWMsg = "CW业务,库存不足【" + entityMap.getKey() + "】";
                    buildAbnormalOrder(packageDTO, collectWaveTaskDTO, errorCWMsg, isStoragePick);
                    return false;
                }

                List<PackageDetailDTO> detailDTOList = entityMap.getValue();
                //CW占用库存
                //------------------------分配库存记录-----满足托盘和箱码的库存-------------------------------
                for (PackageDetailDTO entity : detailDTOList) {
                    BigDecimal planQty = entity.getSkuQty();
                    if (CollectionUtils.isEmpty(needBoxAndPallectSkuLotAndStockDTOList)) {
                        break;
                    }
                    for (SkuLotAndStockDTO stockDTO : needBoxAndPallectSkuLotAndStockDTOList) {
                        if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                        if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = planQty;
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                            //组装分配单
                            AllocationOrderDTO allocationOrderDTO = buildAllocationOrderDTO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), collectWaveTaskDTO.getWaveCode(), entity, stockDTO, finalPlanQty, collectWaveTaskDTO.getCreatedBy(), allocationIsPreEnum);
                            allocationOrderDTOList.add(allocationOrderDTO);
                            //组装扣减三级库存
                            AllocationStockBO context = buildStockDistributeBO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), entity, stockDTO, finalPlanQty);
                            stockCollectBOList.add(context);
                            planQty = BigDecimal.ZERO;
                            break;
                        } else {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            planQty = planQty.subtract(finalPlanQty);
                            stockDTO.setAvailableQty(BigDecimal.ZERO);
                            AllocationOrderDTO allocationOrderDTO = buildAllocationOrderDTO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), collectWaveTaskDTO.getWaveCode(), entity, stockDTO, finalPlanQty, collectWaveTaskDTO.getCreatedBy(), allocationIsPreEnum);
                            allocationOrderDTOList.add(allocationOrderDTO);
                            //组装扣减三级库存
                            AllocationStockBO context = buildStockDistributeBO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), entity, stockDTO, finalPlanQty);
                            stockCollectBOList.add(context);
                        }
                    }
                }
                //---------------------------分配库存记录---------满足托盘和箱码的库存------------------------
                //------------------------分配库存记录-----满足无无无无无无托盘和箱码的库存-------------------------------
                for (PackageDetailDTO entity : detailDTOList) {
                    BigDecimal planQty = entity.getSkuQty().subtract(entity.getAssignQty());
                    if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    if (CollectionUtils.isEmpty(noBoxAndPallectStockDTOList)) {
                        break;
                    }
                    for (SkuLotAndStockDTO stockDTO : noBoxAndPallectStockDTOList) {
                        if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                        if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = planQty;
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                            //组装分配单
                            AllocationOrderDTO allocationOrderDTO = buildAllocationOrderDTO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), collectWaveTaskDTO.getWaveCode(), entity, stockDTO, finalPlanQty, collectWaveTaskDTO.getCreatedBy(), allocationIsPreEnum);
                            allocationOrderDTOList.add(allocationOrderDTO);
                            //组装扣减三级库存
                            AllocationStockBO context = buildStockDistributeBO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), entity, stockDTO, finalPlanQty);
                            stockCollectBOList.add(context);
                            planQty = BigDecimal.ZERO;
                            break;
                        } else {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            planQty = planQty.subtract(finalPlanQty);
                            stockDTO.setAvailableQty(BigDecimal.ZERO);
                            AllocationOrderDTO allocationOrderDTO = buildAllocationOrderDTO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), collectWaveTaskDTO.getWaveCode(), entity, stockDTO, finalPlanQty, collectWaveTaskDTO.getCreatedBy(), allocationIsPreEnum);
                            allocationOrderDTOList.add(allocationOrderDTO);
                            //组装扣减三级库存
                            AllocationStockBO context = buildStockDistributeBO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), entity, stockDTO, finalPlanQty);
                            stockCollectBOList.add(context);
                        }
                    }
                }
                //---------------------------分配库存记录---------满足无无无无无无托盘和箱码的库存------------------------
            } else {
                log.info("cw_stock_Insufficient_error1:package_code:{} sku_code:{}", packageDetailDTO.getPackageCode(), packageDetailDTO.getSkuCode());
                //生成异常单
                buildAbnormalOrderByStock(collectWaveTaskDTO, skuLotAndStockDTOAbnormalOrderTempList, packageDetailDTOAbnormalOrderTempList, packageDTO, false, isStoragePick, isIgnorePeriod);
                return false;
            }
        }
        //移除分配记录为0的分配记录
        allocationOrderDTOList.removeIf(a -> a.getExpQty().compareTo(BigDecimal.ZERO) == 0);
        //包裹明细数量和分配记录保护
        protectPackageDetailAndAllocationQty(allocationOrderDTOList, packageDetailDTOList);
        if (!isOccupyStock) {
            //移除为0的记录和保护削减数据和扣减原始库存数据
            protectAndRemoveZreoData(originStockLocationDTOList, skuLotAndStockDTOTempList);
        }
        //修改包裹状态码
        packageDTO.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
        packageDetailDTOList.forEach(a -> {
            a.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
        });
        //添加包裹明细
        packageDTO.setListDetail(packageDetailDTOList);
        //添加分配记录
        commitAllocationList.addAll(allocationOrderDTOList);
        //添加库存扣减
        commitStockList.addAll(stockCollectBOList);
        //添加拣选单明细
        if (CollectionUtils.isEmpty(commitPick.getDetailList())) {
            List<PickDetailDTO> detailList = new ArrayList<>();
            detailList.add(buildPickDetail(packageDTO, commitPick, 1, collectWaveTaskDTO.getCreatedBy()));
            commitPick.setDetailList(detailList);
            commitPick.setPackageQty(1L);
            commitPick.setQty(packageDTO.getPackageSkuQty());
        } else {
            List<PickDetailDTO> detailList = commitPick.getDetailList();
            if (detailList.stream().noneMatch(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode()))) {
                detailList.add(buildPickDetail(packageDTO, commitPick, detailList.size() + 1, collectWaveTaskDTO.getCreatedBy()));
                commitPick.setDetailList(detailList);
                commitPick.setPackageQty(commitPick.getPackageQty() + 1L);
                commitPick.setQty(commitPick.getQty().add(packageDTO.getPackageSkuQty()));
            }
        }
        collectWaveTaskDTO.setNum(collectWaveTaskDTO.getNum() + 1L);
        return true;
    }

    /**
     * create by: WuXian
     * description:  秒杀单组装查询参数
     * create time: 2021/6/24 13:06
     *
     * @param collectWaveTaskDTO
     * @param packageStruct
     * @return com.dt.domain.bill.param.CollectWaveBillParam
     */
    private CollectWaveBillParam buildBillSpikeParam(CollectWaveTaskDTO collectWaveTaskDTO, String packageStruct) {
        //店铺目前未加入强制分组,在生成汇单任务未加入店铺
        CollectWaveBillParam collectWaveBillParam = new CollectWaveBillParam();
        collectWaveBillParam.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        collectWaveBillParam.setCargoCode(collectWaveTaskDTO.getCargoCode());
        collectWaveBillParam.setCarrierCode(collectWaveTaskDTO.getCarrierCode());
        collectWaveBillParam.setSalePlatform(collectWaveTaskDTO.getSalePlatform());
        collectWaveBillParam.setExpressBranchNotBlank(collectWaveTaskDTO.getExpressBranch());
        collectWaveBillParam.setWaveCode(collectWaveTaskDTO.getWaveCode());
        collectWaveBillParam.setPackageStruct(packageStruct);
        collectWaveBillParam.setCollectStatus(CollectStatusEnum.CREATE_STATUS.getCode());
        collectWaveBillParam.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
        return collectWaveBillParam;
    }

//    /**
//     * create by: WuXian
//     * description:  回写出库单数据
//     * create time: 2021/6/24 13:07
//     *
//     * @param packageDTO
//     * @param shipmentOrderDTOList
//     * @return void
//     */
//    private void allocationShipmentOrder(PackageDTO packageDTO, List<ShipmentOrderDTO> shipmentOrderDTOList, List<AllocationStockBO> commitStockList) {
//        ShipmentOrderDTO shipmentOrderDTO;
//        if (CollectionUtils.isEmpty(shipmentOrderDTOList) || shipmentOrderDTOList.stream().noneMatch(a -> a.getShipmentOrderCode().equals(packageDTO.getShipmentOrderCode()))) {
//            shipmentOrderDTO = remoteShipmentOrderClient.getShipmentOrderByCode(packageDTO.getShipmentOrderCode());
//            if (Objects.equals(shipmentOrderDTO.getStatus(), ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode())) {
//                throw new BaseException(BaseBizEnum.TIP, "创建的订单不允许汇单");
//            }
//            if (Objects.equals(shipmentOrderDTO.getStatus(), ShipmentOrderEnum.STATUS.PREPARE_HANDLER_STATUS.getCode()) || Objects.equals(shipmentOrderDTO.getStatus(), ShipmentOrderEnum.STATUS.PREPARE_HANDLER_FAIL_STATUS.getCode())) {
//                shipmentOrderDTO.setStatus(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode());
//                shipmentOrderDTO.setCollectTime(System.currentTimeMillis());
//            }
//        } else {
//            shipmentOrderDTO = shipmentOrderDTOList.stream().filter(a -> a.getShipmentOrderCode().equals(packageDTO.getShipmentOrderCode())).findFirst().orElse(null);
//        }
//        commitStockList.forEach(it -> {
//            if (it.getShipmentOrderCode().equals(shipmentOrderDTO.getShipmentOrderCode())) {
//                it.setGlobalNo(shipmentOrderDTO.getGlobalNo());
//            }
//        });
//        List<PackageDetailDTO> packageDetailDTOList = packageDTO.getListDetail();
//        //回写出库单数据
//        List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList;
//        if (CollectionUtils.isEmpty(shipmentOrderDTO.getListShipmentOrderDetailDTO())) {
//            shipmentOrderDetailDTOList = remoteShipmentOrderClient.queryShipmentOrderDetailList(packageDTO.getShipmentOrderCode());
//        } else {
//            shipmentOrderDetailDTOList = shipmentOrderDTO.getListShipmentOrderDetailDTO();
//        }
//        if (CollectionUtils.isEmpty(shipmentOrderDetailDTOList)) {
//            throw new BaseException(BaseBizEnum.TIP, String.format("出库单明细异常%s,", packageDTO.getShipmentOrderCode()));
//        }
//        // TODO 预包处理 使用原始商品回写
//        packageDetailDTOList = packageDetailDTOList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
//
//        for (PackageDetailDTO entity : packageDetailDTOList) {
//            shipmentOrderDetailDTOList.forEach(a -> {
//                if (Objects.equals(a.getId(), entity.getPUid())) {
//                    a.setAssignQty(a.getAssignQty().add(entity.getAssignQty()));
//                    //TODO add 2021-04-12 分配数量不能大于计划数量
//                    if (a.getAssignQty().compareTo(a.getExpSkuQty()) > 0) {
//                        throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s商品:%s分配数量不能大于计划数量", a.getShipmentOrderCode(), a.getSkuCode()));
//                    }
//                    if (Objects.equals(shipmentOrderDTO.getStatus(), ShipmentOrderEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())) {
//                        a.setStatus(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode());
//                    }
//                }
//            });
//        }
//        shipmentOrderDTO.setListShipmentOrderDetailDTO(shipmentOrderDetailDTOList);
//        Iterator<ShipmentOrderDTO> is = shipmentOrderDTOList.iterator();
//        while (is.hasNext()) {
//            ShipmentOrderDTO orderDTO = is.next();
//            if (shipmentOrderDTO.getShipmentOrderCode().equals(orderDTO.getShipmentOrderCode())) {
//                is.remove();
//            }
//        }
//
//        shipmentOrderDTOList.add(shipmentOrderDTO);
//    }

    /**
     * create by: WuXian
     * description: TODO 正常B单和C单汇单扣减库存
     * create time: 2021/6/24 12:15
     *
     * @param collectWaveTaskDTO
     * @param commitPick
     * @param commitAllocationList
     * @param commitStockList
     * @param packageDTO
     * @param packageDetailDTOList
     * @param originStockLocationDTOList
     * @param allocationIsPreEnum
     * @param isStoragePick
     * @return java.lang.Boolean
     */
    private Boolean reduceOriginStockByPackDetail(CollectWaveTaskDTO collectWaveTaskDTO, PickDTO commitPick, List<AllocationOrderDTO> commitAllocationList, List<AllocationStockBO> commitStockList, PackageDTO packageDTO, List<PackageDetailDTO> packageDetailDTOList, List<SkuLotAndStockDTO> originStockLocationDTOList, AllocationIsPreEnum allocationIsPreEnum, Boolean isStoragePick) {
        List<AllocationOrderDTO> allocationOrderDTOList = new ArrayList<>();
        List<AllocationStockBO> stockCollectBOList = new ArrayList<>();
        //是否校验禁售 true 是  false 非 是销毁出库 不校验禁售
        Boolean isIgnorePeriod = false;
        //供应链金融
        Boolean isOccupyFinance = false;
        //订单标记忽略效期
        if (OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).stream().anyMatch(a -> a.getCode().equals(OrderTagEnum.IGNORE_VALIDITY_PERIOD.getCode()))) {
            isIgnorePeriod = true;
        }
        //是否销毁出库 true 是  false 非 是销毁出库 不校验禁售
        if (packageDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            Boolean isDestroy = getShipmentIsDestroy(packageDTO.getShipmentOrderCode());
            if (isDestroy) {
                isIgnorePeriod = true;
            }
            isOccupyFinance = getShipmentOrderType(Arrays.asList(packageDTO.getShipmentOrderCode()));
        }
        //处理上游占用三级库存的包裹 获取已经分配的库存
        Boolean isOccupyStock = false;
        if (OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).contains(OrderTagEnum.OCCUPY_LOCATION_STOCK)) {
            isOccupyStock = true;
        }
        //获取当前包裹所有的商品
        List<String> skuCodeList = packageDetailDTOList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        List<SkuLotAndStockDTO> skuLotAndStockDTOList;
        //TODO 上游占用三级库存的包裹
        if (isOccupyStock) {
            ShipmentOrderDTO shipmentOrderDTO = remoteShipmentOrderClient.getShipmentOrderByCode(packageDTO.getShipmentOrderCode());
            skuLotAndStockDTOList = skuStockAndLotBiz.getOccupyStockLocationFromTransaction(shipmentOrderDTO.getGlobalNo(), packageDTO.getCargoCode(), packageDTO.getSkuQuality(), shipmentOrderDTO.getOrderType());
        } else {
            skuLotAndStockDTOList = originStockLocationDTOList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDTO.getCargoCode())).filter(a -> skuCodeList.contains(a.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDTO.getSkuQuality())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(skuLotAndStockDTOList)) {
            //无库存
            //生成异常单
            List<String> skuCodeTList = new ArrayList<>();
            skuCodeTList.addAll(skuCodeList);
            String skuCodeMsg = String.join(";", skuCodeTList.stream().sorted().collect(Collectors.toList()));
            String message = String.format("商品【%s】拣选区分配库存不足", skuCodeMsg);
            if (!StringUtils.isEmpty(message) && message.length() > 2048) {
                message = message.substring(0, 2048);
            }
            buildAbnormalOrder(packageDTO, collectWaveTaskDTO, message, isStoragePick);
            return false;
        }
        //库存数据深复制 用于分配
        List<SkuLotAndStockDTO> skuLotAndStockDTOTempList = ObjectUtil.cloneByStream(skuLotAndStockDTOList);
        if (Objects.equals(packageDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            if (isOccupyFinance) {
                skuLotAndStockDTOTempList = skuLotAndStockDTOTempList.stream().filter(a -> a.getLocationType().equalsIgnoreCase(LocationTypeEnum.LOCATION_TYPE_FINANCE.getType())).collect(Collectors.toList());
            } else {
                skuLotAndStockDTOTempList = skuLotAndStockDTOTempList.stream().filter(a -> !a.getLocationType().equalsIgnoreCase(LocationTypeEnum.LOCATION_TYPE_FINANCE.getType())).collect(Collectors.toList());
            }
        }
        //库存数据深复制 用于异常单计算
        List<SkuLotAndStockDTO> skuLotAndStockDTOAbnormalOrderTempList = ObjectUtil.cloneByStream(skuLotAndStockDTOList);
        if (Objects.equals(packageDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            if (isOccupyFinance) {
                skuLotAndStockDTOAbnormalOrderTempList = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> a.getLocationType().equalsIgnoreCase(LocationTypeEnum.LOCATION_TYPE_FINANCE.getType())).collect(Collectors.toList());
            } else {
                skuLotAndStockDTOAbnormalOrderTempList = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> !a.getLocationType().equalsIgnoreCase(LocationTypeEnum.LOCATION_TYPE_FINANCE.getType())).collect(Collectors.toList());
            }
        }
        List<PackageDetailDTO> packageDetailDTOAbnormalOrderTempList = ObjectUtil.cloneByStream(packageDetailDTOList);
        //指定批次先扣减 库存分配取计划数量
        packageDetailDTOList = packageDetailDTOList.stream().sorted(Comparator.comparing(PackageDetailDTO::getSkuLotNo).reversed()).collect(Collectors.toList());
        for (PackageDetailDTO entity : packageDetailDTOList) {
            //获取当前的商品批次库存 过滤禁售
            List<SkuLotAndStockDTO> skuLotAndStockDTOS;
            if (isOccupyStock) {
                skuLotAndStockDTOS = getPackDetailOccupyLocationStock(collectWaveTaskDTO.getWaveCode(), entity, skuLotAndStockDTOTempList);
            } else {
                //淘天销退B单 汇单支持残次等级
                Boolean isTaoTianXt = false;
                if (Objects.equals(packageDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString()) && OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).contains(OrderTagEnum.TAOTIAN_XT)) {
                    isTaoTianXt = true;
                }
                //属性过滤
                skuLotAndStockDTOS = getPackDetailStock(collectWaveTaskDTO.getWaveCode(), entity, skuLotAndStockDTOTempList, isIgnorePeriod, packageDTO.getBusinessType(), isTaoTianXt);
            }
            //获取当前商品库存最大数据
            BigDecimal actualQty = skuLotAndStockDTOS.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (actualQty.compareTo(entity.getExpQty()) >= 0) {
                BigDecimal planQty = entity.getSkuQty();
                for (SkuLotAndStockDTO stockDTO : skuLotAndStockDTOS) {
                    if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = planQty;
                        entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                        //组装分配单
                        AllocationOrderDTO allocationOrderDTO = buildAllocationOrderDTO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), collectWaveTaskDTO.getWaveCode(), entity, stockDTO, finalPlanQty, collectWaveTaskDTO.getCreatedBy(), allocationIsPreEnum);
                        allocationOrderDTOList.add(allocationOrderDTO);
                        //组装扣减三级库存
                        if (!isOccupyStock) {
                            AllocationStockBO context = buildStockDistributeBO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), entity, stockDTO, finalPlanQty);
                            stockCollectBOList.add(context);
                        }
                        planQty = BigDecimal.ZERO;
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                        planQty = planQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);
                        AllocationOrderDTO allocationOrderDTO = buildAllocationOrderDTO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), collectWaveTaskDTO.getWaveCode(), entity, stockDTO, finalPlanQty, collectWaveTaskDTO.getCreatedBy(), allocationIsPreEnum);
                        allocationOrderDTOList.add(allocationOrderDTO);
                        //组装扣减三级库存
                        if (!isOccupyStock) {
                            AllocationStockBO context = buildStockDistributeBO(packageDTO.getShipmentOrderCode(), commitPick.getPickCode(), entity, stockDTO, finalPlanQty);
                            stockCollectBOList.add(context);
                        }
                    }
                }
            } else {
                log.info("stock_Insufficient_error1:package_code:{} sku_code:{}", entity.getPackageCode(), entity.getSkuCode());
                //生成异常单
                buildAbnormalOrderByStock(collectWaveTaskDTO, skuLotAndStockDTOAbnormalOrderTempList, packageDetailDTOAbnormalOrderTempList, packageDTO, false, isStoragePick, isIgnorePeriod);
                return false;
            }
        }
        //移除分配记录为0的分配记录
        allocationOrderDTOList.removeIf(a -> a.getExpQty().compareTo(BigDecimal.ZERO) == 0);
        //包裹明细数量和分配记录保护
        protectPackageDetailAndAllocationQty(allocationOrderDTOList, packageDetailDTOList);
        if (!isOccupyStock) {
            //移除为0的记录和保护削减数据和扣减原始库存数据
            protectAndRemoveZreoData(originStockLocationDTOList, skuLotAndStockDTOTempList);
        }
        //修改包裹状态码
        packageDTO.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
        packageDetailDTOList.forEach(a -> {
            a.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
        });
        //添加包裹明细
        packageDTO.setListDetail(packageDetailDTOList);
        //添加分配记录
        commitAllocationList.addAll(allocationOrderDTOList);
        //添加库存扣减
        commitStockList.addAll(stockCollectBOList);
        //添加拣选单明细
        if (CollectionUtils.isEmpty(commitPick.getDetailList())) {
            List<PickDetailDTO> detailList = new ArrayList<>();
            detailList.add(buildPickDetail(packageDTO, commitPick, 1, collectWaveTaskDTO.getCreatedBy()));
            commitPick.setDetailList(detailList);
            commitPick.setPackageQty(1L);
            commitPick.setQty(packageDTO.getPackageSkuQty());
        } else {
            List<PickDetailDTO> detailList = commitPick.getDetailList();
            if (detailList.stream().noneMatch(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode()))) {
                detailList.add(buildPickDetail(packageDTO, commitPick, detailList.size() + 1, collectWaveTaskDTO.getCreatedBy()));
                commitPick.setDetailList(detailList);
                commitPick.setPackageQty(commitPick.getPackageQty() + 1L);
                commitPick.setQty(commitPick.getQty().add(packageDTO.getPackageSkuQty()));
            }
        }
        collectWaveTaskDTO.setNum(collectWaveTaskDTO.getNum() + 1L);
        return true;
    }

    /**
     * 重新组装明细
     *
     * @param packageDetailDTOS
     * @return
     */
    private PackageDetailDTO buildCWPackDetail(List<PackageDetailDTO> packageDetailDTOS) {
        PackageDetailDTO packageDetailDTO = ObjectUtil.cloneByStream(packageDetailDTOS.get(0));
        packageDetailDTO.setSkuQty(packageDetailDTOS.stream().map(PackageDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        packageDetailDTO.setExpQty(packageDetailDTOS.stream().map(PackageDetailDTO::getExpQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        return packageDetailDTO;
    }

    /**
     * @param waveCode
     * @param packageDetailDTO
     * @param stockLocationDTOS
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     * <AUTHOR>
     * @describe: 上游占用三级库存
     * @date 2022/12/8 15:25
     */
    private List<SkuLotAndStockDTO> getPackDetailOccupyLocationStock(String waveCode, PackageDetailDTO packageDetailDTO, List<SkuLotAndStockDTO> stockLocationDTOS) {
        //指定属性过滤
        List<SkuLotAndStockDTO> skuLotAndStockDTOTargetList = stockLocationDTOS.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getSkuLotNo())) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getSkuLotNo(), packageDetailDTO.getSkuLotNo())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getExternalSkuLotNo())) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getExternalSkuLotNo(), packageDetailDTO.getExternalSkuLotNo())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDate()) && packageDetailDTO.getExpireDate() > 0L) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getExpireDate(), packageDetailDTO.getExpireDate())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getLocationCode())) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getLocationCode(), packageDetailDTO.getLocationCode())).collect(Collectors.toList());
        }
        //指定批次或只有一个库位一个批次有商品 直接返回
        if (skuLotAndStockDTOTargetList.size() == 1) {
            return skuLotAndStockDTOTargetList;
        }
        List<String> sortFields = new ArrayList<>();
//        B单专用拣选位【3】》存储区【2】》拣选区【1】
        sortFields.add("specialSort_false");
        //添加周转规则
        List<String> sortTurnoverRuleFields = skuStockAndLotBiz.addTurnoverRuleSort(CurrentRouteHolder.getWarehouseCode(), waveCode, packageDetailDTO.getTurnoverRuleCode());
        sortFields.addAll(sortTurnoverRuleFields);
        //查询分配规则--出库单明细上有分配规则--选择最合适的批次
        List<String> sortAllocationRuleFields = skuStockAndLotBiz.addAllocationRuleSort(CurrentRouteHolder.getWarehouseCode(), waveCode, packageDetailDTO.getAllocationRuleCode());
        sortFields.addAll(sortAllocationRuleFields);
        //多字段联合排序
        ListSortUtils.sortList(skuLotAndStockDTOTargetList, sortFields);
        return skuLotAndStockDTOTargetList;
    }

    private Boolean getShipmentIsDestroy(String shipmentOrderCode) {
        ShipmentOrderDTO shipmentOrderDTO = remoteShipmentOrderClient.getShipmentOrderByCode(shipmentOrderCode);
        if (shipmentOrderDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "出库单不存在");
        }
        if (shipmentOrderDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString()) && shipmentOrderDTO.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.DESTROY_OUT_STOCK_TYPE.getCode())) {
            return true;
        }
        return false;
    }

    /**
     * create by: WuXian
     * description:  异常单逻辑
     * create time: 2021/6/24 13:09
     *
     * @param skuLotAndStockDTOAbnormalOrderTempList
     * @param packageDetailDTOAbnormalOrderTempList
     * @param packageDTO
     * @param isPre
     */
    private void buildAbnormalOrderByStock(CollectWaveTaskDTO collectWaveTaskDTO, List<SkuLotAndStockDTO> skuLotAndStockDTOAbnormalOrderTempList, List<PackageDetailDTO> packageDetailDTOAbnormalOrderTempList, PackageDTO packageDTO, Boolean isPre, Boolean isStoragePick, Boolean isIgnorePeriod) {
        Boolean taotian = false;
        if (OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).contains(OrderTagEnum.TAOTAIN)) {
            taotian = true;
        }

        String errorMsg = "";
        if (isPre) {
            errorMsg = "预包区库存不足【禁售-预包】";
        } else {
            packageDetailDTOAbnormalOrderTempList = packageDetailDTOAbnormalOrderTempList.stream().sorted(Comparator.comparing(PackageDetailDTO::getSkuLotNo).reversed()).collect(Collectors.toList());
            for (PackageDetailDTO entity : packageDetailDTOAbnormalOrderTempList) {
                //获取当前的商品批次库存 过滤禁售库存
                List<SkuLotAndStockDTO> skuLotAndStockDTOS = getPackDetailStock(collectWaveTaskDTO.getWaveCode(), entity, skuLotAndStockDTOAbnormalOrderTempList, isIgnorePeriod, packageDTO.getBusinessType(), false);
                //获取当前商品库存最大数据 当前库存扣减，包裹明细数量大于0还有证明库存不足
                BigDecimal actualQty = skuLotAndStockDTOS.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (actualQty.compareTo(entity.getExpQty()) >= 0) {
                    BigDecimal planQty = entity.getSkuQty();
                    entity.setSkuQty(BigDecimal.ZERO);
                    for (SkuLotAndStockDTO stockDTO : skuLotAndStockDTOS) {
                        if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = planQty;
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                            break;
                        } else {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            planQty = planQty.subtract(finalPlanQty);
                            stockDTO.setAvailableQty(BigDecimal.ZERO);
                        }
                    }
                } else {
                    //当前库存不满足分配
                    BigDecimal planQty = actualQty;
                    entity.setSkuQty(entity.getSkuQty().subtract(actualQty));
                    for (SkuLotAndStockDTO stockDTO : skuLotAndStockDTOS) {
                        if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = planQty;
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                            break;
                        } else {
                            //用于移除内存数据
                            BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                            entity.setAssignQty(entity.getAssignQty().add(finalPlanQty));
                            planQty = planQty.subtract(finalPlanQty);
                            stockDTO.setAvailableQty(BigDecimal.ZERO);
                        }
                    }
                }
            }
            //统计当前库存的区
            List<String> locationTypeList = skuLotAndStockDTOAbnormalOrderTempList.stream().map(SkuLotAndStockDTO::getLocationType).distinct().collect(Collectors.toList());

            if (locationTypeList.size() == 1 && locationTypeList.contains(LocationTypeEnum.LOCATION_TYPE_PICK.getType())) {
                errorMsg = "【拣选区】";
            }
            if (locationTypeList.size() == 1 && locationTypeList.contains(LocationTypeEnum.LOCATION_TYPE_STORE.getType())) {
                errorMsg = "【存储区】";
            }
            if (locationTypeList.size() == 2) {
                errorMsg = "【拣选区和存储区】";
            }
            if (!StringUtils.isEmpty(collectWaveTaskDTO.getZoneCode())) {
                errorMsg = errorMsg + String.format("【指定库区%s】", collectWaveTaskDTO.getZoneCode());
            }
            //统计当前分配异常数据
            List<PackageDetailDTO> packageDetailDTOList = packageDetailDTOAbnormalOrderTempList.stream().filter(a -> a.getSkuQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            //商品异常提示语
            List<String> msgDescList = new ArrayList<>();
            for (PackageDetailDTO packageDetailDTO : packageDetailDTOList) {
                //指定属性
                Boolean isAppointAttr = false;
                List<String> appointAttrMsgList = new ArrayList<>();
                if (!StringUtils.isEmpty(packageDetailDTO.getExternalSkuLotNo())
                        || packageDetailDTO.getExpireDate() > 0L
                        || packageDetailDTO.getExpireDateStart() > 0L
                        || packageDetailDTO.getExpireDateEnd() > 0L) {
                    if (packageDetailDTO.getExpireDate() > 0L) {
                        appointAttrMsgList.add("失效日期:" + ConverterUtil.convertVoTime(packageDetailDTO.getExpireDate(), "yyyy-MM-dd"));
                    }
                    if (packageDetailDTO.getExpireDateStart() > 0L && packageDetailDTO.getExpireDateEnd() > 0L) {
                        appointAttrMsgList.add("失效日期起:" + ConverterUtil.convertVoTime(packageDetailDTO.getExpireDateStart(), "yyyy-MM-dd")
                                + "失效日期止:" + ConverterUtil.convertVoTime(packageDetailDTO.getExpireDateEnd(), "yyyy-MM-dd"));
                    }
                    if (!StringUtils.isEmpty(packageDetailDTO.getExternalSkuLotNo())) {
                        appointAttrMsgList.add("外部批次编码:" + packageDetailDTO.getExternalSkuLotNo());
                    }
                    isAppointAttr = true;
                }
                //TODO ==============================库位,批次,外部批次编码,失效日期,效期范围都为空 常用===========================
                if (StringUtils.isEmpty(packageDetailDTO.getSkuLotNo()) && !isAppointAttr && StringUtils.isEmpty(packageDetailDTO.getLocationCode())) {
                    //--
                    BigDecimal actualAllQty;
                    if (taotian) {
                        actualAllQty = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).filter(a -> Objects.equals(a.getInventoryType(), packageDetailDTO.getInventoryType())).map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    } else {
                        actualAllQty = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    if (actualAllQty.compareTo(packageDetailDTO.getSkuQty()) >= 0) {
                        msgDescList.add(String.format("商品:%s库存不足【禁售】", packageDetailDTO.getSkuCode(), packageDetailDTO.getSkuLotNo()));
                    } else {
                        msgDescList.add(String.format("商品:%s库存不足", packageDetailDTO.getSkuCode(), packageDetailDTO.getSkuLotNo()));
                    }
                }
                //TODO ==============================库位,批次,外部批次编码,失效日期,效期范围都为空 常用============================

                //TODO  批次,外部批次编码,失效日期,效期范围目前只考虑单个 +库位

                //TODO ==============================批次不空 库位空 属性空=========================================================
                if (!StringUtils.isEmpty(packageDetailDTO.getSkuLotNo()) && StringUtils.isEmpty(packageDetailDTO.getLocationCode()) && !isAppointAttr) {
                    //A:库位空 + 属性空
                    if (StringUtils.isEmpty(packageDetailDTO.getLocationCode())) {
                        //总库存 包含禁售库存 skuLotAndStockDTOAbnormalOrderTempList 未过滤禁售日期
                        BigDecimal actualAllQty;
                        if (taotian) {
                            actualAllQty = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).filter(a -> Objects.equals(a.getSkuLotNo(), packageDetailDTO.getSkuLotNo())).map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        } else {
                            actualAllQty = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).filter(a -> Objects.equals(a.getInventoryType(), packageDetailDTO.getInventoryType())).filter(a -> Objects.equals(a.getSkuLotNo(), packageDetailDTO.getSkuLotNo())).map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        if (actualAllQty.compareTo(packageDetailDTO.getSkuQty()) >= 0) {
                            msgDescList.add(String.format("商品:%s指定批次%s库存不足【禁售】", packageDetailDTO.getSkuCode(), packageDetailDTO.getSkuLotNo()));
                        } else {
                            msgDescList.add(String.format("商品:%s指定批次%s库存不足", packageDetailDTO.getSkuCode(), packageDetailDTO.getSkuLotNo()));
                        }
                    }
                }

                //TODO ==============================批次不空 库位不空 属性空=========================================================
                if (!StringUtils.isEmpty(packageDetailDTO.getSkuLotNo()) && !StringUtils.isEmpty(packageDetailDTO.getLocationCode()) && !isAppointAttr) {
                    //总库存 包含禁售库存 skuLotAndStockDTOAbnormalOrderTempList 未过滤禁售日期
                    BigDecimal actualAllQty;
                    if (taotian) {
                        actualAllQty = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).filter(a -> Objects.equals(a.getInventoryType(), packageDetailDTO.getInventoryType())).filter(a -> Objects.equals(a.getLocationCode(), packageDetailDTO.getLocationCode())).map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    } else {
                        actualAllQty = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).filter(a -> Objects.equals(a.getLocationCode(), packageDetailDTO.getLocationCode())).map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    if (actualAllQty.compareTo(packageDetailDTO.getSkuQty()) >= 0) {
                        msgDescList.add(String.format("商品:%s库位%s库存不足【禁售】", packageDetailDTO.getSkuCode(), packageDetailDTO.getLocationCode()));
                    } else {
                        msgDescList.add(String.format("商品:%s库位%s库存不足", packageDetailDTO.getSkuCode(), packageDetailDTO.getLocationCode()));
                    }
                }


                //TODO ==============================批次空 库位空 属性不空  指定属性常见=========================================================
                if (StringUtils.isEmpty(packageDetailDTO.getSkuLotNo()) && StringUtils.isEmpty(packageDetailDTO.getLocationCode()) && isAppointAttr) {
                    //总库存 包含禁售库存 skuLotAndStockDTOAbnormalOrderTempList 未过滤禁售日期
                    List<SkuLotAndStockDTO> skuLotAndStockDTOList;
                    if (taotian) {
                        skuLotAndStockDTOList = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getInventoryType(), packageDetailDTO.getInventoryType())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).collect(Collectors.toList());
                    } else {
                        skuLotAndStockDTOList = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).collect(Collectors.toList());
                    }
                    //外部批次编码
                    if (!CollectionUtils.isEmpty(skuLotAndStockDTOList) && !StringUtils.isEmpty(packageDetailDTO.getExternalSkuLotNo())) {
                        skuLotAndStockDTOList = skuLotAndStockDTOList.stream().filter(a -> Objects.equals(a.getExternalSkuLotNo(), packageDetailDTO.getExternalSkuLotNo())).collect(Collectors.toList());
                    }
                    //失效日期
                    if (!CollectionUtils.isEmpty(skuLotAndStockDTOList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDate()) && packageDetailDTO.getExpireDate() > 0L) {
                        skuLotAndStockDTOList = skuLotAndStockDTOList.stream().filter(a -> Objects.equals(a.getExpireDate(), packageDetailDTO.getExpireDate())).collect(Collectors.toList());
                    }
                    //失效时间开始-结束
                    if (!CollectionUtils.isEmpty(skuLotAndStockDTOList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDateStart()) && packageDetailDTO.getExpireDateStart() > 0L) {
                        skuLotAndStockDTOList = skuLotAndStockDTOList.stream().filter(a -> a.getExpireDate() >= packageDetailDTO.getExpireDateStart()).collect(Collectors.toList());
                    }
                    if (!CollectionUtils.isEmpty(skuLotAndStockDTOList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDateEnd()) && packageDetailDTO.getExpireDateEnd() > 0L) {
                        skuLotAndStockDTOList = skuLotAndStockDTOList.stream().filter(a -> a.getExpireDate() <= packageDetailDTO.getExpireDateEnd()).collect(Collectors.toList());
                    }
                    //失效时间开始-结束

                    BigDecimal actualAllQty = skuLotAndStockDTOList.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (actualAllQty.compareTo(packageDetailDTO.getSkuQty()) >= 0) {
                        msgDescList.add(String.format("商品:%s【指定属性:【%s】】库存不足【禁售】", packageDetailDTO.getSkuCode(), appointAttrMsgList.stream().collect(Collectors.joining(","))));
                    } else {
                        msgDescList.add(String.format("商品:%s【指定属性:【%s】】库存不足", packageDetailDTO.getSkuCode(), appointAttrMsgList.stream().collect(Collectors.joining(","))));
                    }
                }

                //TODO ==============================批次空 库位不空 属性不空=========================================================
                if (StringUtils.isEmpty(packageDetailDTO.getSkuLotNo()) && !StringUtils.isEmpty(packageDetailDTO.getLocationCode()) && isAppointAttr) {
                    //总库存 包含禁售库存 skuLotAndStockDTOAbnormalOrderTempList 未过滤禁售日期
                    List<SkuLotAndStockDTO> skuLotAndStockDTOList;
                    if (taotian) {
                        skuLotAndStockDTOList = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).filter(a -> Objects.equals(a.getInventoryType(), packageDetailDTO.getInventoryType())).filter(a -> Objects.equals(a.getLocationCode(), packageDetailDTO.getLocationCode())).collect(Collectors.toList());
                    } else {
                        skuLotAndStockDTOList = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).filter(a -> Objects.equals(a.getLocationCode(), packageDetailDTO.getLocationCode())).collect(Collectors.toList());
                    }
                    //外部批次编码
                    if (!CollectionUtils.isEmpty(skuLotAndStockDTOList) && !StringUtils.isEmpty(packageDetailDTO.getExternalSkuLotNo())) {
                        skuLotAndStockDTOList = skuLotAndStockDTOList.stream().filter(a -> Objects.equals(a.getExternalSkuLotNo(), packageDetailDTO.getExternalSkuLotNo())).collect(Collectors.toList());
                    }
                    //失效日期
                    if (!CollectionUtils.isEmpty(skuLotAndStockDTOList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDate()) && packageDetailDTO.getExpireDate() > 0L) {
                        skuLotAndStockDTOList = skuLotAndStockDTOList.stream().filter(a -> Objects.equals(a.getExpireDate(), packageDetailDTO.getExpireDate())).collect(Collectors.toList());
                    }
                    //失效时间开始-结束
                    if (!CollectionUtils.isEmpty(skuLotAndStockDTOList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDateStart()) && packageDetailDTO.getExpireDateStart() > 0L) {
                        skuLotAndStockDTOList = skuLotAndStockDTOList.stream().filter(a -> a.getExpireDate() >= packageDetailDTO.getExpireDateStart()).collect(Collectors.toList());
                    }
                    if (!CollectionUtils.isEmpty(skuLotAndStockDTOList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDateEnd()) && packageDetailDTO.getExpireDateEnd() > 0L) {
                        skuLotAndStockDTOList = skuLotAndStockDTOList.stream().filter(a -> a.getExpireDate() <= packageDetailDTO.getExpireDateEnd()).collect(Collectors.toList());
                    }
                    //失效时间开始-结束
                    BigDecimal actualAllQty = skuLotAndStockDTOList.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (actualAllQty.compareTo(packageDetailDTO.getSkuQty()) >= 0) {
                        msgDescList.add(String.format("商品:%s库位%s【指定属性:【%s】】库存不足【禁售】", packageDetailDTO.getSkuCode(), packageDetailDTO.getLocationCode(), appointAttrMsgList.stream().collect(Collectors.joining(","))));
                    } else {
                        msgDescList.add(String.format("商品:%s库位%s【指定属性:【%s】】库存不足", packageDetailDTO.getSkuCode(), packageDetailDTO.getLocationCode(), appointAttrMsgList.stream().collect(Collectors.joining(","))));
                    }
                }
                //TODO ==============================批次空 库位不空 属性空=========================================================
                if (StringUtils.isEmpty(packageDetailDTO.getSkuLotNo()) && !StringUtils.isEmpty(packageDetailDTO.getLocationCode()) && !isAppointAttr) {
                    //总库存 包含禁售库存 skuLotAndStockDTOAbnormalOrderTempList 未过滤禁售日期
                    List<SkuLotAndStockDTO> skuLotAndStockDTOList;
                    if (taotian) {
                        skuLotAndStockDTOList = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).filter(a -> Objects.equals(a.getInventoryType(), packageDetailDTO.getInventoryType())).filter(a -> Objects.equals(a.getLocationCode(), packageDetailDTO.getLocationCode())).collect(Collectors.toList());
                    } else {
                        skuLotAndStockDTOList = skuLotAndStockDTOAbnormalOrderTempList.stream().filter(a -> Objects.equals(a.getCargoCode(), packageDetailDTO.getCargoCode())).filter(a -> Objects.equals(a.getSkuCode(), packageDetailDTO.getSkuCode())).filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).filter(a -> Objects.equals(a.getLocationCode(), packageDetailDTO.getLocationCode())).collect(Collectors.toList());
                    }
                    BigDecimal actualAllQty = skuLotAndStockDTOList.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (actualAllQty.compareTo(packageDetailDTO.getSkuQty()) >= 0) {
                        msgDescList.add(String.format("商品:%s库位%s库存不足【禁售】", packageDetailDTO.getSkuCode(), packageDetailDTO.getLocationCode()));
                    } else {
                        msgDescList.add(String.format("商品:%s库位%s库存不足", packageDetailDTO.getSkuCode(), packageDetailDTO.getLocationCode()));
                    }
                }
                //-----------------------
            }
            if (CollectionUtils.isEmpty(msgDescList)) {
                msgDescList.add("库存不足");
            }
            errorMsg = errorMsg + msgDescList.stream().distinct().collect(Collectors.joining(","));
            if (remoteWarehouseClient.getTaoTianWarehouse(packageDTO.getWarehouseCode()) && Objects.equals(packageDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
                errorMsg = errorMsg.replaceAll("【禁售】", "");
            }
        }
        if (!StringUtils.isEmpty(errorMsg) && errorMsg.length() > 2048) {
            log.info("buildAbnormalOrderByStock to large:{} {}", packageDTO.getPackageCode(), errorMsg);
            errorMsg = errorMsg.substring(0, 2048);
        }
        buildAbnormalOrder(packageDTO, collectWaveTaskDTO, errorMsg, isStoragePick);
    }

    /**
     * create by: WuXian
     * description:  包裹明细数量和分配记录保护
     * create time: 2021/6/24 13:09
     *
     * @param allocationOrderDTOS
     * @param packageDetailDTOS
     * @return void
     */
    private void protectPackageDetailAndAllocationQty(List<AllocationOrderDTO> allocationOrderDTOS, List<PackageDetailDTO> packageDetailDTOS) {
        List<String> skuCodeList = packageDetailDTOS.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        String packageCode = packageDetailDTOS.get(0).getPackageCode();
        for (String skuCode : skuCodeList) {
            //分配数量
            BigDecimal allocationQty = allocationOrderDTOS.stream().filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode)).map(AllocationOrderDTO::getExpQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            //包裹明细分配数量
            BigDecimal packageDetailQty = packageDetailDTOS.stream().filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode)).map(PackageDetailDTO::getAssignQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (allocationQty.compareTo(packageDetailQty) != 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("包裹号:%s商品编码:%s,数量分配异常", packageCode, skuCode));
            }
        }
    }


    /**
     * create by: WuXian
     * description:  组建异常单
     * create time: 2021/6/24 13:09
     *
     * @param packageDTO
     * @param collectWaveTaskDTO
     * @param message
     * @return void
     */
    private void buildAbnormalOrder(PackageDTO packageDTO, CollectWaveTaskDTO collectWaveTaskDTO, String message, Boolean isStoragePick) {
        AbnormalOrderDTO abnormalOrder = new AbnormalOrderDTO();
        //订单的业务类型
        abnormalOrder.setOrderBusinessType(packageDTO.getBusinessType());
        abnormalOrder.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        abnormalOrder.setCargoCode(collectWaveTaskDTO.getCargoCode());
        abnormalOrder.setBillNo(packageDTO.getPackageCode());
        abnormalOrder.setBillType(AbnormalOrderEnum.typeEnum.PACKAGE.getValue());
        abnormalOrder.setBusinessType(AbnormalOrderEnum.bussinessTypeEnum.THREE_LEVEL_STOCK_TYPE.getCode());
        abnormalOrder.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
        abnormalOrder.setPoNo(packageDTO.getPoNo());
        abnormalOrder.setSoNo(packageDTO.getSoNo());
        abnormalOrder.setCarrierCode(packageDTO.getCarrierCode());
        abnormalOrder.setRetryCount(0);
        abnormalOrder.setErrorStatus("分配区库存不足");
        abnormalOrder.setCreatedBy(packageDTO.getUpdatedBy());
        abnormalOrder.setCreatedTime(System.currentTimeMillis());
        abnormalOrder.setErrorMsg(message);
        CollectWaveAbnormalBillBO collectWaveAbnormalBillBO = new CollectWaveAbnormalBillBO();
        //添加异常单
        packageDTO.setStatus(PackEnum.STATUS.ASSGIN_STOCK_STATUS.getCode());
        collectWaveAbnormalBillBO.setAbnormalOrderDTO(abnormalOrder);
        collectWaveAbnormalBillBO.setPackageDTO(packageDTO);
        waveBillGtsService.submitCollectAbnormalOrder(collectWaveAbnormalBillBO);
    }


    /**
     * create by: WuXian
     * description:  移除为0的记录和保护削减数据和扣减原始库存数据
     * create time: 2021/6/24 13:10
     *
     * @param stockLocationDTOS
     * @param skuLotAndStockDTOTempList
     * @return void
     */
    private void protectAndRemoveZreoData(List<SkuLotAndStockDTO> stockLocationDTOS, List<SkuLotAndStockDTO> skuLotAndStockDTOTempList) {
        //修改原始库存数据
        stockLocationDTOS.forEach(a -> {
            SkuLotAndStockDTO skuLotAndStockDTO = skuLotAndStockDTOTempList.stream().filter(aa -> Objects.equals(a.getId(), aa.getId())).findFirst().orElse(null);
            if (skuLotAndStockDTO != null) {
                a.setAvailableQty(skuLotAndStockDTO.getAvailableQty());
            }
        });
        //回写内存库存数据批次 移除可用数量为0的数据
//        stockLocationDTOS.removeIf(a->a.getAvailableQty().compareTo(BigDecimal.ZERO) == 0);
        Iterator<SkuLotAndStockDTO> iterator = stockLocationDTOS.iterator();
        while (iterator.hasNext()) {
            SkuLotAndStockDTO skuLotAndStockDTO = iterator.next();
            if (skuLotAndStockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                iterator.remove();
            }
            if (skuLotAndStockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) < 0) {
                throw new BaseException(BaseBizEnum.TIP, "内存库存数据为负数,请核查");
            }
        }
    }

    /**
     * create by: WuXian
     * description:  组装分配单
     * create time: 2021/6/24 13:10
     *
     * @param shipmentOrderCode
     * @param pickCode
     * @param waveCode
     * @param entity
     * @param stockEntity
     * @param qty
     * @param by
     * @param allocationIsPreEnum
     * @return com.dt.domain.bill.dto.AllocationOrderDTO
     */
    private AllocationOrderDTO buildAllocationOrderDTO(String shipmentOrderCode, String pickCode, String waveCode, PackageDetailDTO entity, SkuLotAndStockDTO stockEntity, BigDecimal qty, String by, AllocationIsPreEnum allocationIsPreEnum) {
        AllocationOrderDTO allocationOrderDTO = new AllocationOrderDTO();
        allocationOrderDTO.setWarehouseCode(entity.getWarehouseCode());
        allocationOrderDTO.setCargoCode(entity.getCargoCode());
        allocationOrderDTO.setAllocationOrderCode(remoteSeqRuleClient.findSequence(SeqEnum.ALLOCATION_CODE_000001));
        allocationOrderDTO.setExpQty(qty);
        allocationOrderDTO.setPackUid(entity.getPackUid());
        allocationOrderDTO.setRealQty(qty);
        allocationOrderDTO.setPickQty(qty);
        allocationOrderDTO.setSplitQty(qty);
        allocationOrderDTO.setShipmentOrderCode(shipmentOrderCode);
        allocationOrderDTO.setPickCode(pickCode);
        allocationOrderDTO.setPickSeq(stockEntity.getPickSeq());
        allocationOrderDTO.setLocationCode(stockEntity.getLocationCode());
        allocationOrderDTO.setPackageCode(entity.getPackageCode());
        allocationOrderDTO.setPUid(entity.getId());
        allocationOrderDTO.setSkuCode(entity.getSkuCode());
        allocationOrderDTO.setSkuLotNo(stockEntity.getSkuLotNo());
        allocationOrderDTO.setStatus(AllocationOrderEnum.statusEnum.SUCCESS_STATUS.getValue());
        allocationOrderDTO.setWaveCode(waveCode);
        allocationOrderDTO.setZoneCode(stockEntity.getZoneCode());
        allocationOrderDTO.setTunnelCode(stockEntity.getTunnelCode());
        allocationOrderDTO.setCreatedBy(by);
        allocationOrderDTO.setIsPre(allocationIsPreEnum.getCode());
        allocationOrderDTO.setCreatedTime(System.currentTimeMillis());
        //用于预包后续拆分
        if (!CollectionUtils.isEmpty(stockEntity.getSkuNeedMap())) {
            allocationOrderDTO.setSkuNeedMap(stockEntity.getSkuNeedMap());
        }
        return allocationOrderDTO;
    }

    /**
     * 组装库存扣减数据
     *
     * @param pickCode
     * @param entity
     * @param stockEntity
     * @return
     */
    private AllocationStockBO buildStockDistributeBO(String shipmentOrderCode, String pickCode, PackageDetailDTO entity, SkuLotAndStockDTO stockEntity, BigDecimal qty) {
        AllocationStockBO available = new AllocationStockBO();
        if (entity.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode())) {
            available.setRealGoods(RealOrUnrealGoodsEnum.UNREAL.getCode());
        } else {
            available.setRealGoods(RealOrUnrealGoodsEnum.REAL.getCode());
        }
        available.setWarehouseCode(entity.getWarehouseCode());
        available.setCargoCode(entity.getCargoCode());
        available.setSkuCode(entity.getSkuCode());
        available.setSkuQuality(entity.getSkuQuality());
        available.setPackageCode(entity.getPackageCode());
        available.setShipmentOrderCode(shipmentOrderCode);
        available.setPickCode(pickCode);
        available.setTradeDate(System.currentTimeMillis());
        available.setSkuQty(qty);
        available.setRemark(entity.getId() + "");
        available.setLocationType(stockEntity.getLocationType());
        available.setLocationCode(stockEntity.getLocationCode());
        available.setSkuLotNo(stockEntity.getSkuLotNo());
        available.setZoneCode(stockEntity.getZoneCode());
        available.setZoneType(stockEntity.getZoneType());
        return available;
    }

    /**
     * create by: WuXian
     * description:  获取明细的库存
     * create time: 2021/6/24 13:12
     *
     * @param waveCode
     * @param packageDetailDTO
     * @param stockLocationDTOS
     * @param isIgnorePeriod
     * @param businessType
     * @return
     */
    private List<SkuLotAndStockDTO> getPackDetailStock(String waveCode, PackageDetailDTO packageDetailDTO, List<SkuLotAndStockDTO> stockLocationDTOS, Boolean isIgnorePeriod, String businessType, Boolean isTaoTianXt) {
        //指定属性过滤
        List<SkuLotAndStockDTO> skuLotAndStockDTOTargetList = stockLocationDTOS.stream()
                .filter(a -> a.getCargoCode().equalsIgnoreCase(packageDetailDTO.getCargoCode()))
                .filter(a -> a.getSkuCode().equalsIgnoreCase(packageDetailDTO.getSkuCode()))
                .filter(a -> Objects.equals(a.getSkuQuality(), packageDetailDTO.getSkuQuality())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getSkuLotNo())) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getSkuLotNo(), packageDetailDTO.getSkuLotNo())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getExternalSkuLotNo())) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getExternalSkuLotNo(), packageDetailDTO.getExternalSkuLotNo())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDate()) && packageDetailDTO.getExpireDate() > 0L) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getExpireDate(), packageDetailDTO.getExpireDate())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getLocationCode())) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getLocationCode(), packageDetailDTO.getLocationCode())).collect(Collectors.toList());
        }
        //失效时间开始-结束
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDateStart()) && packageDetailDTO.getExpireDateStart() > 0L) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> a.getExpireDate() >= packageDetailDTO.getExpireDateStart()).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getExpireDateEnd()) && packageDetailDTO.getExpireDateEnd() > 0L) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> a.getExpireDate() <= packageDetailDTO.getExpireDateEnd()).collect(Collectors.toList());
        }
        //失效时间开始-结束

        //TODO 目前只考虑淘天 B2B考虑这个残次等级--非退货仓
        //TODO 淘天全面支持残次等级汇单 add 2025-03-19
        if (remoteWarehouseClient.getTaoTianWarehouse(packageDetailDTO.getWarehouseCode())) {
            if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getInventoryType())) {
                skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getInventoryType(), packageDetailDTO.getInventoryType())).collect(Collectors.toList());
            }
        }
        //TODO 目前只考虑淘天 B2B考虑这个残次等级 -- 退货仓
        if (isTaoTianXt) {
            if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getInventoryType())) {
                skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getInventoryType(), packageDetailDTO.getInventoryType())).collect(Collectors.toList());
            }
        }

        //是否校验禁售
        if (!isIgnorePeriod) {
//            if (packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode())) {
//                //禁售日期批次拦截 【预包】
//                skuLotAndStockDTOTargetList = buildPreWithdrawDate(skuLotAndStockDTOTargetList);
//            } else {
            //禁售日期批次拦截
            //比对禁售时间 非预包子商品 禁售比对时间大于0
//                if (!packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE_CHILD.getCode()) && !CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getWithdrawCompareDate()) && packageDetailDTO.getWithdrawCompareDate() > 0L) {
            if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(packageDetailDTO.getWithdrawCompareDate()) && packageDetailDTO.getWithdrawCompareDate() > 0L) {
                skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> filterStockByWithdrawCompareDate(a, packageDetailDTO)).collect(Collectors.toList());
            } else {
                skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(this::filterStockByWithdrawDate).collect(Collectors.toList());
            }

//            }
        }
//        if (packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE_CHILD.getCode())) {
//            //周转规则排序 分配规则排序【预包】
//            skuLotAndStockDTOTargetList = skuStockAndLotBiz.sortListPreStockLotAndLocation(skuLotAndStockDTOTargetList, PackIsPreEnum.getEnum(packageDetailDTO.getIsPre()));
//        } else {
        //周转规则排序 分配规则排序
        skuLotAndStockDTOTargetList = skuStockAndLotBiz.sortListStockLotAndLocation(waveCode, packageDetailDTO.getWarehouseCode(), skuLotAndStockDTOTargetList, packageDetailDTO.getTurnoverRuleCode(), packageDetailDTO.getAllocationRuleCode());
//        }
        return skuLotAndStockDTOTargetList;
    }

    /**
     * @param a
     * @param packageDetailDTO
     * @return boolean
     * <AUTHOR>
     * @describe: 禁售比对时间
     * @date 2024/7/31 15:43
     */
    private boolean filterStockByWithdrawCompareDate(SkuLotAndStockDTO a, PackageDetailDTO packageDetailDTO) {
        //非效期不考虑禁售比对时间
        if (!a.getIsLifeCycle()) {
            return true;
        }
        //非过期 比对禁售日期大于禁售日期
        if (a.getWithdrawDate() > packageDetailDTO.getWithdrawCompareDate() && !a.getOverExpire()) {
            return true;
        }
        return false;
    }

    /**
     * @param skuLotAndStockDTOS
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     * @author: WuXian
     * description:  预包是预包计划整批禁售
     * create time: 2021/9/9 14:37
     */
    private List<SkuLotAndStockDTO> buildPreWithdrawDate(List<SkuLotAndStockDTO> skuLotAndStockDTOS) {
        //获取预包条码禁售信息 --找出预包条码对应子商品的禁售
        List<SkuLotAndStockDTO> skuLotAndStockDTOList = skuLotAndStockDTOS.stream().filter(a -> {
            if (a.getIsWithdraw()) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        return skuLotAndStockDTOList;
    }

    /**
     * create by: WuXian
     * description:  过滤禁售
     * create time: 2021/6/24 13:12
     *
     * @param a
     * @return boolean
     */
    private boolean filterStockByWithdrawDate(SkuLotAndStockDTO a) {
        //非效期商品不过滤
        if (!a.getIsLifeCycle()) {
            return true;
        }
        //效期商品，非禁售不过滤
        if (a.getIsLifeCycle() && !a.getIsWithdraw()) {
            return true;
        }
        return false;
    }

    /**
     * create by: WuXian
     * description:  获取所有商品批次的库存
     * create time: 2021/6/24 13:13
     *
     * @param collectWaveTaskDTO
     * @param isStoragePick
     * @param isOccupyFinance
     * @param skuCodeList
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     */
    private List<SkuLotAndStockDTO> findStockLotAndLocation(CollectWaveTaskDTO collectWaveTaskDTO, Boolean isStoragePick, Boolean isOccupyFinance, List<String> skuCodeList) {
        //指定库区
        List<String> zoneCodeList = new ArrayList<>();
        if (!StringUtils.isEmpty(collectWaveTaskDTO.getZoneCode())) {
            zoneCodeList = Arrays.asList(collectWaveTaskDTO.getZoneCode().split(","));
        }

        //查询商品
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(collectWaveTaskDTO.getCargoCode());
        skuParam.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return new ArrayList<>();
        }
        //不指定批次查询库存
        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setZoneCodeList(zoneCodeList);
        stockLocationParam.setSkuCodeList(skuCodeList);
        stockLocationParam.setCargoCode(collectWaveTaskDTO.getCargoCode());
        //预包订单查询预包+拣选区库存
        List<String> locationTypeList = new ArrayList<>();
        //默认汇单只查询拣选区库存
        locationTypeList.add(LocationTypeEnum.LOCATION_TYPE_PICK.getType());
        //TODO 2025-04-09 预包改前占

        //B2B包裹占用拣选位和存储位库存
        if (collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString()) && isStoragePick) {
            locationTypeList.add(LocationTypeEnum.LOCATION_TYPE_STORE.getType());
        }
        //供应链金融位
        if (isOccupyFinance) {
            locationTypeList.add(LocationTypeEnum.LOCATION_TYPE_FINANCE.getType());
        }
        stockLocationParam.setLocationTypeList(locationTypeList);
        if (Objects.equals(collectWaveTaskDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            stockLocationParam.setHasPhysicalQty(true);
        } else {
            stockLocationParam.setHasAvailableQty(true);
        }
        List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getListByPage(stockLocationParam);
        if (CollectionUtils.isEmpty(stockLocationDTOList)) {
            return new ArrayList<>();
        }
        //获取批次数据
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(stockLocationDTOList.stream().map(StockLocationDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getListByPage(skuLotParam);
        if (CollectionUtils.isEmpty(skuLotDTOList) || CollectionUtils.isEmpty(stockLocationDTOList)) {
            return new ArrayList<>();
        }
        List<SkuLotAndStockDTO> skuLotAndStockDTOList = new ArrayList<>();
        //组装当前货品批次和库位库存数据
        List<SkuLotAndStockDTO> PickSkuLotAndStockDTOList = skuStockAndLotBiz.buildSkuLotAndStock(skuLotDTOList, stockLocationDTOList, skuDTOList);
        if (CollectionUtils.isEmpty(PickSkuLotAndStockDTOList)) {
            return skuLotAndStockDTOList;
        }
        if (collectWaveTaskDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())) {
            //B2C过滤拣选位标记【B单专用】
            skuLotAndStockDTOList = ObjectUtil.cloneByStream(PickSkuLotAndStockDTOList.stream().filter(a -> !a.getIsB2BPick()).collect(Collectors.toList()));
        } else {
            skuLotAndStockDTOList = ObjectUtil.cloneByStream(PickSkuLotAndStockDTOList);
        }
        if (isOccupyFinance) {
            //TODO 供应链金融使用取巧 将冻结数设置到可用数，后续逻辑统一
            skuLotAndStockDTOList.forEach(a -> {
                if (LocationTypeEnum.LOCATION_TYPE_FINANCE.getType().equalsIgnoreCase(a.getLocationType())) {
                    a.setAvailableQty(a.getFrozenQty());
                }
            });
        }
        return skuLotAndStockDTOList;
    }


    /**
     * create by: WuXian
     * description:  获取包裹明细
     * create time: 2021/6/24 13:13
     *
     * @param warehouseCode
     * @param cargoCode
     * @param packageCodeList
     * @return java.util.List<com.dt.domain.bill.dto.PackageDetailDTO>
     */
    private List<PackageDetailDTO> getPackDetailList(String warehouseCode, String cargoCode, List<String> packageCodeList) {
        PackageDetailParam param = new PackageDetailParam();
        param.setWarehouseCode(warehouseCode);
        param.setCargoCode(cargoCode);
        param.setPackageCodeList(packageCodeList);
        return remotePackageDetailClient.getListByPage(param);
    }

    /**
     * create by: WuXian
     * description:  秒杀获取包裹
     * create time: 2021/6/24 13:13
     *
     * @param collectWaveDTO
     * @param collectWaveDTO
     * @return java.util.List<com.dt.domain.bill.dto.PackageDTO>
     */
    private List<PackageDTO> getSpikePackList(String waveCode, CollectWaveDTO collectWaveDTO, List<String> satisfyPackCodeList) {
        PackageParam packageParam = new PackageParam();
        packageParam.setWarehouseCode(collectWaveDTO.getWarehouseCode());
        packageParam.setCargoCode(collectWaveDTO.getCargoCode());
        packageParam.setCarrierCode(collectWaveDTO.getCarrierCode());
        packageParam.setWaveCode(waveCode);
        packageParam.setSkuQuality(collectWaveDTO.getSkuQuality());
        packageParam.setSalePlatformT(collectWaveDTO.getSalePlatform());
        packageParam.setPackageStruct(collectWaveDTO.getPackageStruct());
        //TODO 商品结构,商品结构+数量不带批次
        packageParam.setAnalysisSku(collectWaveDTO.getAnalysisSku());
        //TODO 商品结构+批次+数量（兼容以前的数据）
        packageParam.setSpikeAnalysisSkuNotBlank(collectWaveDTO.getSpikeAnalysisSku());
        packageParam.setExpressBranchNotBlank(collectWaveDTO.getExpressBranch());
        packageParam.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
        if (!CollectionUtils.isEmpty(satisfyPackCodeList)) {
            packageParam.setPackageCodeList(satisfyPackCodeList);
        }
        List<PackageDTO> packageDTOList = remotePackageClient.getListByPage(packageParam);
        return packageDTOList;
    }

    /**
     * create by: WuXian
     * description:  批量加锁
     * create time: 2021/6/24 13:13
     *
     * @param warehouseCode
     * @param shipmentCodeList
     * @return org.redisson.RedissonMultiLock
     */
    private RedissonMultiLock addLockPackage(String warehouseCode, List<String> shipmentCodeList) {
        List<RLock> lockList = shipmentCodeList.stream().sorted().flatMap(a -> {
            String shipmentOrderLockKey = StrUtil.join(StrUtil.COLON, warehouseCode, ShipmentOrderDTO.class.getSimpleName(), a);
            RLock rLock = redissonClient.getLock(shipmentOrderLockKey);
            return Stream.of(rLock);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lockList)) {
            throw new BaseException(BaseBizEnum.TIP, "未获取到汇单锁");
        }
        RLock[] locks = new RLock[lockList.size()];
        return new RedissonMultiLock(lockList.toArray(locks));
    }


    /**
     * create by: WuXian
     * description:  组装拣选单
     * create time: 2021/6/24 13:14
     *
     * @param collectWaveTaskDTO
     * @param pickRuleDetailDTO
     * @return com.dt.domain.bill.dto.PickDTO
     */
    private PickDTO buildPickDTO(CollectWaveTaskDTO collectWaveTaskDTO, PickRuleDetailDTO pickRuleDetailDTO) {
        PickDTO pickDTO = new PickDTO();
        pickDTO.setPickCode(remoteSeqRuleClient.findSequence(SeqEnum.PK_CODE_000001));
        pickDTO.setWaveCode(collectWaveTaskDTO.getWaveCode());
        pickDTO.setCargoCode(collectWaveTaskDTO.getCargoCode());
        pickDTO.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        pickDTO.setCarrierCode(collectWaveTaskDTO.getCarrierCode());
        pickDTO.setPrintStatus(PickEnum.PrintEnum.PICK_PRINT_0.getCode());
        pickDTO.setStatus(PickEnum.PickStatusEnum.CREATE_STATUS.getCode());
        pickDTO.setBusinessType(collectWaveTaskDTO.getBusinessType());

        pickDTO.setPickMethod(pickRuleDetailDTO.getPickWorkType());
        pickDTO.setSalePlatform(collectWaveTaskDTO.getSalePlatform());
        pickDTO.setCollectBy(collectWaveTaskDTO.getCreatedBy());
        pickDTO.setPrintGroup(pickRuleDetailDTO.getPrintGroup());
        //分析拣选单明细
        pickDTO.setType(pickRuleDetailDTO.getPickOrderType());
        pickDTO.setAllBoxType(pickRuleDetailDTO.getAllBoxType());
        pickDTO.setAllBoxStatus(PickEnum.PrintEnum.PICK_PRINT_0.getCode());
        pickDTO.setWorkType(pickRuleDetailDTO.getWorkType());
        pickDTO.setGoodsListStatus(PickEnum.PrintEnum.PICK_PRINT_0.getCode());
        pickDTO.setGoodsListType(pickRuleDetailDTO.getGoodsListType());
        pickDTO.setExpressBillStatus(PickEnum.PrintEnum.PICK_PRINT_0.getCode());
        pickDTO.setExpressBillType(pickRuleDetailDTO.getExpressBillType());
        pickDTO.setPackMarkStatus(PickEnum.PrintEnum.PICK_PRINT_0.getCode());
        pickDTO.setPackMarkType(pickRuleDetailDTO.getPackMarkType());
        pickDTO.setCreatedTime(System.currentTimeMillis());
        pickDTO.setCreatedBy(collectWaveTaskDTO.getCreatedBy());
        if (collectWaveTaskDTO.getOrderTag() != null && collectWaveTaskDTO.getOrderTag() > 0 && CollectWaveOrderTagEnum.NumToEnum(collectWaveTaskDTO.getOrderTag()).contains(CollectWaveOrderTagEnum.ORDER_MERG)) {
            pickDTO.setOrderTag(PickOrderTagEnum.enumToNum(PickOrderTagEnum.ORDER_MERG));
        } else {
            pickDTO.setOrderTag(0);
        }
        return pickDTO;
    }

    /**
     * create by: WuXian
     * description:  组装拣选单明细
     * create time: 2021/6/24 13:14
     *
     * @param sto
     * @param pickDTO
     * @param num
     * @param _by
     * @return com.dt.domain.bill.dto.PickDetailDTO
     */
    private PickDetailDTO buildPickDetail(PackageDTO sto, PickDTO pickDTO, int num, String _by) {
        PickDetailDTO pickDetailDTO = new PickDetailDTO();
        pickDetailDTO.setWarehouseCode(sto.getWarehouseCode());
        pickDetailDTO.setCargoCode(sto.getCargoCode());
        pickDetailDTO.setPickCode(pickDTO.getPickCode());
        pickDetailDTO.setPackageCode(sto.getPackageCode());
        pickDetailDTO.setPackageStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
        pickDetailDTO.setShipmentOrderCode(sto.getShipmentOrderCode());
        pickDetailDTO.setPoNo(sto.getPoNo());
        pickDetailDTO.setSoNo(sto.getSoNo());
        pickDetailDTO.setExpressNo(sto.getExpressNo());
        pickDetailDTO.setBasketNo(String.valueOf(num));
        pickDetailDTO.setQty(sto.getPackageSkuQty());
        pickDetailDTO.setCreatedTime(System.currentTimeMillis());
        pickDetailDTO.setCreatedBy(_by);
        return pickDetailDTO;
    }

    /**
     * @param collectWaveTaskDTO
     * @return void
     * <AUTHOR>
     * @describe: 汇单任务前置分析
     * @date 2022/12/2 11:18
     */
    public List<String> analysisAllocationStockSyncPickTask(CollectWaveTaskDTO collectWaveTaskDTO) {
        //满足的集合
        List<String> satisfyPackCodeList = new ArrayList<>();

        RpcContextUtil.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        //获取当前任务下所有包裹
        PackageParam packageParam = new PackageParam();
        packageParam.setWarehouseCode(collectWaveTaskDTO.getWarehouseCode());
        packageParam.setCargoCode(collectWaveTaskDTO.getCargoCode());
        packageParam.setCarrierCode(collectWaveTaskDTO.getCarrierCode());
        packageParam.setWaveCode(collectWaveTaskDTO.getWaveCode());
        packageParam.setSalePlatformT(collectWaveTaskDTO.getSalePlatform());
        packageParam.setSaleShopId(collectWaveTaskDTO.getSaleShopId());
        packageParam.setExpressBranchNotBlank(collectWaveTaskDTO.getExpressBranch());
        packageParam.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
        packageParam.setCollectStatus(CollectStatusEnum.CREATE_STATUS.getCode());
        //结构分组
        List<CollectWaveAnalysisDTO> analysisPackList = remotePackageClient.getWaveCollectFrontAnalysisGroupBy(packageParam);
        if (CollectionUtils.isEmpty(analysisPackList)) {
            return Lists.newArrayList();
        }
        //获取所有包裹
        packageParam.setAnalysisSkuList(analysisPackList.stream().map(CollectWaveAnalysisDTO::getAnalysisSku).collect(Collectors.toList()));
        List<PackageDTO> packageDTOList = remotePackageClient.getListByPage(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            return Lists.newArrayList();
        }
        //包裹编码
        List<String> normalAnalysisPackageCodeList;
        if (Objects.equals(collectWaveTaskDTO.getIsPre(), CollectWaveTaskIsPreEnum.NORMAL.getCode())) {
            normalAnalysisPackageCodeList = analysisPackList.stream().map(CollectWaveAnalysisDTO::getPackageCode).distinct().collect(Collectors.toList());
        } else {
            normalAnalysisPackageCodeList = analysisPackList.stream().filter(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.NORMAL.getCode())).map(CollectWaveAnalysisDTO::getPackageCode).distinct().collect(Collectors.toList());
            //预包包裹不用分析
            List<String> preAnalysisPackageCodeList = packageDTOList.stream().filter(a -> Objects.equals(a.getIsPre(), PackEnum.TYPE.PRE.getCode())).map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList());
            //-------------TODO-------预包包裹分析------------------TODO-----------------
            if (!CollectionUtils.isEmpty(preAnalysisPackageCodeList)) {
                StockTransactionParam stockTransactionParam = new StockTransactionParam();
                stockTransactionParam.setBillNoList(preAnalysisPackageCodeList);
                stockTransactionParam.setStockLevel(StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel());
                stockTransactionParam.setOperationType(OperationTypeEnum.OPERATION_SHIPMENT.getType());
                stockTransactionParam.setStatus(StockTransactionStatusEnum.DONE.getCode());
                if (!StringUtils.isEmpty(collectWaveTaskDTO.getZoneCode())) {
                    stockTransactionParam.setZoneCodeList(Arrays.asList(collectWaveTaskDTO.getZoneCode().split(",")));
                }
                List<String> packCodeList = remoteStockTransactionClient.getPreSatisfyAnalysisPackage(stockTransactionParam);
                if (!CollectionUtils.isEmpty(packCodeList)) {
                    satisfyPackCodeList.addAll(packCodeList);
                }
            }
//            satisfyPackCodeList.addAll(preAnalysisPackageCodeList);
        }
        if (!CollectionUtils.isEmpty(normalAnalysisPackageCodeList)) {
            //用于处理指定属性数据
            List<PackageDetailDTO> packageDetailDTOList = new ArrayList<>();
            //查询结构包裹明细
            PackageDetailParam packageDetailParam = new PackageDetailParam();
            packageDetailParam.setPackageCodeList(normalAnalysisPackageCodeList);
            List<PackageDetailDTO> appointPackageDetailDTOList = remotePackageClient.getCollectWavePackageDetailListAppointColumn(packageDetailParam, LambdaHelpUtils.convertToFieldNameList(PackageDetailDTO::getPackageCode, PackageDetailDTO::getCargoCode, PackageDetailDTO::getSkuQty, PackageDetailDTO::getSkuCode, PackageDetailDTO::getSkuLotNo, PackageDetailDTO::getSkuQuality, PackageDetailDTO::getLocationCode, PackageDetailDTO::getExternalSkuLotNo, PackageDetailDTO::getExpireDate, PackageDetailDTO::getAllocationRuleCode, PackageDetailDTO::getTurnoverRuleCode, PackageDetailDTO::getInventoryType, PackageDetailDTO::getWithdrawCompareDate, PackageDetailDTO::getIsPre, PackageDetailDTO::getExpireDateStart, PackageDetailDTO::getExpireDateEnd));
            if (!CollectionUtils.isEmpty(appointPackageDetailDTOList)) {
                packageDetailDTOList.addAll(appointPackageDetailDTOList);
            }
            //解析商品明细
            List<String> skuCodeList = new ArrayList<>();
            //获取所有货主

            Map<String, Boolean> cargoTaoTianMap = remoteCargoOwnerClient.getCargoTaoTianMap(analysisPackList.stream().map(CollectWaveAnalysisDTO::getCargoCode).distinct().collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(cargoTaoTianMap)) {
                analysisPackList.forEach(it -> {
                    it.setCargoTaoTian(false);
                    if (cargoTaoTianMap.containsKey(it.getCargoCode())) {
                        it.setCargoTaoTian(cargoTaoTianMap.get(it.getCargoCode()));
                    }
                });
            }
            skuStockAndLotBiz.analysisPackData(analysisPackList, skuCodeList, packageDetailDTOList);
            //过滤无明细的数据--如全预包
            analysisPackList = analysisPackList.stream().filter(a -> !CollectionUtils.isEmpty(a.getCollectWaveAnalysisSkuDTOList())).collect(Collectors.toList());
            //获取库存
            List<SkuLotAndStockDTO> skuLotAndStockByZone;
            List<String> zoneCodeList = new ArrayList<>();
            if (!StringUtils.isEmpty(collectWaveTaskDTO.getZoneCode())) {
                zoneCodeList.addAll(Arrays.asList(collectWaveTaskDTO.getZoneCode().split(",")));
            }
            skuLotAndStockByZone = skuStockAndLotBiz.getSkuLotAndStockByZoneAnalysis(zoneCodeList, skuCodeList, Arrays.asList(collectWaveTaskDTO.getCargoCode()), Arrays.asList(ZoneTypeEnum.ZONE_TYPE_PICK.getType()));

            if (CollectionUtils.isEmpty(skuLotAndStockByZone)) {
                return Lists.newArrayList();
            }
            //B2C过滤拣选位标记【B单专用】
            skuLotAndStockByZone = skuLotAndStockByZone.stream().filter(a -> !a.getIsB2BPick()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skuLotAndStockByZone)) {
                return Lists.newArrayList();
            }
            //按结构商品 数量 降序
            analysisPackList = analysisPackList.stream().sorted(Comparator.comparing(CollectWaveAnalysisDTO::getCargoCode, Comparator.naturalOrder()).thenComparing(CollectWaveAnalysisDTO::getSalePlatform, Comparator.naturalOrder()).thenComparing(CollectWaveAnalysisDTO::getCarrierCode, Comparator.naturalOrder()).thenComparing(CollectWaveAnalysisDTO::getAnalysisSku, Comparator.naturalOrder()).thenComparing(CollectWaveAnalysisDTO::getNum, Comparator.reverseOrder())).collect(Collectors.toList());
            List<CollectWaveAnalysisDTO> finalAnalysisPackList = analysisPackList;

            List<SkuLotAndStockDTO> finalSkuLotAndStockByZone = skuLotAndStockByZone;
            //分析数据
            if (!CollectionUtils.isEmpty(finalSkuLotAndStockByZone) && !CollectionUtils.isEmpty(packageDTOList) && !CollectionUtils.isEmpty(finalAnalysisPackList)) {
                //开始分析
                List<String> packCodeList = skuStockAndLotBiz.waveCollectAnalysisData(finalSkuLotAndStockByZone, packageDTOList, finalAnalysisPackList, packageDetailDTOList);
                if (!CollectionUtils.isEmpty(packCodeList)) {
                    satisfyPackCodeList.addAll(packCodeList);
                }
            }
        }
        if (CollectionUtils.isEmpty(satisfyPackCodeList)) {
            return Lists.newArrayList();
        }
        return satisfyPackCodeList;
    }
}
