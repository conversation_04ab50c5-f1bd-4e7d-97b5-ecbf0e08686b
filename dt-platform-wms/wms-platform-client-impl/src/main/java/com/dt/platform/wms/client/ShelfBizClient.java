package com.dt.platform.wms.client;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.dto.BaseDTO;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.component.common.enums.asn.AsnOrderTagEnum;
import com.dt.component.common.enums.asn.AsnTypeEnum;
import com.dt.component.common.enums.asn.CustomsClearanceStatusEnum;
import com.dt.component.common.enums.base.*;
import com.dt.component.common.enums.pre.BillLogTypeEnum;
import com.dt.component.common.enums.pre.PrePackagePlanStatusEnum;
import com.dt.component.common.enums.pre.SkuIsPreEnum;
import com.dt.component.common.enums.rec.ReceiptError;
import com.dt.component.common.enums.rec.ReceiptStatusEnum;
import com.dt.component.common.enums.ret.ReturnOrderEnum;
import com.dt.component.common.enums.rs.RSBillSourceEnum;
import com.dt.component.common.enums.shelf.ShelfMarkDetailEnum;
import com.dt.component.common.enums.shelf.ShelfMarkEnum;
import com.dt.component.common.enums.shelf.ShelfStatusEnum;
import com.dt.component.common.enums.shelf.ShelfTypeEnum;
import com.dt.component.common.enums.sku.SkuNewOrOldCtrlEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.wms.WmsShelfBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.exceptions.WmsBizException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.contLog.ContainerLogDTO;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.base.param.*;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.pre.PrePackagePlanDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.pre.PrePackagePlanParam;
import com.dt.domain.bill.param.rs.SalesReturnOrderParam;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.domain.core.stock.param.StockShelfParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.ILocationBiz;
import com.dt.platform.wms.biz.IMixRuleCheckBiz;
import com.dt.platform.wms.biz.IRecommendLocationBiz;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.constant.PretreatmentConstant;
import com.dt.platform.wms.dto.shelf.ShelfBizDTO;
import com.dt.platform.wms.dto.shelf.ShelfCompleteDetailBizDTO;
import com.dt.platform.wms.dto.shelf.ShelfDetailBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.mercury.IRemoteMercuryClient;
import com.dt.platform.wms.integration.pre.IRemotePrePackagePlanClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import com.dt.platform.wms.param.shelf.*;
import com.dt.platform.wms.transaction.IShelfGtsService;
import com.dt.platform.wms.transaction.bo.ShelfCompleteBO;
import com.dt.platform.wms.transaction.bo.ShelfDetailBO;
import io.seata.spring.annotation.GlobalLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class ShelfBizClient implements IShelfBizClient {

    @Resource
    private IRemoteShelfClient remoteShelfClient;

    @Resource
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Resource
    private IShelfGtsService shelfContextService;

    @Resource
    private IRemoteReceiptClient remoteReceiptClient;

    @Resource
    private IRemoteReceiptDetailClient remoteReceiptDetailClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IMixRuleCheckBiz mixRuleCheckBiz;

    @Resource
    private IRemoteLocationClient remoteLocationClient;

    @Resource
    private IRemoteTunnelClient remoteTunnelClient;

    @Resource
    private IRemoteZoneClient remoteZoneClient;

    @Resource
    private IRemoteContainerClient remoteContainerClient;

    @Resource
    private IRemoteReturnOrderClient remoteReturnOrderClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteAsnClient remoteAsnClient;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private IRemotePrePackagePlanClient remotePrePackagePlanClient;

    @Resource
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    private ILocationBiz locationBiz;

    @Resource
    private IRecommendLocationBiz recommendLocationBiz;

    @Resource
    private IRemoteMercuryClient remoteMercuryClient;

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Override
    public Result<Boolean> modifyShelfOpType(ShelfModifyOpTypeParam param) {

        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setCode(param.getCode());
        shelfParam.setStatus(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
        ShelfDTO shelf = remoteShelfClient.get(shelfParam);
        if (ObjectUtils.isEmpty(shelf)) {
            throw new BaseException(BaseBizEnum.TIP, "该上架单不允许修改上架方式!");
        }
        shelf.setOpType(param.getOpType());

        remoteShelfClient.modify(ConverterUtil.convert(shelf, ShelfParam.class));

        return Result.success(true);
    }

    @Override
    public Result<ShelfBizDTO> scanContainer(ScanConstCodeParam param) {
        ContainerDTO container = remoteContainerClient.queryByCode(param.getContCode());
        if (ObjectUtils.isEmpty(container)) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_SHELF_NULL);
        }
        List<String> statusList = new ArrayList<>();
        statusList.add(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
        statusList.add(ShelfStatusEnum.STATUS_DOING.getStatus());
        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setContCode(param.getContCode());
        shelfParam.setStatusList(statusList);
        ShelfDTO shelf = remoteShelfClient.get(shelfParam);
        if (ObjectUtils.isEmpty(shelf)) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_DATA_NULL_RE_SCAN);
        }
        if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelf.getStatus())) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_HAS_COMPLETE_RE_SCAN);
        }
        ShelfBizDTO shelfBiz = ConverterUtil.convert(shelf, ShelfBizDTO.class);
        // 查询上架单明细
        ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
        shelfDetailParam.setShelfCode(shelf.getCode());
        shelfDetailParam.setStatus(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
        List<ShelfDetailDTO> shelfDetailList = remoteShelfClient.getShelfDetailList(shelfDetailParam);
        if (!ObjectUtils.isEmpty(shelfBiz)) {
            WarehouseDTO warehouse = remoteWarehouseClient.queryByCode(shelfBiz.getWarehouseCode());
            CargoOwnerDTO cargoOwner = remoteCargoOwnerClient.queryByCode(shelfBiz.getCargoCode());

            shelfBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
            shelfBiz.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());

        }
        List<ShelfDetailBizDTO> shelfDetailBizList = new ArrayList<>();
        // 补充明细数据
        if (!CollectionUtils.isEmpty(shelfDetailList)) {
            List<ShelfDetailBizDTO> shelfDetailBizTempList =
                    ConverterUtil.convertList(shelfDetailList, ShelfDetailBizDTO.class);
            for (ShelfDetailBizDTO shelfDetail : shelfDetailBizTempList) {
                shelfDetail.setWarehouseName(shelfBiz.getWarehouseName());
                shelfDetail.setCargoName(shelfBiz.getCargoName());
                shelfDetailBizList.add(shelfDetail);
            }
            shelfBiz.setDetailList(shelfDetailBizList);
        }
        return Result.success(shelfBiz);
    }

    @Override
    public Result<ShelfDetailBizDTO> scanUpcCode(ScanUpcCodeParam param) {

        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setCode(param.getShelfCode());
        ShelfDTO shelfDTO = remoteShelfClient.get(shelfParam);
        if (ObjectUtil.isEmpty(shelfDTO)) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_DETAIL_NULL);
        }

        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setCargoCode(shelfDTO.getCargoCode());
        skuUpcParam.setUpcCode(param.getUpcCode());
        SkuUpcDTO skuUpcDTO = remoteSkuClient.getSkuUpc(skuUpcParam);
        if (ObjectUtil.isEmpty(skuUpcDTO)) {
            throw new BaseException(BaseBizEnum.TIP, "条码信息不存在");
        }

        ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
        shelfDetailParam.setShelfCode(param.getShelfCode());
        shelfDetailParam.setSkuCode(skuUpcDTO.getSkuCode());
        List<ShelfDetailDTO> shelfDetailList = remoteShelfClient.getShelfDetailList(shelfDetailParam);
        if (CollectionUtils.isEmpty(shelfDetailList)) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_DETAIL_NULL_RE_SCAN);
        }
        ShelfDetailDTO shelfDetail = shelfDetailList.stream()
                .filter(a -> ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus().equals(a.getStatus())).findAny().orElse(null);
        if (ObjectUtils.isEmpty(shelfDetail)) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_DETAIL_COMPLETE_RE_SCAN);
        }
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(shelfDetail.getCargoCode());
        skuLotParam.setSkuCode(shelfDetail.getSkuCode());
        skuLotParam.setCode(shelfDetail.getSkuLotNo());
        SkuLotDTO skuLot = remoteSkuLotClient.get(skuLotParam);
        if (ObjectUtils.isEmpty(skuLot)) {
            throw new BaseException(BaseBizEnum.TIP, "上架单明细商品对应批次把信息不正确");
        }
        ShelfDetailBizDTO shelfDetailBiz = ConverterUtil.convert(shelfDetail, ShelfDetailBizDTO.class);
        WarehouseDTO warehouse = remoteWarehouseClient.queryByCode(shelfDetail.getWarehouseCode());
        CargoOwnerDTO cargoOwner = remoteCargoOwnerClient.queryByCode(shelfDetail.getCargoCode());
        shelfDetailBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
        shelfDetailBiz.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
        shelfDetailBiz.setSkuQuality(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getSkuQuality());
        shelfDetailBiz.setProductionNo(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getProductionNo());
        shelfDetailBiz.setValidityCode(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getValidityCode());
        shelfDetailBiz.setReceiveDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getReceiveDate());
        shelfDetailBiz.setReceiveDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getReceiveDateFormat());
        shelfDetailBiz.setManufDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getManufDate());
        shelfDetailBiz.setManufDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getManufDateFormat());
        shelfDetailBiz.setExpireDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getExpireDate());
        shelfDetailBiz.setExpireDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getExpireDateFormat());
        shelfDetailBiz.setWithdrawDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getWithdrawDate());
        shelfDetailBiz.setWithdrawDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getWithdrawDateFormat());

        // 推荐库位
        return Result.success(shelfDetailBiz);
    }

    @Override
    public Result<String> recommend(RecommendLocationParam param) {
        LocationDTO locationDTO = recommendLocationBiz.recommendLocation(param.getSkuLotNo(), param.getShelfQty());
        if (null == locationDTO) {
            return Result.success("");
        }
        return Result.success(locationDTO.getCode());
    }

    @Override
    public Result<Boolean> scanLocation(ScanLocationCodeParam param) {
        LocationParam locationParam = new LocationParam();
        locationParam.setCode(param.getLocationCode());
        LocationDTO targetLocation = remoteLocationClient.get(locationParam);
        if (ObjectUtils.isEmpty(targetLocation)) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_LOCATION_NULL_RE_SCAN);
        }
        // 目标库区数据
        ZoneParam targetZoneParam = new ZoneParam();
        targetZoneParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        targetZoneParam.setCode(targetLocation.getZoneCode());
        ZoneDTO targetZone = remoteZoneClient.get(targetZoneParam);
        if (ObjectUtils.isEmpty(targetZoneParam)) {
            throw new BaseException(BaseBizEnum.TIP, "目标库区信息不存在");
        }
        List<String> allowZoneTypeList = new ArrayList<>();
        allowZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_STORE.getType());
        allowZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PICK.getType());
        allowZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PREP.getType());
        allowZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_FINANCE.getType());
        if (!allowZoneTypeList.contains(targetZone.getType())) {
            throw new BaseException(BaseBizEnum.TIP, "目标库位库区不正确！");
        }
        List<String> allowLocationTypeList = new ArrayList<>();
        allowLocationTypeList.add(LocationTypeEnum.LOCATION_TYPE_STORE.getType());
        allowLocationTypeList.add(LocationTypeEnum.LOCATION_TYPE_PICK.getType());
        allowLocationTypeList.add(LocationTypeEnum.LOCATION_TYPE_PREP.getType());
        allowLocationTypeList.add(LocationTypeEnum.LOCATION_TYPE_FINANCE.getType());
        // allowLocationTypeList.add(LocationTypeEnum.LOCATION_TYPE_DIFF.getType());
        if (!allowLocationTypeList.contains(targetLocation.getType())) {
            throw new BaseException(BaseBizEnum.TIP, "目标库位不正确！");
        }
        return Result.success(true);
    }

    @Override
    public Result<ShelfBizDTO> getDetail(ShelfBizParam param) {
        ShelfParam bizParam = ConverterUtil.convert(param, ShelfParam.class);
        ShelfDTO result = remoteShelfClient.getDetail(bizParam);
        ShelfBizDTO shelfBiz = ConverterUtil.convert(result, ShelfBizDTO.class);
        if (!ObjectUtils.isEmpty(shelfBiz)) {
            WarehouseDTO warehouse = remoteWarehouseClient.queryByCode(shelfBiz.getWarehouseCode());
            CargoOwnerDTO cargoOwner = remoteCargoOwnerClient.queryByCode(shelfBiz.getCargoCode());

            shelfBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
            shelfBiz.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
            shelfBiz.setDetailList(ConverterUtil.convertList(result.getDetailList(), ShelfDetailBizDTO.class));
            // 补充明细数据
            for (ShelfDetailBizDTO shelfDetail : shelfBiz.getDetailList()) {
                shelfDetail.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
                shelfDetail.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
                shelfDetail.setSkuQualityName(ObjectUtils.isEmpty(shelfDetail.getSkuQuality()) ? ""
                        : SkuQualityEnum.getEnum(shelfDetail.getSkuQuality()).getMessage());

                SkuLotParam skuLotParam = new SkuLotParam();
                skuLotParam.setCargoCode(shelfDetail.getCargoCode());
                skuLotParam.setSkuCode(shelfDetail.getSkuCode());
                skuLotParam.setCode(shelfDetail.getSkuLotNo());
                SkuLotDTO skuLotDTO = remoteSkuLotClient.get(skuLotParam);
                if (!ObjectUtils.isEmpty(skuLotDTO)) {
                    shelfDetail.setExternalSkuLotNo(skuLotDTO.getExternalSkuLotNo());
                    shelfDetail.setValidityCode(skuLotDTO.getValidityCode());
                    shelfDetail.setExternalLinkBillNo(skuLotDTO.getExternalLinkBillNo());
                    shelfDetail.setInventoryType(skuLotDTO.getInventoryType());
                    shelfDetail.setManufDate(skuLotDTO.getManufDate());
                    shelfDetail.setReceiveDate(skuLotDTO.getReceiveDate());
                    shelfDetail.setExpireDate(skuLotDTO.getExpireDate());
                    shelfDetail.setProductionNo(skuLotDTO.getProductionNo());
                }
            }
            List<ShelfDetailBizDTO> detailBizList = shelfBiz.getDetailList().stream()
                    .sorted(Comparator.comparing(ShelfDetailBizDTO::getSkuCode).thenComparing(ShelfDetailBizDTO::getId))
                    .collect(Collectors.toList());
            shelfBiz.setDetailList(detailBizList);
        }

        // 收货作业批次 | 覆盖出库单号
        if (ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType().equals(shelfBiz.getType())) {
            ReceiptDTO receipt = remoteReceiptClient.queryReceiptByRecId(shelfBiz.getBillNo());
            shelfBiz.setBillNo(ObjectUtils.isEmpty(receipt) ? shelfBiz.getBillNo() : receipt.getAsnId());
        }

        return Result.success(shelfBiz);
    }

    @Override
    public Result<Page<ShelfBizDTO>> getPage(ShelfBizParam param) {
        // upc查询
        if (!CollectionUtils.isEmpty(param.getUpcCodeList())) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setCargoCodeList(param.getCargoCodeList());
            skuUpcParam.setUpcCodeList(param.getUpcCodeList());
            List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            if (!CollectionUtils.isEmpty(skuUpcList)) {
                List<String> skuCodeList = skuUpcList.stream().map(SkuUpcDTO::getSkuCode).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(skuCodeList)) {
                    param.setId(-1L);
                } else {
                    if (CollectionUtil.isEmpty(param.getSkuCodeList())) {
                        param.setSkuCodeList(skuCodeList);
                    } else {
                        Collection<String> intersection = CollectionUtil.intersection(param.getSkuCodeList(), skuCodeList);
                        if (CollectionUtil.isEmpty(intersection)) {
                            param.setId(-1L);
                        } else {
                            param.setSkuCodeList(new ArrayList<>(intersection));
                        }
                    }
                }
            } else {
                param.setId(-1L);
            }
        }
        if (!CollectionUtils.isEmpty(param.getSkuCodeList())) {
            ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
            shelfDetailParam.setSkuCodeList(param.getSkuCodeList());
            List<ShelfDetailDTO> shelfDetailList = remoteShelfClient.getShelfDetailList(shelfDetailParam);

            List<String> shelfCodeList = shelfDetailList.stream().flatMap(a -> Stream.of(a.getShelfCode())).distinct()
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(shelfCodeList)) {
                param.setId(-1L);
            } else {
                if (CollectionUtil.isEmpty(param.getCodeList())) {
                    param.setCodeList(shelfCodeList);
                } else {
                    Collection<String> intersection = CollectionUtil.intersection(param.getCodeList(), shelfCodeList);
                    if (CollectionUtil.isEmpty(intersection)) {
                        param.setId(-1L);
                    } else {
                        param.setCodeList(new ArrayList<>(intersection));
                    }
                }
            }
        }

        ShelfParam bizParam = ConverterUtil.convert(param, ShelfParam.class);
        if (param != null && !CollectionUtils.isEmpty(param.getMarkList())) {
            bizParam.setMark(ShelfMarkEnum.queryParamListToInteger(param.getMarkList()));
        }
        // 到货通知单查询
        if (!StringUtils.isEmpty(param.getBillNo())) {
            AsnDTO asn = remoteAsnClient.queryOneByAsnId(param.getBillNo());
            if (!ObjectUtils.isEmpty(asn)) {
//                ReceiptParam receiptParam = new ReceiptParam();
//                receiptParam.setAsnId(asn.getAsnId());
//                List<ReceiptDTO> receiptList = remoteReceiptClient.getList(receiptParam);
                ReceiptDetailParam receiptDetailParam = new ReceiptDetailParam();
                receiptDetailParam.setAsnId(asn.getAsnId());
                List<ReceiptDetailDTO> receiptList = remoteReceiptDetailClient.getList(receiptDetailParam);
                List<String> recIdList = receiptList.stream().flatMap(a -> Stream.of(a.getRecId())).collect(Collectors.toList());
                bizParam.setBillNo(null);
                bizParam.setBillNoList(recIdList);
            }
        }
        Page<ShelfDTO> page = remoteShelfClient.getPage(bizParam);
        List<ShelfBizDTO> resultBizList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<String> warehouseCodeList = page.getRecords().stream().flatMap(a -> Stream.of(a.getWarehouseCode()))
                    .distinct().collect(Collectors.toList());
            WarehouseParam warehouseParam = new WarehouseParam();
            warehouseParam.setCodeList(warehouseCodeList);
            List<WarehouseDTO> warehouseList = remoteWarehouseClient.queryList(warehouseParam);

            List<String> cargoCodeList = page.getRecords().stream().flatMap(a -> Stream.of(a.getCargoCode())).distinct()
                    .collect(Collectors.toList());
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(cargoCodeList);
            List<CargoOwnerDTO> cargoOwnerList = remoteCargoOwnerClient.queryList(cargoOwnerParam);

            resultBizList = page.getRecords().stream().flatMap(a -> {
                WarehouseDTO warehouse =
                        warehouseList.stream().filter(b -> b.getCode().equals(a.getWarehouseCode())).findAny().orElse(null);
                CargoOwnerDTO cargoOwner =
                        cargoOwnerList.stream().filter(b -> b.getCode().equals(a.getCargoCode())).findAny().orElse(null);

                ShelfBizDTO shelfBiz = ConverterUtil.convert(a, ShelfBizDTO.class);
                if (!ObjectUtils.isEmpty(shelfBiz)) {
                    shelfBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
                    shelfBiz.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
                    shelfBiz
                            .setDetailList(ConverterUtil.convertList(shelfBiz.getDetailList(), ShelfDetailBizDTO.class));
                }
                if (ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType().equals(shelfBiz.getType())) {
                    ReceiptDTO receipt = remoteReceiptClient.queryReceiptByRecId(shelfBiz.getBillNo());
                    shelfBiz.setAsnNo(ObjectUtils.isEmpty(receipt) ? shelfBiz.getBillNo() : receipt.getAsnId());
                } else {
                    shelfBiz.setAsnNo(shelfBiz.getBillNo());
                }
                return Stream.of(shelfBiz);
            }).collect(Collectors.toList());
        }
        Page<ShelfBizDTO> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(resultBizList);
        return Result.success(result);
    }

//    @Override
//    @GlobalLock
//    @Deprecated
//    public Result<Boolean> complete(CodeParam param) {
//        // 上架单
//        ShelfParam shelfParam = new ShelfParam();
//        shelfParam.setCode(param.getCode());
//        ShelfDTO shelf = remoteShelfClient.getDetail(shelfParam);
//        if (!ShelfStatusEnum.STATUS_DOING.getStatus().equals(shelf.getStatus())) {
//            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_STATUS_ERROR);
//        }
//        List<ShelfDetailDTO> detailList = shelf.getDetailList();
//        for (ShelfDetailDTO detail : detailList) {
//            if (!ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(detail.getStatus())) {
//                throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_DETAIL_STATUS_ERROR);
//            }
//        }
//        shelfParam.setCompleteDate(System.currentTimeMillis());
//        shelfParam.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
//
//        // 收货作业批次
//        ReceiptDTO receipt = remoteReceiptClient.queryReceiptByRecId(shelf.getBillNo());
//        ContainerDTO container = null;
//        ContainerLogDTO containerLogDTO = null;
//        if (ObjectUtils.isEmpty(receipt)) {
//            throw new BaseException(ReceiptError.RECEIPT_DATA_ERROR, shelf.getBillNo());
//        }
//        if (ReceiptStatusEnum.COMPLETE_SHELF.getCode().equals(receipt.getStatus())) {
//            throw new BaseException(ReceiptError.RECEIPT_COMPLETE, shelf.getBillNo());
//        }
//        if (!ReceiptStatusEnum.ON_SHELF.getCode().equals(receipt.getStatus())) {
//            throw new BaseException(ReceiptError.RECEIPT_FROBIDDEN_COMPLETE, shelf.getBillNo());
//        }
//        receipt.setStatus(ReceiptStatusEnum.COMPLETE_SHELF.code());
//        receipt.setCompleteShelfDate(System.currentTimeMillis());
//
//        // 解绑容器
//        container = remoteContainerClient.queryByCode(receipt.getContCode());
//        if (StringUtils.isEmpty(container)) {
//            throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
//        }
//        if (!receipt.getAsnId().equalsIgnoreCase(container.getOccupyNo())) {
//            throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
//        }
//        if (!ContainerStatusEnum.OCCUPY.getValue().equals(container.getStatus())) {
//            throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
//        }
//        // 释放容器
//        containerLogDTO = new ContainerLogDTO();
//        containerLogDTO.setWarehouseCode(container.getWarehouseCode());
//        containerLogDTO.setContCode(container.getCode());
//        containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
//        containerLogDTO.setCreatedTime(System.currentTimeMillis());
//        containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
//        containerLogDTO.setOpContent(String.format("上架%s释放容器:%s", shelf.getCode(), container.getCode()));
//        containerLogDTO.setOpDate(System.currentTimeMillis());
//        containerLogDTO.setOccupyNo(container.getOccupyNo());
//        containerLogDTO.setOccupyType(container.getOccupyType());
//
//        container.setStatus(ContainerStatusEnum.ENABLE.getValue());
//        container.setOccupyType("");
//        container.setOccupyNo("");
//        container.setRemark("");
//
//        ShelfCompleteBO shelfComplete = new ShelfCompleteBO();
//        shelfComplete.setShelf(shelf);
//        shelfComplete.setReceipt(receipt);
//        shelfComplete.setContainer(container);
//        shelfComplete.setContainerLogDTO(containerLogDTO);
//
//        Boolean result = shelfContextService.completeShelf(shelfComplete);
//        if (!result) {
//            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
//        }
//        return Result.success(result);
//    }

    /**
     * 页面上架
     *
     * @param param
     * @return
     */
    @Override
    public Result<Boolean> completeWholeShelf(ShelfCompleteParam param) {
        // 这个接口只适用于库内上架
        param.setOpType(OpTypeEnum.OP_TYPE_PAPER.getType());
        String lockKey = StrUtil.join(StrUtil.COLON, CurrentRouteHolder.getWarehouseCode(), ShelfDTO.class.getSimpleName(), param.getCode());
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 300, TimeUnit.SECONDS);
            if (locked) {
                // 增加的基本校验
                param.getDetailList().forEach(it -> {
                    if (StrUtil.isBlank(it.getUid())) {
                        throw new BaseException(BaseBizEnum.TIP, "参数异常，uid必传");
                    }
                });
                // 上架单
                ShelfParam shelfParam = new ShelfParam();
                shelfParam.setCode(param.getCode());
                ShelfDTO shelf = remoteShelfClient.getDetail(shelfParam);
                // 状态保护
                if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelf.getStatus())) {
                    throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_SHELF_STATUS_PROTECTED);
                }
                if (ShelfStatusEnum.STATUS_CANCEL.getStatus().equals(shelf.getStatus())) {
                    throw ExceptionUtil.exceptionWithMessage("上架单已取消");
                }
                //淘天校验 TODO ADD 2024-03-13
                checkTaoTianApply(shelf.getDetailList());
                // 上架方式保护
                if (!shelf.getOpType().equals(param.getOpType())) {
                    throw new BaseException(BaseBizEnum.TIP, "请使用PDA上架！");
                }

                List<ShelfDetailDTO> dbDetailList = shelf.getDetailList();
                List<ShelfCompleteDetailBizDTO> currentDetailList = param.getDetailList();

                checkNewGoods(shelf, shelf.getDetailList());

                // 数据库中有的明细id前端没有，说明上架单已经被人操作过
                long sumIdInDb = dbDetailList.stream().map(ShelfDetailDTO::getId).distinct().mapToLong(Long::longValue).sum();
                long sumIdInParam = currentDetailList.stream()
                        .filter(a -> ObjectUtil.isNotEmpty(a.getId()))
                        .map(ShelfCompleteDetailBizDTO::getId).distinct().mapToLong(Long::longValue).sum();
                if (sumIdInDb != sumIdInParam) {
                    throw new BaseException(BaseBizEnum.TIP, "上架单已经被操作，请刷新页面重新录入！");
                }

                // 过滤有用的详情数据
                List<ShelfCompleteDetailBizDTO> currentOperateDetailList =
                        param.getDetailList().stream().filter(a -> !StringUtils.isEmpty(a.getTargetLocationCode()))
                                .filter(a -> !ObjectUtils.isEmpty(a.getShelfSkuQty()))
                                .filter(a -> BigDecimal.ZERO.compareTo(a.getShelfSkuQty()) < 0).collect(Collectors.toList());

                List<String> targetLocationCodeList =
                        param.getDetailList().stream().filter(a -> !StringUtils.isEmpty(a.getTargetLocationCode()))
                                .filter(a -> !ObjectUtils.isEmpty(a.getShelfSkuQty()))
                                .filter(a -> BigDecimal.ZERO.compareTo(a.getShelfSkuQty()) < 0)
                                .flatMap(a -> Stream.of(a.getTargetLocationCode())).distinct().collect(Collectors.toList());

                if (CollectionUtils.isEmpty(targetLocationCodeList)) {
                    throw new BaseException(BaseBizEnum.TIP, "上架目标库位信息不正确");
                }

                // 库位
                LocationParam targetLocationParam = new LocationParam();
                targetLocationParam.setWarehouseCode(shelf.getWarehouseCode());
                targetLocationParam.setCodeList(targetLocationCodeList);
                List<LocationDTO> targetLocationList = remoteLocationClient.getList(targetLocationParam);
                if (CollectionUtils.isEmpty(targetLocationList)) {
                    throw new BaseException(BaseBizEnum.TIP, "目标库位信息不存在");
                }
                // 校验库位的状态
                List<ZoneDTO> targetZoneList = checkLocationStatus(shelf, targetLocationList);

                // 最大商品数、最大品类数检验
                StockLocationParam stockLocationParam = new StockLocationParam();
                stockLocationParam.setHasPhysicalQty(true);
                stockLocationParam.setLocationCodeList(targetLocationCodeList);
                List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getList(stockLocationParam);
                List<Long> doneIdList = dbDetailList.stream().filter(it -> it.getStatus().equals(ShelfStatusEnum.STATUS_COMPLETED.getStatus())).map(BaseDTO::getId).collect(Collectors.toList());
                for (LocationDTO locationDTO : targetLocationList) {
                    List<StockLocationDTO> targetStockLocation = stockLocationDTOList.stream().filter(it -> it.getLocationCode().equals(locationDTO.getCode())).collect(Collectors.toList());
                    List<StockLocationDTO> source = currentOperateDetailList.stream()
                            .filter(it -> it.getTargetLocationCode().equals(locationDTO.getCode()))
                            .filter(it -> !doneIdList.contains(it.getId()))
                            .map(it -> {
                                StockLocationDTO stockLocationDTO = new StockLocationDTO();
                                stockLocationDTO.setCargoCode(shelf.getCargoCode());
                                stockLocationDTO.setSkuCode(it.getSkuCode());
                                stockLocationDTO.setPhysicalQty(it.getShelfSkuQty());
                                return stockLocationDTO;
                            }).collect(Collectors.toList());
                    locationBiz.maxSkuCheck(targetStockLocation, source, locationDTO);
                }

                // 拆分上架单明细数据
                List<ShelfDetailDTO> saveOrUpdateList =
                        getSplitShelfDetailList(dbDetailList, currentDetailList, targetLocationList);

                List<ShelfDetailDTO> allShelfDetailList = new ArrayList<>();
                allShelfDetailList.addAll(dbDetailList);
                for (ShelfDetailDTO shelfDetail : saveOrUpdateList) {
                    if (!allShelfDetailList.contains(shelfDetail)) {
                        allShelfDetailList.add(shelfDetail);
                    }
                }

                // 上架单
                shelf.setOpBy(CurrentUserHolder.getUserName());
                shelf.setShelfSkuType(
                        allShelfDetailList.stream().filter(a -> ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(a.getStatus()))
                                .flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toSet()).size());
                shelf.setShelfSkuQty(currentOperateDetailList.stream().map(ShelfCompleteDetailBizDTO::getShelfSkuQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));

//                new version
                if (shelf.getShelfSkuQty().compareTo(shelf.getSkuQty()) == 0) {
                    shelf.setCompleteDate(System.currentTimeMillis());
                    shelf.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
                } else {
                    shelf.setStatus(ShelfStatusEnum.STATUS_DOING.getStatus());
                }


                // 会写数据
                ShelfCompleteBO shelfCompleteBO = new ShelfCompleteBO();
                ShelfTypeEnum shelfType = ShelfTypeEnum.getEnum(shelf.getType());
                Holder<ReceiptDTO> receiptDTOHolder = new Holder<>();
                // 先收后审 
                boolean receiptFirst = false;
                List<MessageMqDTO> messageMqDTOList = new ArrayList<>();
                switch (shelfType) {
                    case SHELF_TYPE_RECEIPT: {
                        // 收货作业批次
                        ReceiptDTO receipt = remoteReceiptClient.queryReceiptByRecId(shelf.getBillNo());
                        receiptDTOHolder.set(receipt);
                        ContainerDTO container = null;
                        ContainerLogDTO containerLogDTO = null;
                        if (ObjectUtils.isEmpty(receipt)) {
                            throw new BaseException(ReceiptError.RECEIPT_DATA_ERROR, shelf.getBillNo());
                        }
                        if (ReceiptStatusEnum.COMPLETE_SHELF.getCode().equals(receipt.getStatus())) {
                            throw new BaseException(ReceiptError.RECEIPT_COMPLETE, shelf.getBillNo());
                        }

                        if (!AsnTypeEnum.RETURN.getCode().equalsIgnoreCase(receipt.getType())) {
                            AsnParam asnParam = new AsnParam();
                            asnParam.setAsnId(receipt.getAsnId());
                            AsnDTO asnDTO = remoteAsnClient.get(asnParam);
                            if (null == asnDTO) throw ExceptionUtil.DATA_ERROR;
                            if (AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.RECEIVE_BEFORE_REVIEW)) {
                                receiptFirst = true;
                            }
                        }

//                        先收后审不需要这个校验
                        if (!receiptFirst) {
                            //关仓协同,判定是否能上架
                            checkEnableShelf(receipt);
                        }

                        if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelf.getStatus())) {
                            // 解绑容器
                            container = remoteContainerClient.queryByCode(receipt.getContCode());
                            if (StringUtils.isEmpty(container)) {
                                throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                            }
                            if (!receipt.getAsnId().equalsIgnoreCase(container.getOccupyNo())
                                    && !receipt.getTallyCode().equalsIgnoreCase(container.getOccupyNo())
                                    && !receipt.getRecId().equalsIgnoreCase(container.getOccupyNo())) {
                                throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                            }
                            if (!ContainerStatusEnum.OCCUPY.getValue().equals(container.getStatus())) {
                                throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                            }
                            // 释放容器
                            containerLogDTO = new ContainerLogDTO();
                            containerLogDTO.setWarehouseCode(container.getWarehouseCode());
                            containerLogDTO.setContCode(container.getCode());
                            containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                            containerLogDTO.setCreatedTime(System.currentTimeMillis());
                            containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                            containerLogDTO.setOpContent(String.format("上架%s释放容器:%s", shelf.getCode(), container.getCode()));
                            containerLogDTO.setOpDate(System.currentTimeMillis());
                            containerLogDTO.setOccupyNo(container.getOccupyNo());
                            containerLogDTO.setOccupyType(container.getOccupyType());
                            // 容器
                            container.setStatus(ContainerStatusEnum.ENABLE.getValue());
                            container.setOccupyType("");
                            container.setOccupyNo("");
                            container.setRemark("");
                            // 收货作业批次
                            receipt.setStatus(ReceiptStatusEnum.COMPLETE_SHELF.code());
                            if (receiptFirst) {
                                receipt.setStatus(ReceiptStatusEnum.PRE_SHELF.getCode());
                                receipt.setPreCompleteDate(System.currentTimeMillis());
                            } else {
                                receipt.setCompleteShelfDate(System.currentTimeMillis());
                            }
                        } else {
                            if (ReceiptStatusEnum.WAIT_SHELF.getCode().equals(receipt.getStatus())) {
                                receipt.setStatus(ReceiptStatusEnum.ON_SHELF.code());
                            }
                        }
                        shelfCompleteBO.setReceipt(receipt);
                        shelfCompleteBO.setContainer(container);
                        shelfCompleteBO.setContainerLogDTO(containerLogDTO);
                    }
                    break;
                    case SHELF_TYPE_RETURN: {
                        // 归位上架操作
                        ReturnOrderParam returnOrderParam = new ReturnOrderParam();
                        returnOrderParam.setRetOrderCode(shelf.getBillNo());
                        ReturnOrderDTO returnOrder = remoteReturnOrderClient.get(returnOrderParam);
                        if (ObjectUtils.isEmpty(returnOrder)) {
                            throw new BaseException(BaseBizEnum.TIP, "归位单不存在！");
                        }
                        if (ReturnOrderEnum.RETURN_ORDER_COMPLETED.getStatus().equals(returnOrder.getStatus())) {
                            throw new BaseException(BaseBizEnum.TIP,
                                    String.format("归位单:%s,已经完成！", returnOrder.getRetOrderCode()));
                        }

                        // 上架完成时回写对应容器的归位单明细
                        List<ReturnOrderDetailDTO> modifyReturnOrderDetailList = new ArrayList<>();
                        if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equalsIgnoreCase(shelf.getStatus())) {
                            List<ReturnOrderDetailDTO> returnOrderDetailDTOList = returnOrder.getListDetail().stream()
                                    .filter(it -> it.getContCode().equalsIgnoreCase(shelf.getContCode()))
                                    .filter(it -> !it.getStatus().equalsIgnoreCase(ReturnOrderEnum.RETURN_ORDER_COMPLETED.getStatus()))
                                    .collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(returnOrderDetailDTOList)) {
                                for (ReturnOrderDetailDTO returnOrderDetailDTO : returnOrderDetailDTOList) {
                                    returnOrderDetailDTO.setCompleteOnShelfQty(returnOrderDetailDTO.getExpReturnQty());
                                    returnOrderDetailDTO.setStatus(ReturnOrderEnum.RETURN_ORDER_COMPLETED.getStatus());
                                }
                                modifyReturnOrderDetailList.addAll(returnOrderDetailDTOList);
                            }

                        }

                        ContainerDTO container = null;
                        ContainerLogDTO containerLogDTO = null;
                        // 是否完成归位单
                        if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelf.getStatus())) {
                            // 解绑容器
                            container = remoteContainerClient.queryByCode(shelf.getContCode());
                            if (StringUtils.isEmpty(container)) {
                                throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                            }
                            if (!ContainerStatusEnum.OCCUPY.getValue().equals(container.getStatus())) {
                                throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                            }
                            containerLogDTO = new ContainerLogDTO();
                            containerLogDTO.setWarehouseCode(container.getWarehouseCode());
                            containerLogDTO.setContCode(container.getCode());
                            containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                            containerLogDTO.setCreatedTime(System.currentTimeMillis());
                            containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                            containerLogDTO.setOpContent(String.format("上架%s释放容器:%s", shelf.getCode(), container.getCode()));
                            containerLogDTO.setOpDate(System.currentTimeMillis());
                            containerLogDTO.setOccupyNo(container.getOccupyNo());
                            containerLogDTO.setOccupyType(container.getOccupyType());
                            // 容器
                            container.setStatus(ContainerStatusEnum.ENABLE.getValue());
                            container.setOccupyType("");
                            container.setOccupyNo("");
                            container.setRemark("");

                            // 收货作业批次
                            returnOrder.setCompleteOnShelfQty(returnOrder.getListDetail().stream()
                                    .map(ReturnOrderDetailDTO::getCompleteOnShelfQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        } else {
                            if (ReturnOrderEnum.RETURN_ORDER_CREATE.getStatus().equals(returnOrder.getStatus())) {
                                returnOrder.setStatus(ReturnOrderEnum.RETURN_ORDER_DOING.getStatus());
                            }
                            returnOrder.setCompleteOnShelfQty(returnOrder.getListDetail().stream()
                                    .map(ReturnOrderDetailDTO::getCompleteOnShelfQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        }

                        // 事务数据组装
                        shelfCompleteBO.setReturnOrder(returnOrder);
                        shelfCompleteBO.setReturnOrderDetailList(modifyReturnOrderDetailList);
                        shelfCompleteBO.setContainer(container);
                        shelfCompleteBO.setContainerLogDTO(containerLogDTO);
                    }
                    break;
                    case SHELF_TYPE_SPLIT:
                        if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelf.getStatus())) {
                            freeContainerByShelfCode(shelfCompleteBO, shelf.getCode(), shelf.getContCode());
                            PrePackagePlanDTO prePackagePlanDTO = completePrePlan(shelf.getBillNo(), shelf.getCode());
                            if (null != prePackagePlanDTO) {
                                shelfCompleteBO.setPrePackagePlanDTO(prePackagePlanDTO);
                                BillLogDTO prePackagePlanCompleteLog = new BillLogDTO();
                                prePackagePlanCompleteLog.setWarehouseCode(prePackagePlanDTO.getWarehouseCode());
                                prePackagePlanCompleteLog.setCargoCode(prePackagePlanDTO.getCargoCode());
                                prePackagePlanCompleteLog.setBillNo(prePackagePlanDTO.getPrePlanCode());
                                prePackagePlanCompleteLog.setBillType(BillLogTypeEnum.PRE_PLAN.getType());
                                prePackagePlanCompleteLog.setOpContent("拆包上架完成");
                                prePackagePlanCompleteLog.setOpDate(System.currentTimeMillis());
                                prePackagePlanCompleteLog.setOpRemark("拆包上架完成");
                                prePackagePlanCompleteLog.setOpBy(CurrentUserHolder.getUserName());
                                shelfCompleteBO.setBillLogDTO(prePackagePlanCompleteLog);
                            }
                        }
                        break;
                    case SHELF_TYPE_SALE_RETURN:
                    case SHELF_TYPE_BOM_ASSEMBLE:
                    case SHELF_TYPE_BOM_DISASSEMBLE:
                        break;
                    case SHELF_TYPE_SHELF_OFF:
                    case SHELF_TYPE_PICK:
                    case SHELF_TYPE_DIFF:
                    default:
                        throw new BaseException(BaseBizEnum.TIP, "上架单类型不正确！");
                }

                // 先收后审上架单状态
                if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equalsIgnoreCase(shelf.getStatus()) && receiptFirst) {
                    shelf.setStatus(ShelfStatusEnum.STATUS_PREPARE.getStatus());
                    shelf.setCompleteDate(null);
                    shelf.setPreCompleteDate(System.currentTimeMillis());
                }
                for (ShelfDetailDTO shelfDetailDTO : saveOrUpdateList) {
                    if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equalsIgnoreCase(shelfDetailDTO.getStatus()) && receiptFirst) {
                        shelfDetailDTO.setStatus(ShelfStatusEnum.STATUS_PREPARE.getStatus());
                    }
                }
                // 检验目标库区、库位数据
                checkTargetLocationAndShelfQtyAndLotRule(shelf, currentOperateDetailList, targetZoneList, targetLocationList, receiptDTOHolder);

                shelfCompleteBO.setWarehouseCode(shelf.getWarehouseCode());

                // 剔除明细数据更新
                shelf.setDetailList(null);

                shelfCompleteBO.setShelf(shelf);
                shelfCompleteBO.setDetailList(saveOrUpdateList);
                shelfCompleteBO.setMessageMqDTOList(messageMqDTOList);
                Boolean result = shelfContextService.completeShelf(shelfCompleteBO);
                if (!result) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
                if (ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType().equalsIgnoreCase(shelf.getType())) {
                    ReceiptDTO receiptDTO = remoteReceiptClient.queryReceiptAndDetailByRecId(shelf.getBillNo());
                    if (receiptDTO != null) {
                        AsnLogDTO asnLogDTO = new AsnLogDTO();
                        asnLogDTO.setWarehouseCode(shelf.getWarehouseCode());
                        asnLogDTO.setCargoCode(shelf.getCargoCode());
                        asnLogDTO.setAsnId(receiptDTO.getAsnId());
                        asnLogDTO.setOpBy(CurrentUserHolder.getUserName());
                        asnLogDTO.setMsg(String.format("到货通知单%s相关收货作业批次%s上架单上架:上架单号%s", receiptDTO.getAsnId(),
                                shelf.getBillNo(), shelf.getCode()));
                        remoteAsnClient.saveAsnLog(asnLogDTO);
                    }
                }
                return Result.success(true);
            }
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            return Result.success(false);
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
        return Result.success(true);
    }

    private void checkTaoTianApply(List<ShelfDetailDTO> detailList) {
        //明细上有【上架驳回】/【待申请】标记，不允许上架： 报错“该商品未通过上架申请，不允许上架，请至淘天服务商后台检查溯源码”
        if (detailList.stream().anyMatch(a ->
                ShelfMarkDetailEnum.NumToEnum(a.getMark()).contains(ShelfMarkDetailEnum.WAIT_SHELF)
                        || ShelfMarkDetailEnum.NumToEnum(a.getMark()).contains(ShelfMarkDetailEnum.REJECT))) {
            throw new BaseException(BaseBizEnum.TIP, "上架单未通过上架申请,不允许上架,请至淘天服务商后台检查溯源码");
        }
        //商品校验不通过,不允许上架
        if (detailList.stream().anyMatch(a ->
                ShelfMarkDetailEnum.NumToEnum(a.getMark()).contains(ShelfMarkDetailEnum.WAIT_CHECK)
                        || ShelfMarkDetailEnum.NumToEnum(a.getMark()).contains(ShelfMarkDetailEnum.CHECK_FAIL))) {
            throw new BaseException(BaseBizEnum.TIP, "该商品校验包装方案和认证结果不通过,不允许上架,请至淘天服务商后台包装方案、商品认证结果");
        }
    }

    /**
     * 完成预包计划
     *
     * @param billNo
     * @param shelfCode
     */
    private PrePackagePlanDTO completePrePlan(String billNo, String shelfCode) {
        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setBillNo(billNo);
        List<ShelfDTO> shelfDTOList = remoteShelfClient.getList(shelfParam);
        boolean allMatch = shelfDTOList.stream()
                .filter(shelfDTO -> !shelfDTO.getCode().equals(shelfCode))
                .allMatch(shelfDTO -> ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelfDTO.getStatus()));

        if (allMatch) {
            PrePackagePlanParam prePackagePlanParam = new PrePackagePlanParam();
            prePackagePlanParam.setPrePlanCode(billNo);
            PrePackagePlanDTO prePackagePlanDTO = remotePrePackagePlanClient.get(prePackagePlanParam);
            prePackagePlanDTO.setStatus(PrePackagePlanStatusEnum.COMPLETE.getCode());
            prePackagePlanDTO.setCompleteQty(prePackagePlanDTO.getPlanQty());
            prePackagePlanDTO.setCompleteTime(System.currentTimeMillis());
            return prePackagePlanDTO;
        }
        return null;
    }

    /**
     * 通过上架单释放容器
     *
     * @param object
     * @param shelfCode
     * @param contCode
     */
    private void freeContainerByShelfCode(ShelfCompleteBO object, String shelfCode, String contCode) {
        // 解绑容器
        ContainerDTO container = remoteContainerClient.queryByCode(contCode);
        if (StringUtils.isEmpty(container)) {
            throw new BaseException(BaseBizEnum.FREE_CONTAINER_ERROR);
        }
        if (!ContainerStatusEnum.OCCUPY.getValue().equals(container.getStatus())) {
            throw new BaseException(BaseBizEnum.FREE_CONTAINER_ERROR);
        }
        if (!shelfCode.equals(container.getOccupyNo())) {
            throw new BaseException(BaseBizEnum.FREE_CONTAINER_ERROR);
        }
        ContainerLogDTO containerLogDTO = new ContainerLogDTO();
        containerLogDTO.setWarehouseCode(container.getWarehouseCode());
        containerLogDTO.setContCode(container.getCode());
        containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
        containerLogDTO.setCreatedTime(System.currentTimeMillis());
        containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
        containerLogDTO.setOpContent(String.format("上架%s释放容器:%s", shelfCode, container.getCode()));
        containerLogDTO.setOpDate(System.currentTimeMillis());
        containerLogDTO.setOccupyNo(container.getOccupyNo());
        containerLogDTO.setOccupyType(container.getOccupyType());
        // 容器
        container.setStatus(ContainerStatusEnum.ENABLE.getValue());
        container.setOccupyType("");
        container.setOccupyNo("");
        container.setRemark("");
        if (object != null) {
            object.setContainer(container);
            object.setContainerLogDTO(containerLogDTO);
        }
    }

    /**
     * 功能描述: 校验库位的状态和父级是否为禁用状态 创建时间: 2020/12/17 8:15 下午
     *
     * @param shelf:
     * @param targetLocationList:
     * @return java.util.List<com.dt.domain.base.dto.ZoneDTO>
     * <AUTHOR>
     */
    private List<ZoneDTO> checkLocationStatus(ShelfDTO shelf, List<LocationDTO> targetLocationList) {
        // 校验库位的状态
        remoteLocationClient.checkStatus(targetLocationList);
        // 巷道
        TunnelParam targetTunnelParam = new TunnelParam();
        targetTunnelParam.setCodeList(targetLocationList.stream().flatMap(a -> Stream.of(a.getTunnelCode())).distinct()
                .collect(Collectors.toList()));
        List<TunnelDTO> targetTunnelDTOList = remoteTunnelClient.getList(targetTunnelParam);
        if (CollectionUtils.isEmpty(targetTunnelDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "目标巷道信息不存在");
        }
        // 校验巷道的状态
        remoteTunnelClient.checkStatus(targetTunnelDTOList);
        // 库区
        ZoneParam targetZoneParam = new ZoneParam();
        targetZoneParam.setWarehouseCode(shelf.getWarehouseCode());
        targetZoneParam.setCodeList(targetLocationList.stream().flatMap(a -> Stream.of(a.getZoneCode())).distinct()
                .collect(Collectors.toList()));
        List<ZoneDTO> targetZoneList = remoteZoneClient.getList(targetZoneParam);
        if (CollectionUtils.isEmpty(targetZoneList)) {
            throw new BaseException(BaseBizEnum.TIP, "目标库区信息不存在");
        }
        // 校验库区的状态
        remoteZoneClient.checkStatus(targetZoneList);
        return targetZoneList;
    }

    /**
     * pda上架
     *
     * @param param
     * @return
     */
    @Override
    @GlobalLock
    public Result<Boolean> completeDetail(ShelfDetailCompleteBizParam param) {
        // 这个接口只适用于PDA上架
        param.setOpType(OpTypeEnum.OP_TYPE_RF.getType());
        // 明细
        ShelfDetailParam detailParam = new ShelfDetailParam();
        detailParam.setShelfCode(param.getShelfCode());
        List<ShelfDetailDTO> shelfDetailList = remoteShelfClient.getShelfDetailList(detailParam);


        ShelfDetailDTO shelfDetail =
                shelfDetailList.stream().filter(a -> a.getId().equals(param.getId())).findAny().get();
        if (ObjectUtils.isEmpty(shelfDetail)) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_DETAIL_NULL);
        }
        //淘天校验 TODO ADD 2024-03-13
        checkTaoTianApply(shelfDetailList);


        if (!ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus().equals(shelfDetail.getStatus())) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_DETAIL_STATUS_ERROR);
        }
        if (shelfDetail.getSkuQty().compareTo(param.getShelfSkuQty()) < 0) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_DETAIL_QTY_ERROR);
        }
        // 上架单
        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setCode(shelfDetail.getShelfCode());
        ShelfDTO shelf = remoteShelfClient.get(shelfParam);
        if (ObjectUtils.isEmpty(shelf)) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_SHELF_STATUS_PROTECTED);
        }
        // 上架方式保护
        if (!shelf.getOpType().equals(param.getOpType())) {
            throw new BaseException(BaseBizEnum.TIP, "请使用页面上架！");
        }
        checkNewGoods(shelf, shelfDetailList);

        // 来源信息
        LocationParam originLocationParam = new LocationParam();
        originLocationParam.setWarehouseCode(shelf.getWarehouseCode());
        originLocationParam.setCode(shelfDetail.getOriginLocationCode());
        LocationDTO originLocation = remoteLocationClient.get(originLocationParam);
        if (ObjectUtils.isEmpty(originLocation)) {
            throw new BaseException(BaseBizEnum.TIP, "来源库位信息不存在");
        }
        // 目标库区数据
        ZoneParam originZoneParam = new ZoneParam();
        originZoneParam.setWarehouseCode(shelf.getWarehouseCode());
        originZoneParam.setCode(originLocation.getZoneCode());
        ZoneDTO originZone = remoteZoneClient.get(originZoneParam);
        if (ObjectUtils.isEmpty(originZone)) {
            throw new BaseException(BaseBizEnum.TIP, "来源库区信息不存在");
        }

        ShelfTypeEnum shelfType = ShelfTypeEnum.getEnum(shelf.getType());
        switch (shelfType) {
            case SHELF_TYPE_RECEIPT:
                if (!ZoneTypeEnum.ZONE_TYPE_TEMP.getType().equals(originZone.getType())) {
                    throw new BaseException(BaseBizEnum.TIP, "来源库区信息不正确！");
                }
                if (!LocationTypeEnum.LOCATION_TYPE_TEMP.getType().equals(originLocation.getType())) {
                    throw new BaseException(BaseBizEnum.TIP, "来源库位信息不正确！");
                }
                if (!LocationUseModeEnum.MODE_RECEIPT.getMode().equals(originLocation.getUseMode())) {
                    throw new BaseException(BaseBizEnum.TIP, "来源库位信息不正确！");
                }
                break;
            case SHELF_TYPE_RETURN:
                if (!ZoneTypeEnum.ZONE_TYPE_TEMP.getType().equals(originZone.getType())) {
                    throw new BaseException(BaseBizEnum.TIP, "来源库区信息不正确！");
                }
                if (!LocationTypeEnum.LOCATION_TYPE_TEMP.getType().equals(originLocation.getType())) {
                    throw new BaseException(BaseBizEnum.TIP, "来源库位信息不正确！");
                }
                if (!LocationUseModeEnum.MODE_RETURN.getMode().equals(originLocation.getUseMode())) {
                    throw new BaseException(BaseBizEnum.TIP, "来源库位信息不正确！");
                }
                break;
            case SHELF_TYPE_SPLIT:
            case SHELF_TYPE_SALE_RETURN:
            case SHELF_TYPE_BOM_ASSEMBLE:
            case SHELF_TYPE_BOM_DISASSEMBLE:
                break;
            case SHELF_TYPE_SHELF_OFF:
            case SHELF_TYPE_PICK:
            case SHELF_TYPE_DIFF:
            default:
                throw new BaseException(BaseBizEnum.TIP, "上架单类型不正确！");
        }
        // 目标信息
        LocationParam locationParam = new LocationParam();
        locationParam.setWarehouseCode(shelf.getWarehouseCode());
        locationParam.setCode(param.getTargetLocationCode());
        LocationDTO targetLocation = remoteLocationClient.get(locationParam);
        if (ObjectUtils.isEmpty(targetLocation)) {
            throw new BaseException(BaseBizEnum.TIP, "目标库位信息不存在");
        }

        // 最大商品数、最大品类数检验
        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setHasPhysicalQty(true);
        stockLocationParam.setLocationCode(targetLocation.getCode());
        List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getList(stockLocationParam);
        StockLocationDTO source = new StockLocationDTO();
        source.setCargoCode(shelf.getCargoCode());
        source.setSkuCode(shelfDetail.getSkuCode());
        source.setPhysicalQty(param.getShelfSkuQty());
        locationBiz.maxSkuCheck(stockLocationDTOList, ListUtil.toList(source), targetLocation);

        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setWarehouseCode(shelf.getWarehouseCode());
        zoneParam.setCode(targetLocation.getZoneCode());
        ZoneDTO targetZone = remoteZoneClient.get(zoneParam);
        if (ObjectUtils.isEmpty(targetZone)) {
            throw new BaseException(BaseBizEnum.TIP, "目标库区信息不存在");
        }
        if (!targetZone.getSkuQuality().equals(shelfDetail.getSkuQuality())) {
            throw new BaseException(BaseBizEnum.TIP, "目标库位库区商品属性不正确");
        }
        if (ZoneStatusEnum.STATUS_DISABLED.getStatus().equals(targetZone.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库区:%s未启用", shelfDetail.getSkuCode(),
                    shelfDetail.getSkuLotNo(), targetLocation.getZoneCode()));
        }
        if (LocationStatusEnum.STATUS_DISABLED.getStatus().equals(targetLocation.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库位:%s未启用", shelfDetail.getSkuCode(),
                    shelfDetail.getSkuLotNo(), targetLocation.getCode()));
        }

        // 混放批次
        mixRuleCheckBiz.checkLocationMixRule(param.getTargetLocationCode(), targetLocation.getMixRuleCode(),
                shelfDetail.getCargoCode(), shelfDetail.getSkuCode(), shelfDetail.getSkuLotNo());
        // 上架逻辑
        List<ShelfDetailDTO> saveOrUpdateList = new ArrayList<>();
        if (shelfDetail.getSkuQty().compareTo(param.getShelfSkuQty()) != 0) {
            // 查询最大行号
            Integer maxLineSql = shelfDetailList.stream().map(ShelfDetailDTO::getLineSeq).map(Integer::parseInt).max(Integer::compareTo).orElse(0);
            // 拆分新明细
            ShelfDetailDTO separation = ConverterUtil.convert(shelfDetail, ShelfDetailDTO.class);
            separation.setId(null);
            separation.setVersion(null);
            separation.setStatus(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
            separation.setLineSeq(String.valueOf(maxLineSql + 1));
            separation.setSkuQty(shelfDetail.getSkuQty().subtract(param.getShelfSkuQty()));
            separation.setShelfSkuQty(new BigDecimal("0.000"));

            // 完成旧纪录
            shelfDetail.setSkuQty(param.getShelfSkuQty());
            shelfDetail.setShelfSkuQty(param.getShelfSkuQty());
            shelfDetail.setTargetLocationCode(param.getTargetLocationCode());
            shelfDetail.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());

            saveOrUpdateList.add(separation);
            saveOrUpdateList.add(shelfDetail);
        } else {
            shelfDetail.setTargetLocationCode(param.getTargetLocationCode());
            shelfDetail.setShelfSkuQty(param.getShelfSkuQty());
            shelfDetail.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
            saveOrUpdateList.add(shelfDetail);
        }

        // 状态保护
        if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelf.getStatus())) {
            throw new WmsBizException(WmsShelfBizEnum.SHELF_BIZ_SHELF_STATUS_PROTECTED);
        }
        List<ShelfDetailDTO> completeShelfDetailList =
                shelfDetailList.stream().filter(a -> !ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(a.getStatus()))
                        .filter(a -> !ShelfStatusEnum.STATUS_PREPARE.getStatus().equalsIgnoreCase(a.getStatus()))
                        .collect(Collectors.toList());
        // 当前明细拆行 或者 有其他未完成的明细
        if (saveOrUpdateList.size() > 1 || !CollectionUtils.isEmpty(completeShelfDetailList)) {
            shelf.setStatus(ShelfStatusEnum.STATUS_DOING.getStatus());
        } else {
            shelf.setCompleteDate(System.currentTimeMillis());
            shelf.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
        }
        // 更新上架单
        shelf.setShelfSkuType(
                shelfDetailList.stream().filter(a -> ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(a.getStatus()))
                        .flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toSet()).size());
        shelf.setOpBy(CurrentUserHolder.getUserName());
        shelf.setShelfSkuQty(shelf.getShelfSkuQty().add(shelfDetail.getShelfSkuQty()));

        // 提交库存数据
        StockShelfParam stockShelf = ConverterUtil.convert(shelfDetail, StockShelfParam.class);
        if (!ObjectUtils.isEmpty(stockShelf)) {
            stockShelf.setTradeDate(shelf.getCreatedTime());
            stockShelf.setOriginZoneCode(originZone.getCode());
            stockShelf.setOriginZoneType(originZone.getType());
            stockShelf.setOriginLocationCode(originLocation.getCode());
            stockShelf.setTargetZoneCode(targetZone.getCode());
            stockShelf.setTargetZoneType(targetZone.getType());
            stockShelf.setTargetLocationCode(targetLocation.getCode());
            stockShelf.setWarehouseCode(shelfDetail.getWarehouseCode());
        }

        ShelfCompleteBO shelfCompleteBO = new ShelfCompleteBO();
        Holder<ReceiptDTO> receiptDTOHolder = new Holder<>();
        // 先收后审 
        boolean receiptFirst = false;
        switch (shelfType) {
            case SHELF_TYPE_RECEIPT: {
                // 收货作业批次
                ReceiptDTO receipt = remoteReceiptClient.queryReceiptByRecId(shelf.getBillNo());
                receiptDTOHolder.set(receipt);
                ContainerDTO container = null;
                ContainerLogDTO containerLogDTO = null;
                if (ObjectUtils.isEmpty(receipt)) {
                    throw new BaseException(ReceiptError.RECEIPT_DATA_ERROR, shelf.getBillNo());
                }
                if (ReceiptStatusEnum.COMPLETE_SHELF.getCode().equals(receipt.getStatus())) {
                    throw new BaseException(ReceiptError.RECEIPT_COMPLETE, shelf.getBillNo());
                }
                if (!AsnTypeEnum.RETURN.getCode().equalsIgnoreCase(receipt.getType())) {
                    AsnParam asnParam = new AsnParam();
                    asnParam.setAsnId(receipt.getAsnId());
                    AsnDTO asnDTO = remoteAsnClient.get(asnParam);
                    if (null == asnDTO) throw ExceptionUtil.DATA_ERROR;
                    if (AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.RECEIVE_BEFORE_REVIEW)) {
                        receiptFirst = true;
                    }
                }

                //关仓协同,判定是否能上架
                if (!receiptFirst) {
                    checkEnableShelf(receipt);
                }

                if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelf.getStatus())) {
                    // 解绑容器
                    container = remoteContainerClient.queryByCode(receipt.getContCode());
                    if (StringUtils.isEmpty(container)) {
                        throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                    }
                    if (!receipt.getAsnId().equalsIgnoreCase(container.getOccupyNo())
                            && !receipt.getTallyCode().equalsIgnoreCase(container.getOccupyNo())
                            && !receipt.getRecId().equalsIgnoreCase(container.getOccupyNo())) {
                        throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                    }
                    if (!ContainerStatusEnum.OCCUPY.getValue().equals(container.getStatus())) {
                        throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                    }
                    // 释放容器
                    containerLogDTO = new ContainerLogDTO();
                    containerLogDTO.setWarehouseCode(container.getWarehouseCode());
                    containerLogDTO.setContCode(container.getCode());
                    containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setCreatedTime(System.currentTimeMillis());
                    containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setOpContent(String.format("上架%s释放容器:%s", shelf.getCode(), container.getCode()));
                    containerLogDTO.setOpDate(System.currentTimeMillis());
                    containerLogDTO.setOccupyNo(container.getOccupyNo());
                    containerLogDTO.setOccupyType(container.getOccupyType());
                    // 容器
                    container.setStatus(ContainerStatusEnum.ENABLE.getValue());
                    container.setOccupyType("");
                    container.setOccupyNo("");
                    container.setRemark("");

                    // 收货作业批次
                    receipt.setStatus(ReceiptStatusEnum.COMPLETE_SHELF.code());
                    if (receiptFirst) {
                        receipt.setStatus(ReceiptStatusEnum.PRE_SHELF.getCode());
                        receipt.setPreCompleteDate(System.currentTimeMillis());
                    } else {
                        receipt.setCompleteShelfDate(System.currentTimeMillis());
                    }

                    // 业务类型
                    // TODO 2020-01-02 MODIFY by wuxian
                    stockShelf.setShelfType(shelf.getType());

                } else {
                    if (ReceiptStatusEnum.WAIT_SHELF.getCode().equals(receipt.getStatus())) {
                        receipt.setStatus(ReceiptStatusEnum.ON_SHELF.code());
                    }
                }
                shelfCompleteBO.setReceipt(receipt);
                shelfCompleteBO.setContainer(container);
                shelfCompleteBO.setContainerLogDTO(containerLogDTO);
            }
            break;
            case SHELF_TYPE_RETURN: {
                // 归位上架操作
                ReturnOrderParam returnOrderParam = new ReturnOrderParam();
                returnOrderParam.setRetOrderCode(shelf.getBillNo());
                ReturnOrderDTO returnOrder = remoteReturnOrderClient.get(returnOrderParam);
                if (ObjectUtils.isEmpty(returnOrder)) {
                    throw new BaseException(BaseBizEnum.TIP, "归位单不存在！");
                }
                if (ReturnOrderEnum.RETURN_ORDER_COMPLETED.getStatus().equals(returnOrder.getStatus())) {
                    throw new BaseException(BaseBizEnum.TIP,
                            String.format("归位单:%s,已经完成！", returnOrder.getRetOrderCode()));
                }
                List<ReturnOrderDetailDTO> modifyReturnOrderDetailList = new ArrayList<>();

                Optional<ReturnOrderDetailDTO> returnOrderDetailDTOOptional = returnOrder.getListDetail().stream()
                        .filter(a -> a.getStatus().equals(ReturnOrderEnum.RETURN_ORDER_DOING.getStatus()))
                        .filter(a -> shelf.getContCode().equals(a.getContCode()))
                        .filter(a -> a.getSkuCode().equals(shelfDetail.getSkuCode()))
                        .filter(a -> a.getSkuLotNo().equals(shelfDetail.getSkuLotNo()))
                        .filter(a -> a.getSkuQuality().equals(shelf.getSkuQuality()))
                        .filter(a -> String.valueOf(a.getId()).equalsIgnoreCase(shelfDetail.getUid()))
                        .min(Comparator.comparing(ReturnOrderDetailDTO::getId));
                BigDecimal shelfSkuQty =
                        shelfDetailList.stream().filter(a -> a.getSkuCode().equals(shelfDetail.getSkuCode()))
                                .filter(a -> a.getSkuLotNo().equals(shelfDetail.getSkuLotNo()))
                                .filter(a -> a.getSkuQuality().equals(shelfDetail.getSkuQuality()))
                                .filter(a -> a.getStatus().equals(ShelfStatusEnum.STATUS_COMPLETED.getStatus()))
                                .filter(a -> a.getUid().equals(shelfDetail.getUid()))
                                .map(ShelfDetailDTO::getShelfSkuQty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                returnOrderDetailDTOOptional.ifPresent(returnOrderDetail -> {
                    if (shelfSkuQty.compareTo(returnOrderDetail.getExpReturnQty()) >= 0) {
                        returnOrderDetail.setCompleteOnShelfQty(returnOrderDetail.getExpReturnQty());
                        returnOrderDetail.setStatus(ReturnOrderEnum.RETURN_ORDER_COMPLETED.getStatus());
                    } else {
                        returnOrderDetail.setCompleteOnShelfQty(shelfSkuQty);
                    }
                    modifyReturnOrderDetailList.add(returnOrderDetail);
                });

                // 归位上架明细
                ReturnOrderDetailDTO returnOrderDetail = returnOrder.getListDetail().stream()
                        .filter(a -> a.getId().toString().equals(shelfDetail.getUid())).findAny().get();
                // returnOrderDetail.setCompleteOnShelfQty(returnOrderDetail.getCompleteOnShelfQty().add(param.getShelfSkuQty()));

                // 作业批次明细变动
                if (saveOrUpdateList.size() == 1
                        && ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelfDetail.getStatus())) {
                    returnOrderDetail.setStatus(ReturnOrderEnum.RETURN_ORDER_COMPLETED.getStatus());
                }

                ContainerDTO container = null;
                ContainerLogDTO containerLogDTO = null;
                // 是否完成归位单
                if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelf.getStatus())) {
                    // 解绑容器
                    container = remoteContainerClient.queryByCode(shelf.getContCode());
                    if (StringUtils.isEmpty(container)) {
                        throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                    }
                    if (!ContainerStatusEnum.OCCUPY.getValue().equals(container.getStatus())) {
                        throw new BaseException(ReceiptError.RECEIPT_CONTAINER_ERROR);
                    }
                    // 释放容器
                    containerLogDTO = new ContainerLogDTO();
                    containerLogDTO.setWarehouseCode(container.getWarehouseCode());
                    containerLogDTO.setContCode(container.getCode());
                    containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setCreatedTime(System.currentTimeMillis());
                    containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setOpContent(String.format("上架%s释放容器:%s", shelf.getCode(), container.getCode()));
                    containerLogDTO.setOpDate(System.currentTimeMillis());
                    containerLogDTO.setOccupyNo(container.getOccupyNo());
                    containerLogDTO.setOccupyType(container.getOccupyType());
                    // 容器
                    container.setStatus(ContainerStatusEnum.ENABLE.getValue());
                    container.setOccupyType("");
                    container.setOccupyNo("");
                    container.setRemark("");

                    // 收货作业批次
                    returnOrder.setCompleteOnShelfQty(returnOrder.getCompleteOnShelfQty().add(param.getShelfSkuQty()));

                } else {
                    if (ReturnOrderEnum.RETURN_ORDER_CREATE.getStatus().equals(returnOrder.getStatus())) {
                        returnOrder.setStatus(ReturnOrderEnum.RETURN_ORDER_DOING.getStatus());
                    }
                    returnOrder.setCompleteOnShelfQty(returnOrder.getCompleteOnShelfQty().add(param.getShelfSkuQty()));
                }

                // 业务类型
                // TODO 2020-01-02 MODIFY by wuxian
                stockShelf.setShelfType(shelf.getType());

                // 事务数据组装
                shelfCompleteBO.setReturnOrder(returnOrder);
                shelfCompleteBO.setReturnOrderDetailList(modifyReturnOrderDetailList);
                shelfCompleteBO.setContainer(container);
                shelfCompleteBO.setContainerLogDTO(containerLogDTO);

                // 归位上架明细对应的归位单明细
                ReturnOrderDetailDTO returnOrderDetailDTO = returnOrder.getListDetail().stream()
                        .filter(it -> it.getId().toString().equals(shelfDetail.getUid()))
                        .findFirst().orElseThrow(() -> new BaseException(BaseBizEnum.TIP, "归位上架明细对应归位明细未找到"));
                if (StrUtil.isBlank(returnOrderDetailDTO.getShipmentOrderCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "归位单明细出库单编号为空");
                }
            }
            break;
            case SHELF_TYPE_SPLIT:
                if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(shelf.getStatus())) {
                    freeContainerByShelfCode(shelfCompleteBO, shelf.getCode(), shelf.getContCode());
                    PrePackagePlanDTO prePackagePlanDTO = completePrePlan(shelf.getBillNo(), shelf.getCode());
                    if (null != prePackagePlanDTO) {
                        shelfCompleteBO.setPrePackagePlanDTO(prePackagePlanDTO);
                    }
                }
                break;
            case SHELF_TYPE_SALE_RETURN:
            case SHELF_TYPE_BOM_ASSEMBLE:
            case SHELF_TYPE_BOM_DISASSEMBLE:
                break;
            case SHELF_TYPE_SHELF_OFF:
            case SHELF_TYPE_PICK:
            case SHELF_TYPE_DIFF:
            default:
                throw new BaseException(BaseBizEnum.TIP, "上架单类型不正确！");
        }

        // 校验库位类型
        checkSkuShelfLocationType(ListUtil.toList(shelfDetail), shelf, receiptDTOHolder);

        ShelfDetailBO shelfDetailBO = ConverterUtil.convert(shelfDetail, ShelfDetailBO.class);
        shelfDetailBO.setTradeDate(shelf.getCreatedTime());
        shelfDetailBO.setOriginZoneCode(originZone.getCode());
        shelfDetailBO.setOriginZoneType(originZone.getType());
        shelfDetailBO.setOriginLocationCode(originLocation.getCode());
        shelfDetailBO.setTargetZoneCode(targetZone.getCode());
        shelfDetailBO.setTargetZoneType(targetZone.getType());
        shelfDetailBO.setTargetLocationCode(targetLocation.getCode());
        shelfDetailBO.setTargetLocationType(targetLocation.getType());
        shelfDetailBO.setTargetLocationUseMode(targetLocation.getUseMode());
        shelfDetailBO.setWarehouseCode(shelfDetail.getWarehouseCode());
        // TODO 2020-01-02 MODIFY by wuxian
        shelfDetailBO.setShelfType(shelf.getType());

        // 先收后审设置上架单状态
        if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equalsIgnoreCase(shelf.getStatus()) && receiptFirst) {
            shelf.setStatus(ShelfStatusEnum.STATUS_PREPARE.getStatus());
            shelf.setPreCompleteDate(System.currentTimeMillis());
            shelf.setCompleteDate(null);
        }
        for (ShelfDetailDTO shelfDetailDTO : saveOrUpdateList) {
            if (receiptFirst && ShelfStatusEnum.STATUS_COMPLETED.getStatus().equalsIgnoreCase(shelfDetailDTO.getStatus())) {
                shelfDetailDTO.setStatus(ShelfStatusEnum.STATUS_PREPARE.getStatus());
            }
        }
        shelfCompleteBO.setShelf(shelf);
        shelfCompleteBO.setDetailList(saveOrUpdateList);

        // stock
        Boolean result = shelfContextService.completeShelf(shelfCompleteBO);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        if (ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType().equalsIgnoreCase(shelf.getType())) {
            ReceiptDTO receiptDTO = remoteReceiptClient.queryReceiptAndDetailByRecId(shelf.getBillNo());
            if (receiptDTO != null) {
                AsnLogDTO asnLogDTO = new AsnLogDTO();
                asnLogDTO.setWarehouseCode(shelf.getWarehouseCode());
                asnLogDTO.setCargoCode(shelf.getCargoCode());
                asnLogDTO.setAsnId(receiptDTO.getAsnId());
                asnLogDTO.setOpBy(CurrentUserHolder.getUserName());
                asnLogDTO.setMsg(String.format("到货通知单%s相关收货作业批次%s PAD上架操作:上架单号%s", receiptDTO.getAsnId(),
                        shelf.getBillNo(), shelf.getCode()));
                remoteAsnClient.saveAsnLog(asnLogDTO);
            }
        }
        return Result.success(true);
    }

    private void checkNewGoods(ShelfDTO shelfDTO, List<ShelfDetailDTO> shelfDetailDTOList) {
        if (CollectionUtil.isEmpty(shelfDetailDTOList)) return;
        if (ShelfTypeEnum.SHELF_TYPE_SALE_RETURN.getType().equalsIgnoreCase(shelfDTO.getType())) return;
        SkuParam param = new SkuParam();
        param.setCargoCode(shelfDetailDTOList.get(0).getCargoCode());
        param.setCodeList(shelfDetailDTOList.stream().map(ShelfDetailDTO::getSkuCode).distinct().collect(Collectors.toList()));
        List<SkuDTO> list = remoteSkuClient.getList(param);
        if (CollectionUtil.isEmpty(list)) return;
        for (SkuDTO skuDTO : list) {
            if (SkuNewOrOldCtrlEnum.SKU_NEW_CTRL_YES.getCode().equals(skuDTO.getIsNewRecord())) {
                throw ExceptionUtil.exceptionWithMessage("新品未维护，不允许上架");
            }
        }
    }

    /**
     * @param receipt
     * @return void
     * @author: WuXian
     * description:  关仓协同,判定是否能上架
     * create time: 2022/3/29 18:04
     */
    private void checkEnableShelf(ReceiptDTO receipt) {
        //2022-03-29 关仓协同 限制上架 清关完成 只考虑 入库单类型为采购，调拨，其他
        List<String> asnTypeList = Arrays.asList(AsnTypeEnum.PURCHASE.getCode(), AsnTypeEnum.TRANSFER.getCode(), AsnTypeEnum.OTHERS.getCode(), AsnTypeEnum.PURCHASE_SUPERVISE.getCode(), AsnTypeEnum.PURCHASE_REDEEM.getCode());
        WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(CurrentRouteHolder.getWarehouseCode());
        if (warehouseDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "仓库信息查询失败");
        }
        if (Objects.equals(warehouseDTO.getType(), TaxTypeEnum.TYPE_BONDED_TAX.getCode())) {
            //是否试点仓
            Boolean isDeclarationWarehouse = false;
//            List<String> declarationWarehouseCodeList = defaultWarehouseCodeConfig.getDeclarationWarehouseCodeList();
//            if (!CollectionUtils.isEmpty(declarationWarehouseCodeList)
//                    && (declarationWarehouseCodeList.contains("--") || declarationWarehouseCodeList.contains(receipt.getWarehouseCode()))) {
//                isDeclarationWarehouse = true;
//            }
            if (warehouseDTO != null && Objects.equals(warehouseDTO.getType(), TaxTypeEnum.TYPE_BONDED_TAX.getCode())) {
                isDeclarationWarehouse = true;
            }

            if (asnTypeList.contains(receipt.getType())
                    && isDeclarationWarehouse
                    && warehouseDTO.getType().equals(TaxTypeEnum.TYPE_BONDED_TAX.getCode())) {
                AsnDTO asnDTO = remoteAsnClient.queryOneByAsnId(receipt.getAsnId());
                if (asnDTO == null) {
                    throw new BaseException(BaseBizEnum.TIP, "入库单异常");
                }
                if (!asnDTO.getCustomsClearanceStatus().equalsIgnoreCase(CustomsClearanceStatusEnum.COMPLETE.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "入库单未完成清关,不允许上架");
                }
            }
        } else {
            if (asnTypeList.contains(receipt.getType())) {
                AsnDTO asnDTO = remoteAsnClient.queryOneByAsnId(receipt.getAsnId());
                if (asnDTO == null) {
                    throw new BaseException(BaseBizEnum.TIP, "入库单异常");
                }
                if (AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.NO_BONDED) && !asnDTO.getCustomsClearanceStatus().equalsIgnoreCase(CustomsClearanceStatusEnum.COMPLETE.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "【非保】入库单未完成清关,不允许上架");
                }
            }
        }
    }

    @Override
    public void BatchShelfMore(String warehouseCode) throws Exception {
        RLock lock = redissonClient.getLock("dt_wms_pick_complete_lock:" + warehouseCode);
//        System.out.println(warehouseCode);
        try {
            boolean tryLock = lock.tryLock(5, 30, TimeUnit.SECONDS);
            if (!tryLock) {
                return;
            }
            RpcContextUtil.setWarehouseCode(warehouseCode);
            ShelfParam bizParam1 = new ShelfParam();
            bizParam1.setStatus(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
            List<ShelfDTO> shelfDTOS = remoteShelfClient.getList(bizParam1);
            if (CollectionUtils.isEmpty(shelfDTOS)) {
                return;
            }
            List<String> tempList = shelfDTOS.stream().map(ShelfDTO::getCode).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(tempList)) {
                return;
            }
            for (String entity : tempList) {
                log.info("BatchShelfMore:{}", entity);
                ShelfParam bizParam = new ShelfParam();
                bizParam.setCode(entity);
                ShelfDTO result = remoteShelfClient.getDetail(bizParam);
                ShelfBizDTO shelfBiz = BeanUtil.copyProperties(result, ShelfBizDTO.class);
                if (!ObjectUtils.isEmpty(shelfBiz)) {
                    List<ShelfDetailBizDTO> detailBizList = shelfBiz.getDetailList().stream()
                            .sorted(
                                    Comparator.comparing(ShelfDetailBizDTO::getSkuCode).thenComparing(ShelfDetailBizDTO::getId))
                            .collect(Collectors.toList());
                    shelfBiz.setDetailList(detailBizList);
                    if (shelfBiz.getDetailList().stream().anyMatch(a -> StringUtils.isEmpty(a.getTargetLocationCode())
                            || a.getShelfSkuQty().compareTo(BigDecimal.ZERO) <= 0)) {
                        continue;
                    }
                    // 组装上架数据
                    ShelfCompleteParam shelfCompleteParam = new ShelfCompleteParam();
                    shelfCompleteParam.setCode(entity);
                    shelfCompleteParam.setOpType(shelfBiz.getOpType());
                    shelfCompleteParam.setDetailList(buildCompleteDetial(shelfBiz));
                    completeWholeShelf(shelfCompleteParam);
                } else {
                    continue;
                }
            }
        } catch (Exception e) {
            log.error("批量上架异常", e);
            String message = "批量上架异常";
            if (!StringUtils.isEmpty(e)) {
                message = Optional.ofNullable(e.getMessage()).orElse("批量上架异常");
            }
            throw new BaseException(BaseBizEnum.TIP, message);
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    @Override
    public Result<Boolean> commitTaoTianShelf(ShelfBizParam param) {
        return Result.success(remoteMercuryClient.commitTaoTianShelf(ConverterUtil.convert(param, ShelfParam.class)));
    }

    @Override
    public Result<Boolean> commitTaoTianShelfCheckSku(ShelfBizParam param) {
        return Result.success(remoteMercuryClient.commitTaoTianShelfCheckSku(ConverterUtil.convert(param, ShelfParam.class)));
    }

    @Override
    public Result<Integer> saleReturnUnShelfCount() {
        if (CollectionUtil.isEmpty(defaultWarehouseCodeConfig.getSaleReturnShelfCheckWarehouseCodeList()))
            return Result.success();

        String format = DateUtil.format(DateTime.now(), "HH:mm");
        for (String warehouse : defaultWarehouseCodeConfig.getSaleReturnShelfCheckWarehouseCodeList()) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(StrUtil.join(StrUtil.EMPTY, format, "点上架任务巡检\n"));
            RpcContextUtil.setWarehouseCode(warehouse);
            ShelfParam param = new ShelfParam();
            param.setStatusList(ListUtil.toList(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus(), ShelfStatusEnum.STATUS_DOING.getStatus(), ShelfStatusEnum.STATUS_PREPARE.getStatus()));
            param.setType(ShelfTypeEnum.SHELF_TYPE_SALE_RETURN.getType());
            List<ShelfDTO> list = remoteShelfClient.getList(param);
            if (CollectionUtil.isNotEmpty(list)) {
                SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
                salesReturnOrderParam.setSalesReturnOrderNoList(list.stream().map(ShelfDTO::getBillNo).distinct().collect(Collectors.toList()));
                List<SalesReturnOrderDTO> salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(salesReturnOrderParam);
                if (CollectionUtil.isEmpty(list)) {
                    list = new ArrayList<>();
                } else {
                    List<String> collect = salesReturnOrderDTOList.stream()
                            .filter(salesReturnOrderDTO -> RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource()))
                            .map(SalesReturnOrderDTO::getSalesReturnOrderNo)
                            .collect(Collectors.toList());
                    list = list.stream()
                            .filter(shelfDTO -> collect.contains(shelfDTO.getBillNo()))
                            .collect(Collectors.toList());
                }
            }


            WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(warehouse);
            stringBuilder.append(String.format("【%s】当天需上架未完成单据%d条，请及时处理\n", warehouseDTO.getName(), list.size()));
            for (int i = 0; i < list.size(); i++) {
                if (i == 50) {
                    stringBuilder.append("更多订单请系统查看\n");
                    break;
                }
                ShelfDTO shelfDTO = list.get(i);
                stringBuilder.append(String.format("上架单号：%s    对应销退单号：%s\n", shelfDTO.getCode(), shelfDTO.getBillNo()));
            }

            WechatUtil.sendMessage(stringBuilder.toString(), defaultWarehouseCodeConfig.getSaleReturnShelfCheckWarningUrl());
        }

        return Result.success();
    }

    /**
     * 组装上架单明细
     *
     * @param shelfBiz
     * @return
     */
    private List<ShelfCompleteDetailBizDTO> buildCompleteDetial(ShelfBizDTO shelfBiz) {
        List<ShelfCompleteDetailBizDTO> shelfCompleteDetailBizDTOS = new ArrayList<>();
        for (ShelfDetailBizDTO entity : shelfBiz.getDetailList()) {
            ShelfCompleteDetailBizDTO shelfCompleteDetailBizDTO =
                    ConverterUtil.convert(entity, ShelfCompleteDetailBizDTO.class);
            shelfCompleteDetailBizDTOS.add(shelfCompleteDetailBizDTO);
        }
        return shelfCompleteDetailBizDTOS;
    }

    private List<ShelfDetailDTO> getSplitShelfDetailList(List<ShelfDetailDTO> dbDetailList,
                                                         List<ShelfCompleteDetailBizDTO> currentDetailList, List<LocationDTO> targetLocationList) {

        List<ShelfDetailDTO> saveOrUpdateList = new ArrayList<>();
        Long dbMaxLineSeq = dbDetailList.stream().mapToLong(a -> Long.parseLong(a.getLineSeq())).max().getAsLong();
        Set<String> skuCodeSkuLotNoSet = dbDetailList.stream().map(
                        a -> StrUtil.join(PretreatmentConstant.SPLIT_TAG, a.getSkuCode(), a.getSkuLotNo(), a.getUid()))
                .collect(Collectors.toSet());

        for (String skuCodeSkuLotNo : skuCodeSkuLotNoSet) {
            String skuCode = skuCodeSkuLotNo.split(PretreatmentConstant.SPLIT_TAG)[0];
            String skuLotNo = skuCodeSkuLotNo.split(PretreatmentConstant.SPLIT_TAG)[1];
            String uid = skuCodeSkuLotNo.split(PretreatmentConstant.SPLIT_TAG)[2];
            ShelfDetailDTO waitShelfDetail = dbDetailList.stream()
                    .filter(a -> a.getSkuCode().equals(skuCode))
                    .filter(a -> a.getSkuLotNo().equals(skuLotNo))
                    .filter(a -> a.getUid().equals(uid))
                    .filter(a -> a.getStatus().equals(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus())).findAny()
                    .orElse(null);
            if (ObjectUtils.isEmpty(waitShelfDetail)) {
                continue;
            }
            List<ShelfCompleteDetailBizDTO> rowShelfDetailList =
                    currentDetailList.stream().filter(a -> !StringUtils.isEmpty(a.getTargetLocationCode()))
                            .filter(a -> !ObjectUtils.isEmpty(a.getShelfSkuQty()))
                            .filter(a -> a.getShelfSkuQty().compareTo(BigDecimal.ZERO) > 0)
                            .filter(a -> a.getSkuCode().equals(skuCode))
                            .filter(a -> a.getSkuLotNo().equals(skuLotNo))
                            .filter(a -> a.getUid().equals(uid))
                            .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(rowShelfDetailList)) {
                continue;
            }

            // 如果原始记录拆成0或者库位没填写，则将原始记录的id赋给新增的明细
            currentDetailList.stream()
                    .filter(a -> ObjectUtil.isNotEmpty(a.getId()))
                    .filter(a -> a.getId().equals(waitShelfDetail.getId()))
                    .findFirst().ifPresent(a -> {
                        if (StrUtil.isBlank(a.getTargetLocationCode()) || ObjectUtil.isEmpty(a.getShelfSkuQty()) || a.getShelfSkuQty().compareTo(BigDecimal.ZERO) <= 0) {
                            rowShelfDetailList.stream()
                                    .filter(it -> ObjectUtil.isEmpty(it.getId()))
                                    .findFirst().ifPresent(it -> it.setId(a.getId()));
                        }
                    });

            // 期望上架数量
            BigDecimal expSkuQty = dbDetailList.stream()
                    .filter(a -> a.getSkuCode().equals(skuCode))
                    .filter(a -> a.getSkuLotNo().equals(skuLotNo))
                    .filter(a -> a.getUid().equals(uid))
                    .map(ShelfDetailDTO::getSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 已上架数量
            BigDecimal shelfSkuQty = rowShelfDetailList.stream()
                    .filter(a -> a.getSkuCode().equals(skuCode))
                    .filter(a -> a.getSkuLotNo().equals(skuLotNo))
                    .filter(a -> a.getUid().equals(uid))
                    .map(ShelfCompleteDetailBizDTO::getShelfSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 未上架数量
            BigDecimal newWaitShelfSkuQty = expSkuQty.subtract(shelfSkuQty);
            if (newWaitShelfSkuQty.compareTo(BigDecimal.ZERO) < 0) {
                throw new BaseException(BaseBizEnum.TIP, "上架商量超出明细待上架总数量");
            }

            // 完成所有可提交数据
            for (ShelfCompleteDetailBizDTO shelfCompleteDetail : rowShelfDetailList) {
                ShelfDetailDTO dbShelfDetail = dbDetailList.stream()
                        .filter(a -> a.getId().equals(shelfCompleteDetail.getId())).findAny().orElse(null);
                // 已上架完成的明细需跳过
                if (!ObjectUtils.isEmpty(dbShelfDetail)
                        && ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(dbShelfDetail.getStatus())) {
                    continue;
                }
                LocationDTO targetLocation = targetLocationList.stream()
                        .filter(b -> b.getCode().equals(shelfCompleteDetail.getTargetLocationCode())).findFirst()
                        .orElseThrow(() -> new BaseException(BaseBizEnum.TIP,
                                String.format("目标库位:%s不存在", shelfCompleteDetail.getTargetLocationCode())));

                ShelfDetailDTO saveOrUpdate;
                if (ObjectUtils.isEmpty(dbShelfDetail)) {
                    dbMaxLineSeq = dbMaxLineSeq + 1;
                    saveOrUpdate = JSON.parseObject(JSON.toJSONString(waitShelfDetail), ShelfDetailDTO.class);
                    saveOrUpdate.setId(null);
                    saveOrUpdate.setVersion(null);
                    saveOrUpdate.setLineSeq(String.valueOf(dbMaxLineSeq));
                } else {
                    dbShelfDetail.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
                    saveOrUpdate = JSON.parseObject(JSON.toJSONString(dbShelfDetail), ShelfDetailDTO.class);
                }
                saveOrUpdate.setSkuQty(shelfCompleteDetail.getShelfSkuQty());
                saveOrUpdate.setShelfSkuQty(shelfCompleteDetail.getShelfSkuQty());
                saveOrUpdate.setTargetZoneCode(targetLocation.getZoneCode());
                saveOrUpdate.setTargetZoneType(targetLocation.getZoneType());
                saveOrUpdate.setTargetLocationType(targetLocation.getType());
                saveOrUpdate.setTargetLocationUseMode(targetLocation.getUseMode());
                saveOrUpdate.setTargetLocationCode(shelfCompleteDetail.getTargetLocationCode());
                saveOrUpdate.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
                saveOrUpdateList.add(saveOrUpdate);
            }

            // 拆分未完成明细
            if (newWaitShelfSkuQty.compareTo(BigDecimal.ZERO) > 0) {
                // 这里状态为已完成表示前面的操作过程中修改了原始数据，要新增明细
                if (ShelfStatusEnum.STATUS_COMPLETED.getStatus().equals(waitShelfDetail.getStatus())) {
                    dbMaxLineSeq = dbMaxLineSeq + 1;
                    ShelfDetailDTO separation = JSON.parseObject(JSON.toJSONString(waitShelfDetail), ShelfDetailDTO.class);
                    separation.setId(null);
                    separation.setVersion(null);
                    separation.setTargetLocationCode(null);
                    separation.setStatus(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
                    separation.setLineSeq(String.valueOf(dbMaxLineSeq));
                    separation.setSkuQty(expSkuQty.subtract(shelfSkuQty));
                    separation.setShelfSkuQty(new BigDecimal("0.000"));
                    saveOrUpdateList.add(separation);
                }
                // 前端传过来的原始明细数量可能为零，或者库位为空, 修改原始数据数量
                else {
                    ShelfDetailDTO saveOrUpdate = JSON.parseObject(JSON.toJSONString(waitShelfDetail), ShelfDetailDTO.class);
                    saveOrUpdate.setSkuQty(newWaitShelfSkuQty);
                    saveOrUpdateList.add(saveOrUpdate);
                }
            }
        }
        return saveOrUpdateList;
    }

    private void checkTargetLocationAndShelfQtyAndLotRule(ShelfDTO shelf,
                                                          List<ShelfCompleteDetailBizDTO> currentDetailList, List<ZoneDTO> targetZoneList,
                                                          List<LocationDTO> targetLocationList, Holder<ReceiptDTO> receiptDTOHolder) {
        // 校验库位类型
        checkSkuShelfLocationType(ConverterUtil.convertList(currentDetailList, ShelfDetailDTO.class), shelf, receiptDTOHolder);

        Set<String> skuCodeSkuLotNoSet = shelf.getDetailList().stream().flatMap(
                        a -> Stream.of(String.format("%s%s%s", a.getSkuCode(), PretreatmentConstant.SPLIT_TAG, a.getSkuLotNo())))
                .collect(Collectors.toSet());

        for (String skuCodeSkuLotNo : skuCodeSkuLotNoSet) {
            String skuCode = skuCodeSkuLotNo.split(PretreatmentConstant.SPLIT_TAG)[0];
            String skuLotNo = skuCodeSkuLotNo.split(PretreatmentConstant.SPLIT_TAG)[1];
            BigDecimal expSkuQty = shelf.getDetailList().stream().filter(a -> a.getSkuCode().equals(skuCode))
                    .filter(a -> a.getSkuLotNo().equals(skuLotNo)).map(ShelfDetailDTO::getSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal shelfSkuQty = currentDetailList.stream().filter(a -> a.getSkuCode().equals(skuCode))
                    .filter(a -> a.getSkuLotNo().equals(skuLotNo)).map(ShelfCompleteDetailBizDTO::getShelfSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (shelfSkuQty.compareTo(expSkuQty) > 0) {
                throw new BaseException(BaseBizEnum.TIP,
                        String.format("商品:%s批次:%s上架数量不能大于上架单明细商品数量", skuCode, skuLotNo));
            }
        }


        // 数据校验
        for (ShelfCompleteDetailBizDTO shelfDetail : currentDetailList) {
            if (shelf.getDetailList().stream()
                    .filter(shelfDetailDTO -> shelfDetailDTO.getId().equals(shelfDetail.getId()))
                    .filter(shelfDetailDTO -> shelfDetailDTO.getStatus().equalsIgnoreCase(ShelfStatusEnum.STATUS_COMPLETED.getStatus()))
                    .anyMatch(shelfDetailDTO -> shelfDetailDTO.getShelfSkuQty().compareTo(shelfDetail.getShelfSkuQty()) == 0)) {
                continue;
            }

            LocationDTO targetLocation = targetLocationList.stream()
                    .filter(b -> b.getCode().equals(shelfDetail.getTargetLocationCode())).findFirst()
                    .orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库位:%s不存在",
                            shelfDetail.getSkuCode(), shelfDetail.getSkuLotNo(), shelfDetail.getTargetLocationCode())));
            ZoneDTO targetZone = targetZoneList.stream().filter(b -> b.getCode().equals(targetLocation.getZoneCode()))
                    .findAny().orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库区:%s不存在",
                            shelfDetail.getSkuCode(), shelfDetail.getSkuLotNo(), targetLocation.getZoneCode())));
            if (!shelf.getSkuQuality().equals(targetZone.getSkuQuality())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库区:%s正残属性不匹配",
                        shelfDetail.getSkuCode(), shelfDetail.getSkuLotNo(), targetLocation.getZoneCode()));
            }

            if (ZoneStatusEnum.STATUS_DISABLED.getStatus().equals(targetZone.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库区:%s未启用", shelfDetail.getSkuCode(),
                        shelfDetail.getSkuLotNo(), targetLocation.getZoneCode()));
            }
            if (LocationStatusEnum.STATUS_DISABLED.getStatus().equals(targetLocation.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库位:%s未启用", shelfDetail.getSkuCode(),
                        shelfDetail.getSkuLotNo(), targetLocation.getCode()));
            }

            // 混放批次
            mixRuleCheckBiz.checkLocationMixRule(targetLocation.getCode(), targetLocation.getMixRuleCode(),
                    shelf.getCargoCode(), shelfDetail.getSkuCode(), shelfDetail.getSkuLotNo());
        }
    }

    /**
     * 预包商品只能上架到预包库位
     *
     * @param shelfDetailDTOList 上架明细
     */
    private void checkSkuShelfLocationType(List<ShelfDetailDTO> shelfDetailDTOList, ShelfDTO shelfDTO, Holder<ReceiptDTO> receiptDTOHolder) {
        if (CollectionUtil.isEmpty(shelfDetailDTOList)) return;
        List<String> skuCodeList = shelfDetailDTOList.stream().map(ShelfDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        List<String> locationCodeList = shelfDetailDTOList.stream().map(ShelfDetailDTO::getTargetLocationCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuCodeList)) throw ExceptionUtil.DATA_ERROR;
        if (CollectionUtil.isEmpty(locationCodeList)) throw ExceptionUtil.DATA_ERROR;

        SkuParam skuParam = new SkuParam();
        skuParam.setCodeList(skuCodeList);
        skuParam.setCargoCode(shelfDTO.getCargoCode());
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtil.isEmpty(skuDTOList)) throw ExceptionUtil.DATA_ERROR;

        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(locationCodeList);
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);

        for (ShelfDetailDTO shelfDetailDTO : shelfDetailDTOList) {
            SkuDTO skuDTO = skuDTOList.stream()
                    .filter(it -> it.getCode().equalsIgnoreCase(shelfDetailDTO.getSkuCode()))
                    .filter(it -> it.getCargoCode().equalsIgnoreCase(shelfDTO.getCargoCode()))
                    .findFirst().orElseThrow(ExceptionUtil::dataError);
            LocationDTO locationDTO = locationDTOList.stream().filter(it -> it.getCode().equalsIgnoreCase(shelfDetailDTO.getTargetLocationCode()))
                    .findFirst().orElseThrow(ExceptionUtil::dataError);

            // 预包商品只能上架到预包区
            if (SkuIsPreEnum.PRE.getCode().equalsIgnoreCase(skuDTO.getIsPre())) {
                if (!LocationTypeEnum.LOCATION_TYPE_PREP.getType().equalsIgnoreCase(locationDTO.getType())) {
                    throw ExceptionUtil.exceptionWithMessage("预包商品必须上架到预包位");
                }
                continue;
            }


            // 金融监管只能上架到金融监管位
            ReceiptDTO receiptDTO = receiptDTOHolder.get();
            if (null != receiptDTO) {
                if (AsnTypeEnum.PURCHASE_SUPERVISE.getCode().equalsIgnoreCase(receiptDTO.getType())) {
                    if (!LocationTypeEnum.LOCATION_TYPE_FINANCE.getType().equalsIgnoreCase(locationDTO.getType())) {
                        throw ExceptionUtil.exceptionWithMessage("代采监管入库必须上架到金融监管位");
                    }
                    continue;
                }
            }

            // 其余情况只能上架到拣选区或者存储区
            if (!LocationTypeEnum.LOCATION_TYPE_PICK.getType().equalsIgnoreCase(locationDTO.getType()) && !LocationTypeEnum.LOCATION_TYPE_STORE.getType().equalsIgnoreCase(locationDTO.getType())) {
                throw ExceptionUtil.exceptionWithMessage("普通商品只能上架到拣选位或者存储位");
            }

        }


    }
}
