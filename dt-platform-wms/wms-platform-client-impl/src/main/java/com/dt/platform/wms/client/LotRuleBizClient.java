package com.dt.platform.wms.client;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.base.*;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.exceptions.WmsBizException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.LotRuleDTO;
import com.dt.domain.base.dto.LotRuleDetailDTO;
import com.dt.domain.base.param.LotRuleParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.dto.base.LotRuleBizDTO;
import com.dt.platform.wms.dto.base.LotRuleDetailBizDTO;
import com.dt.platform.wms.integration.IRemoteLotRuleClient;
import com.dt.platform.wms.integration.IRemoteSeqRuleClient;
import com.dt.platform.wms.param.base.LotRuleBizParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class LotRuleBizClient implements ILotRuleBizClient {

    @Resource
    private IRemoteLotRuleClient remoteLotRuleClient;

    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public Result<List<IdNameVO>> getIdNameList() {
        return Result.success(remoteLotRuleClient.getIdNameList());
    }

    @Override
    public Result<Boolean> create(LotRuleBizParam param) {
        //检验批次规则名称不能重复
//        checkLotNameExists(param.getName());
        param.setCode(remoteSeqRuleClient.findSequence(SeqEnum.LOT_RULE_CODE_000001));
        LotRuleDTO lotRuleDTO = ConverterUtil.convert(param, LotRuleDTO.class);
        if (ObjectUtils.isEmpty(lotRuleDTO)) {
            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        if (StringUtils.isEmpty(lotRuleDTO.getName())) {
            throw new BaseException(BaseBizEnum.TIP, "规则名称不能为空");
        }
        if (CollectionUtils.isEmpty(param.getRuleDetailList())) {
            throw new BaseException(BaseBizEnum.TIP, "明细不能为空");
        }
        List<LotRuleDetailDTO> lotRuleDetailBizDTOS = new ArrayList<>();
        for (LotRuleDetailBizDTO entity : param.getRuleDetailList()) {
            LotRulePropKeyEnum lotRulePropKeyEnum = LotRulePropKeyEnum.getEnum(entity.getPropKey());
            LotRuleDetailDTO detailBizDTO = ConverterUtil.convert(entity, LotRuleDetailDTO.class);
            detailBizDTO.setRuleCode(lotRuleDTO.getCode());
            detailBizDTO.setRuleName(param.getName());
            detailBizDTO.setSource("");
            detailBizDTO.setPropValue("");
            detailBizDTO.setDefaultValue("");
            switch (lotRulePropKeyEnum) {
                case PROP_KEY_EXPIRE_DATE:
                case PROP_KEY_MANUF_DATE:
                case PROP_KEY_RECEIVE_DATE:
                    if (LotRuleDateFormatEnum.matchOpCode(detailBizDTO.getTypeFormat()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应的日期格式");
                    }
                    if (LotRuleRwControlEnum.matchOpCode(detailBizDTO.getEnableControl()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应输入控制");
                    }
                    if (LotRuleShowEnum.matchOpCode(detailBizDTO.getShowType()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应展示类型");
                    }
                    if (LotRuleTypeEnum.matchOpCode(detailBizDTO.getType()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应类型");
                    }
                    detailBizDTO.setPropName(lotRulePropKeyEnum.getPropName());
                    lotRuleDetailBizDTOS.add(detailBizDTO);
                    break;
                case PROP_KEY_PRODUCT_NO:
                case PROP_KEY_PALLETCODE_TYPE:
                case PROP_KEY_BOXCODE_TYPE:
                case PROP_KEY_VALIDITY_CODE:
                    if (LotRuleRwControlEnum.matchOpCode(detailBizDTO.getEnableControl()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应输入控制");
                    }
                    if (LotRuleShowEnum.matchOpCode(detailBizDTO.getShowType()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应展示类型");
                    }
                    if (LotRuleTypeEnum.matchOpCode(detailBizDTO.getType()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应类型");
                    }
                    detailBizDTO.setPropName(lotRulePropKeyEnum.getPropName());
                    lotRuleDetailBizDTOS.add(detailBizDTO);
                    break;
                case PROP_KEY_SKU_QUALITY:
                case PROP_KEY_EXTERNAL_LOT:
                case PROP_KEY_INVENTORY_TYPE:
                case PROP_KEY_EXTERNAL_LINK_BILL_NO:
                    if (LotRuleShowEnum.matchOpCode(detailBizDTO.getShowType()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应展示类型");
                    }
                    if (LotRuleRwControlEnum.matchOpCode(detailBizDTO.getEnableControl()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应输入控制");
                    }
                    if (LotRuleTypeEnum.matchOpCode(detailBizDTO.getType()) == null) {
                        throw new BaseException(BaseBizEnum.TIP, "未找到对应类型");
                    }
                    detailBizDTO.setPropName(lotRulePropKeyEnum.getPropName());
                    lotRuleDetailBizDTOS.add(detailBizDTO);
                    break;
                default:
                    break;
            }
        }
        lotRuleDTO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        lotRuleDetailBizDTOS.forEach(a -> a.setWarehouseCode(CurrentRouteHolder.getWarehouseCode()));
        lotRuleDTO.setRuleDetailList(lotRuleDetailBizDTOS);
        //判定是否有规则-无规则第一条新增为默认
        LotRuleParam lotRuleParam = new LotRuleParam();
        List<LotRuleDTO> clientList = remoteLotRuleClient.getList(lotRuleParam);
        if (CollectionUtils.isEmpty(clientList)) {
            lotRuleDTO.setIsDefault(LotRuleDefaultEnum.YES.getCode());
        }
        Boolean result = remoteLotRuleClient.saveDTO(lotRuleDTO);
        return Result.success(result);
    }

    /**
     * 查询规则名是否存在
     *
     * @param name
     */
    private void checkLotNameExists(String name) {
        LotRuleParam lotRuleParam = new LotRuleParam();
        lotRuleParam.setNameExists(name);
        List<LotRuleDTO> lotRuleDTOS = remoteLotRuleClient.getList(lotRuleParam);
        if (!CollectionUtils.isEmpty(lotRuleDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, "规则名称不能重复!!!");
        }
    }

    @Override
    public Result<Boolean> modify(LotRuleBizParam param) {
        LotRuleParam bizParam = ConverterUtil.convert(param, LotRuleParam.class);
        if (ObjectUtils.isEmpty(bizParam)) {
            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        bizParam.setRuleDetailList(ConverterUtil.convertList(param.getRuleDetailList(), LotRuleDetailDTO.class));
        Boolean result = remoteLotRuleClient.modify(bizParam);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> enable(LotRuleBizParam param) {
        LotRuleParam bizParam = ConverterUtil.convert(param, LotRuleParam.class);
        Boolean result = remoteLotRuleClient.modify(bizParam);
        return Result.success(result);
    }

    @Override
    public Result<LotRuleBizDTO> getDetail(LotRuleBizParam param) {
        LotRuleParam bizParam = ConverterUtil.convert(param, LotRuleParam.class);
        if (ObjectUtils.isEmpty(bizParam)) {
            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LotRuleDTO result = remoteLotRuleClient.get(bizParam);
        LotRuleBizDTO bizDto = ConverterUtil.convert(result, LotRuleBizDTO.class);
        if (!ObjectUtils.isEmpty(bizDto)) {
            bizDto.setRuleDetailList(ConverterUtil.convertList(result.getRuleDetailList(), LotRuleDetailBizDTO.class));
        }
        return Result.success(bizDto);
    }

    @Override
    public Result<Boolean> checkUseLotRuleName(Long id, String lotRuleName) {
        return Result.success(remoteLotRuleClient.checkUseLotRuleName(id, lotRuleName));
    }

    @Override
    public void initSkuLotAddRule(String param, String warehouseCode) {
        //设置数据源
        RpcContextUtil.setWarehouseCode(warehouseCode);
        RLock lock = redissonClient.getLock("dt_wms_init_sku_lot_add_lock:" + warehouseCode);
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(1, 60, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "稍后重试");
            }
            //param 是填充的批次规则的 propKey
            if (StringUtils.isEmpty(param)) {
                return;
            }
            LotRulePropKeyEnum lotRulePropKeyEnum = LotRulePropKeyEnum.getEnum(param);
            if (lotRulePropKeyEnum == null) {
                return;
            }
            //获取所有批次规则
            LotRuleParam lotRuleParam = new LotRuleParam();
            List<LotRuleDTO> lotRuleDTOList = remoteLotRuleClient.getList(lotRuleParam);
            if (CollectionUtils.isEmpty(lotRuleDTOList)) {
                return;
            }
            //获取所有批次明细
            List<LotRuleDetailDTO> saveLotRuleDetailDTOList = new ArrayList<>();
            lotRuleDTOList.forEach(it -> {
                List<LotRuleDetailDTO> ruleDetailList = it.getRuleDetailList();
                if (ruleDetailList.stream().noneMatch(a -> Objects.equals(a.getPropKey(), lotRulePropKeyEnum.getPropKey()))) {
                    //目前只处理后加的暗码 TODO 2025-04-23
                    if (Objects.equals(lotRulePropKeyEnum.getPropKey(), LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropKey())) {
                        LotRuleDetailDTO lotRuleDetailDTO = new LotRuleDetailDTO();
                        lotRuleDetailDTO.setRuleCode(it.getCode());
                        lotRuleDetailDTO.setWarehouseCode(it.getWarehouseCode());
                        lotRuleDetailDTO.setRuleName(it.getName());
                        lotRuleDetailDTO.setPropName(lotRulePropKeyEnum.getPropName());
                        lotRuleDetailDTO.setPropKey(lotRulePropKeyEnum.getPropKey());
                        lotRuleDetailDTO.setEnableControl(LotRuleRwControlEnum.DISABLE.getCode());
                        lotRuleDetailDTO.setType(LotRuleTypeEnum.STRING.getCode());
                        lotRuleDetailDTO.setTypeFormat("");
                        lotRuleDetailDTO.setStatus(LotRuleStatusEnum.ENABLE.getStatus());
                        lotRuleDetailDTO.setShowType(LotRuleShowEnum.INPUT.getCode());
                        lotRuleDetailDTO.setRemark("add time:"+ DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
                        saveLotRuleDetailDTOList.add(lotRuleDetailDTO);
                    }
                }
            });
            if(!CollectionUtils.isEmpty(saveLotRuleDetailDTOList)){
                remoteLotRuleClient.saveBatchDetail(saveLotRuleDetailDTOList);
            }

        } catch (Exception e) {
            log.error("批次规则增加异常：e:{}", e.getMessage());
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
    }

    @Override
    public Result<List<LotRuleBizDTO>> getList(LotRuleBizParam param) {
        LotRuleParam bizParam = ConverterUtil.convert(param, LotRuleParam.class);
        if (ObjectUtils.isEmpty(bizParam)) {
            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<LotRuleDTO> resultList = remoteLotRuleClient.getList(bizParam);

        List<LotRuleBizDTO> resultBizList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultList)) {
            resultBizList = resultList.stream().flatMap(a -> {
                LotRuleBizDTO lotRuleBiz = ConverterUtil.convert(a, LotRuleBizDTO.class);
                if (!ObjectUtils.isEmpty(lotRuleBiz)) {
                    lotRuleBiz.setRuleDetailList(ConverterUtil.convertList(a.getRuleDetailList(), LotRuleDetailBizDTO.class));
                }
                return Stream.of(lotRuleBiz);
            }).collect(Collectors.toList());
        }
        return Result.success(resultBizList);
    }

    @Override
    public Result<Page<LotRuleBizDTO>> getPage(LotRuleBizParam param) {
        LotRuleParam bizParam = ConverterUtil.convert(param, LotRuleParam.class);
        if (ObjectUtils.isEmpty(bizParam)) {
            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Page<LotRuleDTO> page = remoteLotRuleClient.getPage(bizParam);

        List<LotRuleDTO> resultList = page.getRecords();
        List<LotRuleBizDTO> resultBizList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultList)) {
            resultBizList = resultList.stream().flatMap(a -> {
                LotRuleBizDTO lotRuleBiz = ConverterUtil.convert(a, LotRuleBizDTO.class);
                if (!ObjectUtils.isEmpty(lotRuleBiz)) {
                    lotRuleBiz.setRuleDetailList(ConverterUtil.convertList(a.getRuleDetailList(), LotRuleDetailBizDTO.class));
                }
                return Stream.of(lotRuleBiz);
            }).collect(Collectors.toList());
        }

        Page<LotRuleBizDTO> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(resultBizList);

        return Result.success(result);
    }
}
