package com.dt.platform.wms.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.AuditEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.base.*;
import com.dt.component.common.enums.bill.AdjustTagEnum;
import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.enums.cargo.CargoConfigStatusEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.message.LargeMessageOperationTypeEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuLifeCtrlEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.transfer.*;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.log.TransferLogDTO;
import com.dt.domain.base.param.*;
import com.dt.domain.bill.bo.TransferPersistBO;
import com.dt.domain.bill.dto.TransferDTO;
import com.dt.domain.bill.dto.TransferDetailDTO;
import com.dt.domain.bill.param.TransferDetailParam;
import com.dt.domain.bill.param.TransferParam;
import com.dt.domain.core.stock.dto.StockDTO;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.domain.core.stock.param.StockParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.ICargoConfigBiz;
import com.dt.platform.wms.biz.ILocationBiz;
import com.dt.platform.wms.biz.IMixRuleCheckBiz;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.param.SkuLotCheckAndFormatParam;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.log.IRemoteTransferLogClient;
import com.dt.platform.wms.integration.message.IRemoteMessageMqClient;
import com.dt.platform.wms.param.rec.CheckSkuLotParam;
import com.dt.platform.wms.param.transfer.*;
import com.dt.platform.wms.transaction.ITransferGtsService;
import com.dt.platform.wms.transaction.bo.TransferBO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by nobody on 2020/12/28 17:43
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class TransferBizClient implements ITransferBizClient {

    static final String withdrawAutoTransferLockKey = "withdrawAutoTransfer";

    @Resource
    private IRemoteLockSupportClient remoteLockSupportClient;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Autowired
    private IRemoteMessageMqClient remoteMessageMqClient;

    @Autowired
    private IRemoteTransferClient remoteTransferClient;

    @Autowired
    private IRemoteLocationClient remoteLocationClient;

    @Autowired
    private IRemoteZoneClient remoteZoneClient;

    @Autowired
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Autowired
    private IRemoteStockClient remoteStockClient;

    @Autowired
    private ISkuLotBiz skulotBizClient;

    @Autowired
    private ITransferGtsService transferGtsService;

    @Autowired
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Autowired
    private IRemoteSkuClient remoteSkuClient;

    @Autowired
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Autowired
    private IRemoteCargoConfigClient remoteCargoConfigClient;

    @Autowired
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Autowired
    private IMixRuleCheckBiz mixRuleCheckBiz;

    @Resource
    private IRemoteTransferLogClient remoteTransferLogClient;

    @Resource
    private ICargoConfigBiz cargoConfigBiz;

    @Resource
    private ILocationBiz locationBiz;

    @Resource
    DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    private WmsOtherConfig wmsOtherConfig;

    @Resource
    private IRemoteLotRuleClient remoteLotRuleClient;


    private static final Map<InventoryTypeEnum, InventoryTypeEnum> adjustMapping = new HashMap<>();

    static {
        adjustMapping.put(InventoryTypeEnum.ZP, InventoryTypeEnum.XQC);
        adjustMapping.put(InventoryTypeEnum.ZCZP, InventoryTypeEnum.XQC);
//        adjustMapping.put(InventoryTypeEnum.XTZP, InventoryTypeEnum.XQC);
        adjustMapping.put(InventoryTypeEnum.ZT, InventoryTypeEnum.XQC);
        adjustMapping.put(InventoryTypeEnum.BLC, InventoryTypeEnum.CC);
        adjustMapping.put(InventoryTypeEnum.CZC, InventoryTypeEnum.CC);
        adjustMapping.put(InventoryTypeEnum.ZCC, InventoryTypeEnum.CC);
//        adjustMapping.put(InventoryTypeEnum.FP, InventoryTypeEnum.CC);
        adjustMapping.put(InventoryTypeEnum.LQC, InventoryTypeEnum.CC);
        adjustMapping.put(InventoryTypeEnum.JS, InventoryTypeEnum.CC);
        adjustMapping.put(InventoryTypeEnum.XS, InventoryTypeEnum.CC);
    }

    @Override
    public Result<String> addTransfer(TransferAddBizParam param) {
        check(param);
        TransferParam transferParam = ConverterUtil.convert(param, TransferParam.class);
        transferParam.setStatus(TransferStatusEnum.CREATED.getCode());
        transferParam.setCode(remoteSeqRuleClient.findSequence(SeqEnum.TRANSFER_CODE_000001));
        //调整字节信息校验
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList()) && defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList().contains(CurrentRouteHolder.getWarehouseCode())) {
            if (StringUtils.isEmpty(param.getBusinessType())) {
                throw new BaseException(BaseBizEnum.TIP, "当前仓库,业务场景不能为空");
            }
        }
        remoteTransferClient.add(transferParam);
        // 添加日志
        TransferLogDTO logDTO = new TransferLogDTO();
        logDTO.setTransferCode(transferParam.getCode());
        logDTO.setCargoCode(transferParam.getCargoCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        logDTO.setOpContent("创建转移单:" + transferParam.getCode());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_INSERT.getType());
        remoteTransferLogClient.save(logDTO);
        return Result.success(transferParam.getCode());
    }

    @Override
    public Result<Page<TransferDTO>> page(TransferParam param) {
        if (param != null && !CollectionUtils.isEmpty(param.getOrderTagList())) {
            param.setOrderTag(TransferTagEnum.queryParamListToInteger(param.getOrderTagList()));
        }
        // 来源商品条码
        if (CollectionUtil.isNotEmpty(param.getOriginUpcCodeList())) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCodeList(param.getOriginUpcCodeList());
            List<SkuUpcDTO> skuUpcDTOList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            List<String> skuCodeList =
                    skuUpcDTOList.stream().map(SkuUpcDTO::getSkuCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(skuCodeList)) {
                return Result.success(new Page<>());
            }
            if (CollectionUtil.isEmpty(param.getOriginSkuCodeList())) {
                param.setOriginSkuCodeList(skuCodeList);
            } else {
                param.getOriginSkuCodeList().retainAll(skuCodeList);
            }
        }
        // 来源商品编码
        if (CollectionUtil.isNotEmpty(param.getOriginSkuCodeList())) {
            TransferDetailParam transferDetailParam = new TransferDetailParam();
            transferDetailParam.setOriginSkuCodeList(param.getOriginSkuCodeList());
            List<TransferDetailDTO> transferDetailDTOList = remoteTransferClient.listDetail(transferDetailParam);
            List<String> transferCodeList = transferDetailDTOList.stream().map(TransferDetailDTO::getTransferCode)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(transferCodeList)) {
                return Result.success(new Page<>());
            }
            if (CollectionUtil.isNotEmpty(param.getCodeList())) {
                param.getCodeList().retainAll(transferCodeList);
            } else {
                param.setCodeList(transferCodeList);
            }
        }
        // 目标商品条码
        if (CollectionUtil.isNotEmpty(param.getTargetUpcCodeList())) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCodeList(param.getTargetUpcCodeList());
            List<SkuUpcDTO> skuUpcDTOList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            List<String> skuCodeList =
                    skuUpcDTOList.stream().map(SkuUpcDTO::getSkuCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(skuCodeList)) {
                return Result.success(new Page<>());
            }
            if (CollectionUtil.isEmpty(param.getTargetSkuCodeList())) {
                param.setTargetSkuCodeList(skuCodeList);
            } else {
                param.getTargetSkuCodeList().retainAll(skuCodeList);
            }
        }
        // 目标商品编码
        if (CollectionUtil.isNotEmpty(param.getTargetSkuCodeList())) {
            TransferDetailParam transferDetailParam = new TransferDetailParam();
            transferDetailParam.setTargetSkuCodeList(param.getTargetSkuCodeList());
            List<TransferDetailDTO> transferDetailDTOList = remoteTransferClient.listDetail(transferDetailParam);
            List<String> transferCodeList = transferDetailDTOList.stream().map(TransferDetailDTO::getTransferCode)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(transferCodeList)) {
                return Result.success(new Page<>());
            }
            if (CollectionUtil.isNotEmpty(param.getCodeList())) {
                param.getCodeList().retainAll(transferCodeList);
            } else {
                param.setCodeList(transferCodeList);
            }
        }
        // 来源批次ID
        if (StrUtil.isNotBlank(param.getOriginSkuLotNo())) {
            TransferDetailParam transferDetailParam = new TransferDetailParam();
            transferDetailParam.setOriginSkuLotNo(param.getOriginSkuLotNo());
            List<TransferDetailDTO> transferDetailDTOList = remoteTransferClient.listDetail(transferDetailParam);
            List<String> transferCodeList = transferDetailDTOList.stream().map(TransferDetailDTO::getTransferCode)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(transferCodeList)) {
                return Result.success(new Page<>());
            }
            if (CollectionUtil.isNotEmpty(param.getCodeList())) {
                param.getCodeList().retainAll(transferCodeList);
            } else {
                param.setCodeList(transferCodeList);
            }
        }
        // 来源库区
        if (StrUtil.isNotBlank(param.getOriginZoneCode())) {
            LocationParam locationParam = new LocationParam();
            locationParam.setZoneCode(param.getOriginZoneCode());
            List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
            List<String> locationCodeList =
                    locationDTOList.stream().map(LocationDTO::getCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(locationCodeList)) {
                return Result.success(new Page<>());
            }
            if (CollectionUtil.isEmpty(param.getOriginLocationCodeList())) {
                param.setOriginLocationCodeList(locationCodeList);
            } else {
                param.getOriginLocationCodeList().retainAll(locationCodeList);
            }
        }
        // 来源库位
        if (StrUtil.isNotBlank(param.getOriginLocationCode())) {
            if (CollectionUtil.isEmpty(param.getOriginLocationCodeList())) {
                param.setOriginLocationCodeList(ListUtil.of(param.getOriginLocationCode()));
            } else {
                param.getOriginLocationCodeList().retainAll(ListUtil.of(param.getOriginLocationCode()));
            }
        }
        if (CollectionUtil.isNotEmpty(param.getOriginLocationCodeList())) {
            TransferDetailParam transferDetailParam = new TransferDetailParam();
            transferDetailParam.setOriginLocationCodeList(param.getOriginLocationCodeList());
            List<TransferDetailDTO> transferDetailDTOList = remoteTransferClient.listDetail(transferDetailParam);
            List<String> transferCodeList = transferDetailDTOList.stream().map(TransferDetailDTO::getTransferCode)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(transferCodeList)) {
                return Result.success(new Page<>());
            }
            if (CollectionUtil.isNotEmpty(param.getCodeList())) {
                param.getCodeList().retainAll(transferCodeList);
            } else {
                param.setCodeList(transferCodeList);
            }
        }
        // 目标库区
        if (StrUtil.isNotBlank(param.getTargetZoneCode())) {
            LocationParam locationParam = new LocationParam();
            locationParam.setZoneCode(param.getTargetZoneCode());
            List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
            List<String> locationCodeList =
                    locationDTOList.stream().map(LocationDTO::getCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(locationCodeList)) {
                return Result.success(new Page<>());
            }
            if (CollectionUtil.isEmpty(param.getTargetLocationCodeList())) {
                param.setTargetLocationCodeList(locationCodeList);
            } else {
                param.getTargetLocationCodeList().retainAll(locationCodeList);
            }
        }
        // 目标库位
        if (StrUtil.isNotBlank(param.getTargetLocationCode())) {
            if (CollectionUtil.isEmpty(param.getTargetLocationCodeList())) {
                param.setTargetLocationCodeList(ListUtil.of(param.getTargetLocationCode()));
            } else {
                param.getTargetLocationCodeList().retainAll(ListUtil.of(param.getTargetLocationCode()));
            }
        }
        if (CollectionUtil.isNotEmpty(param.getTargetLocationCodeList())) {
            TransferDetailParam transferDetailParam = new TransferDetailParam();
            transferDetailParam.setTargetLocationCodeList(param.getTargetLocationCodeList());
            List<TransferDetailDTO> transferDetailDTOList = remoteTransferClient.listDetail(transferDetailParam);
            List<String> transferCodeList = transferDetailDTOList.stream().map(TransferDetailDTO::getTransferCode)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(transferCodeList)) {
                return Result.success(new Page<>());
            }
            if (CollectionUtil.isNotEmpty(param.getCodeList())) {
                param.getCodeList().retainAll(transferCodeList);
            } else {
                param.setCodeList(transferCodeList);
            }
        }

        // 根据来源商品条码找来源商品编码列表
        Page<TransferDTO> page = remoteTransferClient.page(param);
        List<String> cargoCodeList =
                page.getRecords().stream().map(TransferDTO::getCargoCode).distinct().collect(Collectors.toList());
        CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
        cargoOwnerParam.setCodeList(cargoCodeList);
        List<CargoOwnerDTO> cargoOwnerDTOS = remoteCargoOwnerClient.queryList(cargoOwnerParam);

        CargoConfigParam cargoConfigParam = new CargoConfigParam();
        cargoConfigParam.setCargoCodeList(cargoCodeList);
        cargoConfigParam.setPropKey(CargoConfigParamEnum.OUT_STOCK_CHECK.getCode());
        cargoConfigParam.setStatus(CargoConfigStatusEnum.ENABLE.getValue());
        List<CargoConfigDTO> cargoConfigDTOList = remoteCargoConfigClient.getList(cargoConfigParam);

        page.getRecords().forEach(transferDTO -> {
            Optional<CargoOwnerDTO> any = cargoOwnerDTOS.stream()
                    .filter(cargoOwnerDTO -> cargoOwnerDTO.getCode().equals(transferDTO.getCargoCode())).findAny();
            any.ifPresent(cargoOwnerDTO -> {
                transferDTO.setCargoName(cargoOwnerDTO.getName());
                transferDTO.setIsTaoTian(CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO));
            });
            if (cargoConfigBiz.stockNeedUpstreamCheck(cargoConfigDTOList.stream()
                    .filter(cargoConfigDTO -> cargoConfigDTO.getCargoCode().equals(transferDTO.getCargoCode())).findAny()
                    .orElse(null))) {
                transferDTO.setNeedERPCheck("YES");
            } else {
                transferDTO.setNeedERPCheck("NO");
            }
        });
        return Result.success(page);
    }

    @Override
    public Result<TransferDTO> detail(TransferParam param) {
        if (StrUtil.isBlank(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "转移单编码必传");
        }
        TransferDTO transferDTO = remoteTransferClient.get(param);
        if (transferDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "转移单信息不存在");
        }
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(transferDTO.getCargoCode());
        transferDTO.setCargoName(cargoOwnerDTO.getName());

        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(param.getCode());
        List<TransferDetailDTO> transferDetailDTOList = remoteTransferClient.listDetail(transferDetailParam);

        // 商品信息
        List<String> skuCodeList = transferDetailDTOList.stream().flatMap(
                        transferDetailDTO -> Stream.of(transferDetailDTO.getOriginSkuCode(), transferDetailDTO.getTargetSkuCode()))
                .distinct().collect(Collectors.toList());
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(transferDTO.getCargoCode());
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);

        // 批次信息
        List<String> skuLotNoList =
                transferDetailDTOList.stream().flatMap(transferDetailDTO -> Stream.of(transferDetailDTO.getOriginSkuLotNo(),
                        transferDetailDTO.getTargetSkuLotNo())).distinct().collect(Collectors.toList());
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(transferDTO.getCargoCode());
        skuLotParam.setSkuCodeList(skuCodeList);
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);

        transferDetailDTOList.forEach(transferDetailDTO -> {
            Optional<SkuDTO> skuDTOOptional = skuDTOList.stream()
                    .filter(skuDTO -> transferDetailDTO.getTargetSkuCode().equals(skuDTO.getCode())).findAny();
            transferDetailDTO.setIsLifeMgt(SkuLifeCtrlEnum.SKU_LIFE_CTRL_NO.getCode());
            transferDetailDTO.setLifeCycle(0);
            transferDetailDTO.setWithdrawCycle(0);
            skuDTOOptional.ifPresent(skuDTO -> {
                transferDetailDTO.setIsLifeMgt(skuDTO.getIsLifeMgt());
                transferDetailDTO.setLifeCycle(skuDTO.getLifeCycle());
                transferDetailDTO.setWithdrawCycle(skuDTO.getWithdrawCycle());
            });
            transferDetailDTO.setCargoName(cargoOwnerDTO.getName());

            Optional<SkuLotDTO> originSkuLot = skuLotDTOList.stream()
                    .filter(skuLotDTO -> transferDetailDTO.getOriginSkuCode().equals(skuLotDTO.getSkuCode()))
                    .filter(skuLotDTO -> transferDetailDTO.getOriginSkuLotNo().equals(skuLotDTO.getCode())).findAny();
            originSkuLot.ifPresent(skuLotDTO -> {
                transferDetailDTO.setOriginManufDateFormat(skuLotDTO.getManufDateFormat());
                transferDetailDTO.setOriginReceiveDateFormat(skuLotDTO.getReceiveDateFormat());
                transferDetailDTO.setOriginExternalSkuLotNo(skuLotDTO.getExternalSkuLotNo());
                transferDetailDTO.setOriginExpireDateFormat(skuLotDTO.getExpireDateFormat());
                transferDetailDTO.setOriginWithdrawDateFormat(skuLotDTO.getWithdrawDateFormat());
                transferDetailDTO.setOriginExternalLinkBillNo(skuLotDTO.getExternalLinkBillNo());
                transferDetailDTO.setOriginInventoryType(skuLotDTO.getInventoryType());
                transferDetailDTO.setOriginValidityCode(skuLotDTO.getValidityCode());
                transferDetailDTO.setOriginPalletCode(skuLotDTO.getPalletCode());
                transferDetailDTO.setOriginBoxCode(skuLotDTO.getBoxCode());
            });

            Optional<SkuLotDTO> targetSkuLot = skuLotDTOList.stream()
                    .filter(skuLotDTO -> transferDetailDTO.getTargetSkuCode().equals(skuLotDTO.getSkuCode()))
                    .filter(skuLotDTO -> transferDetailDTO.getTargetSkuLotNo().equals(skuLotDTO.getCode())).findAny();
            targetSkuLot.ifPresent(skuLotDTO -> {
                transferDetailDTO.setTargetManufDateFormat(skuLotDTO.getManufDateFormat());
                transferDetailDTO.setTargetExternalSkuLotNo(skuLotDTO.getExternalSkuLotNo());
                transferDetailDTO.setTargetReceiveDateFormat(skuLotDTO.getReceiveDateFormat());
                transferDetailDTO.setTargetExpireDateFormat(skuLotDTO.getExpireDateFormat());
                transferDetailDTO.setTargetWithdrawDateFormat(skuLotDTO.getWithdrawDateFormat());
                transferDetailDTO.setTargetExternalLinkBillNo(skuLotDTO.getExternalLinkBillNo());
                transferDetailDTO.setTargetInventoryType(skuLotDTO.getInventoryType());
                transferDetailDTO.setTargetValidityCode(skuLotDTO.getValidityCode());
                transferDetailDTO.setTargetPalletCode(skuLotDTO.getPalletCode());
                transferDetailDTO.setTargetBoxCode(skuLotDTO.getBoxCode());
            });
            // -----
            if (transferDetailDTO.getIsLifeMgt().equals(SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())
                    && transferDetailDTO.getLifeCycle() > 0) {
                if (transferDetailDTO.getOriginExpireDate() > 0) {
                    Long withdrawDate = transferDetailDTO.getOriginExpireDate()
                            - transferDetailDTO.getWithdrawCycle() * DateUnit.DAY.getMillis();
                    transferDetailDTO.setOriginWithdrawDate(withdrawDate);
                    transferDetailDTO.setOriginWithdrawDateFormat(
                            StringUtils.isEmpty(transferDetailDTO.getOriginWithdrawDateFormat())
                                    ? DatePattern.NORM_DATE_PATTERN : transferDetailDTO.getOriginWithdrawDateFormat());
                }
                if (transferDetailDTO.getTargetExpireDate() > 0) {
                    Long withdrawDate = transferDetailDTO.getTargetExpireDate()
                            - transferDetailDTO.getWithdrawCycle() * DateUnit.DAY.getMillis();
                    transferDetailDTO.setTargetWithdrawDate(withdrawDate);
                    transferDetailDTO.setTargetWithdrawDateFormat(
                            StringUtils.isEmpty(transferDetailDTO.getTargetWithdrawDateFormat())
                                    ? DatePattern.NORM_DATE_PATTERN : transferDetailDTO.getTargetWithdrawDateFormat());
                }

            }
        });
        transferDTO.setTransferDetailDTOList(transferDetailDTOList);
        return Result.success(transferDTO);
    }

    @Override
    public Result<Boolean> maintainLocation(TransferUpdateBizParam param) {
        if (null == param) throw ExceptionUtil.ARG_ERROR;
        if (StrUtil.isBlank(param.getTransferCode())) throw ExceptionUtil.exceptionWithMessage("转移单号必传");
        List<TransferDetailUpdateBizParam> transferDetailUpdateBizParamList = param.getTransferDetailUpdateBizParamList();
        if (CollectionUtil.isEmpty(transferDetailUpdateBizParamList))
            throw ExceptionUtil.exceptionWithMessage("明细不能为空");
        TransferParam transferParam = new TransferParam();
        transferParam.setCode(param.getTransferCode());
        TransferDTO transferDTO = remoteTransferClient.get(transferParam);
        if (null == transferDTO) throw ExceptionUtil.exceptionWithMessage("转移单不存在");

        if (!TransferStatusEnum.ERP_CHECKED.getCode().equalsIgnoreCase(transferDTO.getStatus()) && !TransferStatusEnum.IN_CHECKED.getCode().equalsIgnoreCase(transferDTO.getStatus())) {
            throw ExceptionUtil.exceptionWithMessage("当前状态不允许编辑库位");
        }

        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(transferDTO.getCode());
        List<TransferDetailDTO> transferDetailDTOList = remoteTransferClient.listDetail(transferDetailParam);
        if (CollectionUtil.isEmpty(transferDetailDTOList)) {
            throw ExceptionUtil.exceptionWithMessage("转移单明细不存在");
        }

        if (transferDetailUpdateBizParamList.stream().anyMatch(it -> StrUtil.isBlank(it.getTargetLocationCode()))) {
            throw ExceptionUtil.exceptionWithMessage("目标库位必填");
        }

        List<String> locationCodeList = transferDetailUpdateBizParamList.stream()
                .map(TransferDetailUpdateBizParam::getTargetLocationCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(locationCodeList)) throw ExceptionUtil.exceptionWithMessage("库位编码必填");

        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(locationCodeList);
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
        if (CollectionUtil.isEmpty(locationDTOList)) throw ExceptionUtil.exceptionWithMessage("库位不存在");

        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCodeList(locationDTOList.stream().map(LocationDTO::getZoneCode).distinct().collect(Collectors.toList()));
        List<ZoneDTO> zoneDTOList = remoteZoneClient.getList(zoneParam);
        if (CollectionUtil.isEmpty(zoneDTOList)) throw ExceptionUtil.exceptionWithMessage("库区不存在");
        Map<String, LocationDTO> locationDTOMap = locationDTOList.stream().collect(Collectors.toMap(LocationDTO::getCode, Function.identity()));
        Map<String, ZoneDTO> zoneDTOMap = zoneDTOList.stream().collect(Collectors.toMap(ZoneDTO::getCode, Function.identity()));

        List<TransferDetailDTO> updateDetailList = new ArrayList<>();
        Map<Long, TransferDetailDTO> transferDetailDTOMap = transferDetailDTOList.stream().collect(Collectors.toMap(TransferDetailDTO::getId, Function.identity()));
        for (TransferDetailUpdateBizParam transferDetailUpdateBizParam : transferDetailUpdateBizParamList) {
            TransferDetailDTO transferDetailDTO = transferDetailDTOMap.get(transferDetailUpdateBizParam.getId());
            if (null == transferDetailDTO) throw ExceptionUtil.exceptionWithMessage("明细不存在");
            LocationDTO locationDTO = locationDTOMap.get(transferDetailUpdateBizParam.getTargetLocationCode());
            if (null == locationDTO) throw ExceptionUtil.exceptionWithMessage("库位不存在");
            if (LocationStatusEnum.STATUS_DISABLED.getStatus().equals(locationDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("库位已禁用" + locationDTO.getCode());
            }
            ZoneDTO zoneDTO = zoneDTOMap.get(locationDTO.getZoneCode());
            if (null == zoneDTO) throw ExceptionUtil.exceptionWithMessage("库区不存在");
            if (ZoneStatusEnum.STATUS_DISABLED.getStatus().equals(zoneDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("库区已禁用" + zoneDTO.getCode());
            }
            if (!zoneDTO.getSkuQuality().equalsIgnoreCase(transferDetailDTO.getTargetSkuQuality())) {
                throw ExceptionUtil.exceptionWithMessage("目标库位商品属性与明细不一致");
            }
            if (!ZoneTypeEnum.ZONE_TYPE_STORE.getType().equalsIgnoreCase(zoneDTO.getType()) && !ZoneTypeEnum.ZONE_TYPE_PICK.getType().equalsIgnoreCase(zoneDTO.getType())) {
                throw ExceptionUtil.exceptionWithMessage("目标库位必须是拣选位或者存储位");
            }
            transferDetailDTO.setTargetLocationCode(transferDetailUpdateBizParam.getTargetLocationCode());
            updateDetailList.add(transferDetailDTO);
        }

        TransferPersistBO transferPersistBO = new TransferPersistBO();
        transferPersistBO.setTransferDetailDTOList(updateDetailList);
        remoteTransferClient.update(transferPersistBO);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> updateTransferDetail(TransferUpdateBizParam param) {
        String source = JSON.toJSONString(param);
        if (param.getTransferDetailUpdateBizParamList().isEmpty()) {
            throw new BaseException(BaseBizEnum.TIP, "转移单明细不能为空");
        }
        if (param.getTransferDetailUpdateBizParamList().size() > 50) {
            throw new BaseException(BaseBizEnum.TIP, "明细长度不要超过50【请分成多个转移单处理】");
        }
        // 兼容前端命名要求
        param.getTransferDetailUpdateBizParamList().forEach(transferDetailUpdateBizParam -> {
            if (StrUtil.isBlank(transferDetailUpdateBizParam.getOriginSkuCode())) {
                transferDetailUpdateBizParam.setOriginSkuCode(transferDetailUpdateBizParam.getSkuCode());
            }
            if (StrUtil.isBlank(transferDetailUpdateBizParam.getOriginSkuLotNo())) {
                transferDetailUpdateBizParam.setOriginSkuLotNo(transferDetailUpdateBizParam.getSkuLotNo());
            }
            if (StrUtil.isBlank(transferDetailUpdateBizParam.getOriginLoactionCode())) {
                transferDetailUpdateBizParam.setOriginLoactionCode(transferDetailUpdateBizParam.getLocationCode());
            }
            transferDetailUpdateBizParam.setOriginQty(transferDetailUpdateBizParam.getAvailableQty());
            transferDetailUpdateBizParam.setOriginReceiveDate(transferDetailUpdateBizParam.getReceiveDate());
            transferDetailUpdateBizParam.setOriginManufDate(transferDetailUpdateBizParam.getManufDate());
            transferDetailUpdateBizParam.setOriginExpireDate(transferDetailUpdateBizParam.getExpireDate());
            transferDetailUpdateBizParam.setOriginSkuQuality(transferDetailUpdateBizParam.getSkuQuality());
            transferDetailUpdateBizParam.setOriginProductionNo(transferDetailUpdateBizParam.getProductionNo());
            transferDetailUpdateBizParam.setOriginWithdrawDate(transferDetailUpdateBizParam.getWithdrawDate());
        });


        // 查询主单
        TransferParam transferParam = new TransferParam();
        transferParam.setCode(param.getTransferCode());

        TransferDTO transferDTO = null;
        if (param.getFlag() == null || param.getFlag().equals(TransferUpdateBizParam.FlagEnum.IMPORT_UPDATE) || param.getFlag().equals(TransferUpdateBizParam.FlagEnum.UPDATE)) {
            transferDTO = remoteTransferClient.get(transferParam);
            //页面修改
            if (param.getFlag() != null && param.getFlag().equals(TransferUpdateBizParam.FlagEnum.UPDATE)) {
                param.setBusinessType(transferDTO.getBusinessType());
            }
        }
        if (null == transferDTO) {
            // 导入新增
            if (param.getFlag() != null && param.getFlag().equals(TransferUpdateBizParam.FlagEnum.IMPORT_ADD)) {
                transferDTO = new TransferDTO();
                transferDTO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
                transferDTO.setCargoCode(param.getCargoCode());
                transferDTO.setReason(param.getReason());
                transferDTO.setNote(param.getNote());
                transferDTO.setStatus(TransferStatusEnum.CREATED.getCode());
                transferDTO.setBusinessType(param.getBusinessType());
                transferDTO.setCode(remoteSeqRuleClient.findSequence(SeqEnum.TRANSFER_CODE_000001));
                param.setTransferCode(transferDTO.getCode());
                param.setTransferDTO(transferDTO);
            } else {
                throw new BaseException(BaseBizEnum.TIP, "单据异常");
            }
        }
        if (!TransferStatusEnum.CREATED.getCode().equals(transferDTO.getStatus())
                && !TransferStatusEnum.IN_REJECT.getCode().equals(transferDTO.getStatus())
                && !TransferStatusEnum.ERP_REJECT.getCode().equals(transferDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "只有创建或者审核失败状态下的转移单允许修改");
        }


        List<String> skuCodeList = param
                .getTransferDetailUpdateBizParamList().stream().flatMap(transferDetailUpdateBizParam -> Stream
                        .of(transferDetailUpdateBizParam.getOriginSkuCode(), transferDetailUpdateBizParam.getTargetSkuCode()))
                .distinct().collect(Collectors.toList());
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(transferDTO.getCargoCode());
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);

        // 商品信息校验
        checkSkuInfo(param, skuDTOList);

        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(transferDTO.getCargoCode());

        // 淘天ERP驳回不允许修改，只能取消
        Boolean taoTianWarehouse = CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO);
        if (taoTianWarehouse) {
            if (TransferStatusEnum.ERP_REJECT.getCode().equalsIgnoreCase(transferDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("淘天上游审核驳回的转移单不允许编辑");
            }
        }
        // 基础校验
        check(param);
        // 库位类型校验
        List<String> locationCodeList = param
                .getTransferDetailUpdateBizParamList().stream().flatMap(transferDetailBizParam -> Stream
                        .of(transferDetailBizParam.getTargetLocationCode(), transferDetailBizParam.getOriginLoactionCode()))
                .filter(StrUtil::isNotBlank)
                .distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(locationCodeList)) {
            LocationParam locationParam = new LocationParam();
            locationParam.setCodeList(locationCodeList);
            List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
            List<String> zoneCodeList = locationDTOList.stream().map(LocationDTO::getZoneCode).collect(Collectors.toList());
            ZoneParam zoneParam = new ZoneParam();
            zoneParam.setCodeList(zoneCodeList);
            List<ZoneDTO> zoneDTOList = remoteZoneClient.getList(zoneParam);

            checkLocation(param, locationDTOList, zoneDTOList);
        }

        // 来源批次
        List<String> skuLotNoList = param.getTransferDetailUpdateBizParamList().stream()
                .map(TransferDetailUpdateBizParam::getOriginSkuLotNo).distinct().collect(Collectors.toList());
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setSkuCodeList(skuCodeList);
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);

        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setSkuCodeList(skuCodeList);
        stockLocationParam.setSkuLotNoList(skuLotNoList);
        List<StockLocationDTO> stockLocationDTOS = remoteStockLocationClient.getList(stockLocationParam);

        param.getTransferDetailUpdateBizParamList().forEach(transferDetailDTO -> {
            if (transferDetailDTO.getChangeQty() == null) {
                throw new BaseException(BaseBizEnum.TIP, "转移数量必填");
            }
            if (transferDetailDTO.getChangeQty().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BaseException(BaseBizEnum.TIP, "转移数量必须大于0");
            }
            Optional<SkuDTO> any = skuDTOList.stream()
                    .filter(skuDTO -> skuDTO.getCode().equals(transferDetailDTO.getTargetSkuCode())).findAny();
            if (!any.isPresent()) {
                throw new BaseException(BaseBizEnum.TIP, "商品信息不存在");
            }
            // 目标批次信息查询或生成
            SkuLotCheckAndFormatParam skuLotCheckAndFormatParam = new SkuLotCheckAndFormatParam();
            skuLotCheckAndFormatParam.setExpireDate(transferDetailDTO.getTargetExpireDate());
            skuLotCheckAndFormatParam.setManufDate(transferDetailDTO.getTargetManufDate());
            skuLotCheckAndFormatParam.setProductionNo(transferDetailDTO.getTargetProductionNo());
            skuLotCheckAndFormatParam.setSkuQuality(transferDetailDTO.getTargetSkuQuality());
            skuLotCheckAndFormatParam.setReceiveDate(transferDetailDTO.getTargetReceiveDate());
            skuLotCheckAndFormatParam.setExternalLinkBillNo(transferDetailDTO.getTargetExternalLinkBillNo());
            skuLotCheckAndFormatParam.setInventoryType(transferDetailDTO.getTargetInventoryType());

            skuLotCheckAndFormatParam.setPalletCode(transferDetailDTO.getTargetPalletCode());
            skuLotCheckAndFormatParam.setBoxCode(transferDetailDTO.getTargetBoxCode());
            skuLotCheckAndFormatParam.setValidityCode(transferDetailDTO.getTargetValidityCode());

            //校验效期码
            if(!StringUtils.isEmpty(transferDetailDTO.getTargetValidityCode())&& !wmsOtherConfig.isValidValidityCode(transferDetailDTO.getTargetValidityCode())){
                throw new BaseException(BaseBizEnum.TIP, wmsOtherConfig.getValidValidityRuleMsg());
            }
            if (ObjectUtil.isNotEmpty(transferDetailDTO.getTargetBoxCode())) {
                boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", transferDetailDTO.getTargetBoxCode());
                if (!matches) {
                    throw new BaseException(BaseBizEnum.TIP, "目标箱码:英文大小写、数字、中划线,长度30,请核查");
                }
            }
            if (ObjectUtil.isNotEmpty(transferDetailDTO.getTargetPalletCode())) {
                boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", transferDetailDTO.getTargetPalletCode());
                if (!matches) {
                    throw new BaseException(BaseBizEnum.TIP, "目标托盘号", "目标托盘号:英文大小写、数字、中划线,长度30,请核查");
                }
            }

            List<String> zpList = Arrays.asList(InventoryTypeEnum.ZP.getCode(), InventoryTypeEnum.ZCZP.getCode());
            if (Objects.equals(transferDetailDTO.getTargetSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel())
                    && !zpList.contains(transferDetailDTO.getTargetInventoryType())) {
                throw new BaseException(BaseBizEnum.TIP, "商品属性为【正品】,残次等级只能选择【正品】或【收货暂存良-待退废】");
            }
            if (Objects.equals(transferDetailDTO.getTargetSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())
                    && zpList.contains(transferDetailDTO.getTargetInventoryType())) {
                throw new BaseException(BaseBizEnum.TIP, "商品属性为【次品】,残次等级不能选择【正品】或【收货暂存良-待退废】");
            }

            // 批次属性校验
            CheckSkuLotParam checkSkuLotParam = new CheckSkuLotParam();
            checkSkuLotParam.setSkuCode(any.get().getCode());
            checkSkuLotParam.setSkuQuality(transferDetailDTO.getTargetSkuQuality());
            checkSkuLotParam.setExpireDate(transferDetailDTO.getTargetExpireDate());
            checkSkuLotParam.setManufDate(transferDetailDTO.getTargetManufDate());
            checkSkuLotParam.setProductionNo(transferDetailDTO.getTargetProductionNo());
            checkSkuLotParam.setReceiveDate(transferDetailDTO.getTargetReceiveDate());
            checkSkuLotParam.setExternalLinkBillNo(transferDetailDTO.getTargetExternalLinkBillNo());
            checkSkuLotParam.setInventoryType(transferDetailDTO.getTargetInventoryType());

            checkSkuLotParam.setPalletCode(transferDetailDTO.getTargetPalletCode());
            checkSkuLotParam.setBoxCode(transferDetailDTO.getTargetBoxCode());
            checkSkuLotParam.setValidityCode(transferDetailDTO.getTargetValidityCode());

//            //add 2024-08-12 箱码和托盘 --目前不支持前端输入
//            skuLotDTOList.stream()
//                    .filter(skuLotDTO -> skuLotDTO.getSkuCode().equals(transferDetailDTO.getOriginSkuCode()))
//                    .filter(skuLotDTO -> skuLotDTO.getCode().equals(transferDetailDTO.getOriginSkuLotNo()))
//                    .findFirst().ifPresent(skuLotDTO -> {
//                        if (StrUtil.isNotBlank(skuLotDTO.getBoxCode())) {
//                            checkSkuLotParam.setBoxCode(skuLotDTO.getBoxCode());
//                            skuLotCheckAndFormatParam.setBoxCode(skuLotDTO.getBoxCode());
//                        }
//                        if (StrUtil.isNotBlank(skuLotDTO.getPalletCode())) {
//                            checkSkuLotParam.setPalletCode(skuLotDTO.getPalletCode());
//                            skuLotCheckAndFormatParam.setPalletCode(skuLotDTO.getPalletCode());
//                        }
//                    });
            try {
                skulotBizClient.verificationSkuLotParam(checkSkuLotParam, any.get());
            } catch (Exception exception) {
                throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), checkSkuLotParam.getSkuCode(), exception.getMessage()));
            }

            SkuLotDTO targetSkuLotDTO = skulotBizClient.findAndFormatSkuLot(skuLotCheckAndFormatParam, any.get(), false);

            transferDetailDTO.setTargetSkuLotNo(targetSkuLotDTO.getCode());
            transferDetailDTO.setTargetWithdrawDate(targetSkuLotDTO.getWithdrawDate());

            // 来源批次信息填充
            Optional<SkuLotDTO> originSkuLot = skuLotDTOList.stream()
                    .filter(skuLotDTO -> skuLotDTO.getSkuCode().equals(transferDetailDTO.getOriginSkuCode()))
                    .filter(skuLotDTO -> skuLotDTO.getCode().equals(transferDetailDTO.getOriginSkuLotNo())).findAny();
            if (!originSkuLot.isPresent()) {
                throw new BaseException(BaseBizEnum.TIP, "来源批次信息不存在");
            }
            if (ObjectUtil.isEmpty(targetSkuLotDTO.getId())) {
                originSkuLot.ifPresent(skuLotDTO -> {
                    if (StringUtils.isEmpty(skuLotDTO.getExtraJson())) {
                        targetSkuLotDTO.setExtraJson(skuLotDTO.getExtraJson());
                    }
                });
                remoteSkuLotClient.saveBatch(Lists.newArrayList(targetSkuLotDTO));
            }
            originSkuLot.ifPresent(skuLotDTO -> {
                transferDetailDTO.setOriginExpireDate(skuLotDTO.getExpireDate());
                transferDetailDTO.setOriginManufDate(skuLotDTO.getManufDate());
                transferDetailDTO.setOriginReceiveDate(skuLotDTO.getReceiveDate());
                transferDetailDTO.setOriginSkuQuality(skuLotDTO.getSkuQuality());
                transferDetailDTO.setOriginProductionNo(skuLotDTO.getProductionNo());
                transferDetailDTO.setOriginWithdrawDate(skuLotDTO.getWithdrawDate());
                transferDetailDTO.setOriginInventoryType(skuLotDTO.getInventoryType());
            });
            // 保存一下商品批次库位库存可用数
            Optional<StockLocationDTO> optionalStockLocationDTO = stockLocationDTOS.stream()
                    .filter(stockLocationDTO -> stockLocationDTO.getSkuCode().equals(transferDetailDTO.getOriginSkuCode()))
                    .filter(
                            stockLocationDTO -> stockLocationDTO.getSkuLotNo().equals(transferDetailDTO.getOriginSkuLotNo()))
                    .filter(stockLocationDTO -> stockLocationDTO.getLocationCode()
                            .equals(transferDetailDTO.getOriginLoactionCode()))
                    .filter(stockLocationDTO -> stockLocationDTO.getSkuQuality()
                            .equals(transferDetailDTO.getOriginSkuQuality()))
                    .findAny();
            optionalStockLocationDTO.ifPresent(stockLocationDTO -> {
                transferDetailDTO.setOriginQty(stockLocationDTO.getAvailableQty());
            });
        });

        for (TransferDetailUpdateBizParam transferDetailBizParam : param.getTransferDetailUpdateBizParamList()) {
            transferDetailBizParam.setWarehouseCode(transferDTO.getWarehouseCode());
            transferDetailBizParam.setCargoCode(transferDTO.getCargoCode());
            transferDetailBizParam.setTransferCode(transferDTO.getCode());
        }

        // 校验转移前后数据不能相同
        param.getTransferDetailUpdateBizParamList().forEach(transferDetailUpdateBizParam -> {
            if (transferDetailUpdateBizParam.getOriginSkuLotNo()
                    .equals(transferDetailUpdateBizParam.getTargetSkuLotNo())) {
                throw new BaseException(BaseBizEnum.TIP, "没有修改批次请使用调整功能");
            }
        });

        //        // 来源商品批次库位不能存在多条
        long count = param.getTransferDetailUpdateBizParamList().stream()
                .filter(transferDetailUpdateBizParam -> StrUtil.isNotBlank(transferDetailUpdateBizParam.getTargetLocationCode()))
                .map(transferDetailUpdateBizParam -> StrUtil.join(StrUtil.COLON,
                        transferDetailUpdateBizParam.getOriginSkuLotNo(),
                        transferDetailUpdateBizParam.getTargetSkuLotNo(),
                        transferDetailUpdateBizParam.getOriginLoactionCode(),
                        transferDetailUpdateBizParam.getTargetLocationCode()))
                .distinct().count();
        long count1 = param.getTransferDetailUpdateBizParamList().stream()
                .filter(transferDetailUpdateBizParam -> StrUtil.isNotBlank(transferDetailUpdateBizParam.getTargetLocationCode()))
                .count();
        if (count != count1) {
            throw new BaseException(BaseBizEnum.TIP, "转移明细来源库位,来源商品批次,目标属性,目标库位相同不能重复");
        }
        //调整字节信息校验
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList()) && defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList().contains(CurrentRouteHolder.getWarehouseCode())) {
            if (StringUtils.isEmpty(param.getBusinessType())) {
                throw new BaseException(BaseBizEnum.TIP, "当前仓库,业务场景不能为空");
            }
            checkSpecial(param, skuDTOList);
        }
        // 修改
        transferGtsService.updateTransferDetail(param);

        // 添加日志
        TransferLogDTO logDTO = new TransferLogDTO();
        logDTO.setCargoCode(transferDTO.getCargoCode());
        logDTO.setTransferCode(transferDTO.getCode());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        if (param.getFlag() != null && param.getFlag().equals(TransferUpdateBizParam.FlagEnum.IMPORT_ADD)) {
            logDTO.setOpContent("导入新增转移单:" + transferDTO.getCode());
        } else if (param.getFlag() != null && param.getFlag().equals(TransferUpdateBizParam.FlagEnum.IMPORT_UPDATE)) {
            logDTO.setOpContent("导入修改转移单:" + transferDTO.getCode());
        } else {
            logDTO.setOpContent("修改转移单明细:" + transferDTO.getCode());
        }
        logDTO.setOpRemark("报文:" + source);
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
        remoteTransferLogClient.save(logDTO);
        return Result.success(true);
    }

    /**
     * @param param
     * @param skuDTOList
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2023/2/15 11:41
     */
    private void checkSpecial(TransferUpdateBizParam param, List<SkuDTO> skuDTOList) {

        param.getTransferDetailUpdateBizParamList().forEach(it -> {
            if (StringUtils.isEmpty(it.getReason())) {
                throw new BaseException(BaseBizEnum.TIP, "当前仓库,明细调整原因不能为空");
            }
            if (StringUtils.isEmpty(it.getRemark())) {
                throw new BaseException(BaseBizEnum.TIP, "当前仓库,明细备注不能为空");
            }
            if (StringUtils.isEmpty(it.getRp())) {
                throw new BaseException(BaseBizEnum.TIP, "当前仓库,明细责任方不能为空");
            }
            if (!StringUtils.isEmpty(it.getRemark()) && it.getRemark().length() > 200) {
                throw new BaseException(BaseBizEnum.TIP, "明细备注不能超过200个字符");
            }
            /**
             * 【业务场景】选择【201库内商品正转残】：
             * 转移单所有明细【来源商品属性】必须为正品、【目标商品属性】必须为【次品】
             * 【调整原因】只能选 51 或52
             */
            if (param.getBusinessType().equalsIgnoreCase(TransferBusinessTypeEnum.WAREHOUSE_SKU_AVL_TO_DAMAGE.getCode())) {
                if (!Arrays.asList(TransferDetailReasonEnum.WAREHOUSE_EXCEPTION_TO_DAMAGE.getCode(),
                        TransferDetailReasonEnum.EXPIRE_REASON.getCode()).contains(it.getReason())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务场景选择【库内商品正转残】,明细调整原因必须选【库内异常致残】和【效期原因】");
                }
                if (!Objects.equals(it.getOriginSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务场景选择【库内商品正转残】,明细【来源商品属性】必须为【正品】");
                }
                if (!Objects.equals(it.getTargetSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务场景选择【库内商品正转残】,明细【目标商品属性】必须为【次品】");
                }
            }
            /**
             【业务场景】选择【202库内商品残转正】：
             转移单所有明细【来源商品属性】必须为次品、【目标商品属性】必须为【正品】
             【调整原因】只能选 53
             */
            if (param.getBusinessType().equalsIgnoreCase(TransferBusinessTypeEnum.WAREHOUSE_SKU_DAMAGE_TO_AVL.getCode())) {
                if (!Arrays.asList(TransferDetailReasonEnum.SKU_REPAIR_TO_AVL.getCode()).contains(it.getReason())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务场景选择【库内商品残转正】,明细调整原因必须选【商品修复后转良】");
                }
                if (!Objects.equals(it.getOriginSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务场景选择【库内商品残转正】,明细【来源商品属性】必须为【次品】");
                }
                if (!Objects.equals(it.getTargetSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务场景选择【库内商品残转正】,明细【目标商品属性】必须为【正品】");
                }
            }
            /**
             * 【业务场景】选择【203库存批次属性转移】：
             * 转移单所有明细，来源商品属性和目标商品属性必须一致
             */
            if (param.getBusinessType().equalsIgnoreCase(TransferBusinessTypeEnum.STOCK_ATTR_CHANGE.getCode())) {
                if (!Objects.equals(it.getTargetSkuQuality(), it.getOriginSkuQuality())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务场景选择【库存批次属性转移】,明细来源商品属性和目标商品属性必须一致");
                }
            }
            /**
             * 【业务场景】选择【204库内商品过期转残】：
             * 转移单所有明细，必须为效期商品，并且【来源失效日期】应该是小于当前日期
             */
            if (param.getBusinessType().equalsIgnoreCase(TransferBusinessTypeEnum.WAREHOUSE_SKU_EXPIRE_TO_DAMAGE.getCode())) {
                SkuDTO skuDTO = skuDTOList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(it.getCargoCode()))
                        .filter(a -> a.getCode().equalsIgnoreCase(it.getSkuCode())).findFirst().orElse(null);
                if (skuDTO == null) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("商品编码%s不存在", it.getSkuCode()));
                }
                if (!Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "业务场景选择【库内商品过期转残】,明细商品必须是效期商品");
                }
                if (it.getOriginExpireDate() > System.currentTimeMillis()) {
                    throw new BaseException(BaseBizEnum.TIP, "业务场景选择【库内商品过期转残】,明细商品【来源失效日期】应该是小于等于当前日期");
                }
            }
        });
    }

    @Override
    public Result<Boolean> examine(TransferExamineBizParam transferExamineBizParam) {
        TransferParam transferParam = new TransferParam();
        transferParam.setCode(transferExamineBizParam.getCode());
        TransferDTO transferDTO = remoteTransferClient.get(transferParam);
        if (!TransferStatusEnum.CREATED.getCode().equals(transferDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "转移单状态不是创建状态，无法审核");
        }
        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(transferDTO.getCode());
        List<TransferDetailDTO> transferDetailDTOS = remoteTransferClient.listDetail(transferDetailParam);
        if (CollectionUtil.isEmpty(transferDetailDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, "转移单没有明细，无法审核");
        }
        // 修改为审核中状态
        transferDTO.setStatus(TransferStatusEnum.UNDER_REVIEW.getCode());
        // TODO 调用erp接口，进行数据回传
        remoteTransferClient.update(transferDTO);
        return Result.success(true);
    }

    @Override
    @Deprecated
    public Result<Boolean> submit(TransferExamineBizParam transferExamineBizParam) {

        TransferParam transferParam = new TransferParam();
        transferParam.setCode(transferExamineBizParam.getCode());
        TransferDTO transferDTO = remoteTransferClient.get(transferParam);
        if (ObjectUtil.isEmpty(transferDTO)) {
            throw new BaseException(BaseBizEnum.TIP, "转移单不存在");
        }

        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(transferDTO.getCode());
        List<TransferDetailDTO> transferDetailDTOS = remoteTransferClient.listDetail(transferDetailParam);
        if (CollectionUtil.isEmpty(transferDetailDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, "转移单明细不能为空");
        }

        if (!cargoConfigBiz.stockNeedUpstreamCheck(transferDTO.getCargoCode())) {
            return auditInner(transferExamineBizParam, transferDTO, transferDetailDTOS);
        }

        TransferBO transferBO = new TransferBO();
        transferBO.setTransferDTO(transferDTO);
        transferBO.setTransferDetailDTOList(transferDetailDTOS);
        Boolean submit = transferGtsService.submitToERPForApproval(transferBO);
        // 添加上游审核日志
        TransferLogDTO logDTO = new TransferLogDTO();
        logDTO.setCargoCode(transferDTO.getCargoCode());
        logDTO.setTransferCode(transferDTO.getCode());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setOpContent("提交上游审核:" + transferDTO.getCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(transferExamineBizParam));
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
        remoteTransferLogClient.save(logDTO);
        return Result.success(true);
    }

    /**
     * 内部审核
     *
     * @param param
     * @param transferDTO
     * @param transferDetailDTOS
     * @return
     */
    private Result<Boolean> auditInner(TransferExamineBizParam param, TransferDTO transferDTO,
                                       List<TransferDetailDTO> transferDetailDTOS) {
        if (StrUtil.isBlank(param.getRemark())) {
            throw new BaseException(BaseBizEnum.TIP, "审核说明必填");
        }
        if (StrUtil.isBlank(param.getPass())) {
            throw new BaseException(BaseBizEnum.TIP, "是否审核通过必填");
        }
        // 添加审核日志
        TransferLogDTO logDTO = new TransferLogDTO();
        logDTO.setCargoCode(transferDTO.getCargoCode());
        logDTO.setTransferCode(transferDTO.getCode());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setOpContent("仓内审核通过:" + transferDTO.getCode() + ",审核说明:" + param.getRemark());
        if (AuditEnum.PASS.getCode().equals(param.getPass())) {
            logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
            logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
            //判断是否需要提交上游审核
            if (cargoConfigBiz.stockNeedUpstreamCheck(transferDTO.getCargoCode())) {
                TransferBO bo = new TransferBO();
                bo.setTransferDTO(transferDTO);
                bo.setTransferDetailDTOList(transferDetailDTOS);
                Boolean result = transferGtsService.submitToERPForApproval(bo);
                // 添加上游审核日志
                TransferLogDTO transferLogDTO = new TransferLogDTO();
                transferLogDTO.setCargoCode(transferDTO.getCargoCode());
                transferLogDTO.setTransferCode(transferDTO.getCode());
                transferLogDTO.setOpDate(System.currentTimeMillis());
                transferLogDTO.setOpBy(CurrentUserHolder.getUserName());
                transferLogDTO.setOpContent("提交上游审核:" + transferDTO.getCode());
                transferLogDTO.setOpRemark("报文:" + JSON.toJSONString(param));
                transferLogDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
                remoteTransferLogClient.save(logDTO);
                remoteTransferLogClient.save(transferLogDTO);
                return Result.success(result);
            }
        } else if (AuditEnum.REFUSE.getCode().equals(param.getPass())) {
            logDTO.setOpContent("仓内审核驳回:" + transferDTO.getCode() + ",审核说明:" + param.getRemark());
        }
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
        remoteTransferLogClient.save(logDTO);
        TransferBO transferBO = new TransferBO();
        transferBO.setPass(param.getPass());
        transferBO.setRemark(param.getRemark());
        transferBO.setTransferDTO(transferDTO);
        transferBO.setTransferDetailDTOList(transferDetailDTOS);
        Boolean submit = transferGtsService.innerApproval(transferBO);

        return Result.success(submit);
    }

    @Override
    public Result<Boolean> cancel(TransferCancelBizParam param) {
        TransferParam transferParam = new TransferParam();
        transferParam.setCode(param.getCode());
        TransferDTO transferDTO = remoteTransferClient.get(transferParam);
        if (!TransferStatusEnum.ERP_REJECT.getCode().equals(transferDTO.getStatus())
                && !TransferStatusEnum.CREATED.getCode().equals(transferDTO.getStatus())
                && !TransferStatusEnum.IN_REJECT.getCode().equals(transferDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "转移单状态不是创建或审核失败，无法取消");
        }

        if (!cargoConfigBiz.stockNeedUpstreamCheck(transferDTO.getCargoCode())) {
            transferDTO.setIsErpCancel(false);
        }
        // 如果是上游驳回 不管是否开启参数都需要回传
        if (TransferStatusEnum.ERP_REJECT.getCode().equals(transferDTO.getStatus())) {
            transferDTO.setIsErpCancel(true);
        } else if (TransferStatusEnum.IN_REJECT.getCode().equals(transferDTO.getStatus())) {
            transferDTO.setIsErpCancel(false);
        }
        // 修改为审核状态
        transferDTO.setStatus(TransferStatusEnum.CANCEL.getCode());
        transferGtsService.cancelTransfer(transferDTO);
        // remoteTransferClient.update(transferDTO);
        // 添加日志
        TransferLogDTO logDTO = new TransferLogDTO();
        logDTO.setTransferCode(transferParam.getCode());
        logDTO.setCargoCode(transferDTO.getCargoCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(param));
        logDTO.setOpContent("取消转移单:" + transferParam.getCode());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
        remoteTransferLogClient.save(logDTO);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> confirm(TransferConfirmBizParam transferConfirmBizParam) {
        TransferParam transferParam = new TransferParam();
        transferParam.setCode(transferConfirmBizParam.getCode());
        TransferDTO transferDTO = remoteTransferClient.get(transferParam);

        if (null == transferDTO) {
            throw new BaseException(BaseBizEnum.TIP, "转移单不存在");
        }
        if (!TransferStatusEnum.ERP_CHECKED.getCode().equals(transferDTO.getStatus())
                && !TransferStatusEnum.IN_CHECKED.getCode().equals(transferDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "转移单状态不是审核通过状态，无法确认");
        }
        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(transferDTO.getCode());
        List<TransferDetailDTO> transferDetailDTOList = remoteTransferClient.listDetail(transferDetailParam);
        if (CollectionUtil.isEmpty(transferDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "转移单没有明细，无法确认");
        }
        if (transferDetailDTOList.stream().map(TransferDetailDTO::getTargetLocationCode).anyMatch(StrUtil::isBlank)) {
            throw ExceptionUtil.exceptionWithMessage("请补充目标库位信息再做库存变动");
        }
        // 商品信息
        List<String> allSkuCodeList = transferDetailDTOList.stream().flatMap(
                        transferDetailDTO -> Stream.of(transferDetailDTO.getOriginSkuCode(), transferDetailDTO.getTargetSkuCode()))
                .distinct().collect(Collectors.toList());
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(transferDTO.getCargoCode());
        skuParam.setCodeList(allSkuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);

        List<String> locationCodeList = transferDetailDTOList.stream().flatMap(transferDetailBizParam -> Stream
                        .of(transferDetailBizParam.getOriginLoactionCode(), transferDetailBizParam.getTargetLocationCode()))
                .distinct().collect(Collectors.toList());
        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(locationCodeList);
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);

        // 最大商品数、最大品类数检验
        List<String> targetLocationCodeList = transferDetailDTOList.stream().map(TransferDetailDTO::getTargetLocationCode).distinct().collect(Collectors.toList());
        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setLocationCodeList(locationCodeList);
        stockLocationParam.setHasPhysicalQty(true);
        List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getList(stockLocationParam);
        for (String locationCode : targetLocationCodeList) {
            locationDTOList.stream().filter(it -> it.getCode().equals(locationCode)).findFirst().ifPresent(locationDTO -> {
                List<StockLocationDTO> targetStockLocationList = stockLocationDTOList.stream().filter(it -> it.getLocationCode().equals(locationCode)).collect(Collectors.toList());
                List<StockLocationDTO> origin = transferDetailDTOList.stream()
                        .filter(it -> !it.getOriginLoactionCode().equals(locationCode))
                        .filter(it -> it.getTargetLocationCode().equals(locationCode)).map(it -> {
                            StockLocationDTO stockLocationDTO = new StockLocationDTO();
                            stockLocationDTO.setCargoCode(it.getCargoCode());
                            stockLocationDTO.setSkuCode(it.getTargetSkuCode());
                            stockLocationDTO.setPhysicalQty(it.getChangeQty());
                            return stockLocationDTO;
                        }).collect(Collectors.toList());
                locationBiz.maxSkuCheck(targetStockLocationList, origin, locationDTO);
            });
        }

        transferDetailDTOList.forEach(transferDetailDTO -> {
            Optional<SkuDTO> any =
                    skuDTOList.stream().filter(skuDTO -> skuDTO.getCode().equals(transferDetailDTO.getTargetSkuCode()))
                            .filter(skuDTO -> skuDTO.getCargoCode().equals(transferDetailDTO.getCargoCode())).findAny();
            if (!any.isPresent()) {
                throw new BaseException(BaseBizEnum.TIP, String.format("货主%s下商品%s信息不存在",
                        transferDetailDTO.getCargoCode(), transferDetailDTO.getTargetSkuCode()));
            }
            if (StrUtil.isNotBlank(transferDetailDTO.getTargetLocationCode())) {
                LocationDTO targetLocation = locationDTOList.stream()
                        .filter(locationDTO -> locationDTO.getCode().equals(transferDetailDTO.getTargetLocationCode()))
                        .findFirst().get();
                // 库位校验 如果数据库不存在该sku lot 信息，则把生成的未存库的sku lot 信息给到我去校验
                mixRuleCheckBiz.checkLocationMixRule(transferDetailDTO.getTargetLocationCode(),
                        targetLocation.getMixRuleCode(), transferDTO.getCargoCode(), transferDetailDTO.getTargetSkuCode(),
                        transferDetailDTO.getTargetSkuLotNo());
            }
        });

        TransferBO transferBO = new TransferBO();

        transferBO.setNeedERPCheck(cargoConfigBiz.stockNeedUpstreamCheck(transferDTO.getCargoCode()));

        // 状态为上游审核通过 需要回传上游 此判断要置于库存校验参数判断下方
        if (transferDTO.getStatus().equals(TransferStatusEnum.ERP_CHECKED.getCode())) {
            transferBO.setNeedERPCheck(true);
        } else if (transferDTO.getStatus().equals(TransferStatusEnum.IN_CHECKED.getCode())) {
            transferBO.setNeedERPCheck(false);
        }
        transferDTO.setOpBy(CurrentUserHolder.getUserName());
        transferDTO.setCompleteDate(System.currentTimeMillis());
        transferBO.setTransferDTO(transferDTO);
        transferBO.setTransferDetailDTOList(transferDetailDTOList);
        transferGtsService.confirm(transferBO);

        // 添加日志
        TransferLogDTO logDTO = new TransferLogDTO();
        logDTO.setTransferCode(transferDTO.getCode());
        logDTO.setCargoCode(transferDTO.getCargoCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(transferConfirmBizParam));
        logDTO.setOpContent("确认转移单:" + transferDTO.getCode());
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE.getType());
        remoteTransferLogClient.save(logDTO);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> commitAudit(TransferExamineBizParam param) {

        TransferParam transferParam = new TransferParam();
        transferParam.setCode(param.getCode());
        TransferDTO transferDTO = remoteTransferClient.get(transferParam);
        if (!transferDTO.getStatus().equals(TransferStatusEnum.CREATED.getCode())
                && !transferDTO.getStatus().equals(TransferStatusEnum.IN_REJECT.getCode())
                && !transferDTO.getStatus().equals(TransferStatusEnum.ERP_REJECT.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "转移单状态不正确，无法提交审核");
        }

        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(transferDTO.getCargoCode());
        // 淘天ERP驳回不允许修改，只能取消
        Boolean taoTianWarehouse = CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO);
        if (taoTianWarehouse) {
            if (TransferStatusEnum.ERP_REJECT.getCode().equalsIgnoreCase(transferDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("淘天上游审核驳回的转移单不允许提交审核");
            }
        }

        TransferDetailParam detailParam = new TransferDetailParam();
        detailParam.setTransferCode(transferDTO.getCode());
        detailParam.setCargoCode(transferDTO.getCargoCode());
        List<TransferDetailDTO> transferDetailDTOS = remoteTransferClient.listDetail(detailParam);
        if (CollectionUtils.isEmpty(transferDetailDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, "转移单明细为空，无法提交审核");
        }
        transferDTO.setStatus(TransferStatusEnum.IN_AUDIT.getCode());
        remoteTransferClient.update(transferDTO);
        return Result.success();
    }

    @Override
    public Result<Boolean> innerAudit(TransferExamineBizParam transferExamineBizParam) {
        TransferParam transferParam = new TransferParam();
        transferParam.setCode(transferExamineBizParam.getCode());
        TransferDTO transferDTO = remoteTransferClient.get(transferParam);
        if (ObjectUtil.isEmpty(transferDTO)) {
            throw new BaseException(BaseBizEnum.TIP, "转移单不存在");
        }

        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(transferDTO.getCode());
        List<TransferDetailDTO> transferDetailDTOS = remoteTransferClient.listDetail(transferDetailParam);
        if (CollectionUtil.isEmpty(transferDetailDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, "转移单明细不能为空");
        }
        auditInner(transferExamineBizParam, transferDTO, transferDetailDTOS);
        return Result.success();
    }

    /**
     * 基础校验
     */
    private void check(TransferUpdateBizParam param) {
        if (ObjectUtils.isEmpty(param)) {
            throw new BaseException(BaseBizEnum.NULL_ARGUMENT);
        }
    }

    /**
     * 校验商品信息
     *
     * @param param
     * @param skuDTOList
     */
    private void checkSkuInfo(TransferUpdateBizParam param, List<SkuDTO> skuDTOList) {
        // 商品信息校验
        param.getTransferDetailUpdateBizParamList().forEach(transferDetailUpdateBizParam -> {
            if (skuDTOList.stream()
                    .noneMatch(skuDTO -> skuDTO.getCode().equals(transferDetailUpdateBizParam.getOriginSkuCode()))) {
                throw new BaseException(BaseBizEnum.TIP,
                        String.format("来源商品%s信息不存在", transferDetailUpdateBizParam.getOriginSkuCode()));
            }
            if (skuDTOList.stream()
                    .noneMatch(skuDTO -> skuDTO.getCode().equals(transferDetailUpdateBizParam.getTargetSkuCode()))) {
                throw new BaseException(BaseBizEnum.TIP,
                        String.format("目标商品%s信息不存在", transferDetailUpdateBizParam.getTargetSkuCode()));
            }
        });
//        // 来源商品批次库位不能存在多条
//        long count = param.getTransferDetailUpdateBizParamList().stream()
//                .map(transferDetailUpdateBizParam -> StrUtil.join(StrUtil.COLON,
//                        transferDetailUpdateBizParam.getOriginSkuCode(),
//                        transferDetailUpdateBizParam.getOriginSkuLotNo(),
//                        transferDetailUpdateBizParam.getOriginLoactionCode()))
//                .distinct().count();
//        if (count != param.getTransferDetailUpdateBizParamList().size()) {
//            throw new BaseException(BaseBizEnum.TIP, "转移明细来源商品批次库存不能重复");
//        }
    }

    /**
     * 所有的库区库位类型必须是拣选区或者存储区
     *
     * @param param
     * @param locationDTOList
     * @param zoneDTOList
     */
    private void checkLocation(TransferUpdateBizParam param, List<LocationDTO> locationDTOList,
                               List<ZoneDTO> zoneDTOList) {
        // 库位类型校验
        locationDTOList.forEach(locationDTO -> {
            if (!LocationTypeEnum.LOCATION_TYPE_PICK.getType().equals(locationDTO.getType())
                    && !LocationTypeEnum.LOCATION_TYPE_STORE.getType().equals(locationDTO.getType())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("库位%s不是存储区或拣选区", locationDTO.getCode()));
            }
        });


        param.getTransferDetailUpdateBizParamList().forEach(transferDetailUpdateBizParam -> {
            Optional<LocationDTO> originLocation = locationDTOList.stream()
                    .filter(
                            locationDTO -> locationDTO.getCode().equals(transferDetailUpdateBizParam.getOriginLoactionCode()))
                    .findAny();
            Optional<LocationDTO> targetLocation = locationDTOList.stream()
                    .filter(
                            locationDTO -> locationDTO.getCode().equals(transferDetailUpdateBizParam.getTargetLocationCode()))
                    .findAny();
            // 库位信息必须存在
            if (!originLocation.isPresent()) {
                throw new BaseException(BaseBizEnum.TIP,
                        String.format("来源库位%s信息不存在", transferDetailUpdateBizParam.getOriginLoactionCode()));
            }
            if (StrUtil.isNotBlank(transferDetailUpdateBizParam.getTargetLocationCode()) && !targetLocation.isPresent()) {
                throw new BaseException(BaseBizEnum.TIP,
                        String.format("目标库位%s信息不存在", transferDetailUpdateBizParam.getTargetLocationCode()));
            }
            // 库区正次品属性
            targetLocation.ifPresent(locationDTO -> {

                if (locationDTO.getStatus().equals(LocationStatusEnum.STATUS_DISABLED.getStatus())) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("库位%s已禁用", locationDTO.getCode()));
                }

                ZoneDTO zoneDTO =
                        zoneDTOList.stream().filter(it -> it.getCode().equals(locationDTO.getZoneCode())).findFirst().orElse(null);
                if (zoneDTO == null) {
                    throw ExceptionUtil.exceptionWithMessage("库区不存在");
                }

                if (zoneDTO.getStatus().equals(ZoneStatusEnum.STATUS_DISABLED.getStatus())) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("库区%s已禁用", zoneDTO.getCode()));
                }

                if (!transferDetailUpdateBizParam.getTargetSkuQuality().equals(zoneDTO.getSkuQuality())) {
                    throw new BaseException(BaseBizEnum.TIP,
                            String.format("目标库位%s商品属性不匹配", transferDetailUpdateBizParam.getTargetLocationCode()));
                }
            });
        });
    }

    /**
     * 基础校验
     *
     * @param param
     */
    private void check(TransferAddBizParam param) {
        if (StringUtils.isEmpty(param.getCargoCode())) {
            throw new BaseException(BaseBizEnum.TIP, "请选择货主");
        }
        if (StringUtils.isEmpty(param.getReason())) {
            throw new BaseException(BaseBizEnum.TIP, "请选择转移原因");
        }
        if (StringUtils.isEmpty(param.getNote())) {
            throw new BaseException(BaseBizEnum.TIP, "请添加转移描述");
        }
        // 原因校验
        TransferReasonEnum.getEnum(param.getReason());
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(param.getCargoCode());
        if (null == cargoOwnerDTO) {
            throw new BaseException(BaseBizEnum.TIP, "货主信息不存在");
        }
    }

    @Override
    public Result<String> withdrawAutoTransferRetry() {
        TransferParam transferParam = new TransferParam();
        transferParam.setStatusList(ListUtil.toList(TransferStatusEnum.IN_AUDIT.getCode(), TransferStatusEnum.CREATED.getCode()));
        transferParam.setOrderTag(TransferTagEnum.enumToNum(TransferTagEnum.AUTO_WITHDRAW_TRANSFER));
        List<TransferDTO> transferDTOList = remoteTransferClient.list(transferParam);
        if (CollectionUtil.isEmpty(transferDTOList)) return Result.success();

        for (TransferDTO transferDTO : transferDTOList) {
            remoteLockSupportClient.execute((Supplier<Object>) () -> {
                try {
                    TransferDetailParam transferDetailParam = new TransferDetailParam();
                    transferDetailParam.setTransferCode(transferDTO.getCode());
                    List<TransferDetailDTO> transferDetailDTOS = remoteTransferClient.listDetail(transferDetailParam);
                    if (CollectionUtil.isEmpty(transferDetailDTOS)) {
                        transferDTO.setStatus(TransferStatusEnum.CANCEL.getCode());
                        remoteTransferClient.update(transferDTO);
                        return true;
                    }

                    List<String> skuCodeList = transferDetailDTOS.stream().map(TransferDetailDTO::getOriginSkuCode).distinct().collect(Collectors.toList());
                    List<String> skuLotNoList = transferDetailDTOS.stream().map(TransferDetailDTO::getOriginSkuLotNo).distinct().collect(Collectors.toList());
                    List<String> locationList = transferDetailDTOS.stream().map(TransferDetailDTO::getOriginLoactionCode).distinct().collect(Collectors.toList());


                    StockLocationParam stockLocationParam = new StockLocationParam();
                    stockLocationParam.setHasAvailableQty(true);
                    stockLocationParam.setCargoCode(transferDTO.getCargoCode());
                    stockLocationParam.setZoneTypeList(ListUtil.toList(ZoneTypeEnum.ZONE_TYPE_STORE.getType(), ZoneTypeEnum.ZONE_TYPE_PICK.getType()));
                    stockLocationParam.setSelectColumnList(ListUtil.toList("SKU_CODE", "SKU_LOT_NO", "LOCATION_CODE", "AVAILABLE_QTY"));
                    stockLocationParam.setSkuCodeList(skuCodeList);
                    stockLocationParam.setSkuLotNoList(skuLotNoList);
                    stockLocationParam.setLocationCodeList(locationList);
                    List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getList(stockLocationParam);
                    if (CollectionUtil.isEmpty(stockLocationDTOList)) {
                        transferDTO.setStatus(TransferStatusEnum.CANCEL.getCode());
                        remoteTransferClient.update(transferDTO);
                        return true;
                    }

                    SkuLotParam skuLotParam = new SkuLotParam();
                    skuLotParam.setCargoCode(transferDTO.getCargoCode());
                    skuLotParam.setCodeList(stockLocationDTOList.stream().map(StockLocationDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
                    List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
                    if (CollectionUtil.isEmpty(skuLotDTOList)) {
                        throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCode(), "批次信息不存在"));
                    }

                    SkuParam skuParam = new SkuParam();
                    skuParam.setCargoCode(transferDTO.getCargoCode());
                    skuParam.setCodeList(stockLocationDTOList.stream().map(StockLocationDTO::getSkuCode).distinct().collect(Collectors.toList()));
                    List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
                    if (CollectionUtil.isEmpty(skuDTOList))
                        throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCargoCode(), "商品信息不存在"));

                    LotRuleParam lotRuleParam = new LotRuleParam();
                    lotRuleParam.setCodeList(skuDTOList.stream().map(SkuDTO::getLotRuleCode).distinct().collect(Collectors.toList()));
                    List<LotRuleDTO> lotRuleDTOList = remoteLotRuleClient.getList(lotRuleParam);
                    if (CollectionUtil.isEmpty(lotRuleDTOList))
                        throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCargoCode(), "批次属性规则不存在"));

                    Map<String, SkuLotDTO> skuLotDTOMap = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity()));
                    Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity()));
                    Map<String, LotRuleDTO> lotRuleDTOMap = lotRuleDTOList.stream().collect(Collectors.toMap(LotRuleDTO::getCode, Function.identity()));

                    // 过滤
                    List<WithdrawAutoTransferStockBO> boList = stockLocationDTOList.stream()
                            .flatMap(stockLocationDTO -> {
                                WithdrawAutoTransferStockBO withdrawAutoTransferStockBO = new WithdrawAutoTransferStockBO();
                                SkuLotDTO skuLotDTO = skuLotDTOMap.get(stockLocationDTO.getSkuLotNo());
                                if (null == skuLotDTO)
                                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCode(), "批次信息不存在", stockLocationDTO.getSkuLotNo()));
                                SkuDTO skuDTO = skuDTOMap.get(stockLocationDTO.getSkuCode());
                                if (null == skuDTO)
                                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCode(), "商品信息不存在", stockLocationDTO.getSkuCode()));
                                withdrawAutoTransferStockBO.setWarehouseCode(skuDTO.getWarehouseCode());
                                withdrawAutoTransferStockBO.setInventoryType(skuLotDTO.getInventoryType());
                                withdrawAutoTransferStockBO.setCargoCode(skuDTO.getCargoCode());
                                withdrawAutoTransferStockBO.setSkuCode(skuDTO.getCode());
                                withdrawAutoTransferStockBO.setLocationCode(stockLocationDTO.getLocationCode());
                                withdrawAutoTransferStockBO.setSkuLotNo(stockLocationDTO.getSkuLotNo());
                                withdrawAutoTransferStockBO.setSkuQuality(skuLotDTO.getSkuQuality());
                                withdrawAutoTransferStockBO.setExpireDate(skuLotDTO.getExpireDate());
                                withdrawAutoTransferStockBO.setWithdrawDate(DateTime.of(skuLotDTO.getExpireDate()).offset(DateField.DAY_OF_YEAR, -skuDTO.getWithdrawCycle()).getTime());
                                withdrawAutoTransferStockBO.setAvailableQty(stockLocationDTO.getAvailableQty());
                                return Stream.of(withdrawAutoTransferStockBO);
                            })
                            .collect(Collectors.toList());

                    if (CollectionUtil.isEmpty(boList)) return true;

                    // 考虑一级库存
                    skuCodeList = boList.stream().map(WithdrawAutoTransferStockBO::getSkuCode).distinct().collect(Collectors.toList());
                    StockParam stockParam = new StockParam();
                    stockParam.setCargoCode(transferDTO.getCargoCode());
                    stockParam.setSkuCodeList(skuCodeList);
                    List<StockDTO> stockDTOList = remoteStockClient.getList(stockParam);
                    Map<String, List<StockDTO>> stockMap = stockDTOList.stream().collect(Collectors.groupingBy(StockDTO::getSkuCode));

                    // 禁售日子越早优先转
                    boList.sort(new WithdrawAutoTransferStockBOComparator());


                    for (WithdrawAutoTransferStockBO withdrawAutoTransferStockBO : boList) {
                        List<StockDTO> stockDTOS = stockMap.get(withdrawAutoTransferStockBO.getSkuCode());
                        for (StockDTO stockDTO : stockDTOS) {
                            if (stockDTO.getSkuQuality().equalsIgnoreCase(withdrawAutoTransferStockBO.getSkuQuality())) {
                                if (withdrawAutoTransferStockBO.getAvailableQty().compareTo(stockDTO.getAvailableQty()) >= 0) {
                                    withdrawAutoTransferStockBO.setAvailableQty(stockDTO.getAvailableQty());
                                    stockDTO.setAvailableQty(BigDecimal.ZERO);
                                } else {
                                    stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(withdrawAutoTransferStockBO.getAvailableQty()));
                                }
                            }
                        }
                    }

                    // 过滤掉为0的
                    boList = boList.stream().filter(withdrawAutoTransferStockBO -> withdrawAutoTransferStockBO.getAvailableQty().compareTo(BigDecimal.ZERO) > 0)
                            .collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(boList)) {
                        return true;
                    }

                    TransferUpdateBizParam transferUpdateBizParam = new TransferUpdateBizParam();
                    transferUpdateBizParam.setTransferCode(transferDTO.getCode());
                    List<TransferDetailUpdateBizParam> collect = boList.stream().map(it -> {
                        TransferDetailUpdateBizParam transferDetailUpdateBizParam = new TransferDetailUpdateBizParam();
                        transferDetailUpdateBizParam.setSkuCode(it.getSkuCode());
                        transferDetailUpdateBizParam.setOriginSkuCode(it.getSkuCode());
                        transferDetailUpdateBizParam.setTargetSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
                        String originInventoryType = it.getInventoryType();
                        if (StrUtil.isBlank(originInventoryType)) {
                            throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCargoCode(), transferDTO.getCode(), "批次信息残次等级未维护", it.getInventoryType()));
                        }
                        InventoryTypeEnum inventoryTypeEnum = InventoryTypeEnum.getEnum(it.getInventoryType());
                        InventoryTypeEnum targetInventoryType = adjustMapping.get(inventoryTypeEnum);
                        if (targetInventoryType == null)
                            throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCargoCode(), transferDTO.getCode(), "调整残次等级关系未维护", it.getInventoryType()));
                        transferDetailUpdateBizParam.setTargetInventoryType(targetInventoryType.getCode());
                        SkuLotDTO skuLotDTO = skuLotDTOMap.get(it.getSkuLotNo());
                        SkuDTO skuDTO = skuDTOMap.get(it.getSkuCode());
                        LotRuleDTO lotRuleDTO = lotRuleDTOMap.get(skuDTO.getLotRuleCode());
                        if (null == lotRuleDTO)
                            throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCargoCode(), transferDTO.getCode(), "批次属性规则不存在", skuDTO.getLotRuleCode()));
                        if (skuLotDTO.getReceiveDate() != null && skuLotDTO.getReceiveDate() > 0) {
                            transferDetailUpdateBizParam.setTargetReceiveDate(skuLotDTO.getReceiveDate());
                        } else {
                            if (lotRuleDTO.getRuleDetailList().stream()
                                    .filter(lotRuleDetailDTO -> lotRuleDetailDTO.getPropKey().equalsIgnoreCase(LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey()))
                                    .anyMatch(lotRuleDetailDTO -> lotRuleDetailDTO.getEnableControl().equals(LotRuleRwControlEnum.MUST.getCode()))
                            ) {
                                transferDetailUpdateBizParam.setTargetReceiveDate(DateUtil.beginOfDay(DateTime.now()).getTime());
                            }
                        }
                        transferDetailUpdateBizParam.setTargetManufDate(skuLotDTO.getManufDate());
                        transferDetailUpdateBizParam.setTargetExpireDate(skuLotDTO.getExpireDate());
                        transferDetailUpdateBizParam.setTargetProductionNo(skuLotDTO.getProductionNo());
                        transferDetailUpdateBizParam.setTargetExternalLinkBillNo(skuLotDTO.getExternalLinkBillNo());
                        transferDetailUpdateBizParam.setChangeQty(it.getAvailableQty());
                        transferDetailUpdateBizParam.setOriginSkuLotNo(skuLotDTO.getSkuCode());
                        transferDetailUpdateBizParam.setTargetSkuCode(skuLotDTO.getSkuCode());
                        transferDetailUpdateBizParam.setOriginSkuLotNo(skuLotDTO.getCode());
                        transferDetailUpdateBizParam.setOriginLoactionCode(it.getLocationCode());
                        transferDetailUpdateBizParam.setOriginSkuQuality(skuLotDTO.getSkuQuality());
                        transferDetailUpdateBizParam.setOriginInventoryType(skuLotDTO.getInventoryType());
                        transferDetailUpdateBizParam.setOriginReceiveDate(skuLotDTO.getReceiveDate());
                        transferDetailUpdateBizParam.setOriginManufDate(skuLotDTO.getManufDate());
                        transferDetailUpdateBizParam.setExpireDate(skuLotDTO.getExpireDate());
                        transferDetailUpdateBizParam.setOriginProductionNo(skuLotDTO.getProductionNo());

                        transferDetailUpdateBizParam.setOriginValidityCode(skuLotDTO.getValidityCode());
                        transferDetailUpdateBizParam.setTargetValidityCode(skuLotDTO.getValidityCode());
                        transferDetailUpdateBizParam.setTargetBoxCode(skuLotDTO.getBoxCode());
                        transferDetailUpdateBizParam.setTargetPalletCode(skuLotDTO.getPalletCode());

                        transferDetailUpdateBizParam.setRemark("禁售自动转残");
                        return transferDetailUpdateBizParam;
                    }).collect(Collectors.toList());
                    transferUpdateBizParam.setTransferDetailUpdateBizParamList(collect);

                    // 先审核失败
                    TransferExamineBizParam transferExamineBizParam = new TransferExamineBizParam();
                    transferExamineBizParam.setCode(transferDTO.getCode());
                    transferExamineBizParam.setPass(AuditEnum.REFUSE.getCode());
                    transferExamineBizParam.setRemark("审核失败");
                    innerAudit(transferExamineBizParam);

                    // 再更新明细
                    updateTransferDetail(transferUpdateBizParam);


                    // 提交审核
                    TransferExamineBizParam param = new TransferExamineBizParam();
                    param.setCode(transferDTO.getCode());
                    commitAudit(param);

                    // 自动审核通过
                    transferExamineBizParam = new TransferExamineBizParam();
                    transferExamineBizParam.setCode(transferDTO.getCode());
                    transferExamineBizParam.setPass(AuditEnum.PASS.getCode());
                    transferExamineBizParam.setRemark("自动审核通过");
                    innerAudit(transferExamineBizParam);
                } catch (Exception exception) {
                    log.info(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCode(), exception.getMessage()));
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCode(), exception.getMessage()));
                }
                return true;
            }, ListUtil.toList(StrUtil.join(StrUtil.COLON, transferDTO.getWarehouseCode(), transferDTO.getCargoCode(), withdrawAutoTransferLockKey)));
        }
        return Result.success();
    }


    @Override
    public Result<String> withdrawAutoTransfer() throws InterruptedException {
        List<CargoOwnerDTO> allCargoOwner = remoteCargoOwnerClient.getAllCargoOwner(new CargoOwnerParam());
        if (CollectionUtil.isEmpty(allCargoOwner)) {
            return Result.success("没有货主需要处理");
        }
        for (CargoOwnerDTO cargoOwnerDTO : allCargoOwner) {
            try {
                withdrawAutoTransfer(cargoOwnerDTO.getCode());
            } catch (Exception exception) {
                log.error(exception.getMessage(), exception);
            }
        }
        return Result.success();
    }

    @Override
    public Result<String> withdrawAutoTransfer(String cargoCode) throws InterruptedException {
        try {
            CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(cargoCode);
            return withdrawAutoTransfer(cargoOwnerDTO);
        } catch (Exception exception) {
            WechatUtil.sendMessage(exception.getMessage(), wmsOtherConfig.getWithdrawAutoTransferWarningUrl());
        }
        return Result.success();
    }

    @Override
    public Result<String> withdrawAutoTransfer(CargoOwnerDTO cargoOwnerDTO) throws InterruptedException {
        if (null == cargoOwnerDTO) return Result.success();

        log.info(StrUtil.join(StrUtil.TAB, "withdrawAutoTransferStart", cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), DateTime.now()));

        if (null == wmsOtherConfig.getWithdrawAutoTransferHourLimit()) {
            wmsOtherConfig.setWithdrawAutoTransferHourLimit(23);
        }

        CurrentUserHolder.setUserName("System");
        if (CollectionUtil.isEmpty(wmsOtherConfig.getWithdrawAutoTransferWarehouseList())) return Result.success("没有仓库需要处理禁售自动转残");
        if (!wmsOtherConfig.getWithdrawAutoTransferWarehouseList().contains(cargoOwnerDTO.getWarehouseCode())) {
            return Result.success("不是淘天仓库不需要处理");
        }

        // 需要处理的时间【根据当前执行时间计算需要处理禁售的时间】
        DateTime now = DateTime.now();
        int hour = now.hour(true);
        DateTime withdrawTimeEnd;
        if (23 == hour) {
            withdrawTimeEnd = DateUtil.endOfDay(DateUtil.offset(now, DateField.DAY_OF_YEAR, 1));
        } else {
            withdrawTimeEnd = DateUtil.endOfDay(now);
        }

        if (CollectionUtil.isEmpty(wmsOtherConfig.getWithdrawAutoTransferInventoryTypeConfig()))
            return Result.success("没有配置残次等级列表");
        if (CollectionUtil.isEmpty(wmsOtherConfig.getWithdrawAutoTransferInventoryTypeConfig().get(CurrentRouteHolder.getWarehouseCode())))
            return Result.success("当前仓库没有配置残次等级列表" + CurrentRouteHolder.getWarehouseCode());

        String join = StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), withdrawAutoTransferLockKey);
        RLock fairLock = redissonClient.getFairLock(join);
        boolean locked = fairLock.tryLock(5, 30, TimeUnit.SECONDS);
        if (locked) {
            try {
                StockLocationParam stockLocationParam = new StockLocationParam();
                stockLocationParam.setHasAvailableQty(true);
                stockLocationParam.setCargoCode(cargoOwnerDTO.getCode());
                stockLocationParam.setZoneTypeList(ListUtil.toList(ZoneTypeEnum.ZONE_TYPE_STORE.getType(), ZoneTypeEnum.ZONE_TYPE_PICK.getType()));
                stockLocationParam.setSelectColumnList(ListUtil.toList("SKU_CODE", "SKU_LOT_NO", "LOCATION_CODE", "AVAILABLE_QTY"));
                List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getList(stockLocationParam);
                if (CollectionUtil.isEmpty(stockLocationDTOList)) return Result.success();

                SkuLotParam skuLotParam = new SkuLotParam();
                skuLotParam.setCargoCode(cargoOwnerDTO.getCode());
                skuLotParam.setCodeList(stockLocationDTOList.stream().map(StockLocationDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
                List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
                if (CollectionUtil.isEmpty(skuLotDTOList)) {
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), "批次信息不存在"));
                }

                SkuParam skuParam = new SkuParam();
                skuParam.setCargoCode(cargoOwnerDTO.getCode());
                skuParam.setCodeList(stockLocationDTOList.stream().map(StockLocationDTO::getSkuCode).distinct().collect(Collectors.toList()));
                List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
                if (CollectionUtil.isEmpty(skuDTOList)) {
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), "商品信息不存在"));
                }

                LotRuleParam lotRuleParam = new LotRuleParam();
                lotRuleParam.setCodeList(skuDTOList.stream().map(SkuDTO::getLotRuleCode).distinct().collect(Collectors.toList()));
                List<LotRuleDTO> lotRuleDTOList = remoteLotRuleClient.getList(lotRuleParam);
                if (CollectionUtil.isEmpty(lotRuleDTOList)) {
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), "批次属性规则不存在"));
                }

                Map<String, SkuLotDTO> skuLotDTOMap = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity()));
                Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity()));
                Map<String, LotRuleDTO> lotRuleDTOMap = lotRuleDTOList.stream().collect(Collectors.toMap(LotRuleDTO::getCode, Function.identity()));

                // 过滤
                List<WithdrawAutoTransferStockBO> boList = stockLocationDTOList.stream()
                        .flatMap(stockLocationDTO -> {
                            WithdrawAutoTransferStockBO withdrawAutoTransferStockBO = new WithdrawAutoTransferStockBO();
                            SkuLotDTO skuLotDTO = skuLotDTOMap.get(stockLocationDTO.getSkuLotNo());
                            if (null == skuLotDTO)
                                throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), "批次信息不存在", stockLocationDTO.getSkuLotNo()));
                            SkuDTO skuDTO = skuDTOMap.get(stockLocationDTO.getSkuCode());
                            if (null == skuDTO)
                                throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), "商品信息不存在", stockLocationDTO.getSkuCode()));
                            if (!skuDTO.getTaotian()) {
                                return Stream.empty();
                            }
                            if (SkuLifeCtrlEnum.SKU_LIFE_CTRL_NO.getCode().equals(skuDTO.getIsLifeMgt())) {
                                return Stream.empty();
                            }
                            withdrawAutoTransferStockBO.setWarehouseCode(skuDTO.getWarehouseCode());
                            withdrawAutoTransferStockBO.setInventoryType(skuLotDTO.getInventoryType());
                            withdrawAutoTransferStockBO.setCargoCode(skuDTO.getCargoCode());
                            withdrawAutoTransferStockBO.setSkuCode(skuDTO.getCode());
                            withdrawAutoTransferStockBO.setLocationCode(stockLocationDTO.getLocationCode());
                            withdrawAutoTransferStockBO.setSkuLotNo(stockLocationDTO.getSkuLotNo());
                            withdrawAutoTransferStockBO.setSkuQuality(skuLotDTO.getSkuQuality());
                            withdrawAutoTransferStockBO.setExpireDate(skuLotDTO.getExpireDate());
                            withdrawAutoTransferStockBO.setWithdrawDate(DateTime.of(skuLotDTO.getExpireDate()).offset(DateField.DAY_OF_YEAR, -skuDTO.getWithdrawCycle()).getTime());
                            withdrawAutoTransferStockBO.setAvailableQty(stockLocationDTO.getAvailableQty());
                            return Stream.of(withdrawAutoTransferStockBO);
                        })
                        .filter(withdrawAutoTransferStockBO -> withdrawTimeEnd.isAfter(DateTime.of(withdrawAutoTransferStockBO.getWithdrawDate())))
                        .filter(withdrawAutoTransferStockBO -> wmsOtherConfig.getWithdrawAutoTransferInventoryTypeConfig().get(withdrawAutoTransferStockBO.getWarehouseCode()).contains(withdrawAutoTransferStockBO.getInventoryType()))
                        .collect(Collectors.toList());

                if (CollectionUtil.isEmpty(boList)) return Result.success();

                // 50条明细生成一个转移单
                for (List<WithdrawAutoTransferStockBO> withdrawAutoTransferStockBOS : ListUtil.split(boList, 50)) {
                    TransferAddBizParam transferAddBizParam = new TransferAddBizParam();
                    transferAddBizParam.setWarehouseCode(cargoOwnerDTO.getWarehouseCode());
                    transferAddBizParam.setCargoCode(cargoOwnerDTO.getCode());
                    transferAddBizParam.setReason(TransferReasonEnum.OTHER.getCode());
                    transferAddBizParam.setNote("淘天要求自动执行禁售转残");
                    transferAddBizParam.setOrderTag(TransferTagEnum.enumToNum(TransferTagEnum.AUTO_WITHDRAW_TRANSFER));

                    // 考虑一级库存
                    List<String> skuCodeList = withdrawAutoTransferStockBOS.stream().map(WithdrawAutoTransferStockBO::getSkuCode).distinct().collect(Collectors.toList());
                    StockParam stockParam = new StockParam();
                    stockParam.setCargoCode(cargoOwnerDTO.getCode());
                    stockParam.setSkuCodeList(skuCodeList);
                    List<StockDTO> stockDTOList = remoteStockClient.getList(stockParam);
                    Map<String, List<StockDTO>> stockMap = stockDTOList.stream().collect(Collectors.groupingBy(StockDTO::getSkuCode));

                    // 禁售日子越早优先转
                    withdrawAutoTransferStockBOS.sort(new WithdrawAutoTransferStockBOComparator());

                    for (WithdrawAutoTransferStockBO withdrawAutoTransferStockBO : withdrawAutoTransferStockBOS) {
                        List<StockDTO> stockDTOS = stockMap.get(withdrawAutoTransferStockBO.getSkuCode());
                        for (StockDTO stockDTO : stockDTOS) {
                            if (stockDTO.getSkuQuality().equalsIgnoreCase(withdrawAutoTransferStockBO.getSkuQuality())) {
                                if (withdrawAutoTransferStockBO.getAvailableQty().compareTo(stockDTO.getAvailableQty()) >= 0) {
                                    withdrawAutoTransferStockBO.setAvailableQty(stockDTO.getAvailableQty());
                                    stockDTO.setAvailableQty(BigDecimal.ZERO);
                                } else {
                                    stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(withdrawAutoTransferStockBO.getAvailableQty()));
                                }
                            }
                        }
                    }

                    // 过滤掉为0的
                    withdrawAutoTransferStockBOS = withdrawAutoTransferStockBOS.stream().filter(withdrawAutoTransferStockBO -> withdrawAutoTransferStockBO.getAvailableQty().compareTo(BigDecimal.ZERO) > 0)
                            .collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(withdrawAutoTransferStockBOS)) {
                        continue;
                    }

                    String code = addTransfer(transferAddBizParam).getData();

                    TransferUpdateBizParam transferUpdateBizParam = new TransferUpdateBizParam();
                    transferUpdateBizParam.setTransferCode(code);
                    List<TransferDetailUpdateBizParam> collect = withdrawAutoTransferStockBOS.stream().map(it -> {
                        TransferDetailUpdateBizParam transferDetailUpdateBizParam = new TransferDetailUpdateBizParam();
                        transferDetailUpdateBizParam.setSkuCode(it.getSkuCode());
                        transferDetailUpdateBizParam.setOriginSkuCode(it.getSkuCode());
                        transferDetailUpdateBizParam.setTargetSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
                        String originInventoryType = it.getInventoryType();
                        if (StrUtil.isBlank(originInventoryType)) {
                            throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), code, "批次信息残次等级未维护", it.getInventoryType()));
                        }
                        InventoryTypeEnum inventoryTypeEnum = InventoryTypeEnum.getEnum(it.getInventoryType());
                        InventoryTypeEnum targetInventoryType = adjustMapping.get(inventoryTypeEnum);
                        if (targetInventoryType == null)
                            throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), code, "调整残次等级关系未维护", it.getInventoryType()));
                        transferDetailUpdateBizParam.setTargetInventoryType(targetInventoryType.getCode());
                        SkuLotDTO skuLotDTO = skuLotDTOMap.get(it.getSkuLotNo());
                        SkuDTO skuDTO = skuDTOMap.get(it.getSkuCode());
                        LotRuleDTO lotRuleDTO = lotRuleDTOMap.get(skuDTO.getLotRuleCode());
                        if (null == lotRuleDTO)
                            throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.COLON, cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), code, "批次属性规则不存在", skuDTO.getLotRuleCode()));
                        if (skuLotDTO.getReceiveDate() != null && skuLotDTO.getReceiveDate() > 0) {
                            transferDetailUpdateBizParam.setTargetReceiveDate(skuLotDTO.getReceiveDate());
                        } else {
                            if (lotRuleDTO.getRuleDetailList().stream()
                                    .filter(lotRuleDetailDTO -> lotRuleDetailDTO.getPropKey().equalsIgnoreCase(LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey()))
                                    .anyMatch(lotRuleDetailDTO -> lotRuleDetailDTO.getEnableControl().equals(LotRuleRwControlEnum.MUST.getCode()))
                            ) {
                                transferDetailUpdateBizParam.setTargetReceiveDate(DateUtil.beginOfDay(now).getTime());
                            }
                        }
                        transferDetailUpdateBizParam.setTargetManufDate(skuLotDTO.getManufDate());
                        transferDetailUpdateBizParam.setTargetExpireDate(skuLotDTO.getExpireDate());
                        processDate(transferDetailUpdateBizParam, skuDTO);
                        transferDetailUpdateBizParam.setTargetProductionNo(skuLotDTO.getProductionNo());
                        transferDetailUpdateBizParam.setTargetExternalLinkBillNo(skuLotDTO.getExternalLinkBillNo());
                        transferDetailUpdateBizParam.setChangeQty(it.getAvailableQty());
                        transferDetailUpdateBizParam.setOriginSkuLotNo(skuLotDTO.getSkuCode());
                        transferDetailUpdateBizParam.setTargetSkuCode(skuLotDTO.getSkuCode());
                        transferDetailUpdateBizParam.setOriginSkuLotNo(skuLotDTO.getCode());
                        transferDetailUpdateBizParam.setOriginLoactionCode(it.getLocationCode());
                        transferDetailUpdateBizParam.setOriginSkuQuality(skuLotDTO.getSkuQuality());
                        transferDetailUpdateBizParam.setOriginInventoryType(skuLotDTO.getInventoryType());
                        transferDetailUpdateBizParam.setOriginReceiveDate(skuLotDTO.getReceiveDate());
                        transferDetailUpdateBizParam.setOriginManufDate(skuLotDTO.getManufDate());
                        transferDetailUpdateBizParam.setExpireDate(skuLotDTO.getExpireDate());
                        transferDetailUpdateBizParam.setOriginProductionNo(skuLotDTO.getProductionNo());

                        transferDetailUpdateBizParam.setOriginValidityCode(skuLotDTO.getValidityCode());
                        transferDetailUpdateBizParam.setTargetValidityCode(skuLotDTO.getValidityCode());
                        transferDetailUpdateBizParam.setTargetBoxCode(skuLotDTO.getBoxCode());
                        transferDetailUpdateBizParam.setTargetPalletCode(skuLotDTO.getPalletCode());


                        transferDetailUpdateBizParam.setRemark("禁售自动转残");
                        return transferDetailUpdateBizParam;
                    }).collect(Collectors.toList());
                    transferUpdateBizParam.setTransferDetailUpdateBizParamList(collect);
                    updateTransferDetail(transferUpdateBizParam);

                    try {
                        // 提交审核
                        TransferExamineBizParam param = new TransferExamineBizParam();
                        param.setCode(code);
                        commitAudit(param);

                        // 自动审核通过
                        TransferExamineBizParam transferExamineBizParam = new TransferExamineBizParam();
                        transferExamineBizParam.setCode(code);
                        transferExamineBizParam.setPass(AuditEnum.PASS.getCode());
                        transferExamineBizParam.setRemark("自动审核通过");
                        innerAudit(transferExamineBizParam);
                    } catch (Exception exception) {
                        log.error(exception.getMessage(), exception);
                    }
                }
            } catch (Exception exception) {
                log.error(exception.getMessage(), exception);
                throw exception;
            } finally {
                log.info(StrUtil.join(StrUtil.TAB, "withdrawAutoTransferEnd", cargoOwnerDTO.getWarehouseCode(), cargoOwnerDTO.getCode(), DateTime.now()));
                fairLock.unlock();
            }
        } else {
            throw ExceptionUtil.SYSTEM_BUSY;
        }

        return Result.success("");
    }

    public void processDate(TransferDetailUpdateBizParam param,SkuDTO skuDTO) {
        try {
            // 使用失效日期计算生产日期
            Long manufDate = param.getTargetExpireDate() - Long.valueOf(skuDTO.getLifeCycle()) * CommonConstantUtil.DAY_MILLISECONDS;
            param.setTargetManufDate(manufDate);
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
        }
    }

    @Data
    private static class WithdrawAutoTransferStockBO {
        @ApiModelProperty(value = "仓库编码")
        private String warehouseCode;

        @ApiModelProperty(value = "货主编码")
        private String cargoCode;

        @ApiModelProperty(value = "商品编码")
        private String skuCode;

        @ApiModelProperty(value = "库位编码")
        private String locationCode;

        @ApiModelProperty(value = "批次ID")
        private String skuLotNo;

        @ApiModelProperty(value = "商品属性")
        private String skuQuality;

        @ApiModelProperty(value = "商品属性")
        private Long withdrawDate;

        @ApiModelProperty(value = "失效/过期日期")
        private Long expireDate;

        @ApiModelProperty(value = "残次等级")
        private String inventoryType;

        @ApiModelProperty(value = "可用数量 实物库存-冻结-占用=可用数")
        private BigDecimal availableQty;
    }

    private static class WithdrawAutoTransferStockBOComparator implements Comparator<WithdrawAutoTransferStockBO> {
        @Override
        public int compare(WithdrawAutoTransferStockBO o1, WithdrawAutoTransferStockBO o2) {
            if (o1.getWithdrawDate() < o2.getWithdrawDate()) return -1;
            if (o1.getWithdrawDate() > o2.getWithdrawDate()) return 1;
            if (o1.getExpireDate() < o2.getExpireDate()) return -1;
            if (o1.getExpireDate() > o2.getExpireDate()) return 1;
            if (o1.getAvailableQty().compareTo(o2.getAvailableQty()) > 0) return -1;
            if (o1.getAvailableQty().compareTo(o2.getAvailableQty()) < 0) return 1;
            return 0;
        }
    }


    public static void main(String[] args) {
        WithdrawAutoTransferStockBO first = new WithdrawAutoTransferStockBO();
        first.setWithdrawDate(10000L);
        first.setExpireDate(2000L);
        first.setAvailableQty(BigDecimal.TEN);
        WithdrawAutoTransferStockBO second = new WithdrawAutoTransferStockBO();
        second.setWithdrawDate(10000L);
        second.setExpireDate(3000L);
        second.setAvailableQty(BigDecimal.ONE);
        ArrayList<WithdrawAutoTransferStockBO> list = ListUtil.toList(first, second);
        list.sort(new WithdrawAutoTransferStockBOComparator());
        System.out.println(JSONUtil.toJsonStr(list));
    }

    @Override
    public Result<List<IdNameVO>> queryTag(TransferParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        TransferDTO transferDTO = remoteTransferClient.get(param);
        if (transferDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "转移单不存在");
        }
        Set<TransferTagEnum> transferTagEnums = TransferTagEnum.NumToEnum(transferDTO.getOrderTag());
        return Result.success(IdNameVO.build(new ArrayList<>(transferTagEnums), "code", "desc"));
    }

    @Override
    public Result<Boolean> modifyTag(TransferParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        TransferDTO transferDTO = remoteTransferClient.get(param);
        if (transferDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "转移单不存在");
        }
        if (!TransferStatusEnum.CREATED.getCode().equalsIgnoreCase(transferDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "非创建状态不允许打标");
        }
        if (CollectionUtils.isEmpty(param.getOrderTagList())) {
            transferDTO.setOrderTag(0);
        } else {
            transferDTO.setOrderTag(TransferTagEnum.queryParamListToInteger(param.getOrderTagList()));
        }

        Result<Boolean> success = Result.success(remoteTransferClient.update(transferDTO));
        // 添加日志
        String content = StrUtil.join(StrUtil.EMPTY, "修改标记为：", TransferTagEnum.NumToEnum(transferDTO.getOrderTag()).stream().map(TransferTagEnum::getDesc).collect(Collectors.joining("|")));
        TransferLogDTO logDTO = new TransferLogDTO();
        logDTO.setTransferCode(transferDTO.getCode());
        logDTO.setCargoCode(transferDTO.getCargoCode());
        logDTO.setOpRemark(content);
        logDTO.setOpContent(content);
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_INSERT.getType());
        remoteTransferLogClient.save(logDTO);

        return success;

    }
}