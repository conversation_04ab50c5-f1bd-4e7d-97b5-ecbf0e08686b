package com.dt.platform.wms.biz.excel.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.domain.bill.dto.tally.TallyDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2021/4/2 11:15
 */
@Data
public class ReceiptImportBO implements Serializable {

    @ExcelProperty(value = "*单据号")
    private String billNo;

    @ExcelProperty(value = "*容器号")
    private String contCode;

    @ExcelProperty(value = "*SKU编码")
    private String skuCode;

    @ExcelProperty(value = "*数量")
    private BigDecimal qty;

    @ExcelProperty(value = "入库日期")
    private String receiveTime;

    @ExcelProperty(value = "生产日期")
    private String manufTime;

    @ExcelProperty(value = "失效日期")
    private String expireTime;

    @ExcelProperty(value = "效期暗码")
    private String validityCode;

    @ExcelProperty(value = "*商品属性")
    private String skuQualityName;

    @ExcelProperty(value = "残次等级")
    private String inventoryTypeName;

    @ExcelProperty(value = "生产批次号")
    private String productionNo;

    @ExcelProperty(value = "入库关联号")
    private String externalLinkBillNo;

    @ExcelProperty(value = "托盘号")
    private String palletCode;

    @ExcelProperty(value = "箱码")
    private String boxCode;

    @ExcelProperty(value = "目标库位")
    private String targetLocationCode;

    //以下属性属于格式化属性,不属于导入excel的数据
    private String cargoCode;

    private String skuName;

    private String skuQuality;

    private Long receiveDate;

    private Long manufDate;

    private Long expireDate;

    private Long withdrawDate;

    private String warehouseCode;

    private String upcCode;

    private SkuLotDTO skuLotDTO;

    private SkuDTO skuDTO;

    private AsnDTO asnDTO;

    private Boolean taotiaoXT;

    private TallyDTO tallyDTO;

    private Integer rowId;

    private String locationQuality;

    private String inventoryType;
}
