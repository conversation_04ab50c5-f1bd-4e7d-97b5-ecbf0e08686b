package com.dt.platform.wms.biz.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.allocation.AllocationRuleEnum;
import com.dt.component.common.enums.base.*;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuLifeCtrlEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.box.BoxSkuDTO;
import com.dt.domain.base.dto.box.BoxSkuDetailDTO;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.box.BoxSkuParam;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.sort.ListSortUtils;
import com.dt.platform.wms.ReplenishSkuLotAndStockDTO;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.param.SkuLotCheckAndFormatCWParam;
import com.dt.platform.wms.biz.param.SkuLotCheckAndFormatParam;
import com.dt.platform.wms.dto.rec.CodeNameControlBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.box.IRemoteBoxSkuClient;
import com.dt.platform.wms.param.rec.CheckSkuLotParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/4 17:17
 */
@Service
@Slf4j
public class SkuLotBizImpl implements ISkuLotBiz {

    @Resource
    IRemoteLotRuleClient remoteLotRuleClient;

    @Resource
    IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    IRemoteAllocationRuleClient remoteAllocationRuleClient;

    @Resource
    IRemoteTurnoverRuleClient remoteTurnoverRuleClient;

    @Resource
    IRemoteSkuClient remoteSkuClient;

    @Resource
    IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    IRemoteBoxSkuClient remoteBoxSkuClient;

    @Resource
    IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    IRemoteCargoOwnerClient remoteCargoOwnerClient;


    @Override
    public SkuLotDTO findAndFormatSkuLot(SkuLotCheckAndFormatParam param, SkuDTO skuDTO, Boolean checkFlag) {
        if (ObjectUtil.isEmpty(skuDTO)) {
            throw new BaseException(BaseBizEnum.TIP, "商品不存在");
        }
//        //残次等级都是 商品属性正品 残次等级就是ZP
//        if (Objects.equals(param.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel()) && StringUtils.isEmpty(param.getInventoryType())) {
//            param.setInventoryType(InventoryTypeEnum.ZP.getCode());
//        }
//        //残次等级都是 商品属性次品 残次等级就是CC
//        if (Objects.equals(param.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel()) && StringUtils.isEmpty(param.getInventoryType())) {
//            param.setInventoryType(InventoryTypeEnum.CC.getCode());
//        }
        //校验和格式化批次
        SkuLotParam skuLotParam = formatSkuLotParam(param, skuDTO, checkFlag);
        //标记是否是效期商品
        if (Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {
            skuLotParam.setIsLifeMgt(true);
        } else {
            skuLotParam.setIsLifeMgt(false);
        }
        SkuLotDTO skuLotDTO = remoteSkuLotClient.queryAllocateSkuLotByParam(skuLotParam);
        //为空-数据库未找到批次编码,新生成批次数据
        if (StringUtils.isEmpty(skuLotDTO)) {
            skuLotDTO = ConverterUtil.convert(skuLotParam, SkuLotDTO.class);
            String skuLotCode = remoteSeqRuleClient.findSequence(SeqEnum.SKU_LOT_CODE_000001);
            skuLotDTO.setCode(skuLotCode);
            //TODO 2021-11-29 修改 外部批次编码为空使用批次编码填充
            if (StringUtils.isEmpty(skuLotDTO.getExternalSkuLotNo())) {
                skuLotDTO.setExternalSkuLotNo(skuLotDTO.getCode());
            }
        }
        log.info("findAndFormatSkuLot skuLotDTO:{}", JSONUtil.toJsonStr(skuLotDTO));
        return skuLotDTO;
    }

    @Override
    public SkuLotDTO findAndFormatCWSkuLot(SkuLotCheckAndFormatCWParam skuLotCheckAndFormatCWParam, SkuDTO skuDTO) {
        if (ObjectUtil.isEmpty(skuDTO)) {
            throw new BaseException(BaseBizEnum.TIP, "商品不存在");
        }
        //校验和格式化批次
        SkuLotParam skuLotParam = formatSkuLotCWParam(skuLotCheckAndFormatCWParam, skuDTO);
        //标记是否是效期商品
        if (Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {
            skuLotParam.setIsLifeMgt(true);
        } else {
            skuLotParam.setIsLifeMgt(false);
        }
        SkuLotDTO skuLotDTO = remoteSkuLotClient.queryAllocateSkuLotCWByParam(skuLotParam);
        //为空-数据库未找到批次编码,新生成批次数据
        if (StringUtils.isEmpty(skuLotDTO)) {
            skuLotDTO = ConverterUtil.convert(skuLotParam, SkuLotDTO.class);
            String skuLotCode = remoteSeqRuleClient.findSequence(SeqEnum.SKU_LOT_CODE_000001);
            skuLotDTO.setCode(skuLotCode);
        }
        log.info("findAndFormatCWSkuLot skuLotDTO:{}", JSONUtil.toJsonStr(skuLotDTO));
        return skuLotDTO;
    }

    private SkuLotParam formatSkuLotCWParam(SkuLotCheckAndFormatCWParam param, SkuDTO skuDTO) {
        log.info("formatSkuLotCWParam param:{} skuDTO:{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(skuDTO));
        //校验参数是否必填
        checkRequiredCWParam(skuDTO, param);
        //获取批次明细
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setWarehouseCode(skuDTO.getWarehouseCode());
        skuLotParam.setCargoCode(skuDTO.getCargoCode());
        skuLotParam.setSkuCode(skuDTO.getCode());
        //开启效期管理-效期商品
        String TypeFormat = "yyyyMMdd";
        Long manufDate = DateUtil.parse(DateUtil.format(new Date(param.getManufDate()), TypeFormat), TypeFormat).getTime();
        Long todayDate = DateUtil.parse(DateUtil.format(new Date(), TypeFormat)).getTime();
        if (manufDate > todayDate) {
            log.info("manufDate:{} tomorrowDate:{}", manufDate, todayDate);
            throw new BaseException(BaseBizEnum.TIP, "生产日期不能大于今天");
        }
        skuLotParam.setManufDate(DateUtil.parse(DateUtil.format(new Date(param.getManufDate()), TypeFormat), TypeFormat).getTime());
        skuLotParam.setManufDateFormat(TypeFormat);
        //失效日期
        skuLotParam.setExpireDate(DateUtil.parse(DateUtil.format(new Date(param.getExpireDate()), TypeFormat), TypeFormat).getTime());
        skuLotParam.setExpireDateFormat(TypeFormat);
        skuLotParam.setWithdrawDate(0L);
        skuLotParam.setReceiveDate(0L);
        skuLotParam.setExternalLinkBillNo("");
        skuLotParam.setValidityCode("");
        //校验生产日期和失效日期

        //获取CW货主 不强校验生产+保质期天数=失效日期
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(skuDTO.getCargoCode());

        Boolean cwCargo = false;
        if (cargoOwnerDTO != null && CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.CW_CARGO)) {
            cwCargo = true;
        }

        //失效日期  =   生产日期  +  保质期天数
        Long expireDate = skuLotParam.getManufDate() + Long.valueOf(skuDTO.getLifeCycle()) * CommonConstantUtil.DAY_MILLISECONDS;
        if (!cwCargo && !expireDate.equals(skuLotParam.getExpireDate())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品%s生产日期和失效日期计算异常", skuDTO.getCode()));
        }
        //生产和失效日期不为空,失效日期需大于生产日期
        if (skuLotParam.getExpireDate() > 0 && skuLotParam.getManufDate() > 0 && skuLotParam.getManufDate() >= skuLotParam.getExpireDate()) {
            throw new BaseException(BaseBizEnum.TIP, "失效日期要大于生产日期");
        }
        //生产批次号
        skuLotParam.setProductionNo(param.getProductionNo().trim());
        if (!StringUtils.isEmpty(param.getPalletCode())) {
            skuLotParam.setPalletCode(param.getPalletCode().trim());
        } else {
            skuLotParam.setPalletCode("");
        }
        if (!StringUtils.isEmpty(param.getBoxCode())) {
            skuLotParam.setBoxCode(param.getBoxCode().trim());
        } else {
            skuLotParam.setBoxCode("");
        }
        skuLotParam.setSkuQuality(param.getSkuQuality());
        skuLotParam.setExternalSkuLotNo(param.getExternalSkuLotNo());
        return skuLotParam;
    }

    private void checkRequiredCWParam(SkuDTO skuDTO, SkuLotCheckAndFormatCWParam param) {
        if (ObjectUtils.isEmpty(param.getManufDate()) || param.getManufDate() <= 0) {
            throw new BaseException(BaseBizEnum.TIP, "生产日期不能为空");
        }
        if (ObjectUtils.isEmpty(param.getExpireDate()) || param.getExpireDate() <= 0) {
            throw new BaseException(BaseBizEnum.TIP, "失效日期不能为空");
        }
        //生产批次号
        if (ObjectUtils.isEmpty(param.getProductionNo())) {
            throw new BaseException(BaseBizEnum.TIP, "生产批次号不能为空");
        }
        //质量等级
        if (ObjectUtils.isEmpty(param.getSkuQuality())) {
            throw new BaseException(BaseBizEnum.TIP, "商品质量等级不能为空");
        }
        //CW 外部批次编码 客户必填
        if (ObjectUtils.isEmpty(param.getExternalSkuLotNo())) {
            throw new BaseException(BaseBizEnum.TIP, "客户必填不能为空");
        }
    }

    @Override
    public void verificationSkuLotParam(CheckSkuLotParam param, SkuDTO skuDTO) {
        LotRuleDTO lotRuleDTO = remoteLotRuleClient.querySkuLotRule(skuDTO.getLotRuleCode());
        if (StringUtils.isEmpty(lotRuleDTO) || CollectionUtils.isEmpty(lotRuleDTO.getRuleDetailList())
                || !Objects.equals(lotRuleDTO.getStatus(), LotRuleStatusEnum.ENABLE.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "请维护,商品批次默认规则");
        }
        List<LotRuleDetailDTO> ruleDetailList = lotRuleDTO.getRuleDetailList();
        if (Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {
            if (ObjectUtils.isEmpty(param.getManufDate()) || param.getManufDate() <= 0) {
                throw new BaseException(BaseBizEnum.TIP, "生产日期不能为空");
            }
            if (ObjectUtils.isEmpty(param.getExpireDate()) || param.getExpireDate() <= 0) {
                throw new BaseException(BaseBizEnum.TIP, "失效日期不能为空");
            }
        } else {
            //生产日期
            if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey())
                    && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
                if (ObjectUtils.isEmpty(param.getManufDate()) || param.getManufDate() <= 0) {
                    throw new BaseException(BaseBizEnum.TIP, "生产日期不能为空");
                }
            }
            //失效日期
            if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey())
                    && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
                if (ObjectUtils.isEmpty(param.getExpireDate()) || param.getExpireDate() <= 0) {
                    throw new BaseException(BaseBizEnum.TIP, "失效日期不能为空");
                }
            }
        }
        //生产批次号
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getProductionNo())) {
                throw new BaseException(BaseBizEnum.TIP, "生产批次号不能为空");
            }
        }
        //质量等级
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_SKU_QUALITY.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getSkuQuality())) {
                throw new BaseException(BaseBizEnum.TIP, "商品质量等级不能为空");
            }
        }
        //残次等级
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_INVENTORY_TYPE.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getSkuQuality())) {
                throw new BaseException(BaseBizEnum.TIP, "残次等级不能为空");
            }
        }

        //入库日期
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getReceiveDate()) || param.getReceiveDate() <= 0) {
                throw new BaseException(BaseBizEnum.TIP, "入库日期不能为空");
            }
        }
        //入库关联单号
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LINK_BILL_NO.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getExternalLinkBillNo())) {
                throw new BaseException(BaseBizEnum.TIP, "入库关联单号不能为空");
            }
        }

        //箱码
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_BOXCODE_TYPE.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getBoxCode())) {
                throw new BaseException(BaseBizEnum.TIP, "箱码不能为空");
            }
        }

        //托盘
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PALLETCODE_TYPE.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getPalletCode())) {
                throw new BaseException(BaseBizEnum.TIP, "托盘不能为空");
            }
        }
        //效期暗码
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getValidityCode())) {
                throw new BaseException(BaseBizEnum.TIP, "暗码不能为空");
            }
        }
        //检验托盘
        if (!StringUtils.isEmpty(param.getPalletCode()) && param.getPalletCode().length() > 30) {
            throw new BaseException(BaseBizEnum.TIP, "托盘号长度不能超过30位");
        }
        //检验托盘
        if (!StringUtils.isEmpty(param.getBoxCode()) && param.getBoxCode().length() > 30) {
            throw new BaseException(BaseBizEnum.TIP, "箱码长度不能超过30位");
        }
    }
//
//    @Override
//    public List<ReplenishSkuLotAndStockDTO> getReplenishSkuLotAndStock(String warehouseCode, String cargoCode, String skuCode, String skuQuality) {
//        //组装批次和三级库存
//        List<ReplenishSkuLotAndStockDTO> replenishSkuLotAndStockDTOList = findReplenishStockLotAndLocation(warehouseCode, cargoCode, skuCode, skuQuality);
//        if (CollectionUtils.isEmpty(replenishSkuLotAndStockDTOList)) {
//            return new ArrayList<>();
//        }
//        //查询商品获取分配和周转规则
//        SkuDTO skuDTO = remoteSkuClient.querySkuByCode(cargoCode, skuCode);
//        if (skuDTO == null) {
//            throw new BaseException(BaseBizEnum.TIP, "未找到商品");
//        }
//
//        List<ReplenishSkuLotAndStockDTO> convertList = replenishSkuLotAndStockDTOList.stream().map(a -> {
//            a.setSkuName(skuDTO.getName());
//            SkuUpcDTO skuUpcDTO = skuDTO.getSkuUpcList().stream()
//                    .filter(a1 -> a1.getCargoCode().equals(a.getCargoCode()))
//                    .filter(a1 -> a1.getSkuCode().equals(a.getSkuCode()))
//                    .filter(a1 -> a1.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus()))
//                    .findFirst().orElse(null);
//            if (skuUpcDTO != null) {
//                a.setUpcCode(skuUpcDTO.getUpcCode());
//            }
//            return a;
//        }).collect(Collectors.toList());
//
//        //禁售日期批次拦截
//        convertList = filterWithdrawDateSkuLot(cargoCode, warehouseCode, convertList);
//        //周转规则排序 分配规则排序
//        convertList = sortListReplenishStockLotAndLocation(convertList, skuDTO.getTurnoverRuleCode(), skuDTO.getAllocationRuleCode());
//        return convertList;
//    }

    @Override
    public Map<String, Map<String, Boolean>> tallyGetLotRule(List<String> skuCodeList, String cargoCode) {
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(cargoCode);
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuClientList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuClientList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到商品信息");
        }
        Map<String, Map<String, Boolean>> skuMap = new HashMap<>(skuCodeList.size());
        for (SkuDTO skuDTO : skuClientList) {
            LotRuleDTO lotRuleDTO = remoteLotRuleClient.querySkuLotRule(skuDTO.getLotRuleCode());
            if (StringUtils.isEmpty(lotRuleDTO) || CollectionUtils.isEmpty(lotRuleDTO.getRuleDetailList())
                    || !Objects.equals(lotRuleDTO.getStatus(), LotRuleStatusEnum.ENABLE.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, "请维护,商品批次默认规则");
            }
            List<LotRuleDetailDTO> ruleDetailList = lotRuleDTO.getRuleDetailList();
            Map<String, Boolean> map = new HashMap<>();
            if (Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {

                map.put(LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey(), true);
                map.put(LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey(), true);
            } else {
                //生产日期
                if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey())
                        && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
                    map.put(LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey(), true);
                }
                if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey())
                        && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.EMPTY.getCode()))) {
                    map.put(LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey(), false);
                }
                //失效日期
                if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey())
                        && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
                    map.put(LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey(), true);
                }
                if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey())
                        && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.EMPTY.getCode()))) {
                    map.put(LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey(), false);
                }
            }
            //生产批次号
            if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey())
                    && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
                map.put(LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey(), true);
            }
            if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey())
                    && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.EMPTY.getCode()))) {
                map.put(LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey(), false);
            }
            //入库关联单号
            if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LINK_BILL_NO.getPropKey())
                    && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
                map.put(LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LINK_BILL_NO.getPropKey(), true);
            }
            //质量等级
            map.put(LotRulePropKeyEnum.PROP_KEY_SKU_QUALITY.getPropKey(), true);
            skuMap.put(skuDTO.getCode(), map);
        }
        return skuMap;
    }

    @Override
    public List<SkuLotDTO> buildBoxSkuLotList(SkuLotDTO skuLotDTO) {
        BoxSkuParam boxSkuParam = new BoxSkuParam();
        boxSkuParam.setCargoCode(skuLotDTO.getCargoCode());
        boxSkuParam.setBoxSkuCode(skuLotDTO.getSkuCode());
        List<BoxSkuDTO> boxSkuDTOList = remoteBoxSkuClient.getBoxSkuByBoxSkuEffective(boxSkuParam);
        if (CollectionUtils.isEmpty(boxSkuDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "套盒商品数据异常");
        }
        BoxSkuDTO boxSkuDTO = boxSkuDTOList.get(0);
        if (CollectionUtils.isEmpty(boxSkuDTO.getDetailList())) {
            throw new BaseException(BaseBizEnum.TIP, "套盒商品数据异常");
        }
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(skuLotDTO.getCargoCode());
        skuParam.setCodeList(boxSkuDTO.getDetailList().stream().map(BoxSkuDetailDTO::getChildSkuCode).collect(Collectors.toList()));
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "套盒子商品数据异常");
        }
        List<SkuLotDTO> newSkuLotList = new ArrayList<>();
        newSkuLotList.add(skuLotDTO);
        //处理套盒数据
        boxSkuDTO.getDetailList().forEach(boxSkuDetailDTO -> {
            SkuLotDTO copySkuLotDTO = ObjectUtil.cloneByStream(skuLotDTO);
            //重置ID和商品编码,批次编码
            copySkuLotDTO.setId(null);
            copySkuLotDTO.setSkuCode(boxSkuDetailDTO.getChildSkuCode());
            copySkuLotDTO.setCode(remoteSeqRuleClient.findSequence(SeqEnum.SKU_LOT_CODE_000001));
            SkuDTO childSkuDTO = skuDTOList.stream()
                    .filter(a -> a.getCode().equalsIgnoreCase(boxSkuDetailDTO.getChildSkuCode()))
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(boxSkuDetailDTO.getCargoCode())).findFirst().orElse(null);
            if (childSkuDTO.getIsLifeMgt().equals(SkuLifeCtrlEnum.SKU_LIFE_CTRL_NO.getCode())) {
                copySkuLotDTO.setExpireDate(0L);
                copySkuLotDTO.setManufDate(0L);
            }
            copySkuLotDTO.setLinkSkuLotNo(skuLotDTO.getCode());
            newSkuLotList.add(copySkuLotDTO);
        });
        return newSkuLotList;
    }

    @Override
    public List<CodeNameControlBizDTO> buildPopAdjustSku(List<LotRuleDetailDTO> ruleDetailList, Boolean isLifeMgt, Boolean needSkuQuality, String defaultLinkBillNo) {
        List<CodeNameControlBizDTO> codeNameControlBizDTOList = new ArrayList<>();
        if (needSkuQuality) {
            //商品属性只有正次品 必填
            codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                    LotRulePropKeyEnum.PROP_KEY_SKU_QUALITY.getPropKey(),
                    LotRulePropKeyEnum.PROP_KEY_SKU_QUALITY.getPropName(),
                    true, LotRuleTypeEnum.STRING.name(),
                    LotRuleShowEnum.SELECT.name(), "", null, IdNameVO.build(SkuQualityEnum.class, "level", "message")));
        }
        //残次等级
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_INVENTORY_TYPE.getPropKey())
                && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                    .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_INVENTORY_TYPE.getPropKey())).findFirst().orElse(null);
            List<IdNameVO> needList = InventoryTypeEnum.getNeedList();

            if (lotRuleDetailDTO != null && Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.MUST.getCode())) {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_INVENTORY_TYPE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_INVENTORY_TYPE.getPropName(),
                        true, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.SELECT.name(), "", null, needList));
            } else {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_INVENTORY_TYPE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_INVENTORY_TYPE.getPropName(),
                        false, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.SELECT.name(), "", null, needList));
            }
        }
        //开启效期管理--生产日期必须填写
        if (isLifeMgt) {
            LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                    .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey())).findFirst().orElse(null);
            codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                    LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey(),
                    LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropName(),
                    true, LotRuleTypeEnum.DATE.name(),
                    LotRuleShowEnum.INPUT.name(), lotRuleDetailDTO.getTypeFormat()));

            lotRuleDetailDTO = ruleDetailList.stream()
                    .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey())).findFirst().orElse(null);
            codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                    LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey(),
                    LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropName(),
                    true, LotRuleTypeEnum.DATE.name(),
                    LotRuleShowEnum.INPUT.name(), lotRuleDetailDTO.getTypeFormat()));
        } else {
            //生产日期
            if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey())
                    && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
                LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                        .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey())).findFirst().orElse(null);
                if (lotRuleDetailDTO != null && Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.MUST.getCode())) {
                    codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                            LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey(),
                            LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropName(),
                            true, LotRuleTypeEnum.DATE.name(),
                            LotRuleShowEnum.INPUT.name(), lotRuleDetailDTO.getTypeFormat()));
                } else {
                    codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                            LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey(),
                            LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropName(),
                            false, LotRuleTypeEnum.DATE.name(),
                            LotRuleShowEnum.INPUT.name(), lotRuleDetailDTO.getTypeFormat()));
                }
            }
            //失效日期
            if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey())
                    && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
                LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                        .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey())).findFirst().orElse(null);
                if (lotRuleDetailDTO != null && Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.MUST.getCode())) {
                    codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                            LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey(),
                            LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropName(),
                            true, LotRuleTypeEnum.DATE.name(),
                            LotRuleShowEnum.INPUT.name(), lotRuleDetailDTO.getTypeFormat()));
                } else {
                    codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                            LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey(),
                            LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropName(),
                            false, LotRuleTypeEnum.DATE.name(),
                            LotRuleShowEnum.INPUT.name(), lotRuleDetailDTO.getTypeFormat()));
                }
            }
        }
        //入库日期由前端传入
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey())
                && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                    .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey())).findFirst().orElse(null);
            if (lotRuleDetailDTO != null && Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.MUST.getCode())) {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropName(),
                        true, LotRuleTypeEnum.DATE.name(),
                        LotRuleShowEnum.INPUT.name(), lotRuleDetailDTO.getTypeFormat()));
            } else {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropName(),
                        false, LotRuleTypeEnum.DATE.name(),
                        LotRuleShowEnum.INPUT.name(), lotRuleDetailDTO.getTypeFormat()));
            }
        }
        //生产批次号
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey())
                && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                    .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey())).findFirst().orElse(null);
            if (lotRuleDetailDTO != null && Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.MUST.getCode())) {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropName(),
                        true, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.INPUT.name(), ""));
            } else {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropName(),
                        false, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.INPUT.name(), ""));
            }
        }

        //入库关联号(只有必填和禁用)
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LINK_BILL_NO.getPropKey())
                && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                    .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LINK_BILL_NO.getPropKey())).findFirst().orElse(null);
            if (lotRuleDetailDTO != null && Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.MUST.getCode())) {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LINK_BILL_NO.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LINK_BILL_NO.getPropName(),
                        true, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.INPUT.name(), "", defaultLinkBillNo));
            }
        }

        //暗码
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropKey())
                && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                    .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropKey())).findFirst().orElse(null);
            if (lotRuleDetailDTO != null && Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.MUST.getCode())) {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropName(),
                        true, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.INPUT.name(), ""));
            } else {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropName(),
                        false, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.INPUT.name(), ""));
            }
        }


        // 箱码
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_BOXCODE_TYPE.getPropKey())
                && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                    .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_BOXCODE_TYPE.getPropKey())).findFirst().orElse(null);
            if (lotRuleDetailDTO != null && Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.MUST.getCode())) {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_BOXCODE_TYPE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_BOXCODE_TYPE.getPropName(),
                        true, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.INPUT.name(), ""));
            } else {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_BOXCODE_TYPE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_BOXCODE_TYPE.getPropName(),
                        false, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.INPUT.name(), ""));
            }
        }

        // 托盘
        if (ruleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PALLETCODE_TYPE.getPropKey())
                && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            LotRuleDetailDTO lotRuleDetailDTO = ruleDetailList.stream()
                    .filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PALLETCODE_TYPE.getPropKey())).findFirst().orElse(null);
            if (lotRuleDetailDTO != null && Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.MUST.getCode())) {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_PALLETCODE_TYPE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_PALLETCODE_TYPE.getPropName(),
                        true, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.INPUT.name(), ""));
            } else {
                codeNameControlBizDTOList.add(CodeNameControlBizDTO.build(
                        LotRulePropKeyEnum.PROP_KEY_PALLETCODE_TYPE.getPropKey(),
                        LotRulePropKeyEnum.PROP_KEY_PALLETCODE_TYPE.getPropName(),
                        false, LotRuleTypeEnum.STRING.name(),
                        LotRuleShowEnum.INPUT.name(), ""));
            }
        }
        return codeNameControlBizDTOList;
    }

//
//    /**
//     * 过滤禁售日期批次
//     *
//     * @param cargoCode
//     * @param warehouseCode
//     * @param skuLotAndStockDTOS
//     * @return
//     */
//    private List<ReplenishSkuLotAndStockDTO> filterWithdrawDateSkuLot(String cargoCode, String warehouseCode, List<ReplenishSkuLotAndStockDTO> skuLotAndStockDTOS) {
//        //过滤商品禁售日期的批次，不参与分配
//        SkuParam skuParam = new SkuParam();
//        skuParam.setCargoCode(cargoCode);
//        skuParam.setWarehouseCode(warehouseCode);
//        skuParam.setIsLifeMgt(SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode());
//        skuParam.setCodeList(skuLotAndStockDTOS.stream().map(ReplenishSkuLotAndStockDTO::getSkuCode).distinct().collect(Collectors.toList()));
//        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
//        if (CollectionUtils.isEmpty(skuDTOList)) {
//            return skuLotAndStockDTOS;
//        }
//        skuLotAndStockDTOS = skuLotAndStockDTOS.stream().filter(a -> filterLot(a, skuDTOList)).collect(Collectors.toList());
//        return skuLotAndStockDTOS;
//    }
//
//    /**
//     * 获取当前商品需要的数量
//     * 使用周转和分配规则排序
//     *
//     * @param skuLotAndStockDTOS
//     * @param turnoverRuleCode
//     * @param allocationRuleCode
//     * @return
//     */
//    List<ReplenishSkuLotAndStockDTO> sortListReplenishStockLotAndLocation(List<ReplenishSkuLotAndStockDTO> skuLotAndStockDTOS, String turnoverRuleCode, String allocationRuleCode) {
//        if (CollectionUtils.isEmpty(skuLotAndStockDTOS)) {
//            return new ArrayList<>();
//        }
//        //指定批次或只有一个库位一个批次有商品 直接返回
//        if (skuLotAndStockDTOS.size() == 1) {
//            return skuLotAndStockDTOS;
//        }
//        List<String> sortFeilds = new ArrayList<>();
//        //先处理周转规则->再出来 TODO true升序，false降序
//        //查询周转规则--出库单明细上有周转规则  使用周转规则选择最合适的批次排序  失效日期> 入库日期 排序批次，再去排序库位库存
//        TurnoverRuleDTO turnoverRuleDTO = remoteTurnoverRuleClient.queryTurnoverRuleByCode(turnoverRuleCode);
//        if (turnoverRuleDTO != null && !CollectionUtils.isEmpty(turnoverRuleDTO.getDetailDTOList())) {
//            List<TurnoverRuleDetailDTO> detailDTOList = turnoverRuleDTO.getDetailDTOList().stream().sorted(Comparator.comparing(TurnoverRuleDetailDTO::getOrderIndex)).collect(Collectors.toList());
//            for (TurnoverRuleDetailDTO detailDTO : detailDTOList) {
//                switch (LotRulePropKeyEnum.matchOpCode(detailDTO.getPopKey())) {
//                    case PROP_KEY_MANUF_DATE:
//                        if (detailDTO.getSorting().equals(TurnoverRuleSortEnum.DESC.getCode())) {
//                            sortFeilds.add("manufDate_false");
//                        } else {
//                            sortFeilds.add("manufDate_true");
//                        }
//                        break;
//                    case PROP_KEY_EXPIRE_DATE:
//                        if (detailDTO.getSorting().equals(TurnoverRuleSortEnum.DESC.getCode())) {
//                            sortFeilds.add("expireDate_false");
//                        } else {
//                            sortFeilds.add("expireDate_true");
//                        }
//                        break;
//                    case PROP_KEY_SKU_QUALITY:
//                        if (detailDTO.getSorting().equals(TurnoverRuleSortEnum.DESC.getCode())) {
//                            sortFeilds.add("skuQuality_false");
//                        } else {
//                            sortFeilds.add("skuQuality_true");
//                        }
//                        break;
//                    case PROP_KEY_PRODUCT_NO:
//                        if (detailDTO.getSorting().equals(TurnoverRuleSortEnum.DESC.getCode())) {
//                            sortFeilds.add("productionNo_false");
//                        } else {
//                            sortFeilds.add("productionNo_true");
//                        }
//                        break;
////                    case PROP_KEY_WITHDRAW_DATE:
////                        if (detailDTO.getSorting().equals(TurnoverRuleSortEnum.DESC.getCode())) {
////                            sortFeilds.add("withdrawDate_false");
////                        } else {
////                            sortFeilds.add("withdrawDate_true");
////                        }
////                        break;
//                    case PROP_KEY_EXTERNAL_LOT:
//                        if (detailDTO.getSorting().equals(TurnoverRuleSortEnum.DESC.getCode())) {
//                            sortFeilds.add("externalSkuLotNo_false");
//                        } else {
//                            sortFeilds.add("externalSkuLotNo_true");
//                        }
//                        break;
//                    default:
//                        if (detailDTO.getSorting().equals(TurnoverRuleSortEnum.DESC.getCode())) {
//                            sortFeilds.add("receiveDate_false");
//                        } else {
//                            sortFeilds.add("receiveDate_true");
//                        }
//                        break;
//                }
//            }
//        } else {
//            sortFeilds.add("receiveDate_true");
//        }
//        //查询分配规则--出库单明细上有分配规则--选择最合适的批次
//        //TODO true升序，false降序
//        AllocationRuleDTO allocationRuleDTO = remoteAllocationRuleClient.queryAllocationRuleByCode(allocationRuleCode);
//        //清空库位优先-   订单优先互斥 库存可用数量排序
//        if (allocationRuleDTO != null && Objects.equals(allocationRuleDTO.getRemoveLocation(), AllocationRuleEnum.LocationEmpty.REMOVE_LOCATION.getCode())) {
//            sortFeilds.add("availableQty_true");
//        }
//        //满足订单优先-   清空库位优先互斥
//        if (allocationRuleDTO != null && Objects.equals(allocationRuleDTO.getRemoveLocation(), AllocationRuleEnum.OrderPriority.ORDER_PRIORITY.getCode())) {
//            sortFeilds.add("availableQty_false");
//        }
//        if (CollectionUtils.isEmpty(sortFeilds)) {
//            sortFeilds.add("receiveDate_true");
//        }
//        ListSortUtils.sortList(skuLotAndStockDTOS, sortFeilds);
//        return skuLotAndStockDTOS;
//    }
//
//    /**
//     * 过滤禁售日期
//     *
//     * @param a
//     * @param skuDTOList
//     * @return
//     */
//    private boolean filterLot(ReplenishSkuLotAndStockDTO a, List<SkuDTO> skuDTOList) {
////        //次品不过滤  TODO 2022-03-09 效期商品次品检验禁售
////        if (a != null && a.getSkuQuality().equals(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())) {
////            return true;
////        }
//        //未开启效期管理的数据
//        if (skuDTOList.stream().noneMatch(a1 -> Objects.equals(a1.getCode(), a.getSkuCode()))) {
//            return true;
//        }
//        SkuDTO skuDTO = skuDTOList.stream().filter(a1 -> Objects.equals(a1.getCode(), a.getSkuCode())).findFirst().orElse(null);
//        //未开启效期管理的数据 或者禁售日期不大于0
//        if (skuDTO == null || skuDTO.getWithdrawCycle() <= 0) {
//            return true;
//        }
//        //批次录入失效日期
//        if (a != null && a.getExpireDate() <= 0) {
//            return true;
//        }
//        Long withdrawDate = a.getExpireDate() - Long.valueOf(skuDTO.getWithdrawCycle()) * CommonConstantUtil.DAY_MILLISECONDS;
//        if (withdrawDate < System.currentTimeMillis()) {
//            return false;
//        }
//        return true;
//    }
//
//    private List<ReplenishSkuLotAndStockDTO> findReplenishStockLotAndLocation(String warehouseCode, String cargoCode, String skuCode, String skuQuality) {
//        List<StockLocationDTO> stockLocationDTOS = getStockLocation(warehouseCode, cargoCode, skuCode, skuQuality,
//                Arrays.asList(LocationTypeEnum.LOCATION_TYPE_STORE.getType(), LocationTypeEnum.LOCATION_TYPE_TEMP.getType()));
//        if (CollectionUtils.isEmpty(stockLocationDTOS)) {
//            return new ArrayList<>();
//        }
//        List<SkuLotDTO> skuLotDTOS = remoteSkuLotClient.getSkuLotNoList(stockLocationDTOS.stream().map(StockLocationDTO::getSkuLotNo).collect(Collectors.toList()));
//
//        //分析有库存的库位--库位路径
//        if (CollectionUtils.isEmpty(skuLotDTOS) || CollectionUtils.isEmpty(stockLocationDTOS)) {
//            return new ArrayList<>();
//        }
//        //组装当前货品批次和库位库存数据
//        List<ReplenishSkuLotAndStockDTO> skuLotAndStockDTOS = buildReplenishSkuLotAndStock(skuLotDTOS, stockLocationDTOS);
//        if (CollectionUtils.isEmpty(skuLotAndStockDTOS)) {
//            return new ArrayList<>();
//        }
//        return skuLotAndStockDTOS;
//    }
//
//    /**
//     * 获取存储区可用数库存和暂存区的等待上架库存数
//     *
//     * @param warehouseCode
//     * @param cargoCode
//     * @param skuCode
//     * @param locationTypeList
//     * @return
//     */
//    private List<StockLocationDTO> getStockLocation(String warehouseCode, String cargoCode, String skuCode, String skuQuality, List<String> locationTypeList) {
//        List<StockLocationDTO> locationDTOList = new ArrayList<>();
//        locationTypeList = locationTypeList.stream().distinct().collect(Collectors.toList());
//        for (String locationType : locationTypeList) {
//            //不指定批次
//            StockLocationParam stockLocationParam = new StockLocationParam();
//            stockLocationParam.setWarehouseCode(warehouseCode);
//            stockLocationParam.setCargoCode(cargoCode);
//            stockLocationParam.setSkuCode(skuCode);
//            stockLocationParam.setSkuQuality(skuQuality);
//            stockLocationParam.setLocationType(locationType);
//            if (LocationTypeEnum.LOCATION_TYPE_TEMP.getType().equals(locationType)) {
//                stockLocationParam.setHasWaitShelfQty(true);
//            }
//            if (LocationTypeEnum.LOCATION_TYPE_STORE.getType().equals(locationType)) {
//                stockLocationParam.setHasAvailableQty(true);
//            }
//            List<StockLocationDTO> stockLocationDTOS = remoteStockLocationClient.getList(stockLocationParam);
//            if (!CollectionUtils.isEmpty(stockLocationDTOS)) {
//                if (LocationTypeEnum.LOCATION_TYPE_TEMP.getType().equals(locationType)) {
//                    stockLocationDTOS.forEach(a -> a.setAvailableQty(a.getWaitShelfQty()));
//                    locationDTOList.addAll(stockLocationDTOS);
//                } else {
//                    locationDTOList.addAll(stockLocationDTOS);
//                }
//            }
//        }
//        return locationDTOList;
//    }
//
//    /**
//     * 组装库位和批次信息
//     *
//     * @param skuLotDTOS
//     * @param stockLocationDTOS
//     * @return
//     */
//    private List<ReplenishSkuLotAndStockDTO> buildReplenishSkuLotAndStock(List<SkuLotDTO> skuLotDTOS, List<StockLocationDTO> stockLocationDTOS) {
//        List<ReplenishSkuLotAndStockDTO> skuLotAndStockDTOS = new ArrayList<>();
//        for (int i = 0; i < stockLocationDTOS.size(); i++) {
//            ReplenishSkuLotAndStockDTO skuLotAndStockDTO = ConverterUtil.convert(stockLocationDTOS.get(i), ReplenishSkuLotAndStockDTO.class);
//            SkuLotDTO skuLotDTO = skuLotDTOS.stream().filter(a -> Objects.equals(a.getCode(), skuLotAndStockDTO.getSkuLotNo())).findFirst().orElse(null);
//            BeanUtils.copyProperties(skuLotDTO, skuLotAndStockDTO);
//            skuLotAndStockDTOS.add(skuLotAndStockDTO);
//        }
//        return skuLotAndStockDTOS;
//    }


    /**
     * @param param
     * @param skuDTO
     * @param checkFlag 是否校验禁收 true 校验 false不校验
     * @return com.dt.domain.base.param.SkuLotParam
     * @author: WuXian
     * description:  格式化
     * create time: 2021/12/1 16:18
     */
    private SkuLotParam formatSkuLotParam(SkuLotCheckAndFormatParam param, SkuDTO skuDTO, Boolean checkFlag) {
        log.info("formatSkuLotParam param:{} skuDTO:{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(skuDTO));
        //查询商品默认策略
        LotRuleDTO lotRuleDTO = remoteLotRuleClient.querySkuLotRule(skuDTO.getLotRuleCode());
        if (StringUtils.isEmpty(lotRuleDTO) || CollectionUtils.isEmpty(lotRuleDTO.getRuleDetailList())
                || !Objects.equals(lotRuleDTO.getStatus(), LotRuleStatusEnum.ENABLE.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品%s请维护,商品批次规则", skuDTO.getCode()));
        }
        //校验参数是否必填
        checkRequiredParam(skuDTO, param, lotRuleDTO);
        //获取批次明细
        List<LotRuleDetailDTO> ruleDTORuleDetailList = lotRuleDTO.getRuleDetailList();
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setWarehouseCode(skuDTO.getWarehouseCode());
        skuLotParam.setCargoCode(skuDTO.getCargoCode());
        skuLotParam.setSkuCode(skuDTO.getCode());
        //开启效期管理-效期商品
        if (Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {
            //生产日期
            LotRuleDetailDTO lotRuleDetailDTO = ruleDTORuleDetailList.stream().filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey())).findFirst().orElse(null);
            String TypeFormat = "yyyyMMdd";
            if (lotRuleDetailDTO != null) {
                TypeFormat = lotRuleDetailDTO.getTypeFormat();
            }
            Long manufDate = DateUtil.parse(DateUtil.format(new Date(param.getManufDate()), TypeFormat), lotRuleDetailDTO.getTypeFormat()).getTime();
            Long todayDate = DateUtil.parse(DateUtil.format(new Date(), TypeFormat)).getTime();
            if (manufDate > todayDate) {
                log.info("manufDate:{} tomorrowDate:{}", manufDate, todayDate);
                throw new BaseException(BaseBizEnum.TIP, "生产日期不能大于今天");
            }
            skuLotParam.setManufDate(DateUtil.parse(DateUtil.format(new Date(param.getManufDate()), lotRuleDetailDTO.getTypeFormat()), lotRuleDetailDTO.getTypeFormat()).getTime());
            skuLotParam.setManufDateFormat(TypeFormat);
            //失效日期
            lotRuleDetailDTO = ruleDTORuleDetailList.stream().filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey())).findFirst().orElse(null);
            if (lotRuleDetailDTO != null) {
                TypeFormat = lotRuleDetailDTO.getTypeFormat();
            }
            skuLotParam.setExpireDate(DateUtil.parse(DateUtil.format(new Date(param.getExpireDate()), TypeFormat), TypeFormat).getTime());
            skuLotParam.setExpireDateFormat(TypeFormat);
            //禁售日期 TODO 目前禁售日期的逻辑，改成实时计算 此处维护的是有失效，商品有禁售天数，计算禁售日期。前端所有使用禁售，是实时计算(禁售仅对效期商品有实际用途)
//            lotRuleDetailDTO = ruleDTORuleDetailList.stream().filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_WITHDRAW_DATE.getPropKey())).findFirst().orElse(null);
//            if (lotRuleDetailDTO != null) {
//                TypeFormat = lotRuleDetailDTO.getTypeFormat();
//                if (!Objects.equals(lotRuleDetailDTO.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode())) {
//                    Long withdrawTime = param.getExpireDate() - Long.valueOf(skuDTO.getWithdrawCycle()) * CommonConstantUtil.DAY_MILLISECONDS;
//                    skuLotParam.setWithdrawDate(DateUtil.parse(DateUtil.format(new Date(withdrawTime), TypeFormat), TypeFormat).getTime());
//                    skuLotParam.setWithdrawDateFormat(TypeFormat);
//                } else {
//                    skuLotParam.setWithdrawDate(0L);
//                }
//            } else {
//                skuLotParam.setWithdrawDate(0L);
//            }
            skuLotParam.setWithdrawDate(0L);

            //获取CW货主 不强校验生产+保质期天数=失效日期
            CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(skuDTO.getCargoCode());

            Boolean cwCargo = false;
            if (cargoOwnerDTO != null && CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.CW_CARGO)) {
                cwCargo = true;
            }

            //校验生产日期和失效日期
            //失效日期  =   生产日期  +  保质期天数
            Long expireDate = skuLotParam.getManufDate() + Long.valueOf(skuDTO.getLifeCycle()) * CommonConstantUtil.DAY_MILLISECONDS;
            //非CW货主强校验
            if (!cwCargo && !expireDate.equals(skuLotParam.getExpireDate())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品%s生产日期和失效日期计算异常", skuDTO.getCode()));
            }
            //检验禁收日期  生产日期+禁收日期<当前时间  原来的逻辑
            //检验禁收日期 当天+禁收日期>失效日期 （不能收） 2020-12-30修改逻辑
            Long rejectTime = System.currentTimeMillis() + Long.valueOf(skuDTO.getRejectCycle()) * CommonConstantUtil.DAY_MILLISECONDS;
            if (checkFlag && rejectTime > skuLotParam.getExpireDate()) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品%s当前商品禁止收货", skuDTO.getCode()));
            }
        } else {
            //生产日期
            if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode())) && param.getManufDate() != null && param.getManufDate() > 0) {
                LotRuleDetailDTO lotRuleDetailDTO = ruleDTORuleDetailList.stream().filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey())).findFirst().get();
                skuLotParam.setManufDate(DateUtil.parse(DateUtil.format(new Date(param.getManufDate()), lotRuleDetailDTO.getTypeFormat()), lotRuleDetailDTO.getTypeFormat()).getTime());
                skuLotParam.setManufDateFormat(lotRuleDetailDTO.getTypeFormat());

                Long manufDate = skuLotParam.getManufDate();
                Long tomorrowDate = DateUtil.parse(DateUtil.format(new Date(), lotRuleDetailDTO.getTypeFormat())).getTime();
                if (manufDate > tomorrowDate) {
                    throw new BaseException(BaseBizEnum.TIP, "生产日期不能大于今天");
                }
            } else {
                skuLotParam.setManufDate(0L);
            }
            //失效日期
            if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode())) && param.getExpireDate() != null && param.getExpireDate() > 0) {
                LotRuleDetailDTO lotRuleDetailDTO = ruleDTORuleDetailList.stream().filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey())).findFirst().get();
                skuLotParam.setExpireDate(DateUtil.parse(DateUtil.format(new Date(param.getExpireDate()), lotRuleDetailDTO.getTypeFormat()), lotRuleDetailDTO.getTypeFormat()).getTime());
                skuLotParam.setExpireDateFormat(lotRuleDetailDTO.getTypeFormat());
            } else {
                skuLotParam.setExpireDate(0L);
            }
            //禁售日期
            skuLotParam.setWithdrawDate(0L);
        }
        //生产和失效日期不为空,失效日期需大于生产日期
        if (skuLotParam.getExpireDate() > 0 && skuLotParam.getManufDate() > 0 && skuLotParam.getManufDate() >= skuLotParam.getExpireDate()) {
            throw new BaseException(BaseBizEnum.TIP, "失效日期要大于生产日期");
        }
        //生产批次号 = 生产日期+失效日期 生成批次号目前限制为yyyyMMdd
        if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            if (param.getProductionNo() == null) {
                skuLotParam.setProductionNo("");
            } else {
                skuLotParam.setProductionNo(param.getProductionNo().trim());
            }
        } else {
            skuLotParam.setProductionNo("");
        }
        //收货时间
        if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            //TODO 2022-03-10 移除收货时间为空,不传值默认就是空
            if (param.getReceiveDate() != null && param.getReceiveDate() > 0) {
                LotRuleDetailDTO lotRuleDetailDTO = ruleDTORuleDetailList.stream().filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey())).findFirst().get();
                skuLotParam.setReceiveDate(DateUtil.parse(DateUtil.format(new Date(param.getReceiveDate()), lotRuleDetailDTO.getTypeFormat()), lotRuleDetailDTO.getTypeFormat()).getTime());
                skuLotParam.setReceiveDateFormat(lotRuleDetailDTO.getTypeFormat());
            } else {
                skuLotParam.setReceiveDate(0L);
            }
        } else {
            skuLotParam.setReceiveDate(0L);
        }
        //入库日期不能大于当前天
        Long todayDate = DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd")).getTime();
        if (skuLotParam.getReceiveDate() > 0 && skuLotParam.getReceiveDate() > todayDate) {
            log.info("receiveDate:{} tomorrowDate:{}", skuLotParam.getReceiveDate(), todayDate);
            throw new BaseException(BaseBizEnum.TIP, "入库日期不能大于今天");
        }
        //商品属性
        if (StringUtils.isEmpty(param.getSkuQuality())) {
            throw new BaseException(BaseBizEnum.TIP, "批次必须区分正次品");
        }
        if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_SKU_QUALITY.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            skuLotParam.setSkuQuality(param.getSkuQuality());
        } else {
            skuLotParam.setSkuQuality("");
        }
        //外部批次 非效期商品 选了默认WMS批次ID 使用WMS批次ID 效期商品为空统一使用WMS批次ID
        if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LOT.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            LotRuleDetailDTO lotRuleDetailDTO = ruleDTORuleDetailList.stream().filter(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LOT.getPropKey())).findFirst().orElse(null);
            //外部批次编码目前三种方式组装：1.生产日期 2.失效日期 3.生产日期+失效日期 (为空就使用wms批次编码填充)
            if (lotRuleDetailDTO != null && !StringUtils.isEmpty(lotRuleDetailDTO.getTypeFormat())) {
                ExtranalLotTypeEnum lotTypeEnum = ExtranalLotTypeEnum.getEnum(lotRuleDetailDTO.getTypeFormat());
                switch (lotTypeEnum) {
                    case WMS_BATCH:
                        break;
                    case MANUF_DATE:
                        if (skuLotParam.getManufDate() > 0) {
                            skuLotParam.setExternalSkuLotNo(ConverterUtil.convertVoTime(skuLotParam.getManufDate(), "yyyyMMdd"));
                        }
                        break;
                    case EXPIRE_DATE:
                        if (skuLotParam.getExpireDate() > 0) {
                            skuLotParam.setExternalSkuLotNo(ConverterUtil.convertVoTime(skuLotParam.getExpireDate(), "yyyyMMdd"));
                        }
                        break;
                    case MANUF_AND_EXPIRE_DATE:
                        String manufandexpiredate = "";
                        if (skuLotParam.getManufDate() > 0) {
                            manufandexpiredate = manufandexpiredate + ConverterUtil.convertVoTime(skuLotParam.getManufDate(), "yyyyMMdd");
                        }
                        if (skuLotParam.getExpireDate() > 0) {
                            manufandexpiredate = manufandexpiredate + ConverterUtil.convertVoTime(skuLotParam.getExpireDate(), "yyyyMMdd");
                        }
                        skuLotParam.setExternalSkuLotNo(manufandexpiredate);
                        break;
                    case PRODUCTION_NO:
                        if (!StringUtils.isEmpty(skuLotParam.getProductionNo())) {
                            skuLotParam.setExternalSkuLotNo(skuLotParam.getProductionNo());
                        }
                        break;
                    default:
                        break;
                }
            }
            if (StringUtils.isEmpty(skuLotParam.getExternalSkuLotNo())) {
                skuLotParam.setExternalSkuLotNo("");
            }
        } else {
            skuLotParam.setExternalSkuLotNo("");
        }
        //入库关联号(只有必填和禁用)
        if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LINK_BILL_NO.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            if (param.getExternalLinkBillNo() == null) {
                skuLotParam.setExternalLinkBillNo("");
            } else {
                skuLotParam.setExternalLinkBillNo(param.getExternalLinkBillNo().trim());
            }
        } else {
            skuLotParam.setExternalLinkBillNo("");
        }
        //商品属性
        if (StringUtils.isEmpty(param.getInventoryType())) {
            throw new BaseException(BaseBizEnum.TIP, "残次等级必填");
        }
        List<String> zpList = Arrays.asList(InventoryTypeEnum.ZP.getCode(), InventoryTypeEnum.ZCZP.getCode());
        if (Objects.equals(skuLotParam.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel())
                && !zpList.contains(param.getInventoryType())) {
            throw new BaseException(BaseBizEnum.TIP, "商品属性【正品】,残次等级只能选择【正品】或【收货暂存良-待退废】");
        }
        if (Objects.equals(skuLotParam.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())
                && zpList.contains(param.getInventoryType())) {
            throw new BaseException(BaseBizEnum.TIP, "商品属性【次品】,残次等级不能选择【正品】或【收货暂存良-待退废】");
        }
        //残次等级
        skuLotParam.setInventoryType(param.getInventoryType());
        //托盘
        if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PALLETCODE_TYPE.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            if (param.getPalletCode() == null) {
                skuLotParam.setPalletCode("");
            } else {
                skuLotParam.setPalletCode(param.getPalletCode().trim());
            }
        } else {
            skuLotParam.setPalletCode("");
        }

        //箱码
        if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_BOXCODE_TYPE.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            if (param.getBoxCode() == null) {
                skuLotParam.setBoxCode("");
            } else {
                skuLotParam.setBoxCode(param.getBoxCode().trim());
            }
        } else {
            skuLotParam.setBoxCode("");
        }
        //暗码
        if (ruleDTORuleDetailList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropKey()) && !Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.DISABLE.getCode()))) {
            if (param.getValidityCode() == null) {
                skuLotParam.setValidityCode("");
            } else {
                skuLotParam.setValidityCode(param.getValidityCode().trim());
            }
        } else {
            skuLotParam.setValidityCode("");
        }
        return skuLotParam;
    }

    /**
     * @param skuDTO
     * @param param
     * @param lotRuleDTO
     * @return void
     * @author: WuXian
     * description:  校验参数是否必填
     * create time: 2022/3/14 16:56
     */
    private void checkRequiredParam(SkuDTO skuDTO, SkuLotCheckAndFormatParam param, LotRuleDTO lotRuleDTO) {
        List<LotRuleDetailDTO> lotRuleDetailDTOList = lotRuleDTO.getRuleDetailList();
        if (Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {
            if (ObjectUtils.isEmpty(param.getManufDate()) || param.getManufDate() <= 0) {
                throw new BaseException(BaseBizEnum.TIP, "生产日期不能为空");
            }
            if (ObjectUtils.isEmpty(param.getExpireDate()) || param.getExpireDate() <= 0) {
                throw new BaseException(BaseBizEnum.TIP, "失效日期不能为空");
            }
        } else {
            //生产日期
            if (lotRuleDetailDTOList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_MANUF_DATE.getPropKey())
                    && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
                if (ObjectUtils.isEmpty(param.getManufDate()) || param.getManufDate() <= 0) {
                    throw new BaseException(BaseBizEnum.TIP, "生产日期不能为空");
                }
            }
            //失效日期
            if (lotRuleDetailDTOList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXPIRE_DATE.getPropKey())
                    && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
                if (ObjectUtils.isEmpty(param.getExpireDate()) || param.getExpireDate() <= 0) {
                    throw new BaseException(BaseBizEnum.TIP, "失效日期不能为空");
                }
            }
        }
        //生产批次号
        if (lotRuleDetailDTOList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PRODUCT_NO.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getProductionNo())) {
                throw new BaseException(BaseBizEnum.TIP, "生产批次号不能为空");
            }
        }
        //质量等级
        if (lotRuleDetailDTOList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_SKU_QUALITY.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getSkuQuality())) {
                throw new BaseException(BaseBizEnum.TIP, "商品质量等级不能为空");
            }
        }
        //入库日期
        if (lotRuleDetailDTOList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_RECEIVE_DATE.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getReceiveDate()) || param.getReceiveDate() <= 0) {
                throw new BaseException(BaseBizEnum.TIP, "入库日期不能为空");
            }
        }
        //入库关联单号
        if (lotRuleDetailDTOList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_EXTERNAL_LINK_BILL_NO.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getExternalLinkBillNo())) {
                throw new BaseException(BaseBizEnum.TIP, "入库关联单号不能为空");
            }
        }

        //箱码
        if (lotRuleDetailDTOList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_BOXCODE_TYPE.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getBoxCode())) {
                throw new BaseException(BaseBizEnum.TIP, "箱码不能为空");
            }
        }

        //托盘
        if (lotRuleDetailDTOList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_PALLETCODE_TYPE.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getPalletCode())) {
                throw new BaseException(BaseBizEnum.TIP, "托盘不能为空");
            }
        }

        //暗码
        if (lotRuleDetailDTOList.stream().anyMatch(entity -> Objects.equals(entity.getPropKey(), LotRulePropKeyEnum.PROP_KEY_VALIDITY_CODE.getPropKey())
                && Objects.equals(entity.getEnableControl(), LotRuleRwControlEnum.MUST.getCode()))) {
            if (ObjectUtils.isEmpty(param.getValidityCode())) {
                throw new BaseException(BaseBizEnum.TIP, "暗码不能为空");
            }
        }
        //检验托盘
        if (!StringUtils.isEmpty(param.getPalletCode()) && param.getPalletCode().length() > 30) {
            throw new BaseException(BaseBizEnum.TIP, "托盘号长度不能超过30位");
        }
        //检验托盘
        if (!StringUtils.isEmpty(param.getBoxCode()) && param.getBoxCode().length() > 30) {
            throw new BaseException(BaseBizEnum.TIP, "箱码长度不能超过30位");
        }
    }

}