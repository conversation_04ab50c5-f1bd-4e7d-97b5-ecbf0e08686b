package com.dt.platform.wms.biz;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.platform.wms.dto.asn.AsnRecBizDTO;
import com.dt.platform.wms.dto.rec.*;
import com.dt.platform.wms.param.asn.AsnBizParam;
import com.dt.platform.wms.param.container.ContainerOccupyParam;
import com.dt.platform.wms.param.rec.*;

/**
 * <AUTHOR>
 * @date 2020/9/24 13:35
 */
public interface IRecTallyWorkBenchBiz {

    /**
     * @param param
     * @return com.dt.platform.wms.dto.rec.SkuRecContainerBizDTO
     * @author: WuXian
     * description:  检查容器
     * create time: 2022/3/3 9:54
     */
    SkuRecContainerBizDTO checkContainerOccupy(ContainerOccupyParam param);

    /**
     * @param param
     * @return com.dt.platform.wms.dto.rec.SkuRecCheckBizDTO
     * @author: WuXian
     * description:   检查商品条形码
     * create time: 2022/3/3 9:54
     */
    SkuRecCheckBizDTO checkUpcCode(CheckSkuParam param);

    /**
     * @param param
     * @return com.dt.platform.wms.dto.asn.AsnRecBizDTO
     * @author: WuXian
     * description:  查询收货通知单
     * create time: 2022/3/3 9:54
     */
    AsnRecBizDTO queryAndCheckAsn(AsnRecParam param);

    /**
     * @param param
     * @return com.dt.platform.wms.dto.rec.SkuCommitReturnBizDTO
     * @author: WuXian
     * description:  提交sku
     * create time: 2022/3/3 9:54
     */
    SkuCommitReturnBizDTO submitAndCheckSku(SubmitSkuParam param);

    /**
     * @param form
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  收货作业完成容器
     * create time: 2022/3/3 9:53
     */
    Object checkAndCompleteCont(CompleteReceiptParam form) throws Exception;

    /**
     * @param param
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  清空容器
     * create time: 2022/3/3 9:53
     */
    Boolean clearContainer(ClearContainerParam param);

    /**
     * @param param
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.dt.platform.wms.dto.rec.AsnSkuDetailPageBizDTO>
     * @author: WuXian
     * description:  查询ASN明细
     * create time: 2022/3/3 9:53
     */
    IPage<AsnSkuDetailPageBizDTO> getAsnPageDetail(AsnBizParam param);

    /**
     * @param param
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  收货校验同一容器批次
     * create time: 2022/3/3 9:53
     */
    Boolean checkContainerSkuLot(CheckSkuLotParam param);

    /**
     * @param param
     * @return com.dt.platform.wms.dto.rec.SkuCommitReturnBizDTO
     * <AUTHOR>
     * @describe:
     * @date 2024/3/14 11:01
     */
    SkuCommitReturnBizDTO submitAndCheckSkuTaoTian(SubmitSkuParam param);

    /**
     * @param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 校验SN
     * @date 2024/5/9 9:50
     */
    Boolean checkScanSn(CheckSNParam param);

    /**
     * @param param
     * @return java.lang.String
     * <AUTHOR>
     * @describe: 获取暗码
     * @date 2025/4/23 13:42
     */
    SkuValidityCodeBizDTO queryValidityCode(QueryValidityParam param);
}
