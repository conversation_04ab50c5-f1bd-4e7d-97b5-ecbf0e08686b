package com.dt.platform.wms.client;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.ZoneStatusEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.stock.StorageRuleEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.ZoneDTO;
import com.dt.domain.base.param.WarehouseParam;
import com.dt.domain.base.param.ZoneParam;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.dto.PhysicalPartitionBindDTO;
import com.dt.platform.wms.dto.base.PhysicalPartitionBindBizDTO;
import com.dt.platform.wms.dto.base.ZoneBizDTO;
import com.dt.platform.wms.integration.IRemoteStockLocationClient;
import com.dt.platform.wms.integration.IRemoteWarehouseClient;
import com.dt.platform.wms.integration.IRemoteZoneClient;
import com.dt.platform.wms.param.base.ZoneBizParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class ZoneBizClient implements IZoneBizClient {

    @Resource
    private IRemoteZoneClient remoteZoneClient;


    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;


    @Override
    public Result<Boolean> create(ZoneBizParam param) {
//        //只允许新增一个暂存区仓库
//        checkTempZoneExits(param);
        if (ZoneTypeEnum.ZONE_TYPE_BOX.getType().equals(param.getType()))
            throw new BaseException(BaseBizEnum.TIP, "套盒库区不允许新增");
        checkDistributeZone(param);
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCode(param.getCode());
        List<ZoneDTO> list = remoteZoneClient.getList(zoneParam);
        if (!CollectionUtils.isEmpty(list)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("库区代码:%s 已存在", param.getCode()));
        }
        if (StringUtils.isEmpty(param.getPhysicalPartition())) {
            param.setPhysicalPartition("");
        }
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getCrossBorderCarryoverWarehouseCodeList())
                && defaultWarehouseCodeConfig.getCrossBorderCarryoverWarehouseCodeList().contains(CurrentRouteHolder.getWarehouseCode())
                && !StringUtils.isEmpty(param.getPhysicalPartition())) {

            String warehouseCode = CurrentRouteHolder.getWarehouseCode();
            List<PhysicalPartitionBindDTO> physicalPartitionList = remoteZoneClient.getPhysicalPartitionList(warehouseCode);
            if (CollectionUtils.isEmpty(physicalPartitionList) ||
                    physicalPartitionList.stream().noneMatch(a -> a.getPhysicalPartition().equalsIgnoreCase(param.getPhysicalPartition()))) {
                throw new BaseException(BaseBizEnum.TIP, "未找到有效的物理分区");
            }
            RpcContextUtil.setWarehouseCode(warehouseCode);
        }

        ZoneParam zoneParam1 = new ZoneParam();
        zoneParam1.setEqName(param.getName());
        list = remoteZoneClient.getList(zoneParam1);
        if (!CollectionUtils.isEmpty(list)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("库区名称:%s 已存在", param.getName()));
        }
        ZoneParam bizParam = ConverterUtil.convert(param, ZoneParam.class);
        bizParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        Boolean result = remoteZoneClient.save(bizParam);
        return Result.success(result);
    }

    /**
     * 分销监管区校验 只允许存在正品次品各一个
     *
     * @param param
     */
    private void checkDistributeZone(ZoneBizParam param) {
        if (param.getType().equalsIgnoreCase(ZoneTypeEnum.ZONE_TYPE_DISTRIBUTION.getType())) {
            if (!param.getStorageRule().equalsIgnoreCase(StorageRuleEnum.STORAGE_SKU_N.getRule())) {
                throw new BaseException(BaseBizEnum.TIP, "分销监管区只能创建[一位多品]的库区");
            }
            ZoneParam searchParam = new ZoneParam();
            searchParam.setType(ZoneTypeEnum.ZONE_TYPE_DISTRIBUTION.getType());
            searchParam.setSkuQuality(param.getSkuQuality());
            ZoneDTO zoneDTO = remoteZoneClient.get(searchParam);
            if (!ObjectUtils.isEmpty(zoneDTO) && !zoneDTO.getCode().equals(param.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "分销监管区只允许正品次品各存在一个");
            }
        }
    }

    @Override
    public Result<Boolean> modify(ZoneBizParam param) {
//        //只允许一个暂存区仓库
//        checkTempZoneExits(param);
        if (ZoneTypeEnum.ZONE_TYPE_BOX.getType().equals(param.getType()))
            throw new BaseException(BaseBizEnum.TIP, "套盒库区不允许编辑");
        checkDistributeZone(param);
        ZoneParam zoneParam1 = new ZoneParam();
        zoneParam1.setEqName(param.getName());
        List<ZoneDTO> list = remoteZoneClient.getList(zoneParam1);
        if (list == null) {
            list = new ArrayList<ZoneDTO>();
        }
        if (StringUtils.isEmpty(param.getPhysicalPartition())) {
            param.setPhysicalPartition("");
        }
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getCrossBorderCarryoverWarehouseCodeList())
                && defaultWarehouseCodeConfig.getCrossBorderCarryoverWarehouseCodeList().contains(CurrentRouteHolder.getWarehouseCode())
                && !StringUtils.isEmpty(param.getPhysicalPartition())) {

            String warehouseCode = CurrentRouteHolder.getWarehouseCode();
            List<PhysicalPartitionBindDTO> physicalPartitionList = remoteZoneClient.getPhysicalPartitionList(CurrentRouteHolder.getWarehouseCode());
            if (CollectionUtils.isEmpty(physicalPartitionList) ||
                    physicalPartitionList.stream().noneMatch(a -> a.getPhysicalPartition().equalsIgnoreCase(param.getPhysicalPartition()))) {
                throw new BaseException(BaseBizEnum.TIP, "未找到有效的物理分区");
            }
            RpcContextUtil.setWarehouseCode(warehouseCode);
        }
        list = list.stream().filter(s -> !s.getCode().equalsIgnoreCase(param.getCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(list)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("库区名称:%s 已存在", param.getName()));
        }
        ZoneParam bizParam = ConverterUtil.convert(param, ZoneParam.class);
        Boolean result = remoteZoneClient.modify(bizParam);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> enable(ZoneBizParam param) {
        //库区禁用，先校验是否有库存
        if (Objects.equals(param.getStatus(), ZoneStatusEnum.STATUS_DISABLED.getStatus())) {
            StockLocationParam stockLocationParam = new StockLocationParam();
            stockLocationParam.setZoneCodeList(param.getCodeList());
            stockLocationParam.setHasPhysicalQty(Boolean.TRUE);

            if (remoteStockLocationClient.checkExits(stockLocationParam)) {
                throw new BaseException(BaseBizEnum.TIP, "该库区下存在库存，不允许禁用。");
            }
        }
        ZoneParam bizParam = ConverterUtil.convert(param, ZoneParam.class);
        Boolean result = remoteZoneClient.modify(bizParam);
        return Result.success(result);
    }

    @Override
    public Result<ZoneBizDTO> getDetail(ZoneBizParam param) {
        ZoneParam bizParam = ConverterUtil.convert(param, ZoneParam.class);
        ZoneDTO result = remoteZoneClient.get(bizParam);
        ZoneBizDTO zoneBiz = ConverterUtil.convert(result, ZoneBizDTO.class);
        //填充仓库、用户信息
        if (!ObjectUtils.isEmpty(zoneBiz)) {
            WarehouseDTO warehouse = remoteWarehouseClient.queryByCode(zoneBiz.getWarehouseCode());
            zoneBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
        }
        return Result.success(zoneBiz);
    }

    @Override
    public Result<Page<ZoneBizDTO>> getPage(ZoneBizParam param) {
        ZoneParam bizParam = ConverterUtil.convert(param, ZoneParam.class);
        Page<ZoneDTO> page = remoteZoneClient.getPage(bizParam);
        Page<ZoneBizDTO> result = ConverterUtil.convertPage(page, ZoneBizDTO.class);
        //填充仓库、用户信息
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            List<String> warehouseCodeList = result.getRecords()
                    .stream()
                    .flatMap(a -> Stream.of(a.getWarehouseCode()))
                    .distinct()
                    .collect(Collectors.toList());
            WarehouseParam warehouseParam = new WarehouseParam();
            warehouseParam.setCodeList(warehouseCodeList);
            List<WarehouseDTO> warehouseList = remoteWarehouseClient.queryList(warehouseParam);
            result.getRecords().stream().forEach(a -> {
                WarehouseDTO warehouse = warehouseList.stream()
                        .filter(b -> b.getCode().equals(a.getWarehouseCode()))
                        .findAny()
                        .orElse(null);
                a.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
            });
        }
        return Result.success(result);
    }

    @Override
    public Result<List<IdNameVO>> getAllZoneList() {
        return Result.success(remoteZoneClient.getAllZoneList());
    }

    @Override
    public Result<List<IdNameVO>> getZonePickAndStorageList() {
        return Result.success(remoteZoneClient.getZonePickAndStorageList());
    }

    @Override
    public Result<List<IdNameVO>> getPickZoneList() {
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setType(ZoneTypeEnum.ZONE_TYPE_PICK.getType());
        List<ZoneDTO> list = remoteZoneClient.getList(zoneParam);
        if (CollectionUtils.isEmpty(list)) {
            return Result.success(new ArrayList<>());
        }
        list = list.stream().filter(a -> a.getStatus().equals(ZoneStatusEnum.STATUS_ENABLED.getStatus())).collect(Collectors.toList());
        return Result.success(IdNameVO.build(list, "code", "name"));
    }

    @Override
    public Result<List<IdNameVO>> getStoreZoneList() {
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setType(ZoneTypeEnum.ZONE_TYPE_STORE.getType());
        List<ZoneDTO> list = remoteZoneClient.getList(zoneParam);
        if (CollectionUtils.isEmpty(list)) {
            return Result.success(new ArrayList<>());
        }
        list = list.stream().filter(a -> a.getStatus().equals(ZoneStatusEnum.STATUS_ENABLED.getStatus())).collect(Collectors.toList());
        return Result.success(IdNameVO.build(list, "code", "name"));
    }

    @Override
    public Result<List<ZoneBizDTO>> getList(ZoneBizParam param) {
        ZoneParam bizParam = ConverterUtil.convert(param, ZoneParam.class);
        List<ZoneDTO> list = remoteZoneClient.getList(bizParam);
        List<ZoneBizDTO> zoneBizDTOS = ConverterUtil.convertList(list, ZoneBizDTO.class);
        return Result.success(zoneBizDTOS);
    }

    @Override
    public Result<List<IdNameVO>> getZonePickAndStorageEffectiveList() {
        ZoneParam zoneParam = new ZoneParam();
        List<ZoneDTO> zoneDTOList = remoteZoneClient.getList(zoneParam);
        if (CollectionUtils.isEmpty(zoneDTOList)) {
            return Result.success(new ArrayList<>());
        }
        zoneDTOList = zoneDTOList.stream()
                .filter(a -> a.getStatus().equals(ZoneStatusEnum.STATUS_ENABLED.getStatus()))
                .filter(a -> Arrays.asList(ZoneTypeEnum.ZONE_TYPE_PICK.getType(), ZoneTypeEnum.ZONE_TYPE_STORE.getType()).contains(a.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(zoneDTOList)) {
            return Result.success(new ArrayList<>());
        }
        return Result.success(IdNameVO.build(zoneDTOList, "code", "name"));
    }

    @Override
    public Result<List<IdNameVO>> getPickSkuZoneList() {
        //获取拣选区+预包区
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setTypeList(Arrays.asList(ZoneTypeEnum.ZONE_TYPE_PICK.getType(), ZoneTypeEnum.ZONE_TYPE_PREP.getType()));
        List<ZoneDTO> pickList = remoteZoneClient.getList(zoneParam);
        if (CollectionUtils.isEmpty(pickList)) {
            return Result.success(new ArrayList<>());
        }
        pickList = pickList.stream().filter(a -> a.getStatus().equals(ZoneStatusEnum.STATUS_ENABLED.getStatus())).collect(Collectors.toList());
        return Result.success(IdNameVO.build(pickList, "code", "name"));
    }

    @Override
    public Result<List<IdNameVO>> getPhysicalPartitionList() {
        //TODO 获取物理防火分区数据 ---调用ERP获取物理防火分区企业
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        if (StringUtils.isEmpty(warehouseCode)) {
            throw new BaseException(BaseBizEnum.TIP, "未获取到当前仓库信息");
        }
        //大贸或局部跨境仓可能没有 返回空集合
        List<PhysicalPartitionBindDTO> physicalPartitionList = remoteZoneClient.getPhysicalPartitionList(warehouseCode);
        if (CollectionUtils.isEmpty(physicalPartitionList)) {
            return Result.success(new ArrayList<>());
        }
        return Result.success(IdNameVO.build(physicalPartitionList, "physicalPartition", "physicalPartition"));
    }

    @Override
    public Result<List<PhysicalPartitionBindBizDTO>> getPhysicalPartitionEnterpriseBinding() {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        List<ZoneDTO> zoneDTOList = remoteZoneClient.getList(new ZoneParam());
        if (CollectionUtil.isEmpty(zoneDTOList)) return Result.success(new ArrayList<>());
        List<String> zoneCodeList = zoneDTOList.stream().map(ZoneDTO::getCode).collect(Collectors.toList());
        List<PhysicalPartitionBindDTO> physicalPartitionList = remoteZoneClient.getPhysicalPartitionList(warehouseCode, zoneCodeList);
        if (CollectionUtils.isEmpty(physicalPartitionList)) {
            return Result.success(new ArrayList<>());
        }
        return Result.success(ConverterUtil.convertList(physicalPartitionList, PhysicalPartitionBindBizDTO.class));
    }


}
