package com.dt.platform.wms.biz.impl;

import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.LocationStatusEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.pre.SkuIsPreEnum;
import com.dt.component.common.enums.sku.SkuNewOrOldCtrlEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.domain.base.dto.LocationDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.ZoneDTO;
import com.dt.domain.base.param.LocationParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.ZoneParam;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.biz.ILocationReportBiz;
import com.dt.platform.wms.dto.location.CacheLocationReport;
import com.dt.platform.wms.dto.location.LocationReport;
import com.dt.platform.wms.dto.location.LocationUseReportBizDTO;
import com.dt.platform.wms.dto.location.StockLocationReport;
import com.dt.platform.wms.integration.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LocationReportBizImpl implements ILocationReportBiz {


    @Resource
    IRemoteLocationClient remoteLocationClient;

    @Resource
    IRemoteZoneClient remoteZoneClient;

    @Resource
    IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    IRemoteSkuClient remoteSkuClient;

    @Resource
    IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Override
    public List<LocationUseReportBizDTO> getLocationUseReport() {

        String cacheKey = CurrentRouteHolder.getWarehouseCode() + ":getLocationUseReport";
        if (stringRedisTemplate.hasKey(cacheKey)) {
            CacheLocationReport cacheLocationReport = JSONUtil.toBean(stringRedisTemplate.opsForValue().get(cacheKey), CacheLocationReport.class);
            if (cacheLocationReport != null && !CollectionUtils.isEmpty(cacheLocationReport.getLocationUseReportBizDTOList())) {
                log.info("cache getLocationUseReport:{}", cacheLocationReport.getLocationUseReportBizDTOList().size());
                return cacheLocationReport.getLocationUseReportBizDTOList();
            }
        }
        Long time = System.currentTimeMillis();
        //获取所有有效库区
        ZoneParam zoneParam = new ZoneParam();
//        zoneParam.setStatus(ZoneStatusEnum.STATUS_ENABLED.getStatus());
        List<ZoneDTO> zoneDTOList = remoteZoneClient.getList(zoneParam);
        if (CollectionUtils.isEmpty(zoneDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "无有效库区");
        }
        List<String> zoneCodeList = zoneDTOList.stream().sorted(Comparator.comparing(ZoneDTO::getStatus, Comparator.reverseOrder())
                        .thenComparing(Comparator.comparing(ZoneDTO::getCode)))
                .map(ZoneDTO::getCode)
                .collect(Collectors.toList());
        //获取库区所有有效库位
        LocationParam locationParam = new LocationParam();
        locationParam.setZoneCodeList(zoneCodeList);
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
        if (CollectionUtils.isEmpty(locationDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "无有效库位");
        }
        List<LocationReport> locationReportList = ConverterUtil.convertList(locationDTOList, LocationReport.class);
        //获取所有有效库位
        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setLocationCodeList(locationDTOList.stream().map(LocationDTO::getCode).collect(Collectors.toList()));
        stockLocationParam.setHasPhysicalQty(true);
        List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getList(stockLocationParam);
        List<StockLocationReport> stockLocationReportList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(stockLocationDTOList)) {
            stockLocationReportList = ConverterUtil.convertList(stockLocationDTOList, StockLocationReport.class);
        }
        //获取所有商品信息
        List<SkuDTO> skuList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(stockLocationReportList)) {
            SkuParam skuParam = new SkuParam();
            skuParam.setIsPre(SkuIsPreEnum.NORMAL.getCode());
            skuParam.setCodeList(stockLocationReportList.stream().map(StockLocationReport::getSkuCode).distinct().collect(Collectors.toList()));
            skuParam.setCargoCodeList(stockLocationReportList.stream().map(StockLocationReport::getCargoCode).distinct().collect(Collectors.toList()));
            skuParam.setIsNewRecord(SkuNewOrOldCtrlEnum.SKU_OLD_CTRL_NO.getCode());
            List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
            if (!CollectionUtils.isEmpty(skuDTOList)) {
                skuList = skuDTOList;
            }
        }
        //计算有库存数据体积
        if (!CollectionUtils.isEmpty(skuList) && !CollectionUtils.isEmpty(stockLocationReportList)) {
            List<SkuDTO> finalSkuList = skuList;
            stockLocationReportList.parallelStream().forEach(stockLocationReport -> {
                finalSkuList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(stockLocationReport.getCargoCode()))
                        .filter(a -> a.getCode().equalsIgnoreCase(stockLocationReport.getSkuCode()))
                        .findFirst().ifPresent(skuDTO -> {
                            stockLocationReport.setVolume(skuDTO.getVolume().multiply(stockLocationReport.getPhysicalQty()));
                        });
            });
        }
        //仓库编码
        WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(CurrentRouteHolder.getWarehouseCode());
        //库位按库区分组
        Map<String, List<LocationReport>> locationDTOMap = locationReportList.stream().collect(Collectors.groupingBy(LocationReport::getZoneCode));
        //库存按库区分组
        Map<String, List<StockLocationReport>> stockLocationMap = stockLocationReportList.stream().collect(Collectors.groupingBy(StockLocationReport::getZoneCode));

        List<LocationUseReportBizDTO> locationUseReportBizDTOList = new ArrayList<>();
        //分析按库区
        zoneCodeList.parallelStream().forEach(zoneCode -> {
            LocationUseReportBizDTO locationUseReportBizDTO = new LocationUseReportBizDTO();
            locationUseReportBizDTO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
            locationUseReportBizDTO.setWarehouseCodeName("");
            if (warehouseDTO != null) {
                locationUseReportBizDTO.setWarehouseCodeName(warehouseDTO.getName());
            }
            locationUseReportBizDTO.setZoneCode(zoneCode);
            zoneDTOList.stream().filter(a -> a.getCode().equalsIgnoreCase(zoneCode)).findAny().ifPresent(a -> {
                locationUseReportBizDTO.setZoneName(a.getName());
                locationUseReportBizDTO.setZoneType(a.getType());
                if (!StringUtils.isEmpty(a.getType())) {
                    locationUseReportBizDTO.setZoneTypeName(ZoneTypeEnum.getEnum(a.getType()).getName());
                }
            });
            List<LocationReport> locationList = locationDTOMap.getOrDefault(zoneCode, new ArrayList<>());

            List<String> enableLocationCodeList = locationList.stream().filter(a -> a.getStatus().equals(LocationStatusEnum.STATUS_ENABLED.getStatus())).map(LocationReport::getCode).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(enableLocationCodeList)) {
                enableLocationCodeList = new ArrayList<>();
            }

            locationUseReportBizDTO.setLocationCount(locationList.size());
            locationUseReportBizDTO.setLocationDisEnableCount((int) locationList.stream().filter(a -> a.getStatus().equals(LocationStatusEnum.STATUS_DISABLED.getStatus())).count());
            locationUseReportBizDTO.setLocationEnableCount((int) locationList.stream().filter(a -> a.getStatus().equals(LocationStatusEnum.STATUS_ENABLED.getStatus())).count());
            List<StockLocationReport> stockLocationDList = stockLocationMap.getOrDefault(zoneCode, new ArrayList<>());

            //总库位-有库存库位=无库存库位
            Integer enableCount = locationList.stream().filter(a -> a.getStatus().equals(LocationStatusEnum.STATUS_ENABLED.getStatus())).collect(Collectors.toList()).size();
            List<String> finalEnableLocationCodeList = enableLocationCodeList;
            Integer stockReportCount = stockLocationDList.stream()
                    .filter(a -> finalEnableLocationCodeList.contains(a.getLocationCode()))
                    .map(StockLocationReport::getLocationCode).distinct().collect(Collectors.toList()).size();
            locationUseReportBizDTO.setLocationEmptyCount(enableCount - stockReportCount);
            //计算百分比
            calculationPercent(stockLocationDList.stream().filter(a -> finalEnableLocationCodeList.contains(a.getLocationCode())).collect(Collectors.toList()),
                    locationList.stream().filter(a -> a.getStatus().equals(LocationStatusEnum.STATUS_ENABLED.getStatus())).collect(Collectors.toList()),
                    locationUseReportBizDTO);
            locationUseReportBizDTOList.add(locationUseReportBizDTO);
        });
        log.info("Immediately getLocationUseReport:{}", System.currentTimeMillis() - time);
        //缓存5分钟
        CacheLocationReport cacheLocationReport = new CacheLocationReport();
        cacheLocationReport.setLocationUseReportBizDTOList(locationUseReportBizDTOList);
        stringRedisTemplate.opsForValue().set(cacheKey, JSONUtil.toJsonStr(cacheLocationReport), 1, TimeUnit.MINUTES);
        return locationUseReportBizDTOList;
    }

    private void calculationPercent(List<StockLocationReport> stockLocationDList, List<LocationReport> locationList, LocationUseReportBizDTO locationUseReportBizDTO) {
        if (CollectionUtils.isEmpty(locationList) || CollectionUtils.isEmpty(stockLocationDList)) {
            locationUseReportBizDTO.setTenPercent(0);
            locationUseReportBizDTO.setThirtyPercent(0);
            locationUseReportBizDTO.setFiftyPercent(0);
            locationUseReportBizDTO.setEightyPercent(0);
            locationUseReportBizDTO.setOneHundredAndTwentyPercent(0);
            locationUseReportBizDTO.setOneHundredAndTwentyPercentOver(0);
            return;
        }
        locationList.forEach(locationReport -> {
            BigDecimal locationVolume = locationReport.getLength().multiply(locationReport.getWidth()).multiply(locationReport.getHeight()).setScale(2, BigDecimal.ROUND_HALF_UP);
            List<StockLocationReport> locationReportList = stockLocationDList.stream()
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(locationReport.getCode()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(locationReportList)) {
                locationReport.setStockVolume(locationReportList.stream().map(a -> {
                    return a.getVolume() == null ? BigDecimal.ZERO : a.getVolume();
                }).reduce(BigDecimal.ZERO, BigDecimal::add));
            } else {
                locationReport.setStockVolume(BigDecimal.ZERO);
            }
            //百分比
            BigDecimal percent = BigDecimal.ZERO;
            if (locationVolume.compareTo(BigDecimal.ZERO) > 0) {
                percent = locationReport.getStockVolume().divide(locationVolume, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
            }
            locationReport.setPercent(percent);
        });
        //10%
        locationUseReportBizDTO.setTenPercent((int) locationList.stream()
                .filter(a -> a.getPercent().compareTo(BigDecimal.ZERO) > 0)
                .filter(a -> a.getPercent().compareTo(new BigDecimal("10")) <= 0).count());
        //30%
        locationUseReportBizDTO.setThirtyPercent((int) locationList.stream()
                .filter(a -> a.getPercent().compareTo(new BigDecimal("10")) > 0)
                .filter(a -> a.getPercent().compareTo(new BigDecimal("30")) <= 0)
                .count());
        //50%
        locationUseReportBizDTO.setFiftyPercent((int) locationList.stream()
                .filter(a -> a.getPercent().compareTo(new BigDecimal("30")) > 0)
                .filter(a -> a.getPercent().compareTo(new BigDecimal("50")) <= 0)
                .count());
        //80%
        locationUseReportBizDTO.setEightyPercent((int) locationList.stream()
                .filter(a -> a.getPercent().compareTo(new BigDecimal("50")) > 0)
                .filter(a -> a.getPercent().compareTo(new BigDecimal("80")) <= 0)
                .count());
        //120%
        locationUseReportBizDTO.setOneHundredAndTwentyPercent((int) locationList.stream()
                .filter(a -> a.getPercent().compareTo(new BigDecimal("80")) > 0)
                .filter(a -> a.getPercent().compareTo(new BigDecimal("120")) <= 0)
                .count());
        //大120%
        locationUseReportBizDTO.setOneHundredAndTwentyPercentOver((int) locationList.stream()
                .filter(a -> a.getPercent().compareTo(new BigDecimal("120")) > 0).count());
    }


}
