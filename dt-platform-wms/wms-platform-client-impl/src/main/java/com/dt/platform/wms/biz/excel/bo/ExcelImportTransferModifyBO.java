package com.dt.platform.wms.biz.excel.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 
 */
@Data
public class ExcelImportTransferModifyBO implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @ExcelProperty(value = "*转移单号")
    private String transferCode;

    @ExcelProperty(value = "*来源商品代码")
    private String originSkuCode;

    @ExcelProperty(value = "*来源库位")
    private String originLocationCode;

    @ExcelProperty(value = "*来源批次ID")
    private String originSkuLotNo;

    @ExcelProperty(value = "*目标商品属性")
    private String targetSkuQualityName;

    @ExcelProperty(value = "*目标残次等级")
    private String targetInventoryTypeName;

    @ExcelProperty(value = "*目标库位")
    private String targetLocationCode;

    @ExcelProperty(value = "目标生产日期")
    private String targetProductDateStr;

    @ExcelProperty(value = "目标失效日期")
    private String targetExpireDateStr;

    @ExcelProperty(value = "目标效期暗码")
    private String targetValidityCode;

    @ExcelProperty(value = "目标生产批次号")
    private String targetProductNo;

    @ExcelProperty(value = "目标托盘号")
    private String targetPalletCode;

    @ExcelProperty(value = "目标箱码")
    private String targetBoxCode;

    @ExcelProperty(value = "目标入库日期")
    private String targetReceiveDateStr;

    @ExcelProperty(value = "目标入库关联号")
    private String targetExternalLinkBillNo;

    @ExcelProperty(value = "*转移数量")
    private BigDecimal transferAmount;

    @ExcelProperty(value = "调整原因")
    private String detailReasonDesc;

    @ExcelProperty(value = "责任方")
    private String rpDesc;

    @ExcelProperty(value = "备注")
    private String remark;
    
    private String warehouseCode;
    private String targetSkuQuality;
    
    private Long targetProductDate;
    private Long targetExpireDate;
    private Long targetReceiveDate;

    private String businessType;
    private String rp;
    private String detailReason;

    private String targetInventoryType;
    
}
