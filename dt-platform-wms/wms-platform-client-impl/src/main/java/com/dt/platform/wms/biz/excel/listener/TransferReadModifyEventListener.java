package com.dt.platform.wms.biz.excel.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.dt.component.common.enums.base.LocationTypeEnum;
import com.dt.component.common.enums.cargo.CargoOwnerStatusEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.transfer.TransferDetailRPEnum;
import com.dt.component.common.enums.transfer.TransferDetailReasonEnum;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.LocationDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.param.LocationParam;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.bill.dto.TransferDTO;
import com.dt.domain.bill.param.TransferParam;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.excel.DefaultReadEventListener;
import com.dt.platform.wms.biz.excel.bo.ExcelImportTransferModifyBO;
import com.dt.platform.wms.client.ITransferBizClient;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.param.rec.CheckSkuLotParam;
import com.dt.platform.wms.param.transfer.TransferDetailUpdateBizParam;
import com.dt.platform.wms.param.transfer.TransferUpdateBizParam;
import lombok.Data;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/2/16 18:16
 */
@Data
@Component("transferReadModifyEventListener")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class TransferReadModifyEventListener extends DefaultReadEventListener<ExcelImportTransferModifyBO>    {

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteLocationClient remoteLocationClient;

    @Resource
    private ITransferBizClient transferBizClient;

    @Resource
    private IRemoteTransferClient remoteTransferClient;

    @Resource
    private ISkuLotBiz skuLotBiz;

    @Resource
    DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Override
    public void checkExcel(ExcelImportTransferModifyBO excelImportTransferModifyBO, AnalysisContext context) {
        if (StrUtil.isBlank(excelImportTransferModifyBO.getTransferCode())) {
            errorInfoList.add(getExcelErrorInfo("*转移单号", "转移单号必填"));
        }
        if (StrUtil.isBlank(excelImportTransferModifyBO.getOriginSkuCode())) {
            errorInfoList.add(getExcelErrorInfo("*来源商品代码", "来源商品代码必填"));
        }
        if (StrUtil.isBlank(excelImportTransferModifyBO.getOriginLocationCode())) {
            errorInfoList.add(getExcelErrorInfo("*来源库位", "来源库位必填"));
        }
        if (StrUtil.isBlank(excelImportTransferModifyBO.getOriginSkuLotNo())) {
            errorInfoList.add(getExcelErrorInfo("*来源批次ID", "来源批次ID必填"));
        }
        if (StrUtil.isBlank(excelImportTransferModifyBO.getTargetSkuQualityName())) {
            errorInfoList.add(getExcelErrorInfo("*目标商品属性", "目标商品属性必填"));
        }
        if (StrUtil.isBlank(excelImportTransferModifyBO.getTargetInventoryTypeName())) {
            errorInfoList.add(getExcelErrorInfo("*目标残次等级", "目标残次等级必填"));
        }
        if (StrUtil.isBlank(excelImportTransferModifyBO.getTargetLocationCode())) {
            errorInfoList.add(getExcelErrorInfo("*目标库位", "目标库位必填"));
        }
        if (ObjectUtil.isEmpty(excelImportTransferModifyBO.getTransferAmount())) {
            errorInfoList.add(getExcelErrorInfo("*转移数量", "转移数量必填"));
        } else if (excelImportTransferModifyBO.getTransferAmount().compareTo(BigDecimal.ZERO) <= 0) {
            errorInfoList.add(getExcelErrorInfo("*转移数量", "转移数量必须大于零"));
        } else if (excelImportTransferModifyBO.getTransferAmount().stripTrailingZeros().scale() > 0) {
            errorInfoList.add(getExcelErrorInfo("*转移数量", "转移数量必须是整数"));
        }

        //明细原因
        if (!StringUtils.isEmpty(excelImportTransferModifyBO.getDetailReasonDesc()) && Arrays.stream(TransferDetailReasonEnum.values()).noneMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferModifyBO.getDetailReasonDesc()))) {
            errorInfoList.add(getExcelErrorInfo("调整原因", "转移单的调整原因填写错误,请核查"));
        }
        if (!StringUtils.isEmpty(excelImportTransferModifyBO.getDetailReasonDesc()) && Arrays.stream(TransferDetailReasonEnum.values()).anyMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferModifyBO.getDetailReasonDesc()))) {
            Arrays.stream(TransferDetailReasonEnum.values())
                    .filter(it -> it.getName().equalsIgnoreCase(excelImportTransferModifyBO.getDetailReasonDesc()))
                    .findFirst().ifPresent(a -> excelImportTransferModifyBO.setDetailReason(a.getCode()));
        }
        //明细责任方
        if (!StringUtils.isEmpty(excelImportTransferModifyBO.getRpDesc()) && Arrays.stream(TransferDetailRPEnum.values()).noneMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferModifyBO.getRpDesc()))) {
            errorInfoList.add(getExcelErrorInfo("责任方", "转移单的责任方填写错误,请核查"));
        }
        if (!StringUtils.isEmpty(excelImportTransferModifyBO.getRpDesc()) && Arrays.stream(TransferDetailRPEnum.values()).anyMatch(it -> it.getName().equalsIgnoreCase(excelImportTransferModifyBO.getRpDesc()))) {
            Arrays.stream(TransferDetailRPEnum.values())
                    .filter(it -> it.getName().equalsIgnoreCase(excelImportTransferModifyBO.getRpDesc()))
                    .findFirst().ifPresent(a -> excelImportTransferModifyBO.setRp(a.getCode()));
        }
        //调整字节信息校验
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList()) && defaultWarehouseCodeConfig.getWarehouseAdjustAndTransferOpenCodeList().contains(CurrentRouteHolder.getWarehouseCode())) {
            //调整原因
            if (StringUtils.isEmpty(excelImportTransferModifyBO.getDetailReasonDesc())) {
                errorInfoList.add(getExcelErrorInfo("调整原因", "当前仓库,转移单的调整原因必填"));
            }
            //备注
            if (StringUtils.isEmpty(excelImportTransferModifyBO.getRemark())) {
                errorInfoList.add(getExcelErrorInfo("备注", "当前仓库,转移单的明细备注必填"));
            }
            //责任人
            if (StringUtils.isEmpty(excelImportTransferModifyBO.getRpDesc())) {
                errorInfoList.add(getExcelErrorInfo("责任方", "当前仓库,转移单的责任方必填"));
            }
        }
        if (CollectionUtil.isNotEmpty(errorInfoList)) {
            for (String errorInfo : errorInfoList) {
                callBackPublicService(false, uid, context.readRowHolder().getRowIndex(), excelImportTransferModifyBO, errorInfo);
            }
        }
    }

    private void resetData(ExcelImportTransferModifyBO excelImportTransferModifyBO, AnalysisContext context) {
        List<String> errorMessage = new ArrayList<>();
        excelImportTransferModifyBO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        if (StrUtil.isNotBlank(excelImportTransferModifyBO.getTargetSkuQualityName())) {
            try {
                SkuQualityEnum skuQualityEnum = SkuQualityEnum.getEnumByMessage(excelImportTransferModifyBO.getTargetSkuQualityName());
                excelImportTransferModifyBO.setTargetSkuQuality(skuQualityEnum.getLevel());
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("*目标商品属性", "正残属性错误"));
            }
        }
        if (StrUtil.isNotBlank(excelImportTransferModifyBO.getTargetInventoryTypeName())) {
            try {
                InventoryTypeEnum inventoryTypeEnum = InventoryTypeEnum.getEnumByMessage(excelImportTransferModifyBO.getTargetInventoryTypeName());
                excelImportTransferModifyBO.setTargetInventoryType(inventoryTypeEnum.getCode());
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("*目标残次等级", "目标残次等级错误"));
            }
        }
        if (ObjectUtil.isNotEmpty(excelImportTransferModifyBO.getTargetProductDateStr())) {
            try {
                excelImportTransferModifyBO.setTargetProductDate(TransferReadEventListener.parseDate(excelImportTransferModifyBO.getTargetProductDateStr()));
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("目标生产日期", "目标生产日期格式错误"));
            }
        }
        if (ObjectUtil.isNotEmpty(excelImportTransferModifyBO.getTargetExpireDateStr())) {
            try {
                excelImportTransferModifyBO.setTargetExpireDate(TransferReadEventListener.parseDate(excelImportTransferModifyBO.getTargetExpireDateStr()));
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("目标失效日期", "目标失效日期格式错误"));
            }
        }
        if (ObjectUtil.isNotEmpty(excelImportTransferModifyBO.getTargetReceiveDateStr())) {
            try {
                excelImportTransferModifyBO.setTargetReceiveDate(TransferReadEventListener.parseDate(excelImportTransferModifyBO.getTargetReceiveDateStr()));
            } catch (Exception e) {
                errorMessage.add(getExcelErrorInfo("目标入库日期", "目标入库日期格式错误"));
            }
        }


//        if (ObjectUtil.isNotEmpty(excelImportTransferModifyBO.getTargetValidityCode())) {
//            boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", excelImportTransferModifyBO.getTargetValidityCode());
//            if (!matches) {
//                errorMessage.add(getExcelErrorInfo("目标效期暗码", "目标效期暗码:英文大小写、数字、中划线,长度30,请核查"));
//            }
//        }
        if (ObjectUtil.isNotEmpty(excelImportTransferModifyBO.getTargetBoxCode())) {
            boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", excelImportTransferModifyBO.getTargetBoxCode());
            if (!matches) {
                errorMessage.add(getExcelErrorInfo("目标箱码", "目标箱码:英文大小写、数字、中划线,长度30,请核查"));
            }
        }
        if (ObjectUtil.isNotEmpty(excelImportTransferModifyBO.getTargetPalletCode())) {
            boolean matches = Pattern.matches("^[a-zA-Z0-9-]{1,30}$", excelImportTransferModifyBO.getTargetPalletCode());
            if (!matches) {
                errorMessage.add(getExcelErrorInfo("目标托盘号", "目标托盘号:英文大小写、数字、中划线,长度30,请核查"));
            }
        }

        if (CollectionUtil.isNotEmpty(errorMessage)) {
            for (String errorInfo : errorMessage) {
                callBackPublicService(false, uid, context.readRowHolder().getRowIndex(), excelImportTransferModifyBO, errorInfo);
            }
        }

        errorInfoList.addAll(errorMessage);
    }

    @Override
    public void invoke(ExcelImportTransferModifyBO excelImportTransferModifyBO, AnalysisContext context) {
        //跳过 0 、 1 数据
        if (context.readRowHolder().getRowIndex() <= 1) {
            return;
        }

        checkExcel(excelImportTransferModifyBO, context);
        resetData(excelImportTransferModifyBO, context);

        // 这里只读取，全部读完统一校验
        dataList.add(excelImportTransferModifyBO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

        if (CollectionUtil.isEmpty(dataList)) return;
        TransferParam transferParam = new TransferParam();
        transferParam.setCode(dataList.get(0).getTransferCode());
        TransferDTO transferDTO = remoteTransferClient.get(transferParam);
        if (ObjectUtil.isEmpty(transferDTO)) {
            callBackPublicService(false, uid, 0, dataList.get(0), "转移单号不存在");
            errorInfoList.add(getExcelErrorInfo("业务校验", "转移单不存在"));
        }

        String cargoCode = transferDTO.getCargoCode();
        List<String> skuCodeList = dataList.stream().map(ExcelImportTransferModifyBO::getOriginSkuCode).distinct().collect(Collectors.toList());
        List<String> skuLotNoList = dataList.stream().map(ExcelImportTransferModifyBO::getOriginSkuLotNo).distinct().collect(Collectors.toList());
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(cargoCode);

        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(cargoCode);
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);

        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(cargoCode);
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);

        List<String> locationCodeList = dataList.stream().flatMap(excelImportTransferBO -> Stream.of(excelImportTransferBO.getOriginLocationCode(), excelImportTransferBO.getTargetLocationCode()))
                .distinct()
                .collect(Collectors.toList());

        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(locationCodeList);
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);

        if (basicCheckFail()) return;
        if (checkCargoExistFail(cargoOwnerDTO)) return;
        if (checkTransferAmountFail()) return;
        if (checkSkuFail(skuDTOList, locationDTOList, skuLotDTOList)) return;

        // save
        TransferUpdateBizParam transferUpdateBizParam = new TransferUpdateBizParam();
        transferUpdateBizParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        transferUpdateBizParam.setCargoCode(cargoCode);
        transferUpdateBizParam.setFlag(TransferUpdateBizParam.FlagEnum.IMPORT_UPDATE);
        transferUpdateBizParam.setTransferCode(transferDTO.getCode());
        transferUpdateBizParam.setBusinessType(transferDTO.getBusinessType());
        ArrayList<TransferDetailUpdateBizParam> detailList = new ArrayList<>();
        for (ExcelImportTransferModifyBO excelImportTransferModifyBO : dataList) {
            TransferDetailUpdateBizParam transferDetailUpdateBizParam = new TransferDetailUpdateBizParam();
            transferDetailUpdateBizParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
            transferDetailUpdateBizParam.setCargoCode(cargoCode);
            transferDetailUpdateBizParam.setSkuCode(excelImportTransferModifyBO.getOriginSkuCode());
            transferDetailUpdateBizParam.setSkuLotNo(excelImportTransferModifyBO.getOriginSkuLotNo());
            transferDetailUpdateBizParam.setLocationCode(excelImportTransferModifyBO.getOriginLocationCode());
            transferDetailUpdateBizParam.setTargetLocationCode(excelImportTransferModifyBO.getTargetLocationCode());
            transferDetailUpdateBizParam.setTargetSkuCode(excelImportTransferModifyBO.getOriginSkuCode());
            skuLotDTOList.stream()
                    .filter(skuLotDTO -> skuLotDTO.getCode().equals(excelImportTransferModifyBO.getOriginSkuLotNo()))
                    .findFirst()
                    .ifPresent(skuLotDTO -> {
                        transferDetailUpdateBizParam.setManufDate(skuLotDTO.getManufDate());
                        transferDetailUpdateBizParam.setExpireDate(skuLotDTO.getExpireDate());
                        transferDetailUpdateBizParam.setSkuQuality(skuLotDTO.getSkuQuality());
                        transferDetailUpdateBizParam.setProductionNo(skuLotDTO.getProductionNo());
                        transferDetailUpdateBizParam.setWithdrawDate(skuLotDTO.getWithdrawDate());
                        transferDetailUpdateBizParam.setTargetReceiveDate(skuLotDTO.getReceiveDate());
                    });
            transferDetailUpdateBizParam.setTargetManufDate(excelImportTransferModifyBO.getTargetProductDate());
            transferDetailUpdateBizParam.setTargetExpireDate(excelImportTransferModifyBO.getTargetExpireDate());
            transferDetailUpdateBizParam.setTargetSkuQuality(excelImportTransferModifyBO.getTargetSkuQuality());
            transferDetailUpdateBizParam.setTargetProductionNo(excelImportTransferModifyBO.getTargetProductNo());
            transferDetailUpdateBizParam.setTargetReceiveDate(excelImportTransferModifyBO.getTargetReceiveDate());
            transferDetailUpdateBizParam.setTargetExternalLinkBillNo(excelImportTransferModifyBO.getTargetExternalLinkBillNo());
            transferDetailUpdateBizParam.setChangeQty(excelImportTransferModifyBO.getTransferAmount());
            transferDetailUpdateBizParam.setTargetInventoryType(excelImportTransferModifyBO.getTargetInventoryType());

            transferDetailUpdateBizParam.setTargetValidityCode(excelImportTransferModifyBO.getTargetValidityCode());
            transferDetailUpdateBizParam.setTargetPalletCode(excelImportTransferModifyBO.getTargetPalletCode());
            transferDetailUpdateBizParam.setTargetBoxCode(excelImportTransferModifyBO.getTargetBoxCode());

            transferDetailUpdateBizParam.setReason(excelImportTransferModifyBO.getDetailReason());
            transferDetailUpdateBizParam.setRemark(excelImportTransferModifyBO.getRemark());
            transferDetailUpdateBizParam.setRp(excelImportTransferModifyBO.getRp());

            detailList.add(transferDetailUpdateBizParam);
        }
        transferUpdateBizParam.setTransferDetailUpdateBizParamList(detailList);
        try {
            transferBizClient.updateTransferDetail(transferUpdateBizParam);
            IntStream.range(3, dataList.size() + 3).forEach(it -> {
                callBackPublicService(true, uid, it, dataList.get(it - 3), "");
            });
        } catch (Exception e) {
            callBackPublicService(false, uid, 0, null, e.getMessage());
        }
    }

    /**
     * 基础校验
     *
     * @return
     */
    private boolean basicCheckFail() {
        return CollectionUtil.isNotEmpty(errorInfoList);
    }

    /**
     * 转移单数量校验
     *
     * @return
     */
    private boolean checkTransferAmountFail() {
        long count = dataList.stream()
                .map(ExcelImportTransferModifyBO::getTransferCode)
                .distinct()
                .count();
        if (count > 1) {
            callBackPublicService(false, uid, 0, dataList.get(0), "一次只能导入修改一个转移单");
            return true;
        }
        return false;
    }

    private boolean checkSkuFail(List<SkuDTO> skuDTOList, List<LocationDTO> locationDTOList, List<SkuLotDTO> skuLotDTOList) {
        int errorCount = 0;
        int lineNo = 0;
        for (ExcelImportTransferModifyBO excelImportTransferModifyBO : dataList) {
            lineNo++;
            Optional<SkuDTO> optional = skuDTOList.stream().filter(skuDTO -> skuDTO.getCode().equals(excelImportTransferModifyBO.getOriginSkuCode()))
                    .findFirst();
            // 校验商品批次属性
            if (optional.isPresent()) {
                SkuDTO skuDTO = optional.get();
                CheckSkuLotParam checkSkuLotParam = new CheckSkuLotParam();
                checkSkuLotParam.setSkuCode(skuDTO.getCode());
                checkSkuLotParam.setSkuQuality(excelImportTransferModifyBO.getTargetSkuQuality());
                checkSkuLotParam.setExpireDate(excelImportTransferModifyBO.getTargetExpireDate());
                checkSkuLotParam.setManufDate(excelImportTransferModifyBO.getTargetProductDate());
                checkSkuLotParam.setProductionNo(excelImportTransferModifyBO.getTargetProductNo());
                checkSkuLotParam.setReceiveDate(excelImportTransferModifyBO.getTargetReceiveDate());
                checkSkuLotParam.setExternalLinkBillNo(excelImportTransferModifyBO.getTargetExternalLinkBillNo());

                checkSkuLotParam.setInventoryType(excelImportTransferModifyBO.getTargetInventoryType());
                checkSkuLotParam.setPalletCode(excelImportTransferModifyBO.getTargetPalletCode());
                checkSkuLotParam.setBoxCode(excelImportTransferModifyBO.getTargetBoxCode());
                checkSkuLotParam.setValidityCode(excelImportTransferModifyBO.getTargetValidityCode());
                try {
                    skuLotBiz.verificationSkuLotParam(checkSkuLotParam, skuDTO);
                } catch (Exception e) {
                    errorCount++;
                    callBackPublicService(false, uid, lineNo, excelImportTransferModifyBO, e.getMessage());
                }
            } else {
                // 商品不存在
                errorCount++;
                callBackPublicService(false, uid, lineNo, excelImportTransferModifyBO, "商品不存在");
            }
            // 来源库位
            Optional<LocationDTO> locationDTOOptional = locationDTOList.stream().filter(locationDTO -> locationDTO.getCode().equals(excelImportTransferModifyBO.getOriginLocationCode()))
                    .findFirst();
            if (locationDTOOptional.isPresent()) {
                LocationDTO locationDTO = locationDTOOptional.get();
                if (!locationDTO.getType().equals(LocationTypeEnum.LOCATION_TYPE_STORE.getType()) && !locationDTO.getType().equals(LocationTypeEnum.LOCATION_TYPE_PICK.getType())) {
                    errorCount++;
                    callBackPublicService(false, uid, lineNo, excelImportTransferModifyBO, "来源库位必须是拣选位或者存储位");
                }
            } else {
                errorCount++;
                callBackPublicService(false, uid, lineNo, excelImportTransferModifyBO, "来源库位不存在");
            }

            // 目标库位
            Optional<LocationDTO> targetLocationOptional = locationDTOList.stream().filter(locationDTO -> locationDTO.getCode().equals(excelImportTransferModifyBO.getTargetLocationCode()))
                    .findFirst();
            if (targetLocationOptional.isPresent()) {
                LocationDTO locationDTO = targetLocationOptional.get();
                if (!locationDTO.getType().equals(LocationTypeEnum.LOCATION_TYPE_STORE.getType()) && !locationDTO.getType().equals(LocationTypeEnum.LOCATION_TYPE_PICK.getType())) {
                    errorCount++;
                    callBackPublicService(false, uid, lineNo, excelImportTransferModifyBO, "目标库位必须是拣选位或者存储位");
                }
            } else {
                errorCount++;
                callBackPublicService(false, uid, lineNo, excelImportTransferModifyBO, "目标库位库位不存在");
            }

            // 批次信息校验
            if (skuLotDTOList.stream().noneMatch(skuLotDTO -> skuLotDTO.getCode().equals(excelImportTransferModifyBO.getOriginSkuLotNo()) && skuLotDTO.getSkuCode().equals(excelImportTransferModifyBO.getOriginSkuCode()))) {
                errorCount++;
                callBackPublicService(false, uid, lineNo, excelImportTransferModifyBO, "来源批次与商品不匹配");
            }
        }
        return errorCount > 0;
    }

    private boolean checkCargoExistFail(CargoOwnerDTO cargoOwnerDTO) {
        if (ObjectUtil.isEmpty(cargoOwnerDTO)) {
            callBackPublicService(false, uid, 0, null, getExcelErrorInfo("业务校验", "货主不存在"));
            return true;
        }
        if (CargoOwnerStatusEnum.DISABLE.getValue().equals(cargoOwnerDTO.getStatus())) {
            callBackPublicService(false, uid, 0, null, getExcelErrorInfo("业务校验", "货主禁用"));
            return true;
        }
        return false;
    }
}