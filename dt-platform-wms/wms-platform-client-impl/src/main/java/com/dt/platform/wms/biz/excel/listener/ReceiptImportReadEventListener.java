package com.dt.platform.wms.biz.excel.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.FromSourceEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.asn.*;
import com.dt.component.common.enums.base.*;
import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.enums.cargo.CargoConfigStatusEnum;
import com.dt.component.common.enums.excel.ExcelImportEnum;
import com.dt.component.common.enums.pkg.PackageUnitEnum;
import com.dt.component.common.enums.rec.ReceiptStatusEnum;
import com.dt.component.common.enums.rec.ReceiptTypeEnum;
import com.dt.component.common.enums.sku.*;
import com.dt.component.common.enums.tally.TallyExtraGoodsEnum;
import com.dt.component.common.enums.tally.TallyRealReceiveEnum;
import com.dt.component.common.enums.tally.TallyStatusEnum;
import com.dt.component.common.enums.tally.TallyTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.contLog.ContainerLogDTO;
import com.dt.domain.base.dto.log.TallyLogDTO;
import com.dt.domain.base.param.LocationParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.TunnelParam;
import com.dt.domain.base.param.ZoneParam;
import com.dt.domain.bill.bo.ReceiptImportCommitBillBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.tally.TallyDTO;
import com.dt.domain.bill.dto.tally.TallyDetailDTO;
import com.dt.domain.bill.param.AsnParam;
import com.dt.domain.bill.param.ReceiptDetailParam;
import com.dt.domain.bill.param.ReceiptParam;
import com.dt.domain.bill.param.tally.TallyDetailParam;
import com.dt.domain.bill.param.tally.TallyParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.ITallyBiz;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.excel.DefaultReadEventListener;
import com.dt.platform.wms.biz.excel.bo.ReceiptImportBO;
import com.dt.platform.wms.biz.param.SkuLotCheckAndFormatParam;
import com.dt.platform.wms.dto.tally.TallyReceiveDetailBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.contLog.IRemoteContainerLogClient;
import com.dt.platform.wms.integration.log.IRemoteTallyLogClient;
import com.dt.platform.wms.integration.tally.IRemoteTallyClient;
import com.dt.platform.wms.integration.tally.IRemoteTallyDetailClient;
import com.dt.platform.wms.param.rec.CheckSkuLotParam;
import com.dt.platform.wms.transaction.IReceiptImportCommitGtsService;
import com.dt.platform.wms.transaction.bo.ReceiptImportCommitBO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/2 11:15
 */
@Data
@Slf4j
@Component("receiptImportReadEventListener")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ReceiptImportReadEventListener extends DefaultReadEventListener<ReceiptImportBO>    {

    @Resource
    IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    ISkuLotBiz skuLotBiz;

    @Resource
    IRemoteSkuClient remoteSkuClient;

    @Resource
    IRemoteContainerClient remoteContainerClient;

    @Resource
    IRemoteContainerLogClient remoteContainerLogClient;

    @Resource
    IRemoteTallyLogClient remoteTallyLogClient;

    @Resource
    IRemoteLocationClient remoteLocationClient;

    @Resource
    IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    IRemoteCargoConfigClient remoteCargoConfigClient;

    @Resource
    IRemoteLotRuleClient remoteLotRuleClient;

    @Resource
    IRemoteZoneClient remoteZoneClient;

    @Resource
    IRemoteTunnelClient remoteTunnelClient;

    @Resource
    IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    IRemoteAsnClient remoteAsnClient;

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    IRemoteReceiptClient remoteReceiptClient;

    @Resource
    IRemoteAsnDetailClient remoteAsnDetailClient;

    @Resource
    IReceiptImportCommitGtsService receiptImportCommitGtsService;

    @Resource
    private IRemoteSpecialLocationClient remoteSpecialLocationClient;

    @Resource
    private IRemoteTallyClient remoteTallyClient;

    @Resource
    private IRemoteTallyDetailClient remoteTallyDetailClient;

    protected Boolean is_check = true;

    @Resource
    ITallyBiz tallyBiz;

    @Resource
    IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    WmsOtherConfig wmsOtherConfig;


    private static Pattern pattern_box_pallet = Pattern.compile("\\d{18}");

    @Override
    public void checkExcel(ReceiptImportBO importExcel, AnalysisContext context) {
        //校验必填数据
        checkRowNotEmpty(importExcel);
        //校验数据和组装必要数据
        checkAndBuildData(importExcel);
        //数据格式化
        if (CollectionUtils.isEmpty(errorInfoList)) {
            resetData(importExcel);
        }
    }

    /**
     * @param importExcel
     * @return void
     * @author: WuXian
     * description:  校验数据
     * create time: 2021/12/2 11:41
     */
    private void checkAndBuildData(ReceiptImportBO importExcel) {
        //校验ASN
        importExcel.setTaotiaoXT(false);
        if (!StringUtils.isEmpty(importExcel.getBillNo()) && importExcel.getBillNo().toUpperCase().startsWith(SeqEnum.ASN_CODE_000001.getPrefix())) {
            checkAsnData(importExcel);
        }
        //检验理货报告
        if (!StringUtils.isEmpty(importExcel.getBillNo()) && importExcel.getBillNo().toUpperCase().startsWith(SeqEnum.TALLY_CODE_000001.getPrefix())) {
            checkTallyData(importExcel);
        }
        //校验ASN商品
        if (!StringUtils.isEmpty(importExcel.getSkuCode()) && !StringUtils.isEmpty(importExcel.getCargoCode())) {
            checkSkuData(importExcel);
        }
        //校验容器
        if (!StringUtils.isEmpty(importExcel.getContCode())) {
            checkContainerData(importExcel);
        }
        //库位不为空校验
        if (!StringUtils.isEmpty(importExcel.getTargetLocationCode())) {
            checkLocationData(importExcel);
        }
//        //检验托盘
//        if(!StringUtils.isEmpty(importExcel.getPalletCode()) && !pattern_box_pallet.matcher(importExcel.getPalletCode()).matches()){
//            errorInfoList.add(getExcelErrorInfo("托盘号", "托盘号不符合规范,应为18位数字"));
//            return;
//        }
//        //检验托盘
//        if(!StringUtils.isEmpty(importExcel.getBoxCode()) && !pattern_box_pallet.matcher(importExcel.getBoxCode()).matches()){
//            errorInfoList.add(getExcelErrorInfo("箱码", "箱码不符合规范,应为18位数字"));
//            return;
//        }
        //正次品
        List<String> skuQualityNameList = Arrays.asList(SkuQualityEnum.SKU_QUALITY_AVL.getMessage(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getMessage());
        if (!skuQualityNameList.contains(importExcel.getSkuQualityName())) {
            errorInfoList.add(getExcelErrorInfo("商品质量", "正次品填写错误"));
            return;
        }
        List<String> inventoryTypeNameList = Arrays.stream(InventoryTypeEnum.values()).map(InventoryTypeEnum::getMessage).collect(Collectors.toList());
        if (!inventoryTypeNameList.contains(importExcel.getInventoryTypeName())) {
            errorInfoList.add(getExcelErrorInfo("残次等级", "残次等级不能为空"));
            return;
        }
        //FP和XTZP这两个库存类型要屏蔽
        List<String> inventoryTypeNameListOther = Arrays.asList(InventoryTypeEnum.FP.getMessage(),InventoryTypeEnum.XTZP.getMessage());
        if (inventoryTypeNameListOther.contains(importExcel.getInventoryTypeName())) {
            errorInfoList.add(getExcelErrorInfo("残次等级", "报废品和销退正品不能选择"));
            return;
        }
    }

    /**
     * @param importExcel
     * @return void
     * @author: WuXian
     * description: 理货报告检验
     * create time: 2022/3/3 14:14
     */
    private void checkTallyData(ReceiptImportBO importExcel) {
        //查询是否有审核通过的理货报告
        TallyParam tallyParam = new TallyParam();
        tallyParam.setTallyCode(importExcel.getBillNo());
        tallyParam.setType(TallyTypeEnum.PURCHASE.getCode());
        List<TallyDTO> tallyDTOList = remoteTallyClient.getList(tallyParam);
        if (CollectionUtils.isEmpty(tallyDTOList)) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "理货报告单据不存在"));
            return;
        }
        if (tallyDTOList.stream().noneMatch(a -> a.getStatus().equalsIgnoreCase(TallyStatusEnum.SUCCESS_AUTH.getCode()))) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "未找到审核通过的理货报告"));
            return;
        }
        TallyDTO tallyDTO = tallyDTOList.stream().filter(a -> a.getStatus().equalsIgnoreCase(TallyStatusEnum.SUCCESS_AUTH.getCode())).findFirst().orElse(null);
        if (tallyDTO == null) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "理货报告单据不存在"));
            return;
        }
        TallyDetailParam tallyDetailParam = new TallyDetailParam();
        tallyDetailParam.setTallyCode(tallyDTO.getTallyCode());
        List<TallyDetailDTO> tallyDetailDTOList = remoteTallyDetailClient.getList(tallyDetailParam);
        if (CollectionUtils.isEmpty(tallyDetailDTOList)) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "理货报告单据明细不存在"));
            return;
        }
        if (remoteWarehouseClient.getTaoTianWarehouse(tallyDTO.getWarehouseCode())) {
            if (tallyDetailDTOList.stream()
                    .filter(a -> a.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode()))
                    .filter(a -> Objects.equals(a.getRealReceive(), TallyRealReceiveEnum.YES.getCode()))
                    .filter(a -> Objects.equals(a.getMark(), 0))
                    .noneMatch(a -> a.getSkuCode().equalsIgnoreCase(importExcel.getSkuCode()))) {
                errorInfoList.add(getExcelErrorInfo("*单据号", String.format("理货报告单据%s非多货明细未找到商品%s", importExcel.getBillNo(), importExcel.getSkuCode())));
                return;
            }
        } else {
            if (tallyDetailDTOList.stream()
                    .filter(a -> a.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode()))
                    .noneMatch(a -> a.getSkuCode().equalsIgnoreCase(importExcel.getSkuCode()))) {
                errorInfoList.add(getExcelErrorInfo("*单据号", String.format("理货报告单据%s非多货明细未找到商品%s", importExcel.getBillNo(), importExcel.getSkuCode())));
                return;
            }
        }
        AsnDTO asnDTO = remoteAsnClient.queryOneByAsnId(tallyDTO.getBillNo());
        if (asnDTO == null) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "理货报告单据对应入库单不存在"));
            return;
        }
        if (asnDTO.getStatus().equalsIgnoreCase(AsnStatusEnum.COMPLETE.getCode())) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "理货报告单据对应入库单已收货完成"));
            return;
        }
        //TODO 目前淘天销退不会有理货报告
        if (asnDTO != null && AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.TAOTIAN_XT)) {
            importExcel.setTaotiaoXT(true);
        }
        tallyDTO.setDetailList(tallyDetailDTOList);
        //设置参数后续使用
        importExcel.setTallyDTO(tallyDTO);
        //为了查询商品设置货主
        importExcel.setCargoCode(tallyDTO.getCargoCode());
    }

    /**
     * @param importExcel
     * @return void
     * @author: WuXian
     * description:  检验和获取库位有效性
     * create time: 2021/12/2 11:42
     */
    private void checkLocationData(ReceiptImportBO importExcel) {
        //检验和获取库位有效性
        LocationDTO locationDTO = checkAndGetLocationDTO(importExcel.getTargetLocationCode());
        if (locationDTO == null) {
            errorInfoList.add(getExcelErrorInfo("目标库位", "目标库位不存在"));
            return;
        }
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCode(locationDTO.getZoneCode());
        ZoneDTO zoneDTO = remoteZoneClient.get(zoneParam);
        if (zoneDTO == null) {
            errorInfoList.add(getExcelErrorInfo("目标库位", "目标库位库区不存在"));
            return;
        }
        importExcel.setLocationQuality(zoneDTO.getSkuQuality());
    }

    /**
     * @param importExcel
     * @return void
     * @author: WuXian
     * description:  校验容器
     * create time: 2021/12/2 11:42
     */
    private void checkContainerData(ReceiptImportBO importExcel) {
        //容器编码
        ContainerDTO containerDTO = remoteContainerClient.queryByCode(importExcel.getContCode());
        if (containerDTO == null) {
            errorInfoList.add(getExcelErrorInfo(String.format("容器编码：%s", importExcel.getContCode() == null ? "" : importExcel.getContCode()), "不存在"));
            return;
        }
        if (containerDTO != null && !containerDTO.getStatus().equals(ContainerStatusEnum.ENABLE.getValue())) {
            errorInfoList.add(getExcelErrorInfo(String.format("容器编码：%s", importExcel.getContCode() == null ? "" : importExcel.getContCode()), "不能使用"));
            return;
        }
        //容器号不能重复
        if (!CollectionUtils.isEmpty(dataList)
                && !StringUtils.isEmpty(importExcel.getContCode())
                && dataList.stream().anyMatch(a -> Objects.equals(a.getContCode(), importExcel.getContCode()))) {
            errorInfoList.add(getExcelErrorInfo(String.format("容器编码：%s", importExcel.getContCode()), "重复"));
            return;
        }
    }

    /**
     * @param importExcel
     * @return void
     * @author: WuXian
     * description: 校验商品
     * create time: 2021/12/2 11:42
     */
    private void checkSkuData(ReceiptImportBO importExcel) {
        //货主编码
        SkuParam skuParam = new SkuParam();
        skuParam.setCode(importExcel.getSkuCode());
        skuParam.setCargoCode(importExcel.getCargoCode());
        SkuDTO skuDTO = remoteSkuClient.get(skuParam);
        if (skuDTO == null) {
            errorInfoList.add(getExcelErrorInfo(String.format("商品编码：%s", importExcel.getSkuCode() == null ? "" : importExcel.getSkuCode()), "不存在"));
            return;
        }
        if (skuDTO != null && skuDTO.getSNMgmtIn()) {
            errorInfoList.add(getExcelErrorInfo(String.format("商品编码：%s", importExcel.getSkuCode() == null ? "" : importExcel.getSkuCode()), "开启SN模式,不允许导入收货"));
            return;
        }
        //淘天销退不校验新品维护
        if (importExcel.getTaotiaoXT() == null || !importExcel.getTaotiaoXT()) {
            if (SkuNewOrOldCtrlEnum.SKU_NEW_CTRL_YES.getCode().equals(skuDTO.getIsNewRecord())) {
                errorInfoList.add(getExcelErrorInfo(skuDTO.getCode(), "新品未维护，不允许收货"));
                return;
            }
        }
        importExcel.setWarehouseCode(skuDTO.getWarehouseCode());
        importExcel.setSkuName(skuDTO.getName());
        List<SkuUpcDTO> skuUpcDTOList = remoteSkuClient.querySkuUpcBySkuCode(importExcel.getCargoCode(), importExcel.getSkuCode());
        if (CollectionUtils.isEmpty(skuUpcDTOList)) {
            errorInfoList.add(getExcelErrorInfo(String.format("商品编码UPC:%s", importExcel.getSkuCode() == null ? "" : importExcel.getSkuCode()), "不存在"));
            return;
        }
        SkuUpcDTO skuUpcDTO = skuUpcDTOList.stream().filter(a -> Objects.equals(a.getIsDefault(), SkuUpcDefaultEnum.YES.getStatus())).findFirst().orElse(null);
        if (skuUpcDTO == null) {
            skuUpcDTO = new SkuUpcDTO();
        }
        importExcel.setUpcCode(skuUpcDTO.getUpcCode() == null ? "" : skuUpcDTO.getUpcCode());
        importExcel.setSkuDTO(skuDTO);
    }

    /**
     * @param importExcel
     * @return void
     * @author: WuXian
     * description:  校验asn
     * create time: 2021/12/2 11:41
     */
    private void checkAsnData(ReceiptImportBO importExcel) {
        AsnDTO asnDTO = remoteAsnClient.queryOneByAsnId(importExcel.getBillNo());
        List<AsnDetailDTO> detailDTOList = remoteAsnDetailClient.queryListByAsnId(importExcel.getBillNo());
        if (CollectionUtils.isEmpty(detailDTOList)) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单明细不存在"));
            return;
        } else {
            //淘天 无理货报告且验货不通过的品，在收货时只能收次品
            if (remoteWarehouseClient.getTaoTianWarehouse(asnDTO.getWarehouseCode())
                    && !CollectionUtils.isEmpty(asnDTO.getAsnDetailDTOS())
                    && !StringUtils.isEmpty(importExcel.getSkuCode())
                    && !StringUtils.isEmpty(importExcel.getSkuQuality())) {
                asnDTO.getAsnDetailDTOS().stream()
                        .filter(a -> Objects.equals(a.getSkuCode(), importExcel.getSkuCode()))
                        .findFirst().ifPresent(it -> {
                            if (AsnDetailMarkEnum.NumToEnum(it.getMark()).contains(AsnDetailMarkEnum.INSPECTION_FAIL)
                                    && !Objects.equals(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel(), importExcel.getSkuQuality())) {
                                throw new BaseException(BaseBizEnum.TIP, "淘天无理货报告且验货不通过的品,在收货时只能收次品");
                            }
                        });
            }
        }
        asnDTO.setAsnDetailDTOS(detailDTOList);
        if (asnDTO == null) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单不存在"));
            return;
        }
        if (asnDTO.getType().equalsIgnoreCase(AsnTypeEnum.RETURN.getCode())) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "退货入库不允许导入收货"));
            return;
        }
        if (asnDTO.getStatus().equals(AsnStatusEnum.CANCEL.getCode())) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单已取消"));
            return;
        }
        if (asnDTO.getStatus().equals(AsnStatusEnum.CREATE.getCode())) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单未确认到货"));
            return;
        }
        if (asnDTO.getStatus().equals(AsnStatusEnum.COMPLETE.getCode())) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单已完成收货"));
            return;
        }
        if (asnDTO.getType().equals(AsnTypeEnum.RETURN.getCode())) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "退货入库不允许导入收货"));
            return;
        }
        if (asnDTO.getAsnDetailDTOS().stream().noneMatch(a -> Objects.equals(a.getSkuCode(), importExcel.getSkuCode()))) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单明细商品不存在"));
            return;
        }
        //查询是否有审核通过的理货报告
        TallyParam tallyParam = new TallyParam();
        tallyParam.setBillNo(importExcel.getBillNo());
        tallyParam.setType(TallyTypeEnum.PURCHASE.getCode());
        List<TallyDTO> tallyDTOList = remoteTallyClient.getList(tallyParam);
        if (!CollectionUtils.isEmpty(tallyDTOList) && tallyDTOList.stream().anyMatch(a -> a.getStatus().equalsIgnoreCase(TallyStatusEnum.SUCCESS_AUTH.getCode()))) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单有审核通过的理货报告,请使用理货报告收货"));
            return;
        }
        if (!CollectionUtils.isEmpty(tallyDTOList) && tallyDTOList.stream().anyMatch(a -> !a.getStatus().equalsIgnoreCase(TallyStatusEnum.CANCEL.getCode()))) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单存在有效的理货报告,请取消理货报告"));
            return;
        }

        //4PL必须扫描理货报告
        if (!StringUtils.isEmpty(asnDTO.getOrderTag())) {
            Set<AsnOrderTagEnum> orderTagEnumList = AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag());
            if (orderTagEnumList.stream().anyMatch(a -> a.getCode().equals(AsnOrderTagEnum.FOUR_TAG.code()))) {
                //4pl必须理货
                if (Arrays.asList(AsnTypeEnum.TRANSFER.getCode(),
                        AsnTypeEnum.PURCHASE.getCode(),
                        AsnTypeEnum.OTHERS.getCode(), AsnTypeEnum.PURCHASE_SUPERVISE.getCode(), AsnTypeEnum.PURCHASE_REDEEM.getCode()).contains(asnDTO.getType())) {
                    errorInfoList.add(getExcelErrorInfo("*单据号", "必须创建理货报告审核通过后才允许收货"));
                    return;
                }
            }
        }
        List<String> typeCodeList = new ArrayList<>();
        typeCodeList.add(AsnTypeEnum.PURCHASE.getCode());
        typeCodeList.add(AsnTypeEnum.OTHERS.getCode());
        typeCodeList.add(AsnTypeEnum.TRANSFER.getCode());
        typeCodeList.add(AsnTypeEnum.PURCHASE_SUPERVISE.getCode());
        typeCodeList.add(AsnTypeEnum.PURCHASE_REDEEM.getCode());
        if (typeCodeList.contains(asnDTO.getType())
                && defaultWarehouseCodeConfig.getTrajectoryWarehouseCodeList().contains(asnDTO.getWarehouseCode())
                && asnDTO.getFromSource().equalsIgnoreCase(FromSourceEnum.ERP.value())) {
            //校验回传理货开始节点
            AsnParam asnParam = new AsnParam();
            asnParam.setAsnId(asnDTO.getAsnId());
            asnParam.setMsg("开始理货");
            List<AsnLogDTO> asnLogDTOList = remoteAsnClient.getAsnLogList(asnParam);
            if (CollectionUtils.isEmpty(asnLogDTOList)) {
                errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单理货状态未回传，请回传开始理货再收货。"));
                return;
            }
            //校验回传理货完成节点
            asnParam.setMsg("理货完成");
            asnLogDTOList = remoteAsnClient.getAsnLogList(asnParam);
            if (CollectionUtils.isEmpty(asnLogDTOList)) {
                errorInfoList.add(getExcelErrorInfo("*单据号", "到货通知单理货状态未回传，请回传理货完成再收货。"));
                return;
            }
        }
        //获取当前入库单对应sku的明细
        List<AsnDetailDTO> asnDetailDTOList = asnDTO.getAsnDetailDTOS().stream().filter(a -> a.getSkuCode().equals(importExcel.getSkuCode())).collect(Collectors.toList());
        asnDTO.setAsnDetailDTOS(asnDetailDTOList);
        if (asnDTO != null && AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.TAOTIAN_XT)) {
            importExcel.setTaotiaoXT(true);
        }
        if (remoteWarehouseClient.getTaoTianWarehouse(asnDTO.getWarehouseCode())
                && !CollectionUtils.isEmpty(asnDetailDTOList)
                && Objects.equals(asnDTO.getOutType(), AsnOutTypeEnum.BCRK.getCode())) {
            if (AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.WAIT_INSPECTION)) {
                throw new BaseException(BaseBizEnum.TIP, "当前商品未验货/验货失败,不允许收货,请联系淘天运营验货");
            }
            if (asnDetailDTOList.stream().noneMatch(a -> AsnDetailMarkEnum.NumToEnum(a.getMark()).contains(AsnDetailMarkEnum.INSPECTION_AUTH))) {
                errorInfoList.add(getExcelErrorInfo("*SKU编码", "当前商品未验货/验货失败,不允许收货,请联系淘天运营验货"));
                return;
            }
        }
        //设置参数后续使用
        importExcel.setAsnDTO(asnDTO);
        //为了查询商品设置货主
        importExcel.setCargoCode(asnDTO.getCargoCode());

        //开启先收后审，可以不需要理货报告
        if (AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.RECEIVE_BEFORE_REVIEW)) {
            return;
        } else {
            //淘天的不关注
            if (!remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())) {
                CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey("", asnDTO.getCargoCode(), CargoConfigParamEnum.TALLY_REPORT.getCode());
                if (cargoConfigDTO != null && cargoConfigDTO.getPropValue().equals("1")
                        && cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue())) {
                    errorInfoList.add(getExcelErrorInfo("*单据号", "货主开启理货报告"));
                    return;
                }
            }
            if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())
                    && Objects.equals(asnDTO.getOutType(), AsnOutTypeEnum.CGRK.getCode())) {
                errorInfoList.add(getExcelErrorInfo("*单据号", "淘天入库单,外部业务类型【采购入库】,需要理货报告"));
                return;
            }
        }
    }

    /**
     * @param importExcel
     * @return void
     * @author: WuXian
     * description:  数据格式化
     * create time: 2021/12/2 11:41
     */
    private void resetData(ReceiptImportBO importExcel) {
        if (StringUtils.isEmpty(importExcel.getReceiveTime())) {
            importExcel.setReceiveDate(0L);
        } else {
            if (!isValidDate(importExcel.getReceiveTime())) {
                errorInfoList.add(getExcelErrorInfo("入库日期", "入库日期格式不对"));
                return;
            } else {
                importExcel.setReceiveDate(DateUtil.parse(importExcel.getReceiveTime(), "yyyy-MM-dd").getTime());
            }
        }
        if (StringUtils.isEmpty(importExcel.getManufTime())) {
            importExcel.setManufDate(0L);
        } else {
            if (!isValidDate(importExcel.getManufTime())) {
                errorInfoList.add(getExcelErrorInfo("生产日期", "生产日期格式不对"));
                return;
            } else {
                importExcel.setManufDate(DateUtil.parse(importExcel.getManufTime(), "yyyy-MM-dd").getTime());
            }
        }

        if (StringUtils.isEmpty(importExcel.getExpireTime())) {
            importExcel.setExpireDate(0L);
        } else {
            if (!isValidDate(importExcel.getExpireTime())) {
                errorInfoList.add(getExcelErrorInfo("失效日期", "失效日期格式不对"));
                return;
            } else {
                importExcel.setExpireDate(DateUtil.parse(importExcel.getExpireTime(), "yyyy-MM-dd").getTime());
            }
        }
        if (StringUtils.isEmpty(importExcel.getProductionNo())) {
            importExcel.setProductionNo(null);
        }
        if (StringUtils.isEmpty(importExcel.getExternalLinkBillNo())) {
            importExcel.setExternalLinkBillNo(null);
        }
        if (StringUtils.isEmpty(importExcel.getPalletCode())) {
            importExcel.setPalletCode(null);
        }
        if (StringUtils.isEmpty(importExcel.getValidityCode())) {
            importExcel.setValidityCode("");
        }

        if (StringUtils.isEmpty(importExcel.getBoxCode())) {
            importExcel.setBoxCode(null);
        }
        //校验效期码
        if(!StringUtils.isEmpty(importExcel.getValidityCode())&& !wmsOtherConfig.isValidValidityCode(importExcel.getValidityCode())){
            throw new BaseException(BaseBizEnum.TIP, wmsOtherConfig.getValidValidityRuleMsg());
        }

        String skuQuality = importExcel.getSkuQualityName().equals(SkuQualityEnum.SKU_QUALITY_AVL.getMessage()) ? SkuQualityEnum.SKU_QUALITY_AVL.getLevel() : SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel();
        importExcel.setSkuQuality(skuQuality);

        String inventoryType = InventoryTypeEnum.getEnumByMessage(importExcel.getInventoryTypeName()).getCode();
        importExcel.setInventoryType(inventoryType);

        List<String> zpList = Arrays.asList(InventoryTypeEnum.ZP.getCode(), InventoryTypeEnum.ZCZP.getCode());
        if (Objects.equals(importExcel.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel())
                && !zpList.contains(importExcel.getInventoryType())) {
            errorInfoList.add(getExcelErrorInfo("属性值", "商品属性为【正品】,残次等级只能选择【正品】或【收货暂存良-待退废】"));
            return;
        }
        if (Objects.equals(importExcel.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())
                && zpList.contains(importExcel.getInventoryType())) {
            errorInfoList.add(getExcelErrorInfo("属性值", "商品属性为【次品】,残次等级不能选择【正品】或【收货暂存良-待退废】"));
            return;
        }
        //参数校验
        try {
            skuLotBiz.verificationSkuLotParam(ConverterUtil.convert(importExcel, CheckSkuLotParam.class), importExcel.getSkuDTO());
        } catch (Exception e) {
            errorInfoList.add(getExcelErrorInfo("属性值", e.getMessage() == null ? "" : e.getMessage()));
            return;
        }
        //检验生产日期和失效日期 isWithdraw true 校验禁收  false不校验
        try {
            Boolean isWithdraw = true;
            if (!StringUtils.isEmpty(funcCode) && ExcelImportEnum.EXCEL_RECEIPT_IMPORT.getFuncCode().equalsIgnoreCase(funcCode)) {
                isWithdraw = false;
            }
            SkuLotDTO skuLotDTO = skuLotBiz.findAndFormatSkuLot(buildSkuLotParam(importExcel), importExcel.getSkuDTO(), isWithdraw);
            //校验批次是否存在通知单明细中
            if (importExcel.getAsnDTO() != null && importExcel.getAsnDTO().getType().equalsIgnoreCase(AsnTypeEnum.DISTRIBUTE_RETURN.getCode())) {
                if (StringUtils.isEmpty(skuLotDTO.getId())) {
                    errorInfoList.add(getExcelErrorInfo("属性值", "当前属性生成的批次当前库不存在"));
                    return;
                }
                List<AsnDetailDTO> detailDTOList = importExcel.getAsnDTO().getAsnDetailDTOS();
                if (detailDTOList.stream().noneMatch(a -> a.getSkuLotNo().equalsIgnoreCase(skuLotDTO.getCode()))) {
                    errorInfoList.add(getExcelErrorInfo("属性值", "当前属性生成的批次当前库不存在"));
                    return;
                }
            }
            importExcel.setSkuLotDTO(skuLotDTO);
        } catch (Exception e) {
            errorInfoList.add(getExcelErrorInfo("效期", e.getMessage() == null ? "" : e.getMessage()));
            return;
        }
        //校验库位正次品和导入数据正次品
        if (!StringUtils.isEmpty(importExcel.getTargetLocationCode()) && !Objects.equals(importExcel.getSkuQuality(), importExcel.getLocationQuality())) {
            errorInfoList.add(getExcelErrorInfo("库位属性", "库位的正次品与输入的正次品不一致"));
            return;
        }
        if (importExcel.getBillNo().toUpperCase().startsWith(SeqEnum.TALLY_CODE_000001.getPrefix()) && importExcel.getTallyDTO() != null) {
            List<TallyDetailDTO> detailDTOList = importExcel.getTallyDTO().getDetailList();
            if (CollectionUtils.isEmpty(detailDTOList)) {
                errorInfoList.add(getExcelErrorInfo("属性值", "理货报告明细商品未找到"));
                return;
            }
            detailDTOList = detailDTOList.stream().filter(a -> a.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode())).collect(Collectors.toList());
            TallyDetailDTO tallyDetailDTO = detailDTOList.stream()
                    .filter(a -> a.getExpireDate().equals(importExcel.getExpireDate()))
                    .filter(a -> a.getManufDate().equals(importExcel.getManufDate()))
                    .filter(a -> a.getValidityCode().equals(importExcel.getValidityCode()))
                    .filter(a -> a.getSkuQuality().equals(importExcel.getSkuQuality()))
                    .filter(a -> a.getSkuCode().equals(importExcel.getSkuCode())).findFirst().orElse(null);
            if (tallyDetailDTO == null) {
                errorInfoList.add(getExcelErrorInfo("效期", String.format("理货报告明细商品未找到生产日期%s,失效日期%s,效期暗码:%s", importExcel.getManufTime(), importExcel.getExpireTime(),importExcel.getValidityCode())));
                return;
            }
        }
    }

    /**
     * @param str
     * @return void
     * @author: WuXian
     * description:  时间格式化
     * create time: 2021/12/2 11:42
     */
    public static boolean isValidDate(String str) {
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = (Date) formatter.parse(str);
            return str.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * @param importExcel
     * @return void
     * @author: WuXian
     * description:  校验必填数据
     * create time: 2021/12/2 11:40
     */
    private void checkRowNotEmpty(ReceiptImportBO importExcel) {
        if (StringUtils.isEmpty(importExcel.getBillNo())) {
            errorInfoList.add(getExcelErrorInfo("*单据号", "单据号不能为空"));
            return;
        } else {
            if (!importExcel.getBillNo().toUpperCase().startsWith(SeqEnum.TALLY_CODE_000001.getPrefix())
                    && !importExcel.getBillNo().toUpperCase().startsWith(SeqEnum.ASN_CODE_000001.getPrefix())) {
                errorInfoList.add(getExcelErrorInfo("*单据号", "单据号不存在"));
                return;
            }
        }
        if (StringUtils.isEmpty(importExcel.getSkuCode())) {
            errorInfoList.add(getExcelErrorInfo("商品编码", "商品编码不能为空"));
            return;
        }
        if (StringUtils.isEmpty(importExcel.getContCode())) {
            errorInfoList.add(getExcelErrorInfo("容器编码", "容器编码不能为空"));
            return;
        }
        if (StringUtils.isEmpty(importExcel.getQty())) {
            errorInfoList.add(getExcelErrorInfo("库存数量", "库存数量不能为空"));
            return;
        }
        if (!NumberUtil.isNumber(String.valueOf(importExcel.getQty()))) {
            errorInfoList.add(getExcelErrorInfo("库存数量", "库存数量非数字类型"));
            return;
        }
        if (importExcel.getQty().compareTo(BigDecimal.ZERO) <= 0) {
            errorInfoList.add(getExcelErrorInfo("库存数量", "库存数量不能小于0"));
            return;
        }
        if (StringUtils.isEmpty(importExcel.getSkuQualityName())) {
            errorInfoList.add(getExcelErrorInfo("商品质量", "商品质量不能为空"));
            return;
        }

    }

    /**
     * @param targetLocationCode
     * @return com.dt.domain.base.dto.LocationDTO
     * @author: WuXian
     * description:  检查目标库位有效性
     * create time: 2021/12/2 11:43
     */
    private LocationDTO checkAndGetLocationDTO(String targetLocationCode) {
        LocationParam locationParam = new LocationParam();
        locationParam.setStatus(LocationStatusEnum.STATUS_ENABLED.getStatus());
        locationParam.setCode(targetLocationCode);
        LocationDTO locationDTO = remoteLocationClient.get(locationParam);
        if (locationDTO == null) {
            return null;
        }
        TunnelParam tunnelParam = new TunnelParam();
        tunnelParam.setCode(locationDTO.getTunnelCode());
        TunnelDTO tunnelDTO = remoteTunnelClient.get(tunnelParam);
        if (tunnelDTO == null) {
            return null;
        }
        if (tunnelDTO != null && !Objects.equals(tunnelDTO.getStatus(), TunnelStatusEnum.STATUS_ENABLED.getStatus())) {
            return null;
        }
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCode(locationDTO.getZoneCode());
        ZoneDTO zoneDTO = remoteZoneClient.get(zoneParam);
        if (zoneDTO == null) {
            return null;
        }
        if (zoneDTO != null && !Objects.equals(zoneDTO.getStatus(), ZoneStatusEnum.STATUS_ENABLED.getStatus())) {
            return null;
        }
        return locationDTO;
    }

    @Override
    public void invoke(ReceiptImportBO importExcel, AnalysisContext context) {
        //跳过 0 、 1 数据
        if (context.readRowHolder().getRowIndex() <= 1) {
            return;
        }
        //校验数据
        checkExcel(importExcel, context);
        //汇总错误
        if (CollectionUtils.isEmpty(errorInfoList)) {
            AsnDTO asnDTO = importExcel.getAsnDTO();
            SkuDTO skuDTO = importExcel.getSkuDTO();
            SkuLotDTO skuLotDTO = importExcel.getSkuLotDTO();
            TallyDTO tallyDTO = importExcel.getTallyDTO();
            //移除相应数据,会导致相应数据数据体过大
            importExcel.setAsnDTO(null);
            importExcel.setSkuDTO(null);
            importExcel.setSkuLotDTO(null);
            importExcel.setTallyDTO(null);
            callBackPublicService(true, uid, context.readRowHolder().getRowIndex(), importExcel, "处理成功");
            //赋值回来.用于后续处理
            if (asnDTO != null) {
                importExcel.setAsnDTO(asnDTO);
            }
            if (skuDTO != null) {
                importExcel.setSkuDTO(skuDTO);
            }
            if (skuLotDTO != null) {
                importExcel.setSkuLotDTO(skuLotDTO);
            }
            if (tallyDTO != null) {
                importExcel.setTallyDTO(tallyDTO);
            }
            //存储数据
            importExcel.setRowId(context.readRowHolder().getRowIndex());
            dataList.add(importExcel);
        } else {
            is_check = false;
            log.error("callErrorBackPublicService importExcel:{} import:{} ", context.readRowHolder().getRowIndex(), JSONUtil.toJsonStr(errorInfoList));
            importExcel.setAsnDTO(null);
            importExcel.setSkuDTO(null);
            importExcel.setSkuLotDTO(null);
            importExcel.setTallyDTO(null);
            callBackPublicService(false, uid, context.readRowHolder().getRowIndex(), importExcel, String.join("\n", errorInfoList));
            errorInfoList.clear();
        }
    }

    @Override
//    @GlobalTransactional(timeoutMills = 300000, rollbackFor = Exception.class, name = "gts-receipt-import-context-commit")
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (!is_check) {
            log.error("ReceiptImportBO-error:{}", System.currentTimeMillis());
            return;
        }
        //获取所有容器
        List<String> contCodeList = dataList.stream().map(ReceiptImportBO::getContCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contCodeList)) {
            log.error("contCodeList error msg:{}", "数据存在错误，请修正");
            errorInfoList.add(getExcelErrorInfo("数据", "数据存在错误，请修正"));
            callBackPublicService(false, uid, 0, null, String.join("\n", errorInfoList));
            errorInfoList.clear();
            return;
        }
        if (contCodeList.size() != dataList.size()) {
            log.error("contCodeList error msg:{}", "容器数和导入数量不匹配");
            errorInfoList.add(getExcelErrorInfo("数据", "容器数和导入数量不匹配"));
            callBackPublicService(false, uid, 0, null, String.join("\n", errorInfoList));
            errorInfoList.clear();
            return;
        }
        if (dataList.size() > 500) {
            log.error("size error msg:{}", "excel单次导入数量不能大于500行");
            errorInfoList.add(getExcelErrorInfo("数据", "excel单次导入数量不能大于500行"));
            callBackPublicService(false, uid, 0, null, String.join("\n", errorInfoList));
            errorInfoList.clear();
            return;
        }
        //单次只能导入一种数据要么全是入库单,要么全是理货报告
        String errorCheckMsg = checkImportBillType(dataList);
        if (!StringUtils.isEmpty(errorCheckMsg)) {
            log.error("errorCheckMsg msg:{}", errorCheckMsg);
            errorInfoList.add(getExcelErrorInfo("数据", errorCheckMsg));
            callBackPublicService(false, uid, 0, null, String.join("\n", errorInfoList));
            errorInfoList.clear();
            return;
        }

        //commit data
        List<AsnDTO> asnDTOList = new ArrayList<>();
        List<AsnLogDTO> asnLogDTOList = new ArrayList<>();
        List<TallyLogDTO> tallyLogDTOList = new ArrayList<>();
        List<ContainerDTO> containerDTOList = new ArrayList<>();
        List<ContainerLogDTO> containerLogDTOList = new ArrayList<>();
        List<ReceiptDTO> receiptDTOList = new ArrayList<>();
        List<ReceiptDetailDTO> receiptDetailDTOList = new ArrayList<>();

        List<String> billNoList = dataList.stream().map(ReceiptImportBO::getBillNo).distinct().collect(Collectors.toList());
        //理货报告导入
        if (billNoList.stream().anyMatch(a -> a.toUpperCase().startsWith(SeqEnum.TALLY_CODE_000001.getPrefix()))) {
            //检验数量是否超
            String errorMsg = checkTallyAndSkuQty(dataList);
            if (!StringUtils.isEmpty(errorMsg)) {
                log.error("checkAsnAndSkuQty error msg:{}", errorMsg);
                errorInfoList.add(getExcelErrorInfo("数据", errorMsg));
                callBackPublicService(false, uid, 0, null, String.join("\n", errorInfoList));
                errorInfoList.clear();
                return;
            }
            //组装数据--一行记录一个容器
            for (ReceiptImportBO receiptImportBO : dataList) {
                TallyDTO tallyDTO = receiptImportBO.getTallyDTO();

                AsnDTO asnDTO;
                if (CollectionUtils.isEmpty(asnDTOList) || asnDTOList.stream().noneMatch(a -> a.getAsnId().equalsIgnoreCase(tallyDTO.getBillNo()))) {
                    asnDTO = remoteAsnClient.queryOneByAsnId(tallyDTO.getBillNo());
                    asnDTOList.add(asnDTO);
                } else {
                    asnDTO = asnDTOList.stream().filter(a -> a.getAsnId().equalsIgnoreCase(tallyDTO.getBillNo())).findFirst().orElse(null);
                }
                //组装数据
                ReceiptImportCommitBO receiptImportCommitBO = buildReceiptContCodeParam(receiptImportBO, receiptImportBO.getContCode(), asnDTO, tallyDTO);
                //记录ASN日志
                AsnLogDTO asnLogDTO = ConverterUtil.convert(tallyDTO, AsnLogDTO.class);
                asnLogDTO.setMsg(DateUtil.format(new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss") + "导入(理货报告)收货作业批次收货容器(" + receiptImportBO.getContCode() + ")");
                asnLogDTO.setOpBy(CurrentUserHolder.getUserName());
                receiptImportCommitBO.setAsnLogDTO(asnLogDTO);
                asnLogDTOList.add(asnLogDTO);
                //记录理货报告日志
                TallyLogDTO tallyLogDTO = ConverterUtil.convert(tallyDTO, TallyLogDTO.class);
                tallyLogDTO.setOpContent(DateUtil.format(new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss") + "导入(理货报告)收货作业批次收货容器(" + receiptImportBO.getContCode() + ")");
                tallyLogDTO.setOpRemark(DateUtil.format(new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss") + "导入(理货报告)收货作业批次收货容器(" + receiptImportBO.getContCode() + ")");
                tallyLogDTO.setOpBy(CurrentUserHolder.getUserName());
                receiptImportCommitBO.setTallyLogDTO(tallyLogDTO);
                tallyLogDTOList.add(tallyLogDTO);
                //增加容器日志记录
                ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                containerLogDTO.setWarehouseCode(receiptImportCommitBO.getContainerDTO().getWarehouseCode());
                containerLogDTO.setContCode(receiptImportCommitBO.getContainerDTO().getCode());
                containerLogDTO.setCreatedTime(System.currentTimeMillis());
                containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                containerLogDTO.setOpContent(String.format("一键收货【理货报告:】%s,【作业批次号:】%s绑定容器:%s", tallyDTO.getBillNo(), receiptImportCommitBO.getReceiptDTO().getRecId(), receiptImportCommitBO.getContainerDTO().getCode()));
                containerLogDTO.setOpDate(System.currentTimeMillis());
                containerLogDTO.setOccupyNo(receiptImportCommitBO.getContainerDTO().getOccupyNo());
                containerLogDTO.setOccupyType(receiptImportCommitBO.getContainerDTO().getOccupyType());
                receiptImportCommitBO.setContainerLogDTO(containerLogDTO);
                containerLogDTOList.add(containerLogDTO);
//                receiptImportCommitGtsService.commitImportReceipt(receiptImportCommitBO);

                //commit Data
                containerDTOList.add(receiptImportCommitBO.getContainerDTO());
                receiptDTOList.add(receiptImportCommitBO.getReceiptDTO());
                receiptDetailDTOList.addAll(receiptImportCommitBO.getReceiptDTO().getDetailDTOList());
            }
            //第一步提交绑定容器
            Boolean aBoolean = remoteContainerClient.modifyBatch(containerDTOList);
            //第二步提交单据数据
            ReceiptImportCommitBillBO receiptImportCommitBillBO = new ReceiptImportCommitBillBO();
            receiptImportCommitBillBO.setReceiptDTOList(receiptDTOList);
            receiptImportCommitBillBO.setReceiptDetailDTOList(receiptDetailDTOList);
            receiptImportCommitBillBO.setAsnLogDTOList(asnLogDTOList);
            aBoolean = remoteReceiptClient.modifyCommitContContext(receiptImportCommitBillBO);
            //记录日志
            if (!CollectionUtils.isEmpty(tallyLogDTOList)) {
                remoteTallyLogClient.saveBatch(tallyLogDTOList);
            }
            remoteContainerLogClient.saveBatch(containerLogDTOList);
        } else {
            //非理货报告导入
            //检验数量是否超
            String errorMsg = checkAsnAndSkuQty(dataList);
            if (!StringUtils.isEmpty(errorMsg)) {
                log.error("checkAsnAndSkuQty error msg:{}", errorMsg);
                errorInfoList.add(getExcelErrorInfo("数据", errorMsg));
                callBackPublicService(false, uid, 0, null, String.join("\n", errorInfoList));
                errorInfoList.clear();
                return;
            }
            //组装数据--一行记录一个容器
            for (ReceiptImportBO receiptImportBO : dataList) {
                AsnDTO asnDTO = receiptImportBO.getAsnDTO();
                //组装数据
                ReceiptImportCommitBO receiptImportCommitBO = buildReceiptContCodeParam(receiptImportBO, receiptImportBO.getContCode(), asnDTO, null);
                //记录ASN日志
                AsnLogDTO asnLogDTO = ConverterUtil.convert(asnDTO, AsnLogDTO.class);
                asnLogDTO.setMsg(DateUtil.format(new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss") + "导入收货作业批次收货容器(" + receiptImportBO.getContCode() + ")");
                asnLogDTO.setOpBy(CurrentUserHolder.getUserName());
                receiptImportCommitBO.setAsnLogDTO(asnLogDTO);
                asnLogDTOList.add(asnLogDTO);
                //增加容器日志记录
                ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                containerLogDTO.setWarehouseCode(receiptImportCommitBO.getContainerDTO().getWarehouseCode());
                containerLogDTO.setContCode(receiptImportCommitBO.getContainerDTO().getCode());
                containerLogDTO.setCreatedTime(System.currentTimeMillis());
                containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                containerLogDTO.setOpContent(String.format("一键收货【到货通知单号:】%s,【作业批次号:】%s绑定容器:%s", asnDTO.getAsnId(), receiptImportCommitBO.getReceiptDTO().getRecId(), receiptImportCommitBO.getContainerDTO().getCode()));
                containerLogDTO.setOpDate(System.currentTimeMillis());
                containerLogDTO.setOccupyNo(receiptImportCommitBO.getContainerDTO().getOccupyNo());
                containerLogDTO.setOccupyType(receiptImportCommitBO.getContainerDTO().getOccupyType());
                receiptImportCommitBO.setContainerLogDTO(containerLogDTO);
                containerLogDTOList.add(containerLogDTO);
//                receiptImportCommitGtsService.commitImportReceipt(receiptImportCommitBO);

                //commit Data
                containerDTOList.add(receiptImportCommitBO.getContainerDTO());
                receiptDTOList.add(receiptImportCommitBO.getReceiptDTO());
                receiptDetailDTOList.addAll(receiptImportCommitBO.getReceiptDTO().getDetailDTOList());
            }
            //第一步提交绑定容器
            Boolean aBoolean = remoteContainerClient.modifyBatch(containerDTOList);
            //第二步提交单据数据
            ReceiptImportCommitBillBO receiptImportCommitBillBO = new ReceiptImportCommitBillBO();
            receiptImportCommitBillBO.setReceiptDTOList(receiptDTOList);
            receiptImportCommitBillBO.setReceiptDetailDTOList(receiptDetailDTOList);
            receiptImportCommitBillBO.setAsnLogDTOList(asnLogDTOList);
            aBoolean = remoteReceiptClient.modifyCommitContContext(receiptImportCommitBillBO);
            //记录日志
            remoteContainerLogClient.saveBatch(containerLogDTOList);
        }
    }


    /**
     * @param dataList
     * @return java.lang.String
     * @author: WuXian
     * description: 理货报告校验是否超收
     * create time: 2022/3/3 14:09
     */
    private String checkTallyAndSkuQty(List<ReceiptImportBO> dataList) {
        List<TallyDTO> tallyDTOList = dataList.stream().filter(a -> !Objects.isNull(a.getTallyDTO())).map(ReceiptImportBO::getTallyDTO).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tallyDTOList)) {
            return "没有找到理货报告";
        }
        List<String> tallyCodeList = tallyDTOList.stream().map(TallyDTO::getTallyCode).distinct().collect(Collectors.toList());
        //按理货数据校验
        for (String tallyCode : tallyCodeList) {
            List<ReceiptImportBO> importBOList = dataList.stream().filter(a -> Objects.equals(a.getBillNo(), tallyCode)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(importBOList)) {
                return String.format("理货报告导入收货%s,未找到导入数据", tallyCode);
            }
            List<TallyReceiveDetailBizDTO> tallyToReceiveData = tallyBiz.getTallyToReceiveDataNoLineSeq(tallyCode, null, false);
            //理货报告只查看正常部分商品
            tallyToReceiveData = tallyToReceiveData.stream().filter(a -> a.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode())).collect(Collectors.toList());

            //当前导入商品明细的数量
            List<String> importSkuCodeList = importBOList.stream().map(ReceiptImportBO::getSkuCode).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(importSkuCodeList)) {
                return "找不到商品！！！";
            }
            List<String> expSkuCodeList = tallyToReceiveData.stream().map(TallyReceiveDetailBizDTO::getSkuCode).distinct().collect(Collectors.toList());
            importSkuCodeList.removeIf(a -> expSkuCodeList.contains(a));
            if (!CollectionUtils.isEmpty(importSkuCodeList)) {
                return String.format("理货报告导入收货%s,非多货商品%s未找到", tallyCode, importSkuCodeList.stream().collect(Collectors.joining(",")));
            }

            Map<String, List<ReceiptImportBO>> map = importBOList.stream().collect(Collectors.groupingBy(it ->
                    StrUtil.join("#", it.getCargoCode(), it.getSkuCode(), it.getSkuQuality(), it.getInventoryType(),
                            it.getManufDate(), it.getExpireDate())));

            for (Map.Entry<String, List<ReceiptImportBO>> entity : map.entrySet()) {
                List<ReceiptImportBO> detailDTOList = entity.getValue();
                ReceiptImportBO receiptImportBO = detailDTOList.get(0);
                //当前导入数量
                BigDecimal currentQty = detailDTOList.stream()
                        .filter(a -> Objects.equals(a.getSkuCode(), receiptImportBO.getSkuCode()))
                        .filter(a -> Objects.equals(a.getSkuQuality(), receiptImportBO.getSkuQuality()))
                        .filter(a -> a.getInventoryType().equalsIgnoreCase(receiptImportBO.getInventoryType()))
                        .filter(a -> Objects.equals(a.getExpireDate(), receiptImportBO.getExpireDate()))
                        .filter(a -> Objects.equals(a.getManufDate(), receiptImportBO.getManufDate()))
                        .map(ReceiptImportBO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                //理货报告当前待收数量
                BigDecimal waitQty = tallyToReceiveData.stream()
                        .filter(a -> Objects.equals(a.getSkuCode(), receiptImportBO.getSkuCode()))
                        .filter(a -> Objects.equals(a.getSkuQuality(), receiptImportBO.getSkuQuality()))
                        .filter(a -> a.getInventoryType().equalsIgnoreCase(receiptImportBO.getInventoryType()))
                        .filter(a -> Objects.equals(a.getExpireDate(), receiptImportBO.getExpireDate()))
                        .filter(a -> Objects.equals(a.getManufDate(), receiptImportBO.getManufDate()))
                        .map(TallyReceiveDetailBizDTO::getWaitQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (waitQty.subtract(currentQty).compareTo(BigDecimal.ZERO) < 0) {
                    String message = "理货报告导入收货[商品属性]"
                            + "[商品编码]" + receiptImportBO.getSkuCode()
                            + SkuQualityEnum.getEnum(receiptImportBO.getSkuQuality()).getMessage()
                            + "[残次等级]" + InventoryTypeEnum.getEnum(receiptImportBO.getInventoryType()).getMessage()
                            + "[生产日期]" + ConverterUtil.convertVoTime(receiptImportBO.getManufDate(), "yyyy-MM-dd")
                            + "超收";
                    return message;
                }
            }
//            for (TallyReceiveDetailBizDTO tallyDetailDTO : tallyToReceiveData) {
//                //当前导入数量
//                BigDecimal currentQty = importBOList.stream()
//                        .filter(a -> Objects.equals(a.getSkuCode(), tallyDetailDTO.getSkuCode()))
//                        .filter(a -> Objects.equals(a.getSkuQuality(), tallyDetailDTO.getSkuQuality()))
//                        .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
//                        .filter(a -> Objects.equals(a.getExpireDate(), tallyDetailDTO.getExpireDate()))
//                        .filter(a -> Objects.equals(a.getManufDate(), tallyDetailDTO.getManufDate()))
//                        .map(ReceiptImportBO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
//                //理货报告当前商品计划数量
//                BigDecimal expQty = tallyToReceiveData.stream()
//                        .filter(a -> Objects.equals(a.getSkuCode(), tallyDetailDTO.getSkuCode()))
//                        .filter(a -> Objects.equals(a.getSkuQuality(), tallyDetailDTO.getSkuQuality()))
//                        .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
//                        .filter(a -> Objects.equals(a.getExpireDate(), tallyDetailDTO.getExpireDate()))
//                        .filter(a -> Objects.equals(a.getManufDate(), tallyDetailDTO.getManufDate()))
//                        .map(TallyReceiveDetailBizDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
//                //理货报告实收数量
//                BigDecimal actualQty = tallyToReceiveData.stream()
//                        .filter(a -> Objects.equals(a.getSkuCode(), tallyDetailDTO.getSkuCode()))
//                        .filter(a -> Objects.equals(a.getSkuQuality(), tallyDetailDTO.getSkuQuality()))
//                        .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
//                        .filter(a -> Objects.equals(a.getExpireDate(), tallyDetailDTO.getExpireDate()))
//                        .filter(a -> Objects.equals(a.getManufDate(), tallyDetailDTO.getManufDate()))
//                        .map(TallyReceiveDetailBizDTO::getRecQty).reduce(BigDecimal.ZERO, BigDecimal::add);
//                //receipt收货中数量
//                BigDecimal receiveingQty = receiptDetailDTOList.stream()
//                        .filter(a -> Objects.equals(a.getSkuCode(), tallyDetailDTO.getSkuCode()))
//                        .filter(a -> Objects.equals(a.getSkuQuality(), tallyDetailDTO.getSkuQuality()))
//                        .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
//                        .filter(a -> Objects.equals(a.getExpireDate(), tallyDetailDTO.getExpireDate()))
//                        .filter(a -> Objects.equals(a.getManufDate(), tallyDetailDTO.getManufDate()))
//                        .map(ReceiptDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
//                if (expQty.subtract(actualQty).subtract(receiveingQty).subtract(currentQty).compareTo(BigDecimal.ZERO) < 0) {
//                    return String.format("理货报告导入收货%s,商品%s超收", tallyCode, tallyDetailDTO.getSkuCode());
//                }
//            }
        }
        return "";
    }

    /**
     * @param dataList
     * @return java.lang.String
     * @author: WuXian
     * description: 单次只能导入一种数据要么全是入库单,要么全是理货报告
     * create time: 2022/3/3 13:56
     */
    private String checkImportBillType(List<ReceiptImportBO> dataList) {
        List<String> billNoList = dataList.stream().map(ReceiptImportBO::getBillNo).distinct().collect(Collectors.toList());
        if (billNoList.stream().allMatch(a -> a.toUpperCase().startsWith(SeqEnum.ASN_CODE_000001.getPrefix()))
                || billNoList.stream().allMatch(a -> a.toUpperCase().startsWith(SeqEnum.TALLY_CODE_000001.getPrefix()))) {
            return "";
        }
        return " 单次只能导入入库单或理货报告";
    }

    /**
     * @param dataList
     * @return java.lang.String
     * @author: WuXian
     * description:  检验数量是否超出 超品和超收暂不考虑
     * create time: 2021/12/2 11:45
     */
    private String checkAsnAndSkuQty(List<ReceiptImportBO> dataList) {
        List<AsnDTO> asnDTOList = dataList.stream().filter(a -> !Objects.isNull(a.getAsnDTO())).map(ReceiptImportBO::getAsnDTO).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(asnDTOList)) {
            return "没有找到到货通知单";
        }
        List<String> asnIdList = asnDTOList.stream().map(AsnDTO::getAsnId).distinct().collect(Collectors.toList());
        for (String asnId : asnIdList) {
            List<ReceiptImportBO> importBOList = dataList.stream().filter(a -> Objects.equals(a.getBillNo(), asnId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(importBOList)) {
                return String.format("到货通知单导入收货%s,未找到导入数据", asnId);
            }
            //asn明细
            List<AsnDetailDTO> asnDetailDTOList = remoteAsnDetailClient.queryListByAsnId(asnId);
            //收货作业批次明细
            ReceiptParam receiptParam = new ReceiptParam();
            receiptParam.setAsnId(asnId);
            receiptParam.setStatusList(Arrays.asList(ReceiptStatusEnum.CREATE_SHELF.getCode()));
            List<ReceiptDTO> receiptDTOList = remoteReceiptClient.getList(receiptParam);
            List<ReceiptDetailDTO> receiptDetailDTOList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(receiptDTOList)) {
                ReceiptDetailParam receiptDetailParam = new ReceiptDetailParam();
                receiptDetailParam.setRecIdList(receiptDTOList.stream().map(ReceiptDTO::getRecId).distinct().collect(Collectors.toList()));
                receiptDetailDTOList = remoteReceiptClient.getDetailList(receiptDetailParam);
            }
            //当前导入商品明细的数量
            List<String> importSkuCodeList = importBOList.stream().map(ReceiptImportBO::getSkuCode).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(importSkuCodeList)) {
                return "找不到商品！！！";
            }
            List<String> expSkuCodeList = asnDetailDTOList.stream().map(AsnDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
            for (String skuCode : importSkuCodeList) {
                if (!expSkuCodeList.contains(skuCode)) {
                    return String.format("到货通知单导入收货%s,商品%s超品", asnId, skuCode);
                }
                //当前导入数量
                BigDecimal currentQty = importBOList.stream().filter(a -> Objects.equals(a.getSkuCode(), skuCode)).map(ReceiptImportBO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                //ASN当前商品计划数量
                BigDecimal expQty = asnDetailDTOList.stream().filter(a -> Objects.equals(a.getSkuCode(), skuCode)).map(AsnDetailDTO::getExpSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                //Asn实收数量
                BigDecimal actualQty = asnDetailDTOList.stream().filter(a -> Objects.equals(a.getSkuCode(), skuCode)).map(AsnDetailDTO::getRecSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                //receipt收货中数量
                BigDecimal receiveingQty = receiptDetailDTOList.stream().filter(a -> Objects.equals(a.getSkuCode(), skuCode)).map(ReceiptDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (expQty.subtract(actualQty).subtract(receiveingQty).subtract(currentQty).compareTo(BigDecimal.ZERO) < 0) {
                    return String.format("到货通知单导入收货%s,商品%s超收", asnId, skuCode);
                }
            }
        }
        return "";
    }

    /**
     * @param ReceiptImportBO
     * @param contCode
     * @param asnDTO
     * @param tallyDTO
     * @return com.dt.platform.wms.transaction.bo.ReceiptImportCommitBO
     * @author: WuXian
     * description:  组装BO数据
     * create time: 2021/12/2 11:47
     */
    private ReceiptImportCommitBO buildReceiptContCodeParam(ReceiptImportBO ReceiptImportBO, String contCode, AsnDTO asnDTO, TallyDTO tallyDTO) {
        ReceiptImportCommitBO receiptImportCommitBO = new ReceiptImportCommitBO();
        //组装收货作业批次
        ReceiptDTO receiptDTO = new ReceiptDTO();
        String recId = remoteSeqRuleClient.findSequence(SeqEnum.REC_CODE_000001);
        //容器占用
        ContainerDTO containerDTO = remoteContainerClient.queryByCode(contCode);
        containerDTO.setOccupyNo(asnDTO.getAsnId());
        containerDTO.setOccupyType(WorkBenchTypeEnum.RECEIVE.getType());
        containerDTO.setStatus(ContainerStatusEnum.OCCUPY.getValue());
        if (tallyDTO == null) {
            containerDTO.setRemark(String.format("导入:到货通知单%s收货作业批次编码%s：", asnDTO.getAsnId(), recId));
        } else {
            containerDTO.setRemark(String.format("导入:理货报告%s收货作业批次编码%s：", tallyDTO.getTallyCode(), recId));
        }
        receiptImportCommitBO.setContainerDTO(containerDTO);
        //正次品
        String skuQuality = ReceiptImportBO.getSkuQuality();

        receiptDTO.setRecId(recId);
        if (tallyDTO != null) {
            receiptDTO.setTallyCode(tallyDTO.getTallyCode());
        }
        receiptDTO.setAsnId(asnDTO.getAsnId());
        receiptDTO.setPoNo(asnDTO.getPoNo());
        receiptDTO.setSoNo(asnDTO.getSoNo());
        receiptDTO.setCargoCode(asnDTO.getCargoCode());
        receiptDTO.setWarehouseCode(asnDTO.getWarehouseCode());
        receiptDTO.setPackageUnitCode(PackageUnitEnum.PCS.code());
        receiptDTO.setContCode(contCode);
        receiptDTO.setRecFlag(WorkTypeEnum.RF.getCode());
        receiptDTO.setBenchCode("");
        receiptDTO.setStatus(ReceiptStatusEnum.CREATE_SHELF.code());
        receiptDTO.setType(asnDTO.getType());
        receiptDTO.setReceiptType(ReceiptTypeEnum.IMPORT_RECEIPT.code());
        receiptDTO.setRecSkuQty(ReceiptImportBO.getQty());
        receiptDTO.setRecSkuType(1);

        receiptDTO.setSkuQuality(skuQuality);
        receiptDTO.setCreatedBy(CurrentUserHolder.getUserName());
        //组装从表
        //检验和获取暂存库位有效性
        LocationDTO locationDTO = remoteSpecialLocationClient.getLocationByReceivingSkuQuality(skuQuality);
        //收货作业批次明细组装
        receiptDTO.setDetailDTOList(buildDetailList(Arrays.asList(ReceiptImportBO), receiptDTO));

        receiptDTO.getDetailDTOList().forEach(a -> a.setLocationCode(locationDTO.getCode()));
        receiptImportCommitBO.setReceiptDTO(receiptDTO);

        return receiptImportCommitBO;
    }


    /**
     * @param tempBOList
     * @param receiptDTO
     * @return java.util.List<com.dt.domain.bill.dto.ReceiptDetailDTO>
     * @author: WuXian
     * description:  组装从表明细
     * create time: 2021/12/2 11:48
     */
    private List<ReceiptDetailDTO> buildDetailList(List<ReceiptImportBO> tempBOList, ReceiptDTO receiptDTO) {
        List<ReceiptDetailDTO> receiptDetailDTOList = new ArrayList<>();
        for (ReceiptImportBO entity : tempBOList) {
            Boolean isWithdraw = true;
            if (!StringUtils.isEmpty(funcCode) && ExcelImportEnum.EXCEL_RECEIPT_IMPORT.getFuncCode().equalsIgnoreCase(funcCode)) {
                isWithdraw = false;
            }
            SkuLotDTO skuLotDTO = skuLotBiz.findAndFormatSkuLot(buildSkuLotParam(entity), entity.getSkuDTO(), isWithdraw);
            if (StringUtils.isEmpty(skuLotDTO.getId())) {
                //处理套盒明细批次
                SkuDTO skuDTO = remoteSkuClient.querySkuByCode(skuLotDTO.getCargoCode(), skuLotDTO.getSkuCode());
                if (SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.BOX_SKU)) {
                    List<SkuLotDTO> commitSkyLotList = skuLotBiz.buildBoxSkuLotList(skuLotDTO);
                    Boolean result = remoteSkuLotClient.saveBatch(commitSkyLotList);
                    if (!result) {
                        throw new BaseException(BaseBizEnum.TIP, "生成货品批次异常");
                    }
                } else {
                    Boolean result = remoteSkuLotClient.saveBatch(Arrays.asList(skuLotDTO));
                    if (!result) {
                        throw new BaseException(BaseBizEnum.TIP, "生成货品批次异常");
                    }
                }
            }
            //组装收货作业批次明细
            ReceiptDetailDTO detailDTO = buildReceiptDetailDTO(entity, skuLotDTO, receiptDTO);
            receiptDetailDTOList.add(detailDTO);
        }
        return receiptDetailDTOList;
    }

    /**
     * @param entity
     * @param skuLotDTO
     * @param receiptDTO
     * @return com.dt.domain.bill.dto.ReceiptDetailDTO
     * @author: WuXian
     * description:
     * create time: 2021/12/2 11:50
     */
    private ReceiptDetailDTO buildReceiptDetailDTO(ReceiptImportBO entity, SkuLotDTO skuLotDTO, ReceiptDTO receiptDTO) {
        ReceiptDetailDTO receiptDetailDTO = new ReceiptDetailDTO();
        receiptDetailDTO.setWarehouseCode(entity.getWarehouseCode());
        receiptDetailDTO.setCargoCode(entity.getCargoCode());
        receiptDetailDTO.setContCode(entity.getContCode());
        receiptDetailDTO.setAsnId(receiptDTO.getAsnId());
        receiptDetailDTO.setRecId(receiptDTO.getRecId());
        receiptDetailDTO.setPoNo(receiptDTO.getPoNo());
        receiptDetailDTO.setSkuQty(entity.getQty());
        receiptDetailDTO.setTargetLocationCode(entity.getTargetLocationCode() == null ? "" : entity.getTargetLocationCode());
        receiptDetailDTO.setSkuCode(entity.getSkuCode());
        receiptDetailDTO.setSkuName(entity.getSkuDTO().getName());
        //使用默认条形码
        receiptDetailDTO.setUpcCode(entity.getUpcCode() == null ? "" : entity.getUpcCode());
        receiptDetailDTO.setVolume(entity.getSkuDTO().getVolume().multiply(receiptDetailDTO.getSkuQty()));
        receiptDetailDTO.setGrossWeight(entity.getSkuDTO().getGrossWeight().multiply(receiptDetailDTO.getSkuQty()));
        receiptDetailDTO.setNetWeight(entity.getSkuDTO().getNetWeight().multiply(receiptDetailDTO.getSkuQty()));
        receiptDetailDTO.setLocationCode(entity.getTargetLocationCode());
        //切分对象获取的商品名称
        receiptDetailDTO.setStatus(ReceiptStatusEnum.CREATE_SHELF.code());
        receiptDetailDTO.setSkuLotNo(skuLotDTO.getCode());
        receiptDetailDTO.setSkuQuality(skuLotDTO.getSkuQuality());
        receiptDetailDTO.setInventoryType(skuLotDTO.getInventoryType());
        receiptDetailDTO.setManufDate(skuLotDTO.getManufDate());
        receiptDetailDTO.setManufDateFormat(skuLotDTO.getManufDateFormat());
        receiptDetailDTO.setWithdrawDate(skuLotDTO.getWithdrawDate());
        receiptDetailDTO.setWithdrawDateFormat(skuLotDTO.getWithdrawDateFormat());
        receiptDetailDTO.setReceiveDate(skuLotDTO.getReceiveDate());
        receiptDetailDTO.setReceiveDateFormat(skuLotDTO.getReceiveDateFormat());
        receiptDetailDTO.setExpireDate(skuLotDTO.getExpireDate());
        receiptDetailDTO.setExpireDateFormat(skuLotDTO.getExpireDateFormat());
        receiptDetailDTO.setProductionNo(skuLotDTO.getProductionNo());
        receiptDetailDTO.setValidityCode(skuLotDTO.getValidityCode());
        receiptDetailDTO.setLineSeq("");
        return receiptDetailDTO;
    }

    /**
     * @param entity
     * @return com.dt.platform.wms.biz.param.SkuLotCheckAndFormatParam
     * @author: WuXian
     * description:  格式化批次规则参数
     * create time: 2022/3/3 11:34
     */
    private SkuLotCheckAndFormatParam buildSkuLotParam(ReceiptImportBO entity) {
        SkuLotCheckAndFormatParam skuLotCheckAndFormatParam = ConverterUtil.convert(entity, SkuLotCheckAndFormatParam.class);
        return skuLotCheckAndFormatParam;
    }
}