package com.dt.platform.wms.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.FromSourceEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.component.common.enums.base.IsHttpEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.bill.*;
import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.enums.cargo.CargoConfigStatusEnum;
import com.dt.component.common.enums.intercept.InterceptStatusEnum;
import com.dt.component.common.enums.material.Is4PLEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.pkg.PackIsPreEnum;
import com.dt.component.common.enums.pre.MultiPreEnableStatusEnum;
import com.dt.component.common.enums.pre.MultiPreTypeEnum;
import com.dt.component.common.enums.pre.PrePackageEnableEnum;
import com.dt.component.common.enums.pre.SkuIsPreEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.sku.SkuTagEnum;
import com.dt.component.common.enums.stock.*;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.exceptions.StageDoneException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoConfigDTO;
import com.dt.domain.base.dto.CarrierDTO;
import com.dt.domain.base.dto.ExpressResultDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.box.BoxSkuDTO;
import com.dt.domain.base.dto.box.BoxSkuDetailDTO;
import com.dt.domain.base.dto.pre.MultiPreDefDTO;
import com.dt.domain.base.param.ExpressParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.box.BoxSkuParam;
import com.dt.domain.base.param.pre.MultiPreDefParam;
import com.dt.domain.bill.bo.pretreatment.PretreatmentOperationBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.pre.PrePackageSkuDTO;
import com.dt.domain.bill.dto.pre.PrePackageSkuDetailDTO;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.pre.PrePackageSkuDetailParam;
import com.dt.domain.bill.param.pre.PrePackageSkuParam;
import com.dt.domain.core.stock.dto.StockDTO;
import com.dt.domain.core.stock.dto.StockTransactionDTO;
import com.dt.domain.core.stock.param.StockParam;
import com.dt.domain.core.stock.param.StockTransactionCancelParam;
import com.dt.domain.core.stock.param.StockTransactionParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.biz.IPretreatmentBiz;
import com.dt.platform.wms.biz.IShipmentOrderBiz;
import com.dt.platform.wms.biz.ISkuStockAndLotBiz;
import com.dt.platform.wms.biz.IWeightBiz;
import com.dt.platform.wms.biz.bo.PretreatmentContext;
import com.dt.platform.wms.biz.stock.biz.IStockTransactionBiz;
import com.dt.platform.wms.biz.stock.biz.NoRetryNeedException;
import com.dt.platform.wms.biz.stock.biz.StockOperationContext;
import com.dt.platform.wms.biz.stock.biz.StockOperationHandler;
import com.dt.platform.wms.biz.stock.biz.bo.AllocationStockBO;
import com.dt.platform.wms.client.config.WmsTenantHelper;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.box.IRemoteBoxSkuClient;
import com.dt.platform.wms.integration.pre.IRemoteMultiPreDefClient;
import com.dt.platform.wms.integration.pre.IRemotePrePackageSkuClient;
import com.dt.platform.wms.integration.pre.IRemotePrePackageSkuDetailClient;
import com.dt.platform.wms.integration.stock.IRemoteStockTransactionClient;
import com.dt.platform.wms.param.material.MaterialCalculateParam;
import com.dt.platform.wms.param.pretreatment.PretreatmentRetryParam;
import com.dt.platform.wms.rpc.client.stock.IStockOperationRpcClient;
import com.dt.platform.wms.rpc.param.StockOccupyDetailParam;
import com.dt.platform.wms.rpc.param.StockOccupyParam;
import com.dt.platform.wms.transaction.impl.PreUnpackingHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class PretreatmentBiz implements IPretreatmentBiz {

    @Autowired
    private ISkuStockAndLotBiz skuStockAndLotBiz;

    @DubboReference(injvm = false)
    private IStockOperationRpcClient stockOperationRpcClient;

    @Resource
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    private IRemoteShipmentDetailClient remoteShipmentDetailClient;

    @Resource
    private IRemotePackageClient remotePackageClient;

    @Resource
    private IRemotePackageDetailClient remotePackageDetailClient;

    @Resource
    private IRemoteAbnormalOrderClient remoteAbnormalOrderClient;

    @Resource
    private IRemoteOrderInterceptClient remoteOrderInterceptClient;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IRemoteCarrierClient remoteCarrierClient;

    @Resource
    private IShipmentOrderBiz shipmentOrderBiz;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteBoxSkuClient remoteBoxSkuClient;

    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private PreUnpackingHelper preUnpackingHelper;

    @Resource
    private IRemoteCargoConfigClient remoteCargoConfigClient;

    @Resource
    private IRemotePrePackageSkuClient remotePrePackageSkuClient;

    @Resource
    private IRemotePrePackageSkuDetailClient remotePrePackageSkuDetailClient;

    @Resource
    private IRemoteStockClient remoteStockClient;

    @Resource
    private IRemoteStockTransactionClient remoteStockTransactionClient;

    @Resource
    private IStockTransactionBiz stockTransactionBiz;

    @Resource
    private IWeightBiz weightBiz;

    @Resource
    private IRemoteExpressClient remoteExpressClient;

    @Autowired
    private IRemoteMultiPreDefClient remoteMultiPreDefClient;

    @Resource
    private WmsTenantHelper wmsTenantHelper;

    @Resource
    private StockOperationHandler stockOperationHandler;

    private static final List<String> ABNORMAL_TYPE_LIST = ListUtil.toList(AbnormalOrderEnum.typeEnum.OUT_STOCK_ORDER.getValue()
            , AbnormalOrderEnum.typeEnum.PACKAGE.getValue());
    private static final List<String> ABNORMAL_STATUS_LIST = ListUtil.toList(AbnormalOrderEnum.statusEnum.RETRY_STATUS.getValue()
            , AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
    private static final List<String> ABNORMAL_BUSINESS_TYPE_LIST = ListUtil.toList(AbnormalOrderEnum.bussinessTypeEnum.ARGS_CHECK_TYPE.getCode()
            , AbnormalOrderEnum.bussinessTypeEnum.ONE_LEVEL_STOCK_TYPE.getCode()
            , AbnormalOrderEnum.bussinessTypeEnum.FETCH_FACE_TYPE.getCode()
            , AbnormalOrderEnum.bussinessTypeEnum.TWO_LEVEL_STOCK_TYPE.getCode()
            , AbnormalOrderEnum.bussinessTypeEnum.THREE_LEVEL_STOCK_TYPE.getCode());

    @Override
    public void pretreatment(PretreatmentContext context) throws Exception {
        try {
            // init context
            init(context);

            // intercept
            intercept(context);

            // 拆包
            preUnpack(context);

            // 预包分析
            pretreatmentAnalyzePackage(context);

            // 维护包裹上的一些字段
            maintainPackageFiled(context);

            // 乐漾调用库存占用接口
            autoOccupyStock(context);

            // 发起占用一级库存
            sendLockStockMessage(context);

            // 查询一级库存占用
            stockOccupyQuery(context);

            //有商品开启SN 不走秒杀spikeAnalysisSku 使用UUID 在MD5
            refreshSpikeAnalysisSku(context);

            // 拉面单
            machExpress(context);


        } catch (MultiPreSatisfyException multiPreSatisfyException) {
            log.error(multiPreSatisfyException.getMessage());
            log.error(context.getShipmentOrderCode());
            argCheckFail(context);
            context.getAbnormalOrderDTOList().forEach(abnormalOrderDTO -> abnormalOrderDTO.setErrorMsg("当前货主有多个启用的预包解析的设置，无法唯一确定订单的解析结果，请确认"));
            flushDB(context);
        } finally {
            unlock(context);
        }
    }

    @Override
    public void autoCompensation(PretreatmentContext context) {
        RpcContextUtil.setWarehouseCode(context.getWarehouseCode());
        // 通过指定出库单补偿
        if (CollectionUtil.isNotEmpty(context.getAutoCompensationShipmentCodeList())) {
            autoCompensationForFixedCodeList(context);
            return;
        }
        autoCompensationForStartMessageLoose(context);
    }

    // 预处理触发后消息可能发送失败
    private void autoCompensationForStartMessageLoose(PretreatmentContext context) {
        try {
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setStatus(ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode());
            shipmentOrderParam.setPretreatmentStatus(PretreatmentStatusEnum.STOCK_START.getStatus());
            shipmentOrderParam.setPageSize(100);
            shipmentOrderParam.setUpdatedTimeEnd(DateUtil.offset(DateTime.now(), DateField.MINUTE, -2).getTime());
            Page<ShipmentOrderDTO> page = remoteShipmentOrderClient.getPage(shipmentOrderParam);
            page.getRecords().forEach(shipmentOrderDTO -> {
                try {
                    PretreatmentContext pretreatmentContext = new PretreatmentContext();
                    pretreatmentContext.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
                    pretreatmentContext.setShipmentId(shipmentOrderDTO.getId());
                    pretreatmentContext.setShipmentOrderCode(shipmentOrderDTO.getShipmentOrderCode());
                    pretreatmentContext.setShipmentOrderDTO(shipmentOrderDTO);
                    pretreatment(pretreatmentContext);
                } catch (Exception exception) {
                    log.error(exception.getMessage(), exception);
                }
            });
        } catch (Exception exception) {
            log.error("自动补偿预处理异常，出库单状态为创建状态，出库单预处理状态为预处理触发状态,仓库:" + context.getWarehouseCode());
            log.error(exception.getMessage(), exception);
        }
    }

    // 处理指定的出库单
    private void autoCompensationForFixedCodeList(PretreatmentContext context) {
        try {
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setShipmentOrderCodeList(context.getAutoCompensationShipmentCodeList());
            Page<ShipmentOrderDTO> page = remoteShipmentOrderClient.getPage(shipmentOrderParam);
            page.getRecords().forEach(shipmentOrderDTO -> {
                try {
                    PretreatmentContext pretreatmentContext = new PretreatmentContext();
                    pretreatmentContext.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
                    pretreatmentContext.setShipmentId(shipmentOrderDTO.getId());
                    pretreatmentContext.setShipmentOrderCode(shipmentOrderDTO.getShipmentOrderCode());
                    pretreatmentContext.setShipmentOrderDTO(shipmentOrderDTO);
                    pretreatment(pretreatmentContext);
                } catch (Exception exception) {
                    log.error(exception.getMessage(), exception);
                }
            });
        } catch (Exception exception) {
            log.error("自动补偿预处理异常，通过指定出库单: {} {}", context.getWarehouseCode(), context.getAutoCompensationShipmentCodeList());
            log.error(exception.getMessage(), exception);
        }
    }

    @Override
    public Boolean retryAbnormal(PretreatmentRetryParam param) {
        boolean locked = false;
        String lockKey = StrUtil.join(StrUtil.COLON, CurrentRouteHolder.getWarehouseCode(), AbnormalOrderDTO.class.getSimpleName());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            locked = lock.tryLock(5, 600, TimeUnit.SECONDS);
            if (locked) {
                Lists.partition(param.getAbnormalIdList(), 50).parallelStream().forEach(it -> {
                    RpcContextUtil.setWarehouseCode(param.getWarehouseCode());
                    List<Long> partitionIdList = new ArrayList<>(it);
                    AbnormalOrderParam abnormalOrderParam = new AbnormalOrderParam();
                    abnormalOrderParam.setWarehouseCode(param.getWarehouseCode());
                    abnormalOrderParam.setIdList(partitionIdList);
                    abnormalOrderParam.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
                    List<AbnormalOrderDTO> abnormalOrderDTOList = remoteAbnormalOrderClient.getList(abnormalOrderParam);

                    List<String> shipmentOrderCodeList = new ArrayList<>();
                    List<String> shipmentOrderCodes = abnormalOrderDTOList.stream().filter(abnormalOrderDTO -> AbnormalOrderEnum.typeEnum.OUT_STOCK_ORDER.getValue().equalsIgnoreCase(abnormalOrderDTO.getBillType()))
                            .map(AbnormalOrderDTO::getBillNo)
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(shipmentOrderCodes)) {
                        shipmentOrderCodeList.addAll(shipmentOrderCodes);
                    }

                    List<String> packageCodeList = abnormalOrderDTOList.stream().filter(abnormalOrderDTO -> AbnormalOrderEnum.typeEnum.PACKAGE.getValue().equalsIgnoreCase(abnormalOrderDTO.getBillType()))
                            .map(AbnormalOrderDTO::getBillNo)
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(packageCodeList)) {
                        PackageParam packageParam = new PackageParam();
                        packageParam.setPackageCodeList(packageCodeList);
                        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
                        shipmentOrderCodeList.addAll(packageDTOList.stream().map(PackageDTO::getShipmentOrderCode).collect(Collectors.toList()));
                    }
                    if (CollectionUtil.isNotEmpty(shipmentOrderCodeList)) {
                        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
                        shipmentOrderParam.setShipmentOrderCodeList(shipmentOrderCodeList);
                        List<ShipmentOrderDTO> shipmentOrderDTOList = remoteShipmentOrderClient.getList(shipmentOrderParam);
                        shipmentOrderDTOList.forEach(shipmentOrderDTO -> {
                            PretreatmentContext pretreatmentContext = new PretreatmentContext();
                            pretreatmentContext.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
                            pretreatmentContext.setShipmentOrderDTO(shipmentOrderDTO);
                            pretreatmentContext.setShipmentOrderCode(shipmentOrderDTO.getShipmentOrderCode());
                            pretreatmentContext.setShipmentId(shipmentOrderDTO.getId());
                            pretreatmentContext.setRetry(true);
                            try {
                                pretreatment(pretreatmentContext);
                            } catch (StageDoneException | NoRetryNeedException exception) {
                                log.info(exception.getMessage());
                            } catch (Exception exception) {
                                log.error(exception.getMessage(), exception);
                            }
                        });
                    }
                });
                return true;
            } else {
                throw new BaseException(BaseBizEnum.TIP, "系统繁忙，请稍后再试");
            }
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
        return false;
    }

    /**
     * - 出库单信息和拦截单信息每次都需要重数据库中查询
     * - 推荐快递维护也放到了这里
     */
    protected void init(PretreatmentContext context) throws InterruptedException {
        RpcContextUtil.setWarehouseCode(context.getWarehouseCode());

        // 基础数据校验
        if (StrUtil.isBlank(context.getWarehouseCode())) {
            throw new NoRetryNeedException(BaseBizEnum.TIP, "预处理消息没有仓库编码");
        }
        if (context.getShipmentId() == null && StrUtil.isBlank(context.getShipmentOrderCode())) {
            throw new NoRetryNeedException(BaseBizEnum.TIP, "预处理消息没有出库单ID，也没有出库单编号");
        }

        // 获取锁
        lock(context);

        // 查询数据
        ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTO(context);
        context.setShipmentId(shipmentOrderDTO.getId());
        context.setShipmentOrderCode(shipmentOrderDTO.getShipmentOrderCode());
        packageDTOList(context);
        abnormalOrderDTOList(context);

        lock(context);

        // 推荐快递维护
        if (StrUtil.isBlank(shipmentOrderDTO.getCarrierCode())) {
            shipmentOrderBiz.maintainCarrierIfNeed(shipmentOrderDTO.getWarehouseCode(), shipmentOrderDTO.getCargoCode(), shipmentOrderDTO.getShipmentOrderCode());
            shipmentOrderDTO = remoteShipmentOrderClient.getById(shipmentOrderDTO.getId());
        }
        context.setShipmentOrderDTO(shipmentOrderDTO);

        // 拦截单
        List<OrderInterceptDTO> orderInterceptDTOList = orderInterceptDTOList(context);
        context.setOrderInterceptDTOList(orderInterceptDTOList);
    }

    private void lock(PretreatmentContext context) throws InterruptedException {
        if (context.isLocked()) return;
        if (StrUtil.isBlank(context.getShipmentOrderCode())) return;
        String lockKey = StrUtil.join(StrUtil.COLON, context.getWarehouseCode(), ShipmentOrderDTO.class.getSimpleName(), context.getShipmentOrderCode());
        log.info("lockKey {}", lockKey);
        RLock lock = redissonClient.getFairLock(lockKey);
        boolean locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
        if (locked) {
            context.setLocked(true);
            context.setLock(lock);
            log.info("预处理获取锁成功,出库单:{}", context.getShipmentOrderCode());
        } else {
            throw new BaseException(BaseBizEnum.TIP, "预处理获取锁失败,出库单:{}", context.getShipmentOrderCode());
        }
    }

    private void unlock(PretreatmentContext context) {
        if (context.isLocked()) {
            try {
                context.getLock().unlock();
                log.info("预处理释放锁成功,出库单:{}", context.getShipmentOrderCode());
            } catch (Exception exception) {
                log.error(exception.getMessage(), exception);
                log.error("预处理释放锁抛出异常,出库单信息{}", JSONUtil.toJsonStr(context.getShipmentOrderCode()));
            }
        }
    }

    // 拦截
    protected void intercept(PretreatmentContext context) {
        if (CollectionUtil.isEmpty(orderInterceptDTOList(context))) {
            return;
        }
        // 出库单
        ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTO(context);
        // 查询包裹
        List<PackageDTO> packageDTOList = packageDTOList(context);
        List<String> packageStatusList = new ArrayList<>();
        packageStatusList.add(PackEnum.STATUS.CREATE_STATUS.getCode());
        packageStatusList.add(PackEnum.STATUS.PRETREATMENT_FAIL.getCode());
        packageStatusList.add(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
        packageStatusList.add(PackEnum.STATUS.ASSGIN_STOCK_STATUS.getCode());
        // 需要预处理阶段拦截的包裹
        List<PackageDTO> packageDTOListFirst = packageDTOList.stream()
                .filter(packageDTO -> packageStatusList.contains(packageDTO.getStatus())).collect(Collectors.toList());
        List<String> packageCodeListFirst = packageDTOListFirst.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList());
        // 需要通过归位拦截的包裹
        List<PackageDTO> packageDTOListSecond = packageDTOList.stream()
                .filter(packageDTO -> !packageStatusList.contains(packageDTO.getStatus())).collect(Collectors.toList());

        PretreatmentOperationBO operationBO = context.getOperationBO();
        if (CollectionUtil.isNotEmpty(packageDTOListFirst)) {
            StockTransactionParam stockTransactionParam = new StockTransactionParam();
            stockTransactionParam.setBillNoList(packageCodeListFirst);
            stockTransactionParam.setBillType(BillTypeEnum.BILL_TYPE_PACKAGE.getType());
            stockTransactionParam.setTradeType(TradeTypeEnum.TRADE_TYPE_SHIPMENT.getType());
            stockTransactionParam.setOperationType(OperationTypeEnum.OPERATION_SHIPMENT.getType());
            List<StockTransactionDTO> stockTransactionDTOList = remoteStockTransactionClient.getList(stockTransactionParam);

            // 包裹明细
            PackageDetailParam packageDetailParam = new PackageDetailParam();
            packageDetailParam.setPackageCodeList(packageCodeListFirst);
            List<PackageDetailDTO> packageDetailDTOList = remotePackageDetailClient.getList(packageDetailParam);
            if (CollectionUtil.isEmpty(packageDetailDTOList)) {
                return;
            }
            packageDetailDTOList.forEach(packageDetailDTO -> packageDetailDTO.setStatus(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode()));
            operationBO.setPackageDetailDTOList(packageDetailDTOList);

            if (CollectionUtil.isNotEmpty(stockTransactionDTOList)) {
                StockTransactionCancelParam stockTransactionCancelParam = new StockTransactionCancelParam();
                stockTransactionCancelParam.setStockTransactionDTOList(stockTransactionDTOList);
                stockTransactionCancelParam.setCustomerOperationType(OperationTypeEnum.OPERATION_INTERCEPT.getType());
                stockTransactionCancelParam.setCustomerTradeType(TradeTypeEnum.TRADE_TYPE_INTERCEPTION.getType());
                stockTransactionBiz.cancelAllLevel(stockTransactionCancelParam);
            }
        }

        // 出库单对应的异常单
        List<AbnormalOrderDTO> abnormalOrderDTOList = abnormalOrderDTOList(context);
        abnormalOrderDTOList = abnormalOrderDTOList.stream()
                .filter(abnormalOrderDTO -> AbnormalOrderEnum.statusEnum.RETRY_STATUS.getValue().equals(abnormalOrderDTO.getStatus())
                        || AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue().equals(abnormalOrderDTO.getStatus()))
                .peek(abnormalOrderDTO -> abnormalOrderDTO.setStatus(AbnormalOrderEnum.statusEnum.CANCEL_STATUS.getValue())).collect(Collectors.toList());
        operationBO.getAbnormalOrderDTOList().addAll(abnormalOrderDTOList);

        packageDTOListFirst.forEach(packageDTO -> {
            packageDTO.setStatus(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode());
            packageDTO.setPretreatmentStatus(PretreatmentStatusEnum.INTERCEPT_SUCCESS.getStatus());
            packageDTO.setInterceptCancelDate(System.currentTimeMillis());
        });

        shipmentOrderDTO.setStatus(ShipmentOrderEnum.STATUS.INTERCEPT_STATUS.getCode());
        shipmentOrderDTO.setInterceptCancelDate(System.currentTimeMillis());
        shipmentOrderDTO.setPretreatmentStatus(PretreatmentStatusEnum.INTERCEPT_SUCCESS.getStatus());

        operationBO.setShipmentOrderDTO(shipmentOrderDTO);
        operationBO.setPackageDTOList(packageDTOListFirst);
        if (packageDTOList.stream().allMatch(packageDTO -> packageDTO.getStatus().equals(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode()))) {
            // 存在需要归位拦截的包裹时这里不完成拦截单
            if (CollectionUtil.isEmpty(packageDTOListSecond)) {
                orderInterceptDTOList(context).forEach(orderInterceptDTO -> orderInterceptDTO.setStatus(InterceptStatusEnum.INTERCEPT_COMPLETE.getCode()));
                operationBO.setOrderInterceptDTOList(orderInterceptDTOList(context));
            }
        }
        flushDB(context);
        throw new StageDoneException("拦截完成");
    }

    protected void preUnpack(PretreatmentContext context) {
        ShipmentOrderDTO shipmentOrder = shipmentOrderDTO(context);
        log.info("前置拆单，单据：{}", JSONUtil.toJsonStr(context.getShipmentOrderCode()));

        // 查询所有不是取消包裹
        List<PackageDTO> allPackageDTOList = packageDTOList(context);
        if (CollectionUtil.isNotEmpty(allPackageDTOList)) {
            return;
        }

        CarrierDTO carrier = carrierDTO(context);

        // 快递公司缺失
        if (carrier == null) {
            if (StrUtil.isBlank(shipmentOrder.getCarrierCode())) {
                context.setErrorMessage("快递公司缺失");
            } else {
                context.setErrorMessage("快递公司不存在");
            }
            argCheckFail(context);
            flushDB(context);
            throw new StageDoneException("快递信息错误");
        }

        // 承运商存在、不是接口取、运单号不存在
        if (IsHttpEnum.DISABLE.getValue().equals(carrier.getIsHttp()) && StringUtils.isEmpty(shipmentOrder.getExpressNo())) {
            context.setErrorMessage("非电子面单运单号缺失");
            argCheckFail(context);
            flushDB(context);
            throw new StageDoneException("非电子面单运单号缺失");
        }

        // 出库单明细
        List<ShipmentOrderDetailDTO> detailList = shipmentOrderDetailDTOList(context);

        // 有效状态的包裹
        List<PackageDTO> packageDTOList = allPackageDTOList.stream()
                .filter(it -> !PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode().equalsIgnoreCase(it.getStatus())).collect(Collectors.toList());

        // 是否需要拆包
        boolean needUnpack = packageDTOList.size() == 0;

        if (CollectionUtil.isEmpty(packageDTOList)) {
            PackageDTO packageDTO = new PackageDTO();
            BeanUtil.copyProperties(shipmentOrder, packageDTO);
            packageDTO.setId(null);
            packageDTO.setVersion(null);
            packageDTO.setExpressNo(null);
            packageDTO.setIsPre(PackEnum.TYPE.NORMAL.getCode());
            packageDTO.setCreatedBy(shipmentOrder.getUpdatedBy());
            packageDTO.setCreatedTime(System.currentTimeMillis());
            packageDTO.setStatus(PackEnum.STATUS.CREATE_STATUS.getCode());
            packageDTO.setPretreatmentStatus(PretreatmentStatusEnum.PACKAGE_SUCCESS.getStatus());
            packageDTO.setPackageCode(remoteSeqRuleClient.findSequence(SeqEnum.PACK_CODE_000001));
            packageDTO.setUpdatedTime(null);
            packageDTO.setUpdatedBy("");
            packageDTOList.add(packageDTO);
        } else {
            return;
        }

        //包裹明细
        List<PackageDetailDTO> packageDetailDTOList = packageDetailDTOList(context);

        // 如果包裹明细列表为空，这时候包裹应该只有一个，按照出库单明细生成包裹明细
        if (CollectionUtil.isEmpty(packageDetailDTOList)) {
            PackageDTO firstPackageDTO = packageDTOList.get(0);
            for (ShipmentOrderDetailDTO shipmentOrderDetail : detailList) {
                PackageDetailDTO packageDetailDTO = ConverterUtil.convert(shipmentOrderDetail, PackageDetailDTO.class);
                assert packageDetailDTO != null;
                packageDetailDTO.setId(null);
                packageDetailDTO.setVersion(null);
                packageDetailDTO.setUpdatedTime(null);
                packageDetailDTO.setUpdatedBy("");
                //生成明细ID
                packageDetailDTO.setPackUid(UUID.randomUUID().toString());
                //是否赠品
                packageDetailDTO.setFreeFlag(shipmentOrderDetail.getFreeFlag());
                packageDetailDTO.setSkuQty(shipmentOrderDetail.getExpSkuQty());
                packageDetailDTO.setExpQty(BigDecimal.ZERO);
                packageDetailDTO.setIsPre(PackIsPreEnum.NORMAL.getCode());
                packageDetailDTO.setCreatedTime(firstPackageDTO.getCreatedTime());
                packageDetailDTO.setCreatedBy(firstPackageDTO.getCreatedBy());
                packageDetailDTO.setLineSeq(shipmentOrderDetail.getLineSeq());
                packageDetailDTO.setStatus(PackEnum.STATUS.CREATE_STATUS.getCode());
                packageDetailDTO.setZoneType(ZoneTypeEnum.ZONE_TYPE_PICK.getType());
                packageDetailDTO.setPUid(shipmentOrderDetail.getId());
                packageDetailDTO.setPackageCode(firstPackageDTO.getPackageCode());
                packageDetailDTOList.add(packageDetailDTO);
            }
        }

        // 组装包裹结构
        packageDTOList.forEach(packageDTO -> {
            List<PackageDetailDTO> packageDetailDTOListInPack = packageDetailDTOList.stream().filter(packageDetailDTO -> packageDetailDTO.getPackageCode().equals(packageDTO.getPackageCode())).collect(Collectors.toList());
            packageDTO.setListDetail(packageDetailDTOListInPack);
        });

        // 保税不允许拆包
        if (TaxTypeEnum.TYPE_BONDED_TAX.getCode().equalsIgnoreCase(shipmentOrder.getTaxType()) && !ShipmentOrderEnum.BUSSINESS_TYPE.B2B.name().equals(shipmentOrder.getBusinessType())) {
            needUnpack = false;
        }
        // 前置拆包
        if (needUnpack) {
            packageDTOList = preUnpackingHelper.preUnpack(packageDTOList);
        }

        // 拆包后维护出库单包裹数量
        shipmentOrder.setPackageQty(packageDTOList.size());

        // 维护包裹正次品属性
        packageDTOList.forEach(packageDTO -> packageDTO.setSkuQuality(packageDTO.getListDetail().get(0).getSkuQuality()));

        // 维护包裹商品种类数、商品数量
        packageDTOList.forEach(packageDTO -> {
            long skuTypeCount = packageDTO.getListDetail().stream().map(PackageDetailDTO::getSkuCode).distinct().count();
            packageDTO.setSkuTypeQty((int) skuTypeCount);
            BigDecimal packageSkuQty = packageDTO.getListDetail().stream().map(PackageDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            packageDTO.setPackageSkuQty(packageSkuQty);
        });

        // 数量校验
        shipmentOrder.setListShipmentOrderDetailDTO(detailList);
        boolean checkResult = checkSkuDetailInOrderAndPackage(shipmentOrder, packageDTOList);
        if (!checkResult) {
            throw new StageDoneException("生成包裹校验出库单包裹商品数量失败");
        }

        //维护包裹结构
        packageStructure(packageDTOList);

        shipmentOrder.setPretreatmentStatus(PretreatmentStatusEnum.PACKAGE_SUCCESS.getStatus());
        List<PackageLogDTO> packageLogDTOList = packageDTOList.stream().map(packageDTO -> {
            PackageLogDTO packageLogDTO = new PackageLogDTO();
            BeanUtil.copyProperties(packageDTO, packageLogDTO);
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setOpBy(packageDTO.getCreatedBy());
            packageLogDTO.setOpContent(String.format("包裹创建,单号:%s", packageDTO.getPackageCode()));
            return packageLogDTO;
        }).collect(Collectors.toList());
        List<PackageDetailDTO> packageDetailDTOS = packageDTOList.stream().flatMap(it -> it.getListDetail().stream()).collect(Collectors.toList());

        // 清理异常单
        cleanAllAbnormal(context);

        context.setPackageDTOList(packageDTOList);
        context.setPackageDetailDTOList(packageDetailDTOS);

        // 包裹创建日志
        context.getOperationBO().setPackageLogDTOList(packageLogDTOList);
    }

    /**
     * 承运商不存在
     */
    private void argCheckFail(PretreatmentContext context) {
        ShipmentOrderDTO shipmentOrder = shipmentOrderDTO(context);
        List<AbnormalOrderDTO> argCheckFail = abnormalOrderDTOList(context).stream()
                .filter(abnormalOrderDTO -> AbnormalOrderEnum.bussinessTypeEnum.ARGS_CHECK_TYPE.getCode().equals(abnormalOrderDTO.getBusinessType()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(argCheckFail)) {
            AbnormalOrderDTO abnormalOrder = new AbnormalOrderDTO();
            //订单的业务类型
            abnormalOrder.setOrderBusinessType(shipmentOrder.getBusinessType());
            abnormalOrder.setWarehouseCode(shipmentOrder.getWarehouseCode());
            abnormalOrder.setCargoCode(shipmentOrder.getCargoCode());
            abnormalOrder.setBillNo(shipmentOrder.getShipmentOrderCode());
            abnormalOrder.setPoNo(shipmentOrder.getPoNo());
            abnormalOrder.setSoNo(shipmentOrder.getSoNo());
            abnormalOrder.setCarrierCode(shipmentOrder.getCarrierCode());
            abnormalOrder.setBillType(AbnormalOrderEnum.typeEnum.OUT_STOCK_ORDER.getValue());
            abnormalOrder.setBusinessType(AbnormalOrderEnum.bussinessTypeEnum.ARGS_CHECK_TYPE.getCode());
            abnormalOrder.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
            abnormalOrder.setRetryCount(0);
            abnormalOrder.setErrorStatus("-500");
            abnormalOrder.setErrorMsg(context.getErrorMessage());
            abnormalOrderDTOList(context).add(abnormalOrder);
        } else {
            for (AbnormalOrderDTO abnormalOrder : abnormalOrderDTOList(context)) {
                abnormalOrder.setRetryCount(abnormalOrder.getRetryCount() + 1);
                abnormalOrder.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
            }
        }
        PretreatmentOperationBO preUnpackBO = context.getOperationBO();

        if (ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode().equals(shipmentOrder.getStatus())) {
            ShipmentOrderLogDTO shipmentOrderLogDTO = new ShipmentOrderLogDTO();
            BeanUtil.copyProperties(shipmentOrder, shipmentOrderLogDTO);
            shipmentOrderLogDTO.setOpRemark("");
            shipmentOrderLogDTO.setCreatedBy(shipmentOrder.getUpdatedBy());
            shipmentOrderLogDTO.setOpBy(shipmentOrder.getUpdatedBy());
            shipmentOrderLogDTO.setOpDate(System.currentTimeMillis());
            shipmentOrderLogDTO.setOpContent(String.format("预处理失败,单号:%s", shipmentOrder.getShipmentOrderCode()));
            preUnpackBO.setShipmentOrderLogDTO(shipmentOrderLogDTO);
        }
        shipmentOrder.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_FAIL_STATUS.getCode());
        preUnpackBO.setShipmentOrderDTO(shipmentOrder);
        preUnpackBO.setAbnormalOrderDTOList(abnormalOrderDTOList(context));
    }

    /**
     * 预包分析
     */
    protected void pretreatmentAnalyzePackage(PretreatmentContext context) {
        log.info("预报分析，单据：{}", JSONUtil.toJsonStr(context.getShipmentOrderCode()));
        //查询出库单
        ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTO(context);
        // 是否需要预包分析
        //是否开启溯源码
        Boolean checkSource = isCheckSource(shipmentOrderDTO.getCargoCode());
        if (checkSource) {
            log.warn("开启溯源码，不分析");
            return;
        }
        // 只有B2C的订单需要做预包分析
        if (!shipmentOrderDTO.getBusinessType().equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.name())) {
            log.warn("非B2C，不分析");
            return;
        }


        //查询包裹
        List<PackageDTO> packageDTOList = packageDTOList(context);
        if (CollectionUtil.isEmpty(packageDTOList)) {
            log.warn("没有包裹");
            return;
        }
        if (packageDTOList.stream().anyMatch(packageDTO -> ObjectUtil.isNotEmpty(packageDTO.getId()))) {
            log.warn("包裹已创建");
            return;
        }
        // 前置拆包的不做预包
        if (packageDTOList.size() > 1) {
            log.warn("前置拆单不走预包");
            return;
        }

        List<StockTransactionDTO> stockTransactionDTOS = stockTransactionDTOList(context);
        if (stockTransactionDTOS.stream().anyMatch(stockTransactionDTO -> stockTransactionDTO.getStockLevel().equalsIgnoreCase(StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel()))) {
            log.warn("已经占用了三级账不走预包分析");
            return;
        }

        //查询包裹明细
        List<PackageDetailDTO> packageDetailList = packageDetailDTOList(context);
        Map<String, List<PackageDetailDTO>> packageDetailDTOMap = packageDetailList.stream().collect(Collectors.groupingBy(PackageDetailDTO::getPackageCode));

        packageDTOList.forEach(packageDTO -> {
            List<PackageDetailDTO> detailDTOList = packageDetailDTOMap.get(packageDTO.getPackageCode());

            //指定批次,指定库位,次品的明细，不参与预包结构分析。 TODO ADD 2022-12-14 指定属性不参与预包分析
//            boolean allMatch = detailDTOList.stream()
//                    .allMatch(it -> ObjectUtil.isEmpty(it.getLocationCode())
//                            && ObjectUtil.isEmpty(it.getSkuLotNo())
//                            && ObjectUtil.isEmpty(it.getExternalSkuLotNo())
//                            && it.getExpireDate() == 0L
//                            && it.getSkuQuality().equalsIgnoreCase(SkuQualityEnum.SKU_QUALITY_AVL.getLevel()));
            //淘天预包残次等级需要时ZP
//            if (OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).contains(OrderTagEnum.TAOTAIN)
//                    && detailDTOList.stream().anyMatch(a -> !a.getInventoryType().equalsIgnoreCase(InventoryTypeEnum.ZP.getCode()))) {
//                allMatch = false;
//            }
            // 先执行多预包匹配
            // 预包预处理占用三级账【不好支持这个】
//                List<PackageDetailDTO> resultDetailDTOList = analyzeMultiPackageDetails(packageDTO.getCargoCode(), detailDTOList);
            List<PackageDetailDTO> resultDetailDTOList = new ArrayList<>();

            if (CollectionUtil.isEmpty(resultDetailDTOList)) {
                //根据包裹明细分析是否满足预包包裹
                resultDetailDTOList = analyzePackageDetails(packageDTO.getCargoCode(), detailDTOList);
                if (CollectionUtil.isNotEmpty(resultDetailDTOList) && resultDetailDTOList.stream()
                        .anyMatch(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode()))) {
                    log.info("存在剩余明细");
                    resultDetailDTOList = new ArrayList<>();
                } else {
                    // 预包数量只能是1件
                    if (resultDetailDTOList.stream()
                            .filter(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()))
                            .anyMatch(packageDetailDTO -> packageDetailDTO.getSkuQty().intValue() != 1)) {
                        log.info("预包数量大于1");
                        resultDetailDTOList = new ArrayList<>();
                    }
                }

            }
            if (CollectionUtil.isNotEmpty(resultDetailDTOList)) {
                boolean match = resultDetailDTOList.stream()
                        .anyMatch(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()));
                if (match) {

                    PackageDTO anaylise = new PackageDTO();
                    BeanUtil.copyProperties(packageDTO, anaylise);
                    anaylise.setIsPre(PackEnum.TYPE.PRE.getCode());
                    anaylise.setListDetail(resultDetailDTOList);

                    Result<List<AllocationStockBO>> listResult = skuStockAndLotBiz.analysisOccupyPrePackData(anaylise, resultDetailDTOList);
                    log.info("预包占用三级账分析 {} {}", anaylise.getPackageCode() ,JSONUtil.toJsonStr(listResult));
                    if (!listResult.checkSuccess()) return;
                    context.setAllocationStockBOList(listResult.getData());
                    for (AllocationStockBO allocationStockBO : context.getAllocationStockBOList()) {
                        allocationStockBO.setGlobalNo(shipmentOrderDTO.getGlobalNo());
                    }

                    resultDetailDTOList.addAll(detailDTOList);
                    packageDTO.setListDetail(resultDetailDTOList);
                    packageDTO.setIsPre(PackEnum.TYPE.PRE.getCode());
                    long skuTypeCount = resultDetailDTOList.stream().filter(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode())
                            || packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).map(PackageDetailDTO::getSkuCode).distinct().count();
                    packageDTO.setSkuTypeQty((int) skuTypeCount);
                    BigDecimal packageSkuQty = resultDetailDTOList.stream().filter(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode())
                            || packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).map(PackageDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    packageDTO.setPackageSkuQty(packageSkuQty);
                }
            } else {
                packageDTO.setListDetail(packageDetailDTOMap.get(packageDTO.getPackageCode()));
            }
        });
        //如果有预包 分析包裹结构
        if (packageDTOList.stream().anyMatch(packageDTO -> packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode()))) {
            packageStructure(packageDTOList);
        }
        packageDTOList.forEach(packageDTO -> packageDTO.setPretreatmentStatus(PretreatmentStatusEnum.STOCK_OCCUPY_ONGOING.getStatus()));

        PretreatmentOperationBO operationBO = context.getOperationBO();
        List<PackageDetailDTO> newPackageDetailList = packageDTOList.stream().flatMap(it -> it.getListDetail().stream()).collect(Collectors.toList());
//        //预包商品对应的子商品 禁售比对时间设置成0L
//        if (packageDTOList.stream().anyMatch(packageDTO -> packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode()))) {
//            newPackageDetailList.stream()
//                    .filter(a -> Objects.equals(a.getIsPre(), PackIsPreEnum.PRE_CHILD.getCode()))
//                    .forEach(it -> it.setWithdrawCompareDate(0L));
//        }
        context.setPackageDetailDTOList(newPackageDetailList);
        operationBO.setPackageDetailDTOList(newPackageDetailList);
        operationBO.setPackageDTOList(packageDTOList);
    }

    protected void autoOccupyStock(PretreatmentContext context) {
        ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTO(context);
        if (OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.LY_SHIP)
                && FromSourceEnum.WMS.value().equalsIgnoreCase(shipmentOrderDTO.getFromSource())) {
            StockOccupyParam stockOccupyParam = new StockOccupyParam();
            stockOccupyParam.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
            stockOccupyParam.setCargoCode(shipmentOrderDTO.getCargoCode());
            stockOccupyParam.setGlobalNo(shipmentOrderDTO.getGlobalNo());
            stockOccupyParam.setPoNo(shipmentOrderDTO.getPoNo());
            List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = shipmentOrderDetailDTOList(context);
            stockOccupyParam.setStockOccupyDetailParamList(shipmentOrderDetailDTOList.stream().map(shipmentOrderDetailDTO -> {
                StockOccupyDetailParam stockOccupyDetailParam = new StockOccupyDetailParam();
                stockOccupyDetailParam.setSkuCode(shipmentOrderDetailDTO.getSkuCode());
                stockOccupyDetailParam.setSkuQuality(shipmentOrderDetailDTO.getSkuQuality());
                stockOccupyDetailParam.setQty(shipmentOrderDetailDTO.getExpSkuQty());
                return stockOccupyDetailParam;
            }).collect(Collectors.toList()));
            stockOperationRpcClient.occupy(stockOccupyParam);
        }
    }

    /**
     * 需要发送一级库存占用的场景
     * - 上游未发起占用
     * - 库内新增单据
     * - 预包
     * - 套盒
     */
    protected void sendLockStockMessage(PretreatmentContext context) {
        ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTO(context);
        List<StockTransactionDTO> stockTransactionDTOList = stockTransactionDTOList(context);
        boolean containPre = packageDTOList(context).stream().anyMatch(packageDTO -> packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode()));
        if (CollectionUtil.isEmpty(stockTransactionDTOList)
                || containPre
        ) {
            // 有失败不发起
            if (CollectionUtil.isNotEmpty(stockTransactionDTOList)
                    && stockTransactionDTOList.stream().anyMatch(stockTransactionDTO -> StockTransactionStatusEnum.FAIL.getCode().equalsIgnoreCase(stockTransactionDTO.getStatus()))) {
                return;
            }

            // 预包已经处理了也不用发起
            for (StockTransactionDTO stockTransactionDTO : stockTransactionDTOList) {
                SkuDTO skuDTO = skuDTOList(context).stream().filter(it -> stockTransactionDTO.getSkuCode().equalsIgnoreCase(it.getCode())).findFirst().orElseThrow(() -> new BaseException(BaseBizEnum.TIP, "商品信息不存在"));
                if (skuDTO.getIsPre().equalsIgnoreCase(SkuIsPreEnum.PRE.getCode())) return;
            }

            maintainPackageDetail(context);
            context.getOperationBO().setPackageDTOList(packageDTOList(context));
            context.getOperationBO().setPackageDetailDTOList(packageDTOList(context).stream().flatMap(packageDTO -> packageDTO.getListDetail().stream()).collect(Collectors.toList()));
            flushDB(context);

            StockOperationContext operationContext = StockOperationContext.builder()
                    .warehouseCode(shipmentOrderDTO.getWarehouseCode())
                    .cargoCode(shipmentOrderDTO.getCargoCode())
                    .globalNo(shipmentOrderDTO.getGlobalNo())
                    .parentBillNo(shipmentOrderDTO.getShipmentOrderCode())
                    .operationType(OperationTypeEnum.OPERATION_SHIPMENT.getType())
                    .stockLevel(StockLevelEnum.LEVEL_STOCK.getLevel())
                    .build();

            if (containPre) {
                operationContext.setAllocationStockBOList(context.getAllocationStockBOList());
            }

            stockOperationHandler.process(operationContext);
            throw new StageDoneException("发送一级库存占用消息完成");
        }
    }

    private void maintainPackageDetail(PretreatmentContext context) {
        packageDTOList(context).forEach(packageDTO -> {
            List<PackageDetailDTO> packageDetailDTOListInPack = packageDetailDTOList(context).stream().filter(packageDetailDTO -> packageDetailDTO.getPackageCode().equals(packageDTO.getPackageCode())).collect(Collectors.toList());
            packageDTO.setListDetail(packageDetailDTOListInPack);
        });

    }


    /**
     * 查询一级库存占用
     */
    protected void stockOccupyQuery(PretreatmentContext context) {
        log.info("一级库存查询，单据：{} ", JSONUtil.toJsonStr(context.getShipmentOrderCode()));
        // 批量查询库存交易数据
        List<StockTransactionDTO> stockTransactionDTOList = stockTransactionDTOList(context);

        // 没有核销记录需要补发消息
        if (CollectionUtil.isEmpty(stockTransactionDTOList)) {
            sendLockStockMessage(context);
            throw new IllegalStateException("核销不存在，需要重新发一级库存占用消息");
        }

        // 如果还在处理中的直接返回，下次处理
        if (stockTransactionDTOList.stream().anyMatch(stockTransactionDTO ->
                stockTransactionDTO.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CREATED.getCode())
                        || stockTransactionDTO.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.RETRY.getCode())
                        || stockTransactionDTO.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCEL_ONGOING.getCode()))) {
            log.info("库存处理中");
            throw new StageDoneException("库存处理中");
        }

        //真实商品 库存处理状态是否都是处理成功状态
        boolean match = stockTransactionDTOList.stream()
                .filter(stockTransactionDTO -> stockTransactionDTO.getStockLevel().equalsIgnoreCase(StockLevelEnum.LEVEL_STOCK.getLevel()))
                .filter(stockTransactionDTO -> stockTransactionDTO.getRealGoods().equalsIgnoreCase(RealOrUnrealGoodsEnum.REAL.getCode()))
                .allMatch(stockTransactionDTO -> stockTransactionDTO.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.DONE.getCode()));

        //查询包裹
        List<PackageDTO> packageDTOList = packageDTOList(context);
        if (CollectionUtil.isEmpty(packageDTOList)) {
            throw new IllegalStateException("没有找到包裹");
        }

        Map<String, PackageDTO> packageDTOMap = packageDTOList.stream().collect(Collectors.toMap(PackageDTO::getPackageCode, Function.identity()));
        context.setPackageDTOMap(packageDTOMap);
        //查询包裹明细
        List<String> packageCodeList = packageDTOList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList());
        context.setPackageCodeList(packageCodeList);

        List<PackageDetailDTO> packageDetailDTOList = packageDetailDTOList(context);
        if (CollectionUtil.isEmpty(packageDetailDTOList)) {
            throw new IllegalStateException("没有找到包裹明细");
        }
        Map<String, List<PackageDetailDTO>> packageDetailDTOMap = packageDetailDTOList.stream().collect(Collectors.groupingBy(PackageDetailDTO::getPackageCode));

        //查询异常单
        List<PackageDTO> newPackageDTOList = Lists.newArrayList();
        //成功的逻辑
        if (match) {
            // 失败的核销
            List<StockTransactionDTO> failStockTransactionList = stockTransactionDTOList.stream()
                    .filter(stockTransactionDTO -> StockTransactionStatusEnum.FAIL.getCode().equalsIgnoreCase(stockTransactionDTO.getStatus()))
                    .collect(Collectors.toList());


            boolean preFail = CollectionUtil.isNotEmpty(failStockTransactionList);

            //包裹成功
            packageDTOList.forEach(packageDTO -> {
                //判断是否有失败的预包条码，修改包裹的属性
                if (preFail && PackEnum.TYPE.PRE.getCode().equals(packageDTO.getIsPre())) {
                    packageDTO.setIsPre(PackEnum.TYPE.NORMAL.getCode());
                    //预包库存占用失败，直接转换为普通包裹，然后包裹结构还原会之前的。
                    String[] split = packageDTO.getRemark().split(":::");
                    if (split.length > 1) {
                        packageDTO.setPackageStruct(split[0]);
                        packageDTO.setAnalysisSku(split[1]);
                        if (split.length > 2) {
                            packageDTO.setSpikeAnalysisSku(split[2]);
                        }
                    }
                    List<PackageDetailDTO> detailDTOList = packageDetailDTOMap.get(packageDTO.getPackageCode()).stream().filter(packageDetailDTO -> !packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
                    long skuTypeCount = packageDetailDTOMap.get(packageDTO.getPackageCode()).stream().filter(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).map(PackageDetailDTO::getSkuCode).distinct().count();
                    packageDTO.setSkuTypeQty((int) skuTypeCount);
                    BigDecimal packageSkuQty = packageDetailDTOMap.get(packageDTO.getPackageCode()).stream().filter(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).map(PackageDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    packageDTO.setPackageSkuQty(packageSkuQty);
                    //封装取消的数据
                    context.setPrePackageFail(true);

                    context.getDeletePackageDetailDTOList().addAll(detailDTOList);
                }
                newPackageDTOList.add(packageDTO);
                context.setPackageDTOList(newPackageDTOList);
            });

            // 失败了所有的预包核销都需要取消
            List<StockTransactionDTO> lotLocation = stockTransactionDTOList.stream()
                    .filter(stockTransactionDTO -> stockTransactionDTO.getStockLevel().equalsIgnoreCase(StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel()))
                    .collect(Collectors.toList());
            List<StockTransactionDTO> warehousePre = stockTransactionDTOList.stream()
                    .filter(stockTransactionDTO -> stockTransactionDTO.getStockLevel().equalsIgnoreCase(StockLevelEnum.LEVEL_STOCK.getLevel()))
                    .filter(stockTransactionDTO -> stockTransactionDTO.getRealGoods().equalsIgnoreCase(RealOrUnrealGoodsEnum.UNREAL.getCode()))
                    .collect(Collectors.toList());
            context.setPrePackageFailStockTransactionList(CollectionUtil.unionAll(lotLocation, warehousePre));
            usePrePackageFail(context);
        } else {//失败的逻辑
            stockOccupyFail(context);
            flushDB(context);
            throw new StageDoneException("一级库存占用失败");
        }

        cleanAllAbnormal(context);
    }

    // 使用预包失败
    private void usePrePackageFail(PretreatmentContext context) {
        if (context.isPrePackageFail()) {
            PretreatmentOperationBO operationBO = context.getOperationBO();
            operationBO.setPackageDTOList(packageDTOList(context));
            operationBO.setDeletePackageDetailDTOList(context.getDeletePackageDetailDTOList());
            context.setPackageChanged(true);
            cancelPrePackageStockOccupyStockTransaction(context);
        }
    }

    // 取消使用预包失败的核销
    private void cancelPrePackageStockOccupyStockTransaction(PretreatmentContext context) {
        StockTransactionCancelParam stockTransactionCancelParam = new StockTransactionCancelParam();
        stockTransactionCancelParam.setStockTransactionDTOList(context.getPrePackageFailStockTransactionList());
        stockTransactionBiz.cancelAllLevel(stockTransactionCancelParam);
    }

    // 一级库存占用失败
    private void stockOccupyFail(PretreatmentContext context) {

        saveStockOccupyFailAbnormal(context);

        cancelAllStockOccupyStockTransaction(context);

        // 出库单修改成预处理失败
        PretreatmentOperationBO operationBO = context.getOperationBO();
        ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTO(context);
        if (ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode().equals(shipmentOrderDTO.getStatus())) {
            ShipmentOrderLogDTO shipmentOrderLogDTO = new ShipmentOrderLogDTO();
            BeanUtil.copyProperties(shipmentOrderDTO, shipmentOrderLogDTO);
            shipmentOrderLogDTO.setOpRemark("");
            shipmentOrderLogDTO.setCreatedBy(shipmentOrderDTO.getUpdatedBy());
            shipmentOrderLogDTO.setOpBy(shipmentOrderDTO.getUpdatedBy());
            shipmentOrderLogDTO.setOpDate(System.currentTimeMillis());
            shipmentOrderLogDTO.setOpContent(String.format("预处理失败,单号:%s", shipmentOrderDTO.getShipmentOrderCode()));
            operationBO.setShipmentOrderLogDTO(shipmentOrderLogDTO);
        }

        shipmentOrderDTO.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_FAIL_STATUS.getCode());
        operationBO.setShipmentOrderDTO(shipmentOrderDTO);
        context.setShipmentChanged(true);
        List<PackageDTO> packageDTOList = packageDTOList(context);
        packageDTOList.forEach(packageDTO -> packageDTO.setStatus(PackEnum.STATUS.PRETREATMENT_FAIL.getCode()));
        operationBO.setPackageDTOList(packageDTOList);
        context.setPackageChanged(true);
    }

    // 取消所有的一级库存占用
    private void cancelAllStockOccupyStockTransaction(PretreatmentContext context) {
        StockTransactionCancelParam stockTransactionCancelParam = new StockTransactionCancelParam();
        stockTransactionCancelParam.setStockTransactionDTOList(context.getStockTransactionDTOList());
        stockTransactionBiz.cancelAllLevel(stockTransactionCancelParam);
    }

    // 保存一级库存占用失败的异常单
    private void saveStockOccupyFailAbnormal(PretreatmentContext context) {
        List<AbnormalOrderDTO> abnormalOrderDTOList = abnormalOrderDTOList(context).stream().filter(it -> it.getBusinessType().equals(AbnormalOrderEnum.bussinessTypeEnum.ONE_LEVEL_STOCK_TYPE.getCode())).collect(Collectors.toList());
        Map<String, AbnormalOrderDTO> abnormalOrderDTOMap = abnormalOrderDTOList.stream().collect(Collectors.toMap(AbnormalOrderDTO::getBillNo, Function.identity(), BinaryOperator.minBy(Comparator.comparing(AbnormalOrderDTO::getId))));
        Map<String, List<StockTransactionDTO>> stockTransactionDTOMap = context.getStockTransactionDTOList().stream().collect(Collectors.groupingBy(StockTransactionDTO::getBillNo));

        List<AbnormalOrderDTO> newAbnormalOrderDTOList = new ArrayList<>();
        stockTransactionDTOMap.forEach((key, value) -> {
            List<String> skuCodeList = value.stream().filter(
                    stockTransactionDTO -> stockTransactionDTO.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.FAIL.getCode())).map(StockTransactionDTO::getSkuCode).collect(Collectors.toList());
            PackageDTO packageDTO = context.getPackageDTOMap().get(key);
            if (CollectionUtil.isNotEmpty(skuCodeList)) {
                AbnormalOrderDTO abnormalOrderDTO = abnormalOrderDTOMap.get(key);
                if (!ObjectUtils.isEmpty(abnormalOrderDTO) && !AbnormalOrderEnum.statusEnum.CANCEL_STATUS.getValue().equals(abnormalOrderDTO.getStatus())) {
                    abnormalOrderDTO.setRetryCount(abnormalOrderDTO.getRetryCount() + 1);
                    abnormalOrderDTO.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
                    abnormalOrderDTO.setBusinessType(AbnormalOrderEnum.bussinessTypeEnum.ONE_LEVEL_STOCK_TYPE.getCode());
                } else {
                    abnormalOrderDTO = new AbnormalOrderDTO();
                    //订单的业务类型
                    abnormalOrderDTO.setOrderBusinessType(packageDTO.getBusinessType());
                    abnormalOrderDTO.setWarehouseCode(packageDTO.getWarehouseCode());
                    abnormalOrderDTO.setCargoCode(packageDTO.getCargoCode());
                    abnormalOrderDTO.setBillNo(packageDTO.getPackageCode());
                    abnormalOrderDTO.setPoNo(packageDTO.getPoNo());
                    abnormalOrderDTO.setSoNo(packageDTO.getSoNo());
                    abnormalOrderDTO.setCarrierCode(packageDTO.getCarrierCode());
                    abnormalOrderDTO.setBillType(AbnormalOrderEnum.typeEnum.PACKAGE.getValue());
                    abnormalOrderDTO.setBusinessType(AbnormalOrderEnum.bussinessTypeEnum.ONE_LEVEL_STOCK_TYPE.getCode());
                    abnormalOrderDTO.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
                    abnormalOrderDTO.setRetryCount(0);
                    abnormalOrderDTO.setErrorStatus("-500");
                }
                abnormalOrderDTO.setErrorMsg(String.format("商品%s库存不足！", String.join(",", skuCodeList)));
                newAbnormalOrderDTOList.add(abnormalOrderDTO);
            } else {
                AbnormalOrderDTO abnormalOrderDTO = abnormalOrderDTOMap.get(key);
                if (!ObjectUtils.isEmpty(abnormalOrderDTO) && !AbnormalOrderEnum.statusEnum.CANCEL_STATUS.getValue().equals(abnormalOrderDTO.getStatus())) {
                    abnormalOrderDTO.setRetryCount(abnormalOrderDTO.getRetryCount() + 1);
                    newAbnormalOrderDTOList.add(abnormalOrderDTO);
                }
            }
        });
        PretreatmentOperationBO operationBO = context.getOperationBO();
        operationBO.setAbnormalOrderDTOList(newAbnormalOrderDTOList);
    }

    // 获取面单
    protected void machExpress(PretreatmentContext context) {
        log.info("处理包裹拉取面单，单据：{} ", JSONUtil.toJsonStr(context.getShipmentOrderCode()));
        // 查询出库单
        ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTO(context);

        // 承运商
        CarrierDTO carrier = carrierDTO(context);

        // 查询所有包裹
        List<PackageDTO> packageDTOList = packageDTOList(context);

        List<PackageDetailDTO> packageDetailDTOList = packageDetailDTOList(context);

        // 组装包裹结构
        packageDTOList.forEach(packageDTO -> {
            List<PackageDetailDTO> packageDetailDTOListInPack = packageDetailDTOList.stream().filter(packageDetailDTO -> packageDetailDTO.getPackageCode().equals(packageDTO.getPackageCode())).collect(Collectors.toList());
            packageDTO.setListDetail(packageDetailDTOListInPack);
        });

        // 手工单生成运单序号
        int subExpressSerial = 0;

        // 运单获取
        for (PackageDTO packageDTO : packageDTOList) {
            if (StrUtil.isNotBlank(packageDTO.getExpressNo())) {
                continue;
            }
            //查询异常单

            //快递公司存在、接口拉取、没有运单号
            if (!ObjectUtils.isEmpty(carrier) && IsHttpEnum.ENABLE.getValue().equals(carrier.getIsHttp())) {
                if (StrUtil.isNotBlank(shipmentOrderDTO.getExpressNo())
                        && packageDTOList.stream().noneMatch(it -> StrUtil.equalsIgnoreCase(shipmentOrderDTO.getExpressNo(), it.getExpressNo()))) {
                    packageDTO.setExpressNo(shipmentOrderDTO.getExpressNo());
                }
                //获取运单
                ExpressParam expressParam;
                ExpressResultDTO expressResult = null;
                try {
                    BigDecimal weight = weightBiz.weight(MaterialCalculateParam.builder()
                            .warehouseCode(packageDTO.getWarehouseCode())
                            .cargoCode(packageDTO.getCargoCode())
                            .is4PL(OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.FOUR_TAG) ? Is4PLEnum.YES : Is4PLEnum.NO)
                            .skuDetailList(packageDTO.getListDetail().stream()
                                    .filter(it -> it.getIsPre().equals(PackIsPreEnum.NORMAL.getCode()))
                                    .map(it -> MaterialCalculateParam.SkuDetail.builder()
                                            .skuCode(it.getSkuCode())
                                            .quantity(it.getSkuQty())
                                            .build()).collect(Collectors.toList()))
                            .build());

                    wmsTenantHelper.setTenantId(packageDTO.getWarehouseCode(), packageDTO.getCargoCode());
                    expressParam = remoteExpressClient.getExpressParamV2(shipmentOrderDTO, carrier, packageDTO, packageDTO.getListDetail(), weight);
                    expressResult = remoteExpressClient.getExpressResult(packageDTO.getShipmentOrderCode(), expressParam);
                } catch (Exception e) {
                    log.error(e.getMessage());
                    context.getExpressFailInfo().put(packageDTO.getPackageCode(), e.getMessage());
                }
                if (!ObjectUtils.isEmpty(expressResult)) {
                    List<ExpressResultDTO.WaybillApplyInfo> waybillApplyInfoList = expressResult.getWaybill_apply_info();

                    // 获取快递信息失败暂存具体错误信息
                    if (!expressResult.getSuccess()) {
                        context.getExpressFailInfo().put(packageDTO.getPackageCode(), expressResult.getMessage());
                    }

                    if (null == waybillApplyInfoList) {
                        packageDTO.setExpressNo(null);
                        continue;
                    }

                    ExpressResultDTO.WaybillApplyInfo waybillApplyInfo = waybillApplyInfoList.get(0);
                    //出库单
                    if (StrUtil.isBlank(waybillApplyInfo.getWaybill_code())) {
                        packageDTO.setExpressNo(null);
                        continue;
                    }
                    String expressNo = waybillApplyInfo.getWaybill_code();
                    //包裹
                    packageDTO.setExpressNo(expressNo);
                    packageDTO.setExpressBranch(waybillApplyInfo.getExpressBranchCode());
                    packageDTO.setExpressBranchName(waybillApplyInfo.getExpressBranchName());
                } else {
                    packageDTO.setExpressNo("");
                }
            } else if (null != carrier && IsHttpEnum.DISABLE.getValue().equals(carrier.getIsHttp())) {
                // 手工单运单按照原始订单生成
                if (StrUtil.isNotBlank(shipmentOrderDTO.getExpressNo())) {
                    if (subExpressSerial == 0) {
                        packageDTO.setExpressNo(shipmentOrderDTO.getExpressNo());
                    } else {
                        String expressNo = shipmentOrderDTO.getExpressNo() + "-" + packageDTO.getPackageCode().substring(packageDTO.getPackageCode().length() - 4);
                        packageDTO.setExpressNo(expressNo);
                    }
                    subExpressSerial++;
                }
            }
        }

        saveMachExpressAbnormal(context);

        if (packageDTOList(context).stream().allMatch(packageDTO -> StrUtil.isNotBlank(packageDTO.getExpressNo()))) {
            cleanAllAbnormal(context);
        }

        boolean packageChanged = false;
        // 维护包裹状态
        for (PackageDTO packageDTO : packageDTOList) {
            // 创建状态没有运单 修改状态成预处理失败
            if (PackEnum.STATUS.CREATE_STATUS.getCode().equalsIgnoreCase(packageDTO.getStatus()) && StrUtil.isBlank(packageDTO.getExpressNo())) {
                packageDTO.setStatus(PackEnum.STATUS.PRETREATMENT_FAIL.getCode());
                packageDTO.setPretreatmentStatus(PretreatmentStatusEnum.STOCK_ZONE_FAIL.getStatus());
                packageChanged = true;
            }
            // 创建状态或者预处理失败状态 有了运单号 修改状态成预处理完成
            if (PackEnum.STATUS.CREATE_STATUS.getCode().equalsIgnoreCase(packageDTO.getStatus()) || PackEnum.STATUS.PRETREATMENT_FAIL.getCode().equalsIgnoreCase(packageDTO.getStatus())) {
                if (StrUtil.isNotBlank(packageDTO.getExpressNo())) {
                    packageChanged = true;
                    // 根据出错的sku数量更新包裹状态
                    packageDTO.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
                    packageDTO.setPretreatmentStatus(PretreatmentStatusEnum.STOCK_ZONE_SUCCESS.getStatus());
                    //包裹预配
                    packageDetailDTOList.forEach(packageDetailDTO -> {
                        packageDetailDTO.setExpQty(packageDetailDTO.getSkuQty());
                        packageDetailDTO.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
                    });

                    //包裹日志
                    PackageLogDTO packageLogDTO = new PackageLogDTO();
                    packageLogDTO.setWarehouseCode(packageDTO.getWarehouseCode());
                    packageLogDTO.setCargoCode(packageDTO.getCargoCode());
                    packageLogDTO.setPackageCode(packageDTO.getPackageCode());
                    packageLogDTO.setOpBy(packageDTO.getCreatedBy());
                    packageLogDTO.setCreatedBy(packageDTO.getCreatedBy());
                    packageLogDTO.setOpDate(System.currentTimeMillis());
                    packageLogDTO.setOpContent(String.format("预处理完成,单号:%s", packageDTO.getPackageCode()));
                    context.getPackageLogDTOList().add(packageLogDTO);

                    log.info("wmsPackHandleSuccess:{}", packageDTO.getWarehouseCode() + "#" + packageDTO.getShipmentOrderCode());
                }
            }
        }

        if (packageChanged) {
            context.getOperationBO().setPackageDTOList(packageDTOList(context));
            context.getOperationBO().setPackageDetailDTOList(packageDetailDTOList(context));
        }

        // 存在包裹没有运单、出库单状态为创建 修改状态成预处理失败
        if (ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode().equals(shipmentOrderDTO.getStatus())
                && packageDTOList(context).stream().anyMatch(packageDTO -> StrUtil.isBlank(packageDTO.getExpressNo()))) {
            shipmentOrderDTO.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_FAIL_STATUS.getCode());
            PretreatmentOperationBO operationBO = context.getOperationBO();
            operationBO.setShipmentOrderDTO(shipmentOrderDTO);

            ShipmentOrderLogDTO shipmentOrderLogDTO = new ShipmentOrderLogDTO();
            BeanUtil.copyProperties(shipmentOrderDTO, shipmentOrderLogDTO);
            shipmentOrderLogDTO.setOpRemark("");
            shipmentOrderLogDTO.setCreatedBy(shipmentOrderDTO.getUpdatedBy());
            shipmentOrderLogDTO.setOpBy(shipmentOrderDTO.getUpdatedBy());
            shipmentOrderLogDTO.setOpDate(System.currentTimeMillis());
            shipmentOrderLogDTO.setOpContent(String.format("预处理失败,单号:%s", shipmentOrderDTO.getShipmentOrderCode()));
            operationBO.setShipmentOrderLogDTO(shipmentOrderLogDTO);
        }

        // 出库单状态为创建或者预处理失败、所有包裹都有运单号 修改状态成预处理成功
        if (ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode().equals(shipmentOrderDTO.getStatus())
                || ShipmentOrderEnum.STATUS.PREPARE_HANDLER_FAIL_STATUS.getCode().equals(shipmentOrderDTO.getStatus())) {
            if (CollectionUtil.isNotEmpty(packageDTOList) && packageDTOList.stream().allMatch(packageDTO -> StrUtil.isNotBlank(packageDTO.getExpressNo()))) {
                shipmentOrderDTO.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
                shipmentOrderDTO.setPretreatmentStatus(PretreatmentStatusEnum.STOCK_ZONE_SUCCESS.getStatus());

                ShipmentOrderLogDTO shipmentOrderLogDTO = new ShipmentOrderLogDTO();
                BeanUtil.copyProperties(shipmentOrderDTO, shipmentOrderLogDTO);
                shipmentOrderLogDTO.setOpRemark("");
                shipmentOrderLogDTO.setCreatedBy(packageDTOList.get(0).getCreatedBy());
                shipmentOrderLogDTO.setOpBy(packageDTOList.get(0).getCreatedBy());
                shipmentOrderLogDTO.setOpDate(System.currentTimeMillis());
                shipmentOrderLogDTO.setOpContent(String.format("预处理完成,单号:%s", shipmentOrderDTO.getShipmentOrderCode()));

                List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = shipmentOrderDetailDTOList(context);
                shipmentOrderDetailDTOList.forEach(shipmentOrderDetailDTO -> {
                    shipmentOrderDetailDTO.setExpQty(shipmentOrderDetailDTO.getExpSkuQty());
                    shipmentOrderDetailDTO.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
                });

                PretreatmentOperationBO operationBO = context.getOperationBO();
                operationBO.setShipmentOrderDTO(shipmentOrderDTO);
                operationBO.setShipmentOrderLogDTO(shipmentOrderLogDTO);
                operationBO.setShipmentOrderDetailDTOList(shipmentOrderDetailDTOList);
            }
        }

        flushDB(context);
    }

    // 保存异常单
    private void saveMachExpressAbnormal(PretreatmentContext context) {
        if (packageDTOList(context).stream().anyMatch(packageDTO -> StrUtil.isBlank(packageDTO.getExpressNo()))) {
            List<AbnormalOrderDTO> abnormalOrderDTOList = new ArrayList<>();
            ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTO(context);
            List<AbnormalOrderDTO> machExpressAbnormal = abnormalOrderDTOList(context);
            for (PackageDTO packageDTO : packageDTOList(context)) {
                if (StrUtil.isBlank(packageDTO.getExpressNo())) {
                    AbnormalOrderDTO abnormalOrderDTO = machExpressAbnormal.stream().filter(it -> it.getBillNo().equals(packageDTO.getPackageCode()))
                            .findFirst().orElse(null);
                    if (abnormalOrderDTO != null) {
                        abnormalOrderDTO.setRetryCount(abnormalOrderDTO.getRetryCount() + 1);
                        abnormalOrderDTO.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
                    } else {
                        abnormalOrderDTO = new AbnormalOrderDTO();
                        //订单的业务类型
                        abnormalOrderDTO.setOrderBusinessType(shipmentOrderDTO.getBusinessType());
                        abnormalOrderDTO.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
                        abnormalOrderDTO.setCargoCode(shipmentOrderDTO.getCargoCode());
                        abnormalOrderDTO.setBillNo(packageDTO.getPackageCode());
                        abnormalOrderDTO.setPoNo(shipmentOrderDTO.getPoNo());
                        abnormalOrderDTO.setSoNo(shipmentOrderDTO.getSoNo());
                        abnormalOrderDTO.setCarrierCode(shipmentOrderDTO.getCarrierCode());
                        abnormalOrderDTO.setBillType(AbnormalOrderEnum.typeEnum.PACKAGE.getValue());
                        abnormalOrderDTO.setBusinessType(AbnormalOrderEnum.bussinessTypeEnum.FETCH_FACE_TYPE.getCode());
                        abnormalOrderDTO.setStatus(AbnormalOrderEnum.statusEnum.FAIL_STATUS.getValue());
                        abnormalOrderDTO.setRetryCount(0);
                        abnormalOrderDTO.setErrorStatus("-500");
                    }
                    abnormalOrderDTO.setErrorMsg(String.format("包裹%s获取运单号失败！", abnormalOrderDTO.getBillNo()));
                    String failInfo = context.getExpressFailInfo().get(abnormalOrderDTO.getBillNo());
                    //具体的错误信息
                    if (StrUtil.isNotBlank(failInfo)) {
                        abnormalOrderDTO.setErrorMsg(failInfo);
                    }
                    abnormalOrderDTOList.add(abnormalOrderDTO);
                }
            }
            PretreatmentOperationBO operationBO = context.getOperationBO();
            operationBO.setAbnormalOrderDTOList(abnormalOrderDTOList);
        }
    }

    // 清理异常单
    private void cleanAllAbnormal(PretreatmentContext context) {
        if (CollectionUtil.isNotEmpty(abnormalOrderDTOList(context))) {
            for (AbnormalOrderDTO abnormalOrderDTO : abnormalOrderDTOList(context)) {
                abnormalOrderDTO.setStatus(AbnormalOrderEnum.statusEnum.SUCCESS_STATUS.getValue());
            }
            context.getOperationBO().setAbnormalOrderDTOList(abnormalOrderDTOList(context));
        }
    }

    private List<String> packageCodeList(PretreatmentContext context) {
        if (CollectionUtil.isNotEmpty(context.getPackageCodeList())) {
            return context.getPackageCodeList();
        }
        if (CollectionUtil.isNotEmpty(packageDTOList(context))) {
            return packageDTOList(context).stream().map(PackageDTO::getPackageCode).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private List<OrderInterceptDTO> orderInterceptDTOList(PretreatmentContext context) {
        if (CollectionUtil.isNotEmpty(context.getOrderInterceptDTOList())) {
            return context.getOrderInterceptDTOList();
        }
        OrderInterceptParam orderInterceptParam = new OrderInterceptParam();
        orderInterceptParam.setShipmentOrderCode(shipmentOrderCode(context));
        List<OrderInterceptDTO> orderInterceptDTOList = remoteOrderInterceptClient.getList(orderInterceptParam);
        if (CollectionUtil.isNotEmpty(orderInterceptDTOList)) {
            context.setOrderInterceptDTOList(orderInterceptDTOList);
        }
        return context.getOrderInterceptDTOList();
    }

    /**
     * 功能描述:  维护包裹结构
     * 创建时间:  2021/9/10 1:15 下午
     */
    private void packageStructure(List<PackageDTO> packageDTOList) {
        // // 维护包裹结构
        // 计算包裹结构 -- 后续需要使用
        packageDTOList.forEach(packageDTO -> {
            Map<String, BigDecimal> result;
            List<String> analysisSkuList = new ArrayList<>();
            List<String> spikeAnalysisSkuList = new ArrayList<>();
            if (packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode())) {
                result = packageDTO.getListDetail().stream().filter(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode()))
                        .collect(Collectors.groupingBy(PackageDetailDTO::getSkuCode,
                                Collectors.mapping(PackageDetailDTO::getSkuQty,
                                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                packageDTO.getListDetail().stream().filter(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).forEach(packageDetailDTO -> {
                    String packAnalysisStr = String.format("%s,%s", packageDetailDTO.getSkuCode(), packageDetailDTO.getSkuQty().setScale(0, RoundingMode.FLOOR));
                    analysisSkuList.add(packAnalysisStr);
                });
                //TODO SPIKE 秒杀---商品,批次,外部批次,失效日期,禁售比对日期,残次等级,数量
                packageDTO.getListDetail().stream()
//                        .filter(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode()))
                        .forEach(packageDetailDTO -> {
                            String packSpikeAnalysisStr = String.format("%s#%s#%s#%s#%s#%s#%s%s%s",
                                    packageDetailDTO.getSkuCode(),
                                    packageDetailDTO.getSkuLotNo(),
                                    packageDetailDTO.getExternalSkuLotNo(),
                                    packageDetailDTO.getExpireDate(),
                                    packageDetailDTO.getExpireDateStart(),
                                    packageDetailDTO.getExpireDateEnd(),
                                    packageDetailDTO.getWithdrawCompareDate(),
                                    packageDetailDTO.getInventoryType(),
                                    packageDetailDTO.getSkuQty().setScale(0, RoundingMode.FLOOR));
                            spikeAnalysisSkuList.add(packSpikeAnalysisStr);
                        });
            } else {
                result = packageDTO.getListDetail().stream()
                        .collect(Collectors.groupingBy(PackageDetailDTO::getSkuCode,
                                Collectors.mapping(PackageDetailDTO::getSkuQty,
                                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                packageDTO.getListDetail().forEach(packageDetailDTO -> {
                    String packAnalysisStr = String.format("%s,%s", packageDetailDTO.getSkuCode(), packageDetailDTO.getSkuQty().setScale(0, RoundingMode.FLOOR));
                    analysisSkuList.add(packAnalysisStr);
                });
                //TODO SPIKE 秒杀---商品,批次,外部批次,失效日期,禁售比对日期,残次等级,数量
                packageDTO.getListDetail().forEach(packageDetailDTO -> {
                    String packSpikeAnalysisStr = String.format("%s#%s#%s#%s#%s#%s#%s%s%s",
                            packageDetailDTO.getSkuCode(),
                            packageDetailDTO.getSkuLotNo(),
                            packageDetailDTO.getExternalSkuLotNo(),
                            packageDetailDTO.getExpireDate(),
                            packageDetailDTO.getExpireDateStart(),
                            packageDetailDTO.getExpireDateEnd(),
                            packageDetailDTO.getWithdrawCompareDate(),
                            packageDetailDTO.getInventoryType(),
                            packageDetailDTO.getSkuQty().setScale(0, RoundingMode.FLOOR));
                    spikeAnalysisSkuList.add(packSpikeAnalysisStr);
                });
            }
            if (!CollectionUtils.isEmpty(spikeAnalysisSkuList)) {
                packageDTO.setSpikeAnalysisSku(SecureUtil.md5(spikeAnalysisSkuList.stream().sorted().collect(Collectors.joining(";"))));
            }
            if (result.size() > 1) {
                if (result.entrySet().stream().anyMatch(s -> s.getValue().doubleValue() > 1)) {
                    packageDTO.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.MORE_MORE_ORDER_SKU_TYPE.getCode());
                } else {
                    packageDTO.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.MORE_ONE_ORDER_SKU_TYPE.getCode());
                }
            } else {
                if (result.entrySet().stream().anyMatch(s -> s.getValue().doubleValue() > 1)) {
                    packageDTO.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.ONE_MORE_ORDER_SKU_TYPE.getCode());
                } else {
                    packageDTO.setPackageStruct(ShipmentOrderEnum.PACKAGE_STRUCT.ONE_ONE_ORDER_SKU_TYPE.getCode());
                }
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(analysisSkuList)) {
                if (ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString().equals(packageDTO.getBusinessType())) {
                    String analysisSku = analysisSkuList.stream().sorted().collect(Collectors.joining(";"));
                    if (analysisSku.length() > 250) {
                        packageDTO.setAnalysisSku(SecureUtil.md5(analysisSku));
                    } else {
                        packageDTO.setAnalysisSku(analysisSku);
                    }
                } else {
                    String analysisSku = analysisSkuList.stream().sorted().collect(Collectors.joining(";"));
                    packageDTO.setAnalysisSku(SecureUtil.md5(analysisSku));
                }
            }
            //把原来的值 放到备注字段上
            packageDTO.setRemark(String.join(":::", packageDTO.getPackageStruct(), packageDTO.getAnalysisSku(), packageDTO.getSpikeAnalysisSku()));
        });
    }

    /**
     * 校验出库单明细中商品和包裹中的商品
     */
    private boolean checkSkuDetailInOrderAndPackage(ShipmentOrderDTO shipmentOrderDTO, List<PackageDTO> allPackageDTOList) {
        if (ObjectUtil.isEmpty(shipmentOrderDTO)) {
            log.warn("出库单不能为空");
            return false;
        }
        if (CollectionUtil.isEmpty(shipmentOrderDTO.getListShipmentOrderDetailDTO())) {
            log.warn("出库单明细不能为空");
            return false;
        }
        if (CollectionUtil.isEmpty(allPackageDTOList)) {
            log.warn("包裹不能为空");
            return false;
        }
        for (PackageDTO packageDTO : allPackageDTOList) {
            if (StrUtil.equalsIgnoreCase(packageDTO.getStatus(), PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode())) {
                continue;
            }
            if (CollectionUtil.isEmpty(packageDTO.getListDetail())) {
                log.warn("包裹明细不能为空");
                return false;
            }
        }
        // 过滤掉已取消的包裹
        List<PackageDTO> avlPackageList = allPackageDTOList.stream()
                .filter(it -> !StrUtil.equalsIgnoreCase(it.getStatus(), PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode()))
                .collect(Collectors.toList());

        // 出库单计划商品数量和包裹商品数量
        BigDecimal packageTotalSkuQty = avlPackageList.stream().map(PackageDTO::getPackageSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (shipmentOrderDTO.getSkuQty().compareTo(packageTotalSkuQty) != 0) {
            log.warn("出库单商品数量与包裹商品数量不一致");
            return false;
        }

        // 出库单数量商品数量与出库单明细商品数量是否一致
        List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = shipmentOrderDTO.getListShipmentOrderDetailDTO();
        BigDecimal shipDetailTotalSkuQty = shipmentOrderDetailDTOList.stream().map(ShipmentOrderDetailDTO::getExpSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (shipmentOrderDTO.getSkuQty().compareTo(shipDetailTotalSkuQty) != 0) {
            log.warn("出库单商品数量与其明细中的商品数量总数不一致");
            return false;
        }

        // 包裹明细数量是否与包裹计划数量一致
        for (PackageDTO packageDTO : avlPackageList) {
            BigDecimal reduce = packageDTO.getListDetail().stream().map(PackageDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (packageDTO.getPackageSkuQty().compareTo(reduce) != 0) {
                log.warn("包裹商品数量与包裹明细商品数量不一致");
            }
        }

        // 明细商品是否一致
        List<PackageDetailDTO> packageDetailDTOList = avlPackageList.stream().flatMap(it -> it.getListDetail().stream()).collect(Collectors.toList());
        List<String> skuCodeList = shipmentOrderDetailDTOList.stream().map(ShipmentOrderDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        for (String skuCode : skuCodeList) {
            // 正品
            BigDecimal skuAvlTotalInShipDetail = shipmentOrderDetailDTOList.stream()
                    .filter(it -> StrUtil.equalsIgnoreCase(it.getSkuCode(), skuCode))
                    .filter(it -> StrUtil.equalsIgnoreCase(it.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel()))
                    .map(ShipmentOrderDetailDTO::getExpSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal skuAvlTotalInPackDetail = packageDetailDTOList.stream()
                    .filter(it -> StrUtil.equalsIgnoreCase(it.getSkuCode(), skuCode))
                    .filter(it -> StrUtil.equalsIgnoreCase(it.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel()))
                    .map(PackageDetailDTO::getSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (skuAvlTotalInPackDetail.compareTo(skuAvlTotalInShipDetail) != 0) {
                log.warn("正品商品{}出库单明细数量与包裹明细数量不一致", skuCode);
                return false;
            }
            // 正品
            BigDecimal skuDamageTotalInShipDetail = shipmentOrderDetailDTOList.stream()
                    .filter(it -> StrUtil.equalsIgnoreCase(it.getSkuCode(), skuCode))
                    .filter(it -> StrUtil.equalsIgnoreCase(it.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel()))
                    .map(ShipmentOrderDetailDTO::getExpSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal skuDamageTotalInPackDetail = packageDetailDTOList.stream()
                    .filter(it -> StrUtil.equalsIgnoreCase(it.getSkuCode(), skuCode))
                    .filter(it -> StrUtil.equalsIgnoreCase(it.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel()))
                    .map(PackageDetailDTO::getSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (skuDamageTotalInShipDetail.compareTo(skuDamageTotalInPackDetail) != 0) {
                log.warn("次品商品{}出库单明细数量与包裹明细数量不一致", skuCode);
                return false;
            }
        }

        return true;
    }

    /**
     * 是否检验溯源码
     */
    private Boolean isCheckSource(String cargoCode) {
        CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey("", cargoCode, CargoConfigParamEnum.SOURCE_CODE.getCode());
        if (cargoConfigDTO != null &&
                cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue())
                && Objects.equals(cargoConfigDTO.getPropValue(), "1")) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 多预包分析
     */
    private List<PackageDetailDTO> analyzeMultiPackageDetails(String cargoCode, List<PackageDetailDTO> detailDTOList) {
        MultiPreDefParam multiPreDefParam = new MultiPreDefParam();
        multiPreDefParam.setCargoCode(cargoCode);
        multiPreDefParam.setStatus(MultiPreEnableStatusEnum.ENABLE.getType());
        List<MultiPreDefDTO> multiPreDefDTOList = remoteMultiPreDefClient.getList(multiPreDefParam);
        if (CollectionUtil.isEmpty(multiPreDefDTOList)) return null;

        List<PackageDetailDTO> mergedDetailList = merge(detailDTOList);

        Map<String, List<MultiPreDefDTO>> collect = multiPreDefDTOList.stream().collect(Collectors.groupingBy(MultiPreDefDTO::getMultiPreDefCode));

        // 需要过滤多预包解析是启用但是里面的预包是禁用的数据
        List<String> preSkuCodeList = multiPreDefDTOList.stream().filter(multiPreDefDTO -> multiPreDefDTO.getType().equalsIgnoreCase(MultiPreTypeEnum.PRE.getType()))
                .map(MultiPreDefDTO::getSkuCode)
                .collect(Collectors.toList());
        PrePackageSkuParam prePackageSkuParam = new PrePackageSkuParam();
        prePackageSkuParam.setCargoCode(cargoCode);
        prePackageSkuParam.setPreUpcCodeList(preSkuCodeList);
        List<PrePackageSkuDTO> prePackageSkuDTOList = remotePrePackageSkuClient.getList(prePackageSkuParam);
        if (CollectionUtil.isEmpty(prePackageSkuDTOList)) return null;

        int matchCount = 0;
        String candidate = StrUtil.EMPTY;
        for (String multiPreDefCode : collect.keySet()) {
            List<MultiPreDefDTO> multiPreDefDTOS = collect.get(multiPreDefCode);
            if (multiPreDefDTOS.stream()
                    .filter(multiPreDefDTO -> multiPreDefDTO.getType().equalsIgnoreCase(MultiPreTypeEnum.PRE.getType()))
                    .anyMatch(multiPreDefDTO -> prePackageSkuDTOList.stream()
                            .filter(prePackageSkuDTO -> prePackageSkuDTO.getPreUpcCode().equalsIgnoreCase(multiPreDefDTO.getSkuCode()))
                            .anyMatch(prePackageSkuDTO -> prePackageSkuDTO.getStatus().equalsIgnoreCase(PrePackageEnableEnum.DISABLE.getCode())))) {
                continue;
            }
            boolean satisfy = checkSatisfy(multiPreDefDTOS, mergedDetailList);
            if (satisfy) matchCount++;
            if (satisfy) candidate = multiPreDefCode;
        }

        if (matchCount > 1) throw MULTI_PRE_SATISFY_EXCEPTION;
        return useMultiPreDetailList(mergedDetailList, collect.get(candidate));
    }

    /**
     * 使用多预包的包裹明细结果
     */
    private List<PackageDetailDTO> useMultiPreDetailList(List<PackageDetailDTO> packageDetailDTOList, List<MultiPreDefDTO> multiPreDefDTOList) {
        if (CollectionUtil.isEmpty(packageDetailDTOList)) return null;
        if (CollectionUtil.isEmpty(multiPreDefDTOList)) return null;
        // 使用的多预包个数
        long finalCount = Long.MAX_VALUE;
        for (MultiPreDefDTO multiPreDefDTO : multiPreDefDTOList) {
            if (!MultiPreTypeEnum.DETAIL.getType().equalsIgnoreCase(multiPreDefDTO.getType())) continue;
            PackageDetailDTO packageDetailDTO = packageDetailDTOList.stream()
                    .filter(it -> it.getSkuCode().equalsIgnoreCase(multiPreDefDTO.getSkuCode()))
                    .findFirst().orElseThrow(ExceptionUtil::dataError);
            int count = packageDetailDTO.getSkuQty().divide(multiPreDefDTO.getQty(), RoundingMode.DOWN).intValue();
            if (count < finalCount) finalCount = count;
        }
        if (finalCount <= 0) return null;

        // 增加预包维度
        return multiPreDetailList(packageDetailDTOList, multiPreDefDTOList, BigDecimal.valueOf(finalCount));
    }

    /**
     * @param packageDetailDTOList 原始包裹明细
     * @param multiPreDefDTOList   多预包配置数据
     * @param finalCount           最终使用的预包数量
     */
    private List<PackageDetailDTO> multiPreDetailList(List<PackageDetailDTO> packageDetailDTOList, List<MultiPreDefDTO> multiPreDefDTOList, BigDecimal finalCount) {
        List<PackageDetailDTO> finalDetailList = new ArrayList<>();
        //解析预包结构，返回明细
        String lineSeq = packageDetailDTOList.stream().map(PackageDetailDTO::getLineSeq).max(String::compareTo).orElse("0");
        int num = 0;

        //计算剩余明细
        for (PackageDetailDTO packageDetailDTO : packageDetailDTOList) {
            num = ++num;
            MultiPreDefDTO multiPreDefDTO = multiPreDefDTOList.stream()
                    .filter(it -> it.getSkuCode().equalsIgnoreCase(packageDetailDTO.getSkuCode()))
                    .filter(it -> MultiPreTypeEnum.DETAIL.getType().equalsIgnoreCase(it.getType()))
                    .findFirst().orElse(null);
            packageDetailDTO.setId(null);
            packageDetailDTO.setPUid(null);
            packageDetailDTO.setVersion(null);
            packageDetailDTO.setUpdatedTime(null);
            if (ObjectUtils.isEmpty(multiPreDefDTO)) {
                PackageDetailDTO detailDTO = new PackageDetailDTO();
                BeanUtils.copyProperties(packageDetailDTO, detailDTO);
                detailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
                detailDTO.setPackUid(String.valueOf(UUID.randomUUID()));
                detailDTO.setIsPre(PackIsPreEnum.LAST.getCode());
                finalDetailList.add(detailDTO);
                continue;
            }
            PackageDetailDTO detailDTO = new PackageDetailDTO();
            BeanUtils.copyProperties(packageDetailDTO, detailDTO);
            BigDecimal multiply = multiPreDefDTO.getQty().multiply(finalCount);
            BigDecimal skuQty = detailDTO.getSkuQty().subtract(multiply);
            if (skuQty.compareTo(BigDecimal.ZERO) > 0) {
                detailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
                detailDTO.setPackUid(String.valueOf(UUID.randomUUID()));
                detailDTO.setSkuQty(skuQty);
                detailDTO.setIsPre(PackIsPreEnum.LAST.getCode());
                finalDetailList.add(detailDTO);
            }
            //计算预包对应的明细
            PackageDetailDTO lastDetailDTO = new PackageDetailDTO();
            BeanUtils.copyProperties(packageDetailDTO, lastDetailDTO);
            lastDetailDTO.setPackUid(String.valueOf(UUID.randomUUID()));
            lastDetailDTO.setSkuQty(multiply);
            lastDetailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
            lastDetailDTO.setIsPre(PackIsPreEnum.PRE_CHILD.getCode());
            finalDetailList.add(lastDetailDTO);
        }

        List<String> prePackageSkuCodeList = multiPreDefDTOList.stream()
                .filter(it -> MultiPreTypeEnum.PRE.getType().equalsIgnoreCase(it.getType()))
                .map(MultiPreDefDTO::getSkuCode)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(prePackageSkuCodeList)) return null;
        PrePackageSkuParam prePackageSkuParam = new PrePackageSkuParam();
        prePackageSkuParam.setCargoCode(packageDetailDTOList.get(0).getCargoCode());
        prePackageSkuParam.setPreUpcCodeList(prePackageSkuCodeList);
        List<PrePackageSkuDTO> list = remotePrePackageSkuClient.getList(prePackageSkuParam);
        if (CollectionUtil.isEmpty(list)) return null;

        for (PrePackageSkuDTO prePackageSkuDTO : list) {
            MultiPreDefDTO multiPreDefDTO = multiPreDefDTOList.stream()
                    .filter(it -> it.getSkuCode().equalsIgnoreCase(prePackageSkuDTO.getPreUpcCode()))
                    .filter(it -> it.getType().equalsIgnoreCase(MultiPreTypeEnum.PRE.getType()))
                    .findFirst().orElseThrow(ExceptionUtil::dataError);
            //计算预包条码的明细
            PackageDetailDTO packageDetailDTO = new PackageDetailDTO();
            BeanUtils.copyProperties(packageDetailDTOList.get(0), packageDetailDTO);
            packageDetailDTO.setSkuName(prePackageSkuDTO.getPreUpcName());
            packageDetailDTO.setId(null);
            packageDetailDTO.setPUid(null);
            packageDetailDTO.setVersion(null);
            packageDetailDTO.setUpdatedTime(null);
            packageDetailDTO.setUpdatedBy("");
            packageDetailDTO.setIsPre(PackIsPreEnum.PRE.getCode());
            packageDetailDTO.setSkuQty(finalCount.multiply(multiPreDefDTO.getQty()));
            packageDetailDTO.setSkuCode(prePackageSkuDTO.getPreUpcCode());
            packageDetailDTO.setUpcCode(prePackageSkuDTO.getPreUpcCode());
            //生成明细ID
            packageDetailDTO.setPackUid(UUID.randomUUID().toString());
            packageDetailDTO.setExpQty(BigDecimal.ZERO);
            num = num + 1;
            packageDetailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
            packageDetailDTO.setStatus(PackEnum.STATUS.CREATE_STATUS.getCode());
            packageDetailDTO.setZoneType(ZoneTypeEnum.ZONE_TYPE_PICK.getType());
            finalDetailList.add(packageDetailDTO);
        }
        return finalDetailList;
    }

    /**
     * 判断单签多预包解析是否满足当前包裹
     */
    private boolean checkSatisfy(List<MultiPreDefDTO> multiPreDefDTOList, List<PackageDetailDTO> packageDetailDTOList) {
        if (multiPreDefDTOList.stream().noneMatch(multiPreDefDTO -> multiPreDefDTO.getType().equalsIgnoreCase(MultiPreTypeEnum.DETAIL.getType()))) {
            return false;
        }
        for (MultiPreDefDTO multiPreDefDTO : multiPreDefDTOList) {
            if (!multiPreDefDTO.getType().equals(MultiPreTypeEnum.DETAIL.getType())) {
                continue;
            }

            Optional<PackageDetailDTO> first = packageDetailDTOList.stream().filter(packageDetailDTO -> packageDetailDTO.getSkuCode().equalsIgnoreCase(multiPreDefDTO.getSkuCode()))
                    .findFirst();
            if (!first.isPresent()) return false;
            PackageDetailDTO packageDetailDTO = first.get();
            if (multiPreDefDTO.getQty().compareTo(packageDetailDTO.getSkuQty()) > 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 合并包裹明细
     */
    private List<PackageDetailDTO> merge(List<PackageDetailDTO> packageDetailDTOList) {
        Map<String, PackageDetailDTO> packageDetailDTOMap = new HashMap<>();
        for (PackageDetailDTO packageDetailDTO : packageDetailDTOList) {
            packageDetailDTOMap.computeIfPresent(packageDetailDTO.getSkuCode(), (k, v) -> {
                v.setSkuQty(v.getSkuQty().add(packageDetailDTO.getSkuQty()));
                return v;
            });
            packageDetailDTOMap.computeIfAbsent(packageDetailDTO.getSkuCode(), (k) -> {
                PackageDetailDTO temp = new PackageDetailDTO();
                BeanUtil.copyProperties(packageDetailDTO, temp);
                return temp;
            });
        }
        return new ArrayList<>(packageDetailDTOMap.values());
    }

    private List<PackageDetailDTO> analyzePackageDetails(String cargoCode, List<PackageDetailDTO> detailDTOList) {
        log.info("analyzePackageDetails");
        List<PackageDetailDTO> resultDetailDTOList = Lists.newArrayList();
        //如果没有符合的，直接返回
        if (CollectionUtil.isEmpty(detailDTOList)) {
            log.error("包裹明细不存在");
            return resultDetailDTOList;
        }
        //根据包裹明细code查询预包明细
        List<String> skuCodeList = detailDTOList.stream().map(PackageDetailDTO::getSkuCode).collect(Collectors.toList());
        PrePackageSkuDetailParam prePackageSkuDetailParam = new PrePackageSkuDetailParam();
        prePackageSkuDetailParam.setSkuCodeList(skuCodeList);
        prePackageSkuDetailParam.setCargoCode(cargoCode);
        prePackageSkuDetailParam.setStatus(PrePackageEnableEnum.ENABLE.getCode());
        List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOList = remotePrePackageSkuDetailClient.getList(prePackageSkuDetailParam);
        //没有符合的预包条码直接返回
        if (CollectionUtil.isEmpty(prePackageSkuDetailDTOList)) {
            log.error("预包商品明细不存在");
            return resultDetailDTOList;
        }
        List<String> preUpcCodeList = prePackageSkuDetailDTOList.stream().map(PrePackageSkuDetailDTO::getPreUpcCode).distinct().collect(Collectors.toList());
        //然后获取所有的的预包条码查询预包下所有明细
        prePackageSkuDetailParam.setSkuCodeList(null);
        prePackageSkuDetailParam.setCargoCode(cargoCode);
        prePackageSkuDetailParam.setPreUpcCodeList(preUpcCodeList);
        prePackageSkuDetailDTOList = remotePrePackageSkuDetailClient.getList(prePackageSkuDetailParam);
        Map<String, List<PrePackageSkuDetailDTO>> prePackageSkuDetailDTOMap = prePackageSkuDetailDTOList.stream()
                .collect(Collectors.groupingBy(PrePackageSkuDetailDTO::getPreUpcCode));

        // 预包条码
        String candidate = StrUtil.EMPTY;
        for (List<PrePackageSkuDetailDTO> list : prePackageSkuDetailDTOMap.values()) {
            if (match(list, detailDTOList)) {
                candidate = list.get(0).getPreUpcCode();
            }
        }
        if (StrUtil.isBlank(candidate)) {
            log.info("没有找到合适的预包");
            return resultDetailDTOList;
        }

        //查询大于0的库存预包商品
        StockParam stockParam = new StockParam();
        stockParam.setCargoCode(cargoCode);
        stockParam.setSkuCode(candidate);
        stockParam.setHasAvailableQty(true);
        List<StockDTO> stockDTOList = remoteStockClient.getList(stockParam);
        if (CollectionUtil.isEmpty(stockDTOList)) {
            log.info("预包库存不足 {} {} {}",CurrentRouteHolder.getWarehouseCode(),cargoCode,candidate);
            return resultDetailDTOList;
        }

        return getResultPackageDetailDTOList(detailDTOList, candidate);

    }

    private List<PackageDetailDTO> getResultPackageDetailDTOList(List<PackageDetailDTO> originalDetailDTOList,String preSkuCode) {
        List<PackageDetailDTO> resultDetailDTOList = new ArrayList<>();
        //解析预包结构，返回明细
        String lineSeq = originalDetailDTOList.stream().map(PackageDetailDTO::getLineSeq).max(String::compareTo).orElse("0");
        int num = 1;
        for (PackageDetailDTO packageDetailDTO : originalDetailDTOList) {
            PackageDetailDTO lastDetailDTO = new PackageDetailDTO();
            BeanUtils.copyProperties(packageDetailDTO, lastDetailDTO);
            lastDetailDTO.setPackUid(String.valueOf(UUID.randomUUID()));
            lastDetailDTO.setPUid(0L);
            lastDetailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
            lastDetailDTO.setIsPre(PackIsPreEnum.PRE_CHILD.getCode());
            resultDetailDTOList.add(lastDetailDTO);
            num = num + 1;
        }

        PrePackageSkuParam prePackageSkuParam = new PrePackageSkuParam();
        prePackageSkuParam.setPreUpcCode(preSkuCode);
        prePackageSkuParam.setCargoCode(resultDetailDTOList.get(0).getCargoCode());
        PrePackageSkuDTO prePackageSkuDTO = remotePrePackageSkuClient.get(prePackageSkuParam);
        //计算预包条码的明细
        PackageDetailDTO packageDetailDTO = new PackageDetailDTO();
        BeanUtils.copyProperties(resultDetailDTOList.get(0), packageDetailDTO);
        packageDetailDTO.setSkuName(prePackageSkuDTO.getPreUpcName());
        packageDetailDTO.setId(null);
        packageDetailDTO.setPUid(null);
        packageDetailDTO.setVersion(null);
        packageDetailDTO.setUpdatedTime(null);
        packageDetailDTO.setUpdatedBy("");
        packageDetailDTO.setIsPre(PackIsPreEnum.PRE.getCode());

        packageDetailDTO.setWithdrawCompareDate(0L);
        packageDetailDTO.setSkuLotNo("");
        packageDetailDTO.setExternalSkuLotNo("");
        packageDetailDTO.setExpireDate(0L);
        packageDetailDTO.setExpireDateEnd(0L);
        packageDetailDTO.setExpireDateStart(0L);
        packageDetailDTO.setInventoryType("");
        packageDetailDTO.setLocationCode("");

        packageDetailDTO.setSkuQty(BigDecimal.ONE);
        packageDetailDTO.setSkuCode(preSkuCode);
        packageDetailDTO.setUpcCode(preSkuCode);
        //生成明细ID
        packageDetailDTO.setPackUid(UUID.randomUUID().toString());
        packageDetailDTO.setExpQty(BigDecimal.ZERO);
        num = num + 1;
        packageDetailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
        packageDetailDTO.setStatus(PackEnum.STATUS.CREATE_STATUS.getCode());
        packageDetailDTO.setZoneType(ZoneTypeEnum.ZONE_TYPE_PICK.getType());
        resultDetailDTOList.add(packageDetailDTO);
        return resultDetailDTOList;
    }

    private boolean match(List<PrePackageSkuDetailDTO> list, List<PackageDetailDTO> detailDTOList) {
        Set<String> preSkuSet = list.stream().map(PrePackageSkuDetailDTO::getSkuCode).collect(Collectors.toSet());
        Set<String> collect = detailDTOList.stream().map(PackageDetailDTO::getSkuCode).collect(Collectors.toSet());
        if (preSkuSet.size() != collect.size()) return false;
        for (String skuCode : collect) {
            BigDecimal reduce = detailDTOList.stream().filter(packageDetailDTO -> packageDetailDTO.getSkuCode().equalsIgnoreCase(skuCode))
                    .map(PackageDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal reduce1 = list.stream().filter(prePackageSkuDetailDTO -> prePackageSkuDetailDTO.getSkuCode().equalsIgnoreCase(skuCode))
                    .map(PrePackageSkuDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (reduce.compareTo(reduce1) != 0) {
                log.info("{}",list.get(0).getPreUpcCode());
                return false;
            }
        }
        return true;
    }

    private List<PackageDetailDTO> analyzePackageDetailsOld(String cargoCode, List<PackageDetailDTO> detailDTOList) {
        List<PackageDetailDTO> resultDetailDTOList = Lists.newArrayList();
        //如果没有符合的，直接返回
        if (CollectionUtil.isEmpty(detailDTOList)) {
            return resultDetailDTOList;
        }
        //根据包裹明细code查询预包明细
        List<String> skuCodeList = detailDTOList.stream().map(PackageDetailDTO::getSkuCode).collect(Collectors.toList());
        PrePackageSkuDetailParam prePackageSkuDetailParam = new PrePackageSkuDetailParam();
        prePackageSkuDetailParam.setSkuCodeList(skuCodeList);
        prePackageSkuDetailParam.setCargoCode(cargoCode);
        prePackageSkuDetailParam.setStatus(PrePackageEnableEnum.ENABLE.getCode());
        List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOList = remotePrePackageSkuDetailClient.getList(prePackageSkuDetailParam);
        //没有符合的预包条码直接返回
        if (CollectionUtil.isEmpty(prePackageSkuDetailDTOList)) {
            return resultDetailDTOList;
        }
        List<String> preUpcCodeList = prePackageSkuDetailDTOList.stream().map(PrePackageSkuDetailDTO::getPreUpcCode).distinct().collect(Collectors.toList());
        //然后获取所有的的预包条码查询预包下所有明细
        prePackageSkuDetailParam.setSkuCodeList(null);
        prePackageSkuDetailParam.setCargoCode(cargoCode);
        prePackageSkuDetailParam.setPreUpcCodeList(preUpcCodeList);
        prePackageSkuDetailDTOList = remotePrePackageSkuDetailClient.getList(prePackageSkuDetailParam);
        Map<String, List<PrePackageSkuDetailDTO>> prePackageSkuDetailDTOMap = prePackageSkuDetailDTOList.stream().collect(Collectors.groupingBy(PrePackageSkuDetailDTO::getPreUpcCode));

        //查询大于0的库存预包商品
        StockParam stockParam = new StockParam();
        stockParam.setCargoCode(cargoCode);
        stockParam.setSkuCodeList(new ArrayList<>(prePackageSkuDetailDTOMap.keySet()));
        stockParam.setHasAvailableQty(true);
        List<StockDTO> stockDTOList = remoteStockClient.getList(stockParam);
        Map<String, StockDTO> stockDTOMap = stockDTOList.stream().collect(Collectors.toMap(StockDTO::getSkuCode, Function.identity()));

        log.info("筛选出所有的预包 {}", JSONUtil.toJsonStr(prePackageSkuDetailDTOMap));
        //然后筛选符合的预包---剔除不匹配的预包条码
        Iterator<Map.Entry<String, List<PrePackageSkuDetailDTO>>> iterator = prePackageSkuDetailDTOMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<PrePackageSkuDetailDTO>> entry = iterator.next();
            //如果库存没有了，直接删除，不参与下面的计算
            if (ObjectUtil.isEmpty(stockDTOMap.get(entry.getKey()))) {
                //查询一下库存是否满足
                iterator.remove();
                continue;
            }
            for (PrePackageSkuDetailDTO prePackageSkuDetailDTO : entry.getValue()) {
                //任意一个元素成功，返回true
                boolean match = detailDTOList.stream().anyMatch(a -> a.getSkuCode().equalsIgnoreCase(prePackageSkuDetailDTO.getSkuCode()));
                //没匹配上就删除
                if (!match) {
                    iterator.remove();
                    break;
                }
            }
        }
        log.info("剔除完的的预包 {}", JSONUtil.toJsonStr(prePackageSkuDetailDTOMap));
        //没有符合的预包条码直接返回
        if (CollectionUtil.isEmpty(prePackageSkuDetailDTOMap)) {
            return resultDetailDTOList;
        }
        //进行深度copy
        Map<String, PackageDetailDTO> packageDetailDTOMap = new HashMap<>();
        detailDTOList.forEach(it -> {
            PackageDetailDTO packageDetailDTO = new PackageDetailDTO();
            BeanUtils.copyProperties(it, packageDetailDTO);
            PackageDetailDTO detailDTO = packageDetailDTOMap.get(it.getSkuCode());
            //如果明细相同，则明细的数量相加
            if (ObjectUtil.isNotEmpty(detailDTO)) {
                packageDetailDTO.setSkuQty(packageDetailDTO.getSkuQty().add(detailDTO.getSkuQty()));
            }
            packageDetailDTOMap.put(it.getSkuCode(), packageDetailDTO);
        });
        //符合的预包条码
        Map<String, BigDecimal> bigDecimalMap = new HashMap<>();
        Map<String, BigDecimal> bigDecimalMapOne = new HashMap<>();
        Map<String, BigDecimal> bigDecimalMapTwo = new HashMap<>();
        Map<String, BigDecimal> bigDecimalMapThree = new HashMap<>();
        prePackageSkuDetailDTOMap.forEach((key, value) -> {
            //进行深度copy
            Map<String, PackageDetailDTO> detailDTOHashMap = new HashMap<>();
            packageDetailDTOMap.forEach((k, v) -> {
                PackageDetailDTO packageDetailDTO = new PackageDetailDTO();
                BeanUtils.copyProperties(v, packageDetailDTO);
                detailDTOHashMap.put(k, packageDetailDTO);
            });
            boolean flag = true;
            while (flag) {
                for (PrePackageSkuDetailDTO prePackageSkuDetailDTO : value) {
                    PackageDetailDTO packageDetailDTO = detailDTOHashMap.get(prePackageSkuDetailDTO.getSkuCode());
                    //扣减之后是否小于0
                    if (packageDetailDTO.getSkuQty().subtract(prePackageSkuDetailDTO.getSkuQty()).compareTo(BigDecimal.ZERO) < 0) {
                        flag = false;
                        break;
                    }
                    packageDetailDTO.setSkuQty(packageDetailDTO.getSkuQty().subtract(prePackageSkuDetailDTO.getSkuQty()));
                }
                //计算预包
                if (flag) {
                    //增加预包条码的数量
                    BigDecimal bigDecimal = bigDecimalMapOne.get(key);
                    if (Objects.isNull(bigDecimal)) {
                        bigDecimal = new BigDecimal("1.000");
                    } else {
                        bigDecimal = bigDecimal.add(new BigDecimal("1.000"));
                    }
                    bigDecimalMapOne.put(key, bigDecimal);
                }
            }
            //优先级1的数据组装
            double totalSum;
            if (ObjectUtil.isNotEmpty(bigDecimalMapOne.get(key))) {
                totalSum = value.stream().mapToDouble(a -> a.getSkuQty().multiply(bigDecimalMapOne.get(key)).doubleValue()).sum();
                bigDecimalMap.put(key, new BigDecimal(totalSum));
                //优先级2的数据组装
                totalSum = value.stream().mapToDouble(a -> a.getSkuQty().doubleValue()).sum();
                bigDecimalMapTwo.put(key, new BigDecimal(totalSum));
                //优先级3的数据组装
                int size = value.size();
                bigDecimalMapThree.put(key, new BigDecimal(size));
            }
        });
        //没有符合的预包条码直接返回
        if (CollectionUtil.isEmpty(bigDecimalMapOne)) {
            return resultDetailDTOList;
        }
        log.info("库存前 resultOne bigDecimalMap {}  bigDecimalMapOne {}", JSONUtil.toJsonStr(bigDecimalMap), JSONUtil.toJsonStr(bigDecimalMapOne));
        //校验结果里面的库存
        for (Map.Entry<String, BigDecimal> entry : bigDecimalMapOne.entrySet()) {
            //如果库存没有了，直接删除，不参与下面的计算
            StockDTO stockDTO = stockDTOMap.get(entry.getKey());
            //大于0 小于需求数量的可以进去修改。
            if (ObjectUtil.isNotEmpty(stockDTO) && stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) > 0 && stockDTO.getAvailableQty().compareTo(entry.getValue()) < 0) {
                //查询一下库存是否满足,不满足就把剩余库存塞进去
                bigDecimalMapOne.put(entry.getKey(), stockDTO.getAvailableQty());
                List<PrePackageSkuDetailDTO> value = prePackageSkuDetailDTOMap.get(entry.getKey());
//                entry.setValue(stockDTO.getAvailableQty());
                double totalSum = value.stream().mapToDouble(a -> a.getSkuQty().multiply(bigDecimalMapOne.get(entry.getKey())).doubleValue()).sum();
                bigDecimalMap.put(entry.getKey(), new BigDecimal(totalSum));
            }
        }
        log.info("库存后 resultOne bigDecimalMap {}  bigDecimalMapOne {}", JSONUtil.toJsonStr(bigDecimalMap), JSONUtil.toJsonStr(bigDecimalMapOne));
        Map<String, BigDecimal> resultOne = getResultMap(bigDecimalMap, bigDecimalMapOne);
        //只有一个的时候返回
        if (resultOne.size() <= 1) {
            //解析预包结构，返回明细
            return getResultPackageDetailDTOList(resultDetailDTOList, prePackageSkuDetailDTOMap, packageDetailDTOMap, resultOne);
        }
        //优先级二
        log.info("resultTwo bigDecimalMapTwo {}  resultOne {}", JSONUtil.toJsonStr(bigDecimalMapTwo), JSONUtil.toJsonStr(resultOne));
        Map<String, BigDecimal> resultTwo = getResultMap(bigDecimalMapTwo, resultOne);
        //符合的预包条码
        if (resultTwo.size() <= 1) {
            return getResultPackageDetailDTOList(resultDetailDTOList, prePackageSkuDetailDTOMap, packageDetailDTOMap, resultTwo);
        }
        //优先级三
        log.info("resultThree bigDecimalMapThree {}  resultTwo {}", JSONUtil.toJsonStr(bigDecimalMapThree), JSONUtil.toJsonStr(resultTwo));
        Map<String, BigDecimal> resultThree = getResultMap(bigDecimalMapThree, resultTwo);
        if (resultThree.size() <= 1) {
            return getResultPackageDetailDTOList(resultDetailDTOList, prePackageSkuDetailDTOMap, packageDetailDTOMap, resultThree);
        }
        //优先级四 需要把剩余的明细种类提取出来，然后获取拣选最多的哪个。
        //解析预包结构，返回明细
        Map<String, BigDecimal> bigDecimalMapFour = new HashMap<>();
        resultThree.forEach((key, value) -> {
            //进行深度copy
            Map<String, PackageDetailDTO> detailDTOHashMap = new HashMap<>();
            packageDetailDTOMap.forEach((k, v) -> {
                PackageDetailDTO packageDetailDTO = new PackageDetailDTO();
                BeanUtils.copyProperties(v, packageDetailDTO);
                detailDTOHashMap.put(k, packageDetailDTO);
            });
            List<PackageDetailDTO> resultDTOList = Lists.newArrayList();
            List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOS = prePackageSkuDetailDTOMap.get(key);
            Map<String, PrePackageSkuDetailDTO> packageSkuDetailDTOMap = prePackageSkuDetailDTOS.stream().collect(Collectors.toMap(PrePackageSkuDetailDTO::getSkuCode, Function.identity()));
            //计算剩余明细
            for (int i = 1; i <= value.intValue(); i++) {
                for (PackageDetailDTO packageDetailDTO : detailDTOHashMap.values()) {
                    PrePackageSkuDetailDTO prePackageSkuDetailDTO = packageSkuDetailDTOMap.get(packageDetailDTO.getSkuCode());
                    if (i == value.intValue() && ObjectUtils.isEmpty(prePackageSkuDetailDTO)) {
                        resultDTOList.add(packageDetailDTO);
                        continue;
                    }
                    if (i == value.intValue() && packageDetailDTO.getSkuQty().subtract(prePackageSkuDetailDTO.getSkuQty().multiply(BigDecimal.valueOf(i))).compareTo(BigDecimal.ZERO) > 0) {
                        packageDetailDTO.setSkuQty(packageDetailDTO.getSkuQty().subtract(prePackageSkuDetailDTO.getSkuQty()));
                        packageDetailDTO.setIsPre(PackIsPreEnum.LAST.getCode());
                        resultDTOList.add(packageDetailDTO);
                    }
                }
            }
            //明细剩余种类数
            bigDecimalMapFour.put(key, new BigDecimal(resultDTOList.size()));
        });
        log.info("resultFour bigDecimalMapFour {}  resultThree {}", JSONUtil.toJsonStr(bigDecimalMapFour), JSONUtil.toJsonStr(resultThree));
        Map<String, BigDecimal> resultFour = getResultMap(bigDecimalMapFour, resultThree);
        if (resultFour.size() > 1) {
            //如果有多个，随机一个。
            Random r = new Random();
            int nextInt = r.nextInt(resultFour.size());
            String key = (String) resultFour.keySet().toArray()[nextInt];
            resultFour = resultFour.entrySet().stream().filter(map -> map.getKey().equalsIgnoreCase(key)).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        return getResultPackageDetailDTOList(resultDetailDTOList, prePackageSkuDetailDTOMap, packageDetailDTOMap, resultFour);
    }

    /**
     * 功能描述:  筛选获取最大的值。
     * 创建时间:  2021/9/6 10:18 上午
     *
     * @param result:
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     * <AUTHOR>
     */
    private Map<String, BigDecimal> getResultMap(Map<String, BigDecimal> result, Map<String, BigDecimal> resultVal) {
        //加入库存校验， 库存不足的直接删除。
        if (CollectionUtil.isEmpty(result)) {
            return result;
        }
        //然后筛选符合的预包---剔除不匹配的预包条码
        //如果库存没有了，直接删除，不参与下面的计算
        //查询一下库存是否满足
        result.entrySet().removeIf(entry -> ObjectUtil.isEmpty(resultVal.get(entry.getKey())));

        //匹配结果 获取最大值 如果有多个进入第二优先级
        Collection<BigDecimal> resultValue = result.values();
        BigDecimal max = Collections.max(resultValue);
        //第一优先级结果
        return result.entrySet().stream().filter(map -> map.getValue().compareTo(max) == 0).collect(Collectors.toMap(Map.Entry::getKey, p -> resultVal.get(p.getKey())));
    }

    /**
     * 功能描述:  根据匹配出来的结果，封装新的包裹明细
     * 创建时间:  2021/9/6 10:18 上午
     *
     * @param resultDetailDTOList:
     * @param prePackageSkuDetailDTOMap:
     * @param packageDetailDTOMap:
     * @param resultMap:
     * @return java.util.List<com.dt.domain.bill.dto.PackageDetailDTO>
     * <AUTHOR>
     */
    private List<PackageDetailDTO> getResultPackageDetailDTOList(List<PackageDetailDTO> resultDetailDTOList, Map<String, List<PrePackageSkuDetailDTO>> prePackageSkuDetailDTOMap, Map<String, PackageDetailDTO> packageDetailDTOMap, Map<String, BigDecimal> resultMap) {
        //解析预包结构，返回明细
        String lineSeq = packageDetailDTOMap.values().stream().map(PackageDetailDTO::getLineSeq).max(String::compareTo).orElse("0");
        int num = 1;
        for (Map.Entry<String, BigDecimal> entry : resultMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = entry.getValue();
            List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOList = prePackageSkuDetailDTOMap.get(key);
            Map<String, PrePackageSkuDetailDTO> packageSkuDetailDTOMap = prePackageSkuDetailDTOList.stream().collect(Collectors.toMap(PrePackageSkuDetailDTO::getSkuCode, Function.identity()));
            //计算剩余明细
            for (PackageDetailDTO packageDetailDTO : packageDetailDTOMap.values()) {
                num = num + 1;
                PrePackageSkuDetailDTO prePackageSkuDetailDTO = packageSkuDetailDTOMap.get(packageDetailDTO.getSkuCode());
                packageDetailDTO.setId(null);
                packageDetailDTO.setPUid(null);
                packageDetailDTO.setVersion(null);
                packageDetailDTO.setUpdatedTime(null);
                if (ObjectUtils.isEmpty(prePackageSkuDetailDTO)) {
                    PackageDetailDTO detailDTO = new PackageDetailDTO();
                    BeanUtils.copyProperties(packageDetailDTO, detailDTO);
                    detailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
                    detailDTO.setPackUid(String.valueOf(UUID.randomUUID()));
                    detailDTO.setIsPre(PackIsPreEnum.LAST.getCode());
                    resultDetailDTOList.add(detailDTO);
                    continue;
                }
                PackageDetailDTO detailDTO = new PackageDetailDTO();
                BeanUtils.copyProperties(packageDetailDTO, detailDTO);
                BigDecimal multiply = prePackageSkuDetailDTO.getSkuQty().multiply(value);
                BigDecimal skuQty = detailDTO.getSkuQty().subtract(multiply);
                if (skuQty.compareTo(BigDecimal.ZERO) > 0) {
                    detailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
                    detailDTO.setPackUid(String.valueOf(UUID.randomUUID()));
                    detailDTO.setSkuQty(skuQty);
                    detailDTO.setIsPre(PackIsPreEnum.LAST.getCode());
                    resultDetailDTOList.add(detailDTO);
                }
                //计算预包对应的明细
                PackageDetailDTO lastDetailDTO = new PackageDetailDTO();
                BeanUtils.copyProperties(packageDetailDTO, lastDetailDTO);
                lastDetailDTO.setPackUid(String.valueOf(UUID.randomUUID()));
                lastDetailDTO.setSkuQty(multiply);
                lastDetailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
                lastDetailDTO.setIsPre(PackIsPreEnum.PRE_CHILD.getCode());
                resultDetailDTOList.add(lastDetailDTO);
            }
            PrePackageSkuParam prePackageSkuParam = new PrePackageSkuParam();
            prePackageSkuParam.setPreUpcCode(key);
            prePackageSkuParam.setCargoCode(resultDetailDTOList.get(0).getCargoCode());
            PrePackageSkuDTO prePackageSkuDTO = remotePrePackageSkuClient.get(prePackageSkuParam);
            //计算预包条码的明细
            PackageDetailDTO packageDetailDTO = new PackageDetailDTO();
            BeanUtils.copyProperties(resultDetailDTOList.get(0), packageDetailDTO);
            packageDetailDTO.setSkuName(prePackageSkuDTO.getPreUpcName());
            packageDetailDTO.setId(null);
            packageDetailDTO.setPUid(null);
            packageDetailDTO.setVersion(null);
            packageDetailDTO.setUpdatedTime(null);
            packageDetailDTO.setUpdatedBy("");
            packageDetailDTO.setIsPre(PackIsPreEnum.PRE.getCode());

            packageDetailDTO.setWithdrawCompareDate(0L);
            packageDetailDTO.setSkuLotNo("");
            packageDetailDTO.setExternalSkuLotNo("");
            packageDetailDTO.setExpireDate(0L);
            packageDetailDTO.setExpireDateEnd(0L);
            packageDetailDTO.setExpireDateStart(0L);
            packageDetailDTO.setInventoryType("");
            packageDetailDTO.setLocationCode("");

            packageDetailDTO.setSkuQty(value);
            packageDetailDTO.setSkuCode(key);
            packageDetailDTO.setUpcCode(key);
            //生成明细ID
            packageDetailDTO.setPackUid(UUID.randomUUID().toString());
            packageDetailDTO.setExpQty(BigDecimal.ZERO);
            num = num + 1;
            packageDetailDTO.setLineSeq(StrUtil.join(StrUtil.UNDERLINE, lineSeq, num));
            packageDetailDTO.setStatus(PackEnum.STATUS.CREATE_STATUS.getCode());
            packageDetailDTO.setZoneType(ZoneTypeEnum.ZONE_TYPE_PICK.getType());
            resultDetailDTOList.add(packageDetailDTO);
        }
        return resultDetailDTOList;
    }

    // 获取出库单
    private ShipmentOrderDTO shipmentOrderDTO(PretreatmentContext context) {
        if (ObjectUtil.isNotEmpty(context.getShipmentOrderDTO())) {
            return context.getShipmentOrderDTO();
        }
        if (StrUtil.isNotBlank(context.getShipmentOrderCode())) {
            ShipmentOrderDTO shipmentOrderDTO = remoteShipmentOrderClient.getShipmentOrderByCode(context.getShipmentOrderCode());
            if (null == shipmentOrderDTO) throw new NoRetryNeedException(BaseBizEnum.TIP, "shipment not found");
            context.setShipmentOrderDTO(shipmentOrderDTO);
            return shipmentOrderDTO;
        }
        if (context.getShipmentId() != null) {
            ShipmentOrderDTO shipmentOrderDTO = remoteShipmentOrderClient.getById(context.getShipmentId());
            if (null == shipmentOrderDTO) throw new NoRetryNeedException(BaseBizEnum.TIP, "shipment not found");
            context.setShipmentOrderDTO(shipmentOrderDTO);
            return shipmentOrderDTO;
        }
        throw new NoRetryNeedException(BaseBizEnum.TIP, StrUtil.join(StrUtil.EMPTY,
                "仓库:", context.getWarehouseCode(),
                "出库单编号:", context.getShipmentOrderCode(),
                "出库单ID:", context.getShipmentId(),
                "没有找到对应出库单"));
    }

    // 出库单编号
    private String shipmentOrderCode(PretreatmentContext context) {
        if (StrUtil.isNotBlank(context.getShipmentOrderCode())) {
            return context.getShipmentOrderCode();
        } else {
            return shipmentOrderDTO(context).getShipmentOrderCode();
        }
    }

    // 出库单明细
    private List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList(PretreatmentContext context) {
        if (CollectionUtil.isNotEmpty(context.getShipmentOrderDetailDTOList())) {
            return context.getShipmentOrderDetailDTOList();
        }
        ShipmentOrderDetailParam shipmentOrderDetailParam = new ShipmentOrderDetailParam();
        shipmentOrderDetailParam.setShipmentOrderCode(context.getShipmentOrderCode());
        List<ShipmentOrderDetailDTO> list = remoteShipmentDetailClient.getList(shipmentOrderDetailParam);
        context.setShipmentOrderDetailDTOList(list);
        return list;
    }

    // 包裹信息
    private List<PackageDTO> packageDTOList(PretreatmentContext context) {
        if (CollectionUtil.isNotEmpty(context.getPackageDTOList())) {
            return context.getPackageDTOList();
        }
        List<PackageDTO> packageDTOList = remotePackageClient.listByShipment(context.getShipmentOrderCode()).stream()
                .filter(packageDTO -> !PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode().equals(packageDTO.getStatus()))
                .collect(Collectors.toList());
        context.setPackageDTOList(packageDTOList);
        context.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
        return packageDTOList;
    }

    private List<AbnormalOrderDTO> abnormalOrderDTOList(PretreatmentContext context) {
        if (context.getAbnormalOrderDTOList() == null) {
            List<String> billNoList = new ArrayList<>();
            billNoList.add(shipmentOrderCode(context));
            if (CollectionUtil.isNotEmpty(packageDTOList(context))) {
                billNoList.addAll(packageCodeList(context));
            }
            AbnormalOrderParam abnormalOrderParam = new AbnormalOrderParam();
            abnormalOrderParam.setBillNoList(billNoList);
            abnormalOrderParam.setBillTypeList(ABNORMAL_TYPE_LIST);
            abnormalOrderParam.setBusinessTypeList(ABNORMAL_BUSINESS_TYPE_LIST);
            abnormalOrderParam.setStatusList(ABNORMAL_STATUS_LIST);
            List<AbnormalOrderDTO> abnormalOrderDTOList = remoteAbnormalOrderClient.getList(abnormalOrderParam);
            context.setAbnormalOrderDTOList(abnormalOrderDTOList);
            return abnormalOrderDTOList;
        } else {
            return context.getAbnormalOrderDTOList();
        }
    }

    // 包裹详情
    private List<PackageDetailDTO> packageDetailDTOList(PretreatmentContext context) {

        if (null == context.getPackageDetailDTOList()) {
            List<PackageDetailDTO> list = remotePackageDetailClient.getList(packageCodeList(context));
            context.setPackageDetailDTOList(list);
            return list;
        } else {
            return context.getPackageDetailDTOList();
        }
    }

    // 承运商信息
    private CarrierDTO carrierDTO(PretreatmentContext context) {
        if (ObjectUtil.isNotEmpty(context.getCarrierDTO())) {
            return context.getCarrierDTO();
        }
        if (StrUtil.isBlank(shipmentOrderDTO(context).getCarrierCode())) {
            return null;
        }
        CarrierDTO carrierDTO = remoteCarrierClient.queryByCode(shipmentOrderDTO(context).getCarrierCode());
        context.setCarrierDTO(carrierDTO);
        return carrierDTO;
    }

    private List<StockTransactionDTO> stockTransactionDTOList(PretreatmentContext context) {
        if (CollectionUtil.isNotEmpty(context.getStockTransactionDTOList())) {
            return context.getStockTransactionDTOList();
        }
        ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTO(context);
        StockTransactionParam stockTransactionParam = new StockTransactionParam();
        List<String> parentBillNoList = new ArrayList<>();
        parentBillNoList.add(shipmentOrderDTO.getShipmentOrderCode());
        if (StrUtil.isNotBlank(shipmentOrderDTO.getGlobalNo())) {
            parentBillNoList.add(shipmentOrderDTO.getGlobalNo());
        }
        stockTransactionParam.setParentBillNoList(parentBillNoList);
        stockTransactionParam.setOperationTypeList(ListUtil.of(OperationTypeEnum.OPERATION_SHIPMENT.getType(), OperationTypeEnum.OPERATION_UPSTREAM_OCCUPY.getType()));
        List<StockTransactionDTO> stockTransactionDTOList = remoteStockTransactionClient.getList(stockTransactionParam);
        stockTransactionDTOList = stockTransactionDTOList.stream()
                .filter(stockTransactionDTO -> !stockTransactionDTO.getStatus().equals(StockTransactionStatusEnum.CANCELLED.getCode()))
                .collect(Collectors.toList());
        context.setStockTransactionDTOList(stockTransactionDTOList);
        return stockTransactionDTOList;
    }

    private List<SkuDTO> skuDTOList(PretreatmentContext context) {
        if (CollectionUtil.isNotEmpty(context.getSkuDTOList())) {
            return context.getSkuDTOList();
        } else {
            List<String> skuCodeList = packageDetailDTOList(context).stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
            SkuParam skuParam = new SkuParam();
            skuParam.setCodeList(skuCodeList);
            skuParam.setCargoCode(shipmentOrderDTO(context).getCargoCode());
            List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
            List<SkuDTO> boxSkuList = skuDTOList.stream().filter(skuDTO -> SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.BOX_SKU))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(boxSkuList)) {
                BoxSkuParam boxSkuParam = new BoxSkuParam();
                boxSkuParam.setCargoCode(shipmentOrderDTO(context).getCargoCode());
                boxSkuParam.setBoxSkuCodeList(boxSkuList.stream().map(SkuDTO::getCode).collect(Collectors.toList()));
                List<BoxSkuDTO> boxSkuDTOList = remoteBoxSkuClient.getBoxSkuByBoxSkuEffective(boxSkuParam);
                if (CollectionUtil.isEmpty(boxSkuDTOList)) throw new BaseException(BaseBizEnum.TIP, "套盒信息不存在");
                if (boxSkuDTOList.stream().anyMatch(boxSkuDTO -> CollectionUtil.isEmpty(boxSkuDTO.getDetailList())))
                    throw new BaseException(BaseBizEnum.TIP, "套盒明细信息不存在");
                List<String> boxDetailSkuCodeList = boxSkuDTOList.stream()
                        .flatMap(it -> it.getDetailList().stream())
                        .map(BoxSkuDetailDTO::getChildSkuCode).collect(Collectors.toList());
                skuParam.setCodeList(boxDetailSkuCodeList);
                List<SkuDTO> boxDetailSkuList = remoteSkuClient.getList(skuParam);
                skuDTOList.addAll(boxDetailSkuList);
            }
            context.setSkuDTOList(skuDTOList);
            return skuDTOList;
        }
    }

    private void flushDB(PretreatmentContext context) {
        if (CollectionUtil.isNotEmpty(context.getPackageLogDTOList())) {
            context.getOperationBO().setPackageLogDTOList(context.getPackageLogDTOList());
        }
        // 如果没有拦截单不能取消三级占用失败的异常单
        if (CollectionUtil.isEmpty(context.getOrderInterceptDTOList())) {
            if (CollectionUtil.isNotEmpty(context.getOperationBO().getAbnormalOrderDTOList())) {
                List<AbnormalOrderDTO> abnormalOrderDTOList = context.getOperationBO().getAbnormalOrderDTOList().stream()
                        .filter(abnormalOrderDTO -> !abnormalOrderDTO.getBusinessType().equals(AbnormalOrderEnum.bussinessTypeEnum.THREE_LEVEL_STOCK_TYPE.getCode()))
                        .collect(Collectors.toList());
                context.getOperationBO().setAbnormalOrderDTOList(abnormalOrderDTOList);
            }
        }
        remoteShipmentOrderClient.pretreatmentSubmit(context.getOperationBO());
    }

    private static final class MultiPreSatisfyException extends RuntimeException {
        @Override
        public String getMessage() {
            return "查询到多个多预包解析满足当前包裹";
        }
    }

    private static final MultiPreSatisfyException MULTI_PRE_SATISFY_EXCEPTION = new MultiPreSatisfyException();

    /**
     * 维护包裹上面的字段
     */
    private void maintainPackageFiled(PretreatmentContext context) {
        if (CollectionUtil.isEmpty(packageDTOList(context))) return;
        if (packageDTOList(context).stream().anyMatch(packageDTO -> CollectionUtil.isEmpty(packageDTO.getListDetail())))
            return;
        // 刷新一下商品数据【可能增加了预包数据】
        refreshSku(context);
        // 维护理论重量
        maintainPackageWeight(context);
        // 维护理论体积
        maintainPackageVolume(context);

    }

    /**
     * @param context
     * @return void
     * <AUTHOR>
     * @describe: 有商品开启SN 不走秒杀spikeAnalysisSku 使用UUID 在MD5
     * @date 2024/5/9 13:16
     */
    private void refreshSpikeAnalysisSku(PretreatmentContext context) {
        if (ObjectUtil.isEmpty(context) || CollectionUtil.isEmpty(context.getPackageDTOList())) return;
        // 开启SN 重置spikeAnalysisSku
        context.getPackageDTOList().forEach(packageDTO -> {
            //包裹理论重量信息
            //需要复核的商品
            List<String> needCheckDetailList = packageDetailDTOList(context).stream()
                    .filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode()))
                    .filter(packageDetailDTO -> packageDetailFilter(packageDTO, packageDetailDTO))
                    .map(PackageDetailDTO::getSkuCode)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(needCheckDetailList)) {
                return;
            }
            List<SkuDTO> skuDTOList = skuDTOList(context).stream().filter(skuDTO -> needCheckDetailList.contains(skuDTO.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skuDTOList)) {
                return;
            }
            //包裹复核的商品开启出库SN,不走秒杀
            if (skuDTOList.stream().anyMatch(skuDTO -> skuDTO.getSNMgmtOutNeed())) {
                log.info("处理包裹SN包裹spike:{} ", JSONUtil.toJsonStr(context.getShipmentOrderCode()));
                String spikeAnalysisSku = packageDTO.getSpikeAnalysisSku();
                if (StringUtils.isEmpty(spikeAnalysisSku)) {
                    packageDTO.setSpikeAnalysisSku(SecureUtil.md5(UUID.randomUUID().toString()));
                } else {
                    packageDTO.setSpikeAnalysisSku(spikeAnalysisSku + "###" + RandomUtil.randomNumbers(10));
                }
            }
        });
    }

    /**
     * 刷新商品数据
     */
    private void refreshSku(PretreatmentContext context) {
        if (ObjectUtil.isEmpty(context) || CollectionUtil.isEmpty(context.getPackageDTOList())) return;
        List<String> skuCodeList = context.getPackageDTOList().stream()
                .flatMap(packageDTO -> packageDTO.getListDetail().stream())
                .map(PackageDetailDTO::getSkuCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuCodeList)) return;
        // 没有预包不需要刷新
        if (CollectionUtil.isNotEmpty(context.getSkuDTOList()) && context.getPackageDetailDTOList().stream()
                .noneMatch(packageDetailDTO -> packageDetailDTO.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()))) {
            return;
        }
        SkuParam skuParam = new SkuParam();
        skuParam.setCodeList(skuCodeList);
        skuParam.setCargoCode(shipmentOrderDTO(context).getCargoCode());
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        context.setSkuDTOList(skuDTOList);
    }

    /**
     * 维护包裹理论重量【预包 + 剩余明细】
     */
    private void maintainPackageWeight(PretreatmentContext context) {
        if (ObjectUtil.isEmpty(context) || CollectionUtil.isEmpty(context.getPackageDTOList())) return;
        // 计算包裹理论重量
        context.getPackageDTOList().forEach(packageDTO -> {
            if (CollectionUtil.isEmpty(packageDTO.getListDetail())) return;
            //包裹理论重量信息
            packageDTO.setWeight(
                    packageDTO.getListDetail().stream()
                            .filter(packageDetailDTO -> packageDetailFilter(packageDTO, packageDetailDTO))
                            .flatMap(a -> {
                                SkuDTO skuDTO = skuDTOList(context).stream()
                                        .filter(it -> it.getCargoCode().equalsIgnoreCase(a.getCargoCode()) && it.getCode().equalsIgnoreCase(a.getSkuCode()))
                                        .findFirst().orElse(null);
                                if (skuDTO == null) {
                                    return Stream.empty();
                                }
                                return Stream.of(a.getSkuQty().multiply(skuDTO.getGrossWeight()));
                            }).reduce(BigDecimal.ZERO, BigDecimal::add)
            );
        });
    }

    private boolean packageDetailFilter(PackageDTO packageDTO, PackageDetailDTO packageDetailDTO) {
        if (PackEnum.TYPE.PRE.getCode().equalsIgnoreCase(packageDTO.getIsPre())) {
            return PackIsPreEnum.PRE.getCode().equalsIgnoreCase(packageDetailDTO.getIsPre()) || PackIsPreEnum.LAST.getCode().equalsIgnoreCase(packageDetailDTO.getIsPre());
        }
        return PackIsPreEnum.NORMAL.getCode().equalsIgnoreCase(packageDetailDTO.getIsPre());
    }

    /**
     * 维护包裹理论体积【预包 + 剩余明细】
     */
    private void maintainPackageVolume(PretreatmentContext context) {
        if (ObjectUtil.isEmpty(context) || CollectionUtil.isEmpty(context.getPackageDTOList())) return;

        // 计算包裹理论体积
        context.getPackageDTOList().forEach(packageDTO -> {
            if (CollectionUtil.isEmpty(packageDTO.getListDetail())) return;
            //包裹理论重量信息
            packageDTO.setVolume(
                    packageDTO.getListDetail().stream()
                            .filter(packageDetailDTO -> packageDetailFilter(packageDTO, packageDetailDTO))
                            .flatMap(a -> {
                                SkuDTO skuDTO = skuDTOList(context).stream()
                                        .filter(it -> it.getCargoCode().equalsIgnoreCase(a.getCargoCode()) && it.getCode().equalsIgnoreCase(a.getSkuCode()))
                                        .findFirst().orElse(null);
                                if (skuDTO == null) {
                                    return Stream.empty();
                                }
                                BigDecimal _bigDecimal = a.getSkuQty().multiply(skuDTO.getLength().multiply(skuDTO.getWidth()).multiply(skuDTO.getHeight()));
                                if (_bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
                                    _bigDecimal = skuDTO.getVolume();
                                }
                                return Stream.of(_bigDecimal == null ? BigDecimal.ZERO : _bigDecimal);
                            }).reduce(BigDecimal.ZERO, BigDecimal::add));
            remotePackageClient.handlePackageVolume(packageDTO);
        });
    }
}
