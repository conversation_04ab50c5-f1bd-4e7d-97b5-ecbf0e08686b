package com.dt.platform.wms.biz.mq.rocket.listener.rs;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.dt.component.canal.mq.AbstractCanalMQService;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 销退单通知CCS
 *
 * <AUTHOR>
 */
@Service
@RocketMQMessageListener(topic = "dt_wms_sales_return_order_topic", consumerGroup = "rs_sales_return_order_notify_ccs")
@Slf4j
public class RSSalesReturnOrderNotifyCCSListener extends AbstractCanalMQService<SalesReturnOrderDTO> implements RocketMQListener<FlatMessage> {

    @Resource
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }


    @Override
    protected void insert(SalesReturnOrderDTO salesReturnOrderDTO) {

    }

    @Override
    protected void update(SalesReturnOrderDTO before, SalesReturnOrderDTO after) {
        remoteSalesReturnOrderClient.syncToCCSMessage(after);
    }

    @Override
    protected void delete(SalesReturnOrderDTO salesReturnOrderDTO) {

    }
}
