package com.dt.platform.wms.client.carryover;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.cds.out.api.InventoryOrderRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.CarryOverBatchCreateReqVo;
import com.danding.cds.out.bean.vo.req.CarryOverCreateReqVo;
import com.danding.cds.out.bean.vo.req.CarryOverDetailReqVo;
import com.danding.cds.out.bean.vo.res.ReceiveCarryOverResVO;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.component.common.enums.allocation.AllocationIsPreEnum;
import com.dt.component.common.enums.asn.AsnOrderTagEnum;
import com.dt.component.common.enums.asn.AsnTypeEnum;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.cw.CWTransferStatusEnum;
import com.dt.component.common.enums.cw.CWTransferTypeEnum;
import com.dt.component.common.enums.cw.FirstLineEnum;
import com.dt.component.common.enums.cw.StorageApplicationCabinetStatusEnum;
import com.dt.component.common.enums.finance.FinanceDisposalStatusEnum;
import com.dt.component.common.enums.finance.FinanceRedeemStatusEnum;
import com.dt.component.common.enums.finance.FinanceSupervisionStatusEnum;
import com.dt.component.common.enums.move.MoveStatusEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.pkg.PackReturnEnum;
import com.dt.component.common.enums.pre.BillLogTypeEnum;
import com.dt.component.common.enums.pre.PrePackagePlanStatusEnum;
import com.dt.component.common.enums.pre.PrePackagePlanTypeEnum;
import com.dt.component.common.enums.pre.SkuIsPreEnum;
import com.dt.component.common.enums.shelf.ShelfStatusEnum;
import com.dt.component.common.enums.shelf.ShelfTypeEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.enums.tally.TallyStatusEnum;
import com.dt.component.common.enums.transfer.TransferStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.LocationDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.dto.ZoneDTO;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.base.param.LocationParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.base.param.ZoneParam;
import com.dt.domain.bill.bo.carryover.BookCarryoverBO;
import com.dt.domain.bill.bo.carryover.BookCarryoverControlContext;
import com.dt.domain.bill.client.carryover.IBookCarryoverClient;
import com.dt.domain.bill.client.carryover.IBookCarryoverDetailClient;
import com.dt.domain.bill.client.carryover.IBookCarryoverReceiptClient;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.carryover.*;
import com.dt.domain.bill.dto.cw.*;
import com.dt.domain.bill.dto.finance.*;
import com.dt.domain.bill.dto.pre.*;
import com.dt.domain.bill.dto.tally.TallyDTO;
import com.dt.domain.bill.enums.BookCarryoverEndorsementStatusEnum;
import com.dt.domain.bill.enums.BookCarryoverStatusEnum;
import com.dt.domain.bill.enums.BookCarryoverTaskStatusEnum;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.carryover.*;
import com.dt.domain.bill.param.cw.*;
import com.dt.domain.bill.param.finance.*;
import com.dt.domain.bill.param.pre.*;
import com.dt.domain.bill.param.tally.TallyParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.utils.LambdaHelpUtils;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.config.UrlConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.config.WmsTenantWarehouseHelper;
import com.dt.platform.wms.biz.dto.PhysicalPartitionBindDTO;
import com.dt.platform.wms.dto.carryover.BookCarryoverBizDTO;
import com.dt.platform.wms.dto.carryover.BookCarryoverDetailBizDTO;
import com.dt.platform.wms.dto.carryover.BookCarryoverOriginDetailBizDTO;
import com.dt.platform.wms.dto.carryover.BookCarryoverReceiptBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.carryover.IRemoteBookCarryoverOriginDetailClient;
import com.dt.platform.wms.integration.cw.*;
import com.dt.platform.wms.integration.finance.*;
import com.dt.platform.wms.integration.impl.CallOtherSupport;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.pre.*;
import com.dt.platform.wms.integration.tally.IRemoteTallyClient;
import com.dt.platform.wms.param.carryover.BookCarryoverBizParam;
import com.dt.platform.wms.param.carryover.BookCarryoverOriginDetailBizParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.kafka.common.errors.IllegalSaslStateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <p>
 * 账册结转单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class BookCarryoverBizClientImpl implements IBookCarryoverBizClient {

    @DubboReference
    private IBookCarryoverClient bookCarryoverClient;

    @DubboReference
    private IBookCarryoverDetailClient bookCarryoverDetailClient;

    @DubboReference
    private IBookCarryoverReceiptClient bookCarryoverReceiptClient;

    @Resource
    private IRemoteMoveClient remoteMoveClient;

    @Resource
    private IRemoteShelfClient remoteShelfClient;

    @Resource
    private IRemoteZoneClient remoteZoneClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteReceiptClient remoteReceiptClient;

    @DubboReference
    private InventoryOrderRpc inventoryOrderRpc;

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    private IRemoteTransferClient remoteTransferClient;

    @Resource
    private IRemoteLocationClient remoteLocationClient;

    @Resource
    private IRemotePrePackagePlanClient remotePrePackagePlanClient;

    @Resource
    private IRemotePrePackagePlanDetailClient remotePrePackagePlanDetailClient;

    @Resource
    private IRemotePackageClient remotePackageClient;

    @Resource
    private IRemoteAllocationOrderClient remoteAllocationOrderClient;

    @Resource
    private IRemotePrePackageShelfClient remotePrePackageShelfClient;

    @Resource
    private IRemotePrePackageShelfDetailClient remotePrePackageShelfDetailClient;

    @Resource
    private IRemotePrePackageSkuDetailClient remotePrePackageSkuDetailClient;

    @Resource
    private IRemotePackageReturnClient remotePackageReturnClient;

    @Resource
    private IRemoteTallyClient remoteTallyClient;

    @Resource
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    private IRemoteBillLogClient remoteBillLogClient;

    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private UrlConfig urlConfig;

    @Resource
    private WmsTenantWarehouseHelper wmsTenantWarehouseHelper;

    @Resource
    private IRemoteAsnClient remoteAsnClient;

    @Autowired
    private IRemoteFinanceSupervisionClient remoteFinanceSupervisionClient;
    @Autowired
    private IRemoteFinanceSupervisionMoveDetailClient remoteFinanceSupervisionMoveDetailClient;

    @Autowired
    private IRemoteFinanceRedeemClient remoteFinanceRedeemClient;
    @Autowired
    private IRemoteFinanceRedeemMoveDetailClient remoteFinanceRedeemMoveDetailClient;

    @Autowired
    private IRemoteFinanceDisposalClient remoteFinanceDisposalClient;
    @Autowired
    private IRemoteFinanceDisposalMoveDetailClient remoteFinanceDisposalMoveDetailClient;

    @Autowired
    private IRemoteStorageApplicationCabinetClient remoteStorageApplicationCabinetClient;

    @Autowired
    private IRemoteStorageApplicationCollectClient remoteStorageApplicationCollectClient;

    @Autowired
    private IRemoteStorageApplicationClient remoteStorageApplicationClient;

    @Autowired
    private IRemoteCwTransferClient remoteCwTransferClient;
    @Autowired
    private IRemoteCwTransferCollectClient remoteCwTransferCollectClient;

    @Autowired
    private WmsOtherConfig wmsOtherConfig;

    @Autowired
    private IRemoteBookCarryoverOriginDetailClient remoteBookCarryoverOriginDetailClient;

    @Override
    public Result<Boolean> genBookCarryoverTask() {
        for (int i = 0; i < 10; i++) {
            Result<Boolean> booleanResult = bookCarryoverClient.genBookCarryoverTask();
            if (booleanResult.checkSuccess()) break;
        }
        processBookCarryoverTask();
        return Result.success(true);
    }

    @Override
    public Result<Boolean> processBookCarryoverTask() {
        BookCarryoverTaskDTO taskDTO = bookCarryoverClient.getBookCarryoverTaskWaitProcess().getData();
        if (ObjectUtil.isEmpty(taskDTO)) return Result.success(true);
        // B2B任务不在这里处理
        if (taskDTO.getStatisticStartTime().equals(0L) && taskDTO.getStatisticEndTime().equals(0L))
            return Result.success(true);
        // 判断是否超时
        timeoutCheck(taskDTO);
        if (BookCarryoverTaskStatusEnum.DONE.getCode().equals(taskDTO.getStatus())) return Result.success(true);
        // 生成结转明细
        moveDetail(taskDTO);
        storageApplication(taskDTO);
        cwTransfer(taskDTO);
        transferDetail(taskDTO);
        shelfDetail(taskDTO);
        splitShelfDetail(taskDTO);
        prePackageShelfDetail(taskDTO);
        outStockDetail(taskDTO);
        homing(taskDTO);
        homingShelf(taskDTO);
        sameUserSameWarehouse(taskDTO);

        // 生成结转单
        List<String> lines = genBookCarryover(taskDTO);

        // 同步
        for (String line : lines) {
            sync(line);
        }

        return Result.success(true);
    }

    private void timeoutCheck(BookCarryoverTaskDTO taskDTO) {
        try {
            if (System.currentTimeMillis() - taskDTO.getCreatedTime() > 3600000) {
                WechatUtil.sendMessage("请检查结转处理任务是否超时" + taskDTO.getWarehouseCode(), defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
            }
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
        }
    }

    @Override
    public Result<Boolean> retryBookCarryover(String carryoverCode) {
        if (null == carryoverCode) return Result.failWithMessage("结转单编码不能为空");
        BookCarryoverParam bookCarryoverParam = new BookCarryoverParam();
        bookCarryoverParam.setCarryoverCode(carryoverCode);
        BookCarryoverDTO bookCarryoverDTO = bookCarryoverClient.get(bookCarryoverParam).getData();
        if (null == bookCarryoverDTO) {
            return Result.failWithMessage("没有查询到对应的结转单");
        }

        BookCarryoverDetailParam bookCarryoverDetailParam = new BookCarryoverDetailParam();
        bookCarryoverDetailParam.setCarryoverCode(bookCarryoverDTO.getCarryoverCode());
        List<BookCarryoverDetailDTO> carryoverDetailDTOList = bookCarryoverClient.getDetail(bookCarryoverDetailParam).getData();
        if (CollectionUtil.isEmpty(carryoverDetailDTOList)) {
            return Result.failWithMessage("结转单没有明细");
        }

        // 没有料号
        List<String> cargoCodeList = carryoverDetailDTOList.stream().map(BookCarryoverDetailDTO::getCargoCode).distinct().collect(Collectors.toList());
        List<BookCarryoverDetailDTO> emptyItemCodeList = carryoverDetailDTOList.stream().filter(bookCarryoverDetailDTO -> StrUtil.isBlank(bookCarryoverDetailDTO.getItemCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(emptyItemCodeList)) {
            List<String> skuCodeList = emptyItemCodeList.stream().map(BookCarryoverDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
            SkuParam skuParam = new SkuParam();
            skuParam.setCodeList(skuCodeList);
            skuParam.setCargoCodeList(cargoCodeList);
            List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
            Map<String, List<SkuDTO>> skuGroup = skuDTOList.stream().collect(Collectors.groupingBy(SkuDTO::getCode));
            for (BookCarryoverDetailDTO bookCarryoverDetailDTO : emptyItemCodeList) {
                List<SkuDTO> list = skuGroup.get(bookCarryoverDetailDTO.getSkuCode());
                if (CollectionUtil.isEmpty(list)) {
                    continue;
                }
                list.stream().filter(skuDTO -> skuDTO.getCargoCode().equals(bookCarryoverDetailDTO.getCargoCode()))
                        .findFirst().ifPresent(skuDTO -> bookCarryoverDetailDTO.setItemCode(skuDTO.getItemCode()));
            }
        }

        // 来源企业、目标企业
        fillFirewallZoneAndEnterprise(carryoverDetailDTOList);

        List<BookCarryoverDetailDTO> bookCarryoverDetailDTOList = mergeDetailList(carryoverDetailDTOList);
        for (BookCarryoverDetailDTO bookCarryoverDetailDTO : carryoverDetailDTOList) {
            bookCarryoverDetailDTO.setCarryoverCode("");
        }

        if (carryoverDetailDTOList.stream().allMatch(bookCarryoverDetailDTO -> StrUtil.isNotBlank(bookCarryoverDetailDTO.getItemCode()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginEnterprise()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetEnterprise()))) {
            bookCarryoverDTO.setStatus(BookCarryoverStatusEnum.CREATED.getCode());
        } else {
            warning(bookCarryoverDTO.getWarehouseCode(), "结转单" + bookCarryoverDTO.getCarryoverCode() + "有异常未正确转换，请查看结转单明细中【统一料号、来源企业、目标企业】为空的数据，处理后重试");
        }

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        bookCarryoverDTO.setOriginEnterprise(carryoverDetailDTOList.get(0).getOriginEnterprise());
        bookCarryoverDTO.setTargetEnterprise(carryoverDetailDTOList.get(0).getTargetEnterprise());
        bookCarryoverBO.setBookCarryoverDTO(bookCarryoverDTO);
        bookCarryoverBO.setBookCarryoverDetailDTOList(Stream.of(bookCarryoverDetailDTOList, carryoverDetailDTOList).flatMap(Collection::stream).collect(Collectors.toList()));
        bookCarryoverClient.carryoverPersistence(bookCarryoverBO);

        if (BookCarryoverStatusEnum.CREATED.getCode().equals(bookCarryoverDTO.getStatus())) {
            BillLogDTO billLogDTO = billLogDTO(bookCarryoverDTO, "结转单重试");
            remoteBillLogClient.save(billLogDTO);
        }

        // 同步
        sync(carryoverCode);

        return Result.success(true);
    }

    /**
     * 生成结转单
     */
    private List<String> genBookCarryover(BookCarryoverTaskDTO taskDTO) {
        List<String> list = new ArrayList<>();
        if (ObjectUtil.isEmpty(taskDTO)) return list;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();

        BookCarryoverParam bookCarryoverParam = new BookCarryoverParam();
        bookCarryoverParam.setTaskCode(taskDTO.getTaskCode());
        Result<Page<BookCarryoverDTO>> pageResult = bookCarryoverClient.getPage(bookCarryoverParam);
        if (CollectionUtil.isNotEmpty(pageResult.getData().getRecords())) return list; // 已经生成了结转单

        // 生成明细必须已经完成
        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getMoveDetailCreated())) return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getTransferDetailCreated())) return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getShelfDetailCreated())) return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getSplitShelfDetailCreated())) return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getPrePackageShelfDetailCreated()))
            return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getOutStockDetailCreated())) return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getHomingDetailCreated())) return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getHomingShelfDetailCreated())) return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getSameUserSameWarehouse())) return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getCwIn())) return list;
        if (!BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getCwTransfer())) return list;


        BookCarryoverDetailParam bookCarryoverDetailParam = new BookCarryoverDetailParam();
        bookCarryoverDetailParam.setTaskCode(taskDTO.getTaskCode());
        List<BookCarryoverDetailDTO> carryoverClientTaskDetail = bookCarryoverClient.getDetail(bookCarryoverDetailParam).getData();
        if (CollectionUtil.isEmpty(carryoverClientTaskDetail)) {    // 当前任务没有任何需要结转的数据
            taskDTO.setStatus(BookCarryoverTaskStatusEnum.DONE.getCode());
            BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
            bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return list;
        }

        for (BookCarryoverDetailDTO bookCarryoverDetailDTO : carryoverClientTaskDetail) {
            bookCarryoverDetailDTO.setId(null);
        }

        List<BookCarryoverDetailDTO> reducedDetailList = mergeDetailList(carryoverClientTaskDetail);
        if (CollectionUtil.isEmpty(reducedDetailList)) {    // 当前任务没有任何需要结转的数据
            taskDTO.setStatus(BookCarryoverTaskStatusEnum.DONE.getCode());
            BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
            bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return list;
        }

        List<BookCarryoverDTO> bookCarryoverDTOList = groupCarryover(reducedDetailList);
        Long statisticStartTime = taskDTO.getStatisticStartTime();
        Long statisticEndTime = taskDTO.getStatisticEndTime();
        bookCarryoverDTOList.forEach(bookCarryoverDTO -> {
            bookCarryoverDTO.setStatisticStartTime(statisticStartTime);
            bookCarryoverDTO.setStatisticEndTime(statisticEndTime);
        });

        List<BillLogDTO> billLogDTOList = bookCarryoverDTOList.stream().map(bookCarryoverDTO -> billLogDTO(bookCarryoverDTO, "结转单创建")).collect(Collectors.toList());
        remoteBillLogClient.saveBatch(billLogDTOList);

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        taskDTO.setStatus(BookCarryoverTaskStatusEnum.DONE.getCode());
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);
        bookCarryoverBO.setBookCarryoverDTOList(bookCarryoverDTOList);
        bookCarryoverBO.setBookCarryoverDetailDTOList(reducedDetailList);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);

        return bookCarryoverDTOList.stream().map(BookCarryoverDTO::getCarryoverCode).distinct().collect(Collectors.toList());
    }

    /**
     * 分组生成结转单
     */
    private List<BookCarryoverDTO> groupCarryover(List<BookCarryoverDetailDTO> bookCarryoverDetailDTOList) {
        Map<String, List<BookCarryoverDetailDTO>> carryoverGroup = bookCarryoverDetailDTOList.stream().collect(Collectors.groupingBy(bookCarryoverDetailDTO -> {
            if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginEnterprise()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetEnterprise())) {
                return StrUtil.join("#", bookCarryoverDetailDTO.getOriginEnterprise(), bookCarryoverDetailDTO.getTargetEnterprise());
            }
            if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginZoneCode()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetZoneCode())) {
                return StrUtil.join("##", bookCarryoverDetailDTO.getOriginZoneCode(), bookCarryoverDetailDTO.getTargetZoneCode());
            }
            if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginEnterprise()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetZoneCode())) {
                return StrUtil.join("###", bookCarryoverDetailDTO.getOriginEnterprise(), bookCarryoverDetailDTO.getTargetZoneCode());
            }
            if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginZoneCode()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetEnterprise())) {
                return StrUtil.join("###", bookCarryoverDetailDTO.getOriginZoneCode(), bookCarryoverDetailDTO.getTargetEnterprise());
            }
            return IdUtil.simpleUUID();
        }));
        String taskCode = bookCarryoverDetailDTOList.get(0).getTaskCode();
        List<BookCarryoverDTO> bookCarryoverDTOList = new ArrayList<>();
        for (List<BookCarryoverDetailDTO> detailDTOList : carryoverGroup.values()) {
            String carryOverCode = remoteSeqRuleClient.findSequence(SeqEnum.CARRYOVER_CODE_000001);
            BookCarryoverDTO bookCarryoverDTO = new BookCarryoverDTO();
            BookCarryoverDetailDTO bookCarryoverDetailDTO = detailDTOList.get(0);
            bookCarryoverDTO.setTaskCode(taskCode);
            bookCarryoverDTO.setWarehouseCode(bookCarryoverDetailDTO.getWarehouseCode());
            bookCarryoverDTO.setCarryoverCode(carryOverCode);
            bookCarryoverDTO.setStatus(BookCarryoverStatusEnum.CREATED.getCode());
            bookCarryoverDTO.setOriginEnterprise(bookCarryoverDetailDTO.getOriginEnterprise());
            bookCarryoverDTO.setTargetEnterprise(bookCarryoverDetailDTO.getTargetEnterprise());
            // 待创建
            if (detailDTOList.stream().anyMatch(it -> StrUtil.isBlank(it.getOriginEnterprise())
                    || StrUtil.isBlank(it.getTargetEnterprise())
                    || StrUtil.isBlank(it.getItemCode()))) {
                bookCarryoverDTO.setStatus(BookCarryoverStatusEnum.DRAFT.getCode());
                warning(bookCarryoverDTO.getWarehouseCode(), "结转单" + bookCarryoverDTO.getCarryoverCode() + "有异常未正确转换，请查看结转单明细中【统一料号、来源企业、目标企业】为空的数据，处理后重试");
            }
            detailDTOList.forEach(it -> it.setCarryoverCode(carryOverCode));
            bookCarryoverDTOList.add(bookCarryoverDTO);
        }

        return bookCarryoverDTOList;
    }

    /**
     * 合并减少中间明细
     */
    private List<BookCarryoverDetailDTO> mergeDetailList(List<BookCarryoverDetailDTO> bookCarryoverDetailDTOList) {

        log.info("before merge detail: {}", JSONUtil.toJsonStr(bookCarryoverDetailDTOList));
        // 合并、减少中间结转后的明细
        List<BookCarryoverDetailDTO> reducedDetailList = new ArrayList<>();
        // 按货主+商品分组
        Map<String, List<BookCarryoverDetailDTO>> detailGroupBySku = bookCarryoverDetailDTOList.stream().collect(Collectors.groupingBy(bookCarryoverDetailDTO -> StrUtil.join(StrUtil.COLON, bookCarryoverDetailDTO.getCargoCode(), bookCarryoverDetailDTO.getSkuCode())));
        for (List<BookCarryoverDetailDTO> detailDTOList : detailGroupBySku.values()) {
            List<BookCarryoverDetailDTO> mergedDetailList = new ArrayList<>();
            Stack<BookCarryoverDetailDTO> stack = new Stack<>();
            // 合并相同的明细
            for (BookCarryoverDetailDTO bookCarryoverDetailDTO : detailDTOList) {
                if (notBlankEquals(bookCarryoverDetailDTO.getOriginZoneCode(), bookCarryoverDetailDTO.getTargetZoneCode()))
                    continue;
                if (notBlankEquals(bookCarryoverDetailDTO.getOriginFirewallZone(), bookCarryoverDetailDTO.getTargetFirewallZone()))
                    continue;
                if (notBlankEquals(bookCarryoverDetailDTO.getOriginEnterprise(), bookCarryoverDetailDTO.getTargetEnterprise()))
                    continue;
                if (TaxTypeEnum.TYPE_DUTY_TAX.getCode().equalsIgnoreCase(bookCarryoverDetailDTO.getTaxType())) {
                    continue;
                }
                stack.push(bookCarryoverDetailDTO);
            }
            while (!stack.isEmpty()) {
                BookCarryoverDetailDTO bookCarryoverDetailDTO = stack.pop();
                BookCarryoverDetailDTO sameEnterprise = mergedDetailList.stream().filter(it -> {
                    if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginEnterprise()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetEnterprise())) {
                        return bookCarryoverDetailDTO.getOriginEnterprise().equals(it.getOriginEnterprise()) && bookCarryoverDetailDTO.getTargetEnterprise().equals(it.getTargetEnterprise());
                    } else if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginZoneCode()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetZoneCode())) {
                        return bookCarryoverDetailDTO.getOriginZoneCode().equals(it.getOriginZoneCode()) && bookCarryoverDetailDTO.getTargetZoneCode().equals(it.getTargetZoneCode());
                    } else if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginEnterprise()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetZoneCode())) {
                        return bookCarryoverDetailDTO.getOriginEnterprise().equals(it.getOriginEnterprise()) && bookCarryoverDetailDTO.getTargetZoneCode().equals(it.getTargetZoneCode());
                    } else if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginZoneCode()) && StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetEnterprise())) {
                        return bookCarryoverDetailDTO.getOriginZoneCode().equals(it.getOriginZoneCode()) && bookCarryoverDetailDTO.getTargetEnterprise().equals(it.getTargetEnterprise());
                    } else {
                        return false;
                    }
                }).findFirst().orElse(null);
                if (sameEnterprise == null) { // 没有相同的判断有没有可以合并的
                    BookCarryoverDetailDTO found = mergedDetailList.stream()
                            .filter(it -> notBlankEquals(it.getTargetZoneCode(), bookCarryoverDetailDTO.getOriginZoneCode()) ||
                                    notBlankEquals(it.getTargetEnterprise(), bookCarryoverDetailDTO.getOriginEnterprise()) ||
                                    notBlankEquals(bookCarryoverDetailDTO.getTargetZoneCode(), it.getOriginZoneCode()) ||
                                    notBlankEquals(bookCarryoverDetailDTO.getTargetEnterprise(), it.getOriginEnterprise())).findFirst().orElse(null);
                    if (null == found) {
                        if (!notBlankEquals(bookCarryoverDetailDTO.getOriginEnterprise(), bookCarryoverDetailDTO.getTargetEnterprise())) {
                            if (bookCarryoverDetailDTO.getChangeQty().compareTo(BigDecimal.ZERO) > 0) {
                                BookCarryoverDetailDTO detailDTO = new BookCarryoverDetailDTO();
                                BeanUtil.copyProperties(bookCarryoverDetailDTO, detailDTO);
                                detailDTO.setId(null);
                                mergedDetailList.add(detailDTO);
                            }
                        }
                    } else {
                        if (notBlankEquals(found.getTargetEnterprise(), bookCarryoverDetailDTO.getOriginEnterprise())
                                || notBlankEquals(found.getTargetZoneCode(), bookCarryoverDetailDTO.getOriginZoneCode())) {
                            if (found.getChangeQty().compareTo(bookCarryoverDetailDTO.getChangeQty()) == 0) {
                                mergedDetailList.remove(found);
                                found.setTargetZoneCode(bookCarryoverDetailDTO.getTargetZoneCode());
                                found.setTargetFirewallZone(bookCarryoverDetailDTO.getTargetFirewallZone());
                                found.setTargetEnterprise(bookCarryoverDetailDTO.getTargetEnterprise());
                                stack.push(found);
                            } else if (found.getChangeQty().compareTo(bookCarryoverDetailDTO.getChangeQty()) > 0) {
                                BookCarryoverDetailDTO newDetail = new BookCarryoverDetailDTO();
                                BeanUtil.copyProperties(found, newDetail);
                                newDetail.setChangeQty(bookCarryoverDetailDTO.getChangeQty());
                                newDetail.setTargetZoneCode(bookCarryoverDetailDTO.getTargetZoneCode());
                                newDetail.setTargetFirewallZone(bookCarryoverDetailDTO.getTargetFirewallZone());
                                newDetail.setTargetEnterprise(bookCarryoverDetailDTO.getTargetEnterprise());
                                stack.push(newDetail);
                                found.setChangeQty(found.getChangeQty().subtract(bookCarryoverDetailDTO.getChangeQty()));
                            } else {
                                mergedDetailList.remove(found);
                                found.setTargetZoneCode(bookCarryoverDetailDTO.getTargetZoneCode());
                                found.setTargetFirewallZone(bookCarryoverDetailDTO.getTargetFirewallZone());
                                found.setTargetEnterprise(bookCarryoverDetailDTO.getTargetEnterprise());
                                bookCarryoverDetailDTO.setChangeQty(bookCarryoverDetailDTO.getChangeQty().subtract(found.getChangeQty()));
                                stack.push(bookCarryoverDetailDTO);
                                stack.push(found);
                            }
                        } else if (notBlankEquals(bookCarryoverDetailDTO.getTargetEnterprise(), found.getOriginEnterprise())
                                || notBlankEquals(bookCarryoverDetailDTO.getTargetZoneCode(), found.getOriginZoneCode())) {
                            if (found.getChangeQty().compareTo(bookCarryoverDetailDTO.getChangeQty()) == 0) {
                                mergedDetailList.remove(found);
                                found.setOriginZoneCode(bookCarryoverDetailDTO.getOriginZoneCode());
                                found.setOriginFirewallZone(bookCarryoverDetailDTO.getOriginFirewallZone());
                                found.setOriginEnterprise(bookCarryoverDetailDTO.getOriginEnterprise());
                                stack.push(found);
                            } else if (found.getChangeQty().compareTo(bookCarryoverDetailDTO.getChangeQty()) > 0) {
                                BookCarryoverDetailDTO newDetail = new BookCarryoverDetailDTO();
                                BeanUtil.copyProperties(bookCarryoverDetailDTO, newDetail);
                                newDetail.setChangeQty(bookCarryoverDetailDTO.getChangeQty());
                                newDetail.setTargetZoneCode(found.getTargetZoneCode());
                                newDetail.setTargetFirewallZone(found.getTargetFirewallZone());
                                newDetail.setTargetEnterprise(found.getTargetEnterprise());
                                stack.push(newDetail);
                                found.setChangeQty(found.getChangeQty().subtract(bookCarryoverDetailDTO.getChangeQty()));
                            } else {
                                mergedDetailList.remove(found);
                                found.setOriginZoneCode(bookCarryoverDetailDTO.getOriginZoneCode());
                                found.setOriginFirewallZone(bookCarryoverDetailDTO.getOriginFirewallZone());
                                found.setOriginEnterprise(bookCarryoverDetailDTO.getOriginEnterprise());
                                bookCarryoverDetailDTO.setChangeQty(bookCarryoverDetailDTO.getChangeQty().subtract(found.getChangeQty()));
                                stack.push(bookCarryoverDetailDTO);
                                stack.push(found);
                            }
                        } else {
                            throw new IllegalStateException();
                        }
                    }
                } else {
                    sameEnterprise.setChangeQty(sameEnterprise.getChangeQty().add(bookCarryoverDetailDTO.getChangeQty())); // 相同的合并
                }
            }

            reducedDetailList.addAll(mergedDetailList);
        }

        reducedDetailList = reducedDetailList.stream()
                .filter(bookCarryoverDetailDTO -> !notBlankEquals(bookCarryoverDetailDTO.getOriginEnterprise(), bookCarryoverDetailDTO.getTargetEnterprise()))
                .collect(Collectors.toList());

        log.info("after merge detail: {}", JSONUtil.toJsonStr(reducedDetailList));
        return reducedDetailList;
    }

    /**
     * 同步结转单到CCS
     */
    @Override
    public void sync() {
        syncB2B(ListUtil.empty()); // 同步B单出库的结转单
        syncStatistic(ListUtil.empty()); // 同步统计生成的结转单
    }

    @Override
    public void sync(String carryoverCode) {
        syncB2B(ListUtil.toList(carryoverCode));
        syncStatistic(ListUtil.toList(carryoverCode));
    }

    private void syncStatistic(List<String> carryoverCodeList) {
        BookCarryoverParam bookCarryoverParam = new BookCarryoverParam();
        bookCarryoverParam.setStatus(BookCarryoverStatusEnum.CREATED.getCode());
        bookCarryoverParam.setCarryoverCodeList(carryoverCodeList);
        if (CollectionUtil.isEmpty(carryoverCodeList)) {
            bookCarryoverParam.setCreatedTimeEnd(DateUtil.offset(DateTime.now(), DateField.MINUTE, -5).getTime());
        }
        bookCarryoverParam.setPageSize(500);
        Result<Page<BookCarryoverDTO>> page = bookCarryoverClient.getPage(bookCarryoverParam);
        if (page.checkSuccess()) {
            for (BookCarryoverDTO bookCarryoverDTO : page.getData().getRecords()) {
                if (StrUtil.isNotBlank(bookCarryoverDTO.getBillNo())) continue; // B单出库的不在这里面处理
                int autoCreateEndorsement = 0;
                if (CollectionUtil.isNotEmpty(defaultWarehouseCodeConfig.getAutoCreateEndorsementWarehouseCodeList()) && defaultWarehouseCodeConfig.getAutoCreateEndorsementWarehouseCodeList().contains(bookCarryoverDTO.getWarehouseCode())) {
                    autoCreateEndorsement = 1;
                }

                CarryOverBatchCreateReqVo carryOverBatchCreateReqVo = new CarryOverBatchCreateReqVo();
                carryOverBatchCreateReqVo.setWarehouseCode(bookCarryoverDTO.getWarehouseCode());
                carryOverBatchCreateReqVo.setInOutOrderNo("");
                carryOverBatchCreateReqVo.setAutoCreateEndorsement(autoCreateEndorsement);
                carryOverBatchCreateReqVo.setCarryOverCreateReqVoList(new ArrayList<>());

                CarryOverCreateReqVo carryOverCreateReqVo = new CarryOverCreateReqVo();
                carryOverCreateReqVo.setWarehouseCode(bookCarryoverDTO.getWarehouseCode());
                carryOverCreateReqVo.setCarryOverNo(bookCarryoverDTO.getCarryoverCode());
                carryOverCreateReqVo.setOriginEnterprise(bookCarryoverDTO.getOriginEnterprise());
                carryOverCreateReqVo.setTargetEnterprise(bookCarryoverDTO.getTargetEnterprise());
                carryOverCreateReqVo.setAutoCreateEndorsement(autoCreateEndorsement);
                carryOverBatchCreateReqVo.getCarryOverCreateReqVoList().add(carryOverCreateReqVo);

                carryOverCreateReqVo.setCarryOverDetailList(new ArrayList<>());
                BookCarryoverDetailParam detailParam = new BookCarryoverDetailParam();
                detailParam.setCarryoverCode(bookCarryoverDTO.getCarryoverCode());
                List<BookCarryoverDetailDTO> detailList = bookCarryoverClient.getDetail(detailParam).getData();

                if (CollectionUtil.isEmpty(detailList)) { // 没有明细且关联单号为空不需要同步到CCS 
                    BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
                    bookCarryoverDTO.setStatus(BookCarryoverStatusEnum.DONE.getCode());
                    bookCarryoverBO.setBookCarryoverDTO(bookCarryoverDTO);
                    bookCarryoverClient.carryoverPersistence(bookCarryoverBO);
                    return;
                }

                for (BookCarryoverDetailDTO bookCarryoverDetailDTO : detailList) {
                    CarryOverDetailReqVo carryOverDetail = new CarryOverDetailReqVo();
                    carryOverDetail.setSku(bookCarryoverDetailDTO.getSkuCode());
                    carryOverDetail.setChangeQty(bookCarryoverDetailDTO.getChangeQty());
                    carryOverDetail.setBarCode(bookCarryoverDetailDTO.getUpcCode());
                    carryOverDetail.setName(bookCarryoverDetailDTO.getSkuName());
                    carryOverDetail.setProductId(bookCarryoverDetailDTO.getItemCode());
                    carryOverCreateReqVo.getCarryOverDetailList().add(carryOverDetail);
                }

                try {
                    wmsTenantWarehouseHelper.setTenantId(bookCarryoverDTO.getWarehouseCode());
                    CurrentRouteHolder.setWarehouseCode(bookCarryoverDTO.getWarehouseCode());

                    log.info("call ccs for sync carryover param {}", JSONUtil.toJsonStr(carryOverBatchCreateReqVo));
                    RpcResult<List<ReceiveCarryOverResVO>> result = inventoryOrderRpc.receiveCarryOverDetailBatch(carryOverBatchCreateReqVo);
                    if (result.getCode().equals(200)) {
                        List<ReceiveCarryOverResVO> receiveCarryOverResVOList = result.getData();
                        List<BookCarryoverReceiptDTO> bookCarryoverReceiptDTOList = new ArrayList<>();
                        for (ReceiveCarryOverResVO receiveCarryOverResVO : receiveCarryOverResVOList) {
                            BookCarryoverReceiptDTO bookCarryoverReceiptDTO = new BookCarryoverReceiptDTO();
                            bookCarryoverReceiptDTO.setWarehouseCode(bookCarryoverDTO.getWarehouseCode());
                            bookCarryoverReceiptDTO.setCarryoverCode(receiveCarryOverResVO.getCarryOverNo());
                            bookCarryoverReceiptDTO.setInventoryOrderSn(receiveCarryOverResVO.getInventoryOrderSn());
                            bookCarryoverReceiptDTO.setEndorsementSn(receiveCarryOverResVO.getEndorsementSn());
                            bookCarryoverReceiptDTO.setEndorsementType(receiveCarryOverResVO.getBusinessType());
                            String endorsementStatus = BookCarryoverEndorsementStatusEnum.WAIT.getCode();
                            if (null != receiveCarryOverResVO.getEndorsementStatus()) {
                                endorsementStatus = String.valueOf(receiveCarryOverResVO.getEndorsementStatus());
                            }
                            bookCarryoverReceiptDTO.setEndorsementStatus(endorsementStatus);
                            if (null != receiveCarryOverResVO.getEndorsementStatus()) {
                                bookCarryoverReceiptDTO.setEndorsementStatus(String.valueOf(receiveCarryOverResVO.getEndorsementStatus()));
                            }
                            bookCarryoverReceiptDTOList.add(bookCarryoverReceiptDTO);
                        }

                        if (CollectionUtil.isEmpty(detailList)) { // 没有结转明细直接把状态改成已完成
                            bookCarryoverDTO.setStatus(BookCarryoverStatusEnum.DONE.getCode());
                        } else {
                            bookCarryoverDTO.setStatus(BookCarryoverStatusEnum.WAIT.getCode());
                        }
                        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
                        bookCarryoverBO.setBookCarryoverDTO(bookCarryoverDTO);
                        bookCarryoverBO.setBookCarryoverReceiptDTOList(bookCarryoverReceiptDTOList);
                        bookCarryoverClient.carryoverPersistence(bookCarryoverBO);

                        remoteBillLogClient.save(billLogDTO(bookCarryoverDTO, "同步CCS成功"));
                    } else {
                        warning(bookCarryoverDTO.getWarehouseCode(), result.getMessage() + String.format("%s（出）转 %s（入）", bookCarryoverDTO.getOriginEnterprise(), bookCarryoverDTO.getTargetEnterprise()));
                        remoteBillLogClient.save(billLogDTO(bookCarryoverDTO, result.getMessage()));
                    }
                    log.info("call ccs for sync carryover result {}", JSONUtil.toJsonStr(result));
                } catch (Exception exception) {
                    remoteBillLogClient.save(billLogDTO(bookCarryoverDTO, "系统异常，请重试"));
                    log.info("call ccs for sync carryover exception {}", JSONUtil.toJsonStr(exception.getMessage()));
                }
            }
        }

    }

    private void syncB2B(List<String> carryoverCodeList) {
        BookCarryoverParam bookCarryoverParam = new BookCarryoverParam();
        bookCarryoverParam.setCarryoverCodeList(carryoverCodeList);
        if (CollectionUtil.isEmpty(carryoverCodeList)) {
            bookCarryoverParam.setCreatedTimeEnd(DateUtil.offset(DateTime.now(), DateField.MINUTE, -5).getTime());
        }
        bookCarryoverParam.setStatus(BookCarryoverStatusEnum.CREATED.getCode());
        bookCarryoverParam.setPageSize(500);
        Result<Page<BookCarryoverDTO>> page = bookCarryoverClient.getPage(bookCarryoverParam);
        if (page.checkSuccess()) {
            List<String> billNoList = page.getData().getRecords().stream().map(BookCarryoverDTO::getBillNo)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(billNoList)) {
                BookCarryoverParam carryoverParam = new BookCarryoverParam();
                carryoverParam.setBillNoList(billNoList);
                carryoverParam.setStatus(BookCarryoverStatusEnum.CREATED.getCode());
                List<BookCarryoverDTO> bookCarryoverDTOList = bookCarryoverClient.getList(carryoverParam).getData();
                if (CollectionUtil.isEmpty(bookCarryoverDTOList)) return;

                Map<String, List<BookCarryoverDTO>> groupByBillNo = bookCarryoverDTOList.stream().collect(Collectors.groupingBy(BookCarryoverDTO::getBillNo));
                groupByBillNo.forEach(this::syncB2B);

                // todo
//                processOriginDetailAfterSync();
            }
        }
    }

    private void syncB2B(String billNo, List<BookCarryoverDTO> bookCarryoverDTOList) {
        try {
            BookCarryoverDTO firstOne = bookCarryoverDTOList.get(0);
            BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
            bookCarryoverTaskParam.setTaskCode(firstOne.getTaskCode());
            BookCarryoverTaskDTO bookCarryoverTaskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
            if (null == bookCarryoverTaskDTO) throw new IllegalSaslStateException("");

            CarryOverBatchCreateReqVo carryOverBatchCreateReqVo = new CarryOverBatchCreateReqVo();
            carryOverBatchCreateReqVo.setInOutOrderNo(billNo);
            carryOverBatchCreateReqVo.setWarehouseCode(firstOne.getWarehouseCode());
            carryOverBatchCreateReqVo.setAutoCreateEndorsement(0);
            carryOverBatchCreateReqVo.setCarryOverCreateReqVoList(new ArrayList<>());

            BookCarryoverDetailParam detailParam = new BookCarryoverDetailParam();
            detailParam.setCarryoverCodeList(bookCarryoverDTOList.stream().map(BookCarryoverDTO::getCarryoverCode).collect(Collectors.toList()));
            List<BookCarryoverDetailDTO> bookCarryoverDetailDTOList = bookCarryoverClient.getDetail(detailParam).getData();

            bookCarryoverDTOList.forEach(bookCarryoverDTO -> {
                CarryOverCreateReqVo carryOverCreateReqVo = new CarryOverCreateReqVo();
                carryOverCreateReqVo.setWarehouseCode(bookCarryoverDTO.getWarehouseCode());
                carryOverCreateReqVo.setCarryOverNo(bookCarryoverDTO.getCarryoverCode());
                carryOverCreateReqVo.setAutoCreateEndorsement(0);
                carryOverCreateReqVo.setInOutOrderNo(bookCarryoverDTO.getBillNo());
                carryOverCreateReqVo.setOriginEnterprise(bookCarryoverDTO.getOriginEnterprise());
                carryOverCreateReqVo.setTargetEnterprise(bookCarryoverDTO.getTargetEnterprise());
                carryOverCreateReqVo.setCarryOverDetailList(new ArrayList<>());

                bookCarryoverDetailDTOList.stream().filter(bookCarryoverDetailDTO -> bookCarryoverDetailDTO.getCarryoverCode().equals(bookCarryoverDTO.getCarryoverCode()))
                        .forEach(bookCarryoverDetailDTO -> {
                            CarryOverDetailReqVo carryOverDetail = new CarryOverDetailReqVo();
                            carryOverDetail.setSku(bookCarryoverDetailDTO.getSkuCode());
                            carryOverDetail.setChangeQty(bookCarryoverDetailDTO.getChangeQty());
                            carryOverDetail.setBarCode(bookCarryoverDetailDTO.getUpcCode());
                            carryOverDetail.setName(bookCarryoverDetailDTO.getSkuName());
                            carryOverDetail.setProductId(bookCarryoverDetailDTO.getItemCode());
                            carryOverCreateReqVo.getCarryOverDetailList().add(carryOverDetail);
                        });
                carryOverBatchCreateReqVo.getCarryOverCreateReqVoList().add(carryOverCreateReqVo);
            });

            wmsTenantWarehouseHelper.setTenantId(firstOne.getWarehouseCode());
            CurrentRouteHolder.setWarehouseCode(firstOne.getWarehouseCode());
            log.info("call ccs for sync carryover param {}", JSONUtil.toJsonStr(carryOverBatchCreateReqVo));
            RpcResult<List<ReceiveCarryOverResVO>> result = inventoryOrderRpc.receiveCarryOverDetailBatch(carryOverBatchCreateReqVo);
            if (result.getCode().equals(200)) {
                List<ReceiveCarryOverResVO> receiveCarryOverResVOList = result.getData();
                List<BookCarryoverReceiptDTO> bookCarryoverReceiptDTOList = new ArrayList<>();
                for (ReceiveCarryOverResVO receiveCarryOverResVO : receiveCarryOverResVOList) {
                    BookCarryoverReceiptDTO bookCarryoverReceiptDTO = new BookCarryoverReceiptDTO();
                    bookCarryoverReceiptDTO.setWarehouseCode(firstOne.getWarehouseCode());
                    bookCarryoverReceiptDTO.setCarryoverCode(receiveCarryOverResVO.getCarryOverNo());
                    bookCarryoverReceiptDTO.setInventoryOrderSn(receiveCarryOverResVO.getInventoryOrderSn());
                    bookCarryoverReceiptDTO.setEndorsementSn(receiveCarryOverResVO.getEndorsementSn());
                    bookCarryoverReceiptDTO.setEndorsementType(receiveCarryOverResVO.getBusinessType());
                    String endorsementStatus = BookCarryoverEndorsementStatusEnum.WAIT.getCode();
                    if (null != receiveCarryOverResVO.getEndorsementStatus()) {
                        endorsementStatus = String.valueOf(receiveCarryOverResVO.getEndorsementStatus());
                    }
                    bookCarryoverReceiptDTO.setEndorsementStatus(endorsementStatus);
                    bookCarryoverReceiptDTOList.add(bookCarryoverReceiptDTO);
                }

                bookCarryoverDTOList.forEach(bookCarryoverDTO -> {
                    if (CollectionUtil.isEmpty(bookCarryoverDetailDTOList)) {
                        bookCarryoverDTO.setStatus(BookCarryoverStatusEnum.DONE.getCode());
                    } else {
                        bookCarryoverDTO.setStatus(BookCarryoverStatusEnum.WAIT.getCode());
                    }
                });
                BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
                bookCarryoverBO.setBookCarryoverTaskDTO(bookCarryoverTaskDTO);
                bookCarryoverBO.setBookCarryoverDTOList(bookCarryoverDTOList);
                bookCarryoverBO.setBookCarryoverReceiptDTOList(bookCarryoverReceiptDTOList);
                bookCarryoverClient.taskPersistence(bookCarryoverBO);

                List<BillLogDTO> billLogDTOList = bookCarryoverDTOList.stream().map(bookCarryoverDTO -> billLogDTO(bookCarryoverDTO, "同步CCS成功")).collect(Collectors.toList());
                remoteBillLogClient.saveBatch(billLogDTOList);
            } else {
                for (BookCarryoverDTO bookCarryoverDTO : bookCarryoverDTOList) {
                    warning(bookCarryoverDTO.getWarehouseCode(), result.getMessage() + String.format("%s（出）转 %s（入）", bookCarryoverDTO.getOriginEnterprise(), bookCarryoverDTO.getTargetEnterprise()));
                }
                List<BillLogDTO> billLogDTOList = bookCarryoverDTOList.stream().map(bookCarryoverDTO -> billLogDTO(bookCarryoverDTO, result.getMessage())).collect(Collectors.toList());
                remoteBillLogClient.saveBatch(billLogDTOList);
            }
            log.info("call ccs for sync carryover result {}", JSONUtil.toJsonStr(result));
        } catch (Exception exception) {
            for (BookCarryoverDTO bookCarryoverDTO : bookCarryoverDTOList) {
                remoteBillLogClient.save(billLogDTO(bookCarryoverDTO, "系统异常，请重试"));
            }
            log.error(exception.getMessage(), exception);
            log.info("call ccs for sync carryover exception {}", exception.getMessage());
        }
    }

    private boolean notBlankEquals(String first, String second) {
        return StrUtil.isNotBlank(first) && first.equalsIgnoreCase(second);
    }

    /**
     * 移位结转明细【包括金融业务相关移位】
     */
    private void moveDetail(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();

        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getMoveDetailCreated())) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setMoveDetailCreated(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        // 查询移位完成时间在汇总时间段之间的移位单
        List<MoveDetailDTO> allMoveDetailDTOList = new ArrayList<>();

        MoveParam moveParam = new MoveParam();
        moveParam.setStatus(MoveStatusEnum.STATUS_COMPLETED.getStatus());
        moveParam.setCompleteDateEqStart(taskDTO.getStatisticStartTime());
        moveParam.setCompleteDateEqEnd(taskDTO.getStatisticEndTime());
        List<MoveDTO> moveDTOList = remoteMoveClient.getList(moveParam);
        if (CollectionUtil.isNotEmpty(moveDTOList)) {
            // 查询移位明细
            MoveDetailParam moveDetailParam = new MoveDetailParam();
            moveDetailParam.setMoveCodeList(moveDTOList.stream().map(MoveDTO::getCode).collect(Collectors.toList()));
            List<MoveDetailDTO> moveDetailList = remoteMoveClient.getMoveDetailList(moveDetailParam);
            if (CollectionUtil.isNotEmpty(moveDetailList)) {
                for (MoveDetailDTO moveDetailDTO : moveDetailList) {
                    moveDetailDTO.setCarryOverBillType(BillTypeEnum.BILL_TYPE_MOVE.getType());
                }
                allMoveDetailDTOList.addAll(moveDetailList);
            }
        }

        // 监管移位
        supervisionMoveDetail(allMoveDetailDTOList, taskDTO);
        // 赎回移位
        redeemMoveDetail(allMoveDetailDTOList, taskDTO);
        // 处置移位
        disposalMoveDetail(allMoveDetailDTOList, taskDTO);

        if (CollectionUtil.isEmpty(allMoveDetailDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        for (MoveDetailDTO moveDetailDTO : allMoveDetailDTOList) {
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, moveDetailDTO.getMoveCode(), moveDetailDTO.getId()));
            bookCarryoverDetailDTO.setWarehouseCode(moveDetailDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setCargoCode(moveDetailDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(moveDetailDTO.getSkuCode());
            bookCarryoverDetailDTO.setOriginZoneCode(moveDetailDTO.getOriginZoneCode());
            bookCarryoverDetailDTO.setTargetZoneCode(moveDetailDTO.getTargetZoneCode());
            bookCarryoverDetailDTO.setChangeQty(moveDetailDTO.getActualSkuQty());
            bookCarryoverDetailDTO.setBillNo(moveDetailDTO.getMoveCode());
            bookCarryoverDetailDTO.setDetailId(moveDetailDTO.getId());
            bookCarryoverDetailDTO.setBillType(moveDetailDTO.getCarryOverBillType());
            list.add(bookCarryoverDetailDTO);
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }

    @ApiOperation("CW入库申请")
    private void storageApplication(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();

        wmsTenantWarehouseHelper.setTenantId(taskDTO.getWarehouseCode());

        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getCwIn())) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setCwIn(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
        storageApplicationCabinetParam.setCollectEndTimeStart(taskDTO.getStatisticStartTime());
        storageApplicationCabinetParam.setCollectEndTimeEnd(taskDTO.getStatisticEndTime());
        storageApplicationCabinetParam.setStatus(StorageApplicationCabinetStatusEnum.COLLECTED.getCode());
        List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(storageApplicationCabinetDTOList)) {
            for (StorageApplicationCabinetDTO storageApplicationCabinetDTO : storageApplicationCabinetDTOList) {
                StorageApplicationCollectParam storageApplicationCollectParam = new StorageApplicationCollectParam();
                storageApplicationCollectParam.setStorageApplicationCode(storageApplicationCabinetDTO.getStorageApplicationCode());
                storageApplicationCollectParam.setCabinetCode(storageApplicationCabinetDTO.getCabinetCode());
                List<StorageApplicationCollectDTO> storageApplicationCollectDTOList = remoteStorageApplicationCollectClient.getList(storageApplicationCollectParam);
                if (CollectionUtil.isEmpty(storageApplicationCollectDTOList)) continue;
                LocationParam locationParam = new LocationParam();
                locationParam.setCodeList(storageApplicationCollectDTOList.stream().map(StorageApplicationCollectDTO::getLocationCode).distinct().collect(Collectors.toList()));
                List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);

                for (StorageApplicationCollectDTO collectDTO : storageApplicationCollectDTOList) {
                    BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
                    bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, collectDTO.getStorageApplicationCode(), collectDTO.getId()));
                    bookCarryoverDetailDTO.setWarehouseCode(collectDTO.getWarehouseCode());
                    bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
                    bookCarryoverDetailDTO.setCargoCode(collectDTO.getCargoCode());
                    bookCarryoverDetailDTO.setSkuCode(collectDTO.getSkuCode());
                    bookCarryoverDetailDTO.setOriginEnterprise(inventoryOrderRpc.getInvCompanyNameByInOutOrderNo(collectDTO.getReferenceNumber()).getData());
                    locationDTOList.stream()
                            .filter(it -> it.getCode().equalsIgnoreCase(collectDTO.getLocationCode()))
                            .findFirst().ifPresent(locationDTO -> bookCarryoverDetailDTO.setTargetZoneCode(locationDTO.getZoneCode()));
                    bookCarryoverDetailDTO.setChangeQty(BigDecimal.valueOf(collectDTO.getTotalCount()));
                    bookCarryoverDetailDTO.setBillNo(collectDTO.getReferenceNumber());
                    bookCarryoverDetailDTO.setDetailId(collectDTO.getId());
                    bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_CW_IN.getType());
                    bookCarryoverDetailDTO.setRemark(collectDTO.getCabinetCode());
                    list.add(bookCarryoverDetailDTO);
                }
            }
        }

        StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
        storageApplicationParam.setFirstLine(FirstLineEnum.NO.getCode());
        storageApplicationParam.setCollectEndTimeStart(taskDTO.getStatisticStartTime());
        storageApplicationParam.setCollectEndTimeEnd(taskDTO.getStatisticEndTime());
        List<StorageApplicationDTO> storageApplicationDTOList = remoteStorageApplicationClient.getList(storageApplicationParam);
        if (CollectionUtil.isNotEmpty(storageApplicationDTOList)) {
            for (StorageApplicationDTO storageApplicationDTO : storageApplicationDTOList) {
                StorageApplicationCollectParam storageApplicationCollectParam = new StorageApplicationCollectParam();
                storageApplicationCollectParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
                List<StorageApplicationCollectDTO> storageApplicationCollectDTOList = remoteStorageApplicationCollectClient.getList(storageApplicationCollectParam);
                if (CollectionUtil.isNotEmpty(storageApplicationCollectDTOList)) {
                    LocationParam locationParam = new LocationParam();
                    locationParam.setCodeList(storageApplicationCollectDTOList.stream().map(StorageApplicationCollectDTO::getLocationCode).distinct().collect(Collectors.toList()));
                    List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);

                    for (StorageApplicationCollectDTO collectDTO : storageApplicationCollectDTOList) {
                        BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
                        bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, collectDTO.getStorageApplicationCode(), collectDTO.getId()));
                        bookCarryoverDetailDTO.setWarehouseCode(collectDTO.getWarehouseCode());
                        bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
                        bookCarryoverDetailDTO.setCargoCode(collectDTO.getCargoCode());
                        bookCarryoverDetailDTO.setSkuCode(collectDTO.getSkuCode());
                        bookCarryoverDetailDTO.setOriginEnterprise(wmsOtherConfig.getDefaultCwEnterprise());
                        locationDTOList.stream()
                                .filter(it -> it.getCode().equalsIgnoreCase(collectDTO.getLocationCode()))
                                .findFirst().ifPresent(locationDTO -> bookCarryoverDetailDTO.setTargetZoneCode(locationDTO.getZoneCode()));
                        bookCarryoverDetailDTO.setChangeQty(BigDecimal.valueOf(collectDTO.getTotalCount()));
                        bookCarryoverDetailDTO.setBillNo(collectDTO.getReferenceNumber());
                        bookCarryoverDetailDTO.setDetailId(collectDTO.getId());
                        bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_CW_IN.getType());
                        list.add(bookCarryoverDetailDTO);
                    }
                }
            }
        }


        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }

    @ApiOperation("CW调拨")
    private void cwTransfer(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();

        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getCwTransfer())) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setCwTransfer(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        // 出库
        CwTransferParam transferParam = new CwTransferParam();
        transferParam.setCwTransferType(CWTransferTypeEnum.OUT.getCode());
        transferParam.setStatusList(ListUtil.toList(CWTransferStatusEnum.OUT_COMPLETE.getValue()));
        transferParam.setOutEndTimeStart(taskDTO.getStatisticStartTime());
        transferParam.setOutEndTimeEnd(taskDTO.getStatisticEndTime());
        List<CwTransferDTO> transferDTOList = remoteCwTransferClient.getList(transferParam);

        // 货权转移、移位
        CwTransferParam transferParam2 = new CwTransferParam();
        transferParam2.setCwTransferTypeList(ListUtil.toList(CWTransferTypeEnum.MOVE.getCode(), CWTransferTypeEnum.CARGO_TRANSFER.getCode()));
        transferParam2.setStatusList(ListUtil.toList(CWTransferStatusEnum.SHELF_COMPLETE.getValue()));
        transferParam2.setShelfEndTimeStart(taskDTO.getStatisticStartTime());
        transferParam2.setShelfEndTimeEnd(taskDTO.getStatisticEndTime());
        List<CwTransferDTO> transferDTOList1 = remoteCwTransferClient.getList(transferParam2);
        transferDTOList.addAll(transferDTOList1);

        if (CollectionUtil.isEmpty(transferDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        for (CwTransferDTO transferDTO : transferDTOList) {
            CwTransferCollectParam collectParam = new CwTransferCollectParam();
            collectParam.setCwTransferCode(transferDTO.getCwTransferCode());
            List<CwTransferCollectDTO> collectDTOList = remoteCwTransferCollectClient.getList(collectParam);
            if (CollectionUtil.isEmpty(collectDTOList)) continue;


            LocationParam locationParam = new LocationParam();
            locationParam.setCodeList(collectDTOList.stream().flatMap(it -> Stream.of(it.getLocationCode(), it.getTargetLocationCode())).distinct().collect(Collectors.toList()));
            List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);

            for (CwTransferCollectDTO collectDTO : collectDTOList) {
                BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
                bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, collectDTO.getCwTransferCode(), collectDTO.getId()));
                bookCarryoverDetailDTO.setWarehouseCode(collectDTO.getWarehouseCode());
                bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
                bookCarryoverDetailDTO.setCargoCode(collectDTO.getCargoCode());
                bookCarryoverDetailDTO.setSkuCode(collectDTO.getSkuCode());
                locationDTOList.stream()
                        .filter(it -> it.getCode().equalsIgnoreCase(collectDTO.getLocationCode()))
                        .findFirst().ifPresent(locationDTO -> bookCarryoverDetailDTO.setOriginZoneCode(locationDTO.getZoneCode()));
                if (CWTransferTypeEnum.OUT.getCode().equalsIgnoreCase(transferDTO.getCwTransferType())) {
                    bookCarryoverDetailDTO.setTargetEnterprise(wmsOtherConfig.getDefaultCwOutEnterprise());
                    bookCarryoverDetailDTO.setChangeQty(collectDTO.getCollectCount());

                } else {
                    bookCarryoverDetailDTO.setChangeQty(collectDTO.getShelfQty());
                    locationDTOList.stream()
                            .filter(it -> it.getCode().equalsIgnoreCase(collectDTO.getTargetLocationCode()))
                            .findFirst().ifPresent(locationDTO -> bookCarryoverDetailDTO.setTargetZoneCode(locationDTO.getZoneCode()));
                }
                bookCarryoverDetailDTO.setBillNo(transferDTO.getCwTransferNumber());
                bookCarryoverDetailDTO.setDetailId(collectDTO.getId());
                bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_CW_OUT.getType());
                list.add(bookCarryoverDetailDTO);
            }
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }


    /**
     * 金融监管移位
     */
    private void supervisionMoveDetail(List<MoveDetailDTO> moveDetailDTOList, BookCarryoverTaskDTO taskDTO) {
        // 监管移位
        FinanceSupervisionParam financeSupervisionParam = new FinanceSupervisionParam();
        financeSupervisionParam.setStatus(FinanceSupervisionStatusEnum.COMPLETE.getCode());
        financeSupervisionParam.setSupervisionTimeStart(taskDTO.getStatisticStartTime());
        financeSupervisionParam.setSupervisionTimeEnd(taskDTO.getStatisticEndTime());
        List<FinanceSupervisionDTO> financeSupervisionDTOList = remoteFinanceSupervisionClient.getList(financeSupervisionParam);
        if (CollectionUtil.isNotEmpty(financeSupervisionDTOList)) {
            FinanceSupervisionMoveDetailParam financeSupervisionMoveDetailParam = new FinanceSupervisionMoveDetailParam();
            financeSupervisionMoveDetailParam.setSupervisionCodeList(financeSupervisionDTOList.stream().map(FinanceSupervisionDTO::getSupervisionCode).collect(Collectors.toList()));
            List<FinanceSupervisionMoveDetailDTO> financeSupervisionMoveDetailDTOList = remoteFinanceSupervisionMoveDetailClient.getList(financeSupervisionMoveDetailParam);
            if (CollectionUtil.isNotEmpty(financeSupervisionMoveDetailDTOList)) {
                List<String> locationCodeList = financeSupervisionMoveDetailDTOList.stream().flatMap(it -> Stream.of(it.getOriginLocationCode(), it.getTargetLocationCode()))
                        .distinct().collect(Collectors.toList());
                LocationParam locationParam = new LocationParam();
                locationParam.setCodeList(locationCodeList);
                List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
                Map<String, String> locationZoneMap = locationDTOList.stream().collect(Collectors.toMap(LocationDTO::getCode, LocationDTO::getZoneCode));

                for (FinanceSupervisionMoveDetailDTO financeSupervisionMoveDetailDTO : financeSupervisionMoveDetailDTOList) {
                    MoveDetailDTO moveDetailDTO = new MoveDetailDTO();
                    moveDetailDTO.setMoveCode(financeSupervisionMoveDetailDTO.getSupervisionCode());
                    moveDetailDTO.setId(financeSupervisionMoveDetailDTO.getId());
                    moveDetailDTO.setWarehouseCode(financeSupervisionMoveDetailDTO.getWarehouseCode());
                    moveDetailDTO.setCargoCode(financeSupervisionMoveDetailDTO.getCargoCode());
                    moveDetailDTO.setSkuCode(financeSupervisionMoveDetailDTO.getSkuCode());
                    moveDetailDTO.setOriginZoneCode(locationZoneMap.get(financeSupervisionMoveDetailDTO.getOriginLocationCode()));
                    moveDetailDTO.setTargetZoneCode(locationZoneMap.get(financeSupervisionMoveDetailDTO.getTargetLocationCode()));
                    moveDetailDTO.setActualSkuQty(financeSupervisionMoveDetailDTO.getMoveQty());
                    moveDetailDTO.setCarryOverBillType(BillTypeEnum.BILL_TYPE_SUPERVISION.getType());
                    moveDetailDTOList.add(moveDetailDTO);
                }
            }
        }
    }

    /**
     * 金融赎回移位
     */
    private void redeemMoveDetail(List<MoveDetailDTO> moveDetailDTOList, BookCarryoverTaskDTO taskDTO) {
        FinanceRedeemParam financeRedeemParam = new FinanceRedeemParam();
        financeRedeemParam.setStatus(FinanceRedeemStatusEnum.COMPLETE.getCode());
        financeRedeemParam.setRedeemTimeStart(taskDTO.getStatisticStartTime());
        financeRedeemParam.setRedeemTimeEnd(taskDTO.getStatisticEndTime());
        List<FinanceRedeemDTO> financeRedeemDTOList = remoteFinanceRedeemClient.getList(financeRedeemParam);
        if (CollectionUtil.isNotEmpty(financeRedeemDTOList)) {
            FinanceRedeemMoveDetailParam financeRedeemMoveDetailParam = new FinanceRedeemMoveDetailParam();
            financeRedeemMoveDetailParam.setRedeemCodeList(financeRedeemDTOList.stream().map(FinanceRedeemDTO::getRedeemCode).collect(Collectors.toList()));
            List<FinanceRedeemMoveDetailDTO> financeRedeemMoveDetailDTOList = remoteFinanceRedeemMoveDetailClient.getList(financeRedeemMoveDetailParam);
            if (CollectionUtil.isNotEmpty(financeRedeemMoveDetailDTOList)) {
                List<String> locationCodeList = financeRedeemMoveDetailDTOList.stream().flatMap(it -> Stream.of(it.getOriginLocationCode(), it.getTargetLocationCode()))
                        .distinct().collect(Collectors.toList());
                LocationParam locationParam = new LocationParam();
                locationParam.setCodeList(locationCodeList);
                List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
                Map<String, String> locationZoneMap = locationDTOList.stream().collect(Collectors.toMap(LocationDTO::getCode, LocationDTO::getZoneCode));

                for (FinanceRedeemMoveDetailDTO financeRedeemMoveDetailDTO : financeRedeemMoveDetailDTOList) {
                    MoveDetailDTO moveDetailDTO = new MoveDetailDTO();
                    moveDetailDTO.setMoveCode(financeRedeemMoveDetailDTO.getRedeemCode());
                    moveDetailDTO.setId(financeRedeemMoveDetailDTO.getId());
                    moveDetailDTO.setWarehouseCode(financeRedeemMoveDetailDTO.getWarehouseCode());
                    moveDetailDTO.setCargoCode(financeRedeemMoveDetailDTO.getCargoCode());
                    moveDetailDTO.setSkuCode(financeRedeemMoveDetailDTO.getSkuCode());
                    moveDetailDTO.setOriginZoneCode(locationZoneMap.get(financeRedeemMoveDetailDTO.getOriginLocationCode()));
                    moveDetailDTO.setTargetZoneCode(locationZoneMap.get(financeRedeemMoveDetailDTO.getTargetLocationCode()));
                    moveDetailDTO.setActualSkuQty(financeRedeemMoveDetailDTO.getMoveQty());
                    moveDetailDTO.setCarryOverBillType(BillTypeEnum.BILL_TYPE_REDEEM.getType());
                    moveDetailDTOList.add(moveDetailDTO);
                }
            }
        }
    }

    /**
     * 金融处置移位
     */
    private void disposalMoveDetail(List<MoveDetailDTO> moveDetailDTOList, BookCarryoverTaskDTO taskDTO) {
        FinanceDisposalParam financeDisposalParam = new FinanceDisposalParam();
        financeDisposalParam.setStatus(FinanceDisposalStatusEnum.COMPLETE.getCode());
        financeDisposalParam.setDisposalTimeStart(taskDTO.getStatisticStartTime());
        financeDisposalParam.setDisposalTimeEnd(taskDTO.getStatisticEndTime());
        List<FinanceDisposalDTO> financeDisposalDTOList = remoteFinanceDisposalClient.getList(financeDisposalParam);
        if (CollectionUtil.isNotEmpty(financeDisposalDTOList)) {
            FinanceDisposalMoveDetailParam financeDisposalMoveDetailParam = new FinanceDisposalMoveDetailParam();
            financeDisposalMoveDetailParam.setDisposalCodeList(financeDisposalDTOList.stream().map(FinanceDisposalDTO::getDisposalCode).collect(Collectors.toList()));
            List<FinanceDisposalMoveDetailDTO> financeDisposalMoveDetailDTOList = remoteFinanceDisposalMoveDetailClient.getList(financeDisposalMoveDetailParam);
            if (CollectionUtil.isNotEmpty(financeDisposalMoveDetailDTOList)) {
                List<String> locationCodeList = financeDisposalMoveDetailDTOList.stream().flatMap(it -> Stream.of(it.getOriginLocationCode(), it.getTargetLocationCode()))
                        .distinct().collect(Collectors.toList());
                LocationParam locationParam = new LocationParam();
                locationParam.setCodeList(locationCodeList);
                List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
                Map<String, String> locationZoneMap = locationDTOList.stream().collect(Collectors.toMap(LocationDTO::getCode, LocationDTO::getZoneCode));

                for (FinanceDisposalMoveDetailDTO financeRedeemMoveDetailDTO : financeDisposalMoveDetailDTOList) {
                    MoveDetailDTO moveDetailDTO = new MoveDetailDTO();
                    moveDetailDTO.setMoveCode(financeRedeemMoveDetailDTO.getDisposalCode());
                    moveDetailDTO.setId(financeRedeemMoveDetailDTO.getId());
                    moveDetailDTO.setWarehouseCode(financeRedeemMoveDetailDTO.getWarehouseCode());
                    moveDetailDTO.setCargoCode(financeRedeemMoveDetailDTO.getOriginCargoCode());
                    moveDetailDTO.setSkuCode(financeRedeemMoveDetailDTO.getOriginSkuCode());
                    moveDetailDTO.setOriginZoneCode(locationZoneMap.get(financeRedeemMoveDetailDTO.getOriginLocationCode()));
                    moveDetailDTO.setTargetZoneCode(locationZoneMap.get(financeRedeemMoveDetailDTO.getTargetLocationCode()));
                    moveDetailDTO.setActualSkuQty(financeRedeemMoveDetailDTO.getTargetQty());
                    moveDetailDTO.setCarryOverBillType(BillTypeEnum.BILL_TYPE_DISPOSAL.getType());
                    moveDetailDTOList.add(moveDetailDTO);
                }
            }
        }
    }

    /**
     * 转移明细
     */
    private void transferDetail(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.transferDetailCreated)) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setTransferDetailCreated(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        TransferParam transferParam = new TransferParam();
        transferParam.setStatus(TransferStatusEnum.DONE.getCode());
        transferParam.setCompleteDateStart(taskDTO.getStatisticStartTime());
        transferParam.setCompleteDateEnd(taskDTO.getStatisticEndTime());
        List<TransferDTO> transferDTOList = remoteTransferClient.list(transferParam);
        if (CollectionUtil.isEmpty(transferDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCodeList(transferDTOList.stream().map(TransferDTO::getCode).collect(Collectors.toList()));
        List<TransferDetailDTO> transferDetailDTOList = remoteTransferClient.listDetail(transferDetailParam);
        if (CollectionUtil.isEmpty(transferDetailDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        List<String> locationCodeList = transferDetailDTOList.stream().flatMap(transferDetailDTO -> Stream.of(transferDetailDTO.getOriginLoactionCode(), transferDetailDTO.getTargetLocationCode())).distinct().collect(Collectors.toList());
        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(locationCodeList);
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
        Map<String, String> zoneCodeMap = locationDTOList.stream().collect(Collectors.toMap(locationDTO -> locationDTO.getCode().toUpperCase(), LocationDTO::getZoneCode));

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        for (TransferDetailDTO transferDetailDTO : transferDetailDTOList) {
            String originZoneCode = zoneCodeMap.get(transferDetailDTO.getOriginLoactionCode().toUpperCase());
            String targetZoneCode = zoneCodeMap.get(transferDetailDTO.getTargetLocationCode().toUpperCase());
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, transferDetailDTO.getTransferCode(), transferDetailDTO.getId()));
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setWarehouseCode(transferDetailDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(transferDetailDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(transferDetailDTO.getOriginSkuCode());
            bookCarryoverDetailDTO.setOriginZoneCode(originZoneCode);
            bookCarryoverDetailDTO.setTargetZoneCode(targetZoneCode);
            bookCarryoverDetailDTO.setChangeQty(transferDetailDTO.getChangeQty());
            bookCarryoverDetailDTO.setBillNo(transferDetailDTO.getTransferCode());
            bookCarryoverDetailDTO.setDetailId(transferDetailDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_TRANSFER.getType());
            list.add(bookCarryoverDetailDTO);
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }

    /**
     * 上架结转明细
     */
    private void shelfDetail(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getShelfDetailCreated())) return;
        wmsTenantWarehouseHelper.setTenantId(taskDTO.getWarehouseCode());

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setShelfDetailCreated(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
        shelfParam.setType(ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType());
        shelfParam.setCompleteDateStart(taskDTO.getStatisticStartTime());
        shelfParam.setCompleteDateEnd(taskDTO.getStatisticEndTime());
        List<ShelfDTO> shelfDTOList = remoteShelfClient.getList(shelfParam);
        if (CollectionUtil.isEmpty(shelfDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        // 收货作业批次到上架单号的映射
        Map<String, String> recIdShelfCodeMap = shelfDTOList.stream()
                .filter(shelfDTO -> shelfDTO.getType().equals(ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType()))
                .collect(Collectors.toMap(ShelfDTO::getBillNo, ShelfDTO::getCode));
        Map<String, String> shelfEnterpriseMap = new HashMap<>();
        // 上架单到收货作业批次的映射
        Map<String, String> shelfRecIdMap = shelfDTOList.stream()
                .filter(shelfDTO -> shelfDTO.getType().equals(ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType()))
                .collect(Collectors.toMap(ShelfDTO::getCode, ShelfDTO::getBillNo, BinaryOperator.minBy(String::compareTo)));

        // 为了获取上架单对应清关企业
        // 调拨入库、 其它入库、 采购入库 调CCS获取
        // 退货入库 按照c单的申报企业
        List<String> recIdList = shelfDTOList.stream()
                .filter(shelfDTO -> shelfDTO.getType().equals(ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType()))
                .map(ShelfDTO::getBillNo)
                .collect(Collectors.toList());
        // 一个收货作业批次可能对应多个入库单，清关企业我们取其中一个就行。
        // 通过收货明细查询到货通知单
        List<ReceiptDTO> receiptDTOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(recIdList)) {
            ReceiptParam receiptParam = new ReceiptParam();
            receiptParam.setRecIdList(recIdList);
            receiptDTOList.addAll(remoteReceiptClient.getList(receiptParam));
            // 退货入库 按照c单的申报企业
            receiptDTOList.stream().filter(receiptDTO -> receiptDTO.getType().equals(AsnTypeEnum.RETURN.getCode())).forEach(receiptDTO -> {
                if (recIdShelfCodeMap.containsKey(receiptDTO.getRecId())) {
                    shelfEnterpriseMap.put(recIdShelfCodeMap.get(receiptDTO.getRecId()), getEnterPriseByWarehouse(receiptDTO.getWarehouseCode()));
                }
            });
            // 需要调用CCS获取企业的数据
            Map<String, String> recSoNoMap = receiptDTOList.stream().collect(Collectors.toMap(ReceiptDTO::getRecId, ReceiptDTO::getSoNo));

            ArrayList<String> asnTypeNeedFoundEnterpriseFromCCS = ListUtil.toList(AsnTypeEnum.PURCHASE.getCode(), AsnTypeEnum.TRANSFER.getCode(), AsnTypeEnum.OTHERS.getCode(), AsnTypeEnum.PURCHASE_SUPERVISE.getCode(), AsnTypeEnum.PURCHASE_REDEEM.getCode());
            List<String> soNoList = receiptDTOList.stream().filter(receiptDTO -> asnTypeNeedFoundEnterpriseFromCCS.contains(receiptDTO.getType()))
                    .map(ReceiptDTO::getSoNo).collect(Collectors.toList());
            wmsTenantWarehouseHelper.setTenantId(taskDTO.getWarehouseCode());
            RpcContextUtil.setWarehouseCode(taskDTO.getWarehouseCode());
//            Map<String, String> soNoEnterpriseMap = inventoryOrderRpc.findCompanyByInOutOrderNo(soNoList).getData();
            Map<String, String> soNoEnterpriseMap = CallOtherSupport.execute(it -> inventoryOrderRpc.findCompanyByInOutOrderNo(it).getData(), soNoList, "通过SoNo调用CCS查询清关企业");

            shelfDTOList.stream().filter(shelfDTO -> shelfDTO.getType().equals(ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType()))
                    .forEach(shelfDTO -> {
                        String recId = shelfDTO.getBillNo();
                        String soNo = recSoNoMap.get(recId);
                        String enterprise = soNoEnterpriseMap.get(soNo);
                        if (StrUtil.isNotBlank(enterprise)) {
                            shelfEnterpriseMap.put(shelfDTO.getCode(), enterprise);
                        }
                    });
        }

        Map<String, String> recAsnMap = receiptDTOList.stream().collect(Collectors.toMap(ReceiptDTO::getRecId, ReceiptDTO::getAsnId, BinaryOperator.minBy(String::compareTo)));

        // 如果有来源企业和来源库区都是空的情况判断一下是不是同货主同仓调拨
        for (ShelfDTO shelfDTO : shelfDTOList) {
            String enterprise = shelfEnterpriseMap.get(shelfDTO.getCode());
            if (StrUtil.isBlank(enterprise)) {
                String recId = shelfRecIdMap.get(shelfDTO.getCode());
                if (StrUtil.isNotBlank(recId)) {
                    String asnId = recAsnMap.get(recId);
                    if (StrUtil.isNotBlank(asnId)) {
                        AsnParam asnParam = new AsnParam();
                        asnParam.setAsnId(asnId);
                        AsnDTO asnDTO = remoteAsnClient.get(asnParam);
                        if (null != asnDTO) {
                            if (AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.SAME_USER_SAME_WAREHOUSE)) {
                                if (StrUtil.isNotBlank(asnDTO.getExtraJson())) {
                                    String outOrderNo = JSONUtil.parseObj(asnDTO.getExtraJson()).getStr("outOrderNo");
                                    if (StrUtil.isNotBlank(outOrderNo)) {
                                        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
                                        shipmentOrderParam.setSoNo(outOrderNo);
                                        List<ShipmentOrderDTO> shipmentOrderDTOList = remoteShipmentOrderClient.getList(shipmentOrderParam);
                                        shipmentOrderDTOList.stream().max(Comparator.comparing(ShipmentOrderDTO::getId)).ifPresent(shipmentOrderDTO -> {
                                            TallyParam tallyParam = new TallyParam();
                                            tallyParam.setBillNo(shipmentOrderDTO.getShipmentOrderCode());
                                            tallyParam.setStatus(TallyStatusEnum.SUCCESS_AUTH.getCode());
                                            TallyDTO tallyDTO = remoteTallyClient.get(tallyParam);
                                            if (null != tallyDTO) {
                                                shelfEnterpriseMap.put(shelfDTO.getCode(), tallyDTO.getLesseeEnterprise());
                                            }
                                        });
                                    }
                                }

                            }
                        }
                    }
                }
            }
        }

        ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
        shelfDetailParam.setShelfCodeList(shelfDTOList.stream().map(ShelfDTO::getCode).collect(Collectors.toList()));
        List<ShelfDetailDTO> shelfDetailDTOList = remoteShelfClient.getShelfDetailList(shelfDetailParam);
        if (CollectionUtil.isEmpty(shelfDetailDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(shelfDetailDTOList.stream().map(ShelfDetailDTO::getTargetLocationCode).collect(Collectors.toList()));
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
        Map<String, String> locationZoneMap = locationDTOList.stream().collect(Collectors.toMap(locationDTO -> locationDTO.getCode().toUpperCase(), LocationDTO::getZoneCode));

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        for (ShelfDetailDTO shelfDetailDTO : shelfDetailDTOList) {
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, shelfDetailDTO.getShelfCode(), shelfDetailDTO.getId()));
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setWarehouseCode(shelfDetailDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(shelfDetailDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(shelfDetailDTO.getSkuCode());
            String originEnterprise = shelfEnterpriseMap.get(shelfDetailDTO.getShelfCode());
            bookCarryoverDetailDTO.setOriginEnterprise(originEnterprise);
            bookCarryoverDetailDTO.setTargetZoneCode(locationZoneMap.get(shelfDetailDTO.getTargetLocationCode().toUpperCase()));
            bookCarryoverDetailDTO.setChangeQty(shelfDetailDTO.getShelfSkuQty());
            bookCarryoverDetailDTO.setBillNo(shelfDetailDTO.getShelfCode());
            bookCarryoverDetailDTO.setDetailId(shelfDetailDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_SHELF.getType());
            list.add(bookCarryoverDetailDTO);
        }

        // 退货的收货作业批次做一遍结转到仓库绑定的企业
        List<ReceiptDTO> returnRecList = receiptDTOList.stream()
                .filter(receiptDTO -> receiptDTO.getType().equals(AsnTypeEnum.RETURN.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(returnRecList)) {
            ReceiptDetailParam receiptDetailParam = new ReceiptDetailParam();
            receiptDetailParam.setRecIdList(returnRecList.stream().map(ReceiptDTO::getRecId).collect(Collectors.toList()));
            List<ReceiptDetailDTO> receiptDetailDTOList = remoteReceiptClient.getDetailList(receiptDetailParam);
            if (CollectionUtil.isNotEmpty(receiptDetailDTOList)) {
                List<String> asnList = receiptDetailDTOList.stream().map(ReceiptDetailDTO::getAsnId).distinct().collect(Collectors.toList());
                // 这里要通过运单去ccs拿来源企业
                AsnParam asnParam = new AsnParam();
                asnParam.setAsnIdList(asnList);
                List<AsnDTO> asnDTOList = remoteAsnClient.getList(asnParam);
                List<String> collect = asnDTOList.stream()
                        .map(AsnDTO::getExtraJson)
                        .map(remoteAsnClient::originExpressNo)
                        .filter(StrUtil::isNotBlank)
                        .distinct().collect(Collectors.toList());
//                Map<String, String> temp = inventoryOrderRpc.getInvCompanyNameByMailNo(collect).getData();
                Map<String, String> temp = CallOtherSupport.execute(it -> inventoryOrderRpc.getInvCompanyNameByMailNo(it).getData(), collect, "通过运单号调用CCS获取清关企业");
                if (temp == null) temp = new HashMap<>();
                Map<String, String> data = new HashMap<>(temp);

                Map<String, String> map = asnDTOList.stream().collect(Collectors.toMap(AsnDTO::getAsnId, asnDTO -> {
                    String string = data.get(remoteAsnClient.originExpressNo(asnDTO.getExtraJson()));
                    if (null == string) return StrUtil.EMPTY;
                    return string;
                }));

                for (ReceiptDetailDTO receiptDetailDTO : receiptDetailDTOList) {
                    BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
                    bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, receiptDetailDTO.getRecId(), receiptDetailDTO.getId()));
                    bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
                    bookCarryoverDetailDTO.setWarehouseCode(receiptDetailDTO.getWarehouseCode());
                    bookCarryoverDetailDTO.setCargoCode(receiptDetailDTO.getCargoCode());
                    bookCarryoverDetailDTO.setSkuCode(receiptDetailDTO.getSkuCode());
                    String originEnterprise = map.get(receiptDetailDTO.getAsnId());
                    bookCarryoverDetailDTO.setOriginEnterprise(originEnterprise);
                    bookCarryoverDetailDTO.setTargetEnterprise(getEnterPriseByWarehouse(receiptDetailDTO.getWarehouseCode()));
                    bookCarryoverDetailDTO.setChangeQty(receiptDetailDTO.getSkuQty());
                    bookCarryoverDetailDTO.setBillNo(receiptDetailDTO.getRecId());
                    bookCarryoverDetailDTO.setDetailId(receiptDetailDTO.getId());
                    bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_SHELF.getType());
                    list.add(bookCarryoverDetailDTO);
                }
            }
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }


    /**
     * 拆包上架
     */
    private void splitShelfDetail(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getSplitShelfDetailCreated())) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setSplitShelfDetailCreated(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        // 拆包预包计划
        PrePackagePlanParam prePackagePlanParam = new PrePackagePlanParam();
        prePackagePlanParam.setStatus(PrePackagePlanStatusEnum.COMPLETE.getCode());
        prePackagePlanParam.setType(PrePackagePlanTypeEnum.PRE.getCode());
        prePackagePlanParam.setCompleteTimeEqStart(taskDTO.getStatisticStartTime());
        prePackagePlanParam.setCompleteTimeEqEnd(taskDTO.getStatisticEndTime());
        List<PrePackagePlanDTO> prePackagePlanDTOList = remotePrePackagePlanClient.getList(prePackagePlanParam);
        if (CollectionUtil.isEmpty(prePackagePlanDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        // 拆包锁定明细
        List<String> planCodeList = prePackagePlanDTOList.stream().map(PrePackagePlanDTO::getPrePlanCode).collect(Collectors.toList());
        PrePackagePlanDetailParam planDetailParam = new PrePackagePlanDetailParam();
        planDetailParam.setPrePlanCodeList(planCodeList);
        List<PrePackagePlanDetailDTO> planDetailDTOList = remotePrePackagePlanDetailClient.getList(planDetailParam);
        if (CollectionUtil.isEmpty(planDetailDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        // 预包计划明细里面既有预包品，要过滤掉
        planDetailDTOList = planDetailDTOList.stream().filter(it -> !it.getPreUpcCode().equals(it.getSkuCode())).collect(Collectors.toList());

        // 查询所有的上架单
        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setBillNoList(planCodeList);
        List<ShelfDTO> shelfDTOList = remoteShelfClient.getList(shelfParam);
        if (CollectionUtil.isEmpty(shelfDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        // 上架明细
        ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
        shelfDetailParam.setShelfCodeList(shelfDTOList.stream().map(ShelfDTO::getCode).collect(Collectors.toList()));
        List<ShelfDetailDTO> shelfDetailList = remoteShelfClient.getShelfDetailList(shelfDetailParam);

        List<String> locationCodeList = shelfDetailList.stream().map(ShelfDetailDTO::getTargetLocationCode).distinct().collect(Collectors.toList());
        List<String> planLocationCodeList = planDetailDTOList.stream().map(PrePackagePlanDetailDTO::getLocationCode).distinct().collect(Collectors.toList());
        locationCodeList = Stream.of(locationCodeList, planLocationCodeList).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(locationCodeList);
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
        Map<String, String> locationZoneMap = locationDTOList.stream().collect(Collectors.toMap(locationDTO -> locationDTO.getCode().toUpperCase(), LocationDTO::getZoneCode));

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        // 生成中间明细,通过生成中间结转明细来统一来源库区
        for (PrePackagePlanDetailDTO prePackagePlanDetailDTO : planDetailDTOList) {
            String zoneCode = locationZoneMap.get(prePackagePlanDetailDTO.getLocationCode().toUpperCase());

            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, prePackagePlanDetailDTO.getPrePlanCode(), prePackagePlanDetailDTO.getId()));
            bookCarryoverDetailDTO.setWarehouseCode(prePackagePlanDetailDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(prePackagePlanDetailDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(prePackagePlanDetailDTO.getSkuCode());
            bookCarryoverDetailDTO.setOriginZoneCode(zoneCode);
            bookCarryoverDetailDTO.setTargetEnterprise(getEnterPriseByWarehouse(taskDTO.getWarehouseCode()));
            bookCarryoverDetailDTO.setChangeQty(prePackagePlanDetailDTO.getSkuQty());
            bookCarryoverDetailDTO.setBillNo(prePackagePlanDetailDTO.getPrePlanCode());
            bookCarryoverDetailDTO.setDetailId(prePackagePlanDetailDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_PRE_PACKAGE_PLAN.getType());
            list.add(bookCarryoverDetailDTO);
        }

        for (ShelfDetailDTO shelfDetailDTO : shelfDetailList) {
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, shelfDetailDTO.getShelfCode(), shelfDetailDTO.getId()));
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setWarehouseCode(shelfDetailDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(shelfDetailDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(shelfDetailDTO.getSkuCode());
            bookCarryoverDetailDTO.setOriginEnterprise(getEnterPriseByWarehouse(taskDTO.getWarehouseCode()));
            bookCarryoverDetailDTO.setTargetZoneCode(locationZoneMap.get(shelfDetailDTO.getTargetLocationCode().toUpperCase()));
            bookCarryoverDetailDTO.setChangeQty(shelfDetailDTO.getShelfSkuQty());
            bookCarryoverDetailDTO.setBillNo(shelfDetailDTO.getShelfCode());
            bookCarryoverDetailDTO.setDetailId(shelfDetailDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_SHELF.getType());
            list.add(bookCarryoverDetailDTO);
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }

    /**
     * 组包上架
     */
    private void prePackageShelfDetail(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getPrePackageShelfDetailCreated())) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setPrePackageShelfDetailCreated(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        PrePackagePlanParam prePackagePlanParam = new PrePackagePlanParam();
        prePackagePlanParam.setStatus(PrePackagePlanStatusEnum.COMPLETE.getCode());
        prePackagePlanParam.setType(PrePackagePlanTypeEnum.PACK.getCode());
        prePackagePlanParam.setCompleteTimeEqStart(taskDTO.getStatisticStartTime());
        prePackagePlanParam.setCompleteTimeEqEnd(taskDTO.getStatisticEndTime());
        List<PrePackagePlanDTO> prePackagePlanDTOList = remotePrePackagePlanClient.getList(prePackagePlanParam);
        if (CollectionUtil.isEmpty(prePackagePlanDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        // 组包锁定明细
        List<String> planCodeList = prePackagePlanDTOList.stream().map(PrePackagePlanDTO::getPrePlanCode).collect(Collectors.toList());
        PrePackagePlanDetailParam planDetailParam = new PrePackagePlanDetailParam();
        planDetailParam.setPrePlanCodeList(planCodeList);
        List<PrePackagePlanDetailDTO> planDetailDTOList = remotePrePackagePlanDetailClient.getList(planDetailParam);
        if (CollectionUtil.isEmpty(planDetailDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        // 预包上架单
        PrePackageShelfParam prePackageShelfParam = new PrePackageShelfParam();
        prePackageShelfParam.setBillNoList(planCodeList);
        List<PrePackageShelfDTO> prePackageShelfDTOList = remotePrePackageShelfClient.getList(prePackageShelfParam);
        if (CollectionUtil.isEmpty(prePackageShelfDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        // 预包上架明细
        PrePackageShelfDetailParam prePackageShelfDetailParam = new PrePackageShelfDetailParam();
        prePackageShelfDetailParam.setPreShelfCodeList(prePackageShelfDTOList.stream().map(PrePackageShelfDTO::getPreShelfCode).collect(Collectors.toList()));
        List<PrePackageShelfDetailDTO> prePackageShelfDetailDTOList = remotePrePackageShelfDetailClient.getList(prePackageShelfDetailParam);


        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(planDetailDTOList.stream().map(PrePackagePlanDetailDTO::getLocationCode).collect(Collectors.toList()));
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
        Map<String, String> locationZoneMap = locationDTOList.stream().collect(Collectors.toMap(locationDTO -> locationDTO.getCode().toUpperCase(), LocationDTO::getZoneCode));

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
//        String middleZone = locationZoneMap.get(planDetailDTOList.get(0).getLocationCode());
        // 生成中间明细,通过生成中间结转明细来统一来源库区
        for (PrePackagePlanDetailDTO prePackagePlanDetailDTO : planDetailDTOList) {
            String zoneCode = locationZoneMap.get(prePackagePlanDetailDTO.getLocationCode().toUpperCase());
//            if (zoneCode.equals(middleZone)) continue;

            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, prePackagePlanDetailDTO.getPrePlanCode(), prePackagePlanDetailDTO.getId()));
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setWarehouseCode(prePackagePlanDetailDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(prePackagePlanDetailDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(prePackagePlanDetailDTO.getSkuCode());
            bookCarryoverDetailDTO.setOriginZoneCode(zoneCode);
//            bookCarryoverDetailDTO.setTargetZoneCode(middleZone);
            bookCarryoverDetailDTO.setTargetEnterprise(getEnterPriseByWarehouse(taskDTO.getWarehouseCode()));
            bookCarryoverDetailDTO.setChangeQty(prePackagePlanDetailDTO.getSkuQty());
            bookCarryoverDetailDTO.setBillNo(prePackagePlanDetailDTO.getPrePlanCode());
            bookCarryoverDetailDTO.setDetailId(prePackagePlanDetailDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_PRE_PACKAGE_PLAN.getType());
            list.add(bookCarryoverDetailDTO);
        }

        for (PrePackageShelfDetailDTO prePackageShelfDetailDTO : prePackageShelfDetailDTOList) {
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, prePackageShelfDetailDTO.getPreShelfCode(), prePackageShelfDetailDTO.getId()));
            bookCarryoverDetailDTO.setWarehouseCode(prePackageShelfDetailDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(prePackageShelfDetailDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(prePackageShelfDetailDTO.getSkuCode());
//            bookCarryoverDetailDTO.setOriginZoneCode(middleZone);
            bookCarryoverDetailDTO.setOriginEnterprise(getEnterPriseByWarehouse(taskDTO.getWarehouseCode()));
            bookCarryoverDetailDTO.setTargetZoneCode(prePackageShelfDetailDTO.getTargetZoneCode());
            bookCarryoverDetailDTO.setChangeQty(prePackageShelfDetailDTO.getShelfSkuQty());
            bookCarryoverDetailDTO.setBillNo(prePackageShelfDetailDTO.getPreShelfCode());
            bookCarryoverDetailDTO.setDetailId(prePackageShelfDetailDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_PRE_PACKAGE_SHELF.getType());
            list.add(bookCarryoverDetailDTO);
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }

    /**
     * 出库结转明细
     */
    private void outStockDetail(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getOutStockDetailCreated())) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setOutStockDetailCreated(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        PackageParam packageParam = new PackageParam();
        packageParam.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        packageParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.name());
        packageParam.setStartOutStockDate(taskDTO.getStatisticStartTime());
        packageParam.setEndOutStockDate(taskDTO.getStatisticEndTime());
        List<PackageDTO> packageDTOList = remotePackageClient.getAppointMultipleParam(packageParam, LambdaHelpUtils.convertToFieldNameList(PackageDTO::getPackageCode, PackageDTO::getCargoCode, PackageDTO::getWaveCode, PackageDTO::getIsPre));
        if (CollectionUtil.isEmpty(packageDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        Map<String, PackageDTO> packageDTOMap = packageDTOList.stream().collect(Collectors.toMap(PackageDTO::getPackageCode, Function.identity()));

        AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
        allocationOrderParam.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
        allocationOrderParam.setWaveCodeList(packageDTOList.stream().map(PackageDTO::getWaveCode).distinct().collect(Collectors.toList()));
        List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getAppointMultipleParam(allocationOrderParam, LambdaHelpUtils.convertToFieldNameList(
                AllocationOrderDTO::getPackageCode, AllocationOrderDTO::getCargoCode, AllocationOrderDTO::getWaveCode, AllocationOrderDTO::getIsPre,
                AllocationOrderDTO::getSkuCode, AllocationOrderDTO::getCargoCode, AllocationOrderDTO::getWarehouseCode, AllocationOrderDTO::getZoneCode, AllocationOrderDTO::getSkuLotNo, AllocationOrderDTO::getLocationCode
                , AllocationOrderDTO::getExpQty, AllocationOrderDTO::getPickQty, AllocationOrderDTO::getRealQty, AllocationOrderDTO::getSplitQty, AllocationOrderDTO::getAllocationOrderCode));
        // 避坑，同一个包裹可能出现再多个波次，然后取消分配，这里有可能会把多余的错误数据查出来
        allocationOrderDTOList = allocationOrderDTOList.stream()
                .filter(allocationOrderDTO -> packageDTOMap.get(allocationOrderDTO.getPackageCode()).getWaveCode().equals(allocationOrderDTO.getWaveCode())).collect(Collectors.toList());

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        for (AllocationOrderDTO allocationOrderDTO : allocationOrderDTOList) {
            PackageDTO packageDTO = packageDTOMap.get(allocationOrderDTO.getPackageCode());
            if (PackEnum.TYPE.PRE.getCode().equals(packageDTO.getIsPre())) {
                if (AllocationIsPreEnum.PRE.getCode().equals(allocationOrderDTO.getIsPre())) continue;
                if (AllocationIsPreEnum.NORMAL.getCode().equals(allocationOrderDTO.getIsPre())) continue;
            }
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setWarehouseCode(allocationOrderDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, allocationOrderDTO.getAllocationOrderCode(), allocationOrderDTO.getId()));
            bookCarryoverDetailDTO.setCargoCode(allocationOrderDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(allocationOrderDTO.getSkuCode());
            bookCarryoverDetailDTO.setOriginZoneCode(allocationOrderDTO.getZoneCode());
            bookCarryoverDetailDTO.setTargetEnterprise(getEnterPriseByWarehouse(allocationOrderDTO.getWarehouseCode()));
            bookCarryoverDetailDTO.setChangeQty(allocationOrderDTO.getPickQty());
            bookCarryoverDetailDTO.setBillNo(allocationOrderDTO.getPackageCode());
            bookCarryoverDetailDTO.setDetailId(allocationOrderDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_PACKAGE.getType());
            list.add(bookCarryoverDetailDTO);
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }

    /**
     * 归位
     */
    private void homing(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getHomingDetailCreated())) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setHomingDetailCreated(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        PackageReturnParam packageReturnParam = new PackageReturnParam();
        packageReturnParam.setStatus(PackReturnEnum.STATUS.COMPLETE_STATUS.getCode());
        packageReturnParam.setCompleteDateEqStart(taskDTO.getStatisticStartTime());
        packageReturnParam.setCompleteDateEqEnd(taskDTO.getStatisticEndTime());
        List<PackageReturnDTO> packageReturnDTOList = remotePackageReturnClient.getList(packageReturnParam);
        if (CollectionUtil.isEmpty(packageReturnDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCodeList(packageReturnDTOList.stream().map(PackageReturnDTO::getPackageCode).collect(Collectors.toList()));
        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
        if (CollectionUtil.isEmpty(packageDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        Map<String, PackageDTO> packageDTOMap = packageDTOList.stream().collect(Collectors.toMap(PackageDTO::getPackageCode, Function.identity()));

        AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
        allocationOrderParam.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
        allocationOrderParam.setWaveCodeList(packageDTOList.stream().map(PackageDTO::getWaveCode).distinct().collect(Collectors.toList()));
        List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getList(allocationOrderParam);
        // 避坑，同一个包裹可能出现再多个波次，然后取消分配，这里有可能会把多余的错误数据查出来
        allocationOrderDTOList = allocationOrderDTOList.stream().filter(allocationOrderDTO -> packageDTOMap.get(allocationOrderDTO.getPackageCode()).getWaveCode().equals(allocationOrderDTO.getWaveCode())).collect(Collectors.toList());

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        for (AllocationOrderDTO allocationOrderDTO : allocationOrderDTOList) {
            PackageDTO packageDTO = packageDTOMap.get(allocationOrderDTO.getPackageCode());
            if (PackEnum.TYPE.PRE.getCode().equals(packageDTO.getIsPre())) {
                if (AllocationIsPreEnum.PRE.getCode().equals(allocationOrderDTO.getIsPre())) continue;
                if (AllocationIsPreEnum.NORMAL.getCode().equals(allocationOrderDTO.getIsPre())) continue;
            }
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, allocationOrderDTO.getAllocationOrderCode(), allocationOrderDTO.getId()));
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setWarehouseCode(allocationOrderDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(allocationOrderDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(allocationOrderDTO.getSkuCode());
            bookCarryoverDetailDTO.setOriginZoneCode(allocationOrderDTO.getZoneCode());
            bookCarryoverDetailDTO.setTargetEnterprise(getEnterPriseByWarehouse(allocationOrderDTO.getWarehouseCode()));
            bookCarryoverDetailDTO.setChangeQty(allocationOrderDTO.getPickQty());
            bookCarryoverDetailDTO.setBillNo(allocationOrderDTO.getPackageCode());
            bookCarryoverDetailDTO.setDetailId(allocationOrderDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_PACKAGE.getType());
            list.add(bookCarryoverDetailDTO);
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }

    /**
     * 归位上架
     */
    private void homingShelf(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getHomingShelfDetailCreated())) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setHomingShelfDetailCreated(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
        shelfParam.setType(ShelfTypeEnum.SHELF_TYPE_RETURN.getType());
        shelfParam.setCompleteDateStart(taskDTO.getStatisticStartTime());
        shelfParam.setCompleteDateEnd(taskDTO.getStatisticEndTime());
        List<ShelfDTO> shelfDTOList = remoteShelfClient.getList(shelfParam);
        if (CollectionUtil.isEmpty(shelfDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
        shelfDetailParam.setShelfCodeList(shelfDTOList.stream().map(ShelfDTO::getCode).collect(Collectors.toList()));
        List<ShelfDetailDTO> shelfDetailDTOList = remoteShelfClient.getShelfDetailList(shelfDetailParam);
        if (CollectionUtil.isEmpty(shelfDetailDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(shelfDetailDTOList.stream().map(ShelfDetailDTO::getTargetLocationCode).distinct().collect(Collectors.toList()));
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
        Map<String, String> locationZoneMap = locationDTOList.stream().collect(Collectors.toMap(locationDTO -> locationDTO.getCode().toUpperCase(), LocationDTO::getZoneCode));

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        for (ShelfDetailDTO shelfDetailDTO : shelfDetailDTOList) {
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, shelfDetailDTO.getShelfCode(), shelfDetailDTO.getId()));
            bookCarryoverDetailDTO.setWarehouseCode(shelfDetailDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(shelfDetailDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(shelfDetailDTO.getSkuCode());
            bookCarryoverDetailDTO.setOriginEnterprise(getEnterPriseByWarehouse(shelfDetailDTO.getWarehouseCode()));
            bookCarryoverDetailDTO.setTargetZoneCode(locationZoneMap.get(shelfDetailDTO.getTargetLocationCode().toUpperCase()));
            bookCarryoverDetailDTO.setChangeQty(shelfDetailDTO.getShelfSkuQty());
            bookCarryoverDetailDTO.setBillNo(shelfDetailDTO.getShelfCode());
            bookCarryoverDetailDTO.setDetailId(shelfDetailDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_SHELF.getType());
            list.add(bookCarryoverDetailDTO);
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }

    /**
     * 同用户同仓调拨
     */
    private void sameUserSameWarehouse(BookCarryoverTaskDTO taskDTO) {
        if (ObjectUtil.isEmpty(taskDTO)) return;
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(taskDTO.getId());
        taskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        BookCarryoverControlContext controlContext = JSONUtil.parse(taskDTO.getControlContext()).toBean(BookCarryoverControlContext.class);
        if (BookCarryoverControlContext.PROCESS_DONE.equals(controlContext.getSameUserSameWarehouse())) return;

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setSameUserSameWarehouse(BookCarryoverControlContext.PROCESS_DONE);
        taskDTO.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(taskDTO);

        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setStartOutStockDate(taskDTO.getStatisticStartTime());
        shipmentOrderParam.setEndOutStockDate(taskDTO.getStatisticEndTime());
        shipmentOrderParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.name());
        shipmentOrderParam.setOrderTag(OrderTagEnum.enumToNum(OrderTagEnum.SAME_USER_SAME_WAREHOUSE));
        List<ShipmentOrderDTO> shipmentOrderDTOList = remoteShipmentOrderClient.getList(shipmentOrderParam);
        if (CollectionUtil.isEmpty(shipmentOrderDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        PackageParam packageParam = new PackageParam();
        List<String> shipmentOrderCodeList = shipmentOrderDTOList.stream().map(ShipmentOrderDTO::getShipmentOrderCode).collect(Collectors.toList());
        packageParam.setShipmentOrderCodeList(shipmentOrderCodeList);
        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
        if (CollectionUtil.isEmpty(packageDTOList)) {
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        // 查询理货报告
        TallyParam tallyParam = new TallyParam();
        tallyParam.setBillNoList(shipmentOrderCodeList);
        tallyParam.setStatus(TallyStatusEnum.SUCCESS_AUTH.getCode());
        List<TallyDTO> tallyDTOList = remoteTallyClient.getList(tallyParam);
        Map<String, TallyDTO> tallyDTOMap = tallyDTOList.stream().collect(Collectors.toMap(TallyDTO::getBillNo, Function.identity(), BinaryOperator.maxBy(Comparator.comparing(TallyDTO::getId))));

        Map<String, PackageDTO> packageDTOMap = packageDTOList.stream().collect(Collectors.toMap(PackageDTO::getPackageCode, Function.identity()));
        AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
        allocationOrderParam.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
        allocationOrderParam.setWaveCodeList(packageDTOList.stream().map(PackageDTO::getWaveCode).distinct().collect(Collectors.toList()));
        List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getList(allocationOrderParam);
        // 避坑，同一个包裹可能出现再多个波次，然后取消分配，这里有可能会把多余的错误数据查出来
        allocationOrderDTOList = allocationOrderDTOList.stream().filter(allocationOrderDTO -> packageDTOMap.get(allocationOrderDTO.getPackageCode()).getWaveCode().equals(allocationOrderDTO.getWaveCode())).collect(Collectors.toList());

        List<BookCarryoverDetailDTO> list = new ArrayList<>();
        for (AllocationOrderDTO allocationOrderDTO : allocationOrderDTOList) {
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setTaskCode(taskDTO.getTaskCode());
            bookCarryoverDetailDTO.setSourceType(StrUtil.join(StrUtil.COLON, allocationOrderDTO.getAllocationOrderCode(), allocationOrderDTO.getId()));
            bookCarryoverDetailDTO.setWarehouseCode(allocationOrderDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(allocationOrderDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(allocationOrderDTO.getSkuCode());
            bookCarryoverDetailDTO.setOriginZoneCode(allocationOrderDTO.getZoneCode());
            TallyDTO tallyDTO = tallyDTOMap.get(allocationOrderDTO.getShipmentOrderCode());
            if (null != tallyDTO) {
                bookCarryoverDetailDTO.setTargetEnterprise(tallyDTO.getLesseeEnterprise());
            }
            bookCarryoverDetailDTO.setChangeQty(allocationOrderDTO.getPickQty());
            bookCarryoverDetailDTO.setBillNo(allocationOrderDTO.getPackageCode());
            bookCarryoverDetailDTO.setDetailId(allocationOrderDTO.getId());
            bookCarryoverDetailDTO.setBillType(BillTypeEnum.BILL_TYPE_PACKAGE.getType());
            list.add(bookCarryoverDetailDTO);
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(list);

        // 填充商品信息
        list = fillSkuInfo(list);

        // 合并之前记录原始明细
        List<BookCarryoverOriginDetailDTO> originDetailDTOList = originDetailDTOList(list);
        bookCarryoverBO.setBookCarryoverOriginDetailDTOList(originDetailDTOList);

        // 减少数据插入
        list = mergeDetailList(list);

        bookCarryoverBO.setBookCarryoverDetailDTOList(list);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }


    /**
     * B单结转单生成
     */
    public void carryover(ShipmentOrderDTO shipmentOrderDTO, TallyDTO tallyDTO) {
        if (ObjectUtil.isEmpty(shipmentOrderDTO)) return;
        if (!shipmentOrderDTO.getBusinessType().equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.name())) return;
        if (!TaxTypeEnum.TYPE_BONDED_TAX.getCode().equals(shipmentOrderDTO.getTaxType())) return;
        if (CollectionUtil.isEmpty(defaultWarehouseCodeConfig.getCrossBorderCarryoverWarehouseCodeList())) return;
        if (!defaultWarehouseCodeConfig.getCrossBorderCarryoverWarehouseCodeList().contains(shipmentOrderDTO.getWarehouseCode()))
            return;
        // 特殊场景:同用户同仓调拨不在这里生成结转
        Set<OrderTagEnum> orderTagEnums = OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag());
        if (orderTagEnums.contains(OrderTagEnum.SAME_USER_SAME_WAREHOUSE)) {
            return;
        }

        if (StrUtil.isBlank(tallyDTO.getPhysicalPartition())) {
            TallyParam tallyParam = new TallyParam();
            tallyParam.setTallyCode(tallyDTO.getTallyCode());
            tallyDTO = remoteTallyClient.get(tallyParam);
        }
        // 使用理货报告做taskCode
        String taskCode = tallyDTO.getTallyCode();
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setTaskCode(taskCode);
        BookCarryoverTaskDTO carryoverTaskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        BookCarryoverBO bookCarryoverBO1 = new BookCarryoverBO();
        bookCarryoverBO1.setTaskCode(taskCode);
        bookCarryoverBO1.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
        if (null == carryoverTaskDTO) {
            bookCarryoverClient.addBookCarryoverTaskForB2B(bookCarryoverBO1);
            carryoverTaskDTO = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        }

        // 结转单已经生成
        if (BookCarryoverTaskStatusEnum.DONE.getCode().equals(carryoverTaskDTO.getStatus())) {
            return;
        }
        BookCarryoverParam bookCarryoverParam = new BookCarryoverParam();
        bookCarryoverParam.setTaskCode(taskCode);
        List<BookCarryoverDTO> carryoverDTOList = bookCarryoverClient.getList(bookCarryoverParam).getData();
        if (CollectionUtil.isNotEmpty(carryoverDTOList)) return;

        PackageParam packageParam = new PackageParam();
        packageParam.setShipmentOrderCode(shipmentOrderDTO.getShipmentOrderCode());
        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
        if (CollectionUtil.isEmpty(packageDTOList)) {
            return;
        }
        //闭坑
        List<String> statusList = Arrays.asList(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode());
        packageDTOList = packageDTOList.stream().filter(it -> !statusList.contains(it.getStatus())).collect(Collectors.toList());

        Map<String, PackageDTO> packageDTOMap = packageDTOList.stream().collect(Collectors.toMap(PackageDTO::getPackageCode, Function.identity()));

        AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
        allocationOrderParam.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
        allocationOrderParam.setWaveCodeList(packageDTOList.stream().map(PackageDTO::getWaveCode).distinct().collect(Collectors.toList()));
        List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getList(allocationOrderParam);
        // 避坑，同一个包裹可能出现再多个波次，然后取消分配，这里有可能会把多余的错误数据查出来
        allocationOrderDTOList = allocationOrderDTOList.stream().filter(allocationOrderDTO -> packageDTOMap.get(allocationOrderDTO.getPackageCode()).getWaveCode().equals(allocationOrderDTO.getWaveCode())).collect(Collectors.toList());

        List<BookCarryoverDetailDTO> bookCarryoverDetailDTOList = new ArrayList<>();
        for (AllocationOrderDTO allocationOrderDTO : allocationOrderDTOList) {
            BookCarryoverDetailDTO bookCarryoverDetailDTO = new BookCarryoverDetailDTO();
            bookCarryoverDetailDTO.setTaskCode(taskCode);
            bookCarryoverDetailDTO.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
            bookCarryoverDetailDTO.setCargoCode(allocationOrderDTO.getCargoCode());
            bookCarryoverDetailDTO.setSkuCode(allocationOrderDTO.getSkuCode());
            bookCarryoverDetailDTO.setOriginZoneCode(allocationOrderDTO.getZoneCode());
            bookCarryoverDetailDTO.setTargetEnterprise(tallyDTO.getLesseeEnterprise());
            bookCarryoverDetailDTO.setChangeQty(allocationOrderDTO.getPickQty());
            bookCarryoverDetailDTOList.add(bookCarryoverDetailDTO);
        }

        // 转化防火分区
        fillFirewallZoneAndEnterprise(bookCarryoverDetailDTOList);

        // 填充商品信息
        bookCarryoverDetailDTOList = fillSkuInfo(bookCarryoverDetailDTOList);

        // 合并之前没有保税的品则不需要生成结转
        if (CollectionUtil.isEmpty(bookCarryoverDetailDTOList)) {
            BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
            carryoverTaskDTO.setStatus(BookCarryoverStatusEnum.DONE.getCode());
            bookCarryoverBO.setBookCarryoverTaskDTO(carryoverTaskDTO);
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
            return;
        }

        bookCarryoverDetailDTOList = mergeDetailList(bookCarryoverDetailDTOList);

        // B单出库如果库区对应企业没确定，先预警
        List<String> zoneEnterpriseNotSet = bookCarryoverDetailDTOList.stream().filter(bookCarryoverDetailDTO -> StrUtil.isBlank(bookCarryoverDetailDTO.getOriginEnterprise()))
                .map(BookCarryoverDetailDTO::getOriginZoneCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(zoneEnterpriseNotSet)) {
            warning(tallyDTO.getWarehouseCode(), "仓库" + shipmentOrderDTO.getWarehouseCode() + "库区对应企业未配置，请尽快配置！库存编码列表: " + String.join(",", zoneEnterpriseNotSet));
            ThreadUtil.sleep(60, TimeUnit.SECONDS);
            throw new BaseException(BaseBizEnum.TIP, "库区对应企业未配置，请尽快配置！库存编码列表: " + String.join(",", zoneEnterpriseNotSet));
        }
        List<BookCarryoverDTO> bookCarryoverDTOList;
        if (CollectionUtil.isNotEmpty(bookCarryoverDetailDTOList)) {
            bookCarryoverDTOList = groupCarryover(bookCarryoverDetailDTOList);
            bookCarryoverDTOList.forEach(bookCarryoverDTO -> {
                bookCarryoverDTO.setBillNo(shipmentOrderDTO.getSoNo());
                bookCarryoverDTO.setTaskCode(taskCode);
            });

            BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
            carryoverTaskDTO.setStatus(BookCarryoverTaskStatusEnum.DONE.getCode());
            bookCarryoverBO.setBookCarryoverTaskDTO(carryoverTaskDTO);
            bookCarryoverBO.setBookCarryoverDTOList(bookCarryoverDTOList);
            bookCarryoverBO.setBookCarryoverDetailDTOList(bookCarryoverDetailDTOList);
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
        } else {
            BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
            bookCarryoverDTOList = new ArrayList<>();

            String carryOverCode = remoteSeqRuleClient.findSequence(SeqEnum.CARRYOVER_CODE_000001);
            BookCarryoverDTO bookCarryoverDTO = new BookCarryoverDTO();
            bookCarryoverDTO.setTaskCode(taskCode);
            bookCarryoverDTO.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
            bookCarryoverDTO.setCarryoverCode(carryOverCode);
            bookCarryoverDTO.setStatus(BookCarryoverStatusEnum.CREATED.getCode());
            bookCarryoverDTO.setBillNo(shipmentOrderDTO.getSoNo());
            bookCarryoverDTO.setOriginEnterprise("");
            bookCarryoverDTO.setTargetEnterprise("");
            bookCarryoverDTOList.add(bookCarryoverDTO);

            carryoverTaskDTO.setStatus(BookCarryoverTaskStatusEnum.DONE.getCode());
            bookCarryoverBO.setBookCarryoverTaskDTO(carryoverTaskDTO);
            bookCarryoverBO.setBookCarryoverDTOList(bookCarryoverDTOList);
            bookCarryoverBO.setBookCarryoverDetailDTOList(bookCarryoverDetailDTOList);
            bookCarryoverClient.taskPersistence(bookCarryoverBO);
        }

        for (BookCarryoverDTO bookCarryoverDTO : bookCarryoverDTOList) {
            sync(bookCarryoverDTO.getCarryoverCode());
        }
    }

    @Override
    public void carryover(TallyDTO tallyDTO) {
        if (null == tallyDTO) return;
        if (StrUtil.isBlank(tallyDTO.getBillNo())) return;
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setShipmentOrderCode(tallyDTO.getBillNo());
        ShipmentOrderDTO shipmentOrderDTO = remoteShipmentOrderClient.get(shipmentOrderParam);
        if (null == shipmentOrderDTO) return;
        carryover(shipmentOrderDTO, tallyDTO);
    }

    /**
     * 填充防火分区
     */
    private void fillFirewallZoneAndEnterprise(List<BookCarryoverDetailDTO> detailDTOList) {
        log.info("before fill enterprise: {}", JSONUtil.toJsonStr(detailDTOList));
        if (CollectionUtil.isEmpty(detailDTOList)) return;

        List<String> zoneCodeList = detailDTOList.stream().flatMap(it -> Stream.of(it.getOriginZoneCode(), it.getTargetZoneCode())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(zoneCodeList)) return;

        List<PhysicalPartitionBindDTO> physicalPartitionBindDTOList = remoteZoneClient.getPhysicalPartitionList(detailDTOList.get(0).getWarehouseCode(), zoneCodeList);
        if (CollectionUtil.isEmpty(physicalPartitionBindDTOList)) return;

        Map<String, PhysicalPartitionBindDTO> partitionBindDTOMap = physicalPartitionBindDTOList.stream().collect(Collectors.toMap(PhysicalPartitionBindDTO::getZoneCode, Function.identity()));
        for (BookCarryoverDetailDTO bookCarryoverDetailDTO : detailDTOList) {
            if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginZoneCode())) {
                Optional.ofNullable(partitionBindDTOMap.get(bookCarryoverDetailDTO.getOriginZoneCode())).ifPresent(physicalPartitionBindDTO -> {
                    bookCarryoverDetailDTO.setOriginFirewallZone(physicalPartitionBindDTO.getPhysicalPartition());
                    bookCarryoverDetailDTO.setOriginEnterprise(physicalPartitionBindDTO.getLesseeEnterprise());
                });
            }
            if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetZoneCode())) {
                Optional.ofNullable(partitionBindDTOMap.get(bookCarryoverDetailDTO.getTargetZoneCode())).ifPresent(physicalPartitionBindDTO -> {
                    bookCarryoverDetailDTO.setTargetFirewallZone(physicalPartitionBindDTO.getPhysicalPartition());
                    bookCarryoverDetailDTO.setTargetEnterprise(physicalPartitionBindDTO.getLesseeEnterprise());
                });
            }
        }
        log.info("after fill enterprise: {}", JSONUtil.toJsonStr(detailDTOList));
    }

    /**
     * 校验库区和企业
     */
    private void checkZoneAndEnterprise(List<BookCarryoverDetailDTO> detailDTOList) {
        if (CollectionUtil.isEmpty(detailDTOList)) return;

        for (BookCarryoverDetailDTO bookCarryoverDetailDTO : detailDTOList) {
            // 校验：企业或库区必须有有一个有值才能存入数据库
            if (StrUtil.isBlank(bookCarryoverDetailDTO.getOriginZoneCode()) && StrUtil.isBlank(bookCarryoverDetailDTO.getOriginEnterprise())) {
                log.info("checkZoneAndEnterprise {} ", JSONUtil.toJsonStr(bookCarryoverDetailDTO));
                warning(bookCarryoverDetailDTO.getWarehouseCode(), "结转明细来源库区和来源企业必须有一个有值,对应单据:" + bookCarryoverDetailDTO.getBillNo());
                throw new BaseException(BaseBizEnum.TIP, "结转明细来源库区和来源企业必须有一个有值");
            }
            if (StrUtil.isBlank(bookCarryoverDetailDTO.getTargetZoneCode()) && StrUtil.isBlank(bookCarryoverDetailDTO.getTargetEnterprise())) {
                log.info("checkZoneAndEnterprise {} ", JSONUtil.toJsonStr(bookCarryoverDetailDTO));
                warning(bookCarryoverDetailDTO.getWarehouseCode(), "结转明细目标库区和目标企业必须有一个有值,对应单据:" + bookCarryoverDetailDTO.getBillNo());
                throw new BaseException(BaseBizEnum.TIP, "结转明细目标库区和目标企业必须有一个有值");
            }
        }
    }

    /**
     * 预包条码
     */
    private List<BookCarryoverDetailDTO> preUpc(List<BookCarryoverDetailDTO> detailDTOList) {
        if (CollectionUtil.isEmpty(detailDTOList)) return detailDTOList;
        List<String> skuCodeList = detailDTOList.stream().map(BookCarryoverDetailDTO::getSkuCode).collect(Collectors.toList());
        List<String> cargoCodeList = detailDTOList.stream().map(BookCarryoverDetailDTO::getCargoCode).collect(Collectors.toList());
        SkuParam skuParam = new SkuParam();
        skuParam.setCodeList(skuCodeList);
        skuParam.setCargoCodeList(cargoCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        List<SkuDTO> preSkuDTOList = skuDTOList.stream().filter(skuDTO -> SkuIsPreEnum.PRE.getCode().equals(skuDTO.getIsPre()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(preSkuDTOList)) { // 没有预包品，不需要处理
            return detailDTOList;
        }

        PrePackageSkuDetailParam prePackageSkuDetailParam = new PrePackageSkuDetailParam();
        prePackageSkuDetailParam.setPreUpcCodeList(preSkuDTOList.stream().map(SkuDTO::getCode).collect(Collectors.toList()));
        List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOList = remotePrePackageSkuDetailClient.getList(prePackageSkuDetailParam);
        Map<String, List<PrePackageSkuDetailDTO>> prePackageSkuDetailMap = prePackageSkuDetailDTOList.stream()
                .collect(Collectors.groupingBy(PrePackageSkuDetailDTO::getPreUpcCode));

        Map<String, List<SkuDTO>> skuGroup = skuDTOList.stream().collect(Collectors.groupingBy(SkuDTO::getCode));

        List<BookCarryoverDetailDTO> result = new ArrayList<>();
        for (BookCarryoverDetailDTO bookCarryoverDetailDTO : detailDTOList) {
            SkuDTO skuDTO = skuGroup.get(bookCarryoverDetailDTO.getSkuCode()).stream().filter(it -> it.getCargoCode().equals(bookCarryoverDetailDTO.getCargoCode())).findFirst().orElseThrow(() -> new BaseException(BaseBizEnum.TIP, "商品信息未找到"));
            if (SkuIsPreEnum.PRE.getCode().equals(skuDTO.getIsPre())) {
                List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOS = prePackageSkuDetailMap.get(bookCarryoverDetailDTO.getSkuCode());
                for (PrePackageSkuDetailDTO prePackageSkuDetailDTO : prePackageSkuDetailDTOS) {
                    if (prePackageSkuDetailDTO.getCargoCode().equals(bookCarryoverDetailDTO.getCargoCode())) {
                        BookCarryoverDetailDTO child = new BookCarryoverDetailDTO();
                        BeanUtil.copyProperties(bookCarryoverDetailDTO, child);
                        child.setSkuCode(prePackageSkuDetailDTO.getSkuCode());
                        child.setChangeQty(bookCarryoverDetailDTO.getChangeQty().multiply(prePackageSkuDetailDTO.getSkuQty()));
                        result.add(child);
                    }
                }
            } else {
                result.add(bookCarryoverDetailDTO);
            }
        }
        return result;
    }

    /**
     * 条虫商品信息
     */
    private List<BookCarryoverDetailDTO> fillSkuInfo(List<BookCarryoverDetailDTO> detailDTOList) {
        log.info("before fill sku: {}", JSONUtil.toJsonStr(detailDTOList));
        // 处理预包
        detailDTOList = preUpc(detailDTOList);

        if (CollectionUtil.isEmpty(detailDTOList)) return detailDTOList;
        List<String> cargoCodeList = detailDTOList.stream().map(BookCarryoverDetailDTO::getCargoCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(cargoCodeList)) return detailDTOList;
        List<String> skuCodeList = detailDTOList.stream().map(BookCarryoverDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuCodeList)) return detailDTOList;

        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCodeList(cargoCodeList);
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);

        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setCargoCodeList(cargoCodeList);
        skuUpcParam.setSkuCodeList(skuCodeList);
        skuUpcParam.setIsDefault(SkuUpcDefaultEnum.YES.value());
        List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);

        Map<String, List<SkuDTO>> skuGroup = skuDTOList.stream().collect(Collectors.groupingBy(SkuDTO::getCode));
        Map<String, List<SkuUpcDTO>> upcGroup = skuUpcList.stream().collect(Collectors.groupingBy(SkuUpcDTO::getSkuCode));

        List<BookCarryoverDetailDTO> bookCarryoverDetailDTOList = new ArrayList<>();
        for (BookCarryoverDetailDTO bookCarryoverDetailDTO : detailDTOList) {
            SkuDTO skuDTO = skuGroup.getOrDefault(bookCarryoverDetailDTO.getSkuCode(), ListUtil.empty()).stream().filter(it -> it.getCargoCode().equals(bookCarryoverDetailDTO.getCargoCode()))
                    .findFirst().orElse(null);
            if (skuDTO == null) continue;
            if (TaxTypeEnum.TYPE_DUTY_TAX.getCode().equals(skuDTO.getType())) {
                continue;
            }
            bookCarryoverDetailDTO.setTaxType(skuDTO.getType());
            bookCarryoverDetailDTO.setSkuName(skuDTO.getName());
            bookCarryoverDetailDTO.setItemCode(skuDTO.getItemCode());
            upcGroup.getOrDefault(bookCarryoverDetailDTO.getSkuCode(), ListUtil.empty()).stream().filter(skuUpcDTO -> skuUpcDTO.getCargoCode().equals(bookCarryoverDetailDTO.getCargoCode()))
                    .findFirst().ifPresent(skuUpcDTO -> bookCarryoverDetailDTO.setUpcCode(skuUpcDTO.getUpcCode()));
            bookCarryoverDetailDTOList.add(bookCarryoverDetailDTO);
        }

        checkZoneAndEnterprise(bookCarryoverDetailDTOList);

        log.info("after fill sku: {}", JSONUtil.toJsonStr(bookCarryoverDetailDTOList));
        return bookCarryoverDetailDTOList;
    }

    @Override
    public Result<BookCarryoverBizDTO> get(BookCarryoverBizParam param) {
        BookCarryoverParam bookCarryoverParam = ConverterUtil.convert(param, BookCarryoverParam.class);
        BookCarryoverDTO bookCarryoverDTO = bookCarryoverClient.get(bookCarryoverParam).getData();
        BookCarryoverBizDTO result = ConverterUtil.convert(bookCarryoverDTO, BookCarryoverBizDTO.class);
        if (result != null) {
            BookCarryoverDetailParam bookCarryoverDetailParam = new BookCarryoverDetailParam();
            bookCarryoverDetailParam.setCarryoverCode(bookCarryoverDTO.getCarryoverCode());
            Result<List<BookCarryoverDetailDTO>> carryoverDetailClientList = bookCarryoverDetailClient.getList(bookCarryoverDetailParam);
            if (!CollectionUtils.isEmpty(carryoverDetailClientList.getData())) {
                List<BookCarryoverDetailBizDTO> carryoverDetailBizDTOList = ConverterUtil.convertList(carryoverDetailClientList.getData(), BookCarryoverDetailBizDTO.class);

                List<String> zoneCodeList = new ArrayList<>();
                zoneCodeList.addAll(carryoverDetailBizDTOList.stream().map(BookCarryoverDetailBizDTO::getOriginZoneCode).distinct().collect(Collectors.toList()));
                zoneCodeList.addAll(carryoverDetailBizDTOList.stream().map(BookCarryoverDetailBizDTO::getTargetZoneCode).distinct().collect(Collectors.toList()));
                ZoneParam zoneParam = new ZoneParam();
                zoneParam.setCodeList(zoneCodeList);
                List<ZoneDTO> zoneDTOList = remoteZoneClient.getList(zoneParam);
                Map<String, ZoneDTO> zoneMap = zoneDTOList.stream().collect(Collectors.toMap(ZoneDTO::getCode, Function.identity(), (a, b) -> a));
                if (!CollectionUtils.isEmpty(carryoverDetailBizDTOList)) {
                    carryoverDetailBizDTOList.forEach(it -> {
                        if (zoneMap.containsKey(it.getTargetZoneCode())) {
                            it.setTargetZoneName(zoneMap.get(it.getTargetZoneCode()).getCode());
                        }
                        if (zoneMap.containsKey(it.getOriginZoneCode())) {
                            it.setOriginZoneName(zoneMap.get(it.getOriginZoneCode()).getCode());
                        }
                    });
                }
                result.setBookCarryoverDetailList(carryoverDetailBizDTOList);
            }

            BookCarryoverReceiptParam bookCarryoverReceiptParam = new BookCarryoverReceiptParam();
            bookCarryoverReceiptParam.setCarryoverCode(bookCarryoverDTO.getCarryoverCode());
            Result<List<BookCarryoverReceiptDTO>> bookCarryoverReceiptClientList = bookCarryoverReceiptClient.getList(bookCarryoverReceiptParam);
            if (!CollectionUtils.isEmpty(bookCarryoverReceiptClientList.getData())) {
                List<BookCarryoverReceiptBizDTO> carryoverReceiptBizDTOList = ConverterUtil.convertList(bookCarryoverReceiptClientList.getData(), BookCarryoverReceiptBizDTO.class);
                if (!CollectionUtils.isEmpty(carryoverReceiptBizDTOList)) {
                    carryoverReceiptBizDTOList.forEach(it -> it.setEndorsementStatusDesc(BookCarryoverEndorsementStatusEnum.fromCode(it.getEndorsementStatus()).getName()));
                }
                result.setBookCarryoverReceiptList(carryoverReceiptBizDTOList);
            }
        }
        return Result.success(result);
    }

    @Override
    public Result<Page<BookCarryoverBizDTO>> getPage(BookCarryoverBizParam param) {
        BookCarryoverParam bookCarryoverParam = ConverterUtil.convert(param, BookCarryoverParam.class);
        Page<BookCarryoverDTO> page = bookCarryoverClient.getPage(bookCarryoverParam).getData();
        Page<BookCarryoverBizDTO> result = ConverterUtil.convertPage(page, BookCarryoverBizDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<BookCarryoverOriginDetailBizDTO>> originDetailPage(BookCarryoverOriginDetailBizParam param) {
        Page<BookCarryoverOriginDetailDTO> dtoPage = remoteBookCarryoverOriginDetailClient.getPage(ConverterUtil.convert(param, BookCarryoverOriginDetailParam.class));
        Page<BookCarryoverOriginDetailBizDTO> result = ConverterUtil.convertPage(dtoPage, BookCarryoverOriginDetailBizDTO.class);
        if (CollectionUtil.isNotEmpty(result.getRecords())) {
            List<String> zoneCodeList = dtoPage.getRecords().stream().flatMap(it -> Stream.of(it.getOriginZoneCode(), it.getTargetZoneCode()))
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            ZoneParam zoneParam = new ZoneParam();
            zoneParam.setCodeList(zoneCodeList);
            List<ZoneDTO> list = remoteZoneClient.getList(zoneParam);
            for (BookCarryoverOriginDetailBizDTO detailDTO : result.getRecords()) {
                list.stream().filter(it -> it.getCode().equalsIgnoreCase(detailDTO.getOriginZoneCode()))
                        .findFirst().ifPresent(it -> detailDTO.setOriginZoneName(it.getName()));
                list.stream().filter(it -> it.getCode().equalsIgnoreCase(detailDTO.getTargetZoneCode()))
                        .findFirst().ifPresent(it -> detailDTO.setTargetZoneName(it.getName()));
            }
        }
        return Result.success(result);
    }

    @Override
    public Result<Boolean> retryBookCarryover(BookCarryoverBizParam param) {
        return retryBookCarryover(param.getCarryoverCode());
    }

    private BillLogDTO billLogDTO(BookCarryoverDTO bookCarryoverDTO, String content) {
        BillLogDTO billLogDTO = new BillLogDTO();
        billLogDTO.setWarehouseCode(bookCarryoverDTO.getWarehouseCode());
        billLogDTO.setBillNo(bookCarryoverDTO.getCarryoverCode());
        billLogDTO.setBillType(BillLogTypeEnum.BOOK_CARRYOVER.getType());
        billLogDTO.setOpBy("System");
        billLogDTO.setOpContent(content);
        billLogDTO.setOpRemark(content);
        billLogDTO.setOpDate(System.currentTimeMillis());
        return billLogDTO;
    }

    private String getEnterPriseByWarehouse(String warehouseCode) {
        try {
            if (null == defaultWarehouseCodeConfig) throw new BaseException(BaseBizEnum.TIP, "结转单Nacos配置错误");
            if (CollectionUtil.isEmpty(defaultWarehouseCodeConfig.getCrossBorderCarryoverB2CBindEnterprise()))
                throw new BaseException(BaseBizEnum.TIP, "结转单仓库绑定企业配置错误");
            if (!defaultWarehouseCodeConfig.getCrossBorderCarryoverB2CBindEnterprise().containsKey(warehouseCode)) {
                throw new BaseException(BaseBizEnum.TIP, "仓库未绑定结转企业");
            }
            return defaultWarehouseCodeConfig.getCrossBorderCarryoverB2CBindEnterprise().get(warehouseCode);
        } catch (Exception exception) {
            warning(warehouseCode, warehouseCode + " 仓库结转企业未配置");
            throw exception;
        }
    }

    private void warning(String warehouseCode, String content) {
        if (CollectionUtil.isNotEmpty(urlConfig.getCarryoverWarningUrlMap())) {
            String warningUrl = urlConfig.getCarryoverWarningUrlMap().get(warehouseCode);
            if (StrUtil.isNotBlank(warningUrl)) {
                String mobile = "";
                if (CollectionUtil.isNotEmpty(urlConfig.getCarryoverWarningMobile())) {
                    mobile = urlConfig.getCarryoverWarningMobile().get(warehouseCode);
                }
                WechatUtil.sendMessage(content, ListUtil.toList(warningUrl), mobile);
            }
        }

    }

    private List<BookCarryoverOriginDetailDTO> originDetailDTOList(List<BookCarryoverDetailDTO> bookCarryoverDetailDTOList) {
        if (CollectionUtil.isEmpty(bookCarryoverDetailDTOList)) return new ArrayList<>();
        return bookCarryoverDetailDTOList.stream().map(it -> {
            BookCarryoverOriginDetailDTO originDetailDTO = new BookCarryoverOriginDetailDTO();
            originDetailDTO.setWarehouseCode(it.getWarehouseCode());
            originDetailDTO.setBillNo(it.getBillNo());
            originDetailDTO.setDetailId(it.getDetailId());
            originDetailDTO.setBillType(it.getBillType());
            originDetailDTO.setSkuCode(it.getSkuCode());
            originDetailDTO.setItemCode(it.getItemCode());
            originDetailDTO.setChangeQty(it.getChangeQty());
            originDetailDTO.setOriginZoneCode(it.getOriginZoneCode());
            originDetailDTO.setOriginEnterprise(it.getOriginEnterprise());
            originDetailDTO.setTargetZoneCode(it.getTargetZoneCode());
            originDetailDTO.setTargetEnterprise(it.getTargetEnterprise());
            originDetailDTO.setStatisticTime(System.currentTimeMillis());
            originDetailDTO.setRemark(StrUtil.EMPTY);
            if (StrUtil.isNotBlank(it.getRemark())) {
                originDetailDTO.setRemark(it.getRemark());
            }
            if (TaxTypeEnum.TYPE_DUTY_TAX.getCode().equalsIgnoreCase(it.getTaxType())) {
                originDetailDTO.setRemark(originDetailDTO.getRemark() + ";完税不计入结转");
            }
            if (notBlankEquals(originDetailDTO.getOriginZoneCode(), originDetailDTO.getTargetZoneCode())) {
                originDetailDTO.setRemark(originDetailDTO.getRemark() + ";同库区不计入结转");
            }
            if (notBlankEquals(originDetailDTO.getOriginEnterprise(), originDetailDTO.getTargetEnterprise())) {
                originDetailDTO.setRemark(originDetailDTO.getRemark() + ";同企业不计入结转");
            }
            return originDetailDTO;
        }).collect(Collectors.toList());
    }

    @Override
    @ApiOperation("处理结转原始明细企业信息")
    public synchronized void handleOriginDetailEnterprise() {
        BookCarryoverTaskParam bookCarryoverTaskParam = new BookCarryoverTaskParam();
        bookCarryoverTaskParam.setId(1L);
        BookCarryoverTaskDTO task = bookCarryoverClient.getTask(bookCarryoverTaskParam).getData();
        if (null == task) return;

        if (StrUtil.isBlank(task.getControlContext())) {
            task.setControlContext(JSONUtil.toJsonStr(new HashMap<>()));
        }

        BookCarryoverControlContext controlContext = JSONUtil.parse(task.getControlContext()).toBean(BookCarryoverControlContext.class);
        // 记录一下最大的已处理的ID
        Long maxProcessedOriginDetailId = controlContext.getMaxProcessedOriginDetailId();
        if (null == maxProcessedOriginDetailId) {
            maxProcessedOriginDetailId = 0L;
        }

        BookCarryoverOriginDetailParam originDetailParam = new BookCarryoverOriginDetailParam();
        originDetailParam.setStartId(maxProcessedOriginDetailId);
        originDetailParam.setPageSize(500);
        Page<BookCarryoverOriginDetailDTO> page = remoteBookCarryoverOriginDetailClient.getPage(originDetailParam);
        while (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<BookCarryoverOriginDetailDTO> collect = page.getRecords().stream()
                    .filter(this::originDetailNeedHandle)
                    .collect(Collectors.toList());

            originDetailParam.setStartId(page.getRecords().stream().mapToLong(BookCarryoverOriginDetailDTO::getId).max().orElseThrow(ExceptionUtil::dataError));
            page = remoteBookCarryoverOriginDetailClient.getPage(originDetailParam);

            if (CollectionUtil.isEmpty(collect)) {
                continue;
            }

            fillEnterprise(collect);

            remoteBookCarryoverOriginDetailClient.modifyBatch(collect);
        }

        originDetailParam = new BookCarryoverOriginDetailParam();
        originDetailParam.setStartId(maxProcessedOriginDetailId);
        originDetailParam.setPageSize(500);
        page = remoteBookCarryoverOriginDetailClient.getPage(originDetailParam);
        while (CollectionUtil.isNotEmpty(page.getRecords())) {
            boolean flag = false;
            for (BookCarryoverOriginDetailDTO record : page.getRecords()) {
                if (originDetailNeedHandle(record)) {
                    flag = true;
                    break;
                }
                maxProcessedOriginDetailId = record.getId();
            }
            if (flag) {
                break;
            }
            originDetailParam.setStartId(page.getRecords().stream().mapToLong(BookCarryoverOriginDetailDTO::getId).max().orElseThrow(ExceptionUtil::dataError));
            page = remoteBookCarryoverOriginDetailClient.getPage(originDetailParam);
        }

        BookCarryoverBO bookCarryoverBO = new BookCarryoverBO();
        controlContext.setMaxProcessedOriginDetailId(maxProcessedOriginDetailId);
        task.setControlContext(JSONUtil.toJsonStr(controlContext));
        bookCarryoverBO.setBookCarryoverTaskDTO(task);
        bookCarryoverClient.taskPersistence(bookCarryoverBO);
    }

    @Override
    public void retryCheckTimeoutWarning() {
        BookCarryoverParam bookCarryoverParam = new BookCarryoverParam();
        bookCarryoverParam.setStatus(BookCarryoverStatusEnum.DRAFT.getCode());
        Result<List<BookCarryoverDTO>> listResult = bookCarryoverClient.getList(bookCarryoverParam);
        if (listResult.checkSuccess() && CollectionUtil.isNotEmpty(listResult.getData())) {
            warning(CurrentRouteHolder.getWarehouseCode(), StrUtil.join(StrUtil.EMPTY, "仓库:", CurrentRouteHolder.getWarehouseCode(), "存在待转化的结转单，请及时处理"));
        }
    }

    private boolean originDetailNeedHandle(BookCarryoverOriginDetailDTO detailDTO) {
        // 库区相同不需要处理
        if (notBlankEquals(detailDTO.getOriginZoneCode(), detailDTO.getTargetZoneCode())) {
            return false;
        }
        // 保税不用处理
        if (StrUtil.isNotBlank(detailDTO.getRemark()) && detailDTO.getRemark().contains("完税不计入结转")) {
            return false;
        }
        // 企业都有值不需要处理
        if (StrUtil.isNotBlank(detailDTO.getOriginEnterprise()) && StrUtil.isNotBlank(detailDTO.getTargetEnterprise())) {
            return false;
        }
        return true;
    }

    private void fillEnterprise(List<BookCarryoverOriginDetailDTO> detailDTOList) {
        log.info("before fill enterprise: {}", JSONUtil.toJsonStr(detailDTOList));
        if (CollectionUtil.isEmpty(detailDTOList)) return;

        List<String> zoneCodeList = detailDTOList.stream()
                .flatMap(it -> Stream.of(it.getOriginZoneCode(), it.getTargetZoneCode()))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(zoneCodeList)) return;

        List<PhysicalPartitionBindDTO> physicalPartitionBindDTOList = remoteZoneClient.getPhysicalPartitionList(detailDTOList.get(0).getWarehouseCode(), zoneCodeList);
        if (CollectionUtil.isEmpty(physicalPartitionBindDTOList)) return;

        Map<String, PhysicalPartitionBindDTO> partitionBindDTOMap = physicalPartitionBindDTOList.stream().collect(Collectors.toMap(PhysicalPartitionBindDTO::getZoneCode, Function.identity()));
        for (BookCarryoverOriginDetailDTO bookCarryoverDetailDTO : detailDTOList) {
            if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getOriginZoneCode())) {
                Optional.ofNullable(partitionBindDTOMap.get(bookCarryoverDetailDTO.getOriginZoneCode())).ifPresent(physicalPartitionBindDTO -> {
                    bookCarryoverDetailDTO.setOriginEnterprise(physicalPartitionBindDTO.getLesseeEnterprise());
                });
            }
            if (StrUtil.isNotBlank(bookCarryoverDetailDTO.getTargetZoneCode())) {
                Optional.ofNullable(partitionBindDTOMap.get(bookCarryoverDetailDTO.getTargetZoneCode())).ifPresent(physicalPartitionBindDTO -> {
                    bookCarryoverDetailDTO.setTargetEnterprise(physicalPartitionBindDTO.getLesseeEnterprise());
                });
            }
        }
        log.info("after fill enterprise: {}", JSONUtil.toJsonStr(detailDTOList));
    }
}
