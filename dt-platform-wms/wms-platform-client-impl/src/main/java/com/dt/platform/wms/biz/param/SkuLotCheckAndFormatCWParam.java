package com.dt.platform.wms.biz.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/1/4 17:21
 */
@Data
public class SkuLotCheckAndFormatCWParam implements Serializable {

    @ApiModelProperty(value = "sku属性")
    private String skuQuality;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "收货日期")
    private Long receiveDate;

    @ApiModelProperty(value = "生产批次号 ---CW的批次号")
    private String productionNo;

    @ApiModelProperty(value = "入库关联单号(拓展单号) 不传")
    private String externalLinkBillNo;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

}