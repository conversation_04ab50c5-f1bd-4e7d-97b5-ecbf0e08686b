package com.dt.platform.wms.client;

import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.PretreatmentStatusEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.cargo.CargoOpenEffectEnum;
import com.dt.component.common.enums.message.LargeMessageOperationTypeEnum;
import com.dt.component.common.enums.message.LargeMessageTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.message.LargeMessageDTO;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.domain.bill.dto.ShipmentOrderLogDTO;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.platform.wms.biz.IBusinessLogBiz;
import com.dt.platform.wms.biz.IShipmentOrderParamValidationBiz;
import com.dt.platform.wms.biz.IShipmentOutStockDestroyBiz;
import com.dt.platform.wms.dto.shipment.ShipmentCheckSkuLotBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.message.IRemoteLargeMessageClient;
import com.dt.platform.wms.param.shipment.ShipmentDetailSplitCellParam;
import com.dt.platform.wms.param.shipment.ShipmentDetailSplitParam;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@DubboService(version = "${dubbo.service.version}")
public class ShipmentOrderDetailBizClientImpl implements IShipmentOrderDetailBizClient {

    @Resource
    private IRemoteShipmentDetailClient remoteShipmentDetailClient;

    @Resource
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    IBusinessLogBiz iBusinessLogBiz;

    @Resource
    IRemoteLargeMessageClient remoteLargeMessageClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    IShipmentOutStockDestroyBiz shipmentOutStockDestroyBiz;

    @Resource
    IShipmentOrderParamValidationBiz shipmentOrderParamValidationBiz;

    @Resource
    IRemoteSkuClient remoteSkuClient;

    @Override
    public Result<ShipmentOrderDetailDTO> get(ShipmentOrderDetailParam param) {
        return Result.success(remoteShipmentDetailClient.get(param));
    }

    @Override
    public Result<List<ShipmentOrderDetailDTO>> getList(ShipmentOrderDetailParam param) {
        return Result.success(remoteShipmentDetailClient.getList(param));
    }


    /**
     * 校验被拆分是否混放批次
     *
     * @param splitCommitRows
     * @param originDetailDTOList
     */
    private void checkObscureSkuQuality(List<ShipmentDetailSplitCellParam> splitCommitRows, List<ShipmentOrderDetailDTO> originDetailDTOList, ShipmentOrderDTO shipmentOrderDTO) {
        //被拆的记录 有ID的记录
        List<ShipmentDetailSplitCellParam> splitSourceList = splitCommitRows.stream()
                .filter(s -> s.getSourceId() == 0 && s.getId() != null && s.getId() > 0).collect(Collectors.toList());
        for (ShipmentDetailSplitCellParam rowSource : splitSourceList) {
            ShipmentOrderDetailDTO detailDTO = originDetailDTOList.stream().filter(s -> s.getId().equals(rowSource.getId())).findAny().get();
            String skuCode = detailDTO.getSkuCode();

            ArrayList<ShipmentDetailSplitCellParam> groupByList = new ArrayList<>();
            groupByList.add(rowSource);
            List<ShipmentDetailSplitCellParam> childDetailList = splitCommitRows.stream().filter(s -> rowSource.getId().equals(s.getSourceId())).collect(Collectors.toList());
            rowSource.setSkuCode(skuCode);

            if (StringUtils.isEmpty(rowSource.getSkuLotNo())) {
                rowSource.setSkuQuality(detailDTO.getSkuQuality());
            } else {
                SkuLotParam skuLotParam = new SkuLotParam();
                skuLotParam.setSkuCodeList(Arrays.asList(skuCode));
                skuLotParam.setCode(rowSource.getSkuLotNo());
                skuLotParam.setCargoCode(detailDTO.getCargoCode());
                List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
                if (CollectionUtils.isEmpty(skuLotDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s,批次号:%s不存在", skuCode, rowSource.getSkuLotNo()));
                }
                List<String> skuQualityList = skuLotDTOList.stream().map(SkuLotDTO::getSkuQuality).collect(Collectors.toList());
                if (!skuQualityList.contains(detailDTO.getSkuQuality())) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s,批次号:%s正次品属性不一致", skuCode, rowSource.getSkuLotNo()));
                }
                //淘天校验残次等级需要和批次一致
                if (shipmentOrderDTO.getOrderTag() > 0 && OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.TAOTAIN)) {
                    List<String> inventoryTypeList = skuLotDTOList.stream().map(SkuLotDTO::getInventoryType).collect(Collectors.toList());
                    if (!StringUtils.isEmpty(detailDTO.getInventoryType()) && !inventoryTypeList.contains(detailDTO.getInventoryType())) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s,指定批次号:%s,残次等级与原始残次等级不一致", skuCode, rowSource.getSkuLotNo()));
                    }
                }
                rowSource.setSkuQuality(detailDTO.getSkuQuality());
            }
            //处理子集
            for (ShipmentDetailSplitCellParam child : childDetailList) {

                if (StringUtils.isEmpty(child.getSkuLotNo())) {
                    child.setSkuQuality(detailDTO.getSkuQuality());
                } else {
                    SkuLotParam skuLotParam = new SkuLotParam();
                    skuLotParam.setSkuCodeList(Arrays.asList(skuCode));
                    skuLotParam.setCode(child.getSkuLotNo());
                    skuLotParam.setCargoCode(detailDTO.getCargoCode());
                    List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
                    if (CollectionUtils.isEmpty(skuLotDTOList)) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s,批次号:%s不存在", skuCode, rowSource.getSkuLotNo()));
                    }
                    List<String> skuQualityList = skuLotDTOList.stream().map(SkuLotDTO::getSkuQuality).collect(Collectors.toList());
                    if (!skuQualityList.contains(detailDTO.getSkuQuality())) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s,批次号:%s正次品属性不一致", skuCode, rowSource.getSkuLotNo()));
                    }
                    //淘天校验残次等级需要和批次一致
                    if (shipmentOrderDTO.getOrderTag() > 0 && OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.TAOTAIN)) {
                        List<String> inventoryTypeList = skuLotDTOList.stream().map(SkuLotDTO::getInventoryType).collect(Collectors.toList());
                        if (!StringUtils.isEmpty(detailDTO.getInventoryType()) && !inventoryTypeList.contains(detailDTO.getInventoryType())) {
                            throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s,指定批次号:%s,残次等级与原始残次等级不一致", skuCode, rowSource.getSkuLotNo()));
                        }
                    }
                    child.setSkuQuality(detailDTO.getSkuQuality());
                }
                child.setSkuCode(skuCode);
                groupByList.add(child);
            }
        }
//        //校验正次品混放
//        List<String> skuQuality = splitCommitRows.stream().map(ShipmentDetailSplitCellParam::getSkuQuality).distinct().collect(Collectors.toList());
//        //如果大于1，则说明批次信息有正品和次品
//        if (skuQuality.size() > 1) {
//            throw new BaseException(BaseBizEnum.TIP, String.format("拆分出库单批次只能正品或者次品，不允许正次品混放"));
//        } else if (CollectionUtils.isEmpty(skuQuality)) {
//            throw new BaseException(BaseBizEnum.TIP, String.format("拆分出库单正次品属性异常数据"));
//        }

    }

//    /**
//     * 修改编辑源的记录
//     *
//     * @param source
//     * @param changedCell
//     */
//    private void changeRowSplitSource(ShipmentOrderDetailDTO source, ShipmentDetailSplitCellParam changedCell) {
////        String oldSkuLotDTO = source.getSkuLotNo();
////        BigDecimal oldExpSkuQty = source.getExpSkuQty() == null ? BigDecimal.ZERO : source.getExpSkuQty();
//
////        String skuLotNokey = org.apache.commons.lang3.StringUtils.join(source.getId(), changedCell.getSkuLotNo(), changedCell.getSkuCode());
////        SkuLotDTO skuLotDTO = oretMap.get(skuLotNokey);
////        if (Objects.nonNull(skuLotDTO)) {
////            source.setReceiveDate(skuLotDTO.getReceiveDate());
////            source.setReceiveDateFormat(skuLotDTO.getReceiveDateFormat());
////            source.setManufDate(skuLotDTO.getManufDate());
////            source.setManufDateFormat(skuLotDTO.getManufDateFormat());
////            source.setExpireDate(skuLotDTO.getExpireDate());
////            source.setExpireDateFormat(skuLotDTO.getExpireDateFormat());
////            source.setSkuQuality(skuLotDTO.getSkuQuality());
////            source.setProductionNo(skuLotDTO.getProductionNo());
////            source.setWithdrawDate(skuLotDTO.getWithdrawDate());
////            source.setWithdrawDateFormat(skuLotDTO.getWithdrawDateFormat());
////        } else {
////            source.setReceiveDate(0L);
////            source.setReceiveDateFormat("");
////            source.setManufDate(0L);
////            source.setManufDateFormat("");
////            source.setExpireDate(0L);
////            source.setExpireDateFormat("");
////            source.setProductionNo("");
////            source.setWithdrawDate(0L);
////            source.setWithdrawDateFormat("");
////        }
//        source.setSkuQuality(changedCell.getSkuQuality());
////        source.setSkuLotNo(changedCell.getSkuLotNo());
//        source.setExpSkuQty(changedCell.getExpSkuQty());
//        source.setExternalSkuLotNo(changedCell.getExternalSkuLotNo());
////        return !(org.apache.commons.lang3.StringUtils.equalsIgnoreCase(oldSkuLotDTO, source.getSkuLotNo()) && oldExpSkuQty.compareTo(source.getExpSkuQty()) == 0);
//    }

//    /**
//     * 记录修改日志记录
//     */
//    private String changeLogInfo(ShipmentOrderDetailDTO newDetail, ShipmentOrderDetailDTO oldDetail) {
//        if (newDetail != null && oldDetail != null) {
//            if (newDetail.getId() != null && oldDetail.getId() != null) {
//                BigDecimal newExpSkuQty = newDetail.getExpSkuQty() == null ? BigDecimal.ZERO : newDetail.getExpSkuQty();
//                BigDecimal oldExpSkuQty = oldDetail.getExpSkuQty() == null ? BigDecimal.ZERO : oldDetail.getExpSkuQty();
//                String newSkuLot = (newDetail.getSkuLotNo() == null) ? "" : newDetail.getSkuLotNo();
//                String oldSkuLot = (oldDetail.getSkuLotNo() == null) ? "" : oldDetail.getSkuLotNo();
//                if (newSkuLot.equalsIgnoreCase(oldSkuLot) && newExpSkuQty.compareTo(oldExpSkuQty) == 0) {
//                    return null;
//                } else {
//                    if (newSkuLot.equalsIgnoreCase(oldSkuLot)) {
//                        return String.format("拆分修改记录SKU:[%s],修改前数量:[%s],修改后数量:[%s]", oldDetail.getSkuCode(),
//                                oldExpSkuQty.toString(), newExpSkuQty.toString());
//                    } else if (newExpSkuQty.compareTo(oldExpSkuQty) == 0) {
//                        return String.format("拆分修改记录SKU:[%s],修改前批次号:[%s],修改后批次号:[%s]", oldDetail.getSkuCode(),
//                                oldSkuLot, newSkuLot);
//                    } else {
//                        return String.format("拆分修改记录SKU:[%s],修改前批次号:[%s],修改后批次号:[%s],修改前数量:[%s],修改后数量:[%s]", oldDetail.getSkuCode(),
//                                oldSkuLot, newSkuLot, oldExpSkuQty.toString(), newExpSkuQty.toString());
//                    }
//                }
//            }
//        } else if (newDetail == null && oldDetail != null) {
//            BigDecimal oldExpSkuQty = oldDetail.getExpSkuQty() == null ? BigDecimal.ZERO : oldDetail.getExpSkuQty();
//            String oldSkuLot = (oldDetail.getSkuLotNo() == null) ? "" : oldDetail.getSkuLotNo();
//            return String.format("拆分删除记录SKU:[%s],批次号:[%s],数量:[%s]", oldDetail.getSkuCode(),
//                    oldSkuLot, oldExpSkuQty.toString());
//        } else if (newDetail != null && oldDetail == null) {
//            BigDecimal newExpSkuQty = newDetail.getExpSkuQty() == null ? BigDecimal.ZERO : newDetail.getExpSkuQty();
//            String newSkuLot = (newDetail.getSkuLotNo() == null) ? "" : newDetail.getSkuLotNo();
//            return String.format("拆分新增记录SKU:[%s],批次号:[%s],数量:[%s]", newDetail.getSkuCode(),
//                    newSkuLot, newExpSkuQty.toString());
//        }
//        return null;
//    }

    /**
     * 防止删除行，增添加相同行，这类日志就不记录
     *
     * @param
     * @param
     * @return
     */
    private ShipmentOrderLogDTO convertOrderLog(List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList, String logRecord) {
        ShipmentOrderLogDTO shipmentOrderLogDTO = new ShipmentOrderLogDTO();
        shipmentOrderLogDTO.setOpDate(System.currentTimeMillis());
        shipmentOrderLogDTO.setCargoCode(shipmentOrderDetailDTOList.get(0).getCargoCode());
        shipmentOrderLogDTO.setWarehouseCode(shipmentOrderDetailDTOList.get(0).getWarehouseCode());
        shipmentOrderLogDTO.setShipmentOrderCode(shipmentOrderDetailDTOList.get(0).getShipmentOrderCode());
        shipmentOrderLogDTO.setOpBy(CurrentUserHolder.getUserName());
        shipmentOrderLogDTO.setOpContent("明细拆分操作日志");
        shipmentOrderLogDTO.setOpRemark("WMS修改指定批次");
        return shipmentOrderLogDTO;
    }

//    /**
//     * 记录日志信息
//     *
//     * @param fromSourceDetail
//     * @param toSplitChild
//     * @param logDTOList
//     */
//    private void traceChangedLog(ShipmentOrderDetailDTO fromSourceDetail, List<ShipmentOrderDetailDTO> toSplitChild, List<ShipmentOrderLogDTO> logDTOList) {
//        List<ShipmentOrderDetailDTO> clone_toSplitChild = Lists.newArrayList();
//        clone_toSplitChild.addAll(toSplitChild);
//        ShipmentOrderDetailParam param = new ShipmentOrderDetailParam();
//        param.setShipmentOrderCode(fromSourceDetail.getShipmentOrderCode());
//        List<ShipmentOrderDetailDTO> oldChangedDetailList = remoteShipmentDetailClient.getList(param);
//        ShipmentOrderDetailDTO oldChangedSourceDetail = oldChangedDetailList.stream().filter(s -> s.getId().equals(fromSourceDetail.getId())).findFirst().orElse(null);
//        List<ShipmentOrderDetailDTO> oldSplitChild = oldChangedDetailList.stream().filter(s -> fromSourceDetail.getId().equals(s.getRefSplitDetailId())).collect(Collectors.toList());
//        /**
//         * 非法操作记录，或者传递参数有问题
//         */
//        if (oldChangedSourceDetail == null) {
//            return;
//        }
//        StringBuffer logStringBuffer = new StringBuffer();
//        String logInfo = changeLogInfo(fromSourceDetail, oldChangedSourceDetail);
//        if (!StringUtils.isEmpty(logInfo)) {
//            logStringBuffer.append(logInfo);
//        }
//        if (!CollectionUtils.isEmpty(oldSplitChild)) {
//            for (ShipmentOrderDetailDTO oldDetail : oldSplitChild) {
//                ShipmentOrderDetailDTO newDetail = clone_toSplitChild.stream().filter(s -> oldDetail.getId().equals(s.getId())).findFirst().orElse(null);
//                if (newDetail != null) {
//                    clone_toSplitChild.removeIf(s -> oldDetail.getId().equals(s.getId()));
//                }
//                logInfo = changeLogInfo(newDetail, oldDetail);
//                if (!StringUtils.isEmpty(logInfo)) {
//                    logStringBuffer.append(logInfo);
//                }
//            }
//        }
//        if (!CollectionUtils.isEmpty(clone_toSplitChild)) {
//            for (ShipmentOrderDetailDTO newDetail : clone_toSplitChild) {
//                logInfo = changeLogInfo(newDetail, null);
//                if (!StringUtils.isEmpty(logInfo)) {
//                    logStringBuffer.append(logInfo);
//                }
//            }
//        }
//        if (!StringUtils.isEmpty(logStringBuffer.toString())) {
//            ShipmentOrderLogDTO shipmentOrderLogDTO = new ShipmentOrderLogDTO();
//            shipmentOrderLogDTO.setOpDate(System.currentTimeMillis());
//            shipmentOrderLogDTO.setCargoCode(oldChangedSourceDetail.getCargoCode());
//            shipmentOrderLogDTO.setWarehouseCode(oldChangedSourceDetail.getWarehouseCode());
//            shipmentOrderLogDTO.setShipmentOrderCode(oldChangedSourceDetail.getShipmentOrderCode());
//            shipmentOrderLogDTO.setOpBy(CurrentUserHolder.getUserName());
//            shipmentOrderLogDTO.setOpContent(logStringBuffer.toString());
//            logDTOList.add(shipmentOrderLogDTO);
//        }
//    }

    @Override
    public Result<Boolean> doSplitDetail(ShipmentDetailSplitParam shipmentDetailSplitBizParam) {
        //前端提交数据
        List<ShipmentDetailSplitCellParam> splitCommitRows = shipmentDetailSplitBizParam.getShipmentDetailSplitCellParamList();
        if (CollectionUtils.isEmpty(splitCommitRows)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("拆分记录为空"));
        }
        //1. 订单可以指定批次的条件
        //
        //订单上无【上游占用三级库存】标记
        //订单状态为【创建】且未经过预处理
        //B2C 且 非 【淘天】订单（B2C的淘天订单没必要内部指定批次id）
        //如为B2B订单 且为 【淘天】订单，指定批次id的残次等级 和上游下发指定的残次等级一致
        //2. 订单指定批次后：
        //
        //淘天的保留： 批次ID、残次等级、商品属性
        //非淘天的保留： 批次id、 商品属性

        String logRecord = JSONUtil.toJsonStr(splitCommitRows);
        //输入数据trim
        splitCommitRows.forEach(s -> {
            s.setSkuLotNo(s.getSkuLotNo() == null ? "" : s.getSkuLotNo().trim());
            s.setLocationCode(s.getLocationCode() == null ? "" : s.getLocationCode().trim());
        });
        //判定参数不能为空
        if (StringUtils.isEmpty(shipmentDetailSplitBizParam.getShipmentOrderCode())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("出库单号参数不能为空"));
        }
        //根据被拆分的源记录分组处理下，按照各个分组一组组处理 设置行号提示 获取数据库的出库单数据
        ShipmentOrderParam param = new ShipmentOrderParam();
        param.setShipmentOrderCode(shipmentDetailSplitBizParam.getShipmentOrderCode());
        ShipmentOrderDTO shipmentOrderDTO = remoteShipmentOrderClient.get(param);
        if (shipmentOrderDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("出库单号:%s查询对象为空", shipmentDetailSplitBizParam.getShipmentOrderCode()));
        }
        //预处理之前的数据可以指定批次
        if (!shipmentOrderDTO.getStatus().equalsIgnoreCase(ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "出库单状态不为创建状态");
        }
        //预处理之前的数据可以指定批次
        if (!shipmentOrderDTO.getPretreatmentStatus().equalsIgnoreCase(PretreatmentStatusEnum.INIT.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "出库单已经在预处理中");
        }
        if (OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.OCCUPY_LOCATION_STOCK)) {
            throw new BaseException(BaseBizEnum.TIP, "出库单已经占用三级库存,不能指定批次和库位");
        }
        if (Objects.equals(ShipmentOrderEnum.ORDER_TYPE.PURCHASE_REDEEM_OUT.getCode(), shipmentOrderDTO.getOrderType())) {
            throw new BaseException(BaseBizEnum.TIP, "出库单代采赎回出库,不能指定批次和库位");
        }
        if (shipmentOrderDTO.getOrderTag() > 0 && OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.TAOTAIN)
                && Objects.equals(shipmentOrderDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())) {
            throw new BaseException(BaseBizEnum.TIP, "淘天C单,不能指定批次和库位");
        }

        //获取当前出库单明细数据
        ShipmentOrderDetailParam detailParam = new ShipmentOrderDetailParam();
        detailParam.setShipmentOrderCode(shipmentOrderDTO.getShipmentOrderCode());
        List<ShipmentOrderDetailDTO> originDetailDTOList = remoteShipmentDetailClient.getList(detailParam);
        if (CollectionUtils.isEmpty(originDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("出库单号:%s查询明细为空", shipmentDetailSplitBizParam.getShipmentOrderCode()));
        }
        //校验货主和强效期货主
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(shipmentOrderDTO.getCargoCode());
        if (Objects.isNull(cargoOwnerDTO)) {
            throw new BaseException(BaseBizEnum.TIP, "出库单所属货主信息不存在。");
        }
        if (cargoOwnerDTO.getOpenEffect().equals(CargoOpenEffectEnum.OPEN.getValue())) {
            //TODO 校验强效期批次
//            throw new BaseException(BaseBizEnum.TIP, "出库单所属货主信息开启了效期强校验。");
            splitCommitRows.forEach(a -> {
                if (StringUtils.isEmpty(a.getSkuLotNo())) {
                    throw new BaseException(BaseBizEnum.TIP, "出库单所属货主信息开启了效期强校验,批次不能为空");
                }
            });
        }
        //check提交商品的数量不能超数据库的数量
        this.checkSkuQtyNotMoreThan(originDetailDTOList, splitCommitRows, cargoOwnerDTO.getOpenEffect().equals(CargoOpenEffectEnum.OPEN.getValue()));

        //记录分析 toSourceShipmentOrderList 前端【数据库】新拆出来的list  fromSourceShipmentOrderList 前端【数据库】原始数据记录
        List<ShipmentOrderDetailDTO> fromSourceShipmentOrderList = originDetailDTOList.stream()
                .filter(s -> s.getRefSplitDetailId() == null || s.getRefSplitDetailId() == 0).collect(Collectors.toList());
        List<ShipmentOrderDetailDTO> toSourceShipmentOrderList = originDetailDTOList.stream()
                .filter(s -> s.getRefSplitDetailId() != null && s.getRefSplitDetailId() > 0).collect(Collectors.toList());

        //记录分析  前端提交【数据库数据(sourceId)】的list
        List<ShipmentDetailSplitCellParam> groupBySourceIdMap = splitCommitRows.stream()
                .filter(s -> s.getSourceId() == 0).collect(Collectors.toList());
        List<Long> toIds = groupBySourceIdMap.stream().map(s -> s.getId()).collect(Collectors.toList());

        List<Long> fromIds = fromSourceShipmentOrderList.stream().map(s -> s.getId()).collect(Collectors.toList());

        if (!(CollectionUtils.containsAny(fromIds, toIds) && CollectionUtils.containsAny(toIds, fromIds))) {
            throw new BaseException(BaseBizEnum.TIP, String.format("系统检查传递参数异常fromIds:%s,toIds:%s", fromIds.toString(), toIds.toString()));
        }
        //校验正次品
        this.checkObscureSkuQuality(splitCommitRows, originDetailDTOList, shipmentOrderDTO);

        Map<Long, List<ShipmentDetailSplitCellParam>> groupBySplitSourceIdMap = splitCommitRows.stream()
                .filter(s -> s.getSourceId() > 0).collect(Collectors.groupingBy(ShipmentDetailSplitCellParam::getSourceId));

        List<ShipmentOrderDetailDTO> splitChangedDetail = new ArrayList<>();
        //有拆分记录的商品
        if (!CollectionUtils.isEmpty(groupBySplitSourceIdMap)) {
            for (Map.Entry<Long, List<ShipmentDetailSplitCellParam>> entry : groupBySplitSourceIdMap.entrySet()) {
                Long sourceId = entry.getKey();
                Optional<ShipmentOrderDetailDTO> optionalShipmentOrderDetailDTO = originDetailDTOList.stream()
                        .filter(s -> s.getId().equals(sourceId)).findFirst();
                if (!optionalShipmentOrderDetailDTO.isPresent()) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("被拆分ID:%s,无法找到源记录", sourceId));
                }
                //源sourceId记录
                ShipmentOrderDetailDTO currentDetail = optionalShipmentOrderDetailDTO.get();
                int rowIndex = 0;
                List<ShipmentOrderDetailDTO> splitRowChangedDetail = new ArrayList<>();
                //当前sourceId的所有拆分记录
                for (ShipmentDetailSplitCellParam cellParam : entry.getValue()) {
                    rowIndex++;
                    if (cellParam.getSourceId() == null || cellParam.getSourceId() <= 0) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s,参数错误sourceId为空", currentDetail.getSkuCode()));
                    }
                    if (cellParam.getExpSkuQty() == null || BigDecimal.ZERO.compareTo(cellParam.getExpSkuQty()) >= 0) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s,请输入拆分数量", currentDetail.getSkuCode()));
                    }
                    if (cellParam.getSourceId() != null && cellParam.getSourceId() <= 0) {
                        throw new BaseException(BaseBizEnum.TIP,
                                String.format("商品代码:%s,出库明细属参数SourceId:[%s]异常", currentDetail.getSkuCode(), cellParam.getSourceId()));
                    }
                    ShipmentOrderDetailDTO splitShipmentOrderDetailDTO = new ShipmentOrderDetailDTO();
                    if (cellParam.getId() == null) {
                        BeanUtils.copyProperties(currentDetail, splitShipmentOrderDetailDTO);
                        splitShipmentOrderDetailDTO.setId(null);
                    } else {
                        Optional<ShipmentOrderDetailDTO> optionShipmentOrderDetailDTO = toSourceShipmentOrderList.stream()
                                .filter(s -> s.getId().equals(cellParam.getId())).findFirst();
                        if (!optionShipmentOrderDetailDTO.isPresent()) {
                            throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s,出库明细查找空对象参数异常", currentDetail.getSkuCode()));
                        }
                        BeanUtils.copyProperties(optionShipmentOrderDetailDTO.get(), splitShipmentOrderDetailDTO);
                    }
                    splitShipmentOrderDetailDTO.setSourceExpSkuQty(BigDecimal.ZERO);
                    splitShipmentOrderDetailDTO.setStatus(ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode());
                    splitShipmentOrderDetailDTO.setExpSkuQty(cellParam.getExpSkuQty());
                    splitShipmentOrderDetailDTO.setSkuLotNo(cellParam.getSkuLotNo());
                    splitShipmentOrderDetailDTO.setSkuQuality(cellParam.getSkuQuality());
                    splitShipmentOrderDetailDTO.setLocationCode(cellParam.getLocationCode());
                    splitShipmentOrderDetailDTO.setRefSplitDetailId(currentDetail.getId());
                    //编辑的行删除掉
                    toSourceShipmentOrderList.removeIf(s -> s.getId().equals(cellParam.getId()));
                    splitRowChangedDetail.add(splitShipmentOrderDetailDTO);
                }
                //当前拆分的记录
                ShipmentDetailSplitCellParam shipmentDetailSplitCellParam = splitCommitRows.stream()
                        .filter(s -> sourceId.equals(s.getId())).findFirst().get();
                if (shipmentDetailSplitCellParam.getSourceId() != null && shipmentDetailSplitCellParam.getSourceId() > 0) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s出库明细系统检测参数异常", currentDetail.getSkuCode()
                    ));
                }
                if (shipmentDetailSplitCellParam.getExpSkuQty() == null || BigDecimal.ZERO.compareTo(shipmentDetailSplitCellParam.getExpSkuQty()) >= 0) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s出库明细输入拆分数量参数异常", currentDetail.getSkuCode()
                    ));
                }
                if (currentDetail.getRefSplitDetailId() != null && currentDetail.getRefSplitDetailId() > 0) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s出库明细不于源明细,不允许执行拆单操作", currentDetail.getSkuCode()
                    ));
                }
                //上线初始化数值
                BigDecimal sourceExpSkuQty = (currentDetail.getSourceExpSkuQty() == null || currentDetail.getSourceExpSkuQty().compareTo(BigDecimal.ZERO) <= 0) ?
                        currentDetail.getExpSkuQty() : currentDetail.getSourceExpSkuQty();
                if (currentDetail.getSourceExpSkuQty() == null || currentDetail.getSourceExpSkuQty().compareTo(BigDecimal.ZERO) <= 0) {
                    currentDetail.setSourceExpSkuQty(sourceExpSkuQty);
                }
                currentDetail.setSkuLotNo(shipmentDetailSplitCellParam.getSkuLotNo());
                currentDetail.setExpSkuQty(shipmentDetailSplitCellParam.getExpSkuQty());
                currentDetail.setLocationCode(shipmentDetailSplitCellParam.getLocationCode());
                //删除被删除对象
                groupBySourceIdMap.removeIf(s -> s.getId().equals(currentDetail.getId()));
                //被拆分数量的总和
                BigDecimal splitExpSkuQty = entry.getValue().stream().map(ShipmentDetailSplitCellParam::getExpSkuQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal sumSkuQty = splitExpSkuQty.add(currentDetail.getExpSkuQty());
                int compareTo = sumSkuQty.compareTo(sourceExpSkuQty);
                //比较拆分数值,如果输入数量过大报错提示
                if (compareTo != 0) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("对不起,商品代码:%s拆分数量之和:%s不等于源数量:%s", currentDetail.getSkuCode(), sumSkuQty, sourceExpSkuQty));
                }
                //新拆分的记录
                splitChangedDetail.addAll(splitRowChangedDetail);
                //拆分原始记录
                splitChangedDetail.add(currentDetail);
            }
        }
        //无拆分记录
        int rowIndex = 0;
        for (ShipmentDetailSplitCellParam entry : groupBySourceIdMap) {
            rowIndex++;
            Optional<ShipmentOrderDetailDTO> optionalShipmentOrderDetailDTO = originDetailDTOList.stream()
                    .filter(s -> s.getId().equals(entry.getId())).findFirst();
            if (!optionalShipmentOrderDetailDTO.isPresent()) {
                throw new BaseException(BaseBizEnum.TIP, String.format("被拆分ID:%s,无法找到源记录", entry.getId()));
            }
            ShipmentOrderDetailDTO currentDetail = optionalShipmentOrderDetailDTO.get();
            if (entry.getSourceId() != null && entry.getSourceId() > 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s出库明细系统检测参数异常", currentDetail.getSkuCode()));
            }
            if (entry.getExpSkuQty() == null || BigDecimal.ZERO.compareTo(entry.getExpSkuQty()) >= 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s出库明细输入拆分数量参数异常", currentDetail.getSkuCode()));
            }
            if (currentDetail.getRefSplitDetailId() != null && currentDetail.getRefSplitDetailId() > 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品代码:%s出库明细不于源明细，不允许执行拆单操作", currentDetail.getSkuCode()));
            }

            BigDecimal sourceExpSkuQty = (currentDetail.getSourceExpSkuQty() == null || currentDetail.getSourceExpSkuQty().compareTo(BigDecimal.ZERO) <= 0) ?
                    currentDetail.getExpSkuQty() : currentDetail.getSourceExpSkuQty();

            currentDetail.setSkuLotNo(entry.getSkuLotNo());
            currentDetail.setLocationCode(entry.getLocationCode());
            if (currentDetail.getSourceExpSkuQty() == null || currentDetail.getSourceExpSkuQty().compareTo(BigDecimal.ZERO) <= 0) {
                currentDetail.setSourceExpSkuQty(sourceExpSkuQty);
            }
            int compareTo = currentDetail.getExpSkuQty().compareTo(sourceExpSkuQty);
            if (compareTo != 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("对不起,商品代码:%s拆分数量之和:%s不等于源数量:%s", currentDetail.getSkuCode(), currentDetail.getExpSkuQty(), sourceExpSkuQty));
            }
            splitChangedDetail.add(currentDetail);
        }

        //二次校验 check提交商品的数量不能超数据库的数量
        this.checkSkuQtyNotMoreThanSecond(shipmentOrderDTO.getShipmentOrderCode(), splitChangedDetail, cargoOwnerDTO.getOpenEffect().equals(CargoOpenEffectEnum.OPEN.getValue()));
        //校验批次的存在性 和B2B的销毁出库 批次必填 效期商品，正品过期或次品,非效期 次品
        checkDestoryOutAndExternalSkuLotNo(splitChangedDetail, shipmentOrderDTO);
        //校验库位 B2C校验拣选位  B2B检验拣选和存储位
        shipmentOrderParamValidationBiz.checkShipmentLocationAndSkuLot(shipmentOrderDTO, splitChangedDetail);
        boolean flag = false;
        if (!CollectionUtils.isEmpty(splitChangedDetail)) {
            //更新
            splitChangedDetail.forEach(shipmentOrderDetailDTO -> {
                //凡是有指定批次ID,其他属性一律置空
                if (!StringUtils.isEmpty(shipmentOrderDetailDTO.getSkuLotNo())) {
                    shipmentOrderDetailDTO.setExternalSkuLotNo("");
                    shipmentOrderDetailDTO.setExpireDate(0L);
                    shipmentOrderDetailDTO.setExpireDateStart(0L);
                    shipmentOrderDetailDTO.setExpireDateEnd(0L);
                }
            });
            flag = remoteShipmentDetailClient.splitSaveBatchDetail(splitChangedDetail, toSourceShipmentOrderList);
            if (flag) {
                //记录提交明细日志信息
                ShipmentOrderLogDTO logDTO = convertOrderLog(splitChangedDetail, logRecord);
                iBusinessLogBiz.saveShipmentLogList(Lists.newArrayList(logDTO));
                //记录原始报文
                LargeMessageDTO largeMessageDTO = buildLargeMessageDTO(shipmentOrderDTO, LargeMessageOperationTypeEnum.BILL_OPERATION_UPDATE);
                largeMessageDTO.setMessage(logRecord);
                remoteLargeMessageClient.save(largeMessageDTO);
            }
        }
        return Result.success(flag);
    }

    /**
     * @param shipmentOrderDTO
     * @param operationTypeEnum
     * @return com.dt.domain.base.dto.message.LargeMessageDTO
     * @author: WuXian
     * description:
     * create time: 2021/8/13 13:26
     */
    private LargeMessageDTO buildLargeMessageDTO(ShipmentOrderDTO shipmentOrderDTO, LargeMessageOperationTypeEnum operationTypeEnum) {
        LargeMessageDTO largeMessageDTO = new LargeMessageDTO();
        largeMessageDTO.setCargoCode(shipmentOrderDTO.getCargoCode());
        largeMessageDTO.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
        largeMessageDTO.setBillNo(shipmentOrderDTO.getShipmentOrderCode());
        largeMessageDTO.setType(LargeMessageTypeEnum.BILL_TYPE_SHIPMENT.getType());
        largeMessageDTO.setOperationType(operationTypeEnum.getType());
        return largeMessageDTO;
    }

    /**
     * @param splitCommitRows
     * @param shipmentOrderDTO
     * @return void
     * @author: WuXian
     * description:
     * create time: 2021/7/28 10:12
     */
    private void checkDestoryOutAndExternalSkuLotNo(List<ShipmentOrderDetailDTO> splitCommitRows, ShipmentOrderDTO shipmentOrderDTO) {
        splitCommitRows.forEach(shipmentDetailSplitCellParam -> {
            if (shipmentOrderDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())
                    && shipmentOrderDTO.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.DESTROY_OUT_STOCK_TYPE.getCode())) {
                ShipmentCheckSkuLotBizDTO shipmentCheckSkuLotBizDTO = new ShipmentCheckSkuLotBizDTO();
                shipmentCheckSkuLotBizDTO.setCargoCode(shipmentDetailSplitCellParam.getCargoCode());
                shipmentCheckSkuLotBizDTO.setSkuQuality(shipmentDetailSplitCellParam.getSkuQuality());
                shipmentCheckSkuLotBizDTO.setSkuCode(shipmentDetailSplitCellParam.getSkuCode());
                shipmentCheckSkuLotBizDTO.setSkuLotNo(shipmentDetailSplitCellParam.getSkuLotNo());
                shipmentOutStockDestroyBiz.checkOutStockDestroySkuLot(shipmentCheckSkuLotBizDTO);
            } else {
                if (!StringUtils.isEmpty(shipmentDetailSplitCellParam.getExternalSkuLotNo())) {
                    ShipmentCheckSkuLotBizDTO shipmentCheckSkuLotBizDTO = new ShipmentCheckSkuLotBizDTO();
                    shipmentCheckSkuLotBizDTO.setCargoCode(shipmentDetailSplitCellParam.getCargoCode());
                    shipmentCheckSkuLotBizDTO.setSkuQuality(shipmentDetailSplitCellParam.getSkuQuality());
                    shipmentCheckSkuLotBizDTO.setSkuCode(shipmentDetailSplitCellParam.getSkuCode());
                    shipmentCheckSkuLotBizDTO.setSkuLotNo(shipmentDetailSplitCellParam.getSkuLotNo());
                    shipmentOutStockDestroyBiz.checkOutStockSkuLotExists(shipmentCheckSkuLotBizDTO);
                }
            }
        });
    }

    private void checkSkuQtyNotMoreThanSecond(String shipmentOrderCode, List<ShipmentOrderDetailDTO> splitChangedDetail, Boolean isEffect) {
        ShipmentOrderDetailParam detailParam = new ShipmentOrderDetailParam();
        detailParam.setShipmentOrderCode(shipmentOrderCode);
        List<ShipmentOrderDetailDTO> originDetailDTOList = remoteShipmentDetailClient.getList(detailParam);
        if (CollectionUtils.isEmpty(originDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("出库单号:%s查询明细为空", shipmentOrderCode));
        }
        List<String> skuCodeList = originDetailDTOList.stream().map(ShipmentOrderDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        for (String skuCode : skuCodeList) {
            BigDecimal originQty = originDetailDTOList.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode))
                    .map(ShipmentOrderDetailDTO::getExpSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal commitQty = splitChangedDetail.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode))
                    .map(ShipmentOrderDetailDTO::getExpSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (commitQty.compareTo(originQty) != 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品编码【%s】,数量异常", skuCode));
            }
        }
        //强效期货主校验批次不能修改
        if (isEffect) {
            for (ShipmentOrderDetailDTO shipmentOrderDetailDTO : originDetailDTOList) {
                String externalSkuLotNo = shipmentOrderDetailDTO.getExternalSkuLotNo();
                if (!StringUtils.isEmpty(externalSkuLotNo)) {
                    if (splitChangedDetail.stream().filter(a -> (a.getId() != null && a.getId() > 0 && a.getId().equals(shipmentOrderDetailDTO.getId()))
                                    || (a.getRefSplitDetailId() != null && a.getRefSplitDetailId() > 0 && a.getRefSplitDetailId().equals(shipmentOrderDetailDTO.getId())))
                            .anyMatch(a -> !a.getExternalSkuLotNo().equalsIgnoreCase(externalSkuLotNo))) {
                        throw new BaseException(BaseBizEnum.TIP, "强效期货主不能修改指定批次");
                    }
                }
            }
        }
    }

    private void checkSkuQtyNotMoreThan(List<ShipmentOrderDetailDTO> originDetailDTOList, List<ShipmentDetailSplitCellParam> splitCommitRows, Boolean isEffect) {
        List<String> skuCodeList = originDetailDTOList.stream().map(ShipmentOrderDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        //校验SKU级别不能超
        for (String skuCode : skuCodeList) {
            BigDecimal originQty = originDetailDTOList.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode))
                    .map(ShipmentOrderDetailDTO::getExpSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal commitQty = splitCommitRows.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode))
                    .map(ShipmentDetailSplitCellParam::getExpSkuQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (commitQty.compareTo(originQty) > 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品编码%s,数量超出", skuCode));
            }
            if (commitQty.compareTo(originQty) < 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品编码%s,提交数量不够", skuCode));
            }
        }
        //强效期货主校验批次不能修改
        if (isEffect) {
            for (ShipmentOrderDetailDTO shipmentOrderDetailDTO : originDetailDTOList) {
                String skuLotNo = shipmentOrderDetailDTO.getSkuLotNo();
                if (!StringUtils.isEmpty(skuLotNo)) {
                    if (splitCommitRows.stream().filter(a -> (a.getId() != null && a.getId() > 0 && a.getId().equals(shipmentOrderDetailDTO.getId()))
                                    || (a.getSourceId() != null && a.getSourceId() > 0 && a.getSourceId().equals(shipmentOrderDetailDTO.getId())))
                            .anyMatch(a -> !a.getSkuLotNo().equalsIgnoreCase(skuLotNo))) {
                        throw new BaseException(BaseBizEnum.TIP, "强效期货主不能修改指定批次");
                    }
                }
            }
        }
    }
}
