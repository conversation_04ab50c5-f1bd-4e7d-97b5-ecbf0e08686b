package com.dt.platform.wms.biz.excel.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.base.OpTypeEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.move.MoveStatusEnum;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.param.*;
import com.dt.domain.bill.bo.MoveBO;
import com.dt.domain.bill.dto.MoveDTO;
import com.dt.domain.bill.dto.MoveDetailDTO;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.wms.biz.IMixRuleCheckBiz;
import com.dt.platform.wms.biz.excel.DefaultReadEventListener;
import com.dt.platform.wms.biz.excel.bo.ExcelImportMoveImportBO;
import com.dt.platform.wms.integration.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component("moveImportReadEventListener")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class MoveImportReadEventListener extends DefaultReadEventListener<ExcelImportMoveImportBO> {

    @Autowired
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Autowired
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Autowired
    private IRemoteLocationClient remoteLocationClient;

    @Autowired
    private IRemoteZoneClient remoteZoneClient;

    @Autowired
    private IMixRuleCheckBiz mixRuleCheckBiz;

    @Autowired
    private IRemoteMixRuleClient remoteMixRuleClient;

    @Autowired
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Autowired
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Autowired
    private IRemoteMoveClient remoteMoveClient;

    private boolean normalCheckFail = false;

    @Override
    public void checkExcel(ExcelImportMoveImportBO importExcel, AnalysisContext context) {
        //校验必填数据
        checkRowNotEmpty(importExcel);
        importExcel.setLineNo(context.readRowHolder().getRowIndex());
    }

    private Boolean checkData(List<ExcelImportMoveImportBO> dataList) {
        // 货主+商品代码+批次ID+来源库位+目标库位： 5列值相同的记录，只允许存在一条，不允许重复，否则报错“不允许有相同记录”
        Set<String> uniqueSet = new HashSet<>();
        boolean result = true;
        if (CollectionUtil.isEmpty(dataList)) return false;
        List<String> skuCodeList = dataList.stream().map(ExcelImportMoveImportBO::getSkuCode).distinct().collect(Collectors.toList());
        List<String> skuLotNoList = dataList.stream().map(ExcelImportMoveImportBO::getSkuLotNo).distinct().collect(Collectors.toList());
        List<String> locationCodeList = dataList.stream().flatMap(it -> Stream.of(it.getOriginLocationCode(), it.getTargetLocationCode())).distinct().collect(Collectors.toList());
        // 查询货主信息
        List<CargoOwnerDTO> cargoOwnerDTOList = remoteCargoOwnerClient.getAllCargoOwner(new CargoOwnerParam());
        Map<String, CargoOwnerDTO> cargoOwnerDTOMap = cargoOwnerDTOList.stream().collect(Collectors.toMap(CargoOwnerDTO::getCode, Function.identity()));
        // 查询商品信息
        List<SkuDTO> skuDTOList = skuDTOList(skuCodeList);
        Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(it -> StrUtil.join(StrUtil.COLON, it.getCargoCode(), it.getCode()), Function.identity()));
        Map<String, SkuDTO> skuDTOCodeMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity(), (a, b) -> a));
        // 查询批次信息
        List<SkuLotDTO> skuLotDTOList = skuLotDTOList(skuLotNoList);
        Map<String, SkuLotDTO> skuLotDTOMap = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity()));
        // 库位信息
        List<LocationDTO> locationDTOList = locationDTOList(locationCodeList);
        Map<String, LocationDTO> locationDTOMap = locationDTOList.stream().collect(Collectors.toMap(LocationDTO::getCode, Function.identity()));
        // 库区信息
        List<String> zoneCodeList = locationDTOList.stream().map(LocationDTO::getZoneCode).distinct().collect(Collectors.toList());
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCodeList(zoneCodeList);
        List<ZoneDTO> zoneDTOList = remoteZoneClient.getList(zoneParam);
        Map<String, ZoneDTO> zoneDTOMap = zoneDTOList.stream().collect(Collectors.toMap(ZoneDTO::getCode, Function.identity()));
        // 允许的库区类型
        List<String> allowZoneTypeList = new ArrayList<>();
        allowZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_STORE.getType());
        allowZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PICK.getType());

        for (ExcelImportMoveImportBO importBO : dataList) {
            LocationDTO originLocation = locationDTOMap.get(importBO.getOriginLocationCode());
            ZoneDTO originZone = null;
            if (ObjectUtil.isEmpty(originLocation)) {
                callBackPublicService(false, uid, importBO.getLineNo(), importBO, "当前仓库来源库位不存在");
                result = false;
            } else {
                originZone = zoneDTOMap.get(originLocation.getZoneCode());
                if (ObjectUtil.isEmpty(originZone)) {
                    callBackPublicService(false, uid, importBO.getLineNo(), importBO, "来源库区信息不存在");
                    result = false;
                } else {
                    importBO.setOriginZoneType(originZone.getType());
                    importBO.setOriginZoneCode(originZone.getCode());
                    if (!allowZoneTypeList.contains(originZone.getType())) {
                        callBackPublicService(false, uid, importBO.getLineNo(), importBO, "来源库位只能是存储位和拣选位");
                        result = false;
                    }
                }
            }
            LocationDTO targetLocation = locationDTOMap.get(importBO.getTargetLocationCode());
            ZoneDTO targetZone = null;
            if (ObjectUtil.isEmpty(targetLocation)) {
                callBackPublicService(false, uid, importBO.getLineNo(), importBO, "当前仓库目标库位不存在");
                result = false;
            } else {
                targetZone = zoneDTOMap.get(targetLocation.getZoneCode());
                if (ObjectUtil.isEmpty(targetZone)) {
                    callBackPublicService(false, uid, importBO.getLineNo(), importBO, "目标库区信息不存在");
                    result = false;
                } else {
                    importBO.setTargetZoneCode(targetZone.getCode());
                    importBO.setTargetZoneType(targetZone.getType());
                    if (!allowZoneTypeList.contains(targetZone.getType())) {
                        callBackPublicService(false, uid, importBO.getLineNo(), importBO, "目标库位只能是存储位和拣选位");
                        result = false;
                    }
                }
            }
            if (importBO.getOriginLocationCode().equalsIgnoreCase(importBO.getTargetLocationCode())) {
                callBackPublicService(false, uid, importBO.getLineNo(), importBO, "移位明细不允许来源库位和目标库位相同");
                result = false;
            }
            CargoOwnerDTO cargoOwnerDTO = cargoOwnerDTOMap.get(importBO.getCargoCode());
            if (ObjectUtil.isEmpty(cargoOwnerDTO)) {
                callBackPublicService(false, uid, importBO.getLineNo(), importBO, "当前仓库无此货主");
                result = false;
            }
            if (!skuDTOCodeMap.containsKey(importBO.getSkuCode())) {
                callBackPublicService(false, uid, importBO.getLineNo(), importBO, "当前仓库无此货主商品信息");
                result = false;
            }
            if (cargoOwnerDTOMap.containsKey(importBO.getCargoCode()) && skuDTOCodeMap.containsKey(importBO.getSkuCode())) {
                String skuKey = StrUtil.join(StrUtil.COLON, importBO.getCargoCode(), importBO.getSkuCode());
                if (!skuDTOMap.containsKey(skuKey)) {
                    callBackPublicService(false, uid, importBO.getLineNo(), importBO, "当前货主和商品不匹配");
                    result = false;
                }
            }
            SkuLotDTO skuLotDTO = skuLotDTOMap.get(importBO.getSkuLotNo());
            if (ObjectUtil.isEmpty(skuLotDTO)) {
                callBackPublicService(false, uid, importBO.getLineNo(), importBO, "当前商品下无此批次信息");
                result = false;
            } else {
                importBO.setSkuQuality(skuLotDTO.getSkuQuality());
                if (!skuLotDTO.getCargoCode().equalsIgnoreCase(importBO.getCargoCode()) || !skuLotDTO.getSkuCode().equalsIgnoreCase(importBO.getSkuCode())) {
                    callBackPublicService(false, uid, importBO.getLineNo(), importBO, "当前商品下无此批次信息");
                    result = false;
                }
            }

            if (skuLotDTO != null && originZone != null && targetZone != null) {
                if (!skuLotDTO.getSkuQuality().equalsIgnoreCase(originZone.getSkuQuality()) || !skuLotDTO.getSkuQuality().equalsIgnoreCase(targetZone.getSkuQuality())) {
                    callBackPublicService(false, uid, importBO.getLineNo(), importBO, "来源库位或目标库位的正残属性和商品批次的正残属性不一致");
                    result = false;
                }
            }

            String uniqueKey = StrUtil.join(StrUtil.COLON, importBO.getCargoCode().toUpperCase(), importBO.getSkuCode().toUpperCase(), importBO.getSkuLotNo().toUpperCase(), importBO.getOriginLocationCode().toUpperCase(), importBO.getTargetLocationCode().toUpperCase());
            if (uniqueSet.contains(uniqueKey)) {
                callBackPublicService(false, uid, importBO.getLineNo(), importBO, "不允许有相同记录");
                result = false;
            }
            uniqueSet.add(uniqueKey);
        }

        // 批次混放
        Map<String, List<ExcelImportMoveImportBO>> listMap = dataList.stream().collect(Collectors.groupingBy(ExcelImportMoveImportBO::getTargetLocationCode));
        for (String s : listMap.keySet()) {
            LocationDTO targetLocationDTO = locationDTOMap.get(s);
            if (ObjectUtil.isEmpty(targetLocationDTO)) continue;

            StockLocationParam stockLocationParam = new StockLocationParam();
            stockLocationParam.setLocationCode(targetLocationDTO.getCode());
            stockLocationParam.setHasPhysicalQty(true);
            List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getList(stockLocationParam);
            // 目标库位的批次也要加进来
            if (CollectionUtil.isNotEmpty(stockLocationDTOList)) {
                SkuLotParam skuLotParam = new SkuLotParam();
                skuLotParam.setCodeList(stockLocationDTOList.stream().map(StockLocationDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
                skuLotDTOList.addAll(remoteSkuLotClient.getList(skuLotParam));
                Map<String, SkuLotDTO> map = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity(), (a, b) -> a));
                skuLotDTOList = new ArrayList<>(map.values());
            }

            MixRuleParam mixRuleParam = new MixRuleParam();
            mixRuleParam.setCode(targetLocationDTO.getMixRuleCode());
            MixRuleDTO mixRuleDTO = remoteMixRuleClient.get(mixRuleParam);

            List<ExcelImportMoveImportBO> excelImportMoveImportBOS = listMap.get(s);
            List<StockLocationDTO> sourceStockLocationList = excelImportMoveImportBOS.stream().map(it -> {
                StockLocationDTO stockLocationDTO = new StockLocationDTO();
                stockLocationDTO.setCargoCode(it.getCargoCode());
                stockLocationDTO.setSkuCode(it.getSkuCode());
                stockLocationDTO.setSkuLotNo(it.getSkuLotNo());
                return stockLocationDTO;
            }).collect(Collectors.toList());
            try {
                mixRuleCheckBiz.checkLocationMixRule(sourceStockLocationList, stockLocationDTOList, mixRuleDTO, skuLotDTOList);
            } catch (Exception exception) {
                callBackPublicService(false, uid, 0, null, "批次混放校验失败，目标库位:" + targetLocationDTO.getCode());
                result = false;
            }
        }

        return result;
    }

    private void checkRowNotEmpty(ExcelImportMoveImportBO importExcel) {
        if (ObjectUtil.isEmpty(importExcel)) {
            errorInfoList.add(getExcelErrorInfo("参数不能为空", "参数不能为空"));
        }
        if (StrUtil.isBlank(importExcel.getCargoCode())) {
            errorInfoList.add(getExcelErrorInfo("*货主编码", "货主编码不能为空"));
        }
        if (StrUtil.isBlank(importExcel.getSkuCode())) {
            errorInfoList.add(getExcelErrorInfo("*商品代码", "商品代码不能为空"));
        }
        if (StrUtil.isBlank(importExcel.getSkuLotNo())) {
            errorInfoList.add(getExcelErrorInfo("*批次ID", "批次ID不能为空"));
        }
        if (StrUtil.isBlank(importExcel.getOriginLocationCode())) {
            errorInfoList.add(getExcelErrorInfo("*来源库位", "来源库位不能为空"));
        }
        if (StrUtil.isBlank(importExcel.getTargetLocationCode())) {
            errorInfoList.add(getExcelErrorInfo("*目标库位", "目标库位不能为空"));
        }
        if (StrUtil.isBlank(importExcel.getMoveQtyStr())) {
            errorInfoList.add(getExcelErrorInfo("*移位数量", "移位数量不能为空"));
        } else {
            if (!NumberUtil.isInteger(importExcel.getMoveQtyStr())) {
                errorInfoList.add(getExcelErrorInfo("*移位数量", "移位数量只能填正整数"));
            } else {
                importExcel.setMoveQty(Integer.parseInt(importExcel.getMoveQtyStr()));
                if (importExcel.getMoveQty() <= 0) {
                    errorInfoList.add(getExcelErrorInfo("*移位数量", "移位数量只能填正整数"));
                }
            }
        }
    }


    @Override
    public void invoke(ExcelImportMoveImportBO asnImportBO, AnalysisContext analysisContext) {
        //跳过 0 、 1 数据
        if (analysisContext.readRowHolder().getRowIndex() <= 1) {
            return;
        }
        // 校验数据
        checkExcel(asnImportBO, analysisContext);
        // 汇总错误
        if (CollectionUtils.isEmpty(errorInfoList)) {
            // 设置行号
            asnImportBO.setLineNo(analysisContext.readRowHolder().getRowIndex());
            dataList.add(asnImportBO);
        } else {
            normalCheckFail = true;
            callBackPublicService(false, uid, analysisContext.readRowHolder().getRowIndex(), asnImportBO, String.join("\n", errorInfoList));
            errorInfoList.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 基础检验失败不继续处理
        if (normalCheckFail) return;

        try {
            //校验
            Boolean checkAndBuildData = checkData(dataList);
            //组装数据
            if (!CollectionUtils.isEmpty(dataList) && checkAndBuildData) {
                save(dataList);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            callBackPublicService(false, uid, 1, null, "系统异常");
        }
    }

    private void save(List<ExcelImportMoveImportBO> dataList) {
        List<MoveDTO> allMoveDTOList = new ArrayList<>();
        List<MoveDetailDTO> allMoveDetailDTOList = new ArrayList<>();
        Map<String, List<ExcelImportMoveImportBO>> groupByCargo = dataList.stream().collect(Collectors.groupingBy(ExcelImportMoveImportBO::getCargoCode));
        for (String cargoCode : groupByCargo.keySet()) {
            String batchSerialNo = IdUtil.simpleUUID();
            List<ExcelImportMoveImportBO> excelImportMoveImportBOS = groupByCargo.get(cargoCode);
            List<MoveDetailDTO> moveDetailDTOList = excelImportMoveImportBOS.stream().map(it -> {
                MoveDetailDTO moveDetailDTO = new MoveDetailDTO();
                moveDetailDTO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
                moveDetailDTO.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());
                moveDetailDTO.setCargoCode(it.getCargoCode());
                moveDetailDTO.setSkuCode(it.getSkuCode());
                moveDetailDTO.setSkuLotNo(it.getSkuLotNo());
                moveDetailDTO.setOriginLocationCode(it.getOriginLocationCode());
                moveDetailDTO.setTargetLocationCode(it.getTargetLocationCode());
                moveDetailDTO.setExpSkuQty(new BigDecimal(it.getMoveQty()));
                moveDetailDTO.setSkuQuality(it.getSkuQuality());
                moveDetailDTO.setOriginZoneType(it.getOriginZoneType());
                moveDetailDTO.setOriginZoneCode(it.getOriginZoneCode());
                moveDetailDTO.setTargetZoneType(it.getTargetZoneType());
                moveDetailDTO.setTargetZoneCode(it.getTargetZoneCode());
                moveDetailDTO.setBatchSerialNo(batchSerialNo);
                return moveDetailDTO;
            }).collect(Collectors.toList());
            MoveDTO moveDTO = new MoveDTO();
            moveDTO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
            moveDTO.setCargoCode(cargoCode);
            moveDTO.setOpType(OpTypeEnum.OP_TYPE_PAPER.getType());
            moveDTO.setDetailList(moveDetailDTOList);
            moveDTO.setCode(remoteSeqRuleClient.findSequence(SeqEnum.MOVE_CODE_000001));
            moveDTO.setExpSkuType((int) excelImportMoveImportBOS.stream().flatMap(a -> Stream.of(a.getSkuCode())).count());
            moveDTO.setExpSkuQty(new BigDecimal("0.000"));
            moveDTO.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());
            for (MoveDetailDTO moveDetailDTO : moveDetailDTOList) {
                moveDetailDTO.setMoveCode(moveDTO.getCode());
                moveDTO.setExpSkuQty(moveDTO.getExpSkuQty().add(moveDetailDTO.getExpSkuQty()));
            }

            allMoveDTOList.add(moveDTO);
            allMoveDetailDTOList.addAll(moveDetailDTOList);
        }

        MoveBO moveBO = new MoveBO();
        moveBO.setMoveDetailDTOList(allMoveDetailDTOList);
        moveBO.setMoveDTOList(allMoveDTOList);
        remoteMoveClient.save(moveBO);

        callBackPublicService(true, uid, 0, null, "导入成功");
    }


    private List<SkuDTO> skuDTOList(List<String> skuCodeList) {
        if (CollectionUtil.isEmpty(skuCodeList)) return ListUtil.empty();
        List<List<String>> split = ListUtil.split(skuCodeList, 1000);
        List<SkuDTO> result = new ArrayList<>();
        for (List<String> list : split) {
            SkuParam skuParam = new SkuParam();
            skuParam.setCodeList(new ArrayList<>(list));
            List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
            result.addAll(skuDTOList);
        }
        return result;
    }

    private List<SkuLotDTO> skuLotDTOList(List<String> skuLotNoList) {
        if (CollectionUtil.isEmpty(skuLotNoList)) return ListUtil.empty();
        List<List<String>> split = ListUtil.split(skuLotNoList, 1000);
        List<SkuLotDTO> result = new ArrayList<>();
        for (List<String> list : split) {
            SkuLotParam skuLotParam = new SkuLotParam();
            skuLotParam.setCodeList(new ArrayList<>(list));
            List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
            result.addAll(skuLotDTOList);
        }
        return result;
    }

    private List<LocationDTO> locationDTOList(List<String> locationCodeList) {
        if (CollectionUtil.isEmpty(locationCodeList)) return ListUtil.empty();
        List<List<String>> split = ListUtil.split(locationCodeList, 1000);
        List<LocationDTO> result = new ArrayList<>();
        for (List<String> list : split) {
            LocationParam locationParam = new LocationParam();
            locationParam.setCodeList(new ArrayList<>(list));
            List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
            result.addAll(locationDTOList);
        }
        return result;
    }
}
