package com.dt.platform.wms.client.hf;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SystemEventEnum;
import com.dt.component.common.enums.base.ContainerStatusEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pre.BillLogTypeEnum;
import com.dt.component.common.enums.pre.SkuIsPreEnum;
import com.dt.component.common.enums.sku.SkuStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageInfo;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.contLog.ContainerLogDTO;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.base.param.SalePlatformParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.bill.dto.AllocationOrderDTO;
import com.dt.domain.bill.dto.PickDTO;
import com.dt.domain.bill.dto.PickDetailDTO;
import com.dt.domain.bill.dto.performance.SystemEventDTO;
import com.dt.domain.bill.dto.pre.PrePackageSkuDetailDTO;
import com.dt.domain.bill.param.AllocationOrderParam;
import com.dt.domain.bill.param.PickParam;
import com.dt.domain.bill.param.pre.PrePackageSkuDetailParam;
import com.dt.domain.core.stock.dto.StockTransactionDTO;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.biz.IBusinessLogBiz;
import com.dt.platform.wms.biz.IPickGuideBiz;
import com.dt.platform.wms.client.IPickOrderClient;
import com.dt.platform.wms.dto.hfPick.*;
import com.dt.platform.wms.dto.pick.PickGuideBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.contLog.IRemoteContainerLogClient;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.pre.IRemotePrePackageSkuDetailClient;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.hfPick.*;
import com.dt.platform.wms.param.pick.PickBillTaskParam;
import com.dt.platform.wms.transaction.IPickRestoreGtsService;
import com.dt.platform.wms.transaction.bo.PickRestorePartBO;
import com.dt.platform.wms.transaction.bo.PickSplitPartBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class HFBizClientImpl implements IHFBizClient {


    @Resource
    IRemotePickClient remotePickClient;

    @Resource
    IRemoteBillLogClient remoteBillLogClient;

    @Resource
    IRemoteCarrierClient remoteCarrierClient;

    @Resource
    IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    IRemoteSalePlatform remoteSalePlatform;

    @Resource
    IRemoteZoneClient remoteZoneClient;

    @Resource
    IRemoteContainerClient remoteContainerClient;

    @Resource
    RedissonClient redissonClient;

    @Resource
    IRemoteContainerLogClient remoteContainerLogClient;

    @Resource
    IBusinessLogBiz businessLogBiz;

    @Resource
    IPickGuideBiz pickGuideBiz;

    @Resource
    IRemoteSkuClient remoteSkuClient;

    @Resource
    IRemoteAllocationOrderClient remoteAllocationOrderClient;

    @DubboReference
    IPickOrderClient pickOrderClient;

    @Resource
    private IRemoteSpecialLocationClient remoteSpecialLocationClient;

    @Resource
    IRemoteSkuLotClient iRemoteSkuLotClient;

    @Resource
    IRemoteLocationClient iRemoteLocationClient;

    @Resource
    IRemotePrePackageSkuDetailClient remotePrePackageSkuDetailClient;


    @Resource
    private IPickRestoreGtsService pickRestoreGtsService;

    @Override
    public Result<List<HFCurrentPickTaskBizDTO>> getHFTaskForUser() {
        PickParam pickParam = new PickParam();
        pickParam.setPickBy(CurrentUserHolder.getUserName());
        pickParam.setStatusList(Collections.singletonList(PickEnum.PickStatusEnum.PICK_BEGIN_STATUS.getCode()));
        pickParam.setWorkType(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode());
        //只有原始拣选单可以RF拣货
        pickParam.setPickFlag(PickEnum.PickFlagEnum.ORIGIN.getCode());
        List<PickDTO> pickDTOList = remotePickClient.getList(pickParam);
        if (CollectionUtils.isEmpty(pickDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到领取的任务");
        }
        List<HFCurrentPickTaskBizDTO> hfCurrentPickTaskBizDTOList = new ArrayList<>();

        pickDTOList.forEach(pickDTO -> {
            HFCurrentPickTaskBizDTO hfCurrentPickTaskBizDTO = ConverterUtil.convert(pickDTO, HFCurrentPickTaskBizDTO.class);
            CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(pickDTO.getCargoCode());
            hfCurrentPickTaskBizDTO.setCargoName(cargoOwnerDTO != null ? cargoOwnerDTO.getName() : "");
            CarrierDTO carrierDTO = remoteCarrierClient.queryByCode(pickDTO.getCarrierCode());
            hfCurrentPickTaskBizDTO.setCarrierName(carrierDTO != null ? carrierDTO.getName() : "");
            hfCurrentPickTaskBizDTO.setSalePlatformName("");
            hfCurrentPickTaskBizDTO.setPickMethod(pickDTO.getPickMethod());
            SalePlatformParam salePlatformParam = new SalePlatformParam();
            salePlatformParam.setCode(pickDTO.getSalePlatform());
            List<SalePlatformDTO> salePlatformDTOList = remoteSalePlatform.getList(salePlatformParam);
            salePlatformDTOList.stream()
                    .filter(a -> a.getCode().equalsIgnoreCase(pickDTO.getSalePlatform()))
                    .findFirst().ifPresent(a -> {
                        hfCurrentPickTaskBizDTO.setSalePlatformName(a.getName());
                    });
            hfCurrentPickTaskBizDTO.setZoneCode(pickDTO.getPickZoneAll());
            hfCurrentPickTaskBizDTO.setContCode(StringUtils.isEmpty(pickDTO.getContCode()) ? "" : pickDTO.getContCode());
            hfCurrentPickTaskBizDTOList.add(hfCurrentPickTaskBizDTO);
        });
        return Result.success(hfCurrentPickTaskBizDTOList);
    }

    @Override
    public Result<HFCurrentPickTaskPageBizDTO> getHFTaskPage(SearchParamBizParam param) {
        PickParam pickParam = ConverterUtil.convert(param, PickParam.class);
        HFCurrentPickTaskPageBizDTO hfCurrentPickTaskPageBizDTO = new HFCurrentPickTaskPageBizDTO();
        //二次分拣查询任务
        if (param != null && !StringUtils.isEmpty(param.getIsSplit()) && param.getIsSplit()) {
            if (!StringUtils.isEmpty(param.getBillNo())) {
                //先查询运单号
                PickParam pickDetailParam = new PickParam();
                pickDetailParam.setExpressNo(param.getBillNo());
                List<PickDetailDTO> pickDetailList = remotePickClient.getPickDetailList(pickDetailParam);
                pickDetailParam = new PickParam();
                pickDetailParam.setContCode(param.getBillNo());
                List<PickDTO> pickDTOList = remotePickClient.getList(pickDetailParam);
                if (CollectionUtils.isEmpty(pickDetailList) && CollectionUtils.isEmpty(pickDTOList)) {
                    PageInfo pageInfo = new PageInfo();
                    pageInfo.setTotalPage(0L);
                    pageInfo.setPageSize(Long.valueOf(param.getSize()));
                    pageInfo.setTotalCount(0L);
                    pageInfo.setCurrentPage(Long.valueOf(param.getCurrent()));
                    hfCurrentPickTaskPageBizDTO.setPage(pageInfo);
                    hfCurrentPickTaskPageBizDTO.setTaskList(new ArrayList<>());
                    return Result.success(hfCurrentPickTaskPageBizDTO);
                }
                List<String> pickCodeList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(pickDetailList)) {
                    pickCodeList.addAll(pickDetailList.stream().map(PickDetailDTO::getPickCode).distinct().collect(Collectors.toList()));
                }
                if (!CollectionUtils.isEmpty(pickDTOList)) {
                    pickCodeList.addAll(pickDTOList.stream().map(PickDTO::getPickCode).distinct().collect(Collectors.toList()));
                }
                pickParam.setPickCodeList(pickCodeList);
            }
            pickParam.setStatusList(Collections.singletonList(PickEnum.PickStatusEnum.PICK_END_STATUS.getCode()));
            pickParam.setWorkType(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode());
            pickParam.setPickMethod(PickEnum.PickMethodEnum.PICK_METHOD_0.getCode());
            pickParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString());
            //只有原始拣选单可以RF拣货
            pickParam.setPickFlag(PickEnum.PickFlagEnum.ORIGIN.getCode());
            pickParam.setTypeList(Arrays.asList(PickEnum.PickOrderTypeEnum.SINGLE.getCode(), PickEnum.PickOrderTypeEnum.MORE.getCode()));
        } else {
            if (!StringUtils.isEmpty(param.getExpressNo())) {
                pickParam.setExpressNoList(Collections.singletonList(param.getExpressNo()));
            }
            pickParam.setWorkType(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode());
            //只有原始拣选单可以RF拣货
            pickParam.setPickFlag(PickEnum.PickFlagEnum.ORIGIN.getCode());
            pickParam.setStatusList(Collections.singletonList(PickEnum.PickStatusEnum.CONFIRM_STATUS.getCode()));
        }
        Page<PickDTO> page = remotePickClient.getPage(pickParam);


        PageInfo pageInfo = new PageInfo();
        if (CollectionUtils.isEmpty(page.getRecords())) {
            pageInfo.setTotalPage(0L);
            pageInfo.setPageSize(Long.valueOf(param.getSize()));
            pageInfo.setTotalCount(0L);
            pageInfo.setCurrentPage(Long.valueOf(param.getCurrent()));
            hfCurrentPickTaskPageBizDTO.setPage(pageInfo);
            hfCurrentPickTaskPageBizDTO.setTaskList(new ArrayList<>());
            return Result.success(hfCurrentPickTaskPageBizDTO);
        }
        List<HFCurrentPickTaskBizDTO> hfCurrentPickTaskBizDTOList = new ArrayList<>();
        page.getRecords().forEach(pickDTO -> {
            HFCurrentPickTaskBizDTO hfCurrentPickTaskBizDTO = ConverterUtil.convert(pickDTO, HFCurrentPickTaskBizDTO.class);

            CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(pickDTO.getCargoCode());
            hfCurrentPickTaskBizDTO.setCargoName(cargoOwnerDTO != null ? cargoOwnerDTO.getName() : "");

            CarrierDTO carrierDTO = remoteCarrierClient.queryByCode(pickDTO.getCarrierCode());
            hfCurrentPickTaskBizDTO.setCarrierName(carrierDTO != null ? carrierDTO.getName() : "");

            hfCurrentPickTaskBizDTO.setSalePlatformName("");
            hfCurrentPickTaskBizDTO.setPickMethod(pickDTO.getPickMethod());
            SalePlatformParam salePlatformParam = new SalePlatformParam();
            salePlatformParam.setCode(pickDTO.getSalePlatform());
            List<SalePlatformDTO> salePlatformDTOList = remoteSalePlatform.getList(salePlatformParam);
            salePlatformDTOList.stream()
                    .filter(a -> a.getCode().equalsIgnoreCase(pickDTO.getSalePlatform()))
                    .findFirst().ifPresent(a -> {
                        hfCurrentPickTaskBizDTO.setSalePlatformName(a.getName());
                    });
            hfCurrentPickTaskBizDTO.setZoneCode(pickDTO.getPickZoneAll());
            hfCurrentPickTaskBizDTO.setContCode(StringUtils.isEmpty(pickDTO.getContCode()) ? "" : pickDTO.getContCode());
            hfCurrentPickTaskBizDTO.setPackageQty(pickDTO.getPackageQty());
            hfCurrentPickTaskBizDTOList.add(hfCurrentPickTaskBizDTO);
        });
        pageInfo.setTotalPage(page.getPages());
        pageInfo.setPageSize(page.getSize());
        pageInfo.setTotalCount(page.getTotal());
        pageInfo.setCurrentPage(page.getCurrent());
        hfCurrentPickTaskPageBizDTO.setPage(pageInfo);
        hfCurrentPickTaskPageBizDTO.setTaskList(hfCurrentPickTaskBizDTOList);
        return Result.success(hfCurrentPickTaskPageBizDTO);
    }

    @Override
    public Result<String> bindContByPickCode(ContBindPickBizParam param) {
        String pickCodeLockKey = String.format("dt_wms-bind-cont-by-pick-code:%s:%s", CurrentRouteHolder.getWarehouseCode(), param.getPickCode());
        RLock lock = redissonClient.getLock(pickCodeLockKey);
        try {
            boolean tryLock = lock.tryLock(1, 30, TimeUnit.SECONDS);

            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后重试");
            }
            PickParam pickParam = new PickParam();
            pickParam.setPickBy(CurrentUserHolder.getUserName());
            pickParam.setPickCode(param.getPickCode());
            PickDTO pickDTO = remotePickClient.get(pickParam);
            if (pickDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到您领取的拣选单号:%s", param.getPickCode()));
            }
            if (!StringUtils.isEmpty(pickDTO.getContCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("当前拣选单号:%s已绑定容器", param.getPickCode()));
            }
            ContainerDTO containerDTO = remoteContainerClient.queryByCode(param.getContCode());
            if (containerDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("容器号%s不存在", param.getContCode()));
            }
            if (Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.DISABLE.value())) {
                throw new BaseException(BaseBizEnum.TIP, "请维护容器的有效性");
            }
            if (!Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.ENABLE.value())) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器已占用");
            }
            //1.扫描占用容器
            remoteContainerClient.modifyContainerOccupy(pickDTO.getPickCode(), containerDTO.getCode(),
                    CommonConstantUtil.CONTAINER_PICK, ContainerStatusEnum.OCCUPY.getValue(),
                    "PDA拣货" + ":" + pickDTO.getPickCode());
            businessLogBiz.saveContLog(
                    pickDTO.getWarehouseCode(),
                    containerDTO.getCode(),
                    "PICK",
                    pickDTO.getPickCode(),
                    CurrentUserHolder.getUserName(),
                    String.format("拣选单号%s绑定容器:%s", pickDTO.getPickCode(), containerDTO.getCode()));
            //绑定容器
            pickDTO.setContCode(param.getContCode());
            remotePickClient.modifyBatch(Collections.singletonList(pickDTO));

            //记录拣选单的日志
            BillLogDTO billLogDTO = new BillLogDTO();
            billLogDTO.setCargoCode(pickDTO.getCargoCode());
            billLogDTO.setBillNo(pickDTO.getPickCode());
            billLogDTO.setBillType(BillLogTypeEnum.PICK.getType());
            billLogDTO.setOpBy(CurrentUserHolder.getUserName());
            billLogDTO.setOpDate(System.currentTimeMillis());
            billLogDTO.setOpContent(String.format("拣选单绑定容器:%s", param.getContCode()));
            billLogDTO.setOpRemark(String.format("拣选单绑定容器:%s", param.getContCode()));
            remoteBillLogClient.save(billLogDTO);

            return Result.success("绑定成功");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("bindContByPickCode:{} {}", e, e.getMessage());
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    @Override
    public Result<String> getPickRecommendLocation(CodeParam param) {

        PickDTO pickDTO = checkPickDTO(param.getCode());

        List<PickGuideBizDTO> pickGuideBizDTOList;
        if (ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString().equals(pickDTO.getBusinessType())) {
            pickGuideBizDTOList = pickGuideBiz.getValidityPeriodPickGuide(pickDTO);
        } else {
            pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);
        }
        if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
        }
        if (pickGuideBizDTOList.stream()
                .noneMatch(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))) {
            throw new BaseException(BaseBizEnum.TIP, "当前拣选单已完成拣货");
        }
        //获取第一条拣货信息
        PickGuideBizDTO pickGuideBizDTO = pickGuideBizDTOList.stream()
                .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                .findFirst().orElse(null);
        return Result.success(pickGuideBizDTO.getZoneCode() + ":" + pickGuideBizDTO.getLocationCode());
    }

    @Override
    public Result<List<PickGuideBizDTO>> getPickWaitSku(WaitPickBizParam param) {
        PickDTO pickDTO = checkPickDTO(param.getPickCode());
        List<PickGuideBizDTO> pickGuideBizDTOList;
        if (ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString().equals(pickDTO.getBusinessType())) {
            pickGuideBizDTOList = pickGuideBiz.getValidityPeriodPickGuide(pickDTO);
        } else {
            pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);
        }
        if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
        }
        //获取所有的拣货任务
        if (param.getGetAll()) {
            return Result.success(pickGuideBizDTOList);
        }
        //有库位获取信息，可拣货信息
        if (!StringUtils.isEmpty(param.getLocationCode())) {
            if (pickGuideBizDTOList.stream().noneMatch(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))) {
                throw new BaseException(BaseBizEnum.TIP, String.format("当前库位%s不在拣货任务中", param.getLocationCode()));
            }
            List<PickGuideBizDTO> guideBizDTOS = pickGuideBizDTOList.stream()
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                    .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(guideBizDTOS)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("当前库位:%s已完成拣货", param.getLocationCode()));
            }
            return Result.success(guideBizDTOS);
        }
        //获取拣选单所有的拣货信息
        List<PickGuideBizDTO> pickGuideBizDTOS = pickGuideBizDTOList.stream()
                .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickGuideBizDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("当前拣选单:%s已完成拣货", param.getLocationCode()));
        }
        return Result.success(pickGuideBizDTOS);
    }

    @Override
    public Result<HFPickSkuBizDTO> getSkuFromLocation(PickSkuAndLocationBizParam param) {
        PickDTO pickDTO = checkPickDTO(param.getPickCode());
        List<PickGuideBizDTO> pickGuideBizDTOList;
        if (ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString().equals(pickDTO.getBusinessType())) {
            pickGuideBizDTOList = pickGuideBiz.getValidityPeriodPickGuide(pickDTO);
        } else {
            pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);
        }
        if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
        }
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setCargoCode(pickDTO.getCargoCode());
        skuUpcParam.setUpcCode(param.getUpcCode());
        skuUpcParam.setStatus(SkuStatusEnum.STATUS_ENABLED.getStatus());
        List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
        if (CollectionUtils.isEmpty(skuUpcList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品条码:%s", param.getUpcCode()));
        }
        List<String> skuCodeList = skuUpcList.stream().map(SkuUpcDTO::getSkuCode).distinct().collect(Collectors.toList());

        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(pickDTO.getCargoCode());
        skuParam.setCode(skuCodeList.get(0));
        SkuDTO skuDTO = remoteSkuClient.get(skuParam);
        if (skuDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品:%s", param.getUpcCode()));
        }
        List<PickGuideBizDTO> guideBizDTOS = pickGuideBizDTOList.stream()
                .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                .filter(a -> skuCodeList.contains(a.getSkuCode()))
                .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                .sorted(Comparator.comparing(PickGuideBizDTO::getExpireDateDesc))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(guideBizDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品条码:%s在库位:%s未找到待拣货任务", param.getUpcCode(), param.getLocationCode()));
        }
        HFPickSkuBizDTO hfPickSkuBizDTO = new HFPickSkuBizDTO();
        hfPickSkuBizDTO.setSkuCode(skuDTO.getCode());
        hfPickSkuBizDTO.setSkuName(skuDTO.getName());
        hfPickSkuBizDTO.setPickCode(pickDTO.getPickCode());
        hfPickSkuBizDTO.setUpcCode(param.getUpcCode());


        //获取待拣货第一条商品信息
        PickGuideBizDTO pickGuideBizDTO = guideBizDTOS.get(0);
        hfPickSkuBizDTO.setProductionNo(pickGuideBizDTO.getProductionNo());
        hfPickSkuBizDTO.setExpireDateDesc(pickGuideBizDTO.getExpireDateDesc());
        // 维度 b2bGroup  skuLotNo B/C单分组 B单分组sku+效期  C单货主,商品,生产日期,失效日期,生产批次号,效期码
        hfPickSkuBizDTO.setSkuLotNo(pickGuideBizDTO.getB2bGroup());
        hfPickSkuBizDTO.setPickQty(pickGuideBizDTO.getQty());
        hfPickSkuBizDTO.setPickMethod(pickDTO.getPickMethod());
        hfPickSkuBizDTO.setPackQty(pickDTO.getPackageQty());
        if (pickDTO.getType().equalsIgnoreCase(PickEnum.PickOrderTypeEnum.SPIKE.getCode()) || pickDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            hfPickSkuBizDTO.setPickMethod(PickEnum.PickMethodEnum.PICK_METHOD_0.getCode());
        }
        String guideToSplit = pickGuideBizDTO.getGuideToSplit().replaceAll("\\[", "").replaceAll("]", "");
        List<HFSkuAndBasketNoBizDTO> skuAndBasketNoList = new ArrayList<>();
        if (!StringUtils.isEmpty(guideToSplit)) {
            List<String> guideList = Arrays.asList(guideToSplit.split(","));
            guideList.forEach(guide -> {
                HFSkuAndBasketNoBizDTO hfSkuAndBasketNoBizDTO = new HFSkuAndBasketNoBizDTO();
                hfSkuAndBasketNoBizDTO.setBasketNo(Integer.valueOf(guide.split("x")[0]));
                hfSkuAndBasketNoBizDTO.setQty(guide.split("x")[1]);
                skuAndBasketNoList.add(hfSkuAndBasketNoBizDTO);
            });
        }
        //CW货主
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(skuDTO.getCargoCode());
        hfPickSkuBizDTO.setCwCargo(false);
        if (cargoOwnerDTO != null && CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.CW_CARGO)) {
            hfPickSkuBizDTO.setCwCargo(true);
        }
        hfPickSkuBizDTO.setSkuAndBasketNoList(skuAndBasketNoList);
        return Result.success(hfPickSkuBizDTO);
    }

    @Override
    public Result<String> commitSku(PickCommitSkuBizParam param) {
        String pickCommitSkuLockKey = String.format("dt_wms_commitSku-%s:%s", CurrentRouteHolder.getWarehouseCode(),
                param.getPickCode() + "-" + param.getLocationCode() + "-" + param.getSkuCode() + "-" + param.getSkuLotNo());
        RLock lock = redissonClient.getLock(pickCommitSkuLockKey);
        try {
            boolean tryLock = lock.tryLock(1, 30, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后重试");
            }
            //10 当前商品-批次已完成
            //20 当前库位已完成
            //30 当前拣选单已完成
            String flag = "10";
            PickDTO pickDTO = checkPickDTO(param.getPickCode());
            List<PickGuideBizDTO> pickGuideBizDTOList;
            if (ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString().equals(pickDTO.getBusinessType())) {
                pickGuideBizDTOList = pickGuideBiz.getValidityPeriodPickGuide(pickDTO);
            } else {
                pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);
            }
            if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
            }

            SkuParam skuParam = new SkuParam();
            skuParam.setCargoCode(pickDTO.getCargoCode());
            skuParam.setCode(param.getSkuCode());
            SkuDTO skuDTO = remoteSkuClient.get(skuParam);
            if (skuDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品:%s", param.getSkuCode()));
            }
            //判定商品是否是预包商品
            Boolean isPre = false;
            Map<String, BigDecimal> skuNeedQty = new HashMap<>();
            if (skuDTO.getIsPre().equalsIgnoreCase(SkuIsPreEnum.PRE.getCode())) {
                skuNeedQty = getSkuNeedQty(pickDTO.getCargoCode(), param.getSkuCode());
                isPre = true;
            }

            // 维度 b2bGroup  skuLotNo B/C单分组 B单分组sku+效期  C单货主,商品,生产日期,失效日期,生产批次号,效期码
            List<PickGuideBizDTO> guideBizDTOWaitPickList = pickGuideBizDTOList.stream()
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                    .filter(a -> param.getSkuCode().equalsIgnoreCase(a.getSkuCode()))
                    .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                    .filter(a -> a.getB2bGroup().equalsIgnoreCase(param.getSkuLotNo()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(guideBizDTOWaitPickList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s在库位:%s未找到待拣货任务", param.getUpcCode(), param.getLocationCode()));
            }

            AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
            allocationOrderParam.setPickCode(pickDTO.getPickCode());
            allocationOrderParam.setWaveCode(pickDTO.getWaveCode());
            List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getList(allocationOrderParam);
            if (CollectionUtils.isEmpty(allocationOrderDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品:%s的库存记录", param.getSkuCode()));
            }
            List<String> skuLotNoList = guideBizDTOWaitPickList.get(0).getSkuLotNoList();
            //当前库位的数据
            List<AllocationOrderDTO> currentAllocationOrderDTOList = allocationOrderDTOList.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(param.getSkuCode()))
                    .filter(a -> skuLotNoList.contains(a.getSkuLotNo()))
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                    .collect(Collectors.toList());
            //当前库位库存已完成变更
            if (allocationOrderDTOList.stream().allMatch(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()) && a.getExpQty().compareTo(a.getPickQty()) == 0)) {
                flag = "20";
                return Result.success(flag);
            }
            //预包商品对应的子商品明细也需要库存交回
            List<AllocationOrderDTO> commitStockList = new ArrayList<>();
            commitStockList.addAll(currentAllocationOrderDTOList);
            if (isPre) {
                List<String> packUidList = currentAllocationOrderDTOList.stream().map(AllocationOrderDTO::getPackUid).distinct().collect(Collectors.toList());
                Map<String, BigDecimal> finalSkuNeedQty = skuNeedQty;
                List<AllocationOrderDTO> allocationOrderDTOS = allocationOrderDTOList.stream()
                        .filter(a -> a.getSkuCode().equalsIgnoreCase(param.getSkuCode()))
                        .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                        .filter(a -> finalSkuNeedQty.containsKey(a.getSkuCode()))
                        .filter(a -> packUidList.contains(a.getPackUid()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(allocationOrderDTOS)) {
                    commitStockList.addAll(allocationOrderDTOS);
                }
            }
            //提交库存数据
            currentAllocationOrderDTOList.forEach(a -> a.setPickQty(a.getExpQty()));

            List<SystemEventDTO> systemEventDTOList = buildSystemEventList(pickDTO, currentAllocationOrderDTOList, SystemEventEnum.PICK_END.getCode());
            //提交数据
            PickRestorePartBO pickRestorePartBO = new PickRestorePartBO();
            pickRestorePartBO.setPickCode(pickDTO.getPickCode());
            pickRestorePartBO.setWarehouseCode(pickDTO.getWarehouseCode());
            pickRestorePartBO.setAllocationOrderDTOList(currentAllocationOrderDTOList);
            pickRestorePartBO.setSystemEventDTOList(systemEventDTOList);
            pickRestoreGtsService.confirmPickRestorePart(pickRestorePartBO);
            if (allocationOrderDTOList.stream().allMatch(a -> a.getExpQty().compareTo(a.getPickQty()) == 0)) {
                //全部完成
                CodeParam codeParam = new CodeParam();
                codeParam.setCode(pickDTO.getPickCode());
                pickOrderClient.confirmPickRestore(codeParam, true);
                //当前拣选单已全部完成
                flag = "30";
                return Result.success(flag);
            }
            //当前库位库存已完成变更
            if (allocationOrderDTOList.stream()
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                    .allMatch(a -> a.getExpQty().compareTo(a.getPickQty()) == 0)) {
                flag = "20";
                return Result.success(flag);
            }
            return Result.success(flag);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("commitSku:{} {}", e, e.getMessage());
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    @Override
    public Result<String> commitCWSku(PickCommitSkuBizParam param) {
        //基本参数处理
        formatCWParam(param);

        String pickCommitSkuLockKey = String.format("dt_wms_commitCWSku-%s:%s", CurrentRouteHolder.getWarehouseCode(),
                param.getPickCode() + "-" + param.getLocationCode() + "-" + param.getSkuCode() + "-");
        RLock lock = redissonClient.getLock(pickCommitSkuLockKey);
        try {
            boolean tryLock = lock.tryLock(1, 30, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后重试");
            }
            //10 当前商品-批次已完成
            //20 当前库位已完成
            //30 当前拣选单已完成
            String flag = "10";
            PickDTO pickDTO = checkPickDTO(param.getPickCode());
            //CW指考虑B2B单据
            List<PickGuideBizDTO> pickGuideBizDTOList = pickGuideBiz.getValidityPeriodCWPickGuide(pickDTO);
            if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
            }
            SkuParam skuParam = new SkuParam();
            skuParam.setCargoCode(pickDTO.getCargoCode());
            skuParam.setCode(param.getSkuCode());
            SkuDTO skuDTO = remoteSkuClient.get(skuParam);
            if (skuDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品:%s", param.getSkuCode()));
            }
            // 维度 b2bGroup  skuLotNo B/C单分组 B单分组sku+效期  C单货主,商品,生产日期,失效日期,生产批次号,效期码
            List<PickGuideBizDTO> guideBizDTOWaitPickList = pickGuideBizDTOList.stream()
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                    .filter(a -> param.getSkuCode().equalsIgnoreCase(a.getSkuCode()))
                    .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                    .filter(a -> a.getExpireDate().equals(param.getExpireDate()))
                    .filter(a -> a.getManufDate().equals(param.getManufDate()))
                    .filter(a -> a.getBoxCode().equals(param.getBoxCode()))
                    .filter(a -> a.getPalletCode().equals(param.getPalletCode()))
                    .filter(a -> a.getProductionNo().equals(param.getProductionNo()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(guideBizDTOWaitPickList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s在库位:%s未找到待拣货任务", param.getUpcCode(), param.getLocationCode()));
            }
            //当前数量求和
            BigDecimal commitQty = guideBizDTOWaitPickList.stream().map(a -> new BigDecimal(a.getQty())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (commitQty.compareTo(param.getCommitQty()) != 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s在库位:%s数量不一致,请核查", param.getUpcCode(), param.getLocationCode()));
            }
            AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
            allocationOrderParam.setPickCode(pickDTO.getPickCode());
            allocationOrderParam.setWaveCode(pickDTO.getWaveCode());
            List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getList(allocationOrderParam);
            if (CollectionUtils.isEmpty(allocationOrderDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品:%s的库存记录", param.getSkuCode()));
            }
            //获取当前所有数据
            List<String> skuLotNoList = guideBizDTOWaitPickList.stream().map(PickGuideBizDTO::getSkuLotNo).distinct().collect(Collectors.toList());
            //当前库位的数据
            List<AllocationOrderDTO> currentAllocationOrderDTOList = allocationOrderDTOList.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(param.getSkuCode()))
                    .filter(a -> skuLotNoList.contains(a.getSkuLotNo()))
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                    .collect(Collectors.toList());
            //当前库位库存已完成变更
            if (allocationOrderDTOList.stream().allMatch(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()) && a.getExpQty().compareTo(a.getPickQty()) == 0)) {
                flag = "20";
                return Result.success(flag);
            }
            //预包商品对应的子商品明细也需要库存交回
            List<AllocationOrderDTO> commitStockList = new ArrayList<>();

            commitStockList.addAll(currentAllocationOrderDTOList);
            //提交库存数据
            currentAllocationOrderDTOList.forEach(a -> a.setPickQty(a.getExpQty()));

            List<SystemEventDTO> systemEventDTOList = buildSystemEventList(pickDTO, currentAllocationOrderDTOList, SystemEventEnum.PICK_END.getCode());
            //提交数据
            PickRestorePartBO pickRestorePartBO = new PickRestorePartBO();
            pickRestorePartBO.setPickCode(pickDTO.getPickCode());
            pickRestorePartBO.setWarehouseCode(pickDTO.getWarehouseCode());
            pickRestorePartBO.setAllocationOrderDTOList(currentAllocationOrderDTOList);
            pickRestorePartBO.setSystemEventDTOList(systemEventDTOList);
            pickRestoreGtsService.confirmPickRestorePart(pickRestorePartBO);
            if (allocationOrderDTOList.stream().allMatch(a -> a.getExpQty().compareTo(a.getPickQty()) == 0)) {
                //全部完成
                CodeParam codeParam = new CodeParam();
                codeParam.setCode(pickDTO.getPickCode());
                pickOrderClient.confirmPickRestore(codeParam, true);
                //当前拣选单已全部完成
                flag = "30";
                return Result.success(flag);
            }
            //当前库位库存已完成变更
            if (allocationOrderDTOList.stream()
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                    .allMatch(a -> a.getExpQty().compareTo(a.getPickQty()) == 0)) {
                flag = "20";
                return Result.success(flag);
            }
            return Result.success(flag);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("commitCWSku:{} {}", e, e.getMessage());
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    @Override
    public Result<BigDecimal> queryCWSkuQty(PickCommitSkuBizParam param) {
        //基本参数处理
        if (StringUtils.isEmpty(param.getBoxCode())) {
            param.setBoxCode("");
        }
        if (StringUtils.isEmpty(param.getPalletCode())) {
            param.setPalletCode("");
        }
        if (StringUtils.isEmpty(param.getProductionNo())) {
            param.setProductionNo("");
        }
        if (StringUtils.isEmpty(param.getManufDate())) {
            param.setManufDate(0L);
        }
        if (StringUtils.isEmpty(param.getExpireDate())) {
            param.setExpireDate(0L);
        }

        PickDTO pickDTO = checkPickDTO(param.getPickCode());
        //CW指考虑B2B单据
        List<PickGuideBizDTO> pickGuideBizDTOList = pickGuideBiz.getValidityPeriodCWPickGuide(pickDTO);
        if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
        }
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(pickDTO.getCargoCode());
        skuParam.setCode(param.getSkuCode());
        SkuDTO skuDTO = remoteSkuClient.get(skuParam);
        if (skuDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品:%s", param.getSkuCode()));
        }
        // 维度 b2bGroup  skuLotNo B/C单分组 B单分组sku+效期  C单货主,商品,生产日期,失效日期,生产批次号,效期码
        List<PickGuideBizDTO> guideBizDTOWaitPickList = pickGuideBizDTOList.stream()
                .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                .filter(a -> param.getSkuCode().equalsIgnoreCase(a.getSkuCode()))
                .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                .filter(a -> a.getBoxCode().equals(param.getBoxCode()))
                .filter(a -> a.getPalletCode().equals(param.getPalletCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(guideBizDTOWaitPickList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s在库位:%s未找到待拣货任务", param.getUpcCode(), param.getLocationCode()));
        }
        //当前数量求和
        BigDecimal expQty = guideBizDTOWaitPickList.stream().map(a -> new BigDecimal(a.getQty())).reduce(BigDecimal.ZERO, BigDecimal::add);

        return Result.success(expQty.setScale(0, BigDecimal.ROUND_HALF_UP));
    }

    /**
     * @param param
     */
    private void formatCWParam(PickCommitSkuBizParam param) {
        //基本参数处理
        if (StringUtils.isEmpty(param.getBoxCode())) {
            param.setBoxCode("");
        }
        if (StringUtils.isEmpty(param.getPalletCode())) {
            param.setPalletCode("");
        }
        if (StringUtils.isEmpty(param.getProductionNo())) {
            param.setProductionNo("");
        }
        if (StringUtils.isEmpty(param.getManufDate())) {
            param.setManufDate(0L);
        }
        if (StringUtils.isEmpty(param.getExpireDate())) {
            param.setExpireDate(0L);
        }
        if (param.getCommitQty() == null || param.getCommitQty().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BaseException(BaseBizEnum.TIP, "数量数据异常");
        }
    }

    /**
     * @param pickDTO
     * @param currentAllocationOrderDTOList
     * @return java.util.List<com.dt.domain.bill.dto.performance.SystemEventDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/7/21 10:17
     */
    private List<SystemEventDTO> buildSystemEventList(PickDTO pickDTO, List<AllocationOrderDTO> currentAllocationOrderDTOList, String type) {
        List<SystemEventDTO> systemEventDTOList = new ArrayList<>();
        for (AllocationOrderDTO entity : currentAllocationOrderDTOList) {
            SystemEventDTO systemEventDTO = new SystemEventDTO();
            systemEventDTO.setType(type);
            systemEventDTO.setWarehouseCode(pickDTO.getWarehouseCode());
            systemEventDTO.setCargoCode(pickDTO.getCargoCode());
            systemEventDTO.setWorker(pickDTO.getPickBy());
            systemEventDTO.setWorkTime(System.currentTimeMillis());
            systemEventDTO.setWorkDate(DateUtil.parseDate(DateUtil.date(System.currentTimeMillis()).toDateStr()).getTime());
            systemEventDTO.setBillNo(pickDTO.getPickCode());
            systemEventDTO.setDetailBillNo(entity.getId() + "");
            systemEventDTOList.add(systemEventDTO);
        }
        return systemEventDTOList;
    }

    /**
     * @param cargoCode
     * @param preUpcCode
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     * @author: WuXian
     * description: 获取预包条码的子商品需要的结构数量
     * create time: 2021/8/27 15:42
     */
    private Map<String, BigDecimal> getSkuNeedQty(String cargoCode, String preUpcCode) {
        PrePackageSkuDetailParam prePackageSkuDetailParam = new PrePackageSkuDetailParam();
        prePackageSkuDetailParam.setCargoCode(cargoCode);
        prePackageSkuDetailParam.setPreUpcCode(preUpcCode);
        List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOList = remotePrePackageSkuDetailClient.getList(prePackageSkuDetailParam);
        if (CollectionUtils.isEmpty(prePackageSkuDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到预包条码的子商品数据");
        }
        //使用有序Map
        Map<String, BigDecimal> skuNeedQtyMap = new TreeMap<>();
        prePackageSkuDetailDTOList.stream().sorted(Comparator.comparing(PrePackageSkuDetailDTO::getSkuQty, Comparator.naturalOrder())
                .thenComparing(PrePackageSkuDetailDTO::getSkuCode, Comparator.naturalOrder())).forEach(entity -> {
            skuNeedQtyMap.putIfAbsent(entity.getSkuCode(), entity.getSkuQty());
        });
        if (CollectionUtils.isEmpty(skuNeedQtyMap)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到预包条码的子商品数据");
        }
        return skuNeedQtyMap;
    }

    @Override
    public Result<Boolean> completePicK(CodeParam param) {
        String completePickCode = String.format("dt_wms-completePicK-%s:%s", CurrentRouteHolder.getWarehouseCode(), param.getCode());
        RLock lock = redissonClient.getLock(completePickCode);
        try {
            boolean tryLock = lock.tryLock(1, 30, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后重试");
            }
            PickParam pickParam = new PickParam();
            pickParam.setPickCode(param.getCode());
            PickDTO pickDTO = remotePickClient.get(pickParam);
            if (pickDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到拣选单号:%s", param.getCode()));
            }
            if (!pickDTO.getPickBy().equalsIgnoreCase(CurrentUserHolder.getUserName())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到您领取的拣选单号:%s", param.getCode()));
            }
            if (PickEnum.PickStatusEnum.PICK_END_STATUS.getCode().contains(pickDTO.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("当前拣选单号:%s已完成拣货", param.getCode()));
            }
            if (!Arrays.asList(PickEnum.PickStatusEnum.PICK_BEGIN_STATUS.getCode()).contains(pickDTO.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到拣货状态拣选单号:%s", param.getCode()));
            }

            AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
            allocationOrderParam.setPickCode(pickDTO.getPickCode());
            allocationOrderParam.setWaveCode(pickDTO.getWaveCode());
            List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getList(allocationOrderParam);
            if (CollectionUtils.isEmpty(allocationOrderDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单%s未找到数据", pickDTO.getPickCode()));
            }
            if (allocationOrderDTOList.stream().anyMatch(a -> a.getExpQty().compareTo(a.getPickQty()) > 0)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单%s未完成PDA拣货,不能完成", pickDTO.getPickCode()));
            }
            pickOrderClient.confirmPickRestore(param, true);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("completePicK:{} {}", e, e.getMessage());
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> receivePick(CodeParam param) {
        PickBillTaskParam pickBillTaskParam = new PickBillTaskParam();
        pickBillTaskParam.setPickBy(CurrentUserHolder.getUserName());
        pickBillTaskParam.setPickCode(param.getCode());
        Result<Boolean> billReceive = pickOrderClient.confirmPickBillReceive(pickBillTaskParam);
        return Result.success(billReceive.getData());
    }

    @Override
    public Result<HFCurrentPickTaskBizDTO> scanLocation(ScanLocationBizParam param) {
        PickDTO pickDTO = checkPickDTO(param.getPickCode());
        List<PickGuideBizDTO> pickGuideBizDTOList;
        if (ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString().equals(pickDTO.getBusinessType())) {
            pickGuideBizDTOList = pickGuideBiz.getValidityPeriodPickGuide(pickDTO);
        } else {
            pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);
        }
        if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
        }
        //有库位获取信息，可拣货信息
        if (!StringUtils.isEmpty(param.getLocationCode())) {
            if (pickGuideBizDTOList.stream().noneMatch(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))) {
                throw new BaseException(BaseBizEnum.TIP, String.format("当前库位%s不在拣货任务中", param.getLocationCode()));
            }
            List<PickGuideBizDTO> guideBizDTOS = pickGuideBizDTOList.stream()
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                    .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(guideBizDTOS)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("当前库位:%s已完成拣货", param.getLocationCode()));
            }

        }
        HFCurrentPickTaskBizDTO hfCurrentPickTaskBizDTO = ConverterUtil.convert(pickDTO, HFCurrentPickTaskBizDTO.class);

        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(pickDTO.getCargoCode());
        hfCurrentPickTaskBizDTO.setCargoName(cargoOwnerDTO != null ? cargoOwnerDTO.getName() : "");

        CarrierDTO carrierDTO = remoteCarrierClient.queryByCode(pickDTO.getCarrierCode());
        hfCurrentPickTaskBizDTO.setCarrierName(carrierDTO != null ? carrierDTO.getName() : "");

        hfCurrentPickTaskBizDTO.setSalePlatformName("");
        hfCurrentPickTaskBizDTO.setPickMethod(pickDTO.getPickMethod());
        SalePlatformParam salePlatformParam = new SalePlatformParam();
        salePlatformParam.setCode(pickDTO.getSalePlatform());
        List<SalePlatformDTO> salePlatformDTOList = remoteSalePlatform.getList(salePlatformParam);
        salePlatformDTOList.stream()
                .filter(a -> a.getCode().equalsIgnoreCase(pickDTO.getSalePlatform()))
                .findFirst().ifPresent(a -> {
                    hfCurrentPickTaskBizDTO.setSalePlatformName(a.getName());
                });
        hfCurrentPickTaskBizDTO.setZoneCode(pickDTO.getPickZoneAll());
        hfCurrentPickTaskBizDTO.setLocationCode(param.getLocationCode());
        return Result.success(hfCurrentPickTaskBizDTO);
    }

    @Override
    public Result<Boolean> getHFExistsTaskForUser() {
        PickParam pickParam = new PickParam();
        pickParam.setPickBy(CurrentUserHolder.getUserName());
        pickParam.setStatusList(Arrays.asList(PickEnum.PickStatusEnum.PICK_BEGIN_STATUS.getCode()));
        pickParam.setWorkType(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode());
        //只有原始拣选单可以RF拣货
        pickParam.setPickFlag(PickEnum.PickFlagEnum.ORIGIN.getCode());
        List<PickDTO> pickDTOList = remotePickClient.getList(pickParam);
        if (CollectionUtils.isEmpty(pickDTOList)) {
            return Result.success(false);
        }
        return Result.success(true);
    }

    @Override
    public Result<List<HFCurrentWaitPickBizDTO>> waitPickLocationDetail(CodeParam param) {
        PickDTO pickDTO = checkPickDTO(param.getCode());
        List<PickGuideBizDTO> pickGuideBizDTOList;
        if (ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString().equals(pickDTO.getBusinessType())) {
            pickGuideBizDTOList = pickGuideBiz.getValidityPeriodPickGuide(pickDTO);
        } else {
            pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);
        }
        if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
        }
        //获取拣选单所有的拣货信息
        List<PickGuideBizDTO> pickGuideBizDTOS = pickGuideBizDTOList.stream()
                .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickGuideBizDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("当前拣选单:%s已完成拣货", param.getCode()));
        }
        List<String> locationCodeList = pickGuideBizDTOS.stream().map(PickGuideBizDTO::getLocationCode).distinct().collect(Collectors.toList());
        List<HFCurrentWaitPickBizDTO> hfCurrentWaitPickBizDTOList = new ArrayList<>();
        locationCodeList.forEach(locationCode -> {
            HFCurrentWaitPickBizDTO hfCurrentWaitPickBizDTO = new HFCurrentWaitPickBizDTO();
            hfCurrentWaitPickBizDTO.setLocationCode(locationCode);
            int sum = pickGuideBizDTOS.stream()
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(locationCode))
                    .map(a -> {
                        return Integer.valueOf(a.getQty());
                    })
                    .mapToInt(Integer::intValue).sum();
            hfCurrentWaitPickBizDTO.setQty(sum);
            long count = pickGuideBizDTOS.stream()
                    .filter(a -> a.getLocationCode().equalsIgnoreCase(locationCode)).map(PickGuideBizDTO::getSkuCode).distinct().count();
            hfCurrentWaitPickBizDTO.setSkuType((int) count);
            hfCurrentWaitPickBizDTOList.add(hfCurrentWaitPickBizDTO);
        });
        return Result.success(hfCurrentWaitPickBizDTOList);
    }

    @Override
    public Result<List<HFCurrentLocationWaitPickBizDTO>> currentPickLocationDetail(ScanLocationBizParam param) {

        PickDTO pickDTO = checkPickDTO(param.getPickCode());

        List<PickGuideBizDTO> pickGuideBizDTOList;
        if (ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString().equals(pickDTO.getBusinessType())) {
            pickGuideBizDTOList = pickGuideBiz.getValidityPeriodPickGuide(pickDTO);
        } else {
            pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);
        }
        if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
        }
        //获取拣选单所有的拣货信息
        List<PickGuideBizDTO> pickGuideBizDTOS = pickGuideBizDTOList.stream()
                .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickGuideBizDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("当前拣选单:%s已完成拣货", param.getPickCode()));
        }
        pickGuideBizDTOS = pickGuideBizDTOList.stream()
                .filter(a -> a.getLocationCode().equalsIgnoreCase(param.getLocationCode()))
                .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getPickQty()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickGuideBizDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("当前库位:%s已完成拣货", param.getLocationCode()));
        }
        List<String> skuCodeList = pickGuideBizDTOS.stream().map(PickGuideBizDTO::getSkuCode).distinct().collect(Collectors.toList());
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(pickDTO.getCargoCode());
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "商品信息异常");
        }

        List<HFCurrentLocationWaitPickBizDTO> hfCurrentWaitPickBizDTOList = new ArrayList<>();
        pickGuideBizDTOS.forEach(pickGuideBizDTO -> {
            HFCurrentLocationWaitPickBizDTO hfCurrentWaitPickBizDTO = new HFCurrentLocationWaitPickBizDTO();
            hfCurrentWaitPickBizDTO.setProductionNo(pickGuideBizDTO.getProductionNo());
            hfCurrentWaitPickBizDTO.setValidityCode(pickGuideBizDTO.getValidityCode());
            hfCurrentWaitPickBizDTO.setSkuCode(pickGuideBizDTO.getSkuCode());
            hfCurrentWaitPickBizDTO.setUpcCode(pickGuideBizDTO.getUpcCode());
            hfCurrentWaitPickBizDTO.setExpireDateDesc(pickGuideBizDTO.getExpireDateDesc());
            hfCurrentWaitPickBizDTO.setQty(pickGuideBizDTO.getQty());
            skuDTOList.stream()
                    .filter(a -> a.getCode().equalsIgnoreCase(pickGuideBizDTO.getSkuCode()))
                    .findFirst().ifPresent(a -> {
                        hfCurrentWaitPickBizDTO.setSkuName(a.getName());
                    });
            hfCurrentWaitPickBizDTOList.add(hfCurrentWaitPickBizDTO);
        });
        return Result.success(hfCurrentWaitPickBizDTOList);
    }

    @Override
    public Result<List<HFCurrentPickTaskBizDTO>> getHFSplitTaskForUser() {
        //B2C的单品和多品，先拣后分
        PickParam pickParam = new PickParam();
        pickParam.setSplitBy(CurrentUserHolder.getUserName());
        pickParam.setStatusList(Collections.singletonList(PickEnum.PickStatusEnum.SPLIT_BEGIN_STATUS.getCode()));
        pickParam.setWorkType(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode());
        pickParam.setPickMethod(PickEnum.PickMethodEnum.PICK_METHOD_0.getCode());
        pickParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString());
        //只有原始拣选单可以RF拣货
        pickParam.setPickFlag(PickEnum.PickFlagEnum.ORIGIN.getCode());
        pickParam.setTypeList(Arrays.asList(PickEnum.PickOrderTypeEnum.SINGLE.getCode(), PickEnum.PickOrderTypeEnum.MORE.getCode()));
        List<PickDTO> pickDTOList = remotePickClient.getList(pickParam);
        if (CollectionUtils.isEmpty(pickDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到领取的分拣任务");
        }
        List<HFCurrentPickTaskBizDTO> hfCurrentPickTaskBizDTOList = new ArrayList<>();

        pickDTOList.forEach(pickDTO -> {
            HFCurrentPickTaskBizDTO hfCurrentPickTaskBizDTO = ConverterUtil.convert(pickDTO, HFCurrentPickTaskBizDTO.class);
            CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(pickDTO.getCargoCode());
            hfCurrentPickTaskBizDTO.setCargoName(cargoOwnerDTO != null ? cargoOwnerDTO.getName() : "");
            CarrierDTO carrierDTO = remoteCarrierClient.queryByCode(pickDTO.getCarrierCode());
            hfCurrentPickTaskBizDTO.setCarrierName(carrierDTO != null ? carrierDTO.getName() : "");
            hfCurrentPickTaskBizDTO.setSalePlatformName("");
            hfCurrentPickTaskBizDTO.setPickMethod(pickDTO.getPickMethod());
            SalePlatformParam salePlatformParam = new SalePlatformParam();
            salePlatformParam.setCode(pickDTO.getSalePlatform());
            List<SalePlatformDTO> salePlatformDTOList = remoteSalePlatform.getList(salePlatformParam);
            salePlatformDTOList.stream()
                    .filter(a -> a.getCode().equalsIgnoreCase(pickDTO.getSalePlatform()))
                    .findFirst().ifPresent(a -> {
                        hfCurrentPickTaskBizDTO.setSalePlatformName(a.getName());
                    });
            hfCurrentPickTaskBizDTO.setZoneCode(pickDTO.getPickZoneAll());
            hfCurrentPickTaskBizDTO.setContCode(StringUtils.isEmpty(pickDTO.getContCode()) ? "" : pickDTO.getContCode());
            hfCurrentPickTaskBizDTOList.add(hfCurrentPickTaskBizDTO);
        });
        return Result.success(hfCurrentPickTaskBizDTOList);
    }

    @Override
    public Result<Boolean> getHFExistsSplitTaskForUser() {
        PickParam pickParam = new PickParam();
        pickParam.setSplitBy(CurrentUserHolder.getUserName());
        pickParam.setStatusList(Collections.singletonList(PickEnum.PickStatusEnum.SPLIT_BEGIN_STATUS.getCode()));
        pickParam.setWorkType(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode());
        //只有原始拣选单可以RF拣货
        pickParam.setPickFlag(PickEnum.PickFlagEnum.ORIGIN.getCode());
        List<PickDTO> pickDTOList = remotePickClient.getList(pickParam);
        if (CollectionUtils.isEmpty(pickDTOList)) {
            return Result.success(false);
        }
        return Result.success(true);
    }

    @Override
    public Result<HFPickSkuBizDTO> scanSplitSku(PickSkuAndLocationBizParam param) {
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(param.getPickCode());
        pickParam.setStatusList(Collections.singletonList(PickEnum.PickStatusEnum.SPLIT_BEGIN_STATUS.getCode()));
        pickParam.setWorkType(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode());
        pickParam.setPickMethod(PickEnum.PickMethodEnum.PICK_METHOD_0.getCode());
        pickParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString());
        pickParam.setTypeList(Arrays.asList(PickEnum.PickOrderTypeEnum.SINGLE.getCode(), PickEnum.PickOrderTypeEnum.MORE.getCode()));
        PickDTO pickDTO = remotePickClient.getPickAndDetailList(pickParam);
        if (pickDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到拣选单号:%s", param.getPickCode()));
        }
        if (!pickDTO.getSplitBy().equalsIgnoreCase(CurrentUserHolder.getUserName())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到您领取的分拣中的拣选单号:%s", param.getPickCode()));
        }
        List<PickGuideBizDTO> pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);
        if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
        }
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setCargoCode(pickDTO.getCargoCode());
        skuUpcParam.setUpcCode(param.getUpcCode());
        skuUpcParam.setStatus(SkuStatusEnum.STATUS_ENABLED.getStatus());
        List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
        if (CollectionUtils.isEmpty(skuUpcList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品条码:%s", param.getUpcCode()));
        }
        List<String> skuCodeList = skuUpcList.stream().map(SkuUpcDTO::getSkuCode).distinct().collect(Collectors.toList());

        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(pickDTO.getCargoCode());
        skuParam.setCode(skuCodeList.get(0));
        SkuDTO skuDTO = remoteSkuClient.get(skuParam);
        if (skuDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品:%s", param.getUpcCode()));
        }
        List<PickGuideBizDTO> guideBizDTOS = pickGuideBizDTOList.stream()
                .filter(a -> skuCodeList.contains(a.getSkuCode()))
                .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getSplitQty()))
                .sorted(Comparator.comparing(PickGuideBizDTO::getExpireDateDesc))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(guideBizDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品条码:%s未找到待分拣任务", param.getUpcCode()));
        }
        HFPickSkuBizDTO hfPickSkuBizDTO = new HFPickSkuBizDTO();
        hfPickSkuBizDTO.setSkuCode(skuDTO.getCode());
        hfPickSkuBizDTO.setSkuName(skuDTO.getName());
        hfPickSkuBizDTO.setPickCode(pickDTO.getPickCode());
        hfPickSkuBizDTO.setUpcCode(param.getUpcCode());


        //获取待拣货第一条商品信息
        PickGuideBizDTO pickGuideBizDTO = guideBizDTOS.get(0);
        hfPickSkuBizDTO.setProductionNo(pickGuideBizDTO.getProductionNo());
        hfPickSkuBizDTO.setExpireDateDesc(pickGuideBizDTO.getExpireDateDesc());

        // 维度 b2bGroup  skuLotNo B/C单分组 B单分组sku+效期  C单货主,商品,生产日期,失效日期,生产批次号,效期码
        hfPickSkuBizDTO.setSkuLotNo(pickGuideBizDTO.getB2bGroup());

        hfPickSkuBizDTO.setPickQty(pickGuideBizDTO.getQty());
        hfPickSkuBizDTO.setPackQty(pickDTO.getPackageQty());
        hfPickSkuBizDTO.setPickMethod(pickDTO.getPickMethod());
        hfPickSkuBizDTO.setPickMethod("");
        String guideToSplit = pickGuideBizDTO.getGuideToSplit().replaceAll("\\[", "").replaceAll("]", "");
        List<HFSkuAndBasketNoBizDTO> skuAndBasketNoList = new ArrayList<>();
        if (!StringUtils.isEmpty(guideToSplit)) {
            List<String> guideList = Arrays.asList(guideToSplit.split(","));
            guideList.forEach(guide -> {
                HFSkuAndBasketNoBizDTO hfSkuAndBasketNoBizDTO = new HFSkuAndBasketNoBizDTO();
                hfSkuAndBasketNoBizDTO.setBasketNo(Integer.valueOf(guide.split("x")[0]));
                hfSkuAndBasketNoBizDTO.setQty(guide.split("x")[1]);
                skuAndBasketNoList.add(hfSkuAndBasketNoBizDTO);
            });
        }
        hfPickSkuBizDTO.setSkuAndBasketNoList(skuAndBasketNoList);

        //CW货主
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(skuDTO.getCargoCode());
        hfPickSkuBizDTO.setCwCargo(false);
        if (cargoOwnerDTO != null && CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.CW_CARGO)) {
            hfPickSkuBizDTO.setCwCargo(true);
        }

        return Result.success(hfPickSkuBizDTO);
    }

    @Override
    public Result<Boolean> receiveSplitPick(CodeParam param) {
        String pickCodeLockKey = String.format("dt_wms-hf-receive-split-pick:%s:%s", CurrentRouteHolder.getWarehouseCode(), param.getCode());
        RLock lock = redissonClient.getLock(pickCodeLockKey);
        try {
            boolean tryLock = lock.tryLock(1, 30, TimeUnit.SECONDS);

            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后重试");
            }
            //B2C的单品和多品，先拣后分
            PickParam pickParam = new PickParam();
            pickParam.setPickCode(param.getCode());
            PickDTO pickDTO = remotePickClient.get(pickParam);
            if (pickDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "未找到领取的分拣任务");
            }
            if (pickDTO.getStatus().equalsIgnoreCase(PickEnum.PickStatusEnum.SPLIT_BEGIN_STATUS.getCode())
                    && !StringUtils.isEmpty(pickDTO.getSplitBy())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s已被领取,不能重复领取,请确认", pickDTO.getPickCode()));
            }
            if (Arrays.asList(PickEnum.PickStatusEnum.SPLIT_END_STATUS.getCode(), PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode(), PickEnum.PickStatusEnum.ZJ_BEGIN_STATUS.getCode()).contains(pickDTO.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s已完成分拣,请确认", pickDTO.getPickCode()));
            }
            //目前只有B2C+单品/多品+拣选结束+RF+先拣后分 才可以领取任务
            if (!pickDTO.getStatus().equalsIgnoreCase(PickEnum.PickStatusEnum.PICK_END_STATUS.getCode())
                    || !pickDTO.getWorkType().equalsIgnoreCase(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode())
                    || !pickDTO.getPickMethod().equalsIgnoreCase(PickEnum.PickMethodEnum.PICK_METHOD_0.getCode())
                    || !pickDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())
                    || !Arrays.asList(PickEnum.PickOrderTypeEnum.SINGLE.getCode(), PickEnum.PickOrderTypeEnum.MORE.getCode()).contains(pickDTO.getType())) {
                throw new BaseException(BaseBizEnum.TIP, "当前拣选不能领取的分拣任务");
            }
            pickDTO.setSplitBy(CurrentUserHolder.getUserName());
            pickDTO.setStatus(PickEnum.PickStatusEnum.SPLIT_BEGIN_STATUS.getCode());
            pickDTO.setSplitStartTime(System.currentTimeMillis());
            remotePickClient.modifyBatch(Collections.singletonList(pickDTO));

            //记录拣选单的日志
            BillLogDTO billLogDTO = new BillLogDTO();
            billLogDTO.setCargoCode(pickDTO.getCargoCode());
            billLogDTO.setBillNo(pickDTO.getPickCode());
            billLogDTO.setBillType(BillLogTypeEnum.PICK.getType());
            billLogDTO.setOpBy(CurrentUserHolder.getUserName());
            billLogDTO.setOpDate(System.currentTimeMillis());
            billLogDTO.setOpContent(String.format("%s领取分拣拣选单:%s", CurrentUserHolder.getUserName(), pickDTO.getPickCode()));
            billLogDTO.setOpRemark(String.format("%s领取分拣拣选单:%s", CurrentUserHolder.getUserName(), pickDTO.getPickCode()));
            remoteBillLogClient.save(billLogDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("receiveSplitPick:{} {}", e, e.getMessage());
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return Result.success(true);
    }

    @Override
    public Result<String> commitSplitPick(PickCommitSkuBizParam param) {
        String pickCodeLockKey = String.format("dt-wms-hf-commit-split-pick:%s:%s", CurrentRouteHolder.getWarehouseCode(), param.getPickCode());
        RLock lock = redissonClient.getLock(pickCodeLockKey);
        try {
            boolean tryLock = lock.tryLock(1, 30, TimeUnit.SECONDS);

            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后重试");
            }
            //10 当前商品-批次已完成
            //30 当前拣选单已完成
            String flag = "10";
            PickParam pickParam = new PickParam();
            pickParam.setPickCode(param.getPickCode());
            pickParam.setStatusList(Collections.singletonList(PickEnum.PickStatusEnum.SPLIT_BEGIN_STATUS.getCode()));
            pickParam.setWorkType(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode());
            pickParam.setPickMethod(PickEnum.PickMethodEnum.PICK_METHOD_0.getCode());
            pickParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString());
            pickParam.setTypeList(Arrays.asList(PickEnum.PickOrderTypeEnum.SINGLE.getCode(), PickEnum.PickOrderTypeEnum.MORE.getCode()));
            PickDTO pickDTO = remotePickClient.getPickAndDetailList(pickParam);
            if (pickDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "未找到领取的分拣任务");
            }
            List<PickGuideBizDTO> pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);
            if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "分拣货指引异常");
            }
            SkuParam skuParam = new SkuParam();
            skuParam.setCargoCode(pickDTO.getCargoCode());
            skuParam.setCode(param.getSkuCode());
            SkuDTO skuDTO = remoteSkuClient.get(skuParam);
            if (skuDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品:%s", param.getSkuCode()));
            }
            List<PickGuideBizDTO> guideBizDTOS = pickGuideBizDTOList.stream()
                    .filter(a -> param.getSkuCode().equalsIgnoreCase(a.getSkuCode()))
                    .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getSplitQty()))
                    .filter(a -> a.getB2bGroup().equalsIgnoreCase(param.getSkuLotNo()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(guideBizDTOS)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s未找到待拣货任务", param.getUpcCode()));
            }
            List<String> skuLotNoList = guideBizDTOS.get(0).getSkuLotNoList();

            AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
            allocationOrderParam.setPickCode(pickDTO.getPickCode());
            allocationOrderParam.setWaveCode(pickDTO.getWaveCode());
            List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getList(allocationOrderParam);
            if (CollectionUtils.isEmpty(allocationOrderDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到有效的商品:%s的库存记录", param.getSkuCode()));
            }
            //当前库位的数据
            List<AllocationOrderDTO> currentAllocationOrderDTOList = allocationOrderDTOList.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(param.getSkuCode()))
                    .filter(a -> skuLotNoList.contains(a.getSkuLotNo()))
                    .collect(Collectors.toList());
            //预包商品对应的子商品明细也需要库存交回
            List<AllocationOrderDTO> commitStockList = new ArrayList<>();
            commitStockList.addAll(currentAllocationOrderDTOList);

            currentAllocationOrderDTOList.forEach(a -> a.setSplitQty(a.getExpQty()));
            List<SystemEventDTO> systemEventDTOList = buildSystemEventList(pickDTO, currentAllocationOrderDTOList, SystemEventEnum.PICK_SPLIT_END.getCode());
            //提交数据
            PickSplitPartBO pickRestorePartBO = new PickSplitPartBO();
            pickRestorePartBO.setPickCode(pickDTO.getPickCode());
            pickRestorePartBO.setWarehouseCode(pickDTO.getWarehouseCode());
            pickRestorePartBO.setAllocationOrderDTOList(currentAllocationOrderDTOList);
            pickRestorePartBO.setSystemEventDTOList(systemEventDTOList);
            pickRestoreGtsService.confirmPickSplitPart(pickRestorePartBO);
            if (allocationOrderDTOList.stream().allMatch(a -> a.getExpQty().compareTo(a.getSplitQty()) == 0)) {
                //全部完成分拣结束
                pickDTO.setStatus(PickEnum.PickStatusEnum.SPLIT_END_STATUS.getCode());
                pickDTO.setSplitEndTime(System.currentTimeMillis());
                remotePickClient.modifyBatch(Collections.singletonList(pickDTO));

                //记录拣选单的日志
                BillLogDTO billLogDTO = new BillLogDTO();
                billLogDTO.setCargoCode(pickDTO.getCargoCode());
                billLogDTO.setBillNo(pickDTO.getPickCode());
                billLogDTO.setBillType(BillLogTypeEnum.PICK.getType());
                billLogDTO.setOpBy(CurrentUserHolder.getUserName());
                billLogDTO.setOpDate(System.currentTimeMillis());
                billLogDTO.setOpContent("完成分拣拣选单");
                billLogDTO.setOpRemark("完成领取分拣拣选单");
                remoteBillLogClient.save(billLogDTO);

                ContainerDTO containerDTO = remoteContainerClient.queryByCode(pickDTO.getContCode());
                ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                containerLogDTO.setWarehouseCode(containerDTO.getWarehouseCode());
                containerLogDTO.setContCode(containerDTO.getCode());
                containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                containerLogDTO.setCreatedTime(System.currentTimeMillis());
                containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                containerLogDTO.setOpContent(String.format("RF分拣拣货%s释放容器:%s", pickDTO.getPickCode(), containerDTO.getCode()));
                containerLogDTO.setOpDate(System.currentTimeMillis());
                containerLogDTO.setOccupyNo(containerDTO.getOccupyNo());
                containerLogDTO.setOccupyType(containerDTO.getOccupyType());
                remoteContainerLogClient.save(containerLogDTO);
                //释放容器
                containerDTO.setStatus(ContainerStatusEnum.ENABLE.getValue());
                containerDTO.setOccupyType("");
                containerDTO.setOccupyNo("");
                containerDTO.setRemark("");
                remoteContainerClient.modifyBatch(Collections.singletonList(containerDTO));

                //当前拣选单已全部完成
                flag = "30";
                return Result.success(flag);
            }
            return Result.success(flag);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("commitSplitPick:{} {}", e, e.getMessage());
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    @Override
    public Result<Boolean> completeSplitPick(CodeParam param) {

        String pickCodeLockKey = String.format("dt_wms-hf-complete-split-pick:%s:%s", CurrentRouteHolder.getWarehouseCode(), param.getCode());
        RLock lock = redissonClient.getLock(pickCodeLockKey);
        try {
            boolean tryLock = lock.tryLock(1, 30, TimeUnit.SECONDS);

            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后重试");
            }
            PickParam pickParam = new PickParam();
            pickParam.setPickCode(param.getCode());
            PickDTO pickDTO = remotePickClient.get(pickParam);
            if (pickDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到拣选单号:%s", param.getCode()));
            }
            if (!pickDTO.getSplitBy().equalsIgnoreCase(CurrentUserHolder.getUserName())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到您领取的分拣任务:%s", param.getCode()));
            }
            if (PickEnum.PickStatusEnum.SPLIT_END_STATUS.getCode().contains(pickDTO.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("当前拣选单号:%s已完成分拣", param.getCode()));
            }
            if (!Arrays.asList(PickEnum.PickStatusEnum.SPLIT_BEGIN_STATUS.getCode()).contains(pickDTO.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("未找到二次分拣状态拣选单号:%s", param.getCode()));
            }

            AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
            allocationOrderParam.setPickCode(pickDTO.getPickCode());
            allocationOrderParam.setWaveCode(pickDTO.getWaveCode());
            List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getList(allocationOrderParam);
            if (CollectionUtils.isEmpty(allocationOrderDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单%s未找到数据", pickDTO.getPickCode()));
            }
            if (allocationOrderDTOList.stream().anyMatch(a -> a.getExpQty().compareTo(a.getSplitQty()) > 0)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单%s未完成二次分拣,不能完成", pickDTO.getPickCode()));
            }
            pickDTO.setStatus(PickEnum.PickStatusEnum.SPLIT_END_STATUS.getCode());
            pickDTO.setSplitEndTime(System.currentTimeMillis());
            remotePickClient.modifyBatch(Collections.singletonList(pickDTO));

            //记录拣选单的日志
            BillLogDTO billLogDTO = new BillLogDTO();
            billLogDTO.setCargoCode(pickDTO.getCargoCode());
            billLogDTO.setBillNo(pickDTO.getPickCode());
            billLogDTO.setBillType(BillLogTypeEnum.PICK.getType());
            billLogDTO.setOpBy(CurrentUserHolder.getUserName());
            billLogDTO.setOpDate(System.currentTimeMillis());
            billLogDTO.setOpContent("完成分拣拣选单");
            billLogDTO.setOpRemark("完成领取分拣拣选单");
            remoteBillLogClient.save(billLogDTO);

            ContainerDTO containerDTO = remoteContainerClient.queryByCode(pickDTO.getContCode());
            ContainerLogDTO containerLogDTO = new ContainerLogDTO();
            containerLogDTO.setWarehouseCode(containerDTO.getWarehouseCode());
            containerLogDTO.setContCode(containerDTO.getCode());
            containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
            containerLogDTO.setCreatedTime(System.currentTimeMillis());
            containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
            containerLogDTO.setOpContent(String.format("RF拣货%s释放容器:%s", pickDTO.getPickCode(), containerDTO.getCode()));
            containerLogDTO.setOpDate(System.currentTimeMillis());
            containerLogDTO.setOccupyNo(containerDTO.getOccupyNo());
            containerLogDTO.setOccupyType(containerDTO.getOccupyType());
            remoteContainerLogClient.save(containerLogDTO);
            //释放容器
            containerDTO.setStatus(ContainerStatusEnum.ENABLE.getValue());
            containerDTO.setOccupyType("");
            containerDTO.setOccupyNo("");
            containerDTO.setRemark("");
            remoteContainerClient.modifyBatch(Collections.singletonList(containerDTO));
        } catch (Exception e) {
            e.printStackTrace();
            log.info("completePicK:{} {}", e, e.getMessage());
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<List<PickGuideBizDTO>> getPickSplitWaitSku(CodeParam param) {
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(param.getCode());
        PickDTO pickDTO = remotePickClient.getPickAndDetailList(pickParam);
        if (pickDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到拣选单号:%s", param.getCode()));
        }
        if (!pickDTO.getSplitBy().equalsIgnoreCase(CurrentUserHolder.getUserName())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到您领取的分拣任务:%s", param.getCode()));
        }
        if (PickEnum.PickStatusEnum.SPLIT_END_STATUS.getCode().contains(pickDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("当前拣选单号:%s已完成分拣", param.getCode()));
        }
        if (!Arrays.asList(PickEnum.PickStatusEnum.SPLIT_BEGIN_STATUS.getCode()).contains(pickDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到二次拣货状态拣选单号:%s", param.getCode()));
        }
        List<PickGuideBizDTO> pickGuideBizDTOList = pickGuideBiz.getB2CPreValidityPeriodPickGuide(pickDTO);

        if (CollectionUtils.isEmpty(pickGuideBizDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣货指引异常");
        }
        //获取拣选单所有的拣货信息
        List<PickGuideBizDTO> pickGuideBizDTOS = pickGuideBizDTOList.stream()
                .filter(a -> Integer.parseInt(a.getQty()) > Integer.parseInt(a.getSplitQty()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickGuideBizDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, "当前拣选单已完成拣货");
        }
        //按商品+批次合并
        Map<String, List<PickGuideBizDTO>> map = pickGuideBizDTOS.stream().collect(Collectors.groupingBy(a -> String.format("%s,%s", a.getSkuCode(), a.getB2bGroup())));
        List<PickGuideBizDTO> pickGuideBizDTOListLast = new ArrayList<>();
        map.forEach((key, dtoList) -> {
            PickGuideBizDTO pickGuideBizDTO = new PickGuideBizDTO();
            int sum = dtoList.stream().map(a -> {
                        return Integer.valueOf(a.getQty());
                    })
                    .mapToInt(Integer::intValue).sum();
            pickGuideBizDTO.setQty(sum + "");
            pickGuideBizDTO.setSkuCode(key.split(",")[0]);
            pickGuideBizDTO.setSkuLotNo(key.split(",")[1]);
            pickGuideBizDTO.setExpireDateDesc(dtoList.get(0).getExpireDateDesc());
            pickGuideBizDTO.setProductionNo(dtoList.get(0).getProductionNo());
            pickGuideBizDTO.setValidityCode(dtoList.get(0).getValidityCode());
            pickGuideBizDTO.setSkuName(dtoList.get(0).getSkuName());
            pickGuideBizDTO.setUpcCode(dtoList.get(0).getUpcCode());
            pickGuideBizDTOListLast.add(pickGuideBizDTO);
        });
        return Result.success(pickGuideBizDTOListLast);
    }

    private PickDTO checkPickDTO(String pickCode) {
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(pickCode);
        PickDTO pickDTO = remotePickClient.getPickAndDetailList(pickParam);
        if (pickDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到拣选单号:%s", pickCode));
        }
        if (!pickDTO.getPickBy().equalsIgnoreCase(CurrentUserHolder.getUserName())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到您领取的拣选单号:%s", pickCode));
        }
        if (Objects.equals(PickEnum.PickStatusEnum.PICK_END_STATUS.getCode(), pickDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s已完成拣货", pickCode));
        }
        if (!Objects.equals(PickEnum.PickStatusEnum.PICK_BEGIN_STATUS.getCode(), pickDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("未找到拣货中拣选单号:%s", pickCode));
        }
        return pickDTO;
    }
}
