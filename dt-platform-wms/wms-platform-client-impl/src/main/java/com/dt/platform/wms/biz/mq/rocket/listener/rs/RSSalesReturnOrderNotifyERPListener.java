package com.dt.platform.wms.biz.mq.rocket.listener.rs;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.dt.component.canal.mq.AbstractCanalMQService;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.component.common.enums.rs.RSOrderStatusEnum;
import com.dt.component.common.enums.rs.RSOutEnum;
import com.dt.component.common.enums.rs.RSSuccessEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 销退单通知OMS
 *
 * <AUTHOR>
 */
@Service
@RocketMQMessageListener(topic = "dt_wms_sales_return_order_topic", consumerGroup = "rs_sales_return_order_notify_erp")
@Slf4j
public class RSSalesReturnOrderNotifyERPListener extends AbstractCanalMQService<SalesReturnOrderDTO> implements RocketMQListener<FlatMessage> {

    private static final List<Integer> statusNeedNotifyERPList = ListUtil.toList(RSOrderStatusEnum.OUT.getCode(),
            RSOrderStatusEnum.DECLARE_END.getCode());

    @Autowired
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;


    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }


    @Override
    protected void insert(SalesReturnOrderDTO salesReturnOrderDTO) {

    }

    @Override
    protected void update(SalesReturnOrderDTO before, SalesReturnOrderDTO after) {
        if (after == null) return;
        RpcContextUtil.setWarehouseCode(after.getWarehouseCode());
        if (!statusNeedNotifyERPList.contains(after.getStatus())) return;
        // 保税 + 已申报
        if (TaxTypeEnum.TYPE_BONDED_TAX.getCode().equalsIgnoreCase(after.getTaxType())
                && RSSuccessEnum.SUCCESS.getCode().equals(after.getDeclarationResults())
                && RSOrderStatusEnum.DECLARE_END.getCode().equals(after.getStatus())) {
            sendToErp(after);
        }
        if (TaxTypeEnum.TYPE_DUTY_TAX.getCode().equalsIgnoreCase(after.getTaxType())
                && RSOutEnum.DUTY.getCode().equals(after.getOutType())
                && RSOrderStatusEnum.OUT.getCode().equals(after.getStatus())) {
            sendToErp(after);
        }
    }

    @Override
    protected void delete(SalesReturnOrderDTO salesReturnOrderDTO) {

    }

    private void sendToErp(SalesReturnOrderDTO salesReturnOrderDTO) {
        remoteSalesReturnOrderClient.syncToERP(salesReturnOrderDTO);
    }
}
