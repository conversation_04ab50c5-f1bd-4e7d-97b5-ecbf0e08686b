package com.dt.platform.wms.client.rs.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.dt.component.common.enums.bill.*;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuLifeCtrlEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.bill.client.rs.IOpExceptionDetailClient;
import com.dt.domain.bill.dto.related.RelatedBillDTO;
import com.dt.domain.bill.dto.rs.OpExceptionDTO;
import com.dt.domain.bill.dto.rs.OpExceptionDetailDTO;
import com.dt.domain.bill.param.related.RelatedBillParam;
import com.dt.domain.bill.param.rs.OpExceptionDetailParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.config.UrlConfig;
import com.dt.platform.wms.biz.param.SkuLotCheckAndFormatParam;
import com.dt.platform.wms.client.AdjustBizClient;
import com.dt.platform.wms.client.rs.exception.UnaccountedProfitException;
import com.dt.platform.wms.dto.adjust.AdjustDetailBizDTO;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.integration.related.IRemoteRelatedBillClient;
import com.dt.platform.wms.param.adjust.AdjustBizParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TaoBaoQiMenInventoryReportCallbackHandler {

    @Resource
    private AdjustBizClient adjustBizClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteRelatedBillClient remoteRelatedBillClient;

    @Resource
    private ISkuLotBiz skuLotBiz;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @DubboReference
    private IOpExceptionDetailClient opExceptionDetailClient;

    @Resource
    private UrlConfig urlConfig;

    /**
     * 3.18不回告了 直接调整库存
     * com.dt.platform.wms.biz.ISkuLotBiz#findAndFormatSkuLot这个是统一生成批次的方法,你到时候先调用这个方法生成批次信息，
     * 返回接口的id为null你需要调用com.dt.platform.wms.integration.IRemoteSkuLotClient#saveBatch把批次信息保存下来
     *
     * @param opExceptionDTO opExceptionDTO
     */
    public String exec(OpExceptionDTO opExceptionDTO) {
        // 异常商品明细
        OpExceptionDetailParam opExceptionDetailParam = new OpExceptionDetailParam();
        opExceptionDetailParam.setAbnormalOrderNo(opExceptionDTO.getAbnormalOrderNo());
        Result<List<OpExceptionDetailDTO>> opExceptionDetailClientList = opExceptionDetailClient.getList(opExceptionDetailParam);
        if (!opExceptionDetailClientList.checkSuccess()) {
            log.error("异常商品明细获取失败 {} {}", opExceptionDTO.getAbnormalOrderNo(), opExceptionDetailClientList.getMessage());
            return null;
        }

        List<OpExceptionDetailDTO> opExceptionDetailDTOList = opExceptionDetailClientList.getData();
        if (CollectionUtil.isEmpty(opExceptionDetailDTOList)) throw ExceptionUtil.dataError();

        RpcContextUtil.setWarehouseCode(opExceptionDTO.getWarehouseCode());
        Map<String, SkuDTO> skuDTOMap = remoteSkuClient.skuDTOMap(opExceptionDTO.getCargoCode(), opExceptionDetailDTOList.stream().map(OpExceptionDetailDTO::getSkuCode).distinct().collect(Collectors.toList()));

        // 生成批次信息    
        for (OpExceptionDetailDTO opExceptionDetailDTO : opExceptionDetailDTOList) {
            try {
                if (StrUtil.isBlank(opExceptionDetailDTO.getSkuCode())) {
                    throw new UnaccountedProfitException("不进行盘盈操作");
                }
                SkuDTO skuDTO = skuDTOMap.get(StrUtil.join(StrUtil.COLON, opExceptionDetailDTO.getWarehouseCode(), opExceptionDTO.getCargoCode(), opExceptionDetailDTO.getSkuCode()));
                if (null == skuDTO)
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, opExceptionDetailDTO.getSkuCode(), "不存在"));

                SkuLotCheckAndFormatParam skuLotCheckAndFormatParam = new SkuLotCheckAndFormatParam();
                skuLotCheckAndFormatParam.setManufDate(opExceptionDetailDTO.getManufDate());
                if (InventoryTypeEnum.ZP.getCode().equalsIgnoreCase(opExceptionDetailDTO.getInventoryType())) {
                    skuLotCheckAndFormatParam.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
                } else {
                    skuLotCheckAndFormatParam.setSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
                }
                skuLotCheckAndFormatParam.setInventoryType(opExceptionDetailDTO.getInventoryType());
                skuLotCheckAndFormatParam.setExpireDate(opExceptionDetailDTO.getExpireDate());
                skuLotCheckAndFormatParam.setReceiveDate(DateUtil.beginOfDay(DateTime.now()).getTime());
                skuLotCheckAndFormatParam.setProductionNo(StrUtil.EMPTY);
                skuLotCheckAndFormatParam.setExternalLinkBillNo(opExceptionDTO.getAbnormalOrderNo());
                SkuLotDTO skuLotDTO = skuLotBiz.findAndFormatSkuLot(skuLotCheckAndFormatParam, skuDTO, false);
                if (skuLotDTO.getId() == null) {
                    remoteSkuLotClient.saveBatch(Collections.singletonList(skuLotDTO));
                }
                opExceptionDetailDTO.setSkuLotNo(skuLotDTO.getCode());
            } catch (Exception exception) {
                WechatUtil.sendMessage(StrUtil.join(StrUtil.EMPTY, opExceptionDTO.getWarehouseCode(), "异常登记", opExceptionDetailDTO.getAbnormalOrderNo(), "生成批次信息异常", exception.getMessage()), urlConfig.getOpExceptionSkuLotGenFailUrl());
                throw exception;
            }
        }
        String note = String.format("【系统生成】异常登记单号%s超15天盘盈 ，运单号：%s", opExceptionDTO.getAbnormalOrderNo(), opExceptionDTO.getMailMo());

        AdjustBizParam adjustBizParam = new AdjustBizParam();
        adjustBizParam.setWarehouseCode(opExceptionDTO.getWarehouseCode());
        adjustBizParam.setCargoCode(opExceptionDTO.getCargoCode());
        adjustBizParam.setReason(AdjustReasonEnum.STATUS_CHECK.getCode());
        adjustBizParam.setBillNo(opExceptionDTO.getAbnormalOrderNo());
        adjustBizParam.setStatus(AdjustStatusEnum.CREATED.getStatus());
        adjustBizParam.setType(AdjustTypeEnum.ADD.getStatus());
        adjustBizParam.setNote(note);
        adjustBizParam.setBusinessType(AdjustBusinessTypeEnum.OP_EXCEPTION.getCode());
        ArrayList<AdjustDetailBizDTO> detailList = getAdjustDetailBizDTOS(opExceptionDTO, opExceptionDetailDTOList);
        if (CollectionUtil.isEmpty(detailList)) throw new UnaccountedProfitException("不进行盘盈操作");
        adjustBizParam.setDetailList(detailList);
        Result<Boolean> result = adjustBizClient.createFromOPException(adjustBizParam);
        if (result.checkSuccess() && result.getData()) {
            log.info("盘盈 调整库存成功 {}", opExceptionDTO.getAbnormalOrderNo());
        } else {
            log.error("盘盈 调整库存失败 {} {}", opExceptionDTO.getAbnormalOrderNo(), result.getMessage());
        }

        RelatedBillParam relatedBillParam = new RelatedBillParam();
        relatedBillParam.setBillNo(opExceptionDTO.getAbnormalOrderNo());
        relatedBillParam.setType(RelatedBillTypeEnum.BILL_TYPE_OP_EXCEPTION_ADJUST.getType());
        RelatedBillDTO relatedBillDTO = remoteRelatedBillClient.get(relatedBillParam);
        String adjustCode = relatedBillDTO.getRelatedNo();
        return adjustCode;
    }

    private static ArrayList<AdjustDetailBizDTO> getAdjustDetailBizDTOS(OpExceptionDTO opExceptionDTO, List<OpExceptionDetailDTO> opExceptionDetailDTOList) {
        Map<String, AdjustDetailBizDTO> adjustDetailBizDTOMap = new HashMap<>();
        for (OpExceptionDetailDTO opExceptionDetailDTO : opExceptionDetailDTOList) {
            // 没有商品编码不生成明细
            if (StrUtil.isBlank(opExceptionDetailDTO.getSkuCode())) {
                throw new UnaccountedProfitException("不进行盘盈操作");
            }
            AdjustDetailBizDTO detailBizDTO = adjustDetailBizDTOMap.get(opExceptionDetailDTO.getSkuLotNo());
            if (detailBizDTO != null) {
                detailBizDTO.setAdjustQty(detailBizDTO.getAdjustQty().add(opExceptionDetailDTO.getExpSkuQty()));
            } else {
                AdjustDetailBizDTO adjustDetailBizDTO = new AdjustDetailBizDTO();
                adjustDetailBizDTO.setWarehouseCode(opExceptionDTO.getWarehouseCode());
                adjustDetailBizDTO.setCargoCode(opExceptionDTO.getCargoCode());
                adjustDetailBizDTO.setStatus(AdjustStatusEnum.CREATED.getStatus());
                adjustDetailBizDTO.setSkuCode(opExceptionDetailDTO.getSkuCode());
                adjustDetailBizDTO.setSkuLotNo(opExceptionDetailDTO.getSkuLotNo());
                adjustDetailBizDTO.setLocationCode(opExceptionDetailDTO.getLocationCode());
                adjustDetailBizDTO.setAdjustQty(opExceptionDetailDTO.getExpSkuQty());
                adjustDetailBizDTO.setReason(AdjustDetailReasonEnum.STOCK_INVENTORY.getCode());
                adjustDetailBizDTO.setRp(AdjustDetailRPEnum.STORE_REASON.getCode());
                adjustDetailBizDTO.setRemark("异常登记盘盈");
                adjustDetailBizDTOMap.put(opExceptionDetailDTO.getSkuLotNo(), adjustDetailBizDTO);
            }
        }

        return new ArrayList<>(adjustDetailBizDTOMap.values());
    }
}
