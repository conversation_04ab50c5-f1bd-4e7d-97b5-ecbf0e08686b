package com.dt.platform.wms.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.asn.AsnOutTypeEnum;
import com.dt.component.common.enums.asn.AsnTypeEnum;
import com.dt.component.common.enums.base.ContainerStatusEnum;
import com.dt.component.common.enums.rec.ReceiptError;
import com.dt.component.common.enums.rec.ReceiptStatusEnum;
import com.dt.component.common.enums.rec.ReceiptTypeEnum;
import com.dt.component.common.enums.shelf.ShelfMarkEnum;
import com.dt.component.common.enums.shelf.ShelfStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.ContainerDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.contLog.ContainerLogDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.ContainerParam;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.param.*;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.biz.IReceiptBiz;
import com.dt.platform.wms.dto.receipt.ReceiptBizDTO;
import com.dt.platform.wms.dto.receipt.ReceiptDetailBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.tally.IRemoteTallyClient;
import com.dt.platform.wms.integration.tally.IRemoteTallyDetailClient;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.receipt.ReceiptBizParam;
import com.dt.platform.wms.param.receipt.ReceiptDetailBizParam;
import com.dt.platform.wms.transaction.IReceiptGtsService;
import com.dt.platform.wms.transaction.bo.ReceiptAndContCancelReceiptBO;
import com.dt.platform.wms.transaction.bo.ReceiptAndContCancelReturnReceiptBO;
import com.dt.platform.wms.transaction.bo.ReceiptCancelBO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/21 17:30
 */
@Service
@Slf4j
public class IReceiptBizImpl implements IReceiptBiz {

    @Resource
    IRemoteReceiptClient iRemoteReceiptClient;

    @Resource
    IRemoteReceiptDetailClient remoteReceiptDetailClient;

    @Resource
    private IRemoteContainerClient remoteContainerClient;

    @Resource
    IRemoteCargoOwnerClient iRemoteCargoOwnerClient;

    @Resource
    IRemoteWarehouseClient iRemoteWarehouseClient;

    @Resource
    IRemoteDecimalPlaceClient decimalPlaceClient;

    @Resource
    IRemoteContainerClient iRemoteContainerClient;

    @Resource
    IRemoteAsnClient iRemoteAsnClient;

    @Resource
    IRemoteTallyClient remoteTallyClient;

    @Resource
    IRemoteTallyDetailClient remoteTallyDetailClient;

    @Resource
    private IReceiptGtsService receiptGtsService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    IRemoteShelfClient remoteShelfClient;

    @Override
    public IPage<ReceiptBizDTO> getPage(ReceiptBizParam param) {
        IPage<ReceiptDTO> queryPage = iRemoteReceiptClient.getPage(ConverterUtil.convert(param, ReceiptParam.class));
        if (queryPage == null) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        IPage<ReceiptBizDTO> receiptBizDTOIPage = ConverterUtil.convertPage(queryPage, ReceiptBizDTO.class);
        if (!CollectionUtils.isEmpty(receiptBizDTOIPage.getRecords())) {
            WarehouseDTO warehouseDTO = iRemoteWarehouseClient.queryByCode(receiptBizDTOIPage.getRecords().get(0).getWarehouseCode());
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(receiptBizDTOIPage.getRecords().stream().map(ReceiptBizDTO::getCargoCode).distinct().collect(Collectors.toList()));
            List<CargoOwnerDTO> allCargoOwner = iRemoteCargoOwnerClient.getAllCargoOwner(cargoOwnerParam);
            receiptBizDTOIPage.setRecords(receiptBizDTOIPage.getRecords().stream().map(entity -> {
                if (!Objects.isNull(warehouseDTO)) {
                    entity.setWarehouseName(warehouseDTO.getName());
                } else {
                    entity.setWarehouseName("");
                }
                if (!CollectionUtils.isEmpty(allCargoOwner)) {
                    allCargoOwner.stream().filter(a -> a.getCode().equalsIgnoreCase(entity.getCargoCode())).findFirst().ifPresent(a -> entity.setCargoName(a.getName()));
                } else {
                    entity.setCargoName("");
                }
                entity.setNumberFormat(decimalPlaceClient.getNumberFormat(entity.getWarehouseCode(), entity.getCargoCode()));
                return entity;
            }).collect(Collectors.toList()));
        }
        return receiptBizDTOIPage;
    }

    @Override
    public Boolean cancel(ReceiptBizParam param) {
        if (StringUtils.isEmpty(param.getRecId()) || StringUtils.isEmpty(param.getContCode())) {
            throw new BaseException(BaseBizEnum.NULL_ARGUMENT);
        }
        ReceiptDTO receiptDTO = iRemoteReceiptClient.queryReceiptAndDetailByRecId(param.getRecId());
        if (receiptDTO == null) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        if (!receiptDTO.getStatus().equalsIgnoreCase(ReceiptStatusEnum.CREATE_SHELF.getCode())) {
            throw new BaseException(ReceiptError.RECEIPT_CANCEL_STATUS_ERROR, receiptDTO.getRecId());
        }
        if (!receiptDTO.getReceiptType().equalsIgnoreCase(ReceiptTypeEnum.IMPORT_RECEIPT.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "取消只能操作导入收货的数据");
        }
        receiptDTO.setStatus(ReceiptStatusEnum.CANCLE_SHELF.code());

        if (!CollectionUtils.isEmpty(receiptDTO.getDetailDTOList())) {
            receiptDTO.getDetailDTOList().forEach(a -> a.setStatus(ReceiptStatusEnum.CANCLE_SHELF.code()));
        }
        ContainerDTO occupyContainer = remoteContainerClient.queryByCode(param.getContCode());
        //容器日志记录
        ContainerLogDTO containerLogDTO = new ContainerLogDTO();
        containerLogDTO.setWarehouseCode(occupyContainer.getWarehouseCode());
        containerLogDTO.setContCode(occupyContainer.getCode());
        containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
        containerLogDTO.setCreatedTime(System.currentTimeMillis());
        containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
        containerLogDTO.setOpContent(String.format("收货作业批次%s释放容器:%s", receiptDTO.getRecId(), occupyContainer.getCode()));
        containerLogDTO.setOpDate(System.currentTimeMillis());
        containerLogDTO.setOccupyNo(occupyContainer.getOccupyNo());
        containerLogDTO.setOccupyType(occupyContainer.getOccupyType());

        occupyContainer.setStatus(ContainerStatusEnum.ENABLE.getValue());
        occupyContainer.setOccupyType("");
        occupyContainer.setOccupyNo("");
        occupyContainer.setRemark("");
        //记录容器日志

        ReceiptCancelBO receiptCancelBO = new ReceiptCancelBO();
        receiptCancelBO.setContainer(occupyContainer);
        receiptCancelBO.setReceiptDTO(receiptDTO);
        receiptCancelBO.setContainerLogDTO(containerLogDTO);
        receiptGtsService.receiptCancelCommitContext(receiptCancelBO);
        return true;
    }

    @Override
    public ReceiptBizDTO queryDetail(ReceiptDetailBizParam param) {
        ReceiptDTO receiptDTO = iRemoteReceiptClient.queryReceiptAndDetailByRecId(param.getRecId());
        ReceiptBizDTO dataBizDTO = BeanUtil.copyProperties(receiptDTO, ReceiptBizDTO.class);
        if (!StringUtils.isEmpty(dataBizDTO)) {
            WarehouseDTO warehouseDTO = iRemoteWarehouseClient.queryByCode(dataBizDTO.getWarehouseCode());
            if (!Objects.isNull(warehouseDTO)) {
                dataBizDTO.setWarehouseName(warehouseDTO.getName());
            } else {
                dataBizDTO.setWarehouseName("");
            }
            CargoOwnerDTO cargoOwnerDTO = iRemoteCargoOwnerClient.queryByCode(dataBizDTO.getCargoCode());
            if (!Objects.isNull(cargoOwnerDTO)) {
                dataBizDTO.setCargoName(cargoOwnerDTO.getName());
            } else {
                dataBizDTO.setCargoName("");
            }
            dataBizDTO.setNumberFormat(decimalPlaceClient.getNumberFormat(dataBizDTO.getWarehouseCode(), dataBizDTO.getCargoCode()));
        }
        if (!StringUtils.isEmpty(dataBizDTO) && !CollectionUtils.isEmpty(dataBizDTO.getDetailDTOList())) {
            List<ReceiptDetailBizDTO> detailBizDTOS = dataBizDTO.getDetailDTOList().stream().map(entity -> {
                WarehouseDTO warehouseDTO = iRemoteWarehouseClient.queryByCode(entity.getWarehouseCode());
                if (!Objects.isNull(warehouseDTO)) {
                    entity.setWarehouseName(warehouseDTO.getName());
                } else {
                    entity.setWarehouseName("");
                }
                CargoOwnerDTO cargoOwnerDTO = iRemoteCargoOwnerClient.queryByCode(entity.getCargoCode());
                if (!Objects.isNull(cargoOwnerDTO)) {
                    entity.setCargoName(cargoOwnerDTO.getName());
                } else {
                    entity.setCargoName("");
                }
                entity.setLengthWidthHeightFormat(decimalPlaceClient.getLengthWidthHeightFormat(entity.getWarehouseCode(), entity.getCargoCode()));
                entity.setWeightFormat(decimalPlaceClient.getWeightFormat(entity.getWarehouseCode(), entity.getCargoCode()));
                entity.setVolumeFormat(decimalPlaceClient.getVolumeFormat(entity.getWarehouseCode(), entity.getCargoCode()));
                entity.setNumberFormat(decimalPlaceClient.getNumberFormat(entity.getWarehouseCode(), entity.getCargoCode()));

                if (!StringUtils.isEmpty(entity.getSkuLotNo())) {
                    SkuLotParam skuLotParam = new SkuLotParam();
                    skuLotParam.setSkuCode(entity.getSkuCode());
                    skuLotParam.setCargoCode(entity.getCargoCode());
                    skuLotParam.setCode(entity.getSkuLotNo());
                    SkuLotDTO skuLotDTO = remoteSkuLotClient.get(skuLotParam);
                    if (!ObjectUtils.isEmpty(skuLotDTO)) {
                        entity.setExternalSkuLotNo(skuLotDTO.getExternalSkuLotNo());
                        entity.setExternalLinkBillNo(skuLotDTO.getExternalLinkBillNo());
                        entity.setPalletCode(skuLotDTO.getPalletCode());
                        entity.setBoxCode(skuLotDTO.getBoxCode());
                    }
                }

                return entity;
            }).collect(Collectors.toList());
            dataBizDTO.setDetailDTOList(detailBizDTOS);
        }
        return dataBizDTO;
    }

    @Override
    public Boolean cancelReceiptAndStock(CodeParam param) {
        String recId = param.getCode();
        String lockKey = StrUtil.join(StrUtil.COLON, CurrentRouteHolder.getWarehouseCode(), ReceiptDTO.class.getSimpleName(), recId);
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(1, 30, TimeUnit.SECONDS);
            if (locked) {
                ReceiptDTO receiptDTO = iRemoteReceiptClient.queryReceiptByRecId(recId);
                if (ObjectUtil.isEmpty(receiptDTO)) {
                    throw new BaseException(BaseBizEnum.TIP, "收货作业批次不存在！receiptDTO：" + receiptDTO);
                }
                if (!Objects.equals(receiptDTO.getStatus(), ReceiptStatusEnum.WAIT_SHELF.code())) {
                    throw new BaseException(BaseBizEnum.TIP, "非待上架不能取消收货作业批次");
                }
                ReceiptDetailParam receiptDetailParam = new ReceiptDetailParam();
                receiptDetailParam.setRecId(recId);
                List<ReceiptDetailDTO> receiptDetailDTOList = remoteReceiptDetailClient.getList(receiptDetailParam);
                if (CollectionUtil.isEmpty(receiptDetailDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, "收货作业批次明细不存在！recId：" + recId);
                }
                // TODO ----------------退货入库取消特殊------------------start-----------
                if (receiptDTO.getType().equalsIgnoreCase(AsnTypeEnum.RETURN.getCode())) {
                    ReceiptAndContCancelReturnReceiptBO receiptAndContCancelReturnReceiptBO = new ReceiptAndContCancelReturnReceiptBO();
                    List<String> contCodes = new ArrayList<>();
                    // 取消收货作业批次、封装库存操作参数
                    receiptDTO.setStatus(ReceiptStatusEnum.CANCLE_SHELF.getCode());
                    contCodes.add(receiptDTO.getContCode());

                    // 取消收货作业批次明细
                    receiptDetailDTOList.forEach(receiptDetailDTO -> {
                        receiptDetailDTO.setStatus(ReceiptStatusEnum.CANCLE_SHELF.getCode());
                    });

                    List<ContainerDTO> containerDTOS = new ArrayList<>();
                    List<ContainerLogDTO> containerLogDTOS = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(contCodes)) {
                        ContainerParam containerParam = new ContainerParam();
                        containerParam.setCodeList(contCodes);
                        containerDTOS = remoteContainerClient.getList(containerParam);
                        containerDTOS.forEach(containerDTO -> {
                            // 记录容器释放日志
                            ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                            BeanUtils.copyProperties(containerDTO, containerLogDTO);
                            containerLogDTO.setId(null);
                            containerLogDTO.setContCode(containerDTO.getCode());
                            containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                            containerLogDTO.setOpContent(String.format("取消正常收货,绑定单号%s,释放容器:%s", containerDTO.getOccupyNo(), containerDTO.getCode()));
                            containerLogDTO.setOpDate(System.currentTimeMillis());
                            containerLogDTOS.add(containerLogDTO);

                            // 释放容器
                            containerDTO.setOccupyNo("");
                            containerDTO.setOccupyType("");
                            containerDTO.setRemark("");
                            containerDTO.setStatus(ContainerStatusEnum.ENABLE.getValue());
                        });
                    }

                    // 入库单回滚到创建状态 asn明细会写数量削减
                    AsnParam asnParam = new AsnParam();
                    asnParam.setAsnIdList(receiptDetailDTOList.stream().map(ReceiptDetailDTO::getAsnId).collect(Collectors.toList()));
                    List<AsnDTO> asnDTOList = iRemoteAsnClient.getList(asnParam);
                    if (CollectionUtils.isEmpty(asnDTOList)) {
                        throw new BaseException(BaseBizEnum.TIP, "入库单不存在");
                    }
                    List<AsnDetailDTO> asnDetailDTOList = iRemoteAsnClient.getDetailList(asnParam);
                    if (CollectionUtil.isEmpty(asnDetailDTOList)) {
                        throw new BaseException(BaseBizEnum.TIP, "入库单明细不存在");
                    }
                    List<AsnLogDTO> asnLogDTOList = new ArrayList<>();
                    for (AsnDTO asnDTO : asnDTOList) {
                        List<AsnDetailDTO> detailDTOList = asnDetailDTOList.stream().filter(a -> a.getAsnId().equalsIgnoreCase(asnDTO.getAsnId())).collect(Collectors.toList());
                        detailDTOList.forEach(asnDetailDTO -> {
                            BigDecimal reduceQty = receiptDetailDTOList.stream()
                                    .filter(a -> a.getAsnId().equalsIgnoreCase(asnDetailDTO.getAsnId()))
                                    .filter(a -> a.getSkuCode().equalsIgnoreCase(asnDetailDTO.getSkuCode()))
                                    .filter(a -> a.getPUid().equals(asnDetailDTO.getId()))
                                    .map(ReceiptDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                            if (reduceQty.compareTo(BigDecimal.ZERO) > 0) {
                                asnDetailDTO.setRecSkuQty(asnDetailDTO.getRecSkuQty().subtract(reduceQty));
                                //超收
                                if (asnDetailDTO.getRecSkuQty().compareTo(BigDecimal.ZERO) < 0) {
                                    asnDetailDTO.setRecSkuQty(BigDecimal.ZERO);
                                }
                            }
                        });
                        asnDTO.setRecSkuQty(detailDTOList.stream().map(AsnDetailDTO::getRecSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        asnDTO.setRecSkuType((int) detailDTOList.stream().map(AsnDetailDTO::getSkuCode).distinct().count());
                        // 记录入库单日志
                        AsnLogDTO asnLogDTO = new AsnLogDTO();
                        BeanUtils.copyProperties(asnDTO, asnLogDTO);
                        asnLogDTO.setMsg("取消正常收货记录：" + recId);
                        asnLogDTO.setOpBy(CurrentUserHolder.getUserName());
                        asnLogDTOList.add(asnLogDTO);
                    }
                    //取消上架单
                    ShelfParam shelfParam = new ShelfParam();
                    shelfParam.setBillNoList(Arrays.asList(recId));
                    List<ShelfDTO> shelfDTOS = remoteShelfClient.getList(shelfParam);
                    if (CollectionUtil.isNotEmpty(shelfDTOS)) {
                        ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
                        shelfDetailParam.setShelfCodeList(shelfDTOS.stream().map(ShelfDTO::getCode).collect(Collectors.toList()));
                        List<ShelfDetailDTO> shelfDetailList = remoteShelfClient.getShelfDetailList(shelfDetailParam);
                        for (ShelfDetailDTO shelfDetailDTO : shelfDetailList) {
                            shelfDetailDTO.setStatus(ShelfStatusEnum.STATUS_CANCEL.getStatus());
                        }
                        receiptAndContCancelReturnReceiptBO.setShelfDetailDTOList(shelfDetailList);
                    }
                    for (ShelfDTO shelfDTO : shelfDTOS) {
                        shelfDTO.setStatus(ShelfStatusEnum.STATUS_CANCEL.getStatus());
                    }
                    //待处理数据
                    receiptAndContCancelReturnReceiptBO.setShelfDTOList(shelfDTOS);
                    receiptAndContCancelReturnReceiptBO.setAsnDTOList(asnDTOList);
                    receiptAndContCancelReturnReceiptBO.setAsnLogDTOList(asnLogDTOList);
                    receiptAndContCancelReturnReceiptBO.setAsnDetailDTOList(asnDetailDTOList);
                    receiptAndContCancelReturnReceiptBO.setContainerDTOList(containerDTOS);
                    receiptAndContCancelReturnReceiptBO.setContainerLogDTOList(containerLogDTOS);
                    receiptAndContCancelReturnReceiptBO.setReceiptDTO(receiptDTO);
                    receiptAndContCancelReturnReceiptBO.setReceiptDetailDTOList(receiptDetailDTOList);

                    receiptGtsService.receiptAndContAndStockCancelToReturn(receiptAndContCancelReturnReceiptBO);
                    return true;
                }
                // TODO ----------------退货入库取消特殊---------------------end--------
                //取消bo
                ReceiptAndContCancelReceiptBO receiptAndContCancelReceiptBO = new ReceiptAndContCancelReceiptBO();

                List<String> contCodes = new ArrayList<>();
                // 取消收货作业批次、封装库存操作参数
                receiptDTO.setStatus(ReceiptStatusEnum.CANCLE_SHELF.getCode());
                contCodes.add(receiptDTO.getContCode());

                // 取消收货作业批次明细
                receiptDetailDTOList.forEach(receiptDetailDTO -> {
                    receiptDetailDTO.setStatus(ReceiptStatusEnum.CANCLE_SHELF.getCode());
                });

                List<ContainerDTO> containerDTOS = new ArrayList<>();
                List<ContainerLogDTO> containerLogDTOS = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(contCodes)) {
                    ContainerParam containerParam = new ContainerParam();
                    containerParam.setCodeList(contCodes);
                    containerDTOS = remoteContainerClient.getList(containerParam);
                    containerDTOS.forEach(containerDTO -> {
                        // 记录容器释放日志
                        ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                        BeanUtils.copyProperties(containerDTO, containerLogDTO);
                        containerLogDTO.setId(null);
                        containerLogDTO.setContCode(containerDTO.getCode());
                        containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                        containerLogDTO.setOpContent(String.format("取消正常收货,绑定单号%s,释放容器:%s", containerDTO.getOccupyNo(), containerDTO.getCode()));
                        containerLogDTO.setOpDate(System.currentTimeMillis());
                        containerLogDTOS.add(containerLogDTO);

                        // 释放容器
                        containerDTO.setOccupyNo("");
                        containerDTO.setOccupyType("");
                        containerDTO.setRemark("");
                        containerDTO.setStatus(ContainerStatusEnum.ENABLE.getValue());
                    });
                }

                // 入库单回滚到创建状态 asn明细会写数量削减
                AsnParam asnParam = new AsnParam();
                asnParam.setAsnId(receiptDTO.getAsnId());
                AsnDTO asnDTO = iRemoteAsnClient.get(asnParam);
                if (asnDTO == null) {
                    throw new BaseException(BaseBizEnum.TIP, "入库单不存在");
                }
                List<AsnDetailDTO> asnDetailDTOList = iRemoteAsnClient.getDetailList(asnParam);
                if (CollectionUtil.isEmpty(asnDetailDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, "入库单明细不存在");
                }
                asnDetailDTOList.forEach(asnDetailDTO -> {
                    //淘天 采购入库必有理货报告 理货报告收货与原来有点差异
                    if (iRemoteWarehouseClient.getTaoTianWarehouse(asnDTO.getWarehouseCode()) && Objects.equals(asnDTO.getOutType(), AsnOutTypeEnum.CGRK.getCode())) {
                        BigDecimal reduceQty = receiptDetailDTOList.stream()
                                .filter(a -> a.getSkuCode().equalsIgnoreCase(asnDetailDTO.getSkuCode()))
                                .map(ReceiptDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (reduceQty.compareTo(BigDecimal.ZERO) > 0) {
                            asnDetailDTO.setRecSkuQty(asnDetailDTO.getRecSkuQty().subtract(reduceQty));
                            //超收
                            if (asnDetailDTO.getRecSkuQty().compareTo(BigDecimal.ZERO) < 0) {
                                asnDetailDTO.setRecSkuQty(BigDecimal.ZERO);
                            }
                        }
                    } else {
                        BigDecimal reduceQty = receiptDetailDTOList.stream().filter(a -> a.getSkuCode().equalsIgnoreCase(asnDetailDTO.getSkuCode()))
                                .filter(a -> a.getPUid().equals(asnDetailDTO.getId()))
                                .map(ReceiptDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (reduceQty.compareTo(BigDecimal.ZERO) > 0) {
                            asnDetailDTO.setRecSkuQty(asnDetailDTO.getRecSkuQty().subtract(reduceQty));
                            //超收
                            if (asnDetailDTO.getRecSkuQty().compareTo(BigDecimal.ZERO) < 0) {
                                asnDetailDTO.setRecSkuQty(BigDecimal.ZERO);
                            }
                        }
                    }
                });
                asnDTO.setRecSkuQty(asnDetailDTOList.stream().map(AsnDetailDTO::getRecSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                asnDTO.setRecSkuType((int) asnDetailDTOList.stream().map(AsnDetailDTO::getSkuCode).distinct().count());
                // 记录入库单日志
                AsnLogDTO asnLogDTO = new AsnLogDTO();
                BeanUtils.copyProperties(asnDTO, asnLogDTO);
                asnLogDTO.setMsg("取消正常收货记录：" + recId);
                asnLogDTO.setOpBy(CurrentUserHolder.getUserName());


                ShelfParam shelfParam = new ShelfParam();
                shelfParam.setBillNoList(Arrays.asList(recId));
                List<ShelfDTO> shelfDTOS = remoteShelfClient.getList(shelfParam);
                if (CollectionUtil.isNotEmpty(shelfDTOS)) {
                    ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
                    shelfDetailParam.setShelfCodeList(shelfDTOS.stream().map(ShelfDTO::getCode).collect(Collectors.toList()));
                    List<ShelfDetailDTO> shelfDetailList = remoteShelfClient.getShelfDetailList(shelfDetailParam);
                    for (ShelfDetailDTO shelfDetailDTO : shelfDetailList) {
                        shelfDetailDTO.setStatus(ShelfStatusEnum.STATUS_CANCEL.getStatus());
                    }
                    receiptAndContCancelReceiptBO.setShelfDetailDTOList(shelfDetailList);
                }
                for (ShelfDTO shelfDTO : shelfDTOS) {
                    if (iRemoteWarehouseClient.getTaoTianWarehouse(shelfDTO.getWarehouseCode())
                            && ShelfMarkEnum.NumToEnum(shelfDTO.getMark()).contains(ShelfMarkEnum.AUTH_SHELF)) {
                        throw new BaseException(BaseBizEnum.TIP, "淘天存在上架申请通过的上架单,不允许取消");
                    }
                    shelfDTO.setStatus(ShelfStatusEnum.STATUS_CANCEL.getStatus());
                }
                receiptAndContCancelReceiptBO.setShelfDTOList(shelfDTOS);

                receiptAndContCancelReceiptBO.setAsnDTO(asnDTO);
                receiptAndContCancelReceiptBO.setAsnLogDTO(asnLogDTO);
                receiptAndContCancelReceiptBO.setAsnDetailDTOList(asnDetailDTOList);
                receiptAndContCancelReceiptBO.setContainerDTOList(containerDTOS);
                receiptAndContCancelReceiptBO.setContainerLogDTOList(containerLogDTOS);
                receiptAndContCancelReceiptBO.setReceiptDTO(receiptDTO);
                receiptAndContCancelReceiptBO.setReceiptDetailDTOList(receiptDetailDTOList);

                receiptGtsService.receiptAndContAndStockCancel(receiptAndContCancelReceiptBO);
            } else {
                log.info("{}:收货作业批次取消收货锁竞争失败");
            }
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
        return true;
    }


}
