package com.dt.platform.wms.biz.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/3 9:47
 */
@Data
@Component
@ConfigurationProperties(prefix = "dt-wms.config.urls")
@RefreshScope
public class UrlConfig  implements java.io.Serializable  {

    @ApiModelProperty("一线入境企业微信预警链接")
    private List<String> entranceWeChatWarningUrls;

    @ApiModelProperty("分拣设备报告拦截包裹出库预警链接")
    private List<String> wcsInterceptPackageOutUrls;

    @ApiModelProperty("出库包耗材统计提示")
    private List<String> outStockUpcWeChatInfoUrls;

    @ApiModelProperty("同一包裹结构使用多种包材企业微信预警链接")
    private List<String> structMaterialWarningUrls;

    @ApiModelProperty("结转单异常告警通知【不同仓库配置不同的预警链接】")
    private Map<String, String> carryoverWarningUrlMap;
    @ApiModelProperty("结转单异常告警通知人【不同仓库配置不同的预警链接】")
    private Map<String, String> carryoverWarningMobile;

    @ApiModelProperty("异常登记生成批次信息失败告警")
    private List<String> opExceptionSkuLotGenFailUrl;
    @ApiModelProperty("多货无原单信息告警")
    private List<String> rsExtraNoOriginUrls;
    @ApiModelProperty("数据中台地址")
    private String dataCenterUrl;
}