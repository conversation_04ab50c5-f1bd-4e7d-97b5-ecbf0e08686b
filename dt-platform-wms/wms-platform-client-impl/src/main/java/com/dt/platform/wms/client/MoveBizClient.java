package com.dt.platform.wms.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.exception.BusinessException;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.base.LocationStatusEnum;
import com.dt.component.common.enums.base.OpTypeEnum;
import com.dt.component.common.enums.base.ZoneStatusEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.move.MoveStatusEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.enums.stock.RealOrUnrealGoodsEnum;
import com.dt.component.common.enums.wms.WmsMoveBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.exceptions.WmsBizException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.param.*;
import com.dt.domain.bill.dto.MoveDTO;
import com.dt.domain.bill.dto.MoveDetailDTO;
import com.dt.domain.bill.param.MoveDetailParam;
import com.dt.domain.bill.param.MoveParam;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.biz.ILocationBiz;
import com.dt.platform.wms.biz.IMixRuleCheckBiz;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.stock.biz.bo.MoveStockBO;
import com.dt.platform.wms.dto.move.MoveBizDTO;
import com.dt.platform.wms.dto.move.MoveDetailBizDTO;
import com.dt.platform.wms.dto.stock.StockLocationWithLotBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.move.*;
import com.dt.platform.wms.transaction.IMoveGtsService;
import com.dt.platform.wms.transaction.bo.MoveCompleteDetailBO;
import com.dt.platform.wms.transaction.bo.MoveCompleteMultiDetailBO;
import com.dt.platform.wms.transaction.bo.MoveWholeLocationBO;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalLock;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
public class MoveBizClient implements IMoveBizClient {

    @Resource
    private WmsOtherConfig wmsOtherConfig;

    @Resource
    private IRemoteMoveClient remoteMoveClient;

    @Resource
    private IMoveGtsService moveContextService;

    @Resource
    private IMixRuleCheckBiz mixRuleCheckBiz;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteZoneClient remoteZoneClient;

    @Resource
    private IRemoteLocationClient remoteLocationClient;

    @Resource
    private IRemoteTunnelClient remoteTunnelClient;

    @Resource
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private IRemoteMixRuleClient remoteMixRuleClient;

    @Resource
    private ILocationBiz locationBiz;

//    @Override
//    public Result<Boolean> create(MoveBizParam param) {
//
//        List<String> originLocationCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getOriginLocationCode())).collect(Collectors.toList());
//        List<String> targetLocationCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getTargetLocationCode())).collect(Collectors.toList());
//        List<String> skuCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toList());
//        List<String> skuLotNoList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuLotNo())).collect(Collectors.toList());
//
//        List<String> cargoCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getCargoCode())).distinct().collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(cargoCodeList) || cargoCodeList.size() != 1) {
//            throw new BaseException(BaseBizEnum.TIP, "移位单不允许混货主移位！");
//        }
//        //查询批次ID
//        SkuLotParam skuLotParam = new SkuLotParam();
//        skuLotParam.setCargoCode(param.getDetailList().get(0).getCargoCode());
//        skuLotParam.setCodeList(skuLotNoList);
//        List<SkuLotDTO> skuLotList = remoteSkuLotClient.getList(skuLotParam);
//        if (CollectionUtils.isEmpty(originLocationCodeList) || CollectionUtils.isEmpty(targetLocationCodeList)) {
//            throw new BaseException(BaseBizEnum.TIP, "目标库位和来源库位不能为空！");
//        }
//
//
//        LocationParam locationParam = new LocationParam();
//        locationParam.setCodeList(new ArrayList<>());
//        locationParam.getCodeList().addAll(originLocationCodeList);
//        locationParam.getCodeList().addAll(targetLocationCodeList);
//        List<LocationDTO> locationList = remoteLocationClient.getList(locationParam);
//        if (CollectionUtils.isEmpty(locationList)) {
//            throw new BaseException(BaseBizEnum.TIP, "目标库位信息不存在");
//        }
//        //校验库位的状态
//        List<ZoneDTO> zoneList = checkLocationStatus(locationList);
//
//        List<ZoneTypeEnum> allowMoveZoneTypeList = new ArrayList<>();
//        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_STORE);
//        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PICK);
//
//        StockLocationParam stockLocationParam = new StockLocationParam();
//        stockLocationParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//        stockLocationParam.setCargoCode(param.getDetailList().get(0).getCargoCode());
//        stockLocationParam.setLocationCodeList(originLocationCodeList);
//        stockLocationParam.setSkuCodeList(skuCodeList);
//        stockLocationParam.setSkuLotNoList(skuLotNoList);
//        List<StockLocationDTO> stockLocationList = remoteStockLocationClient.getList(stockLocationParam);
//        MoveParam bizParam = new MoveParam();
//        bizParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//        bizParam.setCargoCode(param.getDetailList().get(0).getCargoCode());
//        bizParam.setOpType(OpTypeEnum.OP_TYPE_PAPER.getType());
//        bizParam.setDetailList(ConverterUtil.convertList(param.getDetailList(), MoveDetailDTO.class));
//        bizParam.setCode(remoteSeqRuleClient.findSequence(SeqEnum.MOVE_CODE_000001));
//        bizParam.setExpSkuType(param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList()).size());
//        bizParam.setExpSkuQty(new BigDecimal("0.000"));
//        bizParam.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());
//        bizParam.setDetailList(ConverterUtil.convertList(param.getDetailList(), MoveDetailDTO.class));
//        for (MoveDetailDTO detail : bizParam.getDetailList()) {
//            if (detail.getOriginLocationCode().equals(detail.getTargetLocationCode())) {
//                throw new BaseException(BaseBizEnum.TIP, "来源库位与目标库位不能一致");
//            }
//            LocationDTO originLocation = locationList.stream().filter(a -> a.getCode().equals(detail.getOriginLocationCode())).findAny().orElse(null);
//            LocationDTO targetLocation = locationList.stream().filter(a -> a.getCode().equals(detail.getTargetLocationCode())).findAny().orElse(null);
//            if (ObjectUtils.isEmpty(targetLocation)) {
//                throw new BaseException(BaseBizEnum.TIP, "目标库位不存在！");
//            }
//            ZoneDTO originZone = zoneList.stream().filter(a -> a.getCode().equals(originLocation.getZoneCode())).findAny().orElse(null);
//            ZoneDTO targetZone = zoneList.stream().filter(a -> a.getCode().equals(targetLocation.getZoneCode())).findAny().orElse(null);
//            if (ObjectUtils.isEmpty(originLocation) || ObjectUtils.isEmpty(targetLocation) || ObjectUtils.isEmpty(originZone) || ObjectUtils.isEmpty(targetZone)) {
//                throw new BaseException(BaseBizEnum.TIP, "库区库位信息不正确！");
//            }
//            if (ZoneStatusEnum.STATUS_DISABLED.getStatus().equals(targetZone.getStatus())) {
//                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库区:%s未启用"),
//                        detail.getSkuCode(), detail.getSkuLotNo(), targetLocation.getZoneCode());
//            }
//            if (LocationStatusEnum.STATUS_DISABLED.getStatus().equals(targetLocation.getStatus())) {
//                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库位:%s未启用"),
//                        detail.getSkuCode(), detail.getSkuLotNo(), targetLocation.getCode());
//            }
//            if (!targetZone.getSkuQuality().equals(originZone.getSkuQuality())) {
//                throw new BaseException(BaseBizEnum.TIP, String.format("目标库位%s库区商品属性与来源库位%s库区商品属性不一致", targetLocation.getCode(), originLocation.getCode()));
//            }
//            //移位限制暂存区
//            ZoneTypeEnum originZoneType = ZoneTypeEnum.getEnum(originZone.getType());
//            ZoneTypeEnum targetZoneType = ZoneTypeEnum.getEnum(targetZone.getType());
//            if (!allowMoveZoneTypeList.contains(originZoneType) || !allowMoveZoneTypeList.contains(targetZoneType)) {
//                throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_ORIGIN_STOCK_NOT_ALLOW_TEMP);
//            }
//            SkuLotDTO skuLot = skuLotList.stream()
//                    .filter(a -> a.getCargoCode().equals(detail.getCargoCode()))
//                    .filter(a -> a.getSkuCode().equals(detail.getSkuCode()))
//                    .filter(a -> a.getCode().equals(detail.getSkuLotNo())).findAny()
//                    .orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s不存在", detail.getSkuCode(), detail.getSkuLotNo())));
//            StockLocationDTO stockLocation = stockLocationList.stream()
//                    .filter(a -> a.getLocationCode().equals(detail.getOriginLocationCode()))
//                    .filter(a -> a.getCargoCode().equals(detail.getCargoCode()))
//                    .filter(a -> a.getSkuCode().equals(detail.getSkuCode()))
//                    .filter(a -> a.getSkuLotNo().equals(detail.getSkuLotNo()))
//                    .filter(a -> a.getAvailableQty().compareTo(BigDecimal.ZERO) > 0)
//                    .findAny()
//                    .orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("来源库位:%s商品:%s批次:%s库存信息不存在", detail.getOriginLocationCode(), detail.getSkuCode(), detail.getSkuLotNo())));
//            //移位单
//            bizParam.setExpSkuQty(bizParam.getExpSkuQty().add(detail.getExpSkuQty()));
//            //明细
//            detail.setSkuQuality(skuLot.getSkuQuality());
//            detail.setMoveCode(bizParam.getCode());
//            detail.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//            detail.setCargoCode(param.getDetailList().get(0).getCargoCode());
//            detail.setOriginZoneCode(originZone.getCode());
//            detail.setOriginZoneType(originZone.getType());
//            detail.setTargetZoneCode(targetZone.getCode());
//            detail.setTargetZoneType(targetZone.getType());
//            detail.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());
//            if (StorageRuleEnum.STORAGE_SKU_1.getRule().equals(targetLocation.getStorageRule())) {
//                //提交数据一位一品校验
//                List<String> storageCheckList = bizParam.getDetailList()
//                        .stream()
//                        .filter(a -> a.getTargetLocationCode().equals(targetLocation.getCode()))
//                        .flatMap(a -> Stream.of(a.getSkuCode()))
//                        .distinct()
//                        .collect(Collectors.toList());
//                if (storageCheckList.size() > 1 || !storageCheckList.contains(detail.getSkuCode())) {
//                    throw new BaseException(BaseBizEnum.TIP, "一位一品库位不允许存放多个商品");
//                }
//
//                //一位一品
//                storageRuleCheckBiz.checkLocationStorageRule(targetLocation, detail.getSkuCode());
//            }
//            //混放批次
//            mixRuleCheckBiz.checkLocationMixRule(detail.getTargetLocationCode(), targetLocation.getMixRuleCode(), detail.getCargoCode(),
//                    detail.getSkuCode(), detail.getSkuLotNo());
//        }
//
//        Boolean result = remoteMoveClient.save(bizParam);
//        return Result.success(result);
//    }

    /**
     * 功能描述:校验库位的状态和父级是否为禁用状态
     * 创建时间:  2020/12/17 8:19 下午
     *
     * @param locationList:
     * @return java.util.List<com.dt.domain.base.dto.ZoneDTO>
     * <AUTHOR>
     */
    private List<ZoneDTO> checkLocationStatus(List<LocationDTO> locationList) {

        remoteLocationClient.checkStatus(locationList);
        //巷道
        TunnelParam targetTunnelParam = new TunnelParam();
        targetTunnelParam.setCodeList(locationList.stream().flatMap(a -> Stream.of(a.getTunnelCode())).distinct().collect(Collectors.toList()));
        List<TunnelDTO> targetTunnelDTOList = remoteTunnelClient.getList(targetTunnelParam);
        if (CollectionUtils.isEmpty(targetTunnelDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "目标巷道信息不存在");
        }
        //校验巷道的状态
        remoteTunnelClient.checkStatus(targetTunnelDTOList);

        //库区
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCodeList(locationList.stream().flatMap(a -> Stream.of(a.getZoneCode())).collect(Collectors.toList()));
        List<ZoneDTO> zoneList = remoteZoneClient.getList(zoneParam);
        if (CollectionUtils.isEmpty(zoneList)) {
            throw new BaseException(BaseBizEnum.TIP, "目标库区信息不存在");
        }
        //校验库区的状态
        remoteZoneClient.checkStatus(zoneList);
        return zoneList;
    }

//    @Override
//    public Result<Boolean> modify(MoveBizParam param) {
//        if (ObjectUtils.isEmpty(param.getId()) && ObjectUtils.isEmpty(param.getCode())) {
//            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
//        }
//        MoveParam moveParam = new MoveParam();
//        moveParam.setId(param.getId());
//        moveParam.setCode(param.getCode());
//        MoveDTO move = remoteMoveClient.get(moveParam);
//        if (ObjectUtils.isEmpty(move)) {
//            throw new WmsBizException(BaseBizEnum.DATA_ERROR);
//        }
//        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(move.getCargoCode());
//
//        List<String> originLocationCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getOriginLocationCode())).collect(Collectors.toList());
//        List<String> targetLocationCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getTargetLocationCode())).collect(Collectors.toList());
//        List<String> skuCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toList());
//        List<String> skuLotNoList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuLotNo())).collect(Collectors.toList());
//
//        List<String> cargoCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getCargoCode())).distinct().collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(cargoCodeList) || cargoCodeList.size() != 1) {
//            throw new BaseException(BaseBizEnum.TIP, "移位单不允许混货主移位！");
//        }
//
//        //查询批次ID
//        SkuLotParam skuLotParam = new SkuLotParam();
//        skuLotParam.setCargoCode(move.getCargoCode());
//        skuLotParam.setCodeList(skuLotNoList);
//        List<SkuLotDTO> skuLotList = remoteSkuLotClient.getList(skuLotParam);
//
//        LocationParam locationParam = new LocationParam();
//        locationParam.setCodeList(new ArrayList<>());
//        locationParam.getCodeList().addAll(originLocationCodeList);
//        locationParam.getCodeList().addAll(targetLocationCodeList);
//        List<LocationDTO> locationList = remoteLocationClient.getList(locationParam);
//
//        ZoneParam zoneParam = new ZoneParam();
//        zoneParam.setCodeList(locationList.stream().flatMap(a -> Stream.of(a.getZoneCode())).collect(Collectors.toList()));
//        List<ZoneDTO> zoneList = remoteZoneClient.getList(zoneParam);
//
//        StockLocationParam stockLocationParam = new StockLocationParam();
//        stockLocationParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//        stockLocationParam.setCargoCode(move.getCargoCode());
//        stockLocationParam.setLocationCodeList(originLocationCodeList);
//        stockLocationParam.setSkuCodeList(skuCodeList);
//        stockLocationParam.setSkuLotNoList(skuLotNoList);
//        List<StockLocationDTO> stockLocationList = remoteStockLocationClient.getList(stockLocationParam);
//
//        List<ZoneTypeEnum> allowMoveZoneTypeList = new ArrayList<>();
//        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_STORE);
//        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PICK);
//
//
//        MoveParam bizParam = ConverterUtil.convert(param, MoveParam.class);
//        if (ObjectUtils.isEmpty(bizParam)) {
//            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
//        }
//        bizParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//        bizParam.setId(move.getId());
//        bizParam.setExpSkuType(param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList()).size());
//        bizParam.setExpSkuQty(new BigDecimal("0.000"));
//        bizParam.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());
//        bizParam.setDetailList(ConverterUtil.convertList(param.getDetailList(), MoveDetailDTO.class));
//
//        for (MoveDetailDTO detail : bizParam.getDetailList()) {
//            if (detail.getOriginLocationCode().equals(detail.getTargetLocationCode())) {
//                throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
//            }
//            LocationDTO originLocation = locationList.stream().filter(a -> a.getCode().equals(detail.getOriginLocationCode())).findAny().get();
//            LocationDTO targetLocation = locationList.stream().filter(a -> a.getCode().equals(detail.getTargetLocationCode())).findAny().get();
//            ZoneDTO originZone = zoneList.stream().filter(a -> a.getCode().equals(originLocation.getZoneCode())).findAny().get();
//            ZoneDTO targetZone = zoneList.stream().filter(a -> a.getCode().equals(targetLocation.getZoneCode())).findAny().get();
//            //移位限制暂存区
//            ZoneTypeEnum originZoneType = ZoneTypeEnum.getEnum(originZone.getType());
//            ZoneTypeEnum targetZoneType = ZoneTypeEnum.getEnum(targetZone.getType());
//            if (!allowMoveZoneTypeList.contains(originZoneType) || !allowMoveZoneTypeList.contains(targetZoneType)) {
//                throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_ORIGIN_STOCK_NOT_ALLOW_TEMP);
//            }
//            if (ZoneStatusEnum.STATUS_DISABLED.getStatus().equals(targetZone.getStatus())) {
//                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库区:%s未启用",
//                        detail.getSkuCode(), detail.getSkuLotNo(), targetLocation.getZoneCode()));
//            }
//            if (LocationStatusEnum.STATUS_DISABLED.getStatus().equals(targetLocation.getStatus())) {
//                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库位:%s未启用",
//                        detail.getSkuCode(), detail.getSkuLotNo(), targetLocation.getCode()));
//            }
//
//            SkuLotDTO skuLot = skuLotList.stream()
//                    .filter(a -> a.getCargoCode().equals(detail.getCargoCode()))
//                    .filter(a -> a.getSkuCode().equals(detail.getSkuCode()))
//                    .filter(a -> a.getCode().equals(detail.getSkuLotNo())).findAny()
//                    .orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s不存在", detail.getSkuCode(), detail.getSkuLotNo())));
//            StockLocationDTO stockLocation = stockLocationList.stream()
//                    .filter(a -> a.getLocationCode().equals(detail.getOriginLocationCode()))
//                    .filter(a -> a.getCargoCode().equals(detail.getCargoCode()))
//                    .filter(a -> a.getSkuCode().equals(detail.getSkuCode()))
//                    .filter(a -> a.getSkuLotNo().equals(detail.getSkuLotNo()))
//                    .filter(a -> a.getAvailableQty().compareTo(BigDecimal.ZERO) > 0)
//                    .findAny()
//                    .orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("来源库位:%s商品:%s批次:%s库存信息不存在", detail.getOriginLocationCode(), detail.getSkuCode(), detail.getSkuLotNo())));
//
//
//            //移位单
//            bizParam.setExpSkuQty(bizParam.getExpSkuQty().add(detail.getExpSkuQty()));
//
//            //明细
//            detail.setSkuQuality(skuLot.getSkuQuality());
//            detail.setMoveCode(bizParam.getCode());
//            detail.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
//            detail.setCargoCode(param.getDetailList().get(0).getCargoCode());
//            detail.setOriginZoneCode(originZone.getCode());
//            detail.setOriginZoneType(originZone.getType());
//            detail.setTargetZoneCode(targetZone.getCode());
//            detail.setTargetZoneType(targetZone.getType());
//            detail.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());
//
//            if (StorageRuleEnum.STORAGE_SKU_1.getRule().equals(targetLocation.getStorageRule())) {
//                //提交数据一位一品校验
//                List<String> storageCheckList = bizParam.getDetailList()
//                        .stream()
//                        .filter(a -> a.getTargetLocationCode().equals(targetLocation.getCode()))
//                        .flatMap(a -> Stream.of(a.getSkuCode()))
//                        .distinct()
//                        .collect(Collectors.toList());
//                if (storageCheckList.size() > 1 || !storageCheckList.contains(detail.getSkuCode())) {
//                    throw new BaseException(BaseBizEnum.TIP, "一位一品库位不允许存放多个商品");
//                }
//
//                //一位一品
//                storageRuleCheckBiz.checkLocationStorageRule(targetLocation, detail.getSkuCode());
//            }
//
//            //混放批次
//            mixRuleCheckBiz.checkLocationMixRule(detail.getTargetLocationCode(), targetLocation.getMixRuleCode(), detail.getCargoCode(),
//                    detail.getSkuCode(), detail.getSkuLotNo());
//        }
//
//        Boolean result = remoteMoveClient.modify(bizParam);
//        return Result.success(result);
//    }

    @Override
    public Result<Boolean> modifyOpType(MoveOpTypeParam param) {
        MoveParam moveParam = new MoveParam();
        moveParam.setCode(param.getCode());
        moveParam.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());
        MoveDTO move = remoteMoveClient.get(moveParam);
        if (ObjectUtils.isEmpty(move)) {
            throw new BaseException(BaseBizEnum.TIP, "该移位单不允许修改操作方式!");
        }
        move.setOpType(param.getOpType());

        remoteMoveClient.modify(ConverterUtil.convert(move, MoveParam.class));

        return Result.success(true);
    }

    @Override
    public Result<MoveBizDTO> getDetail(MoveBizParam param) {
        MoveParam bizParam = ConverterUtil.convert(param, MoveParam.class);
        MoveDTO result = remoteMoveClient.getDetail(bizParam);
        MoveBizDTO moveBiz = ConverterUtil.convert(result, MoveBizDTO.class);
        if (!ObjectUtils.isEmpty(moveBiz)) {
            WarehouseDTO warehouse = remoteWarehouseClient.queryByCode(moveBiz.getWarehouseCode());
            CargoOwnerDTO cargoOwner = remoteCargoOwnerClient.queryByCode(moveBiz.getCargoCode());
            moveBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
            moveBiz.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
            moveBiz.setDetailList(ConverterUtil.convertList(result.getDetailList(), MoveDetailBizDTO.class));

            if (!CollectionUtils.isEmpty(moveBiz.getDetailList())) {
                for (MoveDetailBizDTO shelfDetailBiz : moveBiz.getDetailList()) {
                    shelfDetailBiz.setWarehouseName(warehouse.getName());
                    shelfDetailBiz.setCargoName(cargoOwner.getName());
                    SkuUpcParam upcParam = new SkuUpcParam();
                    upcParam.setSkuCode(shelfDetailBiz.getSkuCode());
                    upcParam.setIsDefault(1);
                    upcParam.setCargoCode(result.getCargoCode());
                    SkuUpcDTO skuUpcDTO = remoteSkuClient.getSkuUpc(upcParam);
                    if (skuUpcDTO != null) {
                        shelfDetailBiz.setSkuUpcCode(skuUpcDTO.getUpcCode());
                    } else {
                        shelfDetailBiz.setSkuUpcCode("");
                    }
                    //设置  @ApiModelProperty(value = "当前可用数量")
                    //    private BigDecimal availableQty;
                    StockLocationParam stockLocationParam = new StockLocationParam();
                    stockLocationParam.setSkuCode(shelfDetailBiz.getSkuCode());
                    stockLocationParam.setSkuLotNo(shelfDetailBiz.getSkuLotNo());
                    stockLocationParam.setLocationCode(shelfDetailBiz.getOriginLocationCode());
                    stockLocationParam.setCargoCode(shelfDetailBiz.getCargoCode());
                    StockLocationDTO stockLocationDTO = remoteStockLocationClient.get(stockLocationParam);
                    if (stockLocationDTO == null) {
                        shelfDetailBiz.setAvailableQty(BigDecimal.ZERO);
                    } else {
                        shelfDetailBiz.setAvailableQty(stockLocationDTO.getAvailableQty());
                    }
                }
            }
        }

        return Result.success(moveBiz);
    }

    @Override
    public Result<Page<MoveBizDTO>> getPage(MoveBizParam param) {

        //格式化参数
        formatParam(param);

        MoveParam bizParam = ConverterUtil.convert(param, MoveParam.class);
        Page<MoveDTO> page = remoteMoveClient.getPage(bizParam);
        List<MoveDTO> resultList = page.getRecords();
        List<MoveBizDTO> resultBizList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultList)) {
            List<String> warehouseCodeList = page.getRecords()
                    .stream()
                    .flatMap(a -> Stream.of(a.getWarehouseCode()))
                    .distinct()
                    .collect(Collectors.toList());
            WarehouseParam warehouseParam = new WarehouseParam();
            warehouseParam.setCodeList(warehouseCodeList);
            List<WarehouseDTO> warehouseList = remoteWarehouseClient.queryList(warehouseParam);

            List<String> cargoCodeList = page.getRecords()
                    .stream()
                    .flatMap(a -> Stream.of(a.getCargoCode()))
                    .distinct()
                    .collect(Collectors.toList());
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(cargoCodeList);
            List<CargoOwnerDTO> cargoOwnerList = remoteCargoOwnerClient.queryList(cargoOwnerParam);

            resultBizList = resultList.stream().flatMap(a -> {
                WarehouseDTO warehouse = warehouseList.stream()
                        .filter(b -> b.getCode().equals(a.getWarehouseCode()))
                        .findAny()
                        .orElse(null);
                CargoOwnerDTO cargoOwner = cargoOwnerList.stream()
                        .filter(b -> b.getCode().equals(a.getCargoCode()))
                        .findAny().orElse(null);
                MoveBizDTO moveBiz = ConverterUtil.convert(a, MoveBizDTO.class);
                if (!ObjectUtils.isEmpty(moveBiz)) {
                    moveBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
                    moveBiz.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
                    moveBiz.setDetailList(ConverterUtil.convertList(moveBiz.getDetailList(), MoveDetailBizDTO.class));
                }
                return Stream.of(moveBiz);
            }).collect(Collectors.toList());
        }
        Page<MoveBizDTO> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(resultBizList);
        return Result.success(result);
    }


    @Override
    public Result<Boolean> complete(CodeParam param) {
        MoveParam moveParam = new MoveParam();
        moveParam.setCode(param.getCode());
        MoveDTO move = remoteMoveClient.getDetail(moveParam);
        if (!MoveStatusEnum.STATUS_DOING.getStatus().equals(move.getStatus())) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_STATUS_ERROR);
        }
        List<MoveDetailDTO> detailList = move.getDetailList();
        for (MoveDetailDTO detail : detailList) {
            if (!MoveStatusEnum.STATUS_COMPLETED.getStatus().equals(detail.getStatus())) {
                throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_STATUS_DETAIL_ERROR);
            }
        }
        moveParam.setStatus(MoveStatusEnum.STATUS_COMPLETED.getStatus());

        Boolean result = remoteMoveClient.modify(moveParam);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> completeRetainDetail(CodeParam param) {
        if (ObjectUtil.isEmpty(param)) {
            throw new BaseException(BaseBizEnum.NULL_ARGUMENT);
        }
        String moveCode = param.getCode();
        MoveParam moveParam = new MoveParam();
        moveParam.setCode(moveCode);
        MoveDTO moveDTO = remoteMoveClient.get(moveParam);
        if (null == moveDTO) {
            throw new BaseException(BaseBizEnum.TIP, "移位单不存在");
        }
        if (MoveStatusEnum.STATUS_COMPLETED.getStatus().equals(moveDTO.getStatus())) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_STATUS_DETAIL_ERROR);
        }
        MoveDetailParam moveDetailParam = new MoveDetailParam();
        moveDetailParam.setMoveCode(moveCode);
        List<MoveDetailDTO> moveDetailList = remoteMoveClient.getMoveDetailList(moveDetailParam);

        // 历史数据废弃
        if (moveDetailList.stream().anyMatch(moveDetailDTO -> StrUtil.isBlank(moveDetailDTO.getBatchSerialNo()))) {
            throw new BaseException(BaseBizEnum.TIP, "历史创建的移位单不再支持，请新建移位单");
        }

        // 库位信息
        List<String> locationCodeList = moveDetailList.stream()
                .flatMap(it -> Stream.of(it.getOriginLocationCode(), it.getTargetLocationCode())).distinct().collect(Collectors.toList());
        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(locationCodeList);
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);
        if (CollectionUtil.isEmpty(locationDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "库位信息不存在");
        }

        List<String> targetLocationCodeList = moveDetailList.stream()
                .map(MoveDetailDTO::getTargetLocationCode).distinct().collect(Collectors.toList());

        // 混放规则信息
        MixRuleParam mixRuleParam = new MixRuleParam();
        mixRuleParam.setCodeList(locationDTOList.stream()
                .filter(it -> targetLocationCodeList.contains(it.getCode()))
                .map(LocationDTO::getMixRuleCode).distinct().collect(Collectors.toList()));
        List<MixRuleDTO> mixRuleDTOList = remoteMixRuleClient.getList(mixRuleParam);

        // 库存信息
        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setLocationCodeList(targetLocationCodeList);
        stockLocationParam.setHasPhysicalQty(true);
        stockLocationParam.setRealGoods(RealOrUnrealGoodsEnum.REAL.getCode());
        List<StockLocationDTO> targetStockLocationList = remoteStockLocationClient.getList(stockLocationParam);

        List<String> originLotNoList = moveDetailList.stream().map(MoveDetailDTO::getSkuLotNo).distinct().collect(Collectors.toList());
        // 批次信息
        List<String> lotNoList = targetStockLocationList.stream().map(StockLocationDTO::getSkuLotNo).distinct().collect(Collectors.toList());
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(Stream.concat(originLotNoList.stream(), lotNoList.stream()).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);

        for (LocationDTO locationDTO : locationDTOList) {
            if (!targetLocationCodeList.contains(locationDTO.getCode())) {
                continue;
            }
            // 库位状态校验
            locationBiz.statusCheck(locationDTO);
            List<StockLocationDTO> stockLocationDTOS = targetStockLocationList.stream()
                    .filter(it -> it.getLocationCode().equals(locationDTO.getCode())).collect(Collectors.toList());
            List<StockLocationDTO> sourceStockLocationList = moveDetailList.stream()
                    .filter(it -> it.getTargetLocationCode().equals(locationDTO.getCode()))
                    .map(it -> {
                        MoveDetailDTO moveDetailDTO = moveDetailList.stream()
                                .filter(itt -> itt.getId().equals(it.getId()))
                                .findFirst().orElseThrow(() -> new BaseException(BaseBizEnum.TIP, "明细不存在" + it.getId()));
                        StockLocationDTO stockLocationDTO = new StockLocationDTO();
                        stockLocationDTO.setSkuCode(moveDetailDTO.getSkuCode());
                        stockLocationDTO.setPhysicalQty(it.getExpSkuQty());
                        stockLocationDTO.setCargoCode(moveDetailDTO.getCargoCode());
                        stockLocationDTO.setSkuLotNo(moveDetailDTO.getSkuLotNo());
                        return stockLocationDTO;
                    }).collect(Collectors.toList());
            // 库位最大商品数、种类数校验
            locationBiz.maxSkuCheck(stockLocationDTOS, sourceStockLocationList, locationDTO);
            // 批次混放校验
            MixRuleDTO mixRuleDTO = mixRuleDTOList.stream().filter(it -> it.getCode().equals(locationDTO.getMixRuleCode())).findFirst().orElseThrow(() -> new BaseException(BaseBizEnum.TIP, "批次混放信息未找到"));
            mixRuleCheckBiz.checkLocationMixRule(sourceStockLocationList, stockLocationDTOS, mixRuleDTO, skuLotDTOList);
        }

        List<MoveStockBO> moveStockBOList = new ArrayList<>();
        for (MoveDetailDTO moveDetailDTO : moveDetailList) {
            LocationDTO originLocation = locationDTOList.stream()
                    .filter(it -> it.getCode().equals(moveDetailDTO.getOriginLocationCode()))
                    .findFirst().orElseThrow(() -> new BaseException(BaseBizEnum.TIP, "来源库位信息不存在"));
            LocationDTO targetLocation = locationDTOList.stream()
                    .filter(it -> it.getCode().equals(moveDetailDTO.getTargetLocationCode()))
                    .findFirst().orElseThrow(() -> new BaseException(BaseBizEnum.TIP, "目标库位信息不存在"));
            moveStockBOList.add(MoveStockBO.builder()
                    .warehouseCode(moveDTO.getWarehouseCode())
                    .cargoCode(moveDTO.getCargoCode())
                    .billNo(moveDTO.getCode())
                    .skuCode(moveDetailDTO.getSkuCode())
                    .skuLotNo(moveDetailDTO.getSkuLotNo())
                    .skuQty(moveDetailDTO.getExpSkuQty())
                    .moveCode(moveCode)
                    .skuQuality(moveDetailDTO.getSkuQuality())
                    .originZoneType(originLocation.getZoneType())
                    .originZoneCode(originLocation.getZoneCode())
                    .originLocationType(originLocation.getType())
                    .originLocationCode(originLocation.getCode())
                    .originLocationUseMode(originLocation.getUseMode())
                    .targetZoneType(targetLocation.getZoneType())
                    .targetZoneCode(targetLocation.getZoneCode())
                    .targetLocationType(targetLocation.getType())
                    .targetLocationCode(targetLocation.getCode())
                    .targetLocationUseMode(targetLocation.getUseMode())
                    .batchSerialNo(moveDetailDTO.getBatchSerialNo())
                    .build());
            moveDetailDTO.setActualSkuQty(moveDetailDTO.getExpSkuQty());
            moveDetailDTO.setStatus(MoveStatusEnum.STATUS_DOING.getStatus());
        }

        moveDTO.setActualSkuType(moveDTO.getExpSkuType());
        moveDTO.setActualSkuQty(moveDTO.getExpSkuQty());
        moveDTO.setStatus(MoveStatusEnum.STATUS_DOING.getStatus());

        MoveCompleteMultiDetailBO bo = new MoveCompleteMultiDetailBO();
        bo.setMoveDTO(moveDTO);
        bo.setMoveDetailDTOList(moveDetailList);
        bo.setMoveStockBOList(moveStockBOList);
        moveContextService.completeMultiMoveDetail(bo);
        return Result.success(true);
    }

    @Override
    public Result<List<StockLocationWithLotBizDTO>> scanOriginLocation(ScanOriginLocationParam param) {
        LocationParam locationParam = new LocationParam();
        locationParam.setCode(param.getLocationCode());
        LocationDTO location = remoteLocationClient.get(locationParam);
        if (ObjectUtils.isEmpty(location)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_LOCATION_NULL_ERROR);
        }

        List<ZoneTypeEnum> allowMoveZoneTypeList = new ArrayList<>();
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_STORE);
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PICK);


        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setLocationCode(location.getCode());
        stockLocationParam.setHasAvailableQty(true);
        List<StockLocationDTO> stockLocationList = remoteStockLocationClient.getList(stockLocationParam);
        if (CollectionUtils.isEmpty(stockLocationList)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_STOCK_LOCATION_NULL_ERROR);
        }
        List<StockLocationWithLotBizDTO> stockLocationBizList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(stockLocationList)) {
            stockLocationBizList = ConverterUtil.convertList(stockLocationList, StockLocationWithLotBizDTO.class);
            List<String> warehouseCodeList = stockLocationBizList
                    .stream()
                    .flatMap(a -> Stream.of(a.getWarehouseCode()))
                    .distinct()
                    .collect(Collectors.toList());
            WarehouseParam warehouseParam = new WarehouseParam();
            warehouseParam.setCodeList(warehouseCodeList);
            List<WarehouseDTO> warehouseList = remoteWarehouseClient.queryList(warehouseParam);

            List<String> cargoCodeList = stockLocationBizList
                    .stream()
                    .flatMap(a -> Stream.of(a.getCargoCode()))
                    .distinct()
                    .collect(Collectors.toList());
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(cargoCodeList);
            List<CargoOwnerDTO> cargoOwnerList = remoteCargoOwnerClient.queryList(cargoOwnerParam);

            List<String> skuCodeList = stockLocationBizList
                    .stream()
                    .flatMap(a -> Stream.of(a.getSkuCode()))
                    .distinct()
                    .collect(Collectors.toList());
            SkuParam skuParam = new SkuParam();
            skuParam.setCodeList(skuCodeList);
            List<SkuDTO> skuList = remoteSkuClient.getList(skuParam);

            List<String> zoneCodeList = stockLocationBizList
                    .stream()
                    .flatMap(a -> Stream.of(a.getZoneCode()))
                    .distinct()
                    .collect(Collectors.toList());
            ZoneParam zoneParam = new ZoneParam();
            zoneParam.setCodeList(zoneCodeList);
            List<ZoneDTO> zoneList = remoteZoneClient.getList(zoneParam);

            List<String> locationCodeList = stockLocationBizList
                    .stream()
                    .flatMap(a -> Stream.of(a.getLocationCode()))
                    .distinct()
                    .collect(Collectors.toList());

            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setSkuCodeList(skuCodeList);
            skuUpcParam.setIsDefault(SkuUpcDefaultEnum.YES.getStatus());
            List<SkuUpcDTO> upcList = remoteSkuClient.getSkuUpcList(skuUpcParam);

            List<String> skuLotNoPageList = stockLocationBizList
                    .stream()
                    .flatMap(a -> Stream.of(a.getSkuLotNo()))
                    .distinct()
                    .collect(Collectors.toList());
            SkuLotParam skuLotParam = new SkuLotParam();
            skuLotParam.setCodeList(skuLotNoPageList);
            List<SkuLotDTO> skuLotList = remoteSkuLotClient.getList(skuLotParam);

            stockLocationBizList.stream().forEach(a -> {
                WarehouseDTO warehouse = warehouseList.stream()
                        .filter(b -> b.getCode().equals(a.getWarehouseCode()))
                        .findAny()
                        .orElse(null);
                CargoOwnerDTO cargoOwner = cargoOwnerList.stream()
                        .filter(b -> b.getCode().equals(a.getCargoCode()))
                        .findAny().orElse(null);
                SkuDTO sku = skuList.stream()
                        .filter(b -> b.getCargoCode().equals(a.getCargoCode()))
                        .filter(b -> b.getCode().equals(a.getSkuCode()))
                        .findAny().orElse(null);
                ZoneDTO zone = zoneList.stream()
                        .filter(b -> b.getCode().equals(a.getZoneCode()))
                        .findAny().orElse(null);
                SkuLotDTO skuLot = skuLotList.stream()
                        .filter(b -> b.getCargoCode().equals(a.getCargoCode()))
                        .filter(b -> b.getCode().equals(a.getSkuLotNo()))
                        .findAny().orElse(null);

                SkuUpcDTO skuUpc = upcList.stream()
                        .filter(b -> b.getCargoCode().equals(a.getCargoCode()))
                        .filter(b -> b.getSkuCode().equals(a.getSkuCode()))
                        .findAny().orElse(null);
                if (!allowMoveZoneTypeList.contains(ZoneTypeEnum.getEnum(zone.getType()))) {
                    throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_ORIGIN_STOCK_NOT_ALLOW_TEMP);
                }

                a.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
                a.setZoneName(ObjectUtils.isEmpty(zone) ? "" : zone.getName());
                a.setZoneType(ObjectUtils.isEmpty(zone) ? "" : zone.getType());
                a.setLocationCode(ObjectUtils.isEmpty(location) ? "" : location.getCode());
                a.setLocationType(ObjectUtils.isEmpty(location) ? "" : location.getType());
                a.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
                a.setSkuName(ObjectUtils.isEmpty(sku) ? "" : sku.getName());
                a.setUpcCode(ObjectUtils.isEmpty(skuUpc) ? "" : skuUpc.getUpcCode());

                a.setSkuQuality(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getSkuQuality());
                a.setProductionNo(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getProductionNo());
                a.setReceiveDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getReceiveDate());
                a.setReceiveDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getReceiveDateFormat());
                a.setManufDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getManufDate());
                a.setManufDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getManufDateFormat());
                a.setExpireDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getExpireDate());
                a.setExpireDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getExpireDateFormat());
                a.setWithdrawDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getWithdrawDate());
                a.setWithdrawDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getWithdrawDateFormat());
            });
        }
        return Result.success(stockLocationBizList);
    }


    @Override
    public Result<StockLocationWithLotBizDTO> scanUpcCode(ScanUpcCodeParam param) {

        LocationParam locationParam = new LocationParam();
        locationParam.setCode(param.getLocationCode());
        LocationDTO location = remoteLocationClient.get(locationParam);
        if (ObjectUtils.isEmpty(location)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_LOCATION_NULL_ERROR);
        }
        //TODO：ERROR
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setUpcCode(param.getUpcCode());
        List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
        if (CollectionUtils.isEmpty(skuUpcList)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_SKU_UPC_NULL_ERROR);
        }
        List<String> skuCodeList = skuUpcList.stream()
                .flatMap(a -> Stream.of(a.getSkuCode()))
                .collect(Collectors.toList());

        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setLocationCode(param.getLocationCode());
        stockLocationParam.setSkuCodeList(skuCodeList);
        stockLocationParam.setHasAvailableQty(true);
        StockLocationDTO stockLocation = remoteStockLocationClient.get(stockLocationParam);
        if (ObjectUtils.isEmpty(stockLocation)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_STOCK_LOCATION_SKU_NULL_ERROR);
        }

        StockLocationWithLotBizDTO result = ConverterUtil.convert(stockLocation, StockLocationWithLotBizDTO.class);
        if (!ObjectUtils.isEmpty(result)) {
            WarehouseDTO warehouse = remoteWarehouseClient.queryByCode(result.getWarehouseCode());
            CargoOwnerDTO cargoOwner = remoteCargoOwnerClient.queryByCode(result.getCargoCode());
            SkuParam skuParam = new SkuParam();
            skuParam.setCode(result.getSkuCode());
            skuParam.setCargoCode(result.getCargoCode());
            SkuDTO sku = remoteSkuClient.get(skuParam);
            ZoneParam zoneParam = new ZoneParam();
            zoneParam.setCode(result.getZoneCode());
            ZoneDTO zone = remoteZoneClient.get(zoneParam);
            SkuUpcParam skuUpcDefaultParam = new SkuUpcParam();
            skuUpcDefaultParam.setCargoCode(cargoOwner.getCode());
            skuUpcDefaultParam.setSkuCode(result.getSkuCode());
            skuUpcDefaultParam.setIsDefault(SkuUpcDefaultEnum.YES.getStatus());
            SkuUpcDTO upc = remoteSkuClient.getSkuUpc(skuUpcDefaultParam);

            SkuLotParam skuLotParam = new SkuLotParam();
            skuLotParam.setCargoCode(stockLocation.getCargoCode());
            skuLotParam.setSkuCode(stockLocation.getSkuCode());
            skuLotParam.setCode(stockLocation.getSkuLotNo());
            SkuLotDTO skuLot = remoteSkuLotClient.get(skuLotParam);
            if (ObjectUtils.isEmpty(skuLot)) {
                throw new BaseException(BaseBizEnum.TIP, "移位单商品对应批次把信息不正确");
            }
            result.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
            result.setZoneName(ObjectUtils.isEmpty(zone) ? "" : zone.getName());
            result.setZoneType(ObjectUtils.isEmpty(zone) ? "" : zone.getType());
            result.setLocationCode(ObjectUtils.isEmpty(location) ? "" : location.getCode());
            result.setLocationType(ObjectUtils.isEmpty(location) ? "" : location.getType());
            result.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
            result.setSkuName(ObjectUtils.isEmpty(sku) ? "" : sku.getName());
            result.setUpcCode(ObjectUtils.isEmpty(upc) ? "" : upc.getUpcCode());
            result.setSkuQuality(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getSkuQuality());
            result.setProductionNo(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getProductionNo());
            result.setReceiveDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getReceiveDate());
            result.setReceiveDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getReceiveDateFormat());
            result.setManufDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getManufDate());
            result.setManufDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getManufDateFormat());
            result.setExpireDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getExpireDate());
            result.setExpireDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getExpireDateFormat());
            result.setWithdrawDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getWithdrawDate());
            result.setWithdrawDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getWithdrawDateFormat());
        }
        return Result.success(result);
    }

    @Override
    public Result<List<StockLocationWithLotBizDTO>> scanUpcCodeNew(ScanUpcCodeParam param) {

        String moveCode = remoteSeqRuleClient.findSequence(SeqEnum.MOVE_CODE_000001);

        LocationParam locationParam = new LocationParam();
        locationParam.setCode(param.getLocationCode());
        LocationDTO location = remoteLocationClient.get(locationParam);
        if (ObjectUtils.isEmpty(location)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_LOCATION_NULL_ERROR);
        }
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setUpcCode(param.getUpcCode());
        List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
        if (CollectionUtils.isEmpty(skuUpcList)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_SKU_UPC_NULL_ERROR);
        }
        List<String> skuCodeList = skuUpcList.stream()
                .flatMap(a -> Stream.of(a.getSkuCode()))
                .collect(Collectors.toList());

        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setLocationCode(param.getLocationCode());
        stockLocationParam.setSkuCodeList(skuCodeList);
        stockLocationParam.setHasAvailableQty(true);
        List<StockLocationDTO> stockLocationDTOS = remoteStockLocationClient.getList(stockLocationParam);
        if (CollectionUtil.isEmpty(stockLocationDTOS)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_STOCK_LOCATION_SKU_NULL_ERROR);
        }

        List<StockLocationWithLotBizDTO> stockLocationWithLotBizDTOS = ConverterUtil.convertList(stockLocationDTOS, StockLocationWithLotBizDTO.class);
        stockLocationWithLotBizDTOS.forEach(it -> {
            WarehouseDTO warehouse = remoteWarehouseClient.queryByCode(it.getWarehouseCode());
            CargoOwnerDTO cargoOwner = remoteCargoOwnerClient.queryByCode(it.getCargoCode());
            SkuParam skuParam = new SkuParam();
            skuParam.setCode(it.getSkuCode());
            skuParam.setCargoCode(it.getCargoCode());
            SkuDTO sku = remoteSkuClient.get(skuParam);
            ZoneParam zoneParam = new ZoneParam();
            zoneParam.setCode(it.getZoneCode());
            ZoneDTO zone = remoteZoneClient.get(zoneParam);
            SkuUpcParam skuUpcDefaultParam = new SkuUpcParam();
            skuUpcDefaultParam.setCargoCode(cargoOwner.getCode());
            skuUpcDefaultParam.setSkuCode(it.getSkuCode());
            skuUpcDefaultParam.setIsDefault(SkuUpcDefaultEnum.YES.getStatus());
            SkuUpcDTO upc = remoteSkuClient.getSkuUpc(skuUpcDefaultParam);

            SkuLotParam skuLotParam = new SkuLotParam();
            skuLotParam.setCargoCode(it.getCargoCode());
            skuLotParam.setSkuCode(it.getSkuCode());
            skuLotParam.setCode(it.getSkuLotNo());
            SkuLotDTO skuLot = remoteSkuLotClient.get(skuLotParam);
            if (ObjectUtils.isEmpty(skuLot)) {
                throw new BaseException(BaseBizEnum.TIP, "移位单商品对应批次把信息不正确");
            }
            it.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
            it.setZoneName(ObjectUtils.isEmpty(zone) ? "" : zone.getName());
            it.setZoneType(ObjectUtils.isEmpty(zone) ? "" : zone.getType());
            it.setLocationCode(ObjectUtils.isEmpty(location) ? "" : location.getCode());
            it.setLocationType(ObjectUtils.isEmpty(location) ? "" : location.getType());
            it.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
            it.setSkuName(ObjectUtils.isEmpty(sku) ? "" : sku.getName());
            it.setUpcCode(ObjectUtils.isEmpty(upc) ? "" : upc.getUpcCode());
            it.setSkuQuality(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getSkuQuality());
            it.setProductionNo(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getProductionNo());
            it.setReceiveDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getReceiveDate());
            it.setReceiveDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getReceiveDateFormat());
            it.setManufDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getManufDate());
            it.setManufDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getManufDateFormat());
            it.setExpireDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getExpireDate());
            it.setExpireDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getExpireDateFormat());
            it.setWithdrawDate(ObjectUtils.isEmpty(skuLot) ? 0 : skuLot.getWithdrawDate());
            it.setWithdrawDateFormat(ObjectUtils.isEmpty(skuLot) ? "" : skuLot.getWithdrawDateFormat());
            it.setMoveCode(moveCode);
        });
        return Result.success(stockLocationWithLotBizDTOS);
    }

    @Override
    @GlobalLock
    public Result<Boolean> scanTargetLocation(ScanTargetLocationParam param) {
        if (param.getOriginLocationCode().equals(param.getTargetLocationCode())) {
            throw ExceptionUtil.exceptionWithMessage("来源库位和目标库存不能相同");
        }
        if (StrUtil.isBlank(param.getMoveCode())) {
            throw ExceptionUtil.exceptionWithMessage("移位单不能为空");
        }
        LocationParam originLocationParam = new LocationParam();
        originLocationParam.setCode(param.getOriginLocationCode());
        LocationDTO originLocation = remoteLocationClient.get(originLocationParam);

        LocationParam targetLocationParam = new LocationParam();
        targetLocationParam.setCode(param.getTargetLocationCode());
        LocationDTO targetLocation = remoteLocationClient.get(targetLocationParam);
        if (ObjectUtils.isEmpty(originLocation) || ObjectUtils.isEmpty(targetLocation)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_LOCATION_NULL_ERROR);
        }
        ZoneParam targetZoneParam = new ZoneParam();
        targetZoneParam.setCode(targetLocation.getZoneCode());
        ZoneDTO targetZone = remoteZoneClient.get(targetZoneParam);
        if (ObjectUtils.isEmpty(originLocation) || ObjectUtils.isEmpty(targetLocation) || ObjectUtils.isEmpty(targetZone)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_LOCATION_NULL_ERROR);
        }
        //目标库区限制
        List<ZoneTypeEnum> allowMoveZoneTypeList = new ArrayList<>();
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_STORE);
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PICK);
        if (!allowMoveZoneTypeList.contains(ZoneTypeEnum.getEnum(targetZone.getType()))) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_ORIGIN_STOCK_NOT_ALLOW_TEMP);
        }
        // 目标库区正次品属性与商品正次品属性是否一致
        if (!param.getSkuQuality().equals(targetZone.getSkuQuality())) {
            throw new BaseException(BaseBizEnum.TIP, "目标库位库区正次品属性与商品正次品属性不一致");
        }
        StockLocationParam originStockLocationParam = new StockLocationParam();
        originStockLocationParam.setLocationCode(param.getOriginLocationCode());
        originStockLocationParam.setSkuCode(param.getSkuCode());
        originStockLocationParam.setSkuLotNo(param.getSkuLotNo());
        originStockLocationParam.setSkuQuality(param.getSkuQuality());
        originStockLocationParam.setHasAvailableQty(true);
        StockLocationDTO originStockLocation = remoteStockLocationClient.get(originStockLocationParam);
        if (ObjectUtils.isEmpty(originStockLocation)) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_STOCK_LOCATION_NULL_ERROR);
        }
        if (originStockLocation.getAvailableQty().compareTo(param.getMoveSkuQty()) < 0) {
            throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_STOCK_LOCATION_NOT_ENOUGH_ERROR);
        }

        //移位单信息
        MoveDTO move = new MoveDTO();
        move.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        move.setCargoCode(originStockLocation.getCargoCode());
        move.setCode(param.getMoveCode());
        move.setSkuQuality(originStockLocation.getSkuQuality());
        move.setExpSkuType(1);
        move.setExpSkuQty(param.getMoveSkuQty());
        move.setActualSkuType(1);
        move.setActualSkuQty(param.getMoveSkuQty());
        move.setOpType(OpTypeEnum.OP_TYPE_RF.getType());
        move.setOpBy(CurrentUserHolder.getUserName());
        move.setCompleteDate(System.currentTimeMillis());
        move.setStatus(MoveStatusEnum.STATUS_DOING.getStatus());

        MoveDetailDTO moveDetail = new MoveDetailDTO();
        moveDetail.setWarehouseCode(move.getWarehouseCode());
        moveDetail.setCargoCode(move.getCargoCode());
        moveDetail.setMoveCode(move.getCode());
        moveDetail.setSkuCode(originStockLocation.getSkuCode());
        moveDetail.setSkuLotNo(originStockLocation.getSkuLotNo());
        moveDetail.setSkuQuality(originStockLocation.getSkuQuality());
        moveDetail.setOriginZoneCode(originLocation.getZoneCode());
        moveDetail.setOriginZoneType(originLocation.getType());
        moveDetail.setOriginLocationCode(originLocation.getCode());
        moveDetail.setTargetZoneCode(targetLocation.getZoneCode());
        moveDetail.setTargetZoneType(targetLocation.getType());
        moveDetail.setTargetLocationCode(targetLocation.getCode());
        moveDetail.setExpSkuQty(param.getMoveSkuQty());
        moveDetail.setActualSkuQty(param.getMoveSkuQty());
        moveDetail.setBatchSerialNo(IdUtil.simpleUUID());
        moveDetail.setStatus(MoveStatusEnum.STATUS_DOING.getStatus());

        //混放批次
        mixRuleCheckBiz.checkLocationMixRule(moveDetail.getTargetLocationCode(), targetLocation.getMixRuleCode(), moveDetail.getCargoCode(),
                moveDetail.getSkuCode(), moveDetail.getSkuLotNo());

        locationBiz.statusCheck(targetLocation);

        // 最大商品数、最大品类数校验
        StockLocationParam targetStockLocationParam = new StockLocationParam();
        targetStockLocationParam.setLocationCode(moveDetail.getTargetLocationCode());
        targetStockLocationParam.setHasPhysicalQty(true);
        List<StockLocationDTO> targetStockLocationList = remoteStockLocationClient.getList(targetStockLocationParam);
        List<StockLocationDTO> originStockLocationList = new ArrayList<>();
        StockLocationDTO stockLocationDTO = new StockLocationDTO();
        stockLocationDTO.setSkuCode(moveDetail.getSkuCode());
        stockLocationDTO.setCargoCode(moveDetail.getCargoCode());
        stockLocationDTO.setPhysicalQty(moveDetail.getActualSkuQty());
        originStockLocationList.add(stockLocationDTO);
        locationBiz.maxSkuCheck(targetStockLocationList, originStockLocationList, targetLocation);

        List<MoveStockBO> moveStockBOList = new ArrayList<>();
        MoveStockBO moveStockBO = MoveStockBO.builder()
                .warehouseCode(move.getWarehouseCode())
                .cargoCode(move.getCargoCode())
                .billNo(move.getCode())
                .skuCode(moveDetail.getSkuCode())
                .skuLotNo(moveDetail.getSkuLotNo())
                .skuQty(moveDetail.getExpSkuQty())
                .moveCode(move.getCode())
                .skuQuality(moveDetail.getSkuQuality())
                .originZoneType(originLocation.getZoneType())
                .originZoneCode(originLocation.getZoneCode())
                .originLocationType(originLocation.getType())
                .originLocationCode(originLocation.getCode())
                .originLocationUseMode(originLocation.getUseMode())
                .targetZoneType(targetLocation.getZoneType())
                .targetZoneCode(targetLocation.getZoneCode())
                .targetLocationType(targetLocation.getType())
                .targetLocationCode(targetLocation.getCode())
                .targetLocationUseMode(targetLocation.getUseMode())
                .batchSerialNo(moveDetail.getBatchSerialNo())
                .build();
        moveStockBOList.add(moveStockBO);

        MoveCompleteDetailBO completeDetail = new MoveCompleteDetailBO();
        completeDetail.setMove(ConverterUtil.convert(move, MoveParam.class));
        completeDetail.setDetailList(Arrays.asList(moveDetail));
        completeDetail.setMoveStockBOList(moveStockBOList);
        Boolean result = moveContextService.completePdaMoveDetail(completeDetail);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }

        return Result.success(true);
    }

    @Override
    public Result<Boolean> moveAdd(MoveAddParam param) {
        List<String> originLocationCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getOriginLocationCode())).distinct().collect(Collectors.toList());
        List<String> targetLocationCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getTargetLocationCode())).distinct().collect(Collectors.toList());
        List<String> skuCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList());
        List<String> skuLotNoList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuLotNo())).distinct().collect(Collectors.toList());

        //查询批次ID
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(param.getCargoCode());
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> skuLotList = remoteSkuLotClient.getList(skuLotParam);

        if (CollectionUtils.isEmpty(skuLotList)) {
            throw new BaseException(BaseBizEnum.TIP, "提交批次未找到");
        }

        if (CollectionUtils.isEmpty(originLocationCodeList) || CollectionUtils.isEmpty(targetLocationCodeList)) {
            throw new BaseException(BaseBizEnum.TIP, "目标库位和来源库位不能为空！");
        }

        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(new ArrayList<>());
        locationParam.getCodeList().addAll(originLocationCodeList);
        locationParam.getCodeList().addAll(targetLocationCodeList);
        List<LocationDTO> locationList = remoteLocationClient.getList(locationParam);
        if (CollectionUtils.isEmpty(locationList)) {
            throw new BaseException(BaseBizEnum.TIP, "目标库位信息不存在");
        }
        //校验库位的状态
        List<ZoneDTO> zoneList = checkLocationStatus(locationList);

        List<ZoneTypeEnum> allowMoveZoneTypeList = new ArrayList<>();
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_STORE);
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PICK);

        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        stockLocationParam.setCargoCode(param.getCargoCode());
        stockLocationParam.setLocationCodeList(originLocationCodeList);
        stockLocationParam.setSkuCodeList(skuCodeList);
        stockLocationParam.setSkuLotNoList(skuLotNoList);
        List<StockLocationDTO> stockLocationList = remoteStockLocationClient.getList(stockLocationParam);

        MoveDTO bizParam = new MoveDTO();
        bizParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        bizParam.setCargoCode(param.getCargoCode());
        bizParam.setOpType(OpTypeEnum.OP_TYPE_PAPER.getType());
        bizParam.setDetailList(ConverterUtil.convertList(param.getDetailList(), MoveDetailDTO.class));
        bizParam.setCode(remoteSeqRuleClient.findSequence(SeqEnum.MOVE_CODE_000001));
        bizParam.setExpSkuType(param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toList()).size());
        bizParam.setExpSkuQty(new BigDecimal("0.000"));
        bizParam.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());
        bizParam.setDetailList(ConverterUtil.convertList(param.getDetailList(), MoveDetailDTO.class));
        String batchSerialNo = IdUtil.simpleUUID();
        for (MoveDetailDTO detail : bizParam.getDetailList()) {
            detail.setCargoCode(bizParam.getCargoCode());
            if (detail.getOriginLocationCode().equals(detail.getTargetLocationCode())) {
                throw new BaseException(BaseBizEnum.TIP, "来源库位与目标库位不能一致");
            }
            LocationDTO originLocation = locationList.stream().filter(a -> a.getCode().equals(detail.getOriginLocationCode())).findAny().orElse(null);
            if (ObjectUtils.isEmpty(originLocation)) {
                throw new BaseException(BaseBizEnum.TIP, "来源库位不存在！");
            }
            LocationDTO targetLocation = locationList.stream().filter(a -> a.getCode().equals(detail.getTargetLocationCode())).findAny().orElse(null);
            if (ObjectUtils.isEmpty(targetLocation)) {
                throw new BaseException(BaseBizEnum.TIP, "目标库位不存在！");
            }
            ZoneDTO originZone = zoneList.stream().filter(a -> a.getCode().equals(originLocation.getZoneCode())).findAny().orElse(null);
            ZoneDTO targetZone = zoneList.stream().filter(a -> a.getCode().equals(targetLocation.getZoneCode())).findAny().orElse(null);
            if (ObjectUtils.isEmpty(originLocation) || ObjectUtils.isEmpty(targetLocation) || ObjectUtils.isEmpty(originZone) || ObjectUtils.isEmpty(targetZone)) {
                throw new BaseException(BaseBizEnum.TIP, "库区库位信息不正确！");
            }
            if (ZoneStatusEnum.STATUS_DISABLED.getStatus().equals(targetZone.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库区:%s未启用"),
                        detail.getSkuCode(), detail.getSkuLotNo(), targetLocation.getZoneCode());
            }
            if (LocationStatusEnum.STATUS_DISABLED.getStatus().equals(targetLocation.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库位:%s未启用"),
                        detail.getSkuCode(), detail.getSkuLotNo(), targetLocation.getCode());
            }
            if (!targetZone.getSkuQuality().equals(originZone.getSkuQuality())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("目标库位%s库区商品属性与来源库位%s库区商品属性不一致", targetLocation.getCode(), originLocation.getCode()));
            }
            //移位限制暂存区
            ZoneTypeEnum originZoneType = ZoneTypeEnum.getEnum(originZone.getType());
            ZoneTypeEnum targetZoneType = ZoneTypeEnum.getEnum(targetZone.getType());
            if (!allowMoveZoneTypeList.contains(originZoneType) || !allowMoveZoneTypeList.contains(targetZoneType)) {
                throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_ORIGIN_STOCK_NOT_ALLOW_TEMP);
            }
            SkuLotDTO skuLot = skuLotList.stream()
                    .filter(a -> a.getCargoCode().equals(detail.getCargoCode()))
                    .filter(a -> a.getSkuCode().equals(detail.getSkuCode()))
                    .filter(a -> a.getCode().equals(detail.getSkuLotNo())).findAny()
                    .orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s不存在", detail.getSkuCode(), detail.getSkuLotNo())));
            StockLocationDTO stockLocation = stockLocationList.stream()
                    .filter(a -> a.getLocationCode().equals(detail.getOriginLocationCode()))
                    .filter(a -> a.getCargoCode().equals(detail.getCargoCode()))
                    .filter(a -> a.getSkuCode().equals(detail.getSkuCode()))
                    .filter(a -> a.getSkuLotNo().equals(detail.getSkuLotNo()))
                    .filter(a -> a.getAvailableQty().compareTo(BigDecimal.ZERO) > 0)
                    .findAny()
                    .orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("来源库位:%s商品:%s批次:%s库存信息不存在", detail.getOriginLocationCode(), detail.getSkuCode(), detail.getSkuLotNo())));
            //移位单
            bizParam.setExpSkuQty(bizParam.getExpSkuQty().add(detail.getExpSkuQty()));
            //明细
            detail.setSkuQuality(skuLot.getSkuQuality());
            detail.setMoveCode(bizParam.getCode());
            detail.setWarehouseCode(bizParam.getWarehouseCode());
            detail.setOriginZoneCode(originZone.getCode());
            detail.setOriginZoneType(originZone.getType());
            detail.setTargetZoneCode(targetZone.getCode());
            detail.setTargetZoneType(targetZone.getType());
            detail.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());
            detail.setBatchSerialNo(batchSerialNo);
            //混放批次
            mixRuleCheckBiz.checkLocationMixRule(detail.getTargetLocationCode(), targetLocation.getMixRuleCode(), detail.getCargoCode(),
                    detail.getSkuCode(), detail.getSkuLotNo());
        }

        Boolean result = remoteMoveClient.saveDTO(bizParam);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> moveUpdate(MoveUpdateParam param) {
        if (ObjectUtils.isEmpty(param.getCode())) {
            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        MoveParam moveParam = new MoveParam();
        moveParam.setCode(param.getCode());
        MoveDTO move = remoteMoveClient.get(moveParam);
        if (ObjectUtils.isEmpty(move)) {
            throw new WmsBizException(BaseBizEnum.DATA_ERROR);
        }
        if (move.getStatus().equals(MoveStatusEnum.STATUS_COMPLETED.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "移位单已完成,不允许修改");
        }
        if (move.getStatus().equals(MoveStatusEnum.STATUS_DOING.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "移位单正在移位,不允许修改");
        }
        if (move.getStatus().equals(MoveStatusEnum.STATUS_CANCELED.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "移位单已取消,不允许修改");
        }

        List<String> originLocationCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getOriginLocationCode())).collect(Collectors.toList());
        List<String> targetLocationCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getTargetLocationCode())).collect(Collectors.toList());
        List<String> skuCodeList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toList());
        List<String> skuLotNoList = param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuLotNo())).collect(Collectors.toList());
        //查询批次ID
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(param.getCargoCode());
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> skuLotList = remoteSkuLotClient.getList(skuLotParam);

        if (CollectionUtils.isEmpty(skuLotList)) {
            throw new BaseException(BaseBizEnum.TIP, "提交批次未找到");
        }
        if (CollectionUtils.isEmpty(originLocationCodeList) || CollectionUtils.isEmpty(targetLocationCodeList)) {
            throw new BaseException(BaseBizEnum.TIP, "目标库位和来源库位不能为空！");
        }
        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(new ArrayList<>());
        locationParam.getCodeList().addAll(originLocationCodeList);
        locationParam.getCodeList().addAll(targetLocationCodeList);
        List<LocationDTO> locationList = remoteLocationClient.getList(locationParam);

        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCodeList(locationList.stream().flatMap(a -> Stream.of(a.getZoneCode())).collect(Collectors.toList()));
        List<ZoneDTO> zoneList = remoteZoneClient.getList(zoneParam);

        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        stockLocationParam.setCargoCode(move.getCargoCode());
        stockLocationParam.setLocationCodeList(originLocationCodeList);
        stockLocationParam.setSkuCodeList(skuCodeList);
        stockLocationParam.setSkuLotNoList(skuLotNoList);
        List<StockLocationDTO> stockLocationList = remoteStockLocationClient.getList(stockLocationParam);

        List<ZoneTypeEnum> allowMoveZoneTypeList = new ArrayList<>();
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_STORE);
        allowMoveZoneTypeList.add(ZoneTypeEnum.ZONE_TYPE_PICK);


        MoveDTO bizParam = ConverterUtil.convert(move, MoveDTO.class);
        if (ObjectUtils.isEmpty(bizParam)) {
            throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        bizParam.setWarehouseCode(move.getWarehouseCode());
        bizParam.setId(move.getId());
        bizParam.setExpSkuType(param.getDetailList().stream().flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toList()).size());
        bizParam.setExpSkuQty(new BigDecimal("0.000"));
        bizParam.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());

        MoveDetailParam moveDetailParam = new MoveDetailParam();
        moveDetailParam.setMoveCode(move.getCode());
        List<MoveDetailDTO> moveDetailDTOList = remoteMoveClient.getMoveDetailList(moveDetailParam);
        //移除的移位明细记录
        List<MoveDetailDTO> removeDetailList = new ArrayList<>();

        List<Long> submitDetailIdList = param.getDetailList().stream().filter(a -> !Objects.isNull(a.getId())).map(MoveUpdateDetailParam::getId).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(moveDetailDTOList)) {
            //提交明细的ID
            Iterator<MoveDetailDTO> it = moveDetailDTOList.iterator();
            while (it.hasNext()) {
                MoveDetailDTO moveDetailDTO = it.next();
                if (!submitDetailIdList.contains(moveDetailDTO.getId())) {
                    removeDetailList.add(moveDetailDTO);
                    it.remove();
                }
            }
        } else {
            List<Long> moveDetailIdList = param.getDetailList().stream().filter(a -> !Objects.isNull(a.getId())).map(MoveUpdateDetailParam::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(moveDetailIdList)) {
                throw new BaseException(BaseBizEnum.TIP, "提交明细参数异常");
            }
        }
        //移除的List
        bizParam.setRemoveDetailList(removeDetailList);
        List<MoveDetailDTO> detailList = new ArrayList<>();
        String batchSerialNo = IdUtil.simpleUUID();
        for (MoveUpdateDetailParam entity : param.getDetailList()) {
            MoveDetailDTO detail = ConverterUtil.convert(entity, MoveDetailDTO.class);
            detail.setCargoCode(move.getCargoCode());
            if (detail.getOriginLocationCode().equals(detail.getTargetLocationCode())) {
                throw new WmsBizException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
            LocationDTO originLocation = locationList.stream().filter(a -> a.getCode().equals(detail.getOriginLocationCode())).findAny().orElse(null);
            if (originLocation == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("来源库位%s不存在", detail.getOriginLocationCode()));
            }
            LocationDTO targetLocation = locationList.stream().filter(a -> a.getCode().equals(detail.getTargetLocationCode())).findAny().orElse(null);
            if (targetLocation == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("目标库位%s不存在", detail.getTargetLocationCode()));
            }
            ZoneDTO originZone = zoneList.stream().filter(a -> a.getCode().equals(originLocation.getZoneCode())).findAny().get();
            ZoneDTO targetZone = zoneList.stream().filter(a -> a.getCode().equals(targetLocation.getZoneCode())).findAny().get();
            //移位限制暂存区
            ZoneTypeEnum originZoneType = ZoneTypeEnum.getEnum(originZone.getType());
            ZoneTypeEnum targetZoneType = ZoneTypeEnum.getEnum(targetZone.getType());
            if (!allowMoveZoneTypeList.contains(originZoneType) || !allowMoveZoneTypeList.contains(targetZoneType)) {
                throw new WmsBizException(WmsMoveBizEnum.MOVE_BIZ_ORIGIN_STOCK_NOT_ALLOW_TEMP);
            }
            if (ZoneStatusEnum.STATUS_DISABLED.getStatus().equals(targetZone.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库区:%s未启用",
                        detail.getSkuCode(), detail.getSkuLotNo(), targetLocation.getZoneCode()));
            }
            if (LocationStatusEnum.STATUS_DISABLED.getStatus().equals(targetLocation.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s目标库位:%s未启用",
                        detail.getSkuCode(), detail.getSkuLotNo(), targetLocation.getCode()));
            }
            if (!targetZone.getSkuQuality().equals(originZone.getSkuQuality())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("目标库位%s库区商品属性与来源库位%s库区商品属性不一致", targetLocation.getCode(), originLocation.getCode()));
            }

            SkuLotDTO skuLot = skuLotList.stream()
                    .filter(a -> a.getCargoCode().equals(detail.getCargoCode()))
                    .filter(a -> a.getSkuCode().equals(detail.getSkuCode()))
                    .filter(a -> a.getCode().equals(detail.getSkuLotNo())).findAny()
                    .orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("商品:%s批次:%s不存在", detail.getSkuCode(), detail.getSkuLotNo())));
            StockLocationDTO stockLocation = stockLocationList.stream()
                    .filter(a -> a.getLocationCode().equals(detail.getOriginLocationCode()))
                    .filter(a -> a.getCargoCode().equals(detail.getCargoCode()))
                    .filter(a -> a.getSkuCode().equals(detail.getSkuCode()))
                    .filter(a -> a.getSkuLotNo().equals(detail.getSkuLotNo()))
                    .filter(a -> a.getAvailableQty().compareTo(BigDecimal.ZERO) > 0)
                    .findAny()
                    .orElseThrow(() -> new BaseException(BaseBizEnum.TIP, String.format("来源库位:%s商品:%s批次:%s库存信息不存在", detail.getOriginLocationCode(), detail.getSkuCode(), detail.getSkuLotNo())));


            //移位单
            bizParam.setExpSkuQty(bizParam.getExpSkuQty().add(detail.getExpSkuQty()));

            //明细
            detail.setSkuQuality(skuLot.getSkuQuality());
            detail.setMoveCode(bizParam.getCode());
            detail.setWarehouseCode(bizParam.getWarehouseCode());
            detail.setOriginZoneCode(originZone.getCode());
            detail.setOriginZoneType(originZone.getType());
            detail.setTargetZoneCode(targetZone.getCode());
            detail.setTargetZoneType(targetZone.getType());
            detail.setBatchSerialNo(batchSerialNo);
            detail.setStatus(MoveStatusEnum.STATUS_WAIT_MOVE.getStatus());

            // 目标库区商品属性必须和商品属性一直
            if (!targetZone.getSkuQuality().equals(skuLot.getSkuQuality())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("库区%s商品属性必须和商品%s商品属性一致", targetZone.getCode(), skuLot.getSkuCode()));
            }

            //混放批次
            mixRuleCheckBiz.checkLocationMixRule(detail.getTargetLocationCode(), targetLocation.getMixRuleCode(), detail.getCargoCode(),
                    detail.getSkuCode(), detail.getSkuLotNo());
            detailList.add(detail);
        }
        bizParam.setDetailList(detailList);

        Boolean result = remoteMoveClient.modifyDTO(bizParam);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> moveWholeLocation(MoveWholeLocationParam param) {

        if (ObjectUtil.isEmpty(param) || StrUtil.isBlank(param.getOriginLocationCode()) || StrUtil.isBlank(param.getTargetLocationCode())) {
            throw new BusinessException("参数不能为空");
        }

        if (StrUtil.equalsIgnoreCase(param.getOriginLocationCode(), param.getTargetLocationCode())) {
            throw new BusinessException("目标库位不能与来源库位相同");
        }

        LocationParam locationParam = new LocationParam();
        locationParam.setCodeList(Lists.newArrayList(param.getOriginLocationCode(), param.getTargetLocationCode()));
        List<LocationDTO> locationDTOList = remoteLocationClient.getList(locationParam);

        LocationDTO originLocation = locationDTOList.stream()
                .filter(it -> it.getCode().equalsIgnoreCase(param.getOriginLocationCode()))
                .findFirst().orElseThrow(() -> new BusinessException("来源库位不存在"));

        LocationDTO targetLocationDTO = locationDTOList.stream()
                .filter(it -> it.getCode().equalsIgnoreCase(param.getTargetLocationCode()))
                .findFirst().orElseThrow(() -> new BusinessException("目标库位不存在"));

        if (ObjectUtil.equals(targetLocationDTO.getStatus(), LocationStatusEnum.STATUS_DISABLED.getStatus())) {
            throw new BusinessException("目标库位禁用");
        }

        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCodeList(locationDTOList.stream().map(LocationDTO::getZoneCode).distinct().collect(Collectors.toList()));
        List<ZoneDTO> zoneBizDTOList = remoteZoneClient.getList(zoneParam);
        if (CollectionUtil.isEmpty(zoneBizDTOList)) {
            throw new BusinessException("库区不存在");
        }

        if (zoneBizDTOList.size() > 1 && ObjectUtil.notEqual(zoneBizDTOList.get(0).getSkuQuality(), zoneBizDTOList.get(1).getSkuQuality())) {
            throw new BusinessException("来源库位商品属性与目标库位商品属性不一致");
        }

        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setLocationCodeList(Lists.newArrayList(param.getOriginLocationCode(), param.getTargetLocationCode()));
        stockLocationParam.setHasAvailableQty(true);
        List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getList(stockLocationParam);

        List<StockLocationDTO> originStockLocationDTOList = stockLocationDTOList.stream()
                .filter(it -> StrUtil.equalsIgnoreCase(param.getOriginLocationCode(), it.getLocationCode()))
                .collect(Collectors.toList());
        List<StockLocationDTO> targetStockLocationDTOList = stockLocationDTOList.stream()
                .filter(it -> StrUtil.equalsIgnoreCase(param.getTargetLocationCode(), it.getLocationCode()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(originStockLocationDTOList)) {
            throw new BusinessException("来源库位无可用商品库存");
        }

        List<String> skuLotNoList = stockLocationDTOList.stream().map(StockLocationDTO::getSkuLotNo).distinct().collect(Collectors.toList());
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);

        MixRuleParam mixRuleParam = new MixRuleParam();
        mixRuleParam.setCode(targetLocationDTO.getMixRuleCode());
        MixRuleDTO mixRuleDTO = remoteMixRuleClient.get(mixRuleParam);

        locationBiz.statusCheck(targetLocationDTO);

        // 最大商品数、最大品类数检验
        locationBiz.maxSkuCheck(targetStockLocationDTOList, originStockLocationDTOList, targetLocationDTO);

        // 混放校验
        mixRuleCheckBiz.checkLocationMixRule(originStockLocationDTOList, targetStockLocationDTOList, mixRuleDTO, skuLotDTOList);
        long currentTimeMillis = System.currentTimeMillis() + 60000L;
        // 生成移位单

        Map<String, List<StockLocationDTO>> stockLocationGroupByCargo = originStockLocationDTOList.stream().collect(Collectors.groupingBy(StockLocationDTO::getCargoCode));
        AtomicInteger lineSeq = new AtomicInteger(1);
        String batchSerialNo = IdUtil.simpleUUID();
        List<MoveDTO> moveDTOList = stockLocationGroupByCargo.entrySet().stream()
                .map(it -> {
                    MoveDTO moveDTO = new MoveDTO();
                    moveDTO.setCode(remoteSeqRuleClient.findSequence(SeqEnum.MOVE_CODE_000001));
                    moveDTO.setWarehouseCode(it.getValue().get(0).getWarehouseCode());
                    moveDTO.setCargoCode(it.getKey());
                    moveDTO.setSkuQuality(it.getValue().get(0).getSkuQuality());

                    List<MoveDetailDTO> moveDetailDTOS = it.getValue().stream().map(stockLocationDTO -> {
                        MoveDetailDTO moveDetailDTO = new MoveDetailDTO();
                        moveDetailDTO.setStatus(MoveStatusEnum.STATUS_COMPLETED.getStatus());
                        moveDetailDTO.setCargoCode(stockLocationDTO.getCargoCode());
                        moveDetailDTO.setMoveCode(moveDTO.getCode());
                        moveDetailDTO.setOriginZoneCode(stockLocationDTO.getZoneCode());
                        moveDetailDTO.setOriginZoneType(stockLocationDTO.getZoneType());
                        moveDetailDTO.setSkuQuality(stockLocationDTO.getSkuQuality());
                        moveDetailDTO.setTargetZoneCode(targetLocationDTO.getZoneCode());
                        moveDetailDTO.setTargetZoneType(targetLocationDTO.getZoneType());
                        moveDetailDTO.setWarehouseCode(stockLocationDTO.getWarehouseCode());
                        moveDetailDTO.setActualSkuQty(stockLocationDTO.getAvailableQty());
                        moveDetailDTO.setExpSkuQty(stockLocationDTO.getAvailableQty());
                        moveDetailDTO.setTargetLocationCode(targetLocationDTO.getCode());
                        moveDetailDTO.setLineSeq(String.valueOf(lineSeq.getAndIncrement()));
                        moveDetailDTO.setOriginLocationCode(stockLocationDTO.getLocationCode());
                        moveDetailDTO.setSkuCode(stockLocationDTO.getSkuCode());
                        moveDetailDTO.setSkuLotNo(stockLocationDTO.getSkuLotNo());
                        moveDetailDTO.setTargetLocationType(targetLocationDTO.getType());
                        moveDetailDTO.setTargetLocationUseMode(targetLocationDTO.getUseMode());
                        moveDetailDTO.setBatchSerialNo(batchSerialNo);
                        return moveDetailDTO;
                    }).collect(Collectors.toList());
                    moveDTO.setDetailList(moveDetailDTOS);
                    moveDTO.setExpSkuQty(moveDetailDTOS.stream().map(MoveDetailDTO::getExpSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    moveDTO.setStatus(MoveStatusEnum.STATUS_COMPLETED.getStatus());
                    moveDTO.setExpSkuType(moveDetailDTOS.stream().map(MoveDetailDTO::getSkuCode).distinct().map(skuCode -> 1).reduce(0, Integer::sum));
                    moveDTO.setOpType(OpTypeEnum.OP_TYPE_RF.getType());
                    moveDTO.setActualSkuQty(moveDTO.getExpSkuQty());
                    moveDTO.setActualSkuType(moveDTO.getExpSkuType());
                    moveDTO.setCompleteDate(currentTimeMillis);
                    moveDTO.setOpBy(CurrentUserHolder.getUserName());
                    return moveDTO;
                }).collect(Collectors.toList());

        MoveWholeLocationBO moveWholeLocationBO = new MoveWholeLocationBO();
        moveWholeLocationBO.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        moveWholeLocationBO.setMoveDTOList(moveDTOList);
        moveWholeLocationBO.setOriginLocation(originLocation);
        moveWholeLocationBO.setTargetLocation(targetLocationDTO);
        Boolean result = moveContextService.moveWholeLocation(moveWholeLocationBO);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> moveCheck(MoveBizParam param) {
        MoveParam moveParam = new MoveParam();
        moveParam.setUpdatedTimeEnd(System.currentTimeMillis() - 60000);
        moveParam.setStatus(MoveStatusEnum.STATUS_DOING.getStatus());
        List<MoveDTO> moveDTOList = remoteMoveClient.getList(moveParam);
        if (CollectionUtil.isEmpty(moveDTOList)) return Result.success(true);
        for (MoveDTO moveDTO : moveDTOList) {
            moveContextService.checkMoveStatus(moveDTO);
        }
        return Result.success(true);
    }

    private void formatParam(MoveBizParam param) {

        if (!StringUtils.isEmpty(param.getCode())) {
            if (!CollectionUtils.isEmpty(param.getCodeList())) {
                param.getCodeList().add(param.getCode());
            } else {
                param.setCodeList(new ArrayList<>());
                param.getCodeList().add(param.getCode());
            }
        }
        if (!StringUtils.isEmpty(param.getCargoCode())) {
            if (!CollectionUtils.isEmpty(param.getCargoCodeList())) {
                param.getCargoCodeList().add(param.getCargoCode());
            } else {
                param.setCargoCodeList(new ArrayList<>());
                param.getCargoCodeList().add(param.getCargoCode());
            }
        }
        if (!StringUtils.isEmpty(param.getWarehouseCode())) {
            if (!CollectionUtils.isEmpty(param.getWarehouseCodeList())) {
                param.getWarehouseCodeList().add(param.getWarehouseCode());
            } else {
                param.setWarehouseCodeList(new ArrayList<>());
                param.getWarehouseCodeList().add(param.getWarehouseCode());
            }
        }

        if (!StringUtils.isEmpty(param.getStatus())) {
            if (!CollectionUtils.isEmpty(param.getStatusList())) {
                param.getStatusList().add(param.getStatus());
            } else {
                param.setStatusList(new ArrayList<>());
                param.getStatusList().add(param.getStatus());
            }
        }

        if (!StringUtils.isEmpty(param.getOpType())) {
            if (!CollectionUtils.isEmpty(param.getOpTypeList())) {
                param.getOpTypeList().add(param.getOpType());
            } else {
                param.setOpTypeList(new ArrayList<>());
                param.getOpTypeList().add(param.getOpType());
            }
        }
        if (!StringUtils.isEmpty(param.getSkuCode())) {
            if (!CollectionUtils.isEmpty(param.getSkuCodeList())) {
                param.getSkuCodeList().add(param.getSkuCode());
            } else {
                param.setSkuCodeList(new ArrayList<>());
                param.getSkuCodeList().add(param.getSkuCode());
            }
        }
        if (!StringUtils.isEmpty(param.getSkuUpcCode())) {
            if (!CollectionUtils.isEmpty(param.getSkuUpcCodeList())) {
                param.getSkuUpcCodeList().add(param.getSkuUpcCode());
            } else {
                param.setSkuUpcCodeList(new ArrayList<>());
                param.getSkuUpcCodeList().add(param.getSkuUpcCode());
            }
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCodeList(param.getSkuUpcCodeList());
            List<SkuUpcDTO> upcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            if (!CollectionUtils.isEmpty(upcList)) {
                param.setSkuCodeList(upcList.stream().flatMap(a -> Stream.of(a.getSkuCode())).collect(Collectors.toList()));
            }
        }
        if (!ObjectUtils.isEmpty(param.getSkuLotNo())) {
            MoveDetailParam moveDetailParam = new MoveDetailParam();
            moveDetailParam.setSkuLotNo(param.getSkuLotNo());
            moveDetailParam.setSkuCodeList(param.getSkuCodeList());
            moveDetailParam.setStatusList(param.getStatusList());
            List<MoveDetailDTO> moveDetailList = remoteMoveClient.getMoveDetailList(moveDetailParam);
            if (!CollectionUtils.isEmpty(moveDetailList)) {
                List<String> codeList = moveDetailList.stream().flatMap(a -> Stream.of(a.getMoveCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(param.getCodeList())) {
                    param.setCodeList(codeList);
                } else {
                    Collection<String> intersection = CollectionUtil.intersection(param.getCodeList(), codeList);
                    if (CollectionUtil.isEmpty(intersection)) {
                        param.setId(-1L);
                    } else {
                        param.setCodeList(new ArrayList<>(intersection));
                    }
                }
            } else {
                param.setCodeList(new ArrayList<>());
                param.getCodeList().add("");
            }
        }
    }
}
