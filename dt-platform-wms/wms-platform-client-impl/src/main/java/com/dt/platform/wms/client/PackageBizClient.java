package com.dt.platform.wms.client;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.PretreatmentStatusEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.bill.ShipmentPreSaleTypeEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.CarrierParam;
import com.dt.domain.base.param.SalePlatformParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.pkg.PackAnalysisBillDTO;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.pkg.PackAnalysisBillParam;
import com.dt.domain.core.stock.dto.ValidityPeriodWarnDTO;
import com.dt.domain.core.stock.param.ValidityPeriodWarnParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.LambdaHelpUtils;
import com.dt.platform.wms.biz.IPackageBiz;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.dto.WarmsNoVideoDTO;
import com.dt.platform.wms.biz.dto.WarmsShipWithdrawDTO;
import com.dt.platform.wms.biz.taotian.BizContext;
import com.dt.platform.wms.biz.taotian.FileUploadApplyDTO;
import com.dt.platform.wms.biz.taotian.FileUploadBizParam;
import com.dt.platform.wms.dto.pkg.PackageBizDTO;
import com.dt.platform.wms.dto.pkg.PackageDetailBizDTO;
import com.dt.platform.wms.dto.pkg.PackageLogBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.mercury.IRemoteMercuryClient;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.pkg.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class PackageBizClient implements IPackageBizClient {
    @Resource
    private IRemotePackageClient remotePackageClient;
    @Resource
    IRemoteDecimalPlaceClient decimalPlaceClient;
    @Resource
    IRemoteCarrierClient remoteCarrierClient;
    @Resource
    IRemoteCargoOwnerClient remoteCargoOwnerClient;
    @Resource
    private IRemoteSalePlatform remoteSalePlatform;
    @Resource
    private IRemotePickDetailClient remotePickDetailClient;
    @Resource
    private IRemotePickClient remotePickClient;
    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    WmsOtherConfig wmsOtherConfig;

    @Resource
    DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    private IRemoteValidityPeriodWarnClient remoteValidityPeriodWarnClient;

    @Resource
    private IRemoteShipmentDetailClient remoteShipmentDetailClient;

    @Resource
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteMercuryClient remoteMercuryClient;

    @Resource
    private IPackageBiz packageBiz;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private IRemotePackageCheckClient remotePackageCheckClient;

    @Resource
    private RemoteTenantHelper remoteTenantHelper;

    @Override
    public Result<List<PackageBizDTO>> initCount() {
        return Result.success(ConverterUtil.convertList(remotePackageClient.initCount(), PackageBizDTO.class));
    }

    @Override
    public Result<Page<PackageBizDTO>> queryPage(PackageBizParam param) {
        PackageParam packageParam = ConverterUtil.convert(param, PackageParam.class);
        SalePlatformParam salePlatformParam = new SalePlatformParam();
        List<SalePlatformDTO> salePlatformList = remoteSalePlatform.getList(salePlatformParam);
        Map<String, SalePlatformDTO> salePlatformDTOMap = salePlatformList.stream().collect(Collectors.toMap(it -> it.getCode().toLowerCase(), Function.identity()));
        Page<PackageDTO> page;
        if (StringUtils.isEmpty(wmsOtherConfig.getNewPackPage()) || !wmsOtherConfig.getNewPackPage()) {
            page = remotePackageClient.queryPage(packageParam);
        } else {
            //upc查询
            if (!CollectionUtils.isEmpty(packageParam.getUpcCodeList())) {
                SkuUpcParam skuUpcParam = new SkuUpcParam();
                skuUpcParam.setUpcCodeList(packageParam.getUpcCodeList());
                if (!CollectionUtils.isEmpty(packageParam.getCargoCodeList())) {
                    skuUpcParam.setCargoCodeList(packageParam.getCargoCodeList());
                }
                List<SkuUpcDTO> skuUpcDTOList = remoteSkuClient.getSkuUpcList(skuUpcParam);
                if (CollectionUtils.isEmpty(skuUpcDTOList)) {
                    packageParam.setCargoCodeList(Arrays.asList("---------------"));
                }
                if (!CollectionUtils.isEmpty(packageParam.getSkuCodeList())) {
                    List<String> skuCodeList = packageParam.getSkuCodeList();
                    skuCodeList.retainAll(skuUpcDTOList.stream().map(SkuUpcDTO::getSkuCode).collect(Collectors.toList()));
                    if (CollectionUtils.isEmpty(skuCodeList)) {
                        packageParam.setCargoCodeList(Arrays.asList("---------------"));
                    } else {
                        packageParam.setSkuCodeList(skuCodeList);
                    }
                } else {
                    packageParam.setSkuCodeList(skuUpcDTOList.stream().map(SkuUpcDTO::getSkuCode).collect(Collectors.toList()));
                }
            }
            page = remotePackageClient.getPageNew(packageParam);
        }
        List<PackageDTO> packageDTOList = page.getRecords();
        if (CollectionUtils.isEmpty(packageDTOList)) {
            Page<PackageBizDTO> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
            result.setRecords(new ArrayList<>());
            return Result.success(result);
        }
        //过滤提取到有效的包裹对应的有效拣选单号
        List<PickDetailDTO> pickDetailDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(packageDTOList)) {
            PickDetailParam pickDetailParam = new PickDetailParam();
            pickDetailParam.setPackageCodeList(packageDTOList.stream().map(s -> s.getPackageCode()).distinct().collect(Collectors.toList()));
            pickDetailDTOList = remotePickDetailClient.getList(pickDetailParam);
            List<String> pickCodeList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(pickDetailDTOList)) {
                PickParam pickParam = new PickParam();
                pickParam.setPickCodeList(pickDetailDTOList.stream().map(s -> s.getPickCode()).distinct().collect(Collectors.toList()));
                pickParam.setStatusList(Stream.of(PickEnum.PickStatusEnum.values()).filter(s -> !s.getCode().equalsIgnoreCase(PickEnum.PickStatusEnum.CANCEL_STATUS.getCode())).map(s -> s.getCode()).collect(Collectors.toList()));
                pickCodeList.addAll(remotePickClient.getList(pickParam).stream().map(s -> s.getPickCode()).collect(Collectors.toList()));
            }
            //过滤掉无效的拣选单明细
            pickDetailDTOList = pickDetailDTOList.stream().filter(s -> pickCodeList.contains(s.getPickCode())).collect(Collectors.toList());
        }
        final List<PickDetailDTO> finalPickDetailDTOList = pickDetailDTOList;
        CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
        cargoOwnerParam.setCodeList(packageDTOList.stream().map(PackageDTO::getCargoCode).distinct().collect(Collectors.toList()));
        List<CargoOwnerDTO> allCargoOwner = remoteCargoOwnerClient.getAllCargoOwner(cargoOwnerParam);

        CarrierParam carrierParam = new CarrierParam();
        carrierParam.setCodeList(packageDTOList.stream().map(PackageDTO::getCarrierCode).distinct().collect(Collectors.toList()));
        List<CarrierDTO> carrierDTOList = remoteCarrierClient.getList(carrierParam);

        List<ShipmentOrderDTO> shipmentOrderDTOList = new ArrayList<>();
        List<ShipmentOrderMaterialDTO> shipmentOrderMaterialDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(packageDTOList)) {
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setShipmentOrderCodeList(packageDTOList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
            List<ShipmentOrderDTO> shipmentOrderDTOS = remoteShipmentOrderClient.getList(shipmentOrderParam);
            if (!CollectionUtils.isEmpty(shipmentOrderDTOS)) {
                shipmentOrderDTOList.addAll(shipmentOrderDTOS);
            }
            List<ShipmentOrderMaterialDTO> shipmentOrderMaterialDTOS = remoteShipmentOrderClient.getPackMaterialList(shipmentOrderParam);
            if (!CollectionUtils.isEmpty(shipmentOrderMaterialDTOS)) {
                shipmentOrderMaterialDTOList.addAll(shipmentOrderMaterialDTOS);
            }
        }
        List<PackageBizDTO> recordsList = packageDTOList.stream().map((PackageDTO packageDTO) -> {
            PackageBizDTO packageBizDTO = new PackageBizDTO();
            BeanUtils.copyProperties(packageDTO, packageBizDTO);
            packageBizDTO.setNumberFormat(0);
            packageBizDTO.setWeightFormat(3);
            packageBizDTO.setCargoName("");
            if (!CollectionUtils.isEmpty(allCargoOwner)) {
                allCargoOwner.stream().filter(a -> a.getCode().equalsIgnoreCase(packageDTO.getCargoCode())).findFirst().ifPresent(a -> packageBizDTO.setCargoName(a.getName()));
            }
            ShipmentOrderDTO shipmentOrderDTO = shipmentOrderDTOList.stream().filter(a -> a.getShipmentOrderCode().equalsIgnoreCase(packageBizDTO.getShipmentOrderCode())).findFirst().orElse(null);
            if (shipmentOrderDTO != null) {
                packageBizDTO.setExpShipTimeDateFormat(ConverterUtil.convertVoTime(shipmentOrderDTO.getExpShipTime() == null ? 0L : shipmentOrderDTO.getExpShipTime()));
                if (!StringUtils.isEmpty(shipmentOrderDTO.getPreSaleType())) {
                    packageBizDTO.setPreSaleTypeName(ShipmentPreSaleTypeEnum.fromCode(shipmentOrderDTO.getPreSaleType()).getMessage());
                } else {
                    packageBizDTO.setPreSaleTypeName("");
                }
                List<ShipmentOrderMaterialDTO> shipmentOrderClientPackMaterialList = shipmentOrderMaterialDTOList.stream().filter(a -> a.getShipmentOrderCode().equalsIgnoreCase(packageBizDTO.getShipmentOrderCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(shipmentOrderClientPackMaterialList)) {
                    packageBizDTO.setMaterialUpcCode(shipmentOrderClientPackMaterialList.stream().map(ShipmentOrderMaterialDTO::getRecPackUpcCode).distinct().collect(Collectors.joining(StrUtil.COMMA)));
                } else {
                    packageBizDTO.setMaterialUpcCode("");
                }
                packageBizDTO.setSaleShop(shipmentOrderDTO.getSaleShop());
                packageBizDTO.setSaleShopId(shipmentOrderDTO.getSaleShopId());
                packageBizDTO.setReceiverAreaName(shipmentOrderDTO.getReceiverAreaName());
                packageBizDTO.setReceiverCityName(shipmentOrderDTO.getReceiverCityName());
                packageBizDTO.setReceiverProvName(shipmentOrderDTO.getReceiverProvName());
                packageBizDTO.setReceiverAddress(shipmentOrderDTO.getReceiverAddress());
                packageBizDTO.setReceiverMan(shipmentOrderDTO.getReceiverMan());
                packageBizDTO.setReceiverTel(shipmentOrderDTO.getReceiverTel());
            }

            Optional<PackEnum.STATUS> optional = Optional.ofNullable(PackEnum.STATUS.findEnumDesc(packageBizDTO.getStatus()));
            packageBizDTO.setStatusName(optional.isPresent() ? optional.get().getDesc() : "");
            Optional<PretreatmentStatusEnum> pretreatmentStatusEnumOptional = Optional.ofNullable(PretreatmentStatusEnum.getEnum(packageBizDTO.getPretreatmentStatus()));
            packageBizDTO.setPretreatmentStatusName(pretreatmentStatusEnumOptional.isPresent() ? pretreatmentStatusEnumOptional.get().getName() : "");
            Optional<PackEnum.TYPE> packEnumOptional = Optional.ofNullable(PackEnum.TYPE.findEnumDesc(packageBizDTO.getIsPre()));
            packageBizDTO.setIsPreName(packEnumOptional.isPresent() ? packEnumOptional.get().getDesc() : "");
            packageBizDTO.setCarrierName("");
            if (!CollectionUtils.isEmpty(carrierDTOList)) {
                carrierDTOList.stream().filter(a -> a.getCode().equalsIgnoreCase(packageDTO.getCarrierCode())).findFirst().ifPresent(a -> packageBizDTO.setCarrierName(a.getName()));
            }
            Optional<SalePlatformDTO> salePlatformDTO = Optional.ofNullable(salePlatformDTOMap.get(packageBizDTO.getSalePlatform().toLowerCase()));
            salePlatformDTO.ifPresent(it -> packageBizDTO.setSalePlatformName(it.getName()));
            List<PickDetailDTO> pickDetailList = finalPickDetailDTOList.stream()
                    .filter(a -> a.getPackageCode().equalsIgnoreCase(packageBizDTO.getPackageCode()))
                    .filter(a -> a.getFlag().equalsIgnoreCase(PickEnum.PickDetailFlagEnum.ORIGIN.getCode()))
                    .collect(Collectors.toList());
            AtomicInteger expressPrintNum = new AtomicInteger(0);
            Optional<PickDetailDTO> pickDetailDTOOptional = pickDetailList.stream().findFirst();
            pickDetailDTOOptional.ifPresent(dto -> {
                expressPrintNum.addAndGet(dto.getExpressPrintNum());
            });
            Iterable<String> pickCodeIterator = pickDetailList.stream().map(a -> a.getPickCode()).distinct().collect(Collectors.toList());
            String pickCode = String.join(",", pickCodeIterator);
            packageBizDTO.setPickCode(pickCode == null ? "" : pickCode);
            packageBizDTO.setExpressPrintNum(expressPrintNum.get());
            return packageBizDTO;
        }).collect(Collectors.toList());
        Page<PackageBizDTO> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(recordsList);
        return Result.success(result);
    }

    @Override
    public Result<PackageBizDTO> getDetail(PackageBizParam packageBizParam) {
        PackageParam packageParam = ConverterUtil.convert(packageBizParam, PackageParam.class);
        PackageDTO packageDTO = remotePackageClient.get(packageParam);
        PackageBizDTO packageBizDTO = null;
        if (packageDTO != null) {
            packageBizDTO = new PackageBizDTO();
            BeanUtils.copyProperties(packageDTO, packageBizDTO);
            packageBizDTO.setNumberFormat(decimalPlaceClient.getNumberFormat(packageBizDTO.getWarehouseCode(), packageBizDTO.getCargoCode()));
            packageBizDTO.setWeightFormat(decimalPlaceClient.getWeightFormat(packageBizDTO.getWarehouseCode(), packageBizDTO.getCargoCode()));
            //非淘天处理视频地址 非淘天
            if (!CollectionUtils.isEmpty(wmsOtherConfig.getFtpServerUrl())
                    && wmsOtherConfig.getFtpServerUrl().containsKey(packageBizDTO.getWarehouseCode())
                    && !StringUtils.isEmpty(packageBizDTO.getExtraJson())
                    && !OrderTagEnum.NumToEnum(packageBizDTO.getOrderTag()).contains(OrderTagEnum.TAOTAIN)) {
                JSONObject jsonObject = JSONUtil.parseObj(packageBizDTO.getExtraJson());
                if (jsonObject.containsKey("videoUrl") && !StringUtils.isEmpty(jsonObject.getStr("videoUrl", ""))) {
                    String videoUrl = jsonObject.getStr("videoUrl", "");
                    String ftpServerUrl = wmsOtherConfig.getFtpServerUrl().get(packageBizDTO.getWarehouseCode());

                    String url = "";
                    if (videoUrl.startsWith("/")) {
                        url = ftpServerUrl + videoUrl;
                    } else {
                        url = ftpServerUrl + "/" + videoUrl;
                    }
                    jsonObject.set("videoUrl", url);
                    packageBizDTO.setExtraJson(jsonObject.toString());
                }
            }
            //获取日志
            packageParam = new PackageParam();
            packageParam.setPackageCode(packageBizDTO.getPackageCode());
            List<PackageLogDTO> packageLogDTOList = remotePackageClient.getPackLog(packageParam);
            List<PackageLogBizDTO> packageLogBizDTOList = ConverterUtil.convertList(packageLogDTOList, PackageLogBizDTO.class);
            packageBizDTO.setLogListDetail(packageLogBizDTOList);
        }
        return Result.success(packageBizDTO);
    }

    @Override
    public Result<List<PackageDetailBizDTO>> getPackageDetailListByCode(CodeParam param) {
        return Result.success(ConverterUtil.convertList(remotePackageClient.getPackageDetailListByCode(param.getCode()), PackageDetailBizDTO.class));
    }

    @Override
    public void wcsSync(PackageDTO packageDTO) {
        packageBiz.wcsSync(packageDTO);
    }

    @Override
    public Result<FileUploadApplyResponse> fileUploadApply(FileUploadParam param) {
        log.info("fileUploadApply-param:{}", JSONUtil.toJsonStr(param));
        if (ObjectUtil.isEmpty(param.getResourceCode())) {
            throw new RuntimeException("仓库编码不能为空");
        }
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getWarehouseCodeList())
                && !defaultWarehouseCodeConfig.getWarehouseCodeList().contains(param.getResourceCode())) {
            throw new RuntimeException("当前环境仓库编码不存在");
        }
        //设置数据源
        RpcContextUtil.setWarehouseCode(param.getResourceCode());

        //调用mercury，还需要转一下货主
        FileUploadBizParam fileUploadBizParam = BeanUtil.copyProperties(param, FileUploadBizParam.class);
        PackageParam packageParam = new PackageParam();
//        packageParam.setStatusList(Arrays.asList(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode()));
        if (ObjectUtil.isNotEmpty(param.getBizContext().getPackageCode())) {
            packageParam.setPackageCode(param.getBizContext().getPackageCode());
        } else {
            packageParam.setExpressNo(param.getBizContext().getExpressNo().split(",")[0]);
        }
        if (StringUtils.isEmpty(packageParam.getPackageCode()) && StringUtils.isEmpty(packageParam.getExpressNo())) {
            throw new RuntimeException("运单号和包裹号不能同时为空");
        }
        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            log.info("fileUploadApply-empty:{}", JSONUtil.toJsonStr(param));
            throw new RuntimeException("未找到包裹");
        }
        PackageDTO packageDTO = packageDTOList.get(0);
        BizContext bizContext = fileUploadBizParam.getBizContext();
        bizContext.setOrderCode(packageDTO.getPoNo());
        fileUploadBizParam.setOwnerCode(packageDTO.getCargoCode());
        fileUploadBizParam.setBizContext(bizContext);

        //设置租户 TODO ADD 2024-12-05
        fileUploadBizParam.setTenantId(remoteTenantHelper.queryTenantId(packageDTO.getWarehouseCode(), packageDTO.getCargoCode()));

        FileUploadApplyDTO fileUploadApplyDTO = remoteMercuryClient.fileUploadApply(fileUploadBizParam);
        fileUploadApplyDTO.setOrderCode(packageDTO.getPoNo());
        return Result.success(BeanUtil.copyProperties(fileUploadApplyDTO, FileUploadApplyResponse.class));
    }


    @Override
    public Result videoReport(PackageVideoReportBizParam packageVideoReportBizParam) {
        log.info("videoReport-param:{}", JSONUtil.toJsonStr(packageVideoReportBizParam));
        if (ObjectUtil.isEmpty(packageVideoReportBizParam.getWarehouseCode())) {
            throw new RuntimeException("仓库编码不能为空");
        }
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getWarehouseCodeList())
                && !defaultWarehouseCodeConfig.getWarehouseCodeList().contains(packageVideoReportBizParam.getWarehouseCode())) {
            throw new RuntimeException("当前环境仓库编码不存在");
        }
        //保存包裹的的视频地址
        RpcContextUtil.setWarehouseCode(packageVideoReportBizParam.getWarehouseCode());
        PackageParam packageParam = new PackageParam();
//        packageParam.setStatusList(Arrays.asList(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode()));
        if (ObjectUtil.isNotEmpty(packageVideoReportBizParam.getPackageCode())) {
            packageParam.setPackageCode(packageVideoReportBizParam.getPackageCode());
        } else {
            packageParam.setExpressNoList(Arrays.asList(packageVideoReportBizParam.getExpressNo().split(",")));
        }
        if (StringUtils.isEmpty(packageParam.getPackageCode()) && CollectionUtils.isEmpty(packageParam.getExpressNoList())) {
            throw new RuntimeException("运单号集合和包裹号不能同时为空");
        }
        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            log.info("videoReport-empty:{}", JSONUtil.toJsonStr(packageVideoReportBizParam));
            throw new RuntimeException("未找到包裹");
        }
        List<PackageDTO> newPackageDTOList = new ArrayList<>();
        for (PackageDTO packageDTO : packageDTOList) {
            if (JSONUtil.isJson(packageDTO.getExtraJson())) {
                JSONObject jsonObject = JSONUtil.parseObj(packageDTO.getExtraJson());
                jsonObject.set("videoUrl", packageVideoReportBizParam.getUrl());
                packageDTO.setExtraJson(jsonObject.toString());
                newPackageDTOList.add(packageDTO);
            }
        }
        remotePackageClient.modifyBatch(newPackageDTOList);
        return Result.success();
    }

    @Override
    public void packageNoVideoWarningJob(String warehouseCode, String param) {
        try {
            if (!remoteWarehouseClient.getTaoTianWarehouse(warehouseCode)) {
                return;
            }
            if (StringUtils.isEmpty(param) && !JSONUtil.isJson(param)) {
                return;
            }
            RpcContextUtil.setWarehouseCode(warehouseCode);
            //{"hours":2,"maxPackCount":200,"openWarm":true,"warmUrls":["https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9e3f1631-a93e-40c9-bdcd-b88f3ee11b75 "],"phones":["17600195127","15858290531"],"warehouseCodeList":["DT_YWTTBS0321"]}
            WarmsNoVideoDTO warmsNoVideoDTO = JSONUtil.toBean(param, WarmsNoVideoDTO.class);
            if (warmsNoVideoDTO == null || !warmsNoVideoDTO.getOpenWarm()
                    || warmsNoVideoDTO.getHours() == null
                    || CollectionUtils.isEmpty(warmsNoVideoDTO.getWarmUrls())
//                    || CollectionUtils.isEmpty(warmsNoVideoDTO.getPhones())
                    || CollectionUtils.isEmpty(warmsNoVideoDTO.getWarehouseCodeList())) {
                return;
            }
            String warehouseCodeAndPhone = warmsNoVideoDTO.getWarehouseCodeList().stream().filter(it -> it.contains(warehouseCode)).findFirst().orElse(null);
            if (StringUtils.isEmpty(warehouseCodeAndPhone)) {
                return;
            }
            WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(warehouseCode);
            //获取出库单
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString());
            shipmentOrderParam.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
            shipmentOrderParam.setNoContainOrderTag(~OrderTagEnum.enumToNum(OrderTagEnum.TAOTIAN_VIDEO));
            shipmentOrderParam.setStartOutStockDate(DateUtil.beginOfDay(new Date()).getTime());
            //出库两个小时无视频预警
            shipmentOrderParam.setEndOutStockDate(System.currentTimeMillis() - warmsNoVideoDTO.getHours() * 3600L * 1000L);
            Integer count = remoteShipmentOrderClient.count(shipmentOrderParam);
            if (count == 0) {
                return;
            }
            String warmMessage = "";//出库包裹无视频包裹数
            if (count > warmsNoVideoDTO.getMaxPackCount()) {
                warmMessage = String.format("**仓库名称:%s **,出库包裹:出库大于%s小时,无视频包裹数【%s】\n", warehouseDTO == null ? "-----" : warehouseDTO.getName(), warmsNoVideoDTO.getHours(), count);
            } else {
                List<ShipmentOrderDTO> shipmentOrderDTOList = remoteShipmentOrderClient.getAppointMultipleParam(shipmentOrderParam, LambdaHelpUtils.convertToFieldNameList(ShipmentOrderDTO::getShipmentOrderCode, ShipmentOrderDTO::getCargoCode));
                if (CollectionUtils.isEmpty(shipmentOrderDTOList)) {
                    return;
                }
                PackageParam packageParam = new PackageParam();
                packageParam.setShipmentOrderCodeList(shipmentOrderDTOList.stream().map(ShipmentOrderDTO::getShipmentOrderCode).collect(Collectors.toList()));
                packageParam.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
                List<PackageDTO> packageDTOList = remotePackageClient.getAppointMultipleParam(packageParam, LambdaHelpUtils.convertToFieldNameList(PackageDTO::getPackageCode));
                if (CollectionUtils.isEmpty(packageDTOList)) {
                    return;
                }
                //
                PackageCheckParam packageCheckParam = new PackageCheckParam();
                packageCheckParam.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
                List<PackageCheckDTO> packageCheckDTOList = remotePackageCheckClient.getPackageCheckListAppointColumn(packageCheckParam,
                        LambdaHelpUtils.convertToFieldNameList(PackageCheckDTO::getCreatedTime, PackageCheckDTO::getId, PackageCheckDTO::getPackageCode, PackageCheckDTO::getExpressNo, PackageCheckDTO::getBenchCode, PackageCheckDTO::getCheckBy));
                if (CollectionUtils.isEmpty(packageCheckDTOList)) {
                    return;
                }
                List<PackageCheckDTO> checkDTOList = packageCheckDTOList.stream().sorted(Comparator.comparing(PackageCheckDTO::getId, Comparator.naturalOrder())).collect(Collectors.toList());
                List<String> warmMessageList = new ArrayList<>();
                warmMessage = String.format("**仓库名称:%s **,出库包裹:出库大于%s小时,无视频包裹数【%s】\n", warehouseDTO == null ? "-----" : warehouseDTO.getName(), warmsNoVideoDTO.getHours(), shipmentOrderDTOList.size());
                for (PackageCheckDTO packageCheckDTO : checkDTOList) {
                    Long minute = DateUtil.between(new Date(), new Date(packageCheckDTO.getCreatedTime()), DateUnit.MINUTE);
                    warmMessageList.add(String.format("运单:%s,质检台:%s,质检人:%s,已出库:%s分钟", packageCheckDTO.getExpressNo(), packageCheckDTO.getBenchCode(), packageCheckDTO.getCheckBy(), minute));
                }
                warmMessage = warmMessage + warmMessageList.stream().collect(Collectors.joining("\n"));
            }
            //组装报文

            String finalWarmMessage = warmMessage;
            warmsNoVideoDTO.getWarmUrls().forEach(url -> {
                try {
                    Map<String, Object> warmMap = new HashMap<String, Object>();
                    warmMap.put("msgtype", "text");
                    Map<String, Object> textContent = new HashMap<String, Object>();
                    textContent.put("content", finalWarmMessage);
                    String phone = warehouseCodeAndPhone.split("#")[1];
                    List<String> phoneList = Arrays.asList(phone.split(CommonConstantUtil.COMMA));
                    if (CollectionUtils.isEmpty(phoneList)) {
                        textContent.put("mentioned_mobile_list", "@all");
                    } else {
                        textContent.put("mentioned_mobile_list", phoneList);
                    }
                    warmMap.put("text", textContent);
                    String post = HttpUtil.post(url, JSONUtil.toJsonStr(warmMap));
                    log.info("packageNoVideoWarningJob:{}", JSONUtil.toJsonStr(post));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {

        }
    }

    @Override
    public void shipWithdrawCompareDateWarn(String warehouseCode, String param) {
        try {
//            if (!remoteWarehouseClient.getTaoTianWarehouse(warehouseCode)) {
//                return;
//            }
            if (StringUtils.isEmpty(param) && !JSONUtil.isJson(param)) {
                return;
            }
            RpcContextUtil.setWarehouseCode(warehouseCode);
            // {"warmUrls":["https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9e3f1631-a93e-40c9-bdcd-b88f3ee11b75 "],"warehouseCodeList":["DT_YWTTBS0321"]}
            WarmsShipWithdrawDTO warmsShipWithdrawDTO = JSONUtil.toBean(param, WarmsShipWithdrawDTO.class);
            if (warmsShipWithdrawDTO == null
                    || CollectionUtils.isEmpty(warmsShipWithdrawDTO.getWarmUrls())
                    || CollectionUtils.isEmpty(warmsShipWithdrawDTO.getWarehouseCodeList())) {
                return;
            }
            String warehouseCodeAndPhone = warmsShipWithdrawDTO.getWarehouseCodeList().stream().filter(it -> it.contains(warehouseCode)).findFirst().orElse(null);
            if (StringUtils.isEmpty(warehouseCodeAndPhone)) {
                return;
            }
            //获取出库单
            // 获取前一天的日期
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = now.minusDays(1).withHour(19).withMinute(0).withSecond(0);
            LocalDateTime endTime = now.minusDays(1).withHour(23).withMinute(59).withSecond(59);

            // 将LocalDateTime转换为Date
            Date startDate = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());

            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString());
            shipmentOrderParam.setStatus(ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode());
            shipmentOrderParam.setCreatedTimeStart(startDate.getTime());
            shipmentOrderParam.setCreatedTimeEnd(endDate.getTime());
            Integer count = remoteShipmentOrderClient.count(shipmentOrderParam);
            if (count == 0) {
                return;
            }
            List<ShipmentOrderDTO> shipmentOrderDTOList = remoteShipmentOrderClient.getAppointMultipleParam(shipmentOrderParam, LambdaHelpUtils.convertToFieldNameList(ShipmentOrderDTO::getShipmentOrderCode, ShipmentOrderDTO::getCargoCode));
            if (CollectionUtils.isEmpty(shipmentOrderDTOList)) {
                return;
            }
            ShipmentOrderDetailParam shipmentOrderDetailParam = new ShipmentOrderDetailParam();
            shipmentOrderDetailParam.setShipmentOrderCodeList(shipmentOrderDTOList.stream().map(ShipmentOrderDTO::getShipmentOrderCode).collect(Collectors.toList()));
            shipmentOrderDetailParam.setWithdrawCompareDateEnd(DateUtil.beginOfDay(new Date()).getTime());
            shipmentOrderDetailParam.setWithdrawCompareDateGTStart(0L);
            List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = remoteShipmentDetailClient.getAppointMultipleParam(shipmentOrderDetailParam,
                    LambdaHelpUtils.convertToFieldNameList(ShipmentOrderDetailDTO::getShipmentOrderCode,
                            ShipmentOrderDetailDTO::getSkuCode,
                            ShipmentOrderDetailDTO::getCargoCode,
                            ShipmentOrderDetailDTO::getWithdrawCompareDate));
            if (CollectionUtils.isEmpty(shipmentOrderDetailDTOList)) {
                return;
            }
            //-- 禁售库存
            ValidityPeriodWarnParam validityPeriodWarnParam = new ValidityPeriodWarnParam();
            validityPeriodWarnParam.setCargoCodeList(shipmentOrderDetailDTOList.stream().map(ShipmentOrderDetailDTO::getCargoCode).distinct().collect(Collectors.toList()));
            validityPeriodWarnParam.setSkuCodeList(shipmentOrderDetailDTOList.stream().map(ShipmentOrderDetailDTO::getSkuCode).distinct().collect(Collectors.toList()));
            validityPeriodWarnParam.setWithdrawDateList(shipmentOrderDetailDTOList.stream().map(it -> {
                return it.getWithdrawCompareDate() + CommonConstantUtil.DAY_MILLISECONDS;
            }).collect(Collectors.toList()));
            validityPeriodWarnParam.setHasAvailableQty(true);
            List<ValidityPeriodWarnDTO> validityPeriodWarnDTOList = remoteValidityPeriodWarnClient.list(validityPeriodWarnParam);
            if (CollectionUtils.isEmpty(validityPeriodWarnDTOList)) {
                return;
            }
            List<Long> withdrawCompareDateList = validityPeriodWarnDTOList.stream()
                    .map(it -> {
                        return it.getWithdrawDate() - CommonConstantUtil.DAY_MILLISECONDS;
                    })
                    .collect(Collectors.toList());

            List<String> shipmentOrderCodeListAndWithdraw = shipmentOrderDetailDTOList.stream().filter(a -> withdrawCompareDateList.contains(a.getWithdrawCompareDate()))
                    .map(ShipmentOrderDetailDTO::getShipmentOrderCode).distinct().collect(Collectors.toList());

            WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(warehouseCode);

            String warmMessage = String.format("**仓库名称:%s **,禁售需要优先处理订单数【%s】\n", warehouseDTO == null ? "-----" : warehouseDTO.getName(), shipmentOrderCodeListAndWithdraw.size());
            warmMessage = warmMessage + shipmentOrderCodeListAndWithdraw.stream().collect(Collectors.joining("\n"));
            //组装报文
            String finalWarmMessage = warmMessage;
            warmsShipWithdrawDTO.getWarmUrls().forEach(url -> {
                try {
                    Map<String, Object> warmMap = new HashMap<String, Object>();
                    warmMap.put("msgtype", "text");
                    Map<String, Object> textContent = new HashMap<String, Object>();
                    textContent.put("content", finalWarmMessage);
                    String phone = warehouseCodeAndPhone.split("#")[1];
                    List<String> phoneList = Arrays.asList(phone.split(CommonConstantUtil.COMMA));
                    if (CollectionUtils.isEmpty(phoneList) || "ALL".equalsIgnoreCase(phone)) {
                        textContent.put("mentioned_mobile_list", "@all");
                    } else {
                        textContent.put("mentioned_mobile_list", phoneList);
                    }
                    warmMap.put("text", textContent);
                    String post = HttpUtil.post(url, JSONUtil.toJsonStr(warmMap));
                    log.info("shipWithdrawCompareDateWarn:{}", JSONUtil.toJsonStr(post));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {

        }
    }

    @Override
    public Result<Map<String, Long>> getPackPrintNum(PackageBizParam packageBizParam) {
        if (packageBizParam == null || CollectionUtils.isEmpty(packageBizParam.getPackageCodeList())) {
            return Result.success(new HashMap<>());
        }
        Map<String, Long> map = remotePackageClient.getPackPrintNum(ConverterUtil.convert(packageBizParam, PackageParam.class));
        return Result.success(map);
    }

    @Override
    public Result<String> videoReportBySelf(PackageVideoReportBySelfBizParam selfBizParam) {
        log.info("videoReportBySelf-param:{}", JSONUtil.toJsonStr(selfBizParam));
        if (StringUtils.isEmpty(selfBizParam.getWarehouseCode())) {
            return Result.fail("仓库编码不能为空");
        }
        if (!CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getWarehouseCodeList())
                && !defaultWarehouseCodeConfig.getWarehouseCodeList().contains(selfBizParam.getWarehouseCode())) {
            return Result.fail("当前环境仓库编码不存在");
        }
        //保存包裹的的视频地址
        RpcContextUtil.setWarehouseCode(selfBizParam.getWarehouseCode());

        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCode(selfBizParam.getPackageCode());
        PackageDTO packageDTO = remotePackageClient.get(packageParam);
        if (StringUtils.isEmpty(packageDTO)) {
            log.info("videoReport-empty:{}", JSONUtil.toJsonStr(selfBizParam));
            return Result.fail("未找到包裹");
        }
        //空
        if (StringUtils.isEmpty(packageDTO.getExtraJson())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("videoUrl", selfBizParam.getUrl());
            packageDTO.setExtraJson(jsonObject.toString());
            remotePackageClient.modifyBatch(Arrays.asList(packageDTO));
        }else{
            if (JSONUtil.isJson(packageDTO.getExtraJson())) {
                JSONObject jsonObject = JSONUtil.parseObj(packageDTO.getExtraJson());
                jsonObject.set("videoUrl", selfBizParam.getUrl());
                packageDTO.setExtraJson(jsonObject.toString());
                remotePackageClient.modifyBatch(Arrays.asList(packageDTO));
            }else{
                log.info("videoReportBySelf no json:{}",JSONUtil.toJsonStr(selfBizParam));
            }
        }
        return Result.success("ok");
    }

    @Override
    public Result<Page<PackAnalysisBillDTO>> getPackAnalysisPage(PackAnalysisBillParam param) {
        Page<PackAnalysisBillDTO> dtoPage =   remotePackageClient.getPackAnalysisPage(param);
        return Result.success(dtoPage);
    }
}
