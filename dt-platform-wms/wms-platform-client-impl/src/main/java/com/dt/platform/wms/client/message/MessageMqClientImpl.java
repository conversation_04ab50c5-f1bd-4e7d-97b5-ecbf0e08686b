package com.dt.platform.wms.client.message;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.bill.MessageMqHandleEnum;
import com.dt.component.common.enums.bill.MessageMqStatusEnum;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.rec.ReceiptStatusEnum;
import com.dt.component.common.enums.stock.StockTransactionStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.msg.StockOperationMessage;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.base.param.log.BillLogParam;
import com.dt.domain.bill.dto.PickDTO;
import com.dt.domain.bill.dto.PickDetailDTO;
import com.dt.domain.bill.dto.ReceiptDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.domain.bill.param.PickDetailParam;
import com.dt.domain.bill.param.PickParam;
import com.dt.domain.bill.param.ReceiptParam;
import com.dt.domain.bill.param.message.MessageMqParam;
import com.dt.domain.bill.param.rs.SalesReturnOrderParam;
import com.dt.domain.core.stock.dto.StockTransactionDTO;
import com.dt.domain.core.stock.param.StockTransactionParam;
import com.dt.platform.utils.*;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.client.cw.ICwTransferBizClient;
import com.dt.platform.wms.client.cw.IStorageApplicationBizClient;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.message.IRemoteMessageHandler;
import com.dt.platform.wms.integration.message.IRemoteMessageMqClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import com.dt.platform.wms.integration.rs.impl.SalesReturnOrderShelfSyncERP;
import com.dt.platform.wms.integration.stock.IRemoteStockTransactionClient;
import com.dt.platform.wms.param.message.MessageMqBizParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.dt.component.common.enums.message.MessageTypeEnum.OPERATION_SALE_RETURN_SYNC_CCS;
import static com.dt.component.common.enums.message.MessageTypeEnum.OPERATION_SALE_RETURN_SYNC_ERP;


/**
 * <p>
 * 消息信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class MessageMqClientImpl implements IMessageMqClient {

    @Resource
    private IRemoteMessageMqClient remoteMessageMqClient;

    @Resource
    IRemoteMessageClient remoteMessageClient;

    @Resource
    private IRemoteStockTransactionClient remoteStockTransactionClient;

    @Resource
    IRemotePickDetailClient remotePickDetailClient;

    @Resource
    IRemotePickClient remotePickClient;

    @Resource
    DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    private IRemoteReceiptClient remoteReceiptClient;

    @DubboReference(injvm = false)
    IStorageApplicationBizClient storageApplicationBizClient;

    @DubboReference(injvm = false)
    ICwTransferBizClient cwTransferBizClient;

    @Resource
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Resource
    private IRemoteAdjustClient remoteAdjustClient;

    @Resource
    private IRemoteTransferClient remoteTransferClient;

    @Resource
    private IRemoteBillLogClient remoteBillLogClient;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SalesReturnOrderShelfSyncERP salesReturnOrderShelfSyncERP;

    @Override
    public Result<Integer> getWaitHandleNum(MessageMqBizParam param) {
        MessageMqParam messageMqParam = ConverterUtil.convert(param, MessageMqParam.class);
        messageMqParam.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        Integer waitHandleNum = remoteMessageMqClient.getWaitHandleNum(messageMqParam);
        return Result.success(waitHandleNum);
    }

    @Override
    public void scanMessageMq(String status, String warehouseCode, String param) {
        RpcContextUtil.setWarehouseCode(warehouseCode);
        //查询创建状态,且看下
        MessageMqParam messageMqParam = new MessageMqParam();
        messageMqParam.setStatus(status);
        //param传数字不加入时间限制
        if (NumberUtil.isNumber(param)) {
            messageMqParam.setLimitNum(Integer.valueOf(param));
        } else {
            if (StrUtil.isNotBlank(param)) {
                messageMqParam.setBillNo(param);
            }else {

            //创建开始时间往前一天
            messageMqParam.setCreatedTimeStart(System.currentTimeMillis() - CommonConstantUtil.DAY_MILLISECONDS);
            //创建结束时间往前30秒
            messageMqParam.setCreatedTimeEnd(System.currentTimeMillis() - 30 * 1000);
            }
        }
        List<MessageMqDTO> messageMqDTOList = remoteMessageMqClient.getListLimit(messageMqParam);
        if (CollectionUtils.isEmpty(messageMqDTOList)) {
            return;
        }
        messageMqDTOList.parallelStream().forEach(messageMqDTO -> {
            RpcContextUtil.setWarehouseCode(messageMqDTO.getWarehouseCode());
            // 00 不处理 10 需要补偿 20 核销处理完成
            MessageMqHandleEnum checkStockTransaction = checkStockTransaction(messageMqDTO.getBillNo(), messageMqDTO.getOperationType(), messageMqDTO.getWarehouseCode(), messageMqDTO);
            switch (checkStockTransaction) {
                //核销处理完成
                case COMPLETE_HANDLE:
                    messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                    remoteMessageMqClient.modify(messageMqDTO);
                    break;
                //补偿消息
                case COMPENSATE:
                    compensate(messageMqDTO);
                    break;
                default:
                    break;
            }
        });
    }

    @Override
    public void dropTaskDataOldByMessageMq(String param, String warehouseCode) {
        //设置数据源
        RpcContextUtil.setWarehouseCode(warehouseCode);
        String syncKey = warehouseCode;
        RLock lock = redissonClient.getLock("dt_wms_drop_message_mq_data_lock:" + syncKey);
        Boolean isLock = false;
        try {
            isLock = lock.tryLock(1, 150, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BaseException(BaseBizEnum.TIP, "操作太快了,请稍后重试");
            }
            Integer count = null;
            do {
                MessageMqParam messageMqCountParam = new MessageMqParam();
                messageMqCountParam.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                messageMqCountParam.setOperationType(MessageTypeEnum.OPERATION_STOCK.getType());
                messageMqCountParam.setCreatedTimeEnd(System.currentTimeMillis() - 15 * CommonConstantUtil.DAY_MILLISECONDS);
                count = remoteMessageMqClient.count(messageMqCountParam);
                if (count == 0) {
                    break;
                }
                MessageMqParam messageMqParam = new MessageMqParam();
                messageMqParam.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                messageMqParam.setOperationType(MessageTypeEnum.OPERATION_STOCK.getType());
                messageMqParam.setCreatedTimeEnd(System.currentTimeMillis() - 15 * CommonConstantUtil.DAY_MILLISECONDS);
                messageMqParam.setLimitNum(1000);
                List<MessageMqDTO> messageMqDTOList = remoteMessageMqClient.getListAppointMultipleParam(messageMqParam, LambdaHelpUtils.convertToFieldNameList(MessageMqDTO::getId));
                if (CollectionUtils.isEmpty(messageMqDTOList)) {
                    break;
                }
                Boolean remove = remoteMessageMqClient.partialPhysicalDeleteById(messageMqDTOList.stream().map(MessageMqDTO::getId).collect(Collectors.toList()));
                log.info("dropTaskDataOldByMessageMq:{} {}", warehouseCode, messageMqDTOList.size());
            } while (count != null && count > 0);

        } catch (Exception e) {
            e.printStackTrace();
            String errorMsg = !StringUtils.isEmpty(e.getMessage()) ? e.getMessage() : "系统异常,请稍后重试!!!";
            throw new BaseException(BaseBizEnum.TIP, errorMsg + ":" + warehouseCode);
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }


    }

    /**
     * @param messageMqDTO
     * @return void
     * @author: WuXian
     * description: 消息补偿
     * create time: 2021/12/3 15:40
     */
    private void compensate(MessageMqDTO messageMqDTO) {
        log.info("compensate start:{}", JSONUtil.toJsonStr(messageMqDTO));
        MessageTypeEnum operationTypeEnum = MessageTypeEnum.getEnum(messageMqDTO.getOperationType());
        switch (operationTypeEnum) {
            //出库 和  //分销圈货出库
            case OPERATION_STOCK:
            case OPERATION_CIRCLE_GOODS_STOCK:
            case OPERATION_CIRCLE_GOODS_SALE_STOCK:
            case OPERATION_RETURN_COMPLETE_CONTAINER:
            case OPERATION_RECEIPT_SHELF:
            case OPERATION_PRE_SPLIT_SHELF:
            case OPERATION_RETURN_SHELF:
            case OPERATION_SALE_RETURN_SHELF:
            case OPERATION_BOM_ASSEMBLE_SHELF:
            case OPERATION_BOM_DISASSEMBLE_SHELF:
                converterSendMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_PACKAGE);
                break;
            //拣选单
            case OPERATION_PICK_RETURN:
                converterSendTransactionMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_PICK);
                break;
            //收货完成容器
            case OPERATION_CIRCLE_GOODS_COMPLETE_CONTAINER:
            case OPERATION_COMPLETE_CONTAINER:
                //取消收货
            case OPERATION_RECEIPT_CANCEL:
            case OPERATION_CANCEL_CIRCLE_GOODS_COMPLETE_CONTAINER:
            case OPERATION_SALE_RETURN_RECEIPT:
                converterSendMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_RECEIPT_LOT);
                break;
            //先收后审--释放库存
            case OPERATION_SHELF_RELEASE:
                converterSendMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_ASN_IN);
                break;
            case OPERATION_OFF_SHELF:
                converterSendMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_SHELF);
                break;
            case OPERATION_CANCEL_ALLOCATION:
            case OPERATION_CANCEL_PICK:
                converterSendCancelAllocationAndPickMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_PICK);
                break;
            case CW_IN_CONFIRM:
            case CW_IN_CONFIRM_COMPLETE:
                converterSendCWMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_CW_IN);
                break;
            case OPERATION_CW_STORAGE_APPLICATION:
                converterSendMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_CW_IN);
                break;
            case CW_OUT_CONFIRM:
            case CW_OUT_CONFIRM_COMPLETE:
                converterSendCWMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_CW_OUT);
                break;
            case OPERATION_CW_TRANSFER_OUT:
            case OPERATION_CW_TRANSFER_COLLECT:
            case OPERATION_CW_TRANSFER_SHELF:
                converterSendMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_CW_OUT);
                break;
            case OPERATION_SALE_RETURN_SYNC_ERP:
                try {
                    Long useTime = System.currentTimeMillis() - messageMqDTO.getCreatedTime();
                    if (useTime > 5 * 60 * 1000) {
                        if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                            String message = String.format("[流程:%s]仓库:%s,单号:%s,耗时:%s(s)", operationTypeEnum.message(), messageMqDTO.getWarehouseCode(), messageMqDTO.getBillNo(), useTime / 1000);
                            WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                        }
                    }
                    SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
                    salesReturnOrderParam.setSalesReturnOrderNo(messageMqDTO.getBillNo());
                    SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
                    if (OPERATION_SALE_RETURN_SYNC_ERP.getType().equalsIgnoreCase(messageMqDTO.getOperationType())) {
                        remoteSalesReturnOrderClient.syncToERP(salesReturnOrderDTO);
                    }
                } catch (Exception exception) {
                    log.error(exception.getMessage(), exception);
                }
                break;
            case OPERATION_SALE_RETURN_CREATE_CALLBACK:
            case OPERATION_SALE_RETURN_ARRIVE_CALLBACK:
            case OPERATION_SALE_RETURN_REJECT_CALLBACK:
            case OPERATION_SALE_RETURN_PULL:
            case OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN:
            case OPERATION_SALE_RETURN_RECEIVE_CALLBACK_ORIGIN:
            case OPERATION_SALE_RETURN_SHELF_CALLBACK:
                remoteSalesReturnOrderClient.callback(ListUtil.toList(messageMqDTO));
                break;
            case OPERATION_ADJUST_COMPLETE_CALLBACK:
                remoteAdjustClient.callback(ListUtil.toList(messageMqDTO));
                break;
            case OPERATION_TRANSFER_OCCUPY_CALLBACK:
            case OPERATION_TRANSFER_COMPLETE_CALLBACK:
            case OPERATION_TRANSFER_OCCUPY_CANCEL_CALLBACK:
                remoteTransferClient.callback(ListUtil.toList(messageMqDTO));
                break;
            case OPERATION_SALE_RETURN_SHELF_SYNC_ERP:
                salesReturnOrderShelfSyncERP.handle(messageMqDTO);
                break;
            case OPERATION_CANCEL_SHIPMENT:
                converterSendMessage(messageMqDTO, operationTypeEnum, BillTypeEnum.BILL_TYPE_PACKAGE);
                break;
            //目前不需要处理的统一只打印
            default:
                log.info("compensate other warehouseCode:{} billNo:{},operationType:{}", messageMqDTO.getWarehouseCode(), messageMqDTO.getBillNo(), messageMqDTO.getOperationType());
                break;
        }
    }

    /**
     * @param messageMqDTO
     * @param operationTypeEnum
     * @param billTypeCwIn
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2023/8/23 11:43
     */
    private void converterSendCWMessage(MessageMqDTO messageMqDTO, MessageTypeEnum operationTypeEnum, BillTypeEnum billTypeCwIn) {
        Long useTime = System.currentTimeMillis() - messageMqDTO.getCreatedTime();
        if (useTime > 5 * 60 * 1000) {
            if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                String message = String.format("[流程:%s]仓库:%s,单号:%s,耗时:%s(s)", operationTypeEnum.message(), messageMqDTO.getWarehouseCode(), messageMqDTO.getBillNo(), useTime / 1000);
                WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
            }
        }
        if (Objects.equals(operationTypeEnum, MessageTypeEnum.CW_IN_CONFIRM) || Objects.equals(operationTypeEnum, MessageTypeEnum.CW_IN_CONFIRM_COMPLETE)) {
            storageApplicationBizClient.sync(messageMqDTO);
        }
        if (Objects.equals(operationTypeEnum, MessageTypeEnum.CW_OUT_CONFIRM) || Objects.equals(operationTypeEnum, MessageTypeEnum.CW_OUT_CONFIRM_COMPLETE)) {
            cwTransferBizClient.syncCCS(messageMqDTO);
        }

    }

    private void converterSendCancelAllocationAndPickMessage(MessageMqDTO messageMqDTO, MessageTypeEnum operationTypeEnum, BillTypeEnum billTypePick) {
        StockOperationMessage stockOperationMessage = new StockOperationMessage();
        stockOperationMessage.setWarehouseCode(messageMqDTO.getWarehouseCode());
        stockOperationMessage.setParentBillNo(messageMqDTO.getBillNo());
        //拣选单交回创建后5分钟未处理监听报警
        Long useTime = System.currentTimeMillis() - messageMqDTO.getCreatedTime();
        if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
            String message = String.format("[拣选单:%s]仓库:%s,单号:%s,耗时:%s(s)", operationTypeEnum.message(), messageMqDTO.getWarehouseCode(), messageMqDTO.getBillNo(), useTime / 1000);
            WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
        }
        if (!StringUtils.isEmpty(messageMqDTO.getBatchSerialNo())) {
            stockOperationMessage.setBatchSerialNo(messageMqDTO.getBatchSerialNo());
        }
        stockOperationMessage.setOperationType(operationTypeEnum.getType());
        stockOperationMessage.setCargoCode(messageMqDTO.getCargoCode());
        stockOperationMessage.setBillType(billTypePick.getType());
        remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
    }

    /**
     * @param messageMqDTO
     * @param operationTypeEnum
     * @param billTypeEnum
     * @return void
     * <AUTHOR>
     * @describe: 非事务消息
     * @date 2023/6/15 13:32
     */
    private void converterSendMessage(MessageMqDTO messageMqDTO, MessageTypeEnum operationTypeEnum, BillTypeEnum billTypeEnum) {
        Long useTime = System.currentTimeMillis() - messageMqDTO.getCreatedTime();
        if (useTime > 5 * 60 * 1000) {
            if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                String message = String.format("[流程:%s]仓库:%s,单号:%s,耗时:%s(s)", operationTypeEnum.message(), messageMqDTO.getWarehouseCode(), messageMqDTO.getBillNo(), useTime / 1000);
                WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
            }
        }
        StockOperationMessage stockOperationMessage = new StockOperationMessage();
        stockOperationMessage.setWarehouseCode(messageMqDTO.getWarehouseCode());
        stockOperationMessage.setBillNo(messageMqDTO.getBillNo());
        stockOperationMessage.setBillNoList(Arrays.asList(messageMqDTO.getBillNo()));
        stockOperationMessage.setOperationType(operationTypeEnum.getType());
        stockOperationMessage.setCargoCode(messageMqDTO.getCargoCode());
        stockOperationMessage.setBillType(billTypeEnum.getType());
        stockOperationMessage.setBatchSerialNo(messageMqDTO.getBatchSerialNo());
        remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
    }

    /**
     * @param messageMqDTO
     * @param operationTypeEnum
     * @param billTypeEnum
     * @return void
     * @author: WuXian
     * description:  补偿发送事务消息
     * create time: 2021/12/3 15:50
     */
    private void converterSendTransactionMessage(MessageMqDTO messageMqDTO, MessageTypeEnum operationTypeEnum, BillTypeEnum billTypeEnum) {
        StockOperationMessage stockOperationMessage = new StockOperationMessage();
        stockOperationMessage.setWarehouseCode(messageMqDTO.getWarehouseCode());
        if (operationTypeEnum.getType().equalsIgnoreCase(MessageTypeEnum.OPERATION_PICK_RETURN.getType())) {
            //拣选单交回创建后5分钟未处理监听报警
            Long useTime = System.currentTimeMillis() - messageMqDTO.getCreatedTime();
            if (useTime > 5 * 60 * 1000) {
                log.info("compensate-pick-warm-timeOut:{}", String.format("pickCode:%s,%s--%s(s)", messageMqDTO.getWarehouseCode(), messageMqDTO.getBillNo(), useTime / 1000));
                if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                    String message = String.format("[拣选交回]仓库:%s,单号:%s,耗时:%s(s)", messageMqDTO.getWarehouseCode(), messageMqDTO.getBillNo(), useTime / 1000);
                    WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                }
            }
            stockOperationMessage.setParentBillNo(messageMqDTO.getBillNo());
        } else {
            stockOperationMessage.setBillNo(messageMqDTO.getBillNo());
        }
        stockOperationMessage.setBatchSerialNo(messageMqDTO.getBatchSerialNo());
        stockOperationMessage.setOperationType(operationTypeEnum.getType());
        stockOperationMessage.setCargoCode(messageMqDTO.getCargoCode());
        stockOperationMessage.setBillType(billTypeEnum.getType());
        remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
    }

    /**
     * @param billNo
     * @param operationType
     * @param warehouseCode
     * @return MessageMqHandleEnum
     * @author: WuXian
     * description: 查询对应单据和操作动作的核销
     * create time: 2021/12/3 11:27
     */
    private MessageMqHandleEnum checkStockTransaction(String billNo, String operationType, String warehouseCode, MessageMqDTO messageMqDTO) {
        if (StringUtils.isEmpty(billNo) || StringUtils.isEmpty(operationType)) {
            return MessageMqHandleEnum.PASS;
        }
        StockTransactionParam stockTransactionParam = new StockTransactionParam();

        MessageTypeEnum operationTypeEnum = MessageTypeEnum.getEnum(operationType);
        if (StrUtil.isNotBlank(operationTypeEnum.getMessageHandler())) {
            IRemoteMessageHandler bean = SpringUtil.getBean(operationTypeEnum.getMessageHandler(), IRemoteMessageHandler.class);
            bean.handle(messageMqDTO);
            return MessageMqHandleEnum.PASS;
        }


        switch (operationTypeEnum) {
            case CW_IN_CONFIRM:
            case CW_IN_CONFIRM_COMPLETE:
            case CW_OUT_CONFIRM:
            case CW_OUT_CONFIRM_COMPLETE:
                return MessageMqHandleEnum.COMPENSATE;
            case OPERATION_CW_TRANSFER_OUT:
            case OPERATION_CW_TRANSFER_COLLECT:
            case OPERATION_CW_TRANSFER_SHELF:
                stockTransactionParam.setBillNo(messageMqDTO.getBillNo());
                if (StrUtil.isNotBlank(messageMqDTO.getBatchSerialNo())) {
                    stockTransactionParam.setBatchSerialNo(messageMqDTO.getBatchSerialNo());
                }
                stockTransactionParam.setOperationType(operationType);
                List<StockTransactionDTO> stockTransactionCWDTOList = remoteStockTransactionClient.getList(stockTransactionParam);
                if (CollectionUtils.isEmpty(stockTransactionCWDTOList)) {
                    log.info("checkStockTransaction empty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                if (stockTransactionCWDTOList.stream().anyMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.FAIL.getCode()))) {
                    log.info("checkStockTransaction fail warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                stockTransactionCWDTOList = stockTransactionCWDTOList.stream()
                        .filter(a -> !a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(stockTransactionCWDTOList)) {
                    log.info("checkStockTransaction warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                if (stockTransactionCWDTOList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.DONE.getCode()))) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                break;
            //拣选单交回异步
            case OPERATION_PICK_RETURN:
                PickParam pickParam = new PickParam();
                pickParam.setPickCode(billNo);
                PickDTO pickDTO = remotePickClient.get(pickParam);
                if (pickDTO == null) {
                    return MessageMqHandleEnum.PASS;
                }
                if (Objects.equals(pickDTO.getPickFlag(), PickEnum.PickFlagEnum.MERGE.getCode())) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                //需要有核销完成的的数据
                List<String> pickStatusNeedStock = Arrays.asList(PickEnum.PickStatusEnum.ZJ_BEGIN_STATUS.getCode(), PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode(), PickEnum.PickStatusEnum.PICK_END_STATUS.getCode());
                if (pickStatusNeedStock.contains(pickDTO.getStatus())) {
                    PickDetailParam pickDetailParam = new PickDetailParam();
                    pickDetailParam.setPickCode(billNo);
                    List<PickDetailDTO> pickDetailDTOList = remotePickDetailClient.getPickDetailListAppointColumn(pickDetailParam, LambdaHelpUtils.convertToFieldNameList(PickDetailDTO::getPickCode, PickDetailDTO::getPackageCode, PickDetailDTO::getShipmentOrderCode));
                    stockTransactionParam.setBillNoList(pickDetailDTOList.stream().map(PickDetailDTO::getPackageCode).collect(Collectors.toList()));
                    stockTransactionParam.setOperationType(operationType);
                    List<StockTransactionDTO> stockTransactionDTOList = remoteStockTransactionClient.getList(stockTransactionParam);
                    if (CollectionUtils.isEmpty(stockTransactionDTOList)) {
                        log.info("checkStockTransaction empty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                        return MessageMqHandleEnum.COMPENSATE;
                    }
                    if (stockTransactionDTOList.stream().anyMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.FAIL.getCode()))) {
                        log.info("checkStockTransaction fail warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                        return MessageMqHandleEnum.COMPENSATE;
                    }
                    stockTransactionDTOList = stockTransactionDTOList.stream()
                            .filter(a -> !a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(stockTransactionDTOList)) {
                        log.info("checkStockTransaction warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                        return MessageMqHandleEnum.COMPENSATE;
                    }
                    if (stockTransactionDTOList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.DONE.getCode()))) {
                        return MessageMqHandleEnum.COMPLETE_HANDLE;
                    }
                } else {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                break;
            //出库单出库异步
            case OPERATION_STOCK:
            case OPERATION_CIRCLE_GOODS_SALE_STOCK:
            case OPERATION_RETURN_COMPLETE_CONTAINER:
            case OPERATION_RECEIPT_SHELF:
            case OPERATION_BOM_ASSEMBLE_SHELF:
            case OPERATION_BOM_DISASSEMBLE_SHELF:
            case OPERATION_PRE_SPLIT_SHELF:
            case OPERATION_RETURN_SHELF:
            case OPERATION_SALE_RETURN_SHELF:
            case OPERATION_SALE_RETURN_RECEIPT:
            case OPERATION_OFF_SHELF:
            case OPERATION_CW_STORAGE_APPLICATION:
                //出库单出库异步
                stockTransactionParam.setBillNo(billNo);
                if (StrUtil.isNotBlank(messageMqDTO.getBatchSerialNo())) {
                    stockTransactionParam.setBatchSerialNo(messageMqDTO.getBatchSerialNo());
                }
                stockTransactionParam.setOperationType(operationType);
                List<StockTransactionDTO> stockTransactionDTOList = remoteStockTransactionClient.getList(stockTransactionParam);
                if (CollectionUtils.isEmpty(stockTransactionDTOList)) {
                    log.info("checkStockTransaction empty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                if (stockTransactionDTOList.stream().anyMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.FAIL.getCode()))) {
                    log.info("checkStockTransaction fail warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                stockTransactionDTOList = stockTransactionDTOList.stream()
                        .filter(a -> !a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(stockTransactionDTOList)) {
                    log.info("checkStockTransaction warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                if (stockTransactionDTOList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.DONE.getCode()))) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                break;
            case OPERATION_CANCEL_ALLOCATION:
                stockTransactionParam.setParentBillNo(billNo);
                stockTransactionParam.setOperationType(MessageTypeEnum.OPERATION_CONVERGE.getType());
                if (!StringUtils.isEmpty(messageMqDTO.getBatchSerialNo())) {
                    stockTransactionParam.setBatchSerialNo(messageMqDTO.getBatchSerialNo());
                }
                List<StockTransactionDTO> stockTransactionCancelPickDTOList = remoteStockTransactionClient.getList(stockTransactionParam);
                if (CollectionUtils.isEmpty(stockTransactionCancelPickDTOList)) {
                    log.info("checkStockTransaction-CancelAllocation empty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                if (stockTransactionCancelPickDTOList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                if (stockTransactionCancelPickDTOList.stream().anyMatch(a -> !a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                    return MessageMqHandleEnum.COMPENSATE;
                }
                break;
            case OPERATION_CANCEL_PICK:
                PickDetailParam pickDetailParam = new PickDetailParam();
                pickDetailParam.setPickCode(billNo);
                List<PickDetailDTO> pickDetailDTOList = remotePickDetailClient.getList(pickDetailParam);
                if (!StringUtils.isEmpty(messageMqDTO.getBatchSerialNo())) {
                    stockTransactionParam.setBatchSerialNo(messageMqDTO.getBatchSerialNo());
                }
                stockTransactionParam.setBillNoList(pickDetailDTOList.stream().map(PickDetailDTO::getPackageCode).collect(Collectors.toList()));
                stockTransactionParam.setOperationType(MessageTypeEnum.OPERATION_PICK_RETURN.getType());
                List<StockTransactionDTO> cancelPickStockTransactionDTOList = remoteStockTransactionClient.getList(stockTransactionParam);
                if (CollectionUtils.isEmpty(cancelPickStockTransactionDTOList)) {
                    log.info("checkStockTransaction-CancelPick empty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                if (cancelPickStockTransactionDTOList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                if (cancelPickStockTransactionDTOList.stream().anyMatch(a -> !a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                    return MessageMqHandleEnum.COMPENSATE;
                }
                break;
            //收货完成容器
            case OPERATION_CIRCLE_GOODS_COMPLETE_CONTAINER:
            case OPERATION_COMPLETE_CONTAINER:
                stockTransactionParam.setBillNo(billNo);
                List<StockTransactionDTO> stockTransactionReceiptDTOList = remoteStockTransactionClient.getList(stockTransactionParam);
                if (CollectionUtils.isEmpty(stockTransactionReceiptDTOList)) {
                    log.info("checkStockTransaction empty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                //收货核销全部完成
                if (stockTransactionReceiptDTOList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.DONE.getCode()))) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                //收货核销全部取消
                if (stockTransactionReceiptDTOList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                break;
            //取消收货
            case OPERATION_RECEIPT_CANCEL:
            case OPERATION_CANCEL_CIRCLE_GOODS_COMPLETE_CONTAINER:
                stockTransactionParam.setBillNo(billNo);
                List<StockTransactionDTO> stockTransactionReceiptCancelDTOList = remoteStockTransactionClient.getList(stockTransactionParam);
                if (CollectionUtils.isEmpty(stockTransactionReceiptCancelDTOList)) {
                    log.info("stockTransactionReceiptCancelDTOList empty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                //取消只要有一条非取消状态，需要补偿
                if (stockTransactionReceiptCancelDTOList.stream().anyMatch(a -> !a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                    return MessageMqHandleEnum.COMPENSATE;
                }
                //收货核销全部取消
                if (stockTransactionReceiptCancelDTOList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                break;
            //收货上架释放库存
            case OPERATION_SHELF_RELEASE:
                ReceiptParam receiptParam = new ReceiptParam();
                receiptParam.setAsnId(messageMqDTO.getBillNo());
                List<ReceiptDTO> receiptDTOList = remoteReceiptClient.getList(receiptParam);
                if (CollectionUtils.isEmpty(receiptDTOList)) {
                    log.info("receiptDTOList empty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                receiptDTOList = receiptDTOList.stream().filter(a -> a.getStatus().equalsIgnoreCase(ReceiptStatusEnum.PRE_SHELF.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(receiptDTOList)) {
                    log.info("receiptDTOList empty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                if (receiptDTOList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(ReceiptStatusEnum.COMPLETE_SHELF.getCode()))) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                if (receiptDTOList.stream().anyMatch(a -> !a.getStatus().equalsIgnoreCase(ReceiptStatusEnum.COMPLETE_SHELF.getCode()))) {
                    log.info("stockTransactionReleaseStockDTOList em pty warehouseCode:{} billNo:{} operationTypeEnum:{}", warehouseCode, billNo, operationType);
                    return MessageMqHandleEnum.COMPENSATE;
                }
                break;
            case OPERATION_SALE_RETURN_SYNC_ERP:
            case OPERATION_SALE_RETURN_SYNC_CCS:
                SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
                salesReturnOrderParam.setSalesReturnOrderNo(messageMqDTO.getBillNo());
                SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
                if (null == salesReturnOrderDTO) return MessageMqHandleEnum.COMPLETE_HANDLE;
                if (OPERATION_SALE_RETURN_SYNC_ERP.getType().equalsIgnoreCase(messageMqDTO.getOperationType())) {
                    if (StrUtil.isNotBlank(salesReturnOrderDTO.getAsnId())) {
                        return MessageMqHandleEnum.COMPLETE_HANDLE;
                    } else {
                        return MessageMqHandleEnum.COMPENSATE;
                    }
                }
                if (OPERATION_SALE_RETURN_SYNC_CCS.getType().equalsIgnoreCase(messageMqDTO.getOperationType())) {
                    BillLogParam billLogParam = new BillLogParam();
                    billLogParam.setBillNo(messageMqDTO.getBillNo());
                    List<BillLogDTO> list = remoteBillLogClient.getList(billLogParam);
                    if (list.stream().anyMatch(billLogDTO -> billLogDTO.getOpContent().contains("推送CCS成功"))) {
                        return MessageMqHandleEnum.COMPLETE_HANDLE;
                    } else {
                        return MessageMqHandleEnum.COMPENSATE;
                    }
                }
                //目前不需要处理的统一只打印
            case OPERATION_SALE_RETURN_CREATE_CALLBACK:
            case OPERATION_SALE_RETURN_ARRIVE_CALLBACK:
            case OPERATION_SALE_RETURN_REJECT_CALLBACK:
            case OPERATION_SALE_RETURN_PULL:
            case OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN:
            case OPERATION_SALE_RETURN_RECEIVE_CALLBACK_ORIGIN:
            case OPERATION_SALE_RETURN_SHELF_CALLBACK:
            case OPERATION_ADJUST_COMPLETE_CALLBACK:
            case OPERATION_TRANSFER_OCCUPY_CALLBACK:
            case OPERATION_TRANSFER_OCCUPY_CANCEL_CALLBACK:
            case OPERATION_TRANSFER_COMPLETE_CALLBACK:
            case OPERATION_SALE_RETURN_SHELF_SYNC_ERP:
                return MessageMqHandleEnum.COMPENSATE;
            case OPERATION_CANCEL_SHIPMENT:
                stockTransactionParam.setBillNo(billNo);
                List<StockTransactionDTO> pretreatmentStockTransactionList = remoteStockTransactionClient.getList(stockTransactionParam);
                if (CollectionUtils.isEmpty(pretreatmentStockTransactionList)) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                //取消只要有一条非取消状态，需要补偿
                if (pretreatmentStockTransactionList.stream()
                        .anyMatch(a -> !a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                    return MessageMqHandleEnum.COMPENSATE;
                }
                //收货核销全部取消
                if (pretreatmentStockTransactionList.stream().allMatch(a -> a.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CANCELLED.getCode()))) {
                    return MessageMqHandleEnum.COMPLETE_HANDLE;
                }
                break;
            default:
                log.info("checkStockTransaction other warehouseCode:{} billNo:{},operationType:{}", warehouseCode, billNo, operationType);
                break;
        }
        return MessageMqHandleEnum.PASS;
    }

}
