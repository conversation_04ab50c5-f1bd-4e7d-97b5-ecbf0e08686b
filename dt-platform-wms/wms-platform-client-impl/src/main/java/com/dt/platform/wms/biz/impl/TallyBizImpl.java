package com.dt.platform.wms.biz.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.FromSourceEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.rec.ReceiptExtraStatusEnum;
import com.dt.component.common.enums.rec.ReceiptStatusEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.enums.tally.TallyExtraGoodsEnum;
import com.dt.component.common.enums.tally.TallyRealReceiveEnum;
import com.dt.component.common.enums.tally.TallyStatusEnum;
import com.dt.component.common.enums.tally.TallyTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.dto.log.TallyLogDTO;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.rec.ReceiptExtraDTO;
import com.dt.domain.bill.dto.rec.ReceiptExtraDetailDTO;
import com.dt.domain.bill.dto.tally.TallyDTO;
import com.dt.domain.bill.dto.tally.TallyDetailDTO;
import com.dt.domain.bill.param.AllocationOrderParam;
import com.dt.domain.bill.param.PackageParam;
import com.dt.domain.bill.param.ReceiptDetailParam;
import com.dt.domain.bill.param.ReceiptParam;
import com.dt.domain.bill.param.rec.ReceiptExtraDetailParam;
import com.dt.domain.bill.param.rec.ReceiptExtraParam;
import com.dt.domain.bill.param.tally.TallyDetailParam;
import com.dt.domain.bill.param.tally.TallyParam;
import com.dt.platform.wms.biz.IBusinessLogBiz;
import com.dt.platform.wms.biz.ITallyBiz;
import com.dt.platform.wms.dto.tally.TallyReceiveDetailBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.log.IRemoteTallyLogClient;
import com.dt.platform.wms.integration.rec.IRemoteReceiptExtraClient;
import com.dt.platform.wms.integration.rec.IRemoteReceiptExtraDetailClient;
import com.dt.platform.wms.integration.tally.IRemoteTallyClient;
import com.dt.platform.wms.integration.tally.IRemoteTallyDetailClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/14 16:29
 */
@Service
public class TallyBizImpl implements ITallyBiz {

    @Resource
    IRemotePackageClient remotePackageClient;

    @Resource
    IRemoteAllocationOrderClient remoteAllocationOrderClient;

    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteTallyClient remoteTallyClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteTallyLogClient remoteTallyLogClient;

    @Resource
    private IBusinessLogBiz businessLogBiz;

    @Resource
    private IRemoteTallyDetailClient remoteTallyDetailClient;

    @Resource
    private IRemoteReceiptClient remoteReceiptClient;

    @Resource
    private IRemoteReceiptDetailClient remoteReceiptDetailClient;

    @Resource
    private IRemoteReceiptExtraClient remoteReceiptExtraClient;

    @Resource
    private IRemoteReceiptExtraDetailClient remoteReceiptExtraDetailClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;


    @Override
    public Boolean saveShipmentOrderTally(ShipmentOrderDTO shipmentOrderDTO) {
        if (shipmentOrderDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "出库单不存在");
        }
        if (!shipmentOrderDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            throw new BaseException(BaseBizEnum.TIP, "非B2B出库单,不能新增理货报告");
        }
        if (!shipmentOrderDTO.getStatus().equalsIgnoreCase(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "非已汇总出库单,不能新增理货报告");
        }
        if (shipmentOrderDTO.getFromSource().equalsIgnoreCase(FromSourceEnum.WMS.value())) {
            throw new BaseException(BaseBizEnum.TIP, "WMS自建的理货报告不允许新增");
        }
        if (remoteWarehouseClient.getTaoTianWarehouse(shipmentOrderDTO.getWarehouseCode())) {
            throw new BaseException(BaseBizEnum.TIP, "淘天的出库单,理货报告不允许新增");
        }
        //校验理货报告是否存在有效的
        checkTallyExists(shipmentOrderDTO);

        PackageParam packageParam = new PackageParam();
        packageParam.setShipmentOrderCode(shipmentOrderDTO.getShipmentOrderCode());
        List<PackageDTO> dtoList = remotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(dtoList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到包裹,不能新增理货报告");
        }
        //包裹分析
        List<PackageDTO> packageDTOList = buildFilterPack(dtoList);
        //查询分配记录
        AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
        allocationOrderParam.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
        allocationOrderParam.setWaveCodeList(packageDTOList.stream().map(PackageDTO::getWaveCode).collect(Collectors.toList()));
        List<AllocationOrderDTO> allocationOrderDTOS = remoteAllocationOrderClient.getList(allocationOrderParam);
        if (CollectionUtils.isEmpty(allocationOrderDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到分配记录,不能新增理货报告");
        }
        //过滤无效的分配记录
        List<AllocationOrderDTO> allocationOrderDTOList = buildFilterAllocationOrder(packageDTOList, allocationOrderDTOS);
        //新增理货报告
        TallyDTO tallyDTO = new TallyDTO();
        tallyDTO.setTallyCode(remoteSeqRuleClient.findSequence(SeqEnum.TALLY_CODE_000001));
        tallyDTO.setStatus(TallyStatusEnum.CREATE.getCode());
        tallyDTO.setBillNo(shipmentOrderDTO.getShipmentOrderCode());
        tallyDTO.setType(TallyTypeEnum.STOCK_OUT.getCode());
        tallyDTO.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
        tallyDTO.setCargoCode(shipmentOrderDTO.getCargoCode());
        tallyDTO.setTallyBy(CurrentUserHolder.getUserName());
        tallyDTO.setOpDate(System.currentTimeMillis());
        List<TallyDetailDTO> tallyDetailDTOList = buildTallyDetailList(allocationOrderDTOList, tallyDTO);
        tallyDTO.setDetailList(tallyDetailDTOList);
        //存储理货报告
        remoteTallyClient.save(tallyDTO);

        TallyLogDTO logDTO = new TallyLogDTO();
        logDTO.setOpBy(CurrentUserHolder.getUserName());
        logDTO.setOpContent("出库单新增理货报告:" + tallyDTO.getTallyCode());
        logDTO.setTallyCode(tallyDTO.getTallyCode());
        logDTO.setOpDate(System.currentTimeMillis());
        logDTO.setCargoCode(tallyDTO.getCargoCode());
        logDTO.setOpRemark("报文:" + JSON.toJSONString(tallyDTO));
        remoteTallyLogClient.save(logDTO);
        //保存理货报告
        //记录出库单日志
        businessLogBiz.saveShipmentLog(shipmentOrderDTO.getWarehouseCode(), shipmentOrderDTO.getCargoCode(),
                shipmentOrderDTO.getShipmentOrderCode(), CurrentUserHolder.getUserName(), "出库单生成理货报告");
        return true;
    }

    @Override
    public List<TallyReceiveDetailBizDTO> getTallyToReceiveData(String tallyCode, List<String> skuCodeList, Boolean isAllReceive, Boolean receiptImportConfirm) {
        if (StringUtils.isEmpty(tallyCode)) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        TallyParam tallyParam = new TallyParam();
        tallyParam.setTallyCode(tallyCode);
        TallyDTO tallyDTO = remoteTallyClient.get(tallyParam);
        if (StringUtils.isEmpty(tallyDTO)) {
            throw new BaseException(BaseBizEnum.TIP, "理货报告数据为空");
        }
        TallyDetailParam tallyDetailParam = new TallyDetailParam();
        tallyDetailParam.setTallyCode(tallyCode);
        tallyDetailParam.setSkuCodeList(skuCodeList);
        List<TallyDetailDTO> tallyDetailDTOList = remoteTallyDetailClient.getList(tallyDetailParam);
        if (CollectionUtils.isEmpty(tallyDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "理货数据为空");
        }
        //只处理真实收货的
        //淘天到行号
        if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())) {
            //获取所有可以收货的
            if (isAllReceive) {
                tallyDetailDTOList = tallyDetailDTOList.stream()
                        .filter(a -> Objects.equals(a.getRealReceive(), TallyRealReceiveEnum.YES.getCode()))
                        .collect(Collectors.toList());
            } else {
                //获取所有能收货的，无异常信息
                tallyDetailDTOList = tallyDetailDTOList.stream()
                        .filter(a -> Objects.equals(a.getRealReceive(), TallyRealReceiveEnum.YES.getCode()))
                        //无任何标记可以收货
                        .filter(a -> Objects.equals(a.getMark(), 0))
                        .collect(Collectors.toList());
            }
        } else {
            tallyDetailDTOList = tallyDetailDTOList.stream().filter(a -> Objects.equals(a.getRealReceive(), TallyRealReceiveEnum.YES.getCode())).collect(Collectors.toList());
        }

        List<TallyReceiveDetailBizDTO> tallyReceiveDetailBizDTOList = new ArrayList<>();
        //货主+商品+正次品+生产+失效+多货
        Map<String, List<TallyDetailDTO>> map;
        //淘天到行号
        if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())) {
            map = tallyDetailDTOList.stream().collect(Collectors.groupingBy(it ->
                    StrUtil.join("#", it.getCargoCode(), it.getSkuCode(), it.getSkuQuality(), it.getLineNo(), it.getInventoryType(),
                            it.getManufDate(), it.getExpireDate(), it.getExtraGoods(),it.getValidityCode())));
        } else {
            //非淘天不到行号
            map = tallyDetailDTOList.stream().collect(Collectors.groupingBy(it ->
                    StrUtil.join("#", it.getCargoCode(), it.getSkuCode(), it.getSkuQuality(), it.getInventoryType(),
                            it.getManufDate(), it.getExpireDate(), it.getExtraGoods(),it.getValidityCode())));
        }
        //获取当前多货数据
        List<ReceiptExtraDetailDTO> receiptExtraDetailDTOList = new ArrayList<>();
        if (tallyDetailDTOList.stream().anyMatch(a -> !a.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode()))) {
            ReceiptExtraParam receiptExtraParam = new ReceiptExtraParam();
            receiptExtraParam.setAsnId(tallyDTO.getBillNo());
            if (receiptImportConfirm) {
                receiptExtraParam.setStatusList(Arrays.asList(ReceiptExtraStatusEnum.WAIT_SHELF.getCode(),
                        ReceiptExtraStatusEnum.ON_SHELF.getCode(), ReceiptExtraStatusEnum.COMPLETE_SHELF.getCode()));
            } else {
                receiptExtraParam.setStatusList(Arrays.asList(ReceiptExtraStatusEnum.CREATE_SHELF.getCode(), ReceiptExtraStatusEnum.WAIT_SHELF.getCode(),
                        ReceiptExtraStatusEnum.ON_SHELF.getCode(), ReceiptExtraStatusEnum.COMPLETE_SHELF.getCode()));
            }
            List<ReceiptExtraDTO> receiptExtraDTOList = remoteReceiptExtraClient.getList(receiptExtraParam);
            if (!CollectionUtils.isEmpty(receiptExtraDTOList)) {
                ReceiptExtraDetailParam receiptExtraDetailParam = new ReceiptExtraDetailParam();
                receiptExtraDetailParam.setRecExtraIdList(receiptExtraDTOList.stream().map(ReceiptExtraDTO::getRecExtraId).distinct().collect(Collectors.toList()));
                receiptExtraDetailParam.setSkuCodeList(skuCodeList);
                List<ReceiptExtraDetailDTO> detailDTOS = remoteReceiptExtraDetailClient.getList(receiptExtraDetailParam);
                if (!CollectionUtils.isEmpty(detailDTOS)) {
                    receiptExtraDetailDTOList.addAll(detailDTOS);
                }
            }
        }
        //获取当前收货作业批次数据
        List<ReceiptDetailDTO> receiptDetailDTOList = new ArrayList<>();
        if (tallyDetailDTOList.stream().anyMatch(a -> a.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode()))) {
            ReceiptParam receiptParam = new ReceiptParam();
            receiptParam.setAsnId(tallyDTO.getBillNo());
            if (receiptImportConfirm) {
                receiptParam.setStatusList(Arrays.asList(ReceiptStatusEnum.WAIT_SHELF.getCode(),
                        ReceiptStatusEnum.ON_SHELF.getCode(), ReceiptStatusEnum.COMPLETE_SHELF.getCode()));
            } else {
                receiptParam.setStatusList(Arrays.asList(ReceiptStatusEnum.CREATE_SHELF.getCode(), ReceiptStatusEnum.WAIT_SHELF.getCode(),
                        ReceiptStatusEnum.ON_SHELF.getCode(), ReceiptStatusEnum.COMPLETE_SHELF.getCode()));
            }
            List<ReceiptDTO> receiptDTOList = remoteReceiptClient.getList(receiptParam);
            if (!CollectionUtils.isEmpty(receiptDTOList)) {
                ReceiptDetailParam receiptDetailParam = new ReceiptDetailParam();
                receiptDetailParam.setRecIdList(receiptDTOList.stream().map(ReceiptDTO::getRecId).distinct().collect(Collectors.toList()));
                receiptDetailParam.setSkuCodeList(skuCodeList);
                List<ReceiptDetailDTO> detailDTOS = remoteReceiptDetailClient.getList(receiptDetailParam);
                if (!CollectionUtils.isEmpty(detailDTOS)) {
                    receiptDetailDTOList.addAll(detailDTOS);
                }
            }
        }
        map.forEach((key, value) -> {
            List<TallyDetailDTO> detailDTOList = value;
            TallyDetailDTO tallyDetailDTO = detailDTOList.get(0);
            TallyReceiveDetailBizDTO tallyReceiveDetailBizDTO = new TallyReceiveDetailBizDTO();
            tallyReceiveDetailBizDTO.setTallyCode(tallyCode);
            tallyReceiveDetailBizDTO.setCargoCode(tallyDetailDTO.getCargoCode());
            tallyReceiveDetailBizDTO.setInventoryType(tallyDetailDTO.getInventoryType());
            tallyReceiveDetailBizDTO.setValidityCode(tallyDetailDTO.getValidityCode());
            tallyReceiveDetailBizDTO.setSkuCode(tallyDetailDTO.getSkuCode());
            tallyReceiveDetailBizDTO.setExpireDate(tallyDetailDTO.getExpireDate());
            tallyReceiveDetailBizDTO.setManufDate(tallyDetailDTO.getManufDate());
            tallyReceiveDetailBizDTO.setExtraGoods(tallyDetailDTO.getExtraGoods());
            tallyReceiveDetailBizDTO.setCallBackUpper(tallyDetailDTO.getCallBackUpper());
            tallyReceiveDetailBizDTO.setSkuQty(detailDTOList.stream().map(TallyDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            tallyReceiveDetailBizDTO.setSkuQuality(tallyDetailDTO.getSkuQuality());
            tallyReceiveDetailBizDTO.setLineNo(tallyDetailDTO.getLineNo());
            //淘天用于排序,后变更的放在最后面
            tallyReceiveDetailBizDTO.setUpdatedTime(tallyDetailDTO.getUpdatedTime());
            BigDecimal qty;
            //正常收货
            if (tallyDetailDTO.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode())) {
                //淘天到行号
                if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())) {
                    qty = receiptDetailDTOList.stream()
                            .filter(a -> a.getCargoCode().equalsIgnoreCase(tallyDetailDTO.getCargoCode()))
                            .filter(a -> a.getSkuCode().equalsIgnoreCase(tallyDetailDTO.getSkuCode()))
                            .filter(a -> a.getSkuQuality().equalsIgnoreCase(tallyDetailDTO.getSkuQuality()))
                            .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
                            .filter(a -> a.getValidityCode().equalsIgnoreCase(tallyDetailDTO.getValidityCode()))
                            .filter(a -> a.getManufDate().equals(tallyDetailDTO.getManufDate()))
                            .filter(a -> a.getExtNo().equals(tallyDetailDTO.getLineNo()))
                            .filter(a -> a.getExpireDate().equals(tallyDetailDTO.getExpireDate()))
                            .map(ReceiptDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                } else {
                    qty = receiptDetailDTOList.stream()
                            .filter(a -> a.getCargoCode().equalsIgnoreCase(tallyDetailDTO.getCargoCode()))
                            .filter(a -> a.getSkuCode().equalsIgnoreCase(tallyDetailDTO.getSkuCode()))
                            .filter(a -> a.getSkuQuality().equalsIgnoreCase(tallyDetailDTO.getSkuQuality()))
                            .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
                            .filter(a -> a.getValidityCode().equalsIgnoreCase(tallyDetailDTO.getValidityCode()))
                            .filter(a -> a.getManufDate().equals(tallyDetailDTO.getManufDate()))
                            .filter(a -> a.getExpireDate().equals(tallyDetailDTO.getExpireDate()))
                            .map(ReceiptDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
            } else {
                //淘天到行号
                if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())) {
                    qty = receiptExtraDetailDTOList.stream()
                            .filter(a -> a.getCargoCode().equalsIgnoreCase(tallyDetailDTO.getCargoCode()))
                            .filter(a -> a.getSkuCode().equalsIgnoreCase(tallyDetailDTO.getSkuCode()))
                            .filter(a -> a.getSkuQuality().equalsIgnoreCase(tallyDetailDTO.getSkuQuality()))
                            .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
                            .filter(a -> a.getValidityCode().equalsIgnoreCase(tallyDetailDTO.getValidityCode()))
                            .filter(a -> a.getManufDate().equals(tallyDetailDTO.getManufDate()))
                            .filter(a -> a.getExtNo().equals(tallyDetailDTO.getLineNo()))
                            .filter(a -> a.getExpireDate().equals(tallyDetailDTO.getExpireDate()))
                            .map(ReceiptExtraDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                } else {
                    qty = receiptExtraDetailDTOList.stream()
                            .filter(a -> a.getCargoCode().equalsIgnoreCase(tallyDetailDTO.getCargoCode()))
                            .filter(a -> a.getSkuCode().equalsIgnoreCase(tallyDetailDTO.getSkuCode()))
                            .filter(a -> a.getSkuQuality().equalsIgnoreCase(tallyDetailDTO.getSkuQuality()))
                            .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
                            .filter(a -> a.getValidityCode().equalsIgnoreCase(tallyDetailDTO.getValidityCode()))
                            .filter(a -> a.getManufDate().equals(tallyDetailDTO.getManufDate()))
                            .filter(a -> a.getExpireDate().equals(tallyDetailDTO.getExpireDate()))
                            .map(ReceiptExtraDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
            }
            tallyReceiveDetailBizDTO.setRecQty(qty);
            tallyReceiveDetailBizDTO.setWaitQty(tallyReceiveDetailBizDTO.getSkuQty().subtract(tallyReceiveDetailBizDTO.getRecQty()));
            tallyReceiveDetailBizDTOList.add(tallyReceiveDetailBizDTO);
        });
        //排序一下 升序
        List<TallyReceiveDetailBizDTO> tallyReceiveDetailBizDTOListLast = tallyReceiveDetailBizDTOList.stream()
                .sorted(Comparator.comparing(TallyReceiveDetailBizDTO::getUpdatedTime, Comparator.naturalOrder()))
                .collect(Collectors.toList());
        return tallyReceiveDetailBizDTOListLast;
    }

    @Override
    public List<TallyReceiveDetailBizDTO> getTallyToReceiveDataNoLineSeq(String tallyCode, List<String> skuCodeList, Boolean isAllReceive) {
        if (StringUtils.isEmpty(tallyCode)) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        TallyParam tallyParam = new TallyParam();
        tallyParam.setTallyCode(tallyCode);
        TallyDTO tallyDTO = remoteTallyClient.get(tallyParam);
        if (StringUtils.isEmpty(tallyDTO)) {
            throw new BaseException(BaseBizEnum.TIP, "理货报告数据为空");
        }
        TallyDetailParam tallyDetailParam = new TallyDetailParam();
        tallyDetailParam.setTallyCode(tallyCode);
        tallyDetailParam.setSkuCodeList(skuCodeList);
        List<TallyDetailDTO> tallyDetailDTOList = remoteTallyDetailClient.getList(tallyDetailParam);
        if (CollectionUtils.isEmpty(tallyDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "理货数据为空");
        }
        //只处理真实收货的
        if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())) {
            //获取所有可以收货的
            if (isAllReceive) {
                tallyDetailDTOList = tallyDetailDTOList.stream()
                        .filter(a -> Objects.equals(a.getRealReceive(), TallyRealReceiveEnum.YES.getCode()))
                        .collect(Collectors.toList());
            } else {
                //获取所有能收货的，无异常信息
                tallyDetailDTOList = tallyDetailDTOList.stream()
                        .filter(a -> Objects.equals(a.getRealReceive(), TallyRealReceiveEnum.YES.getCode()))
                        //无任何标记可以收货
                        .filter(a -> Objects.equals(a.getMark(), 0))
                        .collect(Collectors.toList());
            }
        } else {
            tallyDetailDTOList = tallyDetailDTOList.stream().filter(a -> Objects.equals(a.getRealReceive(), TallyRealReceiveEnum.YES.getCode())).collect(Collectors.toList());
        }

        List<TallyReceiveDetailBizDTO> tallyReceiveDetailBizDTOList = new ArrayList<>();
        //货主+商品+正次品+生产+失效+多货
        Map<String, List<TallyDetailDTO>> map;

        map = tallyDetailDTOList.stream().collect(Collectors.groupingBy(it ->
                StrUtil.join("#", it.getCargoCode(), it.getSkuCode(), it.getSkuQuality(), it.getInventoryType(),
                        it.getManufDate(), it.getExpireDate(), it.getExtraGoods(),it.getValidityCode())));
        //获取当前多货数据
        List<ReceiptExtraDetailDTO> receiptExtraDetailDTOList = new ArrayList<>();
        if (tallyDetailDTOList.stream().anyMatch(a -> !a.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode()))) {
            ReceiptExtraParam receiptExtraParam = new ReceiptExtraParam();
            receiptExtraParam.setAsnId(tallyDTO.getBillNo());
            receiptExtraParam.setStatusList(Arrays.asList(ReceiptExtraStatusEnum.CREATE_SHELF.getCode(), ReceiptExtraStatusEnum.WAIT_SHELF.getCode(),
                    ReceiptExtraStatusEnum.ON_SHELF.getCode(), ReceiptExtraStatusEnum.COMPLETE_SHELF.getCode()));
            List<ReceiptExtraDTO> receiptExtraDTOList = remoteReceiptExtraClient.getList(receiptExtraParam);
            if (!CollectionUtils.isEmpty(receiptExtraDTOList)) {
                ReceiptExtraDetailParam receiptExtraDetailParam = new ReceiptExtraDetailParam();
                receiptExtraDetailParam.setRecExtraIdList(receiptExtraDTOList.stream().map(ReceiptExtraDTO::getRecExtraId).distinct().collect(Collectors.toList()));
                receiptExtraDetailParam.setSkuCodeList(skuCodeList);
                List<ReceiptExtraDetailDTO> detailDTOS = remoteReceiptExtraDetailClient.getList(receiptExtraDetailParam);
                if (!CollectionUtils.isEmpty(detailDTOS)) {
                    receiptExtraDetailDTOList.addAll(detailDTOS);
                }
            }
        }
        //获取当前收货作业批次数据
        List<ReceiptDetailDTO> receiptDetailDTOList = new ArrayList<>();
        if (tallyDetailDTOList.stream().anyMatch(a -> a.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode()))) {
            ReceiptParam receiptParam = new ReceiptParam();
            receiptParam.setAsnId(tallyDTO.getBillNo());

            receiptParam.setStatusList(Arrays.asList(ReceiptStatusEnum.CREATE_SHELF.getCode(), ReceiptStatusEnum.WAIT_SHELF.getCode(),
                    ReceiptStatusEnum.ON_SHELF.getCode(), ReceiptStatusEnum.COMPLETE_SHELF.getCode()));
            List<ReceiptDTO> receiptDTOList = remoteReceiptClient.getList(receiptParam);
            if (!CollectionUtils.isEmpty(receiptDTOList)) {
                ReceiptDetailParam receiptDetailParam = new ReceiptDetailParam();
                receiptDetailParam.setRecIdList(receiptDTOList.stream().map(ReceiptDTO::getRecId).distinct().collect(Collectors.toList()));
                receiptDetailParam.setSkuCodeList(skuCodeList);
                List<ReceiptDetailDTO> detailDTOS = remoteReceiptDetailClient.getList(receiptDetailParam);
                if (!CollectionUtils.isEmpty(detailDTOS)) {
                    receiptDetailDTOList.addAll(detailDTOS);
                }
            }
        }
        map.forEach((key, value) -> {
            List<TallyDetailDTO> detailDTOList = value;
            TallyDetailDTO tallyDetailDTO = detailDTOList.get(0);
            TallyReceiveDetailBizDTO tallyReceiveDetailBizDTO = new TallyReceiveDetailBizDTO();
            tallyReceiveDetailBizDTO.setTallyCode(tallyCode);
            tallyReceiveDetailBizDTO.setCargoCode(tallyDetailDTO.getCargoCode());
            tallyReceiveDetailBizDTO.setInventoryType(tallyDetailDTO.getInventoryType());
            tallyReceiveDetailBizDTO.setValidityCode(tallyDetailDTO.getValidityCode());
            tallyReceiveDetailBizDTO.setSkuCode(tallyDetailDTO.getSkuCode());
            tallyReceiveDetailBizDTO.setExpireDate(tallyDetailDTO.getExpireDate());
            tallyReceiveDetailBizDTO.setManufDate(tallyDetailDTO.getManufDate());
            tallyReceiveDetailBizDTO.setExtraGoods(tallyDetailDTO.getExtraGoods());
            tallyReceiveDetailBizDTO.setCallBackUpper(tallyDetailDTO.getCallBackUpper());
            tallyReceiveDetailBizDTO.setSkuQty(detailDTOList.stream().map(TallyDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            tallyReceiveDetailBizDTO.setSkuQuality(tallyDetailDTO.getSkuQuality());
            tallyReceiveDetailBizDTO.setLineNo(tallyDetailDTO.getLineNo());
            //淘天用于排序,后变更的放在最后面
            tallyReceiveDetailBizDTO.setUpdatedTime(tallyDetailDTO.getUpdatedTime());
            BigDecimal qty;
            //正常收货
            if (tallyDetailDTO.getExtraGoods().equalsIgnoreCase(TallyExtraGoodsEnum.NORMAL.getCode())) {
                qty = receiptDetailDTOList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(tallyDetailDTO.getCargoCode()))
                        .filter(a -> a.getSkuCode().equalsIgnoreCase(tallyDetailDTO.getSkuCode()))
                        .filter(a -> a.getSkuQuality().equalsIgnoreCase(tallyDetailDTO.getSkuQuality()))
                        .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
                        .filter(a -> a.getValidityCode().equalsIgnoreCase(tallyDetailDTO.getValidityCode()))
                        .filter(a -> a.getManufDate().equals(tallyDetailDTO.getManufDate()))
                        .filter(a -> a.getExpireDate().equals(tallyDetailDTO.getExpireDate()))
                        .map(ReceiptDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                qty = receiptExtraDetailDTOList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(tallyDetailDTO.getCargoCode()))
                        .filter(a -> a.getSkuCode().equalsIgnoreCase(tallyDetailDTO.getSkuCode()))
                        .filter(a -> a.getSkuQuality().equalsIgnoreCase(tallyDetailDTO.getSkuQuality()))
                        .filter(a -> a.getInventoryType().equalsIgnoreCase(tallyDetailDTO.getInventoryType()))
                        .filter(a -> a.getValidityCode().equalsIgnoreCase(tallyDetailDTO.getValidityCode()))
                        .filter(a -> a.getManufDate().equals(tallyDetailDTO.getManufDate()))
                        .filter(a -> a.getExpireDate().equals(tallyDetailDTO.getExpireDate()))
                        .map(ReceiptExtraDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            tallyReceiveDetailBizDTO.setRecQty(qty);
            tallyReceiveDetailBizDTO.setWaitQty(tallyReceiveDetailBizDTO.getSkuQty().subtract(tallyReceiveDetailBizDTO.getRecQty()));
            tallyReceiveDetailBizDTOList.add(tallyReceiveDetailBizDTO);
        });
        return tallyReceiveDetailBizDTOList;
    }

    /**
     * @param shipmentOrderDTO
     * @return void
     * @author: WuXian
     * description:  校验理货报告是否存在有效的
     * create time: 2022/1/14 17:15
     */
    private void checkTallyExists(ShipmentOrderDTO shipmentOrderDTO) {
        TallyParam tallyParam = new TallyParam();
        tallyParam.setType(TallyTypeEnum.STOCK_OUT.getCode());
        tallyParam.setBillNo(shipmentOrderDTO.getShipmentOrderCode());
        List<TallyDTO> tallyDTOList = remoteTallyClient.getList(tallyParam);
        if (!CollectionUtils.isEmpty(tallyDTOList) && tallyDTOList.stream().anyMatch(a -> !a.getStatus().equalsIgnoreCase(TallyStatusEnum.CANCEL.getCode()))) {
            throw new BaseException(BaseBizEnum.TIP, "已存在有效的理货报告,不能新增理货报告");
        }
    }

    /**
     * @param allocationOrderDTOList
     * @param tallyDTO
     * @return java.util.List<com.dt.domain.bill.dto.tally.TallyDetailDTO>
     * @author: WuXian
     * description:
     * create time: 2022/1/14 16:55
     */
    private List<TallyDetailDTO> buildTallyDetailList(List<AllocationOrderDTO> allocationOrderDTOList, TallyDTO tallyDTO) {
        List<TallyDetailDTO> tallyDetailDTOList = new ArrayList<>();
        //获取所有商品
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(tallyDTO.getCargoCode());
        skuParam.setCodeList(allocationOrderDTOList.stream().map(AllocationOrderDTO::getSkuCode).distinct().collect(Collectors.toList()));
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "商品查询不到");
        }
        //获取所有商品
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setCargoCode(tallyDTO.getCargoCode());
        skuUpcParam.setSkuCodeList(allocationOrderDTOList.stream().map(AllocationOrderDTO::getSkuCode).distinct().collect(Collectors.toList()));
        List<SkuUpcDTO> skuUpcDTOList = remoteSkuClient.getSkuUpcList(skuUpcParam);
        if (CollectionUtils.isEmpty(skuUpcDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "商品查询不到");
        }
        //获取所有批次
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(tallyDTO.getCargoCode());
        skuLotParam.setCodeList(allocationOrderDTOList.stream().map(AllocationOrderDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
        if (CollectionUtils.isEmpty(skuLotDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "批次属性不存在");
        }
        //明细按照 批次-商品合并
        Map<String, List<AllocationOrderDTO>> map = allocationOrderDTOList.stream().collect(Collectors.groupingBy(AllocationOrderDTO::getSkuLotNo));
        map.forEach((key, value) -> {
            String skuLotNo = key;
            List<AllocationOrderDTO> allocationOrderDTOS = value;
            TallyDetailDTO tallyDetailDTO = new TallyDetailDTO();
            tallyDetailDTO.setWarehouseCode(tallyDTO.getWarehouseCode());
            tallyDetailDTO.setCargoCode(tallyDTO.getCargoCode());
            tallyDetailDTO.setTallyCode(tallyDTO.getTallyCode());
            tallyDetailDTO.setSkuLotNo(skuLotNo);
            SkuLotDTO skuLotDTO = skuLotDTOList.stream().filter(a -> a.getCode().equalsIgnoreCase(skuLotNo)).findFirst().orElse(null);
            if (StringUtils.isEmpty(skuLotDTO)) {
                throw new BaseException(BaseBizEnum.TIP, "批次属性不存在");
            }
            //--
            tallyDetailDTO.setExtraGoods(TallyExtraGoodsEnum.NORMAL.getCode());
            tallyDetailDTO.setExpireDate(skuLotDTO.getExpireDate());
            tallyDetailDTO.setManufDate(skuLotDTO.getManufDate());
//            tallyDetailDTO.setStatus(tallyDTO.getStatus());
            BigDecimal qty = allocationOrderDTOS.stream().map(AllocationOrderDTO::getRealQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            tallyDetailDTO.setSkuQuality(skuLotDTO.getSkuQuality());
            tallyDetailDTO.setSkuQty(qty);
            //
            tallyDetailDTO.setSkuCode(skuLotDTO.getSkuCode());
            SkuDTO skuDTO = skuDTOList.stream().filter(a -> a.getCode().equalsIgnoreCase(skuLotDTO.getSkuCode())).findFirst().orElse(null);
            if (StringUtils.isEmpty(skuDTO)) {
                throw new BaseException(BaseBizEnum.TIP, "商品不存在");
            }
            tallyDetailDTO.setSkuName(skuDTO.getName());
            SkuUpcDTO skuUpcDTO = skuUpcDTOList.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(skuLotDTO.getSkuCode()))
                    .filter(a -> a.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus()))
                    .findFirst().orElse(null);
            if (StringUtils.isEmpty(skuUpcDTO)) {
                throw new BaseException(BaseBizEnum.TIP, "商品条码不存在");
            }
            tallyDetailDTO.setUpcCode(skuUpcDTO.getUpcCode());
            tallyDetailDTOList.add(tallyDetailDTO);
        });
        return tallyDetailDTOList;
    }

    /**
     * @param dtoList
     * @return java.util.List<com.dt.domain.bill.dto.PackageDTO>
     * @author: WuXian
     * description:  包裹分析
     * create time: 2022/1/14 16:48
     */
    private List<PackageDTO> buildFilterPack(List<PackageDTO> dtoList) {
        //无效包裹状态码
        List<String> cancelStatusList = Arrays.asList(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode(),
                PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode(),
                PackEnum.STATUS.PART_ASSIGN_STATUS.getCode());
        List<PackageDTO> packageDTOList = dtoList.stream().filter(a -> !cancelStatusList.contains(a.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packageDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到有效汇总的包裹,不能新增理货报告");
        }
        //待汇总或已复核的包裹状态码
        List<String> waitCollectAndCheckStatus = Arrays.asList(PackEnum.STATUS.CREATE_STATUS.getCode(),
                PackEnum.STATUS.PRETREATMENT_FAIL.getCode(),
                PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode(),
                PackEnum.STATUS.ASSGIN_STOCK_STATUS.getCode(),
                PackEnum.STATUS.ASSGIN_LOCATION_STATUS.getCode(),
                PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode(),
                PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode(),
                PackEnum.STATUS.UNENOUGH_STOCK_STATUS.getCode(),
                PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        packageDTOList = dtoList.stream().filter(a -> waitCollectAndCheckStatus.contains(a.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(packageDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "存在待汇总或复核开始的包裹,不能新增理货报告");
        }
        //已汇总，拣选开始，拣选结束
        return dtoList.stream().filter(a -> Arrays.asList(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode(),
                PackEnum.STATUS.PICK_BEGIN_STATUS.getCode(),
                PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode()).contains(a.getStatus())).collect(Collectors.toList());
    }

    /**
     * @param packageDTOList
     * @param allocationOrderDTOS
     * @return java.util.List<com.dt.domain.bill.dto.AllocationOrderDTO>
     * @author: WuXian
     * description:  过滤无效的分配记录
     * create time: 2022/1/14 16:45
     */
    private List<AllocationOrderDTO> buildFilterAllocationOrder(List<PackageDTO> packageDTOList, List<AllocationOrderDTO> allocationOrderDTOS) {
        List<AllocationOrderDTO> allocationOrderDTOList = new ArrayList<>();
        for (PackageDTO packageDTO : packageDTOList) {
            List<AllocationOrderDTO> allocationOrderDTOS1 = allocationOrderDTOS.stream()
                    .filter(a -> a.getWaveCode().equalsIgnoreCase(packageDTO.getWaveCode()))
                    .filter(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode())).collect(Collectors.toList());
            allocationOrderDTOList.addAll(allocationOrderDTOS1);
        }
        if (CollectionUtils.isEmpty(allocationOrderDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到分配记录,不能新增理货报告");
        }
        return allocationOrderDTOList;
    }
}
