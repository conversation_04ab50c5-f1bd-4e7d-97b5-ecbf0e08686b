package com.dt.platform.wms.client.rs;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.SourceCodeScanTypeEnum;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.component.common.enums.base.*;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.bill.MessageMqStatusEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.pre.BillLogTypeEnum;
import com.dt.component.common.enums.rs.*;
import com.dt.component.common.enums.shelf.ShelfMarkDetailEnum;
import com.dt.component.common.enums.shelf.ShelfMarkEnum;
import com.dt.component.common.enums.shelf.ShelfStatusEnum;
import com.dt.component.common.enums.shelf.ShelfTypeEnum;
import com.dt.component.common.enums.sku.*;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.msg.StockOperationMessage;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.contLog.ContainerLogDTO;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.base.param.*;
import com.dt.domain.bill.client.rs.bo.SalesReturnOrderBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.rs.*;
import com.dt.domain.bill.dto.sourceCode.OutSourceCodeDTO;
import com.dt.domain.bill.param.AllocationOrderParam;
import com.dt.domain.bill.param.PackageParam;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.domain.bill.param.rs.*;
import com.dt.domain.bill.param.sourceCode.OutSourceCodeParam;
import com.dt.platform.utils.*;
import com.dt.platform.wms.biz.IBusinessLogBiz;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.param.SkuLotCheckAndFormatParam;
import com.dt.platform.wms.dto.rs.*;
import com.dt.platform.wms.dto.wave.SkuLotAndStockDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.contLog.IRemoteContainerLogClient;
import com.dt.platform.wms.integration.impl.CallUnderOtherContext;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.rs.*;
import com.dt.platform.wms.integration.sourceCode.IRemoteOutSourceCodeClient;
import com.dt.platform.wms.param.rs.SalesReturnInspectBizParam;
import com.dt.platform.wms.param.rs.SalesReturnInspectExpressDetailBizParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.dt.platform.utils.ListUtil.merge;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class SalesReturnInspectBizClientImpl implements ISalesReturnInspectBizClient {

    @Resource
    private IRemoteOpExceptionClient remoteOpExceptionClient;

    @Resource
    private IRemoteOpExceptionReviewClient remoteOpExceptionReviewClient;

    @Resource
    private IRemoteMessageClient remoteMessageClient;

    @Resource
    private IRemoteSpecialLocationClient remoteSpecialLocationClient;

    @Resource
    private IRemoteSalesReturnInspectClient remoteSalesReturnInspectClient;

    @Resource
    private IRemoteSalesReturnInspectDetailClient remoteSalesReturnInspectDetailClient;

    @Resource
    IRemoteWorkBenchClient remoteWorkBenchClient;

    @Resource
    IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Resource
    IRemoteSalesReturnOrderDetailClient remoteSalesReturnOrderDetailClient;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    IBusinessLogBiz businessLogBiz;

    @Resource
    private IRemoteBillLogClient remoteBillLogClient;

    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private IRemoteContainerClient remoteContainerClient;

    @Resource
    IRemoteContainerLogClient remoteContainerLogClient;

    @Resource
    private IRemoteOutSourceCodeClient remoteOutSourceCodeClient;

    @Resource
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    private IRemotePackageClient remotePackageClient;

    @Resource
    private IRemoteAllocationOrderClient remoteAllocationOrderClient;

    @Resource
    private ISkuLotBiz skuLotBiz;

    @Autowired
    private IRemoteErpCargoConfigClient remoteErpCargoConfigClient;

    @Autowired
    private WmsOtherConfig wmsOtherConfig;

    @Override
    public Result<Page<SalesReturnInspectBizDTO>> getPage(SalesReturnInspectBizParam returnInspectBizParam) {
        //运单查询在明细
        if (!CollectionUtils.isEmpty(returnInspectBizParam.getExpressNoList()) || !CollectionUtils.isEmpty(returnInspectBizParam.getSalesReturnOrderNoList())) {
            SalesReturnInspectDetailParam salesReturnInspectDetailParam = new SalesReturnInspectDetailParam();
            salesReturnInspectDetailParam.setExpressNoList(returnInspectBizParam.getExpressNoList());
            salesReturnInspectDetailParam.setInspectOrderNoList(returnInspectBizParam.getInspectOrderNoList());
            salesReturnInspectDetailParam.setSalesReturnOrderNoList(returnInspectBizParam.getSalesReturnOrderNoList());
            List<SalesReturnInspectDetailDTO> salesReturnInspectDetailDTOList = remoteSalesReturnInspectDetailClient.getList(salesReturnInspectDetailParam);
            if (CollectionUtils.isEmpty(salesReturnInspectDetailDTOList)) {
                returnInspectBizParam.setInspectOrderNo("---------------");
            } else {
                returnInspectBizParam.setInspectOrderNoList(salesReturnInspectDetailDTOList.stream().map(SalesReturnInspectDetailDTO::getInspectOrderNo).distinct().collect(Collectors.toList()));
            }
        }
        SalesReturnInspectParam salesReturnInspectParam = ConverterUtil.convert(returnInspectBizParam, SalesReturnInspectParam.class);
        Page<SalesReturnInspectDTO> returnExtraDTOPage = remoteSalesReturnInspectClient.getPage(salesReturnInspectParam);
        Page<SalesReturnInspectBizDTO> result = ConverterUtil.convertPage(returnExtraDTOPage, SalesReturnInspectBizDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<SalesReturnInspectBizDTO> getDetail(SalesReturnInspectBizParam returnInspectBizParam) {
        SalesReturnInspectParam salesReturnInspectParam = ConverterUtil.convert(returnInspectBizParam, SalesReturnInspectParam.class);
        SalesReturnInspectDTO salesReturnInspectDTO = remoteSalesReturnInspectClient.get(salesReturnInspectParam);
        if (salesReturnInspectDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "未找到质检记录");
        }
        SalesReturnInspectBizDTO salesReturnInspectBizDTO = ConverterUtil.convert(salesReturnInspectDTO, SalesReturnInspectBizDTO.class);
        return Result.success(salesReturnInspectBizDTO);
    }

    @Override
    public Result<Boolean> scanWorkbenchByInspect(SalesReturnInspectBizParam returnInspectBizParam) {
        WorkBenchParam workBenchParam = new WorkBenchParam();
        workBenchParam.setCode(returnInspectBizParam.getWorkbenchCode());
        List<WorkBenchDTO> workBenchDTOList = remoteWorkBenchClient.getList(workBenchParam);
        if (CollectionUtils.isEmpty(workBenchDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "当前质检台不存在,请核查");
        }
        WorkBenchDTO workBenchDTO = workBenchDTOList.get(0);
        if (!Objects.equals(workBenchDTO.getType(), WorkBenchTypeEnum.XT_RECEIVE.getType())) {
            throw new BaseException(BaseBizEnum.TIP, "当前质检台类型错误,请核查");
        }
        if (Objects.equals(workBenchDTO.getStatus(), WorkBenchStatusEnum.DISABLE.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, "当前质检台状态为禁用,请核查");
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<String> scanContByInspect(SalesReturnInspectBizParam returnInspectBizParam) {
        String inspectOrderNo = "";
        String syncKey = CurrentRouteHolder.getWarehouseCode() + returnInspectBizParam.getContCode();
        RLock lock = redissonClient.getLock("dt_wms_st_inspect_cont_lock:" + syncKey);
        Boolean isLock = false;
        try {
            isLock = lock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BaseException(BaseBizEnum.TIP, "操作太快了,请稍后重试");
            }
            ContainerDTO containerDTO = remoteContainerClient.queryByCode(returnInspectBizParam.getContCode());
            if (containerDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器不存在,请核查");
            }
            if (Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.DISABLE.getValue())) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器禁用,请核查");
            }
            if (Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.OCCUPY.getValue())) {
                if (!Objects.equals(containerDTO.getOccupyType(), ContainerTypeEnum.XT_RECEIVE.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "当前容器非销退入库流程占用,不可用,请核查");
                }
                SalesReturnInspectParam salesReturnInspectParam = new SalesReturnInspectParam();
                salesReturnInspectParam.setInspectOrderNo(containerDTO.getOccupyNo());
                List<SalesReturnInspectDTO> returnInspectDTOList = remoteSalesReturnInspectClient.getList(salesReturnInspectParam);
                if (CollectionUtils.isEmpty(returnInspectDTOList)) {
                    inspectOrderNo = containerDTO.getOccupyNo();
                } else {
                    if (returnInspectDTOList.stream().filter(a -> !a.getStatus().equals(SalesReturnInspectStatusEnum.OVER.getCode())).count() > 1) {
                        throw new BaseException(BaseBizEnum.TIP, "当前容器找到多个操作中的销退质检单,请核查");
                    }
                    SalesReturnInspectDTO salesReturnInspectDTO = returnInspectDTOList.get(0);
                    if (Objects.equals(salesReturnInspectDTO.getStatus(), SalesReturnInspectStatusEnum.COMPLETE.getCode())) {
                        throw new BaseException(BaseBizEnum.TIP, "当前容器已完成,不允许使用,请核查");
                    }
                    inspectOrderNo = salesReturnInspectDTO.getInspectOrderNo();
                }
            } else {
                inspectOrderNo = remoteSeqRuleClient.findSequence(SeqEnum.ST_ZJ_000001);
                //绑定容器
                //1.扫描占用容器
                remoteContainerClient.modifyContainerOccupy(inspectOrderNo, containerDTO.getCode(),
                        ContainerTypeEnum.XT_RECEIVE.getCode(), ContainerStatusEnum.OCCUPY.getValue(),
                        ContainerTypeEnum.XT_RECEIVE.getName() + ":" + inspectOrderNo);

                businessLogBiz.saveContLog(
                        CurrentRouteHolder.getWarehouseCode(),
                        containerDTO.getCode(),
                        WorkBenchTypeEnum.XT_RECEIVE.getType(),
                        inspectOrderNo,
                        CurrentUserHolder.getUserName(),
                        String.format("销退质检单:%s绑定容器:%s", inspectOrderNo, containerDTO.getCode()));
            }
        } catch (Exception e) {
            e.printStackTrace();
            String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "操作太快了,请稍后重试!!!" : e.getMessage();
            throw new BaseException(BaseBizEnum.TIP, errorMsg);
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
        return Result.success(inspectOrderNo);
    }

    @Override
    public Result<List<SalesReturnInspectScanExpressBizDTO>> scanExpressByInspect(SalesReturnInspectBizParam returnInspectBizParam) {
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
//        salesReturnOrderParam.setReverseExpressNo(returnInspectBizParam.getExpressNo());
        salesReturnOrderParam.setReverseExpressNoList(remoteSalesReturnOrderClient.getExtraExpressNoList(returnInspectBizParam.getExpressNo()));
        List<SalesReturnOrderDTO> salesReturnOrderDTOList = remoteSalesReturnOrderClient.getList(salesReturnOrderParam);
        if (CollectionUtils.isEmpty(salesReturnOrderDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "当前运单未关联销退单号,请核查");
        }
        List<Integer> salesOrderOtherStatusList = Arrays.asList(RSOrderStatusEnum.CREATED.getCode(), RSOrderStatusEnum.REGISTERED.getCode(), RSOrderStatusEnum.REJECT.getCode());
        if (salesReturnOrderDTOList.stream().allMatch(a -> salesOrderOtherStatusList.contains(a.getStatus()))) {
            throw new BaseException(BaseBizEnum.TIP, String.format("当前运单号:%s,不可质检,请核查", returnInspectBizParam.getExpressNo()));
        }
        //可质检的状态码(交接完成,质检中) 可以质检
        List<Integer> salesOrderStatusList = Arrays.asList(RSOrderStatusEnum.HANDOVER.getCode(), RSOrderStatusEnum.CHECKING.getCode());
        if (salesReturnOrderDTOList.stream().noneMatch(a -> salesOrderStatusList.contains(a.getStatus()))) {
            throw new BaseException(BaseBizEnum.TIP, String.format("当前运单号:%s,已完成质检,请核查", returnInspectBizParam.getExpressNo()));
        }
        List<SalesReturnOrderDTO> returnOrderDTOList = salesReturnOrderDTOList.stream()
                .filter(a -> salesOrderStatusList.contains(a.getStatus())).collect(Collectors.toList());
        List<SalesReturnInspectScanExpressBizDTO> inspectScanExpressBizDTOList = new ArrayList<>();
        returnOrderDTOList.forEach(it -> {
            SalesReturnInspectScanExpressBizDTO salesReturnInspectScanExpressBizDTO = new SalesReturnInspectScanExpressBizDTO();
            salesReturnInspectScanExpressBizDTO.setSalesReturnOrderNo(it.getSalesReturnOrderNo());
            salesReturnInspectScanExpressBizDTO.setCargoCode(it.getCargoCode());
            salesReturnInspectScanExpressBizDTO.setCargoName(it.getCargoName());
            salesReturnInspectScanExpressBizDTO.setSaleShopId(it.getSaleShopId());
            salesReturnInspectScanExpressBizDTO.setSaleShopName(it.getSaleShopName());
            if (!RSBillSourceEnum.OMS.getCode().equals(it.getBillSource())) {
                salesReturnInspectScanExpressBizDTO.setBillSourceDesc(RSBillSourceEnum.desc(it.getBillSource()));
            }
            salesReturnInspectScanExpressBizDTO.setInstructionType(it.salesReturnOrderExtraDTO().getInstructionType());
            salesReturnInspectScanExpressBizDTO.setInstructionType(it.salesReturnOrderExtraDTO().getInstructionType());
            salesReturnInspectScanExpressBizDTO.setInstructionTypeDesc(RSAdditionalOrderEnum.desc(it.salesReturnOrderExtraDTO().getInstructionType()));
            salesReturnInspectScanExpressBizDTO.setReturnReasonDesc(RSReturnReasonEnum.desc(it.getReturnReason()));
            salesReturnInspectScanExpressBizDTO.setHighRiskCustomDesc(it.highRiskCustomDesc());
            salesReturnInspectScanExpressBizDTO.setHighRiskPackageDesc(it.highRiskPackageDesc());
            inspectScanExpressBizDTOList.add(salesReturnInspectScanExpressBizDTO);
        });
        return Result.success(inspectScanExpressBizDTOList);
    }

    private boolean receiveLess(List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        Map<String, BigDecimal> detailExpQtyGroup = salesReturnOrderDetailDTOList.stream()
                .collect(Collectors.groupingBy(SalesReturnOrderDetailDTO::getSkuCode, Collectors.mapping(SalesReturnOrderDetailDTO::getExpectQty, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, BigDecimal> receiveQtyGroup = salesReturnOrderReceiveDTOList.stream()
                .collect(Collectors.groupingBy(SalesReturnOrderReceiveDTO::getSkuCode, Collectors.mapping(SalesReturnOrderReceiveDTO::getQty, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        for (String skuCode : detailExpQtyGroup.keySet()) {
            BigDecimal expQty = detailExpQtyGroup.get(skuCode);
            BigDecimal receiveQty = receiveQtyGroup.getOrDefault(skuCode, BigDecimal.ZERO);
            if (expQty.compareTo(receiveQty) > 0) {
                log.error("{} {} {} {}", salesReturnOrderDetailDTOList.get(0).getSalesReturnOrderNo(), skuCode, expQty, receiveQty);
                return true;
            }
        }

        return false;
    }

    private void receiveLessCheckOpException(SalesReturnOrderDTO salesReturnOrderDTO) {
        if (!RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource())) return;
        if (!RSReturnTypeEnum.CUSTOMER_RETURNS.getCode().equals(salesReturnOrderDTO.getReturnType())) return;

        OpExceptionParam param = new OpExceptionParam();
        param.setAbnormalType(OpAbnormalTypeEnum.T_1007.getCode());
        param.setMailMo(salesReturnOrderDTO.getReverseExpressNo());
        List<OpExceptionDTO> list = remoteOpExceptionClient.getList(param);

        for (OpExceptionDTO opExceptionDTO : list) {
            OpExceptionReviewParam opExceptionReviewParam = new OpExceptionReviewParam();
            opExceptionReviewParam.setAbnormalOrderNo(opExceptionDTO.getAbnormalOrderNo());
            for (OpExceptionReviewDTO opExceptionReviewDTO : remoteOpExceptionReviewClient.getList(opExceptionReviewParam)) {
                if (OpInstructionTypeEnum.RECEIVE_CONTINUE_INSPECTE.getCode().equalsIgnoreCase(opExceptionReviewDTO.getInstructionType())) {
                    return;
                }
            }
        }

        throw ExceptionUtil.exceptionWithMessage("淘天少货质检，需先提报商品少件异常且收到继续质检指令");

    }

    /**
     * 判断是否质检通过
     */
    private Result<Integer> completeExpressCheckInspectResult(SalesReturnInspectBizParam inspectBizParam,
                                                              SalesReturnOrderDTO salesReturnOrderDTO,
                                                              List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList,
                                                              Boolean returnAllowEntry) {

        if (!RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource())) return Result.success();

        List<String> skuCodeList = salesReturnOrderDetailDTOList.stream().map(SalesReturnOrderDetailDTO::getSkuCode)
                .filter(StrUtil::isNotBlank)
                .distinct().collect(Collectors.toList());

        Set<String> set = new HashSet<>();
        if (Objects.equals(salesReturnOrderDTO.getTaxType(), TaxTypeEnum.TYPE_BONDED_TAX.getCode())) {
            Long betweenSecond = DateUtil.betweenMs(new Date(salesReturnOrderDTO.getClearanceTime()), new Date()) / 1000;
            if (betweenSecond - 30 * CommonConstantUtil.SECONDS_PER_DAY >= 0) {
                set.add("清关超30天");
                set.add("存在不可二次入区品");
                inspectBizParam.getPackList().forEach(it -> it.setInspectionResult(RSInspectionEnum.FAIL.getCode()));
            }

            if (!returnAllowEntry) {
                inspectBizParam.getPackList().forEach(it -> it.setInspectionResult(RSInspectionEnum.FAIL.getCode()));
                set.add("存在不可二次入区品");
            }

            if (inspectBizParam.getPackList().stream().anyMatch(it -> ObjectUtil.equals(it.getSecondEntry(), RSSecondEntryEnum.NO.getCode()))) {
                set.add("存在不可二次入区品");
            }
            inspectBizParam.getPackList().stream().filter(it -> ObjectUtil.equals(it.getSecondEntry(), RSSecondEntryEnum.NO.getCode()))
                    .forEach(it -> it.setInspectionResult(RSInspectionEnum.FAIL.getCode()));

        }

        if (inspectBizParam.getPackList().stream()
                .anyMatch(a -> Objects.equals(a.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel()))) {
            set.add("存在次品");
        }
        inspectBizParam.getPackList().stream()
                .filter(a -> Objects.equals(a.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel()))
                .forEach(it -> it.setInspectionResult(RSInspectionEnum.FAIL.getCode()));

        skuCodeList.forEach(skuCode -> {
            List<SalesReturnOrderDetailDTO> detailDTOS = salesReturnOrderDetailDTOList.stream().filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode)).collect(Collectors.toList());
            List<SalesReturnInspectExpressDetailBizParam> detailBizParamList = inspectBizParam.getPackList().stream()
                    .filter(a -> a.getSkuCode().equals(skuCode)).collect(Collectors.toList());
            BigDecimal commitQty = detailBizParamList.stream().map(SalesReturnInspectExpressDetailBizParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal expQty = detailDTOS.stream().map(SalesReturnOrderDetailDTO::getExpectQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (expQty.compareTo(commitQty) > 0) {
                set.add("实收数量与应收数量不一致");
            }
        });

        if (StrUtil.isBlank(inspectBizParam.getAbnormalCauseMessage()) && CollectionUtil.isNotEmpty(set)) {
            inspectBizParam.setAbnormalCauseMessage(String.join("/", set));
        }

        // 灰度
        if (wmsOtherConfig.getPartReturnCanary() && !wmsOtherConfig.getPartReturnCanaryCargoCodeList().contains(salesReturnOrderDTO.getCargoCode())) {
            if (CollectionUtil.isNotEmpty(set)) {
                inspectBizParam.getPackList().forEach(it -> it.setInspectionResult(RSInspectionEnum.FAIL.getCode()));
            }
        }

        if (ObjectUtil.equals(inspectBizParam.getInspectionResult(), RSInspectionEnum.PASS.getCode())) {
            if (CollectionUtil.isNotEmpty(set)) {
                if (inspectBizParam.getPackList().stream().allMatch(it -> RSInspectionEnum.FAIL.getCode().equals(it.getInspectionResult()))) {
                    return Result.fail(BaseBizEnum.TIP_OTHER.getValue(), String.join("/", set), RSInspectionEnum.FAIL.getCode());
                } else {
                    if (TaxTypeEnum.TYPE_DUTY_TAX.getCode().equalsIgnoreCase(salesReturnOrderDTO.getTaxType())) {
                        return Result.fail(BaseBizEnum.TIP_OTHER.getValue(), String.join("/", set), RSInspectionEnum.FAIL.getCode());
                    }
                    return Result.fail(BaseBizEnum.TIP_OTHER.getValue(), String.join("/", set), RSInspectionEnum.PART_PASS.getCode());
                }
            }
        }

        if (ObjectUtil.equals(inspectBizParam.getInspectionResult(), RSInspectionEnum.PART_PASS.getCode())) {
            if (inspectBizParam.getPackList().stream().allMatch(it -> RSInspectionEnum.FAIL.getCode().equals(it.getInspectionResult()))) {
                throw ExceptionUtil.exceptionWithMessage("当前单据不可以部分质检通过");
            }
        }

        if (ObjectUtil.equals(inspectBizParam.getInspectionResult(), RSInspectionEnum.FAIL.getCode())) {
            if (inspectBizParam.getPackList().stream().anyMatch(it -> ObjectUtil.notEqual(it.getInspectionResult(), RSInspectionEnum.FAIL.getCode()))) {
                if (TaxTypeEnum.TYPE_BONDED_TAX.getCode().equalsIgnoreCase(salesReturnOrderDTO.getTaxType())) {
                    throw ExceptionUtil.exceptionWithMessage("当前单据存在部分质检通过明细");
                }
            }
        }

        return Result.success();
    }

    private void completeExpressCheckContainer(SalesReturnInspectBizParam inspectBizParam) {
        ContainerDTO containerDTO = remoteContainerClient.queryByCode(inspectBizParam.getContCode());
        if (containerDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "当前容器不存在,请核查");
        }
        if (Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.DISABLE.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, "当前容器禁用,请核查");
        }

        String inspectOrderNo;
        if (Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.OCCUPY.getValue())) {
            if (!Objects.equals(containerDTO.getOccupyType(), ContainerTypeEnum.XT_RECEIVE.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器非销退入库流程占用,不可用,请核查");
            }
            if (!Objects.equals(containerDTO.getOccupyNo(), inspectBizParam.getInspectOrderNo())) {
                inspectOrderNo = containerDTO.getOccupyNo();
                inspectBizParam.setInspectOrderNo(inspectOrderNo);
            }
        } else {
            inspectOrderNo = remoteSeqRuleClient.findSequence(SeqEnum.ST_ZJ_000001);
            //绑定容器
            //1.扫描占用容器
            remoteContainerClient.modifyContainerOccupy(inspectOrderNo, containerDTO.getCode(),
                    ContainerTypeEnum.XT_RECEIVE.getCode(), ContainerStatusEnum.OCCUPY.getValue(),
                    ContainerTypeEnum.XT_RECEIVE.getName() + ":" + inspectOrderNo);

            businessLogBiz.saveContLog(
                    CurrentRouteHolder.getWarehouseCode(),
                    containerDTO.getCode(),
                    WorkBenchTypeEnum.XT_RECEIVE.getType(),
                    inspectOrderNo,
                    CurrentUserHolder.getUserName(),
                    String.format("销退质检单:%s绑定容器:%s", inspectOrderNo, containerDTO.getCode()));
            inspectBizParam.setInspectOrderNo(inspectOrderNo);
        }
    }

    @Override
    public Result<SalesReturnInspectResultDTO> completeExpressV2(SalesReturnInspectBizParam returnInspectBizParam) {
        log.info("{}", JSONUtil.toJsonStr(returnInspectBizParam));
        //获取销退单
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
        SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
        if (salesReturnOrderDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "未找到销退单,请核查");
        }

        RSBillSourceEnum billSource = RSBillSourceEnum.fromInt(salesReturnOrderDTO.getBillSource());
        switch (billSource) {
            case DY_MARKET:
                return completeExpressForDyMarket(returnInspectBizParam);
            case MALL_DIRECT:
            case MALL_PLATFORM:
            case OMS:
                return completeExpress(returnInspectBizParam);
            default:
                throw ExceptionUtil.SYSTEM_ERROR;
        }
    }

    private Result<SalesReturnInspectResultDTO> completeExpressForDyMarket(SalesReturnInspectBizParam returnInspectBizParam) {
        String syncKey = CurrentRouteHolder.getWarehouseCode() + returnInspectBizParam.getExpressNo();
        RLock lock = redissonClient.getLock("dt_wms_st_inspect_express_lock:" + syncKey);
        Boolean isLock = false;
        try {
            isLock = lock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BaseException(BaseBizEnum.TIP, "操作太快了,请稍后重试");
            }
            String warehouseCode = CurrentRouteHolder.getWarehouseCode();
            //获取销退单
            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            salesReturnOrderParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
            SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
            if (salesReturnOrderDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "未找到销退单,请核查");
            }
            //获取销退单明细
            SalesReturnOrderDetailParam salesReturnOrderDetailParam = new SalesReturnOrderDetailParam();
            salesReturnOrderDetailParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
            List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList = remoteSalesReturnOrderDetailClient.getList(salesReturnOrderDetailParam);
            if (CollectionUtils.isEmpty(salesReturnOrderDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "未找到销退单商品明细,请核查,请核查");
            }

            List<String> skuCodeList = salesReturnOrderDetailDTOList.stream().map(SalesReturnOrderDetailDTO::getSkuCode)
                    .filter(StrUtil::isNotBlank)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(skuCodeList)) throw ExceptionUtil.exceptionWithMessage("收货明细不能为空");

            List<Integer> salesOrderStatusList = Arrays.asList(RSOrderStatusEnum.HANDOVER.getCode(), RSOrderStatusEnum.CHECKING.getCode());
            if (!salesOrderStatusList.contains(salesReturnOrderDTO.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, "当前销退单状态,不可操作完成质检,请核查");
            }

            //校验整体数量不能超收
            checkSkuOver(salesReturnOrderDetailDTOList, returnInspectBizParam.getPackList());

            skuCodeList.stream().forEach(skuCode -> {
                List<SalesReturnOrderDetailDTO> detailDTOS = salesReturnOrderDetailDTOList.stream().filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode)).collect(Collectors.toList());
                List<SalesReturnInspectExpressDetailBizParam> detailBizParamList = returnInspectBizParam.getPackList().stream()
                        .filter(a -> a.getSkuCode().equals(skuCode)).collect(Collectors.toList());
                String upcCode = detailDTOS.stream().map(SalesReturnOrderDetailDTO::getUpcCode).findFirst().orElse(skuCode);
                BigDecimal commitQty = detailBizParamList.stream().map(SalesReturnInspectExpressDetailBizParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal expQty = detailDTOS.stream().map(SalesReturnOrderDetailDTO::getExpectQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (expQty.compareTo(commitQty) < 0) {
                    throw new BaseException(BaseBizEnum.TIP, "商品:" + upcCode + "超收,实收" + commitQty + "件应收" + expQty + "件请核查");
                }
            });
            //check效期 效期商品
            checkSkuPeriod(salesReturnOrderDTO, salesReturnOrderDetailDTOList, returnInspectBizParam.getPackList());
            RpcContextUtil.setWarehouseCode(warehouseCode);

            //获取销退质检单
            SalesReturnInspectParam salesReturnInspectParam = new SalesReturnInspectParam();
            salesReturnInspectParam.setInspectOrderNo(returnInspectBizParam.getInspectOrderNo());
            SalesReturnInspectDTO salesReturnInspectDTO = remoteSalesReturnInspectClient.get(salesReturnInspectParam);
            if (salesReturnInspectDTO != null && !Objects.equals(salesReturnInspectDTO.getStatus(), SalesReturnInspectStatusEnum.CREATE.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "当前销退单质检单状态,不可操作,请核查");
            }
            //组装明细
            if (salesReturnInspectDTO == null) {
                salesReturnInspectDTO = buildSalesReturnInspectDTO(returnInspectBizParam, salesReturnOrderDTO);
            } else {
                if (!Objects.equals(salesReturnOrderDTO.getRealWarehouseCode(), salesReturnInspectDTO.getRealWarehouseCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "不同实体仓包裹不允许混放,请更换容器");
                }
                if (!Objects.equals(salesReturnOrderDTO.getTaxType(), salesReturnInspectDTO.getTaxType())) {
                    throw new BaseException(BaseBizEnum.TIP, "不同贸易类型包裹不允许混放,请更换容器");
                }
                salesReturnInspectDTO.setPackageCount(salesReturnInspectDTO.getPackageCount() + 1);
            }
            //质检单质检时间 和 状态
            Long inspectTime = System.currentTimeMillis();
            salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CHECK_END.getCode());
            salesReturnOrderDTO.setInspectionTime(inspectTime);
            salesReturnOrderDTO.setInspectionResult(returnInspectBizParam.getInspectionResult());
            SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
            salesReturnOrderExtraDTO.setInspectionBy(CurrentUserHolder.getUserName());
            salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
            salesReturnOrderDTO.setContainer(salesReturnInspectDTO.getContCode());
            salesReturnOrderDTO.setAbnormalCause(returnInspectBizParam.getAbnormalCauseMessage());
            //组装质检明细
            SalesReturnInspectDetailDTO salesReturnInspectDetailDTO = buildSalesReturnInspectDetailDTO(salesReturnInspectDTO, returnInspectBizParam, salesReturnOrderDTO);
            //组装收货明细
            List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList = buildSalesReturnOrderReceiveDTOList(salesReturnInspectDTO, returnInspectBizParam, salesReturnOrderDTO);
            if (CollectionUtil.isEmpty(salesReturnOrderReceiveDTOList))
                throw ExceptionUtil.exceptionWithMessage("收货信息不能为空");


            SalesReturnOrderDTO returnOrderDTO = remoteSalesReturnOrderClient.buildExtraJson(salesReturnOrderDTO, salesReturnOrderDTO.getInspectionTime());

            salesReturnInspectDTO.setSalesReturnOrderDTO(returnOrderDTO);
            salesReturnInspectDetailDTO.setSalesReturnStatus(returnOrderDTO.getStatus());
            salesReturnInspectDTO.setSalesReturnInspectDetailDTO(salesReturnInspectDetailDTO);

            List<MessageMqDTO> messageMqDTOList = new ArrayList<>();
            messageMqDTOList.addAll(remoteSalesReturnOrderClient.messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_RECEIVE_CALLBACK_ORIGIN));
            log.info("salesReturnInspectDTO:{}", JSONUtil.toJsonStr(salesReturnInspectDTO));
            // 校验防伪码等信息
            List<SkuTraceCode> skuTraceCodeList = CallUnderOtherContext.execute(() -> skuTraceCodeList(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());
            List<SkuSnCode> skuSnCodeList = CallUnderOtherContext.execute(() -> skuSnCodeList(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());
            List<SkuSourceCode> sourceCodeList = CallUnderOtherContext.execute(() -> sourceCodeList(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());
            checkTraceCode(skuTraceCodeList, salesReturnOrderReceiveDTOList);
            // 校验SN
            checkSnCode(skuSnCodeList, salesReturnOrderReceiveDTOList);
            // 校验溯源码
            checkSourceCode(sourceCodeList, salesReturnOrderReceiveDTOList);

            RpcContextUtil.setWarehouseCode(warehouseCode);

            // 生成批次信息【注意从这里开始品换成新的品】
            genSkuLotInfoForDyMarket(returnOrderDTO, salesReturnOrderReceiveDTOList);
            // 合并
            salesReturnOrderReceiveDTOList = merge(salesReturnOrderReceiveDTOList,
                    it -> StrUtil.join("#", it.getSkuCode(), it.getSkuLotNo(), it.getSourceCode(), it.getSn(), it.getTraceCode(), it.getSkuQuality(), it.getInventoryType(), it.getSecondEntry()), new BiConsumer<SalesReturnOrderReceiveDTO, SalesReturnOrderReceiveDTO>() {
                @Override
                public void accept(SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO, SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO2) {
                    salesReturnOrderReceiveDTO.setQty(salesReturnOrderReceiveDTO.getQty().add(salesReturnOrderReceiveDTO2.getQty()));
                }
            });
            salesReturnInspectDTO.setSalesReturnOrderReceiveDTOList(salesReturnOrderReceiveDTOList);
            // 收货的品必须在销退单明细里面【必须在替换后执行】
            for (SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO : salesReturnOrderReceiveDTOList) {
                if (salesReturnOrderDetailDTOList.stream().noneMatch(salesReturnOrderDetailDTO -> salesReturnOrderDetailDTO.getSkuCode().equalsIgnoreCase(salesReturnOrderReceiveDTO.getSkuCode()))) {
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, salesReturnOrderReceiveDTO.getUpcCode(), "对应商品编码不在销退单明细中"));
                }
            }
            salesReturnInspectDTO.setSalesReturnOrderReceiveDTOList(salesReturnOrderReceiveDTOList);

            // 生成上架单
            List<ShelfDTO> shelfDTOList = shelfDTOListV2(returnOrderDTO, salesReturnOrderReceiveDTOList);
            if (CollectionUtil.isNotEmpty(shelfDTOList)) {
                salesReturnInspectDTO.setShelfDTOList(shelfDTOList);
                salesReturnInspectDTO.setShelfDetailDTOList(shelfDTOList.stream().flatMap(it -> it.getDetailList().stream()).collect(Collectors.toList()));

                // 库存处理
                MessageMqDTO messageMqDTO = new MessageMqDTO();
                messageMqDTO.setWarehouseCode(returnOrderDTO.getWarehouseCode());
                messageMqDTO.setCargoCode(returnOrderDTO.getCargoCode());
                messageMqDTO.setBillNo(returnOrderDTO.getSalesReturnOrderNo());
                messageMqDTO.setOperationType(MessageTypeEnum.OPERATION_SALE_RETURN_RECEIPT.getType());
                messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RS_ORDER.getType());
                messageMqDTO.setCreatedTime(System.currentTimeMillis());
                messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
                messageMqDTOList.add(messageMqDTO);
            }

            //计算回写：
            receiveWriteBack(salesReturnOrderDetailDTOList, salesReturnOrderReceiveDTOList);
            salesReturnInspectDTO.setSalesReturnOrderDetailDTOList(salesReturnOrderDetailDTOList);

            salesReturnInspectDTO.setMessageMqDTOList(messageMqDTOList);
            remoteSalesReturnInspectClient.commit(salesReturnInspectDTO);

            //记录日志
            BillLogDTO billLogDTO = new BillLogDTO();
            billLogDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
            billLogDTO.setBillType(BillLogTypeEnum.RS_SALES_ORDER.getType());
            billLogDTO.setOpBy(CurrentUserHolder.getUserName());
            billLogDTO.setOpDate(System.currentTimeMillis());
            billLogDTO.setOpContent("质检完成");

            BigDecimal avlQty = salesReturnOrderReceiveDTOList.stream().filter(a -> a.getSkuQuality().equals(SkuQualityEnum.SKU_QUALITY_AVL.getLevel())).map(SalesReturnOrderReceiveDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal damageQty = salesReturnOrderReceiveDTOList.stream().filter(a -> a.getSkuQuality().equals(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())).map(SalesReturnOrderReceiveDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

            billLogDTO.setOpRemark(String.format("通过,正品数量:%s,次品数量:%s", avlQty.intValue(), damageQty.intValue()));
            remoteBillLogClient.save(billLogDTO);

            remoteSalesReturnOrderClient.callback(messageMqDTOList);

            // 发送库存消息
            for (MessageMqDTO messageMqDTO : messageMqDTOList) {
                if (messageMqDTO.getOperationType().equalsIgnoreCase(MessageTypeEnum.OPERATION_SALE_RETURN_RECEIPT.getType())) {
                    try {
                        // stock operation
                        StockOperationMessage stockOperationMessage = new StockOperationMessage();
                        stockOperationMessage.setWarehouseCode(messageMqDTO.getWarehouseCode());
                        stockOperationMessage.setCargoCode(messageMqDTO.getCargoCode());
                        stockOperationMessage.setOperationType(messageMqDTO.getOperationType());
                        stockOperationMessage.setBillNo(messageMqDTO.getBillNo());
                        remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
                    } catch (Exception ignored) {
                    }
                }
            }

            SalesReturnInspectResultDTO salesReturnInspectResultDTO = new SalesReturnInspectResultDTO();
            salesReturnInspectResultDTO.setBillSource(salesReturnOrderDTO.getBillSource());
            return Result.success(salesReturnInspectResultDTO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof BaseException) {
                return Result.failWithMessage(e.getMessage());
            }
            return Result.failWithMessage("系统异常");
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    public Result<SalesReturnInspectResultDTO> completeExpress(SalesReturnInspectBizParam returnInspectBizParam) {
        log.info("{}", JSONUtil.toJsonStr(returnInspectBizParam));

        // 质检结果没传默认质检通过
        if (ObjectUtil.isEmpty(returnInspectBizParam.getInspectionResult())) {
            returnInspectBizParam.setInspectionResult(RSInspectionEnum.PASS.getCode());
        }

        String syncKey = CurrentRouteHolder.getWarehouseCode() + returnInspectBizParam.getExpressNo();
        RLock lock = redissonClient.getLock("dt_wms_st_inspect_express_lock:" + syncKey);
        Boolean isLock = false;
        try {
            isLock = lock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BaseException(BaseBizEnum.TIP, "操作太快了,请稍后重试");
            }
            String warehouseCode = CurrentRouteHolder.getWarehouseCode();
//            TODO 特殊码
            //获取销退单
            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            salesReturnOrderParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
            SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
            if (salesReturnOrderDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "未找到销退单,请核查");
            }
            //获取销退单明细
            SalesReturnOrderDetailParam salesReturnOrderDetailParam = new SalesReturnOrderDetailParam();
            salesReturnOrderDetailParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
            List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList = remoteSalesReturnOrderDetailClient.getList(salesReturnOrderDetailParam);
            if (CollectionUtils.isEmpty(salesReturnOrderDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "未找到销退单商品明细,请核查,请核查");
            }

            List<String> skuCodeList = salesReturnOrderDetailDTOList.stream().map(SalesReturnOrderDetailDTO::getSkuCode)
                    .filter(StrUtil::isNotBlank)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(skuCodeList)) throw ExceptionUtil.exceptionWithMessage("收货明细不能为空");

            // 校验或者切换容器、质检单
            completeExpressCheckContainer(returnInspectBizParam);

            Boolean returnAllowEntry = remoteErpCargoConfigClient.saleReturnAllowEntry(salesReturnOrderDTO.getRealWarehouseCode(), salesReturnOrderDTO.getCargoCode());

            // 判断是否质检通过
            Result<Integer> integerResult = completeExpressCheckInspectResult(returnInspectBizParam, salesReturnOrderDTO, salesReturnOrderDetailDTOList, returnAllowEntry);
            if (!integerResult.checkSuccess()) {
                SalesReturnInspectResultDTO data = new SalesReturnInspectResultDTO();
                data.setAbnormalCauseMessage(integerResult.getMessage());
                data.setInspectionResult(integerResult.getData());
                data.setInspectionResultDesc(RSInspectionEnum.desc(data.getInspectionResult()));
                if (RSInspectionEnum.PART_PASS.getCode().equals(integerResult.getData())) {
                    partInspectFailDetail(data, returnInspectBizParam);
                }
                receiveLess(data, returnInspectBizParam, salesReturnOrderDetailDTOList);
                return Result.fail(integerResult.getCode(), integerResult.getMessage(), data);
            }

            if (salesReturnOrderDTO.hasWarehouseRejectInstruction()) {
                throw ExceptionUtil.exceptionWithMessage("该运单只可操作拒收！");
            }

            List<Integer> salesOrderStatusList = Arrays.asList(RSOrderStatusEnum.HANDOVER.getCode(), RSOrderStatusEnum.CHECKING.getCode());
            if (!salesOrderStatusList.contains(salesReturnOrderDTO.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, "当前销退单状态,不可操作完成质检,请核查");
            }

            //校验整体数量不能超收
            checkSkuOver(salesReturnOrderDetailDTOList, returnInspectBizParam.getPackList());

            skuCodeList.stream().forEach(skuCode -> {
                List<SalesReturnOrderDetailDTO> detailDTOS = salesReturnOrderDetailDTOList.stream().filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode)).collect(Collectors.toList());
                List<SalesReturnInspectExpressDetailBizParam> detailBizParamList = returnInspectBizParam.getPackList().stream()
                        .filter(a -> a.getSkuCode().equals(skuCode)).collect(Collectors.toList());
                String upcCode = detailDTOS.stream().map(SalesReturnOrderDetailDTO::getUpcCode).findFirst().orElse(skuCode);
                BigDecimal commitQty = detailBizParamList.stream().map(SalesReturnInspectExpressDetailBizParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal expQty = detailDTOS.stream().map(SalesReturnOrderDetailDTO::getExpectQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (expQty.compareTo(commitQty) < 0) {
                    throw new BaseException(BaseBizEnum.TIP, "商品:" + upcCode + "超收,实收" + commitQty + "件应收" + expQty + "件请核查");
                }
            });
            //check效期 效期商品
            checkSkuPeriod(salesReturnOrderDTO, salesReturnOrderDetailDTOList, returnInspectBizParam.getPackList());
            RpcContextUtil.setWarehouseCode(warehouseCode);

            //获取销退质检单
            SalesReturnInspectParam salesReturnInspectParam = new SalesReturnInspectParam();
            salesReturnInspectParam.setInspectOrderNo(returnInspectBizParam.getInspectOrderNo());
            SalesReturnInspectDTO salesReturnInspectDTO = remoteSalesReturnInspectClient.get(salesReturnInspectParam);
            if (salesReturnInspectDTO != null && !Objects.equals(salesReturnInspectDTO.getStatus(), SalesReturnInspectStatusEnum.CREATE.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "当前销退单质检单状态,不可操作,请核查");
            }
            //组装明细
            if (salesReturnInspectDTO == null) {
                salesReturnInspectDTO = buildSalesReturnInspectDTO(returnInspectBizParam, salesReturnOrderDTO);
            } else {
                if (!Objects.equals(salesReturnOrderDTO.getRealWarehouseCode(), salesReturnInspectDTO.getRealWarehouseCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "不同实体仓包裹不允许混放,请更换容器");
                }
                if (!Objects.equals(salesReturnOrderDTO.getTaxType(), salesReturnInspectDTO.getTaxType())) {
                    throw new BaseException(BaseBizEnum.TIP, "不同贸易类型包裹不允许混放,请更换容器");
                }
                if (RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource()) &&
                        !Objects.equals(returnInspectBizParam.getInspectionResult(), salesReturnInspectDTO.getInspectionResult())) {
                    if (RSInspectionEnum.PART_PASS.getCode().equals(returnInspectBizParam.getInspectionResult())) {
                        // 暂时部分通过不校验
                    } else {
                        throw new BaseException(BaseBizEnum.TIP, "质检通过和质检不通过的包裹不允许混放,请更换容器");
                    }
                }
                salesReturnInspectDTO.setPackageCount(salesReturnInspectDTO.getPackageCount() + 1);
            }
            //质检单质检时间 和 状态
            Long inspectTime = System.currentTimeMillis();
            if (Objects.equals(salesReturnOrderDTO.getTaxType(), TaxTypeEnum.TYPE_BONDED_TAX.getCode())
                    && salesReturnOrderDTO.getClearanceTime() > 0L) {
                Long betweenSecond = DateUtil.betweenMs(new Date(salesReturnOrderDTO.getClearanceTime()), new Date(inspectTime)) / 1000;
                if (betweenSecond > 0) {
                    BigDecimal overDueDay = new BigDecimal(betweenSecond + "").divide(new BigDecimal("86400"), 10, BigDecimal.ROUND_HALF_UP);
                    if (overDueDay.compareTo(new BigDecimal("30")) > 0) {
                        salesReturnOrderDTO.setOverdue(RSOverdueEnum.OVERDUE.getCode());
                    } else {
                        salesReturnOrderDTO.setOverdue(RSOverdueEnum.NOT.getCode());
                    }
                }
            }
            salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CHECK_END.getCode());
            salesReturnOrderDTO.setInspectionTime(inspectTime);
            salesReturnOrderDTO.setInspectionResult(returnInspectBizParam.getInspectionResult());
            salesReturnOrderDTO.setContainer(salesReturnInspectDTO.getContCode());
            salesReturnOrderDTO.setAbnormalCause(returnInspectBizParam.getAbnormalCauseMessage());
            //组装质检明细
            SalesReturnInspectDetailDTO salesReturnInspectDetailDTO = buildSalesReturnInspectDetailDTO(salesReturnInspectDTO, returnInspectBizParam, salesReturnOrderDTO);
            //组装收货明细
            List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList = buildSalesReturnOrderReceiveDTOList(salesReturnInspectDTO, returnInspectBizParam, salesReturnOrderDTO);
            if (CollectionUtil.isEmpty(salesReturnOrderReceiveDTOList))
                throw ExceptionUtil.exceptionWithMessage("收货信息不能为空");

            SalesReturnOrderDTO returnOrderDTO = remoteSalesReturnOrderClient.buildExtraJson(salesReturnOrderDTO, salesReturnOrderDTO.getInspectionTime());

            salesReturnInspectDTO.setSalesReturnOrderDTO(returnOrderDTO);
            salesReturnInspectDetailDTO.setSalesReturnStatus(returnOrderDTO.getStatus());
            salesReturnInspectDTO.setSalesReturnInspectDetailDTO(salesReturnInspectDetailDTO);

            // 推荐入区
            for (SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO : salesReturnOrderReceiveDTOList) {
                String allowEntry = salesReturnOrderDetailDTOList.stream()
                        .filter(salesReturnOrderDetailDTO -> salesReturnOrderDetailDTO.getSkuCode().equalsIgnoreCase(salesReturnOrderReceiveDTO.getSkuCode()))
                        .map(SalesReturnOrderDetailDTO::getAllowEntry)
                        .findFirst().orElse(StrUtil.EMPTY);
                if (RSAllowEntryEnum.NOT_ALLOWED.getCode().equalsIgnoreCase(allowEntry)) {
                    salesReturnOrderReceiveDTO.setSecondEntry(RSSecondEntryEnum.NO.getCode());
                    salesReturnOrderReceiveDTO.setSecondEntryFailReason("淘天要求");
                } else if (RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource())
                        && RSOverdueEnum.OVERDUE.getCode().equals(salesReturnOrderDTO.getOverdue())) {
                    salesReturnOrderReceiveDTO.setSecondEntry(RSSecondEntryEnum.NO.getCode());
                }
                // 满足二次入区条件且前端没有指定是否二次入库，默认是，防止数据没有设置值
                if (salesReturnOrderReceiveDTO.getSecondEntry() == null) {
                    salesReturnOrderReceiveDTO.setSecondEntry(RSSecondEntryEnum.YES.getCode());
                }
                if (salesReturnOrderReceiveDTO.getSkuQuality().equalsIgnoreCase(SkuQualityEnum.SKU_QUALITY_AVL.getLevel())) {
                    salesReturnOrderReceiveDTO.setDamageReason("");
                }
                // OMS处理二次入区
                if (RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource()) && TaxTypeEnum.TYPE_BONDED_TAX.getCode().equalsIgnoreCase(salesReturnOrderDTO.getTaxType())) {
                    if (!returnAllowEntry) {
                        salesReturnOrderReceiveDTO.setSecondEntry(RSSecondEntryEnum.NO.getCode());
                        salesReturnOrderReceiveDTO.setSecondEntryFailReason("该货主禁用二次入区");
                    } else {
                        if (RSOverdueEnum.OVERDUE.getCode().equals(salesReturnOrderDTO.getOverdue())) {
                            salesReturnOrderReceiveDTO.setSecondEntry(RSSecondEntryEnum.NO.getCode());
                            salesReturnOrderReceiveDTO.setSecondEntryFailReason("清关超三十天");
                        } else {
                            if (SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel().equalsIgnoreCase(salesReturnOrderReceiveDTO.getSkuQuality())) {
                                salesReturnOrderReceiveDTO.setSecondEntry(RSSecondEntryEnum.NO.getCode());
                                salesReturnOrderReceiveDTO.setSecondEntryFailReason("当前品为次品，不可二次入区");
                            }
                        }
                    }
                }
            }

            List<MessageMqDTO> messageMqDTOList = new ArrayList<>();
            messageMqDTOList.addAll(remoteSalesReturnOrderClient.messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_RECEIVE_CALLBACK_ORIGIN));
            log.info("salesReturnInspectDTO:{}", JSONUtil.toJsonStr(salesReturnInspectDTO));
            // 校验防伪码等信息
            List<SkuTraceCode> skuTraceCodeList = CallUnderOtherContext.execute(() -> skuTraceCodeList(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());
            List<SkuSnCode> skuSnCodeList = CallUnderOtherContext.execute(() -> skuSnCodeList(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());
            List<SkuSourceCode> sourceCodeList = CallUnderOtherContext.execute(() -> sourceCodeList(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());
            checkTraceCode(skuTraceCodeList, salesReturnOrderReceiveDTOList);
            // 校验SN
            checkSnCode(skuSnCodeList, salesReturnOrderReceiveDTOList);
            // 校验溯源码
            checkSourceCode(sourceCodeList, salesReturnOrderReceiveDTOList);

            RpcContextUtil.setWarehouseCode(warehouseCode);

            // 生成批次信息【注意从这里开始品换成新的品】
            genSkuLotInfo(returnOrderDTO, salesReturnOrderReceiveDTOList);

            // 合并
            salesReturnOrderReceiveDTOList = merge(salesReturnOrderReceiveDTOList,
                    it -> StrUtil.join("#", it.getSkuCode(), it.getSkuLotNo(), it.getSourceCode(), it.getSn(), it.getTraceCode(), it.getSkuQuality(), it.getInventoryType(), it.getSecondEntry()), new BiConsumer<SalesReturnOrderReceiveDTO, SalesReturnOrderReceiveDTO>() {
                        @Override
                        public void accept(SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO, SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO2) {
                            salesReturnOrderReceiveDTO.setQty(salesReturnOrderReceiveDTO.getQty().add(salesReturnOrderReceiveDTO2.getQty()));
                        }
                    });
            salesReturnInspectDTO.setSalesReturnOrderReceiveDTOList(salesReturnOrderReceiveDTOList);

            // 收货的品必须在销退单明细里面【必须在替换后执行】
            for (SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO : salesReturnOrderReceiveDTOList) {
                if (salesReturnOrderDetailDTOList.stream().noneMatch(salesReturnOrderDetailDTO -> salesReturnOrderDetailDTO.getSkuCode().equalsIgnoreCase(salesReturnOrderReceiveDTO.getSkuCode()))) {
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, salesReturnOrderReceiveDTO.getUpcCode(), "对应商品编码不在销退单明细中"));
                }
            }
            salesReturnInspectDTO.setSalesReturnOrderReceiveDTOList(salesReturnOrderReceiveDTOList);

            // 生成上架单
            List<ShelfDTO> shelfDTOList = shelfDTOListV2(returnOrderDTO, salesReturnOrderReceiveDTOList);
            if (CollectionUtil.isNotEmpty(shelfDTOList)) {
                salesReturnInspectDTO.setShelfDTOList(shelfDTOList);
                salesReturnInspectDTO.setShelfDetailDTOList(shelfDTOList.stream().flatMap(it -> it.getDetailList().stream()).collect(Collectors.toList()));

                // 库存处理
                MessageMqDTO messageMqDTO = new MessageMqDTO();
                messageMqDTO.setWarehouseCode(returnOrderDTO.getWarehouseCode());
                messageMqDTO.setCargoCode(returnOrderDTO.getCargoCode());
                messageMqDTO.setBillNo(returnOrderDTO.getSalesReturnOrderNo());
                messageMqDTO.setOperationType(MessageTypeEnum.OPERATION_SALE_RETURN_RECEIPT.getType());
                messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RS_ORDER.getType());
                messageMqDTO.setCreatedTime(System.currentTimeMillis());
                messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
                messageMqDTOList.add(messageMqDTO);
            }

            //计算回写：
            receiveWriteBack(salesReturnOrderDetailDTOList, salesReturnOrderReceiveDTOList);
            salesReturnInspectDTO.setSalesReturnOrderDetailDTOList(salesReturnOrderDetailDTOList);

            // 少收
            if (receiveLess(salesReturnOrderDetailDTOList, salesReturnOrderReceiveDTOList)) {
                receiveLessCheckOpException(salesReturnOrderDTO);
            }

            // 部分退标签
            SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
            if (RSInspectionEnum.PART_PASS.getCode().equals(salesReturnOrderDTO.getInspectionResult())) {
                salesReturnOrderExtraDTO.setPartPass(true);
            }
            salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
            // 是否支持二次入区
            if (TaxTypeEnum.TYPE_BONDED_TAX.getCode().equalsIgnoreCase(salesReturnOrderDTO.getTaxType()) && RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource())) {
                if (returnAllowEntry) {
                    salesReturnOrderDTO.setReturnAllowEntry(ReturnAllowEntryEnum.ALLOWED.getCode());
                } else {
                    salesReturnOrderDTO.setReturnAllowEntry(ReturnAllowEntryEnum.NOT_ALLOWED.getCode());
                }
            }

            // 销退上架回告消息
            shelfCallbackMessage(salesReturnOrderDTO, messageMqDTOList);
            salesReturnInspectDTO.setMessageMqDTOList(messageMqDTOList);
            remoteSalesReturnInspectClient.commit(salesReturnInspectDTO);

            // 回告销退上架
            messageMqDTOList.stream().filter(messageMqDTO -> messageMqDTO.getOperationType().equalsIgnoreCase(MessageTypeEnum.OPERATION_SALE_RETURN_SHELF_CALLBACK.getType()))
                    .findFirst().ifPresent(remoteSalesReturnOrderClient::callback);

            // 判断是否需要等待
            boolean holdable = holdable(salesReturnOrderDTO, salesReturnOrderReceiveDTOList);

            // 等待
            boolean hold = false;
            if (holdable) {
                hold = hold(salesReturnOrderDTO);
            }

            //记录日志
            BillLogDTO billLogDTO = new BillLogDTO();
            billLogDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
            billLogDTO.setBillType(BillLogTypeEnum.RS_SALES_ORDER.getType());
            billLogDTO.setOpBy(CurrentUserHolder.getUserName());
            billLogDTO.setOpDate(System.currentTimeMillis());
            billLogDTO.setOpContent("质检完成");

            BigDecimal avlQty = salesReturnOrderReceiveDTOList.stream().filter(a -> a.getSkuQuality().equals(SkuQualityEnum.SKU_QUALITY_AVL.getLevel())).map(SalesReturnOrderReceiveDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal damageQty = salesReturnOrderReceiveDTOList.stream().filter(a -> a.getSkuQuality().equals(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel())).map(SalesReturnOrderReceiveDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

            billLogDTO.setOpRemark(String.format("通过,正品数量:%s,次品数量:%s", avlQty.intValue(), damageQty.intValue()));
            remoteBillLogClient.save(billLogDTO);

            remoteSalesReturnOrderClient.callback(messageMqDTOList);

            // 发送库存消息
            for (MessageMqDTO messageMqDTO : messageMqDTOList) {
                if (messageMqDTO.getOperationType().equalsIgnoreCase(MessageTypeEnum.OPERATION_SALE_RETURN_RECEIPT.getType())) {
                    try {
                        // stock operation
                        StockOperationMessage stockOperationMessage = new StockOperationMessage();
                        stockOperationMessage.setWarehouseCode(messageMqDTO.getWarehouseCode());
                        stockOperationMessage.setCargoCode(messageMqDTO.getCargoCode());
                        stockOperationMessage.setOperationType(messageMqDTO.getOperationType());
                        stockOperationMessage.setBillNo(messageMqDTO.getBillNo());
                        remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
                    } catch (Exception ignored) {
                    }
                }
            }

            SalesReturnInspectResultDTO salesReturnInspectResultDTO = new SalesReturnInspectResultDTO();
            salesReturnInspectResultDTO.setBillSource(salesReturnOrderDTO.getBillSource());
            salesReturnInspectResultDTO.setHoldable(holdable);
            if (hold) {
                salesReturnInspectResultDTO.setHold(true);
                if (salesReturnOrderReceiveDTOList.stream().anyMatch(salesReturnOrderReceiveDTO -> RSSecondEntryEnum.NO.getCode().equals(salesReturnOrderReceiveDTO.getSecondEntry()))) {
                    ArrayList<SalesReturnOrderReveiveHoldDetail> salesReturnOrderReceiveBizDTOList = new ArrayList<>();
                    for (SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO : salesReturnOrderReceiveDTOList) {
                        SalesReturnOrderReveiveHoldDetail e = new SalesReturnOrderReveiveHoldDetail();
                        e.setUpcCode(salesReturnOrderReceiveDTO.getUpcCode());
                        e.setSecondEntryDesc(RSSecondEntryEnum.desc(salesReturnOrderReceiveDTO.getSecondEntry()));
                        if (RSSecondEntryEnum.YES.getCode().equals(salesReturnOrderReceiveDTO.getSecondEntry())) {
                            e.setContDesc("等待合流");
                        } else {
                            e.setContDesc("区外仓上架");
                        }
                        salesReturnOrderReceiveBizDTOList.add(e);
                    }
                    // 合并
                    Map<String, List<SalesReturnOrderReveiveHoldDetail>> collect = salesReturnOrderReceiveBizDTOList.stream().collect(Collectors.groupingBy(it -> StrUtil.join(StrUtil.COLON, it.getUpcCode(), it.getSecondEntryDesc())));
                    List<SalesReturnOrderReveiveHoldDetail> merged = collect.values().stream().flatMap(it -> Stream.of(it.get(0))).collect(Collectors.toList());
                    salesReturnInspectResultDTO.setSalesReturnOrderReceiveBizDTOList(merged);
                }
            }
            return Result.success(salesReturnInspectResultDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}", e);
            if (e instanceof BaseException) {
                BaseException baseException = (BaseException) e;
                String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "操作太快了,请稍后重试!!!" : e.getMessage();
                if (baseException != null && Objects.equals(baseException.getCode(), -20)) {
                    throw new BaseException(BaseBizEnum.TIP_OTHER, errorMsg);
                } else {
                    throw new BaseException(BaseBizEnum.TIP, errorMsg);
                }
            } else {

                throw ExceptionUtil.SYSTEM_BUSY;
            }
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    private void partInspectFailDetail(SalesReturnInspectResultDTO salesReturnInspectResultDTO, SalesReturnInspectBizParam returnInspectBizParam) {
        List<SalesReturnOrderInspectFailDetail> list = new ArrayList<>();
        for (SalesReturnInspectExpressDetailBizParam param : returnInspectBizParam.getPackList()) {
            if (!RSInspectionEnum.FAIL.getCode().equals(param.getInspectionResult())) continue;
            SalesReturnOrderInspectFailDetail detail = list.stream()
                    .filter(it -> it.getUpcCode().equalsIgnoreCase(param.getUpcCode()))
                    .filter(it -> it.getSkuQuality().equalsIgnoreCase(param.getSkuQuality()))
                    .findFirst().orElse(null);
            if (detail == null) {
                detail = new SalesReturnOrderInspectFailDetail();
                detail.setQty(param.getQty());
                detail.setSkuQuality(param.getSkuQuality());
                detail.setUpcCode(param.getUpcCode());
                detail.setSkuQualityDesc(SkuQualityEnum.desc(detail.getSkuQuality()));
                detail.setSkuName(param.getSkuName());
                list.add(detail);
            } else {
                detail.setQty(detail.getQty().add(param.getQty()));
            }
        }
        salesReturnInspectResultDTO.setInspectFailDetailList(list);
    }

    private void receiveLess(SalesReturnInspectResultDTO salesReturnInspectResultDTO, SalesReturnInspectBizParam returnInspectBizParam, List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList) {
        BigDecimal exp = salesReturnOrderDetailDTOList.stream()
                .map(SalesReturnOrderDetailDTO::getExpectQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal real = returnInspectBizParam.getPackList().stream()
                .map(SalesReturnInspectExpressDetailBizParam::getQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        salesReturnInspectResultDTO.setReceiveLessMessage(StrUtil.join(StrUtil.EMPTY, "实收数量", real.intValue(), ",应收数量", exp.intValue()));
    }

    private void shelfCallbackMessage(SalesReturnOrderDTO salesReturnOrderDTO, List<MessageMqDTO> messageMqDTOList) {
        if (null == salesReturnOrderDTO) return;
        if (RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource())) return;
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
        messageMqDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
        messageMqDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        messageMqDTO.setOperationType(MessageTypeEnum.OPERATION_SALE_RETURN_SHELF_CALLBACK.getType());
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RS_ORDER.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        messageMqDTOList.add(messageMqDTO);
    }

    private boolean holdable(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        // 整单不可二次入区
        boolean allNotAllow = salesReturnOrderReceiveDTOList.stream().allMatch(salesReturnOrderReceiveDTO -> RSSecondEntryEnum.NO.getCode().equals(salesReturnOrderReceiveDTO.getSecondEntry()));
        if (allNotAllow) {
            return false;
        }

        if (RSReturnTypeEnum.CUSTOMER_RETURNS.getCode().equals(salesReturnOrderDTO.getReturnType())) {
            return true;
        }

        // 拆单标
        boolean separateOrderFlag = false;
        SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
        String extendProps = salesReturnOrderExtraDTO.getExtendProps();
        if (StrUtil.isNotBlank(extendProps) && JSONUtil.isJson(extendProps)) {
            JSONObject jsonObject = JSONUtil.parseObj(extendProps);
            JSONObject bondedSplitInfo = jsonObject.getJSONObject("bondedSplitInfo");
            if (null != bondedSplitInfo) {
                String str = bondedSplitInfo.getStr("separateOrderFlag", "false");
                if ("true".equalsIgnoreCase(str.trim())) {
                    separateOrderFlag = true;
                }
            }
        }

        if (RSReturnTypeEnum.CUSTOMER_REJECT.getCode().equals(salesReturnOrderDTO.getReturnType())
                || RSReturnTypeEnum.TMS_CUT.getCode().equals(salesReturnOrderDTO.getReturnType())) {
            return separateOrderFlag;
        }

        return false;
    }

    private boolean hold(SalesReturnOrderDTO dto) {
        DateTime now = DateTime.now();
        for (int i = 0; i < 5; i++) {
            ThreadUtil.sleep(1, TimeUnit.SECONDS);
            if (DateTime.now().offset(DateField.SECOND, -5).isAfter(now)) {
                return false;
            }
            SalesReturnOrderParam param = new SalesReturnOrderParam();
            param.setSalesReturnOrderNo(dto.getSalesReturnOrderNo());
            SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(param);
            if (salesReturnOrderDTO == null) return false;
            if (remoteSalesReturnOrderClient.holdTag(salesReturnOrderDTO)) {
                return true;
            }
        }
        return false;
    }


    private void checkSourceCode(List<SkuSourceCode> skuSourceCodeList, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        if (CollectionUtil.isEmpty(skuSourceCodeList)) return;
        if (CollectionUtil.isEmpty(salesReturnOrderReceiveDTOList)) return;

        Map<String, List<SkuSourceCode>> traceCodeGroupBySku = skuSourceCodeList.stream().collect(Collectors.groupingBy(SkuSourceCode::getSkuCode));
        Map<String, List<SalesReturnOrderReceiveDTO>> receiveGroupBySkuCode = salesReturnOrderReceiveDTOList.stream()
                .collect(Collectors.groupingBy(SalesReturnOrderReceiveDTO::getSkuCode));
        for (String skuCode : traceCodeGroupBySku.keySet()) {
            List<SkuSourceCode> sourceCodeList = traceCodeGroupBySku.get(skuCode);
            List<String> collect = sourceCodeList.stream().map(SkuSourceCode::getSourceCode).map(String::toUpperCase).distinct().collect(Collectors.toList());
            List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOS = receiveGroupBySkuCode.get(skuCode);
            if (CollectionUtil.isEmpty(sourceCodeList)) continue;
            if (CollectionUtil.isEmpty(salesReturnOrderReceiveDTOS)) continue;
            // 存在防伪码等信息，收货信息里面的防伪码必须在防伪码信息中
            salesReturnOrderReceiveDTOS.stream()
                    .filter(it -> !CompareUtil.contain(collect, it.getSourceCode()))
                    .findFirst().ifPresent(salesReturnOrderReceiveDTO -> {
                        throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "商品出库信息存在溯源码，质检溯源码必须在出库扫描的溯源码之中，商品编码", salesReturnOrderReceiveDTO.getSkuCode(), "SN", salesReturnOrderReceiveDTO.getSn()));
                    });
            Map<String, BigDecimal> originTraceCodeUsed = sourceCodeList.stream()
                    .collect(Collectors.groupingBy(SkuSourceCode::getSourceCode, Collectors.mapping(itt -> BigDecimal.ONE, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

            Map<String, BigDecimal> inspectTraceCodeUsed = salesReturnOrderReceiveDTOS.stream()
                    .collect(Collectors.groupingBy(SalesReturnOrderReceiveDTO::getSourceCode, Collectors.mapping(SalesReturnOrderReceiveDTO::getQty, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            // 溯源码使用不能超过发货信息是烧苗的数量
            for (String sourceCode : inspectTraceCodeUsed.keySet()) {
                BigDecimal inspectUse = inspectTraceCodeUsed.get(sourceCode);
                BigDecimal bigDecimal = originTraceCodeUsed.get(sourceCode);
                log.info("商品 {} 溯源码 {} 质检 {} 出库扫码记录 {}", skuCode, sourceCode, inspectUse, bigDecimal);
                if (null == bigDecimal)
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "商品出库信息存在溯源码，溯源码必须在出库扫描的溯源码之中，商品编码", skuCode, "溯源码", sourceCode));
                if (inspectUse.compareTo(bigDecimal) > 0)
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "商品出库信息存在溯源码，溯源码必须在出库扫描的溯源码之中，商品编码", skuCode, "溯源码", sourceCode));
            }
        }

    }

    /**
     * @param salesReturnOrderDetailDTOList
     * @param packList
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2024/9/13 13:40
     */
    private void checkSkuOver(List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList, List<SalesReturnInspectExpressDetailBizParam> packList) {
        BigDecimal originQty = salesReturnOrderDetailDTOList.stream().map(SalesReturnOrderDetailDTO::getExpectQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal commitQty = packList.stream().map(SalesReturnInspectExpressDetailBizParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (originQty.compareTo(commitQty) < 0) {
            throw new BaseException(BaseBizEnum.TIP, "实际质检总数量大于销退单计划明细总数量,请核查!!!");
        }

    }

    private void genSkuLotInfo(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        // 只有淘天的需要重新生成批次
        if (!RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource())) return;
        // 注意退货仓的商品信息跟正向发货的不一致，要通过条码去匹配
        List<String> upcCodeList = salesReturnOrderReceiveDTOList.stream().map(SalesReturnOrderReceiveDTO::getUpcCode).distinct().collect(Collectors.toList());
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setCargoCode(salesReturnOrderDTO.getCargoCode());
        skuUpcParam.setUpcCodeList(upcCodeList);
        List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
        Map<String, String> upcSkuCodeMap = skuUpcList.stream()
                .collect(Collectors.toMap(skuUpcDTO -> skuUpcDTO.getUpcCode().toUpperCase(), SkuUpcDTO::getSkuCode, BinaryOperator.maxBy(String::compareTo)));
        Map<String, SkuDTO> skuDTOMap = remoteSkuClient.skuDTOMap(salesReturnOrderDTO.getCargoCode(), skuUpcList.stream().map(SkuUpcDTO::getSkuCode).distinct().collect(Collectors.toList()));
        for (SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO : salesReturnOrderReceiveDTOList) {
            SkuLotCheckAndFormatParam skuLotCheckAndFormatParam = new SkuLotCheckAndFormatParam();
            skuLotCheckAndFormatParam.setExpireDate(salesReturnOrderReceiveDTO.getExpireDate());
            skuLotCheckAndFormatParam.setManufDate(salesReturnOrderReceiveDTO.getManufDate());
            skuLotCheckAndFormatParam.setProductionNo(StrUtil.EMPTY);
            skuLotCheckAndFormatParam.setSkuQuality(salesReturnOrderReceiveDTO.getSkuQuality());
            skuLotCheckAndFormatParam.setReceiveDate(System.currentTimeMillis());
            skuLotCheckAndFormatParam.setExternalLinkBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
            skuLotCheckAndFormatParam.setInventoryType(salesReturnOrderReceiveDTO.getInventoryType());
            String skuCode = upcSkuCodeMap.get(salesReturnOrderReceiveDTO.getUpcCode().toUpperCase());
            SkuDTO skuDTO = skuDTOMap.get(StrUtil.join(StrUtil.COLON, salesReturnOrderDTO.getWarehouseCode(), salesReturnOrderDTO.getCargoCode(), skuCode));
            SkuLotDTO skuLot = skuLotBiz.findAndFormatSkuLot(skuLotCheckAndFormatParam, skuDTO, false);
            // 批次和商品编码都要换成新的
            salesReturnOrderReceiveDTO.setSkuLotNo(skuLot.getCode());
            salesReturnOrderReceiveDTO.setSkuCode(skuCode);
            if (ObjectUtil.isEmpty(skuLot.getId())) {
                remoteSkuLotClient.saveBatch(ListUtil.toList(skuLot));
            }
            // 仅针对淘天的订单，若商品当前日期大于等于禁售日期，且商品选择为正品，则提示：禁售商品不可为正品。停留当前页面不可完成当前运单
            if (SkuQualityEnum.SKU_QUALITY_AVL.getLevel().equalsIgnoreCase(salesReturnOrderReceiveDTO.getSkuQuality())) {
                SkuLotAndStockDTO skuLotAndStockDTO = new SkuLotAndStockDTO();
                skuLotAndStockDTO.setExpireDate(salesReturnOrderReceiveDTO.getExpireDate());
                filterLot(skuLotAndStockDTO, skuDTO);
            }
        }
    }


    private void genSkuLotInfoForDyMarket(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        Map<String, SkuDTO> skuDTOMap = remoteSkuClient.skuDTOMap(salesReturnOrderDTO.getCargoCode(), salesReturnOrderReceiveDTOList.stream().map(SalesReturnOrderReceiveDTO::getSkuCode).distinct().collect(Collectors.toList()));
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(salesReturnOrderReceiveDTOList.stream().map(SalesReturnOrderReceiveDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
        for (SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO : salesReturnOrderReceiveDTOList) {
            SkuLotDTO skuLotDTO = skuLotDTOList.stream().filter(it -> it.getCode().equalsIgnoreCase(salesReturnOrderReceiveDTO.getSkuLotNo())).findFirst().orElse(null);
            SkuLotCheckAndFormatParam skuLotCheckAndFormatParam = new SkuLotCheckAndFormatParam();
            if (null != skuLotDTO) {
                BeanUtil.copyProperties(skuLotDTO, skuLotCheckAndFormatParam);
            }
            skuLotCheckAndFormatParam.setExpireDate(salesReturnOrderReceiveDTO.getExpireDate());
            skuLotCheckAndFormatParam.setManufDate(salesReturnOrderReceiveDTO.getManufDate());
            skuLotCheckAndFormatParam.setSkuQuality(salesReturnOrderReceiveDTO.getSkuQuality());
            if (SkuQualityEnum.SKU_QUALITY_AVL.getLevel().equalsIgnoreCase(salesReturnOrderReceiveDTO.getSkuQuality())) {
                skuLotCheckAndFormatParam.setInventoryType(InventoryTypeEnum.ZP.getCode());
            } else {
                skuLotCheckAndFormatParam.setInventoryType(InventoryTypeEnum.CC.getCode());
            }
            skuLotCheckAndFormatParam.setReceiveDate(System.currentTimeMillis());
            skuLotCheckAndFormatParam.setExternalLinkBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
            skuLotCheckAndFormatParam.setBoxCode("");
            skuLotCheckAndFormatParam.setPalletCode("");
            skuLotCheckAndFormatParam.setValidityCode("");
            String skuCode = salesReturnOrderReceiveDTO.getSkuCode();
            SkuDTO skuDTO = skuDTOMap.get(StrUtil.join(StrUtil.COLON, salesReturnOrderDTO.getWarehouseCode(), salesReturnOrderDTO.getCargoCode(), skuCode));
            SkuLotDTO skuLot = skuLotBiz.findAndFormatSkuLot(skuLotCheckAndFormatParam, skuDTO, false);
            // 批次和商品编码都要换成新的
            salesReturnOrderReceiveDTO.setSkuLotNo(skuLot.getCode());
            salesReturnOrderReceiveDTO.setSkuCode(skuCode);
            if (ObjectUtil.isEmpty(skuLot.getId())) {
                remoteSkuLotClient.saveBatch(ListUtil.toList(skuLot));
            }
            // 仅针对淘天的订单，若商品当前日期大于等于禁售日期，且商品选择为正品，则提示：禁售商品不可为正品。停留当前页面不可完成当前运单
            if (SkuQualityEnum.SKU_QUALITY_AVL.getLevel().equalsIgnoreCase(salesReturnOrderReceiveDTO.getSkuQuality())) {
                SkuLotAndStockDTO skuLotAndStockDTO = new SkuLotAndStockDTO();
                skuLotAndStockDTO.setExpireDate(salesReturnOrderReceiveDTO.getExpireDate());
                filterLot(skuLotAndStockDTO, skuDTO);
            }
        }
    }

    private List<ShelfDTO> shelfDTOListV2(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        RSBillSourceEnum rsBillSourceEnum = RSBillSourceEnum.fromInt(salesReturnOrderDTO.getBillSource());
        switch (rsBillSourceEnum) {
            case OMS:
                return ListUtil.empty();
            case MALL_DIRECT:
            case MALL_PLATFORM:
                return shelfDTOListForTaoTian(salesReturnOrderDTO, salesReturnOrderReceiveDTOList);
            case DY_MARKET:
                return shelfDTOListForDyMarket(salesReturnOrderDTO, salesReturnOrderReceiveDTOList);
            default:
                return ListUtil.empty();
        }
    }

    @ApiOperation("生成上架单")
    private List<ShelfDTO> shelfDTOListForDyMarket(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        AtomicInteger lineSeq = new AtomicInteger(1);
        List<ShelfDTO> shelfDTOList = new ArrayList<>();
        for (SkuQualityEnum skuQualityEnum : SkuQualityEnum.values()) {
            List<SalesReturnOrderReceiveDTO> collect = salesReturnOrderReceiveDTOList.stream()
                    .filter(it -> skuQualityEnum.getLevel().equalsIgnoreCase(it.getSkuQuality()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                String shelfCode = remoteSeqRuleClient.findSequence(SeqEnum.SHELF_CODE_000001);
                ContainerDTO containerDTO = remoteContainerClient.findOrCreateUsableContainer();
                LocationDTO locationDTO = remoteSpecialLocationClient.getLocationByReceivingSkuQuality(skuQualityEnum.getLevel());
                List<ShelfDetailDTO> shelfDetailDTOList = new ArrayList<>();
                for (SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO : collect) {
                    ShelfDetailDTO shelfDetailDTO = new ShelfDetailDTO();
                    shelfDetailDTO.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
                    shelfDetailDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
                    shelfDetailDTO.setSkuCode(salesReturnOrderReceiveDTO.getSkuCode());
                    shelfDetailDTO.setUpcCode(salesReturnOrderReceiveDTO.getUpcCode());
                    shelfDetailDTO.setContCode(containerDTO.getCode());
                    shelfDetailDTO.setLineSeq(String.valueOf(lineSeq.getAndAdd(1)));
                    shelfDetailDTO.setShelfCode(shelfCode);
                    shelfDetailDTO.setUid(IdUtil.simpleUUID());
                    shelfDetailDTO.setStatus(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
                    shelfDetailDTO.setSkuName(salesReturnOrderReceiveDTO.getSkuName());
                    shelfDetailDTO.setSkuQty(salesReturnOrderReceiveDTO.getQty());
                    shelfDetailDTO.setOriginLocationCode(locationDTO.getCode());
                    shelfDetailDTO.setOriginZoneType(locationDTO.getType());
                    shelfDetailDTO.setOriginZoneCode(locationDTO.getZoneCode());
                    shelfDetailDTO.setSkuLotNo(salesReturnOrderReceiveDTO.getSkuLotNo());
                    shelfDetailDTO.setSkuQuality(skuQualityEnum.getLevel());
                    shelfDetailDTOList.add(shelfDetailDTO);
                }
                ShelfDTO shelfDTO = new ShelfDTO();
                shelfDTO.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
                shelfDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
                shelfDTO.setCode(shelfCode);
                shelfDTO.setContCode(containerDTO.getCode());
                shelfDTO.setStatus(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
                shelfDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
                shelfDTO.setSkuQuality(skuQualityEnum.getLevel());
                long count = shelfDetailDTOList.stream().map(ShelfDetailDTO::getSkuCode).distinct().count();
                shelfDTO.setSkuType(((int) count));
                BigDecimal reduce = shelfDetailDTOList.stream().map(ShelfDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                shelfDTO.setSkuQty(reduce);
                shelfDTO.setOpType(OpTypeEnum.OP_TYPE_PAPER.getType());
                shelfDTO.setType(ShelfTypeEnum.SHELF_TYPE_SALE_RETURN.getType());
                shelfDTO.setDetailList(shelfDetailDTOList);
                shelfDTO.setCreatedTime(System.currentTimeMillis());
                shelfDTO.setCreatedBy(CurrentUserHolder.getUserName());
                shelfDTO.setPackageUnitCode("PCS");
                shelfDTOList.add(shelfDTO);

                containerDTO.setOccupyNo(salesReturnOrderDTO.getSalesReturnOrderNo());
                containerDTO.setStatus(ContainerStatusEnum.OCCUPY.getValue());
                containerDTO.setOccupyType(WorkBenchTypeEnum.XT_RECEIVE_RECEIPT.getType());
                if (ObjectUtil.isEmpty(containerDTO.getId())) {
                    remoteContainerClient.saveBatch(ListUtil.toList(containerDTO));
                } else {
                    remoteContainerClient.modifyBatch(ListUtil.toList(containerDTO));
                }

                // 容器日志
                ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                containerLogDTO.setWarehouseCode(containerDTO.getWarehouseCode());
                containerLogDTO.setContCode(containerDTO.getCode());
                containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                containerLogDTO.setCreatedTime(System.currentTimeMillis());
                containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                containerLogDTO.setOpContent(String.format("销退质检单:%s绑定容器:%s", salesReturnOrderDTO.getSalesReturnOrderNo(), containerDTO.getCode()));
                containerLogDTO.setOpDate(System.currentTimeMillis());
                containerLogDTO.setOccupyNo(salesReturnOrderDTO.getSalesReturnOrderNo());
                containerLogDTO.setOccupyType(WorkBenchTypeEnum.XT_RECEIVE_RECEIPT.getType());
                remoteContainerLogClient.saveBatch(ListUtil.toList(containerLogDTO));
            }

        }
        return shelfDTOList;
    }

    @ApiOperation("生成上架单")
    private List<ShelfDTO> shelfDTOListForTaoTian(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        AtomicInteger lineSeq = new AtomicInteger(1);
        List<ShelfDTO> shelfDTOList = new ArrayList<>();
        for (SkuQualityEnum skuQualityEnum : SkuQualityEnum.values()) {
            for (RSSecondEntryEnum secondEntryEnum : RSSecondEntryEnum.values()) {
                List<SalesReturnOrderReceiveDTO> collect = salesReturnOrderReceiveDTOList.stream()
                        .filter(it -> skuQualityEnum.getLevel().equalsIgnoreCase(it.getSkuQuality()))
                        .filter(it -> secondEntryEnum.getCode().equals(it.getSecondEntry()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect)) {
                    String shelfCode = remoteSeqRuleClient.findSequence(SeqEnum.SHELF_CODE_000001);
                    ContainerDTO containerDTO = remoteContainerClient.findOrCreateUsableContainer();
                    LocationDTO locationDTO = remoteSpecialLocationClient.getLocationByReceivingSkuQuality(skuQualityEnum.getLevel());
                    List<ShelfDetailDTO> shelfDetailDTOList = new ArrayList<>();
                    for (SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO : collect) {
                        ShelfDetailDTO shelfDetailDTO = new ShelfDetailDTO();
                        shelfDetailDTO.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
                        shelfDetailDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
                        shelfDetailDTO.setSkuCode(salesReturnOrderReceiveDTO.getSkuCode());
                        shelfDetailDTO.setUpcCode(salesReturnOrderReceiveDTO.getUpcCode());
                        shelfDetailDTO.setContCode(containerDTO.getCode());
                        shelfDetailDTO.setLineSeq(String.valueOf(lineSeq.getAndAdd(1)));
                        shelfDetailDTO.setShelfCode(shelfCode);
                        shelfDetailDTO.setUid(IdUtil.simpleUUID());
                        shelfDetailDTO.setStatus(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
                        shelfDetailDTO.setSkuName(salesReturnOrderReceiveDTO.getSkuName());
                        shelfDetailDTO.setSkuQty(salesReturnOrderReceiveDTO.getQty());
                        shelfDetailDTO.setOriginLocationCode(locationDTO.getCode());
                        shelfDetailDTO.setOriginZoneType(locationDTO.getType());
                        shelfDetailDTO.setOriginZoneCode(locationDTO.getZoneCode());
                        shelfDetailDTO.setSkuLotNo(salesReturnOrderReceiveDTO.getSkuLotNo());
                        shelfDetailDTO.setSkuQuality(skuQualityEnum.getLevel());
                        shelfDetailDTO.setMark(ShelfMarkDetailEnum.enumToNum(ShelfMarkDetailEnum.WAIT_CHECK));
                        shelfDetailDTOList.add(shelfDetailDTO);
                    }
                    ShelfDTO shelfDTO = new ShelfDTO();
                    shelfDTO.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
                    shelfDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
                    shelfDTO.setCode(shelfCode);
                    shelfDTO.setContCode(containerDTO.getCode());
                    shelfDTO.setStatus(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus());
                    shelfDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
                    shelfDTO.setSkuQuality(skuQualityEnum.getLevel());
                    long count = shelfDetailDTOList.stream().map(ShelfDetailDTO::getSkuCode).distinct().count();
                    shelfDTO.setSkuType(((int) count));
                    BigDecimal reduce = shelfDetailDTOList.stream().map(ShelfDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    shelfDTO.setSkuQty(reduce);
                    shelfDTO.setOpType(OpTypeEnum.OP_TYPE_PAPER.getType());
                    shelfDTO.setType(ShelfTypeEnum.SHELF_TYPE_SALE_RETURN.getType());
                    shelfDTO.setDetailList(shelfDetailDTOList);
                    shelfDTO.setCreatedTime(System.currentTimeMillis());
                    shelfDTO.setCreatedBy(CurrentUserHolder.getUserName());
                    shelfDTO.setPackageUnitCode("PCS");
                    List<ShelfMarkEnum> markEnumList = new ArrayList<>();
                    markEnumList.add(ShelfMarkEnum.WAIT_CHECK);
                    markEnumList.add(ShelfMarkEnum.TAO_TIAN);
                    if (RSSecondEntryEnum.YES.getCode().equals(secondEntryEnum.getCode())) {
                        markEnumList.add(ShelfMarkEnum.ENTRY_ALLOW);
                        if (remoteSalesReturnOrderClient.holdTag(salesReturnOrderDTO)) {
                            markEnumList.add(ShelfMarkEnum.HOLD);
                        }
                    }
                    if (RSSecondEntryEnum.NO.getCode().equals(secondEntryEnum.getCode())) {
                        markEnumList.add(ShelfMarkEnum.ENTRY_DENY);
                    }
                    shelfDTO.setMark(ShelfMarkEnum.enumToNum(markEnumList));
                    shelfDTOList.add(shelfDTO);

                    containerDTO.setOccupyNo(salesReturnOrderDTO.getSalesReturnOrderNo());
                    containerDTO.setStatus(ContainerStatusEnum.OCCUPY.getValue());
                    containerDTO.setOccupyType(WorkBenchTypeEnum.XT_RECEIVE_RECEIPT.getType());
                    if (ObjectUtil.isEmpty(containerDTO.getId())) {
                        remoteContainerClient.saveBatch(ListUtil.toList(containerDTO));
                    } else {
                        remoteContainerClient.modifyBatch(ListUtil.toList(containerDTO));
                    }

                    // 容器日志
                    ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                    containerLogDTO.setWarehouseCode(containerDTO.getWarehouseCode());
                    containerLogDTO.setContCode(containerDTO.getCode());
                    containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setCreatedTime(System.currentTimeMillis());
                    containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setOpContent(String.format("销退质检单:%s绑定容器:%s", salesReturnOrderDTO.getSalesReturnOrderNo(), containerDTO.getCode()));
                    containerLogDTO.setOpDate(System.currentTimeMillis());
                    containerLogDTO.setOccupyNo(salesReturnOrderDTO.getSalesReturnOrderNo());
                    containerLogDTO.setOccupyType(WorkBenchTypeEnum.XT_RECEIVE_RECEIPT.getType());
                    remoteContainerLogClient.saveBatch(ListUtil.toList(containerLogDTO));
                }

            }
        }
        return shelfDTOList;
    }

    @Override
    public Result<String> checkReceiveLess(SalesReturnInspectBizParam returnInspectBizParam) {
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
        SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
        if (salesReturnOrderDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "未找到销退单,请核查");
        }

        //获取销退单明细
        SalesReturnOrderDetailParam salesReturnOrderDetailParam = new SalesReturnOrderDetailParam();
        salesReturnOrderDetailParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
        List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList = remoteSalesReturnOrderDetailClient.getList(salesReturnOrderDetailParam);
        if (CollectionUtils.isEmpty(salesReturnOrderDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到销退单商品明细,请核查,请核查");
        }

        // 提交的收货明细
        List<SalesReturnInspectExpressDetailBizParam> packList = returnInspectBizParam.getPackList();

        // 淘天这里需要处理成新的品去校验
        if (RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource())) {
            List<String> upcCodeList = packList.stream().map(SalesReturnInspectExpressDetailBizParam::getUpcCode).distinct().collect(Collectors.toList());
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setCargoCode(salesReturnOrderDTO.getCargoCode());
            skuUpcParam.setUpcCodeList(upcCodeList);
            List<SkuUpcDTO> skuUpcList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(upcCodeList)) {
                skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            }
            Map<String, String> upcSkuCodeMap = skuUpcList.stream()
                    .collect(Collectors.toMap(skuUpcDTO -> skuUpcDTO.getUpcCode().toUpperCase(), SkuUpcDTO::getSkuCode, BinaryOperator.maxBy(String::compareTo)));
            for (SalesReturnInspectExpressDetailBizParam salesReturnInspectExpressDetailBizParam : packList) {
                SkuLotCheckAndFormatParam skuLotCheckAndFormatParam = new SkuLotCheckAndFormatParam();
                skuLotCheckAndFormatParam.setExpireDate(salesReturnInspectExpressDetailBizParam.getExpireDate());
                skuLotCheckAndFormatParam.setManufDate(salesReturnInspectExpressDetailBizParam.getManufDate());
                skuLotCheckAndFormatParam.setProductionNo(StrUtil.EMPTY);
                skuLotCheckAndFormatParam.setSkuQuality(salesReturnInspectExpressDetailBizParam.getSkuQuality());
                skuLotCheckAndFormatParam.setReceiveDate(System.currentTimeMillis());
                skuLotCheckAndFormatParam.setExternalLinkBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
                skuLotCheckAndFormatParam.setInventoryType(salesReturnInspectExpressDetailBizParam.getInventoryType());
                String skuCode = upcSkuCodeMap.get(salesReturnInspectExpressDetailBizParam.getUpcCode().toUpperCase());
                salesReturnInspectExpressDetailBizParam.setSkuCode(skuCode);
            }
        }

        Map<String, String> skuUpcMap = salesReturnOrderDetailDTOList.stream().collect(Collectors.toMap(SalesReturnOrderDetailDTO::getSkuCode, SalesReturnOrderDetailDTO::getUpcCode, BinaryOperator.maxBy(String::compareTo)));
        Map<String, BigDecimal> detailExpQtyGroupBySku = salesReturnOrderDetailDTOList.stream().collect(Collectors.groupingBy(SalesReturnOrderDetailDTO::getSkuCode, Collectors.mapping(SalesReturnOrderDetailDTO::getExpectQty, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        Map<String, BigDecimal> receiveQtyGroupBySku = packList.stream().collect(Collectors.groupingBy(SalesReturnInspectExpressDetailBizParam::getSkuCode, Collectors.mapping(SalesReturnInspectExpressDetailBizParam::getQty, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        if (RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource()) || RSBillSourceEnum.DY_MARKET.getCode().equals(salesReturnOrderDTO.getBillSource())) {
            Set<String> message = new HashSet<>();
            for (String skuCode : detailExpQtyGroupBySku.keySet()) {
                BigDecimal expQty = detailExpQtyGroupBySku.get(skuCode);
                BigDecimal receiveQty = receiveQtyGroupBySku.getOrDefault(skuCode, BigDecimal.ZERO);
                if (expQty.compareTo(receiveQty) > 0) {
                    String upcCode = skuUpcMap.getOrDefault(skuCode, skuCode);
                    message.add(String.format("<p>商品:%s少收，实收%d件，应收%d件,请核查</p>", upcCode, receiveQty.intValue(), expQty.intValue()));
                }
            }
            if (CollectionUtil.isNotEmpty(message)) {
                String collect = String.join("\n", message);
                return Result.fail(BaseBizEnum.TIP_AND_PROCESS_DONE.getValue(), collect, collect);
            }
        } else {
            for (String skuCode : detailExpQtyGroupBySku.keySet()) {
                BigDecimal expQty = detailExpQtyGroupBySku.get(skuCode);
                BigDecimal receiveQty = receiveQtyGroupBySku.getOrDefault(skuCode, BigDecimal.ZERO);
                if (expQty.compareTo(receiveQty) > 0)
                    return Result.fail(BaseBizEnum.TIP_AND_PROCESS_DONE.getValue(), "该运单存在少件，请二次确认并进行异常上报！", "该运单存在少件，请二次确认并进行异常上报！");
            }
        }

        return Result.success();
    }


    @ApiOperation("收货数量回写")
    public void receiveWriteBack(List<SalesReturnOrderDetailDTO> detailDTOList, List<SalesReturnOrderReceiveDTO> receiveDTOList) {
        if (CollectionUtil.isEmpty(detailDTOList)) return;
        if (CollectionUtil.isEmpty(receiveDTOList)) return;
        Map<String, List<SalesReturnOrderDetailDTO>> detailGroupBySku = detailDTOList.stream().collect(Collectors.groupingBy(SalesReturnOrderDetailDTO::getSkuCode));
        Map<String, List<SalesReturnOrderReceiveDTO>> receiveGroupBySku = receiveDTOList.stream().collect(Collectors.groupingBy(SalesReturnOrderReceiveDTO::getSkuCode));
        for (String skuCode : detailGroupBySku.keySet()) {
            List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList = detailGroupBySku.get(skuCode);
            if (CollectionUtil.isEmpty(salesReturnOrderDetailDTOList)) continue;
            List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOS = receiveGroupBySku.get(skuCode);
            if (CollectionUtil.isEmpty(salesReturnOrderReceiveDTOS)) continue;
            BigDecimal receiveAvl = salesReturnOrderReceiveDTOS.stream()
                    .filter(salesReturnOrderReceiveDTO -> CompareUtil.notBlankEqualIgnoreCase(salesReturnOrderReceiveDTO.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel()))
                    .map(SalesReturnOrderReceiveDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal receiveDamage = salesReturnOrderReceiveDTOS.stream()
                    .filter(salesReturnOrderReceiveDTO -> CompareUtil.notBlankEqualIgnoreCase(salesReturnOrderReceiveDTO.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel()))
                    .map(SalesReturnOrderReceiveDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (SalesReturnOrderDetailDTO salesReturnOrderDetailDTO : salesReturnOrderDetailDTOList) {
                while (salesReturnOrderDetailDTO.receiveLackQty().compareTo(BigDecimal.ZERO) > 0) {
                    if (receiveAvl.compareTo(BigDecimal.ZERO) == 0 && receiveDamage.compareTo(BigDecimal.ZERO) == 0)
                        break;
                    if (receiveAvl.compareTo(BigDecimal.ZERO) > 0) {
                        if (salesReturnOrderDetailDTO.receiveLackQty().compareTo(receiveAvl) > 0) {
                            salesReturnOrderDetailDTO.setAvlQty(salesReturnOrderDetailDTO.getAvlQty().add(receiveAvl));
                            receiveAvl = BigDecimal.ZERO;
                        } else if (salesReturnOrderDetailDTO.receiveLackQty().compareTo(receiveAvl) < 0) {
                            receiveAvl = receiveAvl.subtract(salesReturnOrderDetailDTO.receiveLackQty());
                            salesReturnOrderDetailDTO.setAvlQty(salesReturnOrderDetailDTO.getAvlQty().add(salesReturnOrderDetailDTO.receiveLackQty()));
                        } else {
                            salesReturnOrderDetailDTO.setAvlQty(salesReturnOrderDetailDTO.getAvlQty().add(receiveAvl));
                            receiveAvl = BigDecimal.ZERO;
                        }
                    }
                    if (receiveDamage.compareTo(BigDecimal.ZERO) > 0) {
                        if (salesReturnOrderDetailDTO.receiveLackQty().compareTo(receiveDamage) > 0) {
                            salesReturnOrderDetailDTO.setDamageQty(salesReturnOrderDetailDTO.getDamageQty().add(receiveDamage));
                            receiveDamage = BigDecimal.ZERO;
                        } else if (salesReturnOrderDetailDTO.receiveLackQty().compareTo(receiveDamage) < 0) {
                            receiveDamage = receiveDamage.subtract(salesReturnOrderDetailDTO.receiveLackQty());
                            salesReturnOrderDetailDTO.setDamageQty(salesReturnOrderDetailDTO.getDamageQty().add(salesReturnOrderDetailDTO.receiveLackQty()));
                        } else {
                            salesReturnOrderDetailDTO.setDamageQty(salesReturnOrderDetailDTO.getDamageQty().add(receiveDamage));
                            receiveDamage = BigDecimal.ZERO;
                        }
                    }
                }
            }
        }

    }


    /**
     * @param salesReturnOrderDTO
     * @param salesReturnOrderDetailDTOList
     * @param packList
     * @return void
     * <AUTHOR>
     * @describe: 计算效期是否计算正确
     * @date 2024/2/2 9:36
     */
    private void checkSkuPeriod(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList, List<SalesReturnInspectExpressDetailBizParam> packList) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();

        // 淘天的后生成批次库存，那里在校验批次效期时间
        if (RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource())) return;

        //设置数据源
        RpcContextUtil.setWarehouseCode(salesReturnOrderDTO.getRealWarehouseCode());

        SkuParam skuParam = new SkuParam();
        String shipmentCargoCode = salesReturnOrderDTO.salesReturnOrderExtraDTO().getShipmentCargoCode();
        if (!StringUtils.isEmpty(shipmentCargoCode)) {
            skuParam.setCargoCode(salesReturnOrderDTO.salesReturnOrderExtraDTO().getShipmentCargoCode());
        } else {
            skuParam.setCargoCode(salesReturnOrderDTO.getCargoCode());
        }
        skuParam.setCodeList(salesReturnOrderDetailDTOList.stream().map(SalesReturnOrderDetailDTO::getSkuCode).distinct().collect(Collectors.toList()));
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return;
        }
        Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity()));
        packList.forEach(it -> {
            SkuDTO skuDTO = skuDTOMap.get(it.getSkuCode());
            if (skuDTO != null && skuDTO.getIsLifeMgt().equals(SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {
                if (StringUtils.isEmpty(it.getManufDate()) || StringUtils.isEmpty(it.getExpireDate())
                        || it.getManufDate() <= 0L || it.getExpireDate() <= 0L) {
                    throw new BaseException(BaseBizEnum.TIP, "效期商品,生产和失效日期不能为空");
                }
                it.setManufDate(DateUtil.parse(DateUtil.format(new Date(it.getManufDate()), "yyyy-MM-dd"), "yyyy-MM-dd").getTime());
                it.setExpireDate(DateUtil.parse(DateUtil.format(new Date(it.getExpireDate()), "yyyy-MM-dd"), "yyyy-MM-dd").getTime());
                Long expireDateNew = it.getManufDate() + Long.valueOf(skuDTO.getLifeCycle()) * CommonConstantUtil.DAY_MILLISECONDS;
                if (!expireDateNew.equals(it.getExpireDate())) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("商品%s生产日期和失效日期计算异常", skuDTO.getCode()));
                }
            }
        });
        //还回去
        RpcContextUtil.setWarehouseCode(warehouseCode);
    }

    /**
     * @param salesReturnInspectDTO
     * @param returnInspectBizParam
     * @param salesReturnOrderDTO
     * @return java.util.List<com.dt.domain.bill.dto.rs.SalesReturnOrderReceiveDTO>
     * <AUTHOR>
     * @describe:
     * @date 2024/1/26 16:29
     */
    private List<SalesReturnOrderReceiveDTO> buildSalesReturnOrderReceiveDTOList(SalesReturnInspectDTO salesReturnInspectDTO, SalesReturnInspectBizParam returnInspectBizParam, SalesReturnOrderDTO salesReturnOrderDTO) {
        List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList = new ArrayList<>();
        List<SalesReturnInspectExpressDetailBizParam> inspectBizParamPackList = returnInspectBizParam.getPackList();
        for (SalesReturnInspectExpressDetailBizParam salesReturnInspectExpressDetailBizParam : inspectBizParamPackList) {
            SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO = new SalesReturnOrderReceiveDTO();
            salesReturnOrderReceiveDTO.setOpBy(CurrentUserHolder.getUserName());
            salesReturnOrderReceiveDTO.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
            salesReturnOrderReceiveDTO.setContCode(returnInspectBizParam.getContCode());
            salesReturnOrderReceiveDTO.setSkuCode(salesReturnInspectExpressDetailBizParam.getSkuCode());
            salesReturnOrderReceiveDTO.setSkuName(salesReturnInspectExpressDetailBizParam.getSkuName());
            salesReturnOrderReceiveDTO.setUpcCode(salesReturnInspectExpressDetailBizParam.getUpcCode());
            salesReturnOrderReceiveDTO.setSecurityCode(salesReturnInspectExpressDetailBizParam.getSecurityCode());
            salesReturnOrderReceiveDTO.setQty(salesReturnInspectExpressDetailBizParam.getQty());
            salesReturnOrderReceiveDTO.setSkuQuality(salesReturnInspectExpressDetailBizParam.getSkuQuality());
            salesReturnOrderReceiveDTO.setManufDate(salesReturnInspectExpressDetailBizParam.getManufDate());
            salesReturnOrderReceiveDTO.setExpireDate(salesReturnInspectExpressDetailBizParam.getExpireDate());
            salesReturnOrderReceiveDTO.setCheckDate(System.currentTimeMillis());
            salesReturnOrderReceiveDTO.setWorkbenchCode(returnInspectBizParam.getWorkbenchCode());
            salesReturnOrderReceiveDTO.setDamageReason(salesReturnInspectExpressDetailBizParam.getDamageReason());
            salesReturnOrderReceiveDTO.setSkuLotNo(salesReturnInspectExpressDetailBizParam.getSkuLotNo());
            salesReturnOrderReceiveDTO.setSn(salesReturnInspectExpressDetailBizParam.getSn());
            salesReturnOrderReceiveDTO.setTraceCode(salesReturnInspectExpressDetailBizParam.getTraceCode());
            salesReturnOrderReceiveDTO.setSourceCode(salesReturnInspectExpressDetailBizParam.getSourceCode());
            salesReturnOrderReceiveDTO.setInventoryType(salesReturnInspectExpressDetailBizParam.getInventoryType());
            if (StrUtil.isBlank(salesReturnOrderReceiveDTO.getInventoryType()) || SkuQualityEnum.SKU_QUALITY_AVL.getLevel().equalsIgnoreCase(salesReturnOrderReceiveDTO.getSkuQuality())) {
                salesReturnOrderReceiveDTO.setInventoryType(InventoryTypeEnum.ZP.getCode());
            }
            if (RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource())) {
                salesReturnOrderReceiveDTO.setInventoryType("");
            }
            salesReturnOrderReceiveDTO.setSecondEntry(salesReturnInspectExpressDetailBizParam.getSecondEntry());
            salesReturnOrderReceiveDTO.setSecondEntryFailReason(salesReturnInspectExpressDetailBizParam.getSecondEntryFailReason());
            if (salesReturnOrderReceiveDTO.getSkuQuality().equalsIgnoreCase(SkuQualityEnum.SKU_QUALITY_AVL.getLevel())) {
                salesReturnOrderReceiveDTO.setDamageReason("");
            }
            salesReturnOrderReceiveDTOList.add(salesReturnOrderReceiveDTO);
        }
        return salesReturnOrderReceiveDTOList;
    }

    /**
     * @param salesReturnInspectDTO
     * @param returnInspectBizParam
     * @param salesReturnOrderDTO
     * @return java.util.List<com.dt.domain.bill.dto.rs.SalesReturnInspectDetailDTO>
     * <AUTHOR>
     * @describe:
     * @date 2024/1/26 16:23
     */
    private SalesReturnInspectDetailDTO buildSalesReturnInspectDetailDTO(SalesReturnInspectDTO salesReturnInspectDTO, SalesReturnInspectBizParam returnInspectBizParam, SalesReturnOrderDTO salesReturnOrderDTO) {
        SalesReturnInspectDetailDTO salesReturnInspectDetailDTO = new SalesReturnInspectDetailDTO();
        salesReturnInspectDetailDTO.setInspectOrderNo(salesReturnInspectDTO.getInspectOrderNo());
        salesReturnInspectDetailDTO.setExpressNo(returnInspectBizParam.getExpressNo());
        salesReturnInspectDetailDTO.setOpBy(CurrentUserHolder.getUserName());
        salesReturnInspectDetailDTO.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        salesReturnInspectDetailDTO.setSalesReturnStatus(salesReturnOrderDTO.getStatus());
        salesReturnInspectDetailDTO.setInspectionResult(salesReturnInspectDTO.getInspectionResult());
        salesReturnInspectDetailDTO.setInspectTime(System.currentTimeMillis());
        return salesReturnInspectDetailDTO;
    }

    /**
     * @param returnInspectBizParam
     * @param salesReturnOrderDTO
     * @return com.dt.domain.bill.dto.rs.SalesReturnInspectDTO
     * <AUTHOR>
     * @describe:
     * @date 2024/1/26 16:18
     */
    private SalesReturnInspectDTO buildSalesReturnInspectDTO(SalesReturnInspectBizParam returnInspectBizParam, SalesReturnOrderDTO salesReturnOrderDTO) {
        SalesReturnInspectDTO salesReturnInspectDTO = new SalesReturnInspectDTO();
        salesReturnInspectDTO.setInspectOrderNo(returnInspectBizParam.getInspectOrderNo());
        salesReturnInspectDTO.setStatus(SalesReturnInspectStatusEnum.CREATE.getCode());
        salesReturnInspectDTO.setContCode(returnInspectBizParam.getContCode());
        salesReturnInspectDTO.setTaxType(salesReturnOrderDTO.getTaxType());
        salesReturnInspectDTO.setRealWarehouseCode(salesReturnOrderDTO.getRealWarehouseCode());
        salesReturnInspectDTO.setRealWarehouseName(salesReturnOrderDTO.getRealWarehouseName());
        salesReturnInspectDTO.setInspectionResult(returnInspectBizParam.getInspectionResult());
        salesReturnInspectDTO.setPackageCount(1);
        return salesReturnInspectDTO;
    }

    @Override
    public Result<Boolean> completeCont(SalesReturnInspectBizParam returnInspectBizParam) {
        String syncKey = CurrentRouteHolder.getWarehouseCode() + returnInspectBizParam.getContCode();
        RLock lock = redissonClient.getLock("dt_wms_st_inspect_cont_lock:" + syncKey);
        Boolean isLock = false;
        try {
            isLock = lock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BaseException(BaseBizEnum.TIP, "操作太快了,请稍后重试");
            }
            ContainerDTO containerDTO = remoteContainerClient.queryByCode(returnInspectBizParam.getContCode());
            if (containerDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器不存在,请核查");
            }
            if (Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.DISABLE.getValue())) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器禁用,请核查");
            }
            if (Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.ENABLE.getValue())) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器空闲,不允许完成容器,请核查");
            }
            if (Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.OCCUPY.getValue())) {
                if (!Objects.equals(containerDTO.getOccupyType(), ContainerTypeEnum.XT_RECEIVE.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "当前容器非销退入库流程占用,不可用,请核查");
                }
                if (!Objects.equals(containerDTO.getOccupyNo(), returnInspectBizParam.getInspectOrderNo())) {
                    throw new BaseException(BaseBizEnum.TIP, "当前容器绑定的销退质检单号非当前单号,请核查");
                }
            }
            //获取销退质检单
            SalesReturnInspectParam salesReturnInspectParam = new SalesReturnInspectParam();
            salesReturnInspectParam.setInspectOrderNo(returnInspectBizParam.getInspectOrderNo());
            salesReturnInspectParam.setContCode(returnInspectBizParam.getContCode());
            SalesReturnInspectDTO salesReturnInspectDTO = remoteSalesReturnInspectClient.get(salesReturnInspectParam);
            if (salesReturnInspectDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器,未找到操作中的质检单");
            }
            if (Objects.equals(salesReturnInspectDTO.getStatus(), SalesReturnInspectStatusEnum.COMPLETE.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器绑定的质检单已完成,不可操作完成容器,请核查");
            }
            if (Objects.equals(salesReturnInspectDTO.getStatus(), SalesReturnInspectStatusEnum.OVER.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器绑定的质检单已结束,不可操作完成容器,请核查");
            }
            int lastNum = salesReturnInspectDTO.getPackageCount() - salesReturnInspectDTO.getShelfCount() - salesReturnInspectDTO.getOutCount();
            String inspectMessage = "销退质检完成容器";
            if (lastNum == 0) {
                salesReturnInspectDTO.setStatus(SalesReturnInspectStatusEnum.OVER.getCode());
                inspectMessage = "销退质检结束容器";
                salesReturnInspectDTO.setOverTime(System.currentTimeMillis());
            } else {
                salesReturnInspectDTO.setStatus(SalesReturnInspectStatusEnum.COMPLETE.getCode());
            }
            salesReturnInspectDTO.setCompleteTime(System.currentTimeMillis());
            remoteSalesReturnInspectClient.modify(salesReturnInspectDTO);

            //记录日志
            BillLogDTO billLogDTO = new BillLogDTO();
            billLogDTO.setBillNo(salesReturnInspectDTO.getInspectOrderNo());
            billLogDTO.setBillType(BillLogTypeEnum.RS_SALES_ZJ.getType());
            billLogDTO.setOpBy(CurrentUserHolder.getUserName());
            billLogDTO.setOpDate(System.currentTimeMillis());
            billLogDTO.setOpContent(inspectMessage);
            billLogDTO.setOpRemark(inspectMessage);
            remoteBillLogClient.save(billLogDTO);
            //释放容器
            if (Objects.equals(SalesReturnInspectStatusEnum.OVER.getCode(), salesReturnInspectDTO.getStatus())) {
                //解绑日志
                ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                containerLogDTO.setWarehouseCode(containerDTO.getWarehouseCode());
                containerLogDTO.setContCode(containerDTO.getCode());
                containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                containerLogDTO.setCreatedTime(System.currentTimeMillis());
                containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                String message = StringUtils.isEmpty(containerDTO.getOccupyType()) ? "" : ContainerTypeEnum.getEnum(containerDTO.getOccupyType()).getName();
                containerLogDTO.setOpContent(String.format("销退质检完成容器【作业类型:%s】单号%s释放容器:%s", message, containerDTO.getOccupyNo(), containerDTO.getCode()));
                containerLogDTO.setOpDate(System.currentTimeMillis());
                containerLogDTO.setOccupyNo(containerDTO.getOccupyNo());
                containerLogDTO.setOccupyType(containerDTO.getOccupyType());
                remoteContainerLogClient.save(containerLogDTO);
                //释放容器
                ContainerParam containerParam = ConverterUtil.convert(containerDTO, ContainerParam.class);
                containerParam.setStatus(ContainerStatusEnum.ENABLE.getValue());
                containerParam.setRemark("");
                containerParam.setOccupyType("");
                remoteContainerClient.release(containerParam);
            }
        } catch (Exception e) {
            e.printStackTrace();
            String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "操作太快了,请稍后重试!!!" : e.getMessage();
            throw new BaseException(BaseBizEnum.TIP, errorMsg);
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Page<SalesReturnInspectDetailBizDTO>> getPageToDetail(SalesReturnInspectBizParam returnInspectBizParam) {
        SalesReturnInspectDetailParam salesReturnInspectDetailParam = ConverterUtil.convert(returnInspectBizParam, SalesReturnInspectDetailParam.class);
        Page<SalesReturnInspectDetailDTO> returnExtraDTOPage = remoteSalesReturnInspectDetailClient.getPage(salesReturnInspectDetailParam);
        Page<SalesReturnInspectDetailBizDTO> result = ConverterUtil.convertPage(returnExtraDTOPage, SalesReturnInspectDetailBizDTO.class);
        return Result.success(result);
    }

    @ApiOperation("使用退货仓的效期天数替换发货仓的效期天数")
    private void substituteLifeCycle(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnInspectSalesOrderSkuBizDTO> salesReturnInspectSalesOrderSkuBizDTOList) {
        if (null == salesReturnOrderDTO) return;
        if (!RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource())) return;
        if (CollectionUtil.isEmpty(salesReturnInspectSalesOrderSkuBizDTOList)) return;
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setCargoCode(salesReturnOrderDTO.getCargoCode());
        skuUpcParam.setUpcCodeList(salesReturnInspectSalesOrderSkuBizDTOList.stream().flatMap(it -> it.getUpcCodeList().stream()).collect(Collectors.toList()));
        List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
        if (CollectionUtil.isEmpty(skuUpcList)) {
            log.error("退货仓条码信息异常");
            return;
        }

        Map<String, SkuUpcDTO> upcMap = skuUpcList.stream().collect(Collectors.toMap(SkuUpcDTO::getUpcCode, Function.identity(), BinaryOperator.maxBy(Comparator.comparing(SkuUpcDTO::getId))));

        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(salesReturnOrderDTO.getCargoCode());
        skuParam.setCodeList(skuUpcList.stream().map(SkuUpcDTO::getSkuCode).distinct().collect(Collectors.toList()));
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtil.isEmpty(skuDTOList)) {
            log.error("退货仓商品档案信息异常");
            return;
        }

        Map<String, SkuDTO> skuMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity(), BinaryOperator.maxBy(Comparator.comparing(SkuDTO::getId))));

        for (SalesReturnInspectSalesOrderSkuBizDTO salesReturnInspectSalesOrderSkuBizDTO : salesReturnInspectSalesOrderSkuBizDTOList) {
            for (String upcCode : salesReturnInspectSalesOrderSkuBizDTO.getUpcCodeList()) {
                SkuUpcDTO skuUpcDTO = upcMap.get(upcCode);
                if (null != skuUpcDTO) {
                    SkuDTO skuDTO = skuMap.get(skuUpcDTO.getSkuCode());
                    if (null != skuDTO) {
                        salesReturnInspectSalesOrderSkuBizDTO.setIsLifeMgt(SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode().equals(skuDTO.getIsLifeMgt()));
                        salesReturnInspectSalesOrderSkuBizDTO.setLifeCycle(skuDTO.getLifeCycle());
                        salesReturnInspectSalesOrderSkuBizDTO.setWithdrawCycle(skuDTO.getWithdrawCycle());
                        salesReturnInspectSalesOrderSkuBizDTO.setRejectCycle(skuDTO.getRejectCycle());
                        if (null != salesReturnInspectSalesOrderSkuBizDTO.getExpireDate() && salesReturnInspectSalesOrderSkuBizDTO.getExpireDate() > 0 && salesReturnInspectSalesOrderSkuBizDTO.getIsLifeMgt()) {
                            salesReturnInspectSalesOrderSkuBizDTO.setManufDate(DateTime.of(salesReturnInspectSalesOrderSkuBizDTO.getExpireDate()).offset(DateField.DAY_OF_YEAR, -skuDTO.getLifeCycle()).getTime());
                            salesReturnInspectSalesOrderSkuBizDTO.setManufDateDesc(DateDescUtil.normalDateStr(salesReturnInspectSalesOrderSkuBizDTO.getManufDate()));
                            log.info("通过失效日期计算生产日期 {} {}", salesReturnOrderDTO.getSalesReturnOrderNo(), skuDTO.getCode());
                        }
                        break;
                    }
                }
            }
        }
    }

    @Override
    public Result<SalesReturnInspectCheckBizDto> checkSalesReturnBefore(SalesReturnInspectBizParam returnInspectBizParam) {
        //  获取销退单
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
        SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
        if (salesReturnOrderDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "未找到销退单,请核查");
        }

        Integer billSource = salesReturnOrderDTO.getBillSource();
        if (billSource.equals(RSBillSourceEnum.OMS.getCode())) {
            return Result.success(new SalesReturnInspectCheckBizDto());
        }
        SalesReturnOrderDetailParam salesReturnOrderDetailParam = new SalesReturnOrderDetailParam();
        salesReturnOrderDetailParam.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList = remoteSalesReturnOrderDetailClient.getList(salesReturnOrderDetailParam);

        if (billSource.equals(RSBillSourceEnum.MALL_PLATFORM.getCode())) {
            return checkSalesReturnWithMallPlatformBefore(salesReturnOrderDTO, salesReturnOrderDetailDTOList);
        }
        if (billSource.equals(RSBillSourceEnum.MALL_DIRECT.getCode())) {
            syncSkuEntranceIfNeed(salesReturnOrderDTO, salesReturnOrderDetailDTOList);
        }
        // 订单类型为 OMS 天猫直营 直接通过
        return Result.success(new SalesReturnInspectCheckBizDto());
    }

    private void syncSkuEntranceIfNeed(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList) {
        for (SalesReturnOrderDetailDTO salesReturnOrderDetailDTO : salesReturnOrderDetailDTOList) {
            SkuParam query = new SkuParam();
            query.setCargoCode(salesReturnOrderDTO.getCargoCode());
            query.setCode(salesReturnOrderDetailDTO.getSkuCode());
            SkuDTO skuDTO = remoteSkuClient.get(query);
            if (skuDTO.getIsEntrance() == null) {
                // 补全数据
                try {
                    String shipmentCargoCode = salesReturnOrderDTO.salesReturnOrderExtraDTO().getShipmentCargoCode();
                    SkuUpcParam skuUpcParam = new SkuUpcParam();
                    skuUpcParam.setCargoCode(salesReturnOrderDTO.getCargoCode());
                    skuUpcParam.setSkuCode(skuDTO.getCode());
                    List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
                    if (CollectionUtil.isNotEmpty(skuUpcList)) {
                        CallUnderOtherContext.execute(() -> {
                            SkuUpcParam shipmentSkuUpcParam = new SkuUpcParam();
                            shipmentSkuUpcParam.setCargoCode(shipmentCargoCode);
                            shipmentSkuUpcParam.setUpcCodeList(skuUpcList.stream().map(SkuUpcDTO::getUpcCode).distinct().collect(Collectors.toList()));
                            List<SkuUpcDTO> shipmentSkuUpcList = remoteSkuClient.getSkuUpcList(shipmentSkuUpcParam);
                            if (CollectionUtil.isNotEmpty(shipmentSkuUpcList)) {
                                shipmentSkuUpcList.stream()
                                        .filter(skuUpcDTO -> skuUpcDTO.getSkuCode().equalsIgnoreCase(skuDTO.getCode()))
                                        .findFirst().ifPresent(skuUpcDTO -> {
                                            SkuParam skuParam = new SkuParam();
                                            skuParam.setCargoCode(skuUpcDTO.getCargoCode());
                                            skuParam.setCode(skuUpcDTO.getSkuCode());
                                            Optional.ofNullable(remoteSkuClient.get(skuParam)).ifPresent(it -> {
                                                if (null != it.getIsEntrance()) {
                                                    skuDTO.setIsEntrance(it.getIsEntrance());
                                                    log.info("{}调用商品中心补充入口类信息{}", skuDTO.getCode(), skuDTO.getWarehouseCode());
                                                    remoteSkuClient.modify(skuDTO);
                                                }
                                            });

                                        });
                            }
                            return null;
                        }, salesReturnOrderDTO.getRealWarehouseCode());
                    }
                } catch (Exception exception) {
                    log.error("自动补全商品档案是否入口类信息异常", exception);
                }
            }
        }
    }


    /**
     * 校验订单类型为天猫平台的销退单
     */
    private Result<SalesReturnInspectCheckBizDto> checkSalesReturnWithMallPlatformBefore(SalesReturnOrderDTO salesReturnOrderDTO, List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList) {
        SalesReturnInspectCheckBizDto result = new SalesReturnInspectCheckBizDto();
        List<SkuUpcCode> skuUpcCodeList = new ArrayList<>();
        for (int i = 0; i < salesReturnOrderDetailDTOList.size(); i++) {
            SalesReturnOrderDetailDTO salesReturnOrderDetailDTO = salesReturnOrderDetailDTOList.get(i);
            SkuParam query = new SkuParam();
            query.setCargoCode(salesReturnOrderDTO.getCargoCode());
            query.setCode(salesReturnOrderDetailDTO.getSkuCode());
            SkuDTO skuDTO = remoteSkuClient.get(query);
            if (null == skuDTO) {
                throw ExceptionUtil.exceptionWithMessage("商品不存在");
            }
            if (skuDTO.getIsEntrance() == null) {
                // 商品没有维护是否是入口类
                if (salesReturnOrderDTO.getReturnType().equals(RSReturnTypeEnum.CUSTOMER_RETURNS.getCode())) {
                    // 退款类型为客退件
                    SkuUpcCode skuUpcCode = new SkuUpcCode();
                    skuUpcCode.setSkuCode(salesReturnOrderDetailDTO.getSkuCode());
                    skuUpcCode.setUpcCode(salesReturnOrderDetailDTO.getUpcCode());
                    skuUpcCodeList.add(skuUpcCode);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(skuUpcCodeList)) {
            int i = 1;
            for (SkuUpcCode skuUpcCode : skuUpcCodeList) {
                skuUpcCode.setId(String.valueOf(i));
                i++;
            }
            result.setCargoName(salesReturnOrderDTO.getCargoName());
            result.setSkuUpcCodeList(skuUpcCodeList);
            return Result.fail(result);
        }
        return Result.success(result);
    }


    @Override
    public Result<SalesReturnInspectSalesOrderBizDTO> getSalesOrderInformation(SalesReturnInspectBizParam returnInspectBizParam) {
        //获取销退单
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
        SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
        if (salesReturnOrderDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "未找到销退单,请核查");
        }

        SalesReturnOrderDetailParam salesReturnOrderDetailParam = new SalesReturnOrderDetailParam();
        salesReturnOrderDetailParam.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        List<SalesReturnOrderDetailDTO> returnOrderDetailClientList = remoteSalesReturnOrderDetailClient.getList(salesReturnOrderDetailParam);

        // 查询发货信息
        List<AllocationOrderDTO> allocationOrderDTOList = CallUnderOtherContext.execute(() -> shipmentInfo(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());
        List<String> skuCodeList = allocationOrderDTOList.stream().map(AllocationOrderDTO::getSkuCode).distinct().collect(Collectors.toList());

        SalesReturnInspectParam salesReturnInspectParam = new SalesReturnInspectParam();
        salesReturnInspectParam.setInspectOrderNo(returnInspectBizParam.getInspectOrderNo());
        salesReturnInspectParam.setContCode(returnInspectBizParam.getContCode());
        SalesReturnInspectDTO salesReturnInspectDTO = remoteSalesReturnInspectClient.get(salesReturnInspectParam);
        if (salesReturnInspectDTO != null) {
            if (Objects.equals(salesReturnInspectDTO.getStatus(), SalesReturnInspectStatusEnum.COMPLETE.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器绑定的质检单已完成,不可操作,请核查");
            }
            if (Objects.equals(salesReturnInspectDTO.getStatus(), SalesReturnInspectStatusEnum.OVER.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "当前容器绑定的质检单已结束,不可操作,请核查");
            }
            if (!Objects.equals(salesReturnInspectDTO.getRealWarehouseCode(), salesReturnOrderDTO.getRealWarehouseCode())) {
                throw new BaseException(BaseBizEnum.TIP, "不同实体仓不允许混放,请更换容器");
            }
            if (!Objects.equals(salesReturnInspectDTO.getTaxType(), salesReturnOrderDTO.getTaxType())) {
                throw new BaseException(BaseBizEnum.TIP, "不同贸易类型不允许混放,请更换容器");
            }
        }

        SalesReturnInspectDetailParam salesReturnInspectDetailParam = new SalesReturnInspectDetailParam();
        salesReturnInspectDetailParam.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        List<SalesReturnInspectDetailDTO> salesReturnInspectDetailDTOList = remoteSalesReturnInspectDetailClient.getList(salesReturnInspectDetailParam);
        if (!CollectionUtils.isEmpty(salesReturnInspectDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("销退单号:%s,运单号:%s,存在质检记录,请核查", salesReturnOrderDTO.getSalesReturnOrderNo(), salesReturnOrderDTO.getReverseExpressNo()));
        }

        //获取wms实体仓当前货主所有条码
        RpcContextUtil.setWarehouseCode(salesReturnOrderDTO.getRealWarehouseCode());
        // 这个是真实发货仓发货的货主
        String shipmentCargoCode = salesReturnOrderDTO.salesReturnOrderExtraDTO().getShipmentCargoCode();
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setCargoCode(salesReturnOrderDTO.getCargoCode()); //
        if (StrUtil.isNotBlank(shipmentCargoCode)) {
            skuUpcParam.setCargoCode(shipmentCargoCode);
        }
        skuUpcParam.setStatus(SkuStatusEnum.STATUS_ENABLED.getStatus());
        skuUpcParam.setSkuCodeList(skuCodeList);
        List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
        if (CollectionUtils.isEmpty(skuUpcList)) {
            skuUpcList = new ArrayList<>();
        }
        Map<String, List<SkuUpcDTO>> skuUpcMap = skuUpcList.stream().collect(Collectors.groupingBy(SkuUpcDTO::getSkuCode));
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(salesReturnOrderDTO.getCargoCode()); //
        if (StrUtil.isNotBlank(shipmentCargoCode)) {
            skuParam.setCargoCode(shipmentCargoCode);
        }
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuList)) {
            skuList = new ArrayList<>();
        }

        Map<String, SkuLotDTO> skuLotMap = remoteSkuLotClient.skuLotMap(allocationOrderDTOList.stream().map(AllocationOrderDTO::getSkuLotNo).distinct().collect(Collectors.toList()));

        //数据源设置回来
        RpcContextUtil.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
        Map<String, SkuDTO> skuMap = skuList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity()));
        Map<String, List<AllocationOrderDTO>> allocationGroup = allocationOrderDTOList.stream().collect(Collectors.groupingBy(AllocationOrderDTO::getSkuLotNo));

        SalesReturnInspectSalesOrderBizDTO salesReturnInspectSalesOrderBizDTO = new SalesReturnInspectSalesOrderBizDTO();
        salesReturnInspectSalesOrderBizDTO.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        salesReturnInspectSalesOrderBizDTO.setSaleShopId(salesReturnOrderDTO.getSaleShopId());
        salesReturnInspectSalesOrderBizDTO.setSaleShopName(salesReturnOrderDTO.getSaleShopName());
        salesReturnInspectSalesOrderBizDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
        salesReturnInspectSalesOrderBizDTO.setCargoName(salesReturnOrderDTO.getCargoName());
        salesReturnInspectSalesOrderBizDTO.setBillSource(salesReturnOrderDTO.getBillSource());
        salesReturnInspectSalesOrderBizDTO.setBillSourceDesc(RSBillSourceEnum.desc(salesReturnOrderDTO.getBillSource()));
        salesReturnInspectSalesOrderBizDTO.setInstructionType(salesReturnOrderDTO.salesReturnOrderExtraDTO().getInstructionType());
        salesReturnInspectSalesOrderBizDTO.setInstructionTypeDesc(RSAdditionalOrderEnum.desc(salesReturnOrderDTO.salesReturnOrderExtraDTO().getInstructionType()));
        salesReturnInspectSalesOrderBizDTO.setReturnReasonDesc(RSReturnReasonEnum.desc(salesReturnOrderDTO.getReturnReason()));
        salesReturnInspectSalesOrderBizDTO.setHighRiskCustomDesc(salesReturnOrderDTO.highRiskCustomDesc());
        salesReturnInspectSalesOrderBizDTO.setHighRiskPackageDesc(salesReturnOrderDTO.highRiskPackageDesc());
        salesReturnInspectSalesOrderBizDTO.setReturnTypeDesc(RSReturnTypeEnum.desc(salesReturnOrderDTO.getReturnType()));
        salesReturnInspectSalesOrderBizDTO.setRealWarehouseCode(salesReturnOrderDTO.getRealWarehouseCode());
        salesReturnInspectSalesOrderBizDTO.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());

        if (Objects.equals(salesReturnOrderDTO.getTaxType(), TaxTypeEnum.TYPE_BONDED_TAX.getCode())) {
            Long betweenSecond = DateUtil.betweenMs(new Date(salesReturnOrderDTO.getClearanceTime()), new Date()) / 1000;
            if (betweenSecond > 0) {
//                salesReturnInspectSalesOrderBizDTO.setDeclareTime(DateUtil.betweenDay(new Date(salesReturnOrderDTO.getDeclareTime()), new Date(), false));
                BigDecimal bigDecimal = new BigDecimal(betweenSecond + "").divide(new BigDecimal("86400"), 10, BigDecimal.ROUND_HALF_UP);
                salesReturnInspectSalesOrderBizDTO.setDeclareTime(bigDecimal);
            } else {
                salesReturnInspectSalesOrderBizDTO.setDeclareTime(BigDecimal.ZERO);
            }
        } else {
            salesReturnInspectSalesOrderBizDTO.setDeclareTime(BigDecimal.ZERO);
        }
        salesReturnInspectSalesOrderBizDTO.setTaxType(salesReturnOrderDTO.getTaxType());
        salesReturnInspectSalesOrderBizDTO.setTaxTypeDesc(TaxTypeEnum.desc(salesReturnOrderDTO.getTaxType()));
        List<SalesReturnInspectSalesOrderSkuBizDTO> salesReturnInspectSalesOrderSkuBizDTOList = new ArrayList<>();
        allocationGroup.forEach((key, value) -> {
            AllocationOrderDTO allocationOrderDTO = value.get(0);
            SalesReturnInspectSalesOrderSkuBizDTO salesReturnInspectSalesOrderSkuBizDTO = new SalesReturnInspectSalesOrderSkuBizDTO();
            salesReturnInspectSalesOrderSkuBizDTO.setSkuLotNo(allocationOrderDTO.getSkuLotNo());
            salesReturnInspectSalesOrderSkuBizDTO.setSkuCode(allocationOrderDTO.getSkuCode());
            salesReturnInspectSalesOrderSkuBizDTO.setUpcCodeList(new ArrayList<>());
            List<SkuUpcDTO> skuUpcDTOList = skuUpcMap.get(allocationOrderDTO.getSkuCode());
            if (!CollectionUtils.isEmpty(skuUpcDTOList)) {
                skuUpcDTOList.stream().findFirst().ifPresent(skuUpcDTO -> salesReturnInspectSalesOrderSkuBizDTO.setUpcCode(skuUpcDTO.getUpcCode()));
                skuUpcDTOList.stream().filter(it -> it.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus())).findFirst().ifPresent(skuUpcDTO -> salesReturnInspectSalesOrderSkuBizDTO.setUpcCode(skuUpcDTO.getUpcCode()));
                salesReturnInspectSalesOrderSkuBizDTO.setUpcCodeList(skuUpcDTOList.stream().map(SkuUpcDTO::getUpcCode).collect(Collectors.toList()));
            }
            salesReturnInspectSalesOrderSkuBizDTO.setIsLifeMgt(false);
            salesReturnInspectSalesOrderSkuBizDTO.setLifeCycle(0);
            SkuDTO skuDTO = skuMap.get(allocationOrderDTO.getSkuCode());
            if (skuDTO != null) {
                salesReturnInspectSalesOrderSkuBizDTO.setSkuName(skuDTO.getName());
                salesReturnInspectSalesOrderSkuBizDTO.setRejectCycle(skuDTO.getRejectCycle());
                salesReturnInspectSalesOrderSkuBizDTO.setWithdrawCycle(skuDTO.getWithdrawCycle());
                salesReturnInspectSalesOrderSkuBizDTO.setIsEntrance(skuDTO.getIsEntrance());
                if (Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {
                    salesReturnInspectSalesOrderSkuBizDTO.setIsLifeMgt(true);
                    salesReturnInspectSalesOrderSkuBizDTO.setLifeCycle(skuDTO.getLifeCycle());
                }
            }
            salesReturnInspectSalesOrderSkuBizDTO.setId(UUID.fastUUID().toString());
            Optional.ofNullable(skuLotMap.get(allocationOrderDTO.getSkuLotNo())).ifPresent(it -> {
                salesReturnInspectSalesOrderSkuBizDTO.setExpireDate(it.getExpireDate());
                salesReturnInspectSalesOrderSkuBizDTO.setExpireDateDesc(DateDescUtil.normalDateStr(it.getExpireDate()));
                salesReturnInspectSalesOrderSkuBizDTO.setSkuQuality(it.getSkuQuality());
                salesReturnInspectSalesOrderSkuBizDTO.setSkuQualityDesc(SkuQualityEnum.desc(it.getSkuQuality()));
                salesReturnInspectSalesOrderSkuBizDTO.setManufDate(it.getManufDate());
                salesReturnInspectSalesOrderSkuBizDTO.setManufDateDesc(DateDescUtil.normalDateStr(it.getManufDate()));
            });
            salesReturnInspectSalesOrderSkuBizDTO.setExpQty(value.stream().map(AllocationOrderDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            salesReturnInspectSalesOrderSkuBizDTOList.add(salesReturnInspectSalesOrderSkuBizDTO);
        });

        substituteLifeCycle(salesReturnOrderDTO, salesReturnInspectSalesOrderSkuBizDTOList);

        salesReturnInspectSalesOrderBizDTO.setSkuType(salesReturnInspectSalesOrderSkuBizDTOList.stream().map(SalesReturnInspectSalesOrderSkuBizDTO::getSkuCode).distinct().count());
        salesReturnInspectSalesOrderBizDTO.setSkuQty(salesReturnInspectSalesOrderSkuBizDTOList.stream().map(SalesReturnInspectSalesOrderSkuBizDTO::getExpQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        salesReturnInspectSalesOrderBizDTO.setSkuList(salesReturnInspectSalesOrderSkuBizDTOList);
        //更新销退单质检中
        if (Objects.equals(salesReturnOrderDTO.getStatus(), RSOrderStatusEnum.HANDOVER.getCode())) {
            salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CHECKING.getCode());
            SalesReturnOrderDTO salesReturnOrderDTONew = remoteSalesReturnOrderClient.buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
            remoteSalesReturnOrderClient.modify(salesReturnOrderDTONew);

            //记录日志
            BillLogDTO billLogDTO = new BillLogDTO();
            billLogDTO.setBillNo(salesReturnOrderDTONew.getSalesReturnOrderNo());
            billLogDTO.setBillType(BillLogTypeEnum.RS_SALES_ORDER.getType());
            billLogDTO.setOpBy(CurrentUserHolder.getUserName());
            billLogDTO.setOpDate(System.currentTimeMillis());
            billLogDTO.setOpContent("开始质检");
            billLogDTO.setOpRemark("");
            remoteBillLogClient.save(billLogDTO);
        }
        //设置是否是淘天仓
        salesReturnInspectSalesOrderBizDTO.setIsTaoTian(RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource()));
        // 增加防伪码信息
        List<SkuTraceCode> skuTraceCodeList = CallUnderOtherContext.execute(() -> skuTraceCodeList(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());
        List<SkuSnCode> snCodeList = CallUnderOtherContext.execute(() -> skuSnCodeList(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());
        List<SkuSourceCode> sourceCodeList = CallUnderOtherContext.execute(() -> sourceCodeList(salesReturnOrderDTO.getPoNo()), salesReturnOrderDTO.getRealWarehouseCode());

        // 查询正向出库单无溯源记录，判断销退单上的商品信息如果是天猫平台+客退件+入口类品，增加流程扫描溯源码
        if (RSBillSourceEnum.MALL_PLATFORM.getCode().equals(salesReturnOrderDTO.getBillSource())
                && RSReturnTypeEnum.CUSTOMER_RETURNS.getCode().equals(salesReturnOrderDTO.getReturnType())) {
            for (SalesReturnInspectSalesOrderSkuBizDTO salesReturnInspectSalesOrderSkuBizDTO : salesReturnInspectSalesOrderBizDTO.getSkuList()) {
                if (SkuEntranceEnum.ENTRANCE_YES.getCode().equals(salesReturnInspectSalesOrderSkuBizDTO.getIsEntrance())) {
                    long count = sourceCodeList.stream()
                            .filter(skuSourceCode -> skuSourceCode.getSkuCode().equalsIgnoreCase(salesReturnInspectSalesOrderSkuBizDTO.getSkuCode()))
                            .count();
                    if (count == 0L) {
                        salesReturnInspectSalesOrderSkuBizDTO.setNeedAddSourceCode(true);
                    }
                }
            }
        }


        RpcContextUtil.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
        salesReturnInspectSalesOrderBizDTO.setSkuTraceCodeList(skuTraceCodeList);
        salesReturnInspectSalesOrderBizDTO.setSkuSnCodeList(snCodeList);
        salesReturnInspectSalesOrderBizDTO.setSourceCodeList(sourceCodeList);

        // 是否支持二次入区
        if (RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource()) && salesReturnOrderDTO.getTaxType().equalsIgnoreCase(TaxTypeEnum.TYPE_BONDED_TAX.getCode())) {
            Boolean b = remoteErpCargoConfigClient.saleReturnAllowEntry(salesReturnOrderDTO.getRealWarehouseCode(), salesReturnOrderDTO.getCargoCode());
            if (!b) {
                salesReturnInspectSalesOrderBizDTO.setNotAllowEntry(true);
                salesReturnInspectSalesOrderBizDTO.setNotAllowEntryDesc("不支持二次入区");
            }
        }

        // 销退单明细
        skuCodeList = returnOrderDetailClientList.stream().map(SalesReturnOrderDetailDTO::getSkuCode).collect(Collectors.toList());
        skuUpcParam = new SkuUpcParam();
        skuUpcParam.setCargoCode(salesReturnOrderDTO.getCargoCode());
        skuUpcParam.setSkuCodeList(skuCodeList);
        skuUpcParam.setStatus(SkuStatusEnum.STATUS_ENABLED.getStatus());
        skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
        Map<String, List<SkuUpcDTO>> returnWarehouseSkuUpcMap = skuUpcList.stream().collect(Collectors.groupingBy(SkuUpcDTO::getSkuCode));
        List<SalesReturnOrderDetailBizDTO> salesReturnOrderDetailBizDTOList = returnOrderDetailClientList.stream()
                .map(it -> ConverterUtil.convert(it, SalesReturnOrderDetailBizDTO.class))
                .filter(Objects::nonNull)
                .peek(it -> {
                    if (RSBillSourceEnum.taoTianBillSourceCodeList().contains(salesReturnOrderDTO.getBillSource())) {
                        Optional.ofNullable(returnWarehouseSkuUpcMap.get(it.getSkuCode())).ifPresent(skuUpcDTOS -> {
                            it.setUpcCodeList(skuUpcDTOS.stream().map(SkuUpcDTO::getUpcCode).collect(Collectors.toList()));
                        });
                    }
                    if (RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource())) {
                        Optional.ofNullable(skuUpcMap.get(it.getSkuCode())).ifPresent(skuUpcDTOS -> {
                            it.setUpcCodeList(skuUpcDTOS.stream().map(SkuUpcDTO::getUpcCode).collect(Collectors.toList()));
                        });
                    }
                    if (RSBillSourceEnum.DY_MARKET.getCode().equals(salesReturnOrderDTO.getBillSource())) {
                        Optional.ofNullable(skuUpcMap.get(it.getSkuCode())).ifPresent(skuUpcDTOS -> {
                            it.setUpcCodeList(skuUpcDTOS.stream().map(SkuUpcDTO::getUpcCode).collect(Collectors.toList()));
                        });
                    }

                    it.setInventoryTypeDesc(InventoryTypeEnum.desc(it.getInventoryType()));
                    if (StrUtil.isNotBlank(it.getSkuQuality())) {
                        it.setSkuQualityDesc(SkuQualityEnum.desc(it.getSkuQuality()));
                    } else {
                        it.setSkuQualityDesc(InventoryTypeEnum.skuQualityDesc(it.getInventoryType()));
                    }
                    // 当为淘天仓 并且退货类型是 客退件时 需要判断是否是入口类
                    if (salesReturnInspectSalesOrderBizDTO.getIsTaoTian()) {
                        SkuParam skuParams = new SkuParam();
                        skuParams.setCargoCode(salesReturnOrderDTO.getCargoCode());
                        skuParams.setCode(it.getSkuCode());
                        SkuDTO skuDTO = remoteSkuClient.get(skuParams);
                        it.setIsEntrance(skuDTO.getIsEntrance());
                    }
                })
                .collect(Collectors.toList());
        salesReturnInspectSalesOrderBizDTO.setSalesReturnOrderDetailBizDTOList(salesReturnOrderDetailBizDTOList);

        // 给前端补充数据
        for (SalesReturnInspectSalesOrderSkuBizDTO salesReturnInspectSalesOrderSkuBizDTO : salesReturnInspectSalesOrderBizDTO.getSkuList()) {
            salesReturnOrderDetailBizDTOList.stream()
                    .filter(salesReturnOrderDetailBizDTO -> salesReturnOrderDetailBizDTO.getUpcCodeList().stream().anyMatch(it -> salesReturnInspectSalesOrderSkuBizDTO.getUpcCodeList().stream().anyMatch(itt -> itt.equalsIgnoreCase(it))))
                    .findFirst().ifPresent(it -> {
                        salesReturnInspectSalesOrderSkuBizDTO.setAllowEntry(it.getAllowEntry());
                    });
        }

        return Result.success(salesReturnInspectSalesOrderBizDTO);
    }


    private List<AllocationOrderDTO> shipmentInfo(String poNo) {
        if (StrUtil.isBlank(poNo)) throw ExceptionUtil.DATA_ERROR;
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
        shipmentOrderParam.setPoNo(poNo);
        ShipmentOrderDTO shipmentOrderDTO = remoteShipmentOrderClient.get(shipmentOrderParam);
        if (null == shipmentOrderDTO) throw ExceptionUtil.exceptionWithMessage("发货信息不存在");

        PackageParam packageParam = new PackageParam();
        packageParam.setPoNo(poNo);
        packageParam.setShipmentOrderCode(shipmentOrderDTO.getShipmentOrderCode());
        packageParam.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
        if (CollectionUtil.isEmpty(packageDTOList)) {
            throw ExceptionUtil.exceptionWithMessage("发货信息不存在");
        }

        AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
        allocationOrderParam.setShipmentOrderCode(shipmentOrderDTO.getShipmentOrderCode());
        allocationOrderParam.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList()));
        allocationOrderParam.setWaveCodeList(packageDTOList.stream().map(PackageDTO::getWaveCode).distinct().collect(Collectors.toList()));
        List<AllocationOrderDTO> allocationOrderDTOList = remoteAllocationOrderClient.getList(allocationOrderParam);
        if (CollectionUtil.isEmpty(allocationOrderDTOList)) {
            throw ExceptionUtil.exceptionWithMessage("发货信息不存在");
        }

        return allocationOrderDTOList;
    }

    private List<SkuTraceCode> skuTraceCodeList(String poNo) {
        if (StrUtil.isBlank(poNo)) return ListUtil.empty();
        OutSourceCodeParam param = new OutSourceCodeParam();
        param.setPoNo(poNo);
        List<OutSourceCodeDTO> list = remoteOutSourceCodeClient.getList(param);
        return list.stream()
                .filter(it -> it.getScanType().equalsIgnoreCase(SourceCodeScanTypeEnum.ANTI_COUNTERFEITING_BUCKLE.getCode())
                        || it.getScanType().equalsIgnoreCase(SourceCodeScanTypeEnum.SEALING_TAPE.getCode())
                        || it.getScanType().equalsIgnoreCase(SourceCodeScanTypeEnum.PULL_TAPE_CODE.getCode()))
                .flatMap(it -> IntStream.range(0, it.getQty().intValue())
                        .mapToObj(itt -> SkuTraceCode.builder().skuCode(it.getSkuCode()).traceCode(it.getSnCode()).id(IdUtil.simpleUUID()).build()))
                .collect(Collectors.toList());
    }

    private List<SkuSourceCode> sourceCodeList(String poNo) {
        if (StrUtil.isBlank(poNo)) return ListUtil.empty();
        OutSourceCodeParam param = new OutSourceCodeParam();
        param.setPoNo(poNo);
        List<OutSourceCodeDTO> list = remoteOutSourceCodeClient.getList(param);
        return list.stream()
                .filter(it -> it.getScanType().equalsIgnoreCase(SourceCodeScanTypeEnum.SOURCE_CODE.getCode()))
                .flatMap(it -> IntStream.range(0, it.getQty().intValue())
                        .mapToObj(itt -> SkuSourceCode.builder().skuCode(it.getSkuCode()).sourceCode(it.getSnCode()).id(IdUtil.simpleUUID()).build()))
                .collect(Collectors.toList());
    }

    private List<SkuSnCode> skuSnCodeList(String poNo) {
        if (StrUtil.isBlank(poNo)) return ListUtil.empty();
        OutSourceCodeParam param = new OutSourceCodeParam();
        param.setPoNo(poNo);
        List<OutSourceCodeDTO> list = remoteOutSourceCodeClient.getList(param);
        return list.stream()
                .filter(it -> it.getScanType().equalsIgnoreCase(SourceCodeScanTypeEnum.SN.getCode()))
                .flatMap(it -> IntStream.range(0, it.getQty().intValue())
                        .mapToObj(itt -> SkuSnCode.builder().skuCode(it.getSkuCode()).snCode(it.getSnCode()).id(IdUtil.simpleUUID()).build()))
                .collect(Collectors.toList());
    }

    private void checkTraceCode(List<SkuTraceCode> skuTraceCodeList, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        if (CollectionUtil.isEmpty(skuTraceCodeList)) return;
        if (CollectionUtil.isEmpty(salesReturnOrderReceiveDTOList)) return;

        Map<String, List<SkuTraceCode>> traceCodeGroupBySku = skuTraceCodeList.stream().collect(Collectors.groupingBy(SkuTraceCode::getSkuCode));
        Map<String, List<SalesReturnOrderReceiveDTO>> receiveGroupBySkuCode = salesReturnOrderReceiveDTOList.stream()
                .collect(Collectors.groupingBy(SalesReturnOrderReceiveDTO::getSkuCode));
        for (String skuCode : traceCodeGroupBySku.keySet()) {
            List<SkuTraceCode> traceCodeList = traceCodeGroupBySku.get(skuCode);
            List<String> collect = traceCodeList.stream().map(SkuTraceCode::getTraceCode).map(String::toUpperCase).distinct().collect(Collectors.toList());
            List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOS = receiveGroupBySkuCode.get(skuCode);
            if (CollectionUtil.isEmpty(traceCodeList)) continue;
            if (CollectionUtil.isEmpty(salesReturnOrderReceiveDTOS)) continue;
            // 存在防伪码等信息，收货信息里面的防伪码必须在防伪码信息中
            salesReturnOrderReceiveDTOS.stream()
                    .filter(it -> !CompareUtil.contain(collect, it.getTraceCode()))
                    .findFirst().ifPresent(salesReturnOrderReceiveDTO -> {
                        throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "商品出库信息存在防伪码等信息，质检防伪码信息必须在出库扫描的防伪码之中，商品编码", salesReturnOrderReceiveDTO.getSkuCode(), "防伪码", salesReturnOrderReceiveDTO.getTraceCode()));
                    });
            Map<String, BigDecimal> originTraceCodeUsed = traceCodeList.stream()
                    .collect(Collectors.groupingBy(SkuTraceCode::getTraceCode, Collectors.mapping(itt -> BigDecimal.ONE, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

            Map<String, BigDecimal> inspectTraceCodeUsed = salesReturnOrderReceiveDTOS.stream()
                    .collect(Collectors.groupingBy(SalesReturnOrderReceiveDTO::getTraceCode, Collectors.mapping(SalesReturnOrderReceiveDTO::getQty, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            // 溯源码使用不能超过发货信息是烧苗的数量
            for (String traceCode : inspectTraceCodeUsed.keySet()) {
                BigDecimal inspectUse = inspectTraceCodeUsed.get(traceCode);
                BigDecimal bigDecimal = originTraceCodeUsed.get(traceCode);
                if (null == bigDecimal)
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "商品出库信息存在防伪码等信息，质检防伪码信息必须在出库扫描的防伪码之中，商品编码", skuCode, "防伪码", traceCode));
                if (inspectUse.compareTo(bigDecimal) > 0)
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "商品出库信息存在防伪码等信息，质检防伪码信息必须在出库扫描的防伪码之中，商品编码", skuCode, "防伪码", traceCode));
            }
        }

    }

    private void checkSnCode(List<SkuSnCode> skuSnCodeList, List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList) {
        if (CollectionUtil.isEmpty(skuSnCodeList)) return;
        if (CollectionUtil.isEmpty(salesReturnOrderReceiveDTOList)) return;

        Map<String, List<SkuSnCode>> traceCodeGroupBySku = skuSnCodeList.stream().collect(Collectors.groupingBy(SkuSnCode::getSkuCode));
        Map<String, List<SalesReturnOrderReceiveDTO>> receiveGroupBySkuCode = salesReturnOrderReceiveDTOList.stream()
                .collect(Collectors.groupingBy(SalesReturnOrderReceiveDTO::getSkuCode));
        for (String skuCode : traceCodeGroupBySku.keySet()) {
            List<SkuSnCode> snCodeList = traceCodeGroupBySku.get(skuCode);
            List<String> collect = snCodeList.stream().map(SkuSnCode::getSnCode).map(String::toUpperCase).distinct().collect(Collectors.toList());
            List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOS = receiveGroupBySkuCode.get(skuCode);
            if (CollectionUtil.isEmpty(snCodeList)) continue;
            if (CollectionUtil.isEmpty(salesReturnOrderReceiveDTOS)) continue;
            // 存在防伪码等信息，收货信息里面的防伪码必须在防伪码信息中
            salesReturnOrderReceiveDTOS.stream()
                    .filter(it -> !CompareUtil.contain(collect, it.getSn()))
                    .findFirst().ifPresent(salesReturnOrderReceiveDTO -> {
                        throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "商品出库信息存在SN，质检SN必须在出库扫描的SN之中，商品编码", salesReturnOrderReceiveDTO.getSkuCode(), "SN", salesReturnOrderReceiveDTO.getSn()));
                    });
            Map<String, BigDecimal> originTraceCodeUsed = snCodeList.stream()
                    .collect(Collectors.groupingBy(SkuSnCode::getSnCode, Collectors.mapping(itt -> BigDecimal.ONE, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

            Map<String, BigDecimal> inspectTraceCodeUsed = salesReturnOrderReceiveDTOS.stream()
                    .collect(Collectors.groupingBy(SalesReturnOrderReceiveDTO::getSn, Collectors.mapping(SalesReturnOrderReceiveDTO::getQty, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
            // 溯源码使用不能超过发货信息是烧苗的数量
            for (String sn : inspectTraceCodeUsed.keySet()) {
                BigDecimal inspectUse = inspectTraceCodeUsed.get(sn);
                BigDecimal bigDecimal = originTraceCodeUsed.get(sn);
                if (null == bigDecimal)
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "商品出库信息存在SN，SN必须在出库扫描的SN之中，商品编码", skuCode, "SN", sn));
                if (inspectUse.compareTo(bigDecimal) > 0)
                    throw ExceptionUtil.exceptionWithMessage(StrUtil.join(StrUtil.EMPTY, "商品出库信息存在SN，SN必须在出库扫描的SN之中，商品编码", skuCode, "SN", sn));
            }
        }

    }

    @Override
    public Result<String> rejectCurrentExpressNo(SalesReturnInspectBizParam returnInspectBizParam) {
        if (StrUtil.isBlank(returnInspectBizParam.getSalesReturnOrderNo()))
            throw ExceptionUtil.exceptionWithMessage("销退单号必传");
        if (StrUtil.isBlank(returnInspectBizParam.getRejectReason()))
            throw ExceptionUtil.exceptionWithMessage("拒收原因必传");
        if (CollectionUtil.isEmpty(returnInspectBizParam.getRejectImageList()))
            throw ExceptionUtil.exceptionWithMessage("图片必传");
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(returnInspectBizParam.getSalesReturnOrderNo());
        SalesReturnOrderDTO salesReturnOrderDTO = remoteSalesReturnOrderClient.get(salesReturnOrderParam);
        if (null == salesReturnOrderDTO) {
            throw new BaseException(BaseBizEnum.TIP, "当前运单未关联销退单号,请核查");
        }

        Integer returnOrderDTOStatus = salesReturnOrderDTO.getStatus();
        if (Stream.of(RSOrderStatusEnum.CHECKING.getCode(), RSOrderStatusEnum.HANDOVER.getCode()).noneMatch(it -> it.equals(returnOrderDTOStatus))) {
            throw ExceptionUtil.exceptionWithMessage("当前单据状态不能拒收");
        }

        if (salesReturnOrderDTO.hasWarehouseReceiveInstruction()) {
            throw ExceptionUtil.exceptionWithMessage("该运单不可拒收！");
        }

        if (!RSBillSourceEnum.MALL_DIRECT.getCode().equals(salesReturnOrderDTO.getBillSource())) {
            throw ExceptionUtil.exceptionWithMessage("该环节只可拒收天猫直营的包裹，请检查！");
        }

        if (!RSReturnTypeEnum.CUSTOMER_RETURNS.getCode().equals(salesReturnOrderDTO.getReturnType())) {
            throw ExceptionUtil.exceptionWithMessage("只有客退件才可拒收，请检查！");
        }

        List<MessageMqDTO> callbackReject = remoteSalesReturnOrderClient.messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_REJECT_CALLBACK);
        List<MessageMqDTO> callbackOrigin = remoteSalesReturnOrderClient.messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN);
        callbackReject.addAll(callbackOrigin);


        SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
        salesReturnOrderExtraDTO.setRejectReason(returnInspectBizParam.getRejectReason());
        salesReturnOrderExtraDTO.setRejectImageList(returnInspectBizParam.getRejectImageList());
        salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);

        salesReturnOrderDTO.setStatus(RSOrderStatusEnum.REJECT.getCode());
        salesReturnOrderDTO = remoteSalesReturnOrderClient.buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());

        remoteSalesReturnOrderClient.billLogDTO(salesReturnOrderDTO, "已拒收", "拒收当前运单", CurrentUserHolder.getUserName());

        SalesReturnOrderBO salesReturnOrderBO = new SalesReturnOrderBO();
        salesReturnOrderBO.setSalesReturnOrderDTOList(ListUtil.toList(salesReturnOrderDTO));
        salesReturnOrderBO.setMessageMqDTOList(callbackReject);
        remoteSalesReturnOrderClient.save(salesReturnOrderBO);

        remoteSalesReturnOrderClient.callback(callbackReject);
        return Result.success();
    }

    private void filterLot(SkuLotAndStockDTO skuLotAndStockDTO, SkuDTO skuDTO) {
        //未开启效期管理的数据 或者禁售日期不大于0
        if (skuDTO == null || skuDTO.getWithdrawCycle() <= 0) {
            return;
        }
        //未开启效期管理的数据
        if (!skuDTO.getIsLifeMgt().equals(SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())) {
            return;
        }
        //批次未录入失效日期,效期商品无失效日期
        if (skuLotAndStockDTO != null && skuLotAndStockDTO.getExpireDate() <= 0) {
            return;
        }
        //计算禁售日期是否禁售
        Long withdrawDate = skuLotAndStockDTO.getExpireDate() - Long.valueOf(skuDTO.getWithdrawCycle()) * CommonConstantUtil.DAY_MILLISECONDS;
        if (withdrawDate < System.currentTimeMillis()) {
            throw ExceptionUtil.exceptionWithMessage("禁售商品不可为正品");
        }
    }

}
