package com.dt.platform.wms.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.cds.out.api.InventoryOrderRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.InventoryOrderInfoCwSubmitReqVO;
import com.danding.cds.out.bean.vo.req.InventoryOrderItemCwSubmitReqVO;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.base.LocationTypeEnum;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.bill.MessageMqStatusEnum;
import com.dt.component.common.enums.bill.ShipmentCustomsClearanceStatusEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.cw.CWCollectTypeEnum;
import com.dt.component.common.enums.cw.FirstLineEnum;
import com.dt.component.common.enums.cw.StorageApplicationCabinetStatusEnum;
import com.dt.component.common.enums.cw.StorageApplicationStatusEnum;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.component.common.enums.pre.BillLogTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.sku.SkuTagEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.msg.StockOperationMessage;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.base.param.LocationParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.base.param.ZoneParam;
import com.dt.domain.bill.bo.cw.StorageApplicationBO;
import com.dt.domain.bill.dto.cw.*;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.param.cw.*;
import com.dt.domain.bill.param.message.MessageMqParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.IStorageApplicationBiz;
import com.dt.platform.wms.biz.config.WmsTenantWarehouseHelper;
import com.dt.platform.wms.biz.cw.IRegAnalyzeBiz;
import com.dt.platform.wms.biz.param.SkuLotCheckAndFormatCWParam;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.cw.*;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.message.IRemoteMessageMqClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StorageApplicationBizImpl implements IStorageApplicationBiz {
    private static final List<Integer> ongoingCabinetStatusList = new ArrayList<>();

    static {
        ongoingCabinetStatusList.add(StorageApplicationCabinetStatusEnum.CREATE.getCode());
        ongoingCabinetStatusList.add(StorageApplicationCabinetStatusEnum.UNLOAD_WAIT.getCode());
        ongoingCabinetStatusList.add(StorageApplicationCabinetStatusEnum.UNLOADED.getCode());
        ongoingCabinetStatusList.add(StorageApplicationCabinetStatusEnum.COLLECTING.getCode());
    }

    private static final String CODE_FORMAT = "^[A-Z0-9-]{0,50}$";
    private static final Pattern CODE_PATTERN = Pattern.compile(CODE_FORMAT);

    @Resource
    private IRemoteStorageApplicationClient remoteStorageApplicationClient;
    @Resource
    private IRemoteStorageApplicationCabinetClient remoteStorageApplicationCabinetClient;

    @Resource
    private IRemoteStorageApplicationSkuClient remoteStorageApplicationSkuClient;

    @Resource
    private IRemoteLocationClient remoteLocationClient;

    @Resource
    private IRemoteZoneClient remoteZoneClient;

    @Resource
    private IRemoteBillLogClient remoteBillLogClient;

    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteStorageApplicationCollectClient remoteStorageApplicationCollectClient;

    @Resource
    private IRemoteMessageMqClient remoteMessageMqClient;

    @Resource
    private WmsTenantWarehouseHelper wmsTenantWarehouseHelper;

    @Resource
    private IRemoteRegRecordClient remoteRegRecordClient;

    @DubboReference
    private InventoryOrderRpc inventoryOrderRpc;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IRemoteMessageClient remoteMessageClient;

    @Resource
    private ISkuLotBiz skuLotBiz;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRegAnalyzeBiz regAnalyzeBiz;

    public void sync(MessageMqDTO messageMqDTO) {
        try {
            if (null == messageMqDTO.getId()) {
                MessageMqParam messageMqParam = new MessageMqParam();
                messageMqParam.setOperationType(messageMqDTO.getOperationType());
                messageMqParam.setBillNo(messageMqDTO.getBillNo());
                messageMqParam.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
                List<MessageMqDTO> messageMqDTOList = remoteMessageMqClient.getList(messageMqParam);
                messageMqDTO = messageMqDTOList.get(0);
            }

            if (MessageTypeEnum.CW_IN_CONFIRM.getType().equalsIgnoreCase(messageMqDTO.getOperationType())) {

                StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
                storageApplicationParam.setCargoCode(messageMqDTO.getCargoCode());
                storageApplicationParam.setStorageApplicationCode(messageMqDTO.getBillNo());
                StorageApplicationDTO storageApplicationDTO = remoteStorageApplicationClient.get(storageApplicationParam);
                if (null == storageApplicationDTO) {
                    messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                    remoteMessageMqClient.modify(messageMqDTO);
                    return;
                }

                InventoryOrderInfoCwSubmitReqVO inventoryOrderInfoCwSubmitReqVO = new InventoryOrderInfoCwSubmitReqVO();
                inventoryOrderInfoCwSubmitReqVO.setInOutFlag("in");
                inventoryOrderInfoCwSubmitReqVO.setInOutOrderNo(storageApplicationDTO.getReferenceNumber());
                inventoryOrderInfoCwSubmitReqVO.setWmsWarehouseCode(storageApplicationDTO.getWarehouseCode());
                inventoryOrderInfoCwSubmitReqVO.setOwnerCode(storageApplicationDTO.getCargoCode());
                CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(storageApplicationDTO.getCargoCode());
                if (null == cargoOwnerDTO) throw ExceptionUtil.DATA_ERROR;
                inventoryOrderInfoCwSubmitReqVO.setOwnerName(cargoOwnerDTO.getName());
                inventoryOrderInfoCwSubmitReqVO.setPickUpNo(storageApplicationDTO.getLadingNumber());

                StorageApplicationSkuParam storageApplicationSkuParam = new StorageApplicationSkuParam();
                storageApplicationSkuParam.setCargoCode(storageApplicationDTO.getCargoCode());
                storageApplicationSkuParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
                List<StorageApplicationSkuDTO> storageApplicationSkuDTOList = remoteStorageApplicationSkuClient.getList(storageApplicationSkuParam);
                if (CollectionUtil.isEmpty(storageApplicationSkuDTOList)) {
                    messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                    remoteMessageMqClient.modify(messageMqDTO);
                    return;
                }

                SkuParam skuParam = new SkuParam();
                skuParam.setCargoCode(storageApplicationDTO.getCargoCode());
                skuParam.setCodeList(storageApplicationSkuDTOList.stream().map(StorageApplicationSkuDTO::getSkuCode).distinct().collect(Collectors.toList()));
                List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);

                List<InventoryOrderItemCwSubmitReqVO> itemCwSubmitReqVOList = new ArrayList<>();
                int idx = 1;
                for (StorageApplicationSkuDTO storageApplicationSkuDTO : storageApplicationSkuDTOList) {
                    InventoryOrderItemCwSubmitReqVO itemCwSubmitReqVO = new InventoryOrderItemCwSubmitReqVO();
                    itemCwSubmitReqVO.setIdx(idx);
                    idx = idx + 1;
                    SkuDTO skuDTO = skuDTOList.stream()
                            .filter(it -> it.getCode().equalsIgnoreCase(storageApplicationSkuDTO.getSkuCode()))
                            .findFirst()
                            .orElse(null);
                    if (null == skuDTO) {
                        throw ExceptionUtil.DATA_ERROR;
                    }
                    itemCwSubmitReqVO.setProductId(skuDTO.getItemCode());
                    itemCwSubmitReqVO.setPlanDeclareQty(BigDecimal.valueOf(storageApplicationSkuDTO.getQty()));
                    itemCwSubmitReqVOList.add(itemCwSubmitReqVO);
                }
                inventoryOrderInfoCwSubmitReqVO.setItemCwSubmitReqVOList(itemCwSubmitReqVOList);

                wmsTenantWarehouseHelper.setTenantId(storageApplicationDTO.getWarehouseCode());
                RpcResult<String> inventoryByCw = inventoryOrderRpc.createInventoryByCw(inventoryOrderInfoCwSubmitReqVO);
                log.info("in-createInventoryByCw:{}", JSONUtil.toJsonStr(inventoryByCw));
                if (200 == inventoryByCw.getCode()) {
                    messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                    remoteMessageMqClient.modify(messageMqDTO);
                }
            }
            if (MessageTypeEnum.CW_IN_CONFIRM_COMPLETE.getType().equalsIgnoreCase(messageMqDTO.getOperationType())) {
                StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
                storageApplicationParam.setCargoCode(messageMqDTO.getCargoCode());
                storageApplicationParam.setStorageApplicationCode(messageMqDTO.getBillNo());
                StorageApplicationDTO storageApplicationDTO = remoteStorageApplicationClient.get(storageApplicationParam);
                if (null == storageApplicationDTO) {
                    messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                    remoteMessageMqClient.modify(messageMqDTO);
                    return;
                }
                wmsTenantWarehouseHelper.setTenantId(storageApplicationDTO.getWarehouseCode());
                RpcResult<String> inventoryByCw = inventoryOrderRpc.finishTallyByInOutOrderNo(storageApplicationDTO.getReferenceNumber());
                log.info("in-finishTallyByInOutOrderNo:{}", JSONUtil.toJsonStr(inventoryByCw));
                if (200 == inventoryByCw.getCode()) {
                    messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                    remoteMessageMqClient.modify(messageMqDTO);
                }
            }
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
        }
    }

    @Override
    public Result<Boolean> save(StorageApplicationDTO storageApplicationDTO) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;
            }

            String sequence = remoteSeqRuleClient.findSequence(SeqEnum.SW_STORAGE_APPLICATION_000001);
            storageApplicationDTO.setStorageApplicationCode(sequence);

            CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.cwCargo();
            if (null == cargoOwnerDTO) throw ExceptionUtil.exceptionWithMessage("CW货主不存在");
            storageApplicationDTO.setCargoCode(cargoOwnerDTO.getCode());

            commonCheckAndDataFill(storageApplicationDTO);

            saveCheck(storageApplicationDTO);

            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);
            storageApplicationBO.setStorageApplicationCabinetDTOList(storageApplicationDTO.getStorageApplicationCabinetDTOList());
            storageApplicationBO.setStorageApplicationSkuDTOList(storageApplicationDTO.getStorageApplicationSkuDTOList());
            Boolean persist = remoteStorageApplicationClient.persist(storageApplicationBO);
            return Result.success(persist);
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    private void saveCheck(StorageApplicationDTO storageApplicationDTO) {
        // 入库参考号有效状态唯一
        StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
        storageApplicationParam.setReferenceNumber(storageApplicationDTO.getReferenceNumber());
        storageApplicationParam.setNoStatus(StorageApplicationStatusEnum.CANCEL.getCode());
        List<StorageApplicationDTO> storageApplicationDTOList = remoteStorageApplicationClient.getList(storageApplicationParam);
        if (CollectionUtil.isNotEmpty(storageApplicationDTOList)) {
            throw ExceptionUtil.exceptionWithMessage("入库参考号存在有效状态入库申请");
        }

        // 提单号/调整单号有效状态唯一
        storageApplicationParam = new StorageApplicationParam();
        storageApplicationParam.setLadingNumber(storageApplicationDTO.getLadingNumber());
        storageApplicationParam.setNoStatus(StorageApplicationStatusEnum.CANCEL.getCode());
        storageApplicationDTOList = remoteStorageApplicationClient.getList(storageApplicationParam);
        if (CollectionUtil.isNotEmpty(storageApplicationDTOList)) {
            throw ExceptionUtil.exceptionWithMessage("提单号/调整单号存在有效状态入库申请");
        }

        if (CollectionUtil.isNotEmpty(storageApplicationDTO.getStorageApplicationCabinetDTOList())) {
            StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
            storageApplicationCabinetParam.setCabinetCodeList(storageApplicationDTO.getStorageApplicationCabinetDTOList().stream().map(StorageApplicationCabinetDTO::getCabinetCode).collect(Collectors.toList()));
            storageApplicationCabinetParam.setStatusList(ongoingCabinetStatusList);
            List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);
            if (CollectionUtil.isNotEmpty(storageApplicationCabinetDTOList)) {
                throw ExceptionUtil.exceptionWithMessage("不允许同一柜号同时存在两个进行中的入库申请中");
            }
        }

    }

    // todo 联调是测试
    private void modifyCheck(StorageApplicationDTO storageApplicationDTO, StorageApplicationDTO old) {
        if (!StorageApplicationStatusEnum.CREATE.getCode().equals(old.getStatus())) {
            throw ExceptionUtil.exceptionWithMessage("只有创建状态才允许编辑");
        }

        // 入库参考号有效状态唯一
        if (!old.getReferenceNumber().equalsIgnoreCase(storageApplicationDTO.getReferenceNumber())) {
            throw ExceptionUtil.exceptionWithMessage("入库参考号不允许编辑");
        }

        // 提单号/调整单号有效状态唯一
        if (!old.getLadingNumber().equalsIgnoreCase(storageApplicationDTO.getLadingNumber())) {
            StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
            storageApplicationParam.setLadingNumber(storageApplicationDTO.getLadingNumber());
            storageApplicationParam.setNoStatus(StorageApplicationStatusEnum.CANCEL.getCode());
            List<StorageApplicationDTO> storageApplicationDTOList = remoteStorageApplicationClient.getList(storageApplicationParam);
            if (CollectionUtil.isNotEmpty(storageApplicationDTOList)) {
                throw ExceptionUtil.exceptionWithMessage("提单号/调整单号： 有效状态唯一");
            }
        }

        if (CollectionUtil.isNotEmpty(storageApplicationDTO.getStorageApplicationCabinetDTOList())) {
            StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
            storageApplicationCabinetParam.setCabinetCodeList(storageApplicationDTO.getStorageApplicationCabinetDTOList().stream().map(StorageApplicationCabinetDTO::getCabinetCode).collect(Collectors.toList()));
            storageApplicationCabinetParam.setStatusList(ongoingCabinetStatusList);
            List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);
            List<StorageApplicationCabinetDTO> ongoingCabinetInOtherBill = storageApplicationCabinetDTOList.stream()
                    .filter(it -> !it.getStorageApplicationCode().equals(storageApplicationDTO.getStorageApplicationCode()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ongoingCabinetInOtherBill)) {
                throw ExceptionUtil.exceptionWithMessage("不允许同一柜号同时存在两个进行中的入库申请中");
            }
        }
    }

    private void commonCheckAndDataFill(StorageApplicationDTO storageApplicationDTO) {
        shouldNotEmpty("参数", storageApplicationDTO);
        shouldNotBlank("客户", storageApplicationDTO.getCustomName());
        shouldNotBlank("入库参考号", storageApplicationDTO.getReferenceNumber());
        shouldNotBlank("提单号/调整单号", storageApplicationDTO.getLadingNumber());
        shouldNotEmpty("是否一线", storageApplicationDTO);
        if (!FirstLineEnum.contains(storageApplicationDTO.getFirstLine()))
            throw ExceptionUtil.exceptionWithMessage("是否一线传值错误");

        // 转大写
        storageApplicationDTO.setReferenceNumber(storageApplicationDTO.getReferenceNumber().toUpperCase());
        storageApplicationDTO.setLadingNumber(storageApplicationDTO.getLadingNumber().toUpperCase());

        if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
            if (CollectionUtil.isEmpty(storageApplicationDTO.getStorageApplicationCabinetDTOList())) {
                throw ExceptionUtil.exceptionWithMessage("一线柜号信息不能为空");
            }
        }

        if (CollectionUtil.isNotEmpty(storageApplicationDTO.getStorageApplicationCabinetDTOList())) {
            for (StorageApplicationCabinetDTO storageApplicationCabinetDTO : storageApplicationDTO.getStorageApplicationCabinetDTOList()) {
                shouldNotEmpty("参数", storageApplicationCabinetDTO);
                shouldNotBlank("柜号", storageApplicationCabinetDTO.getCabinetCode());
                storageApplicationCabinetDTO.setCabinetCode(storageApplicationCabinetDTO.getCabinetCode().toUpperCase());
                regexCheck("柜号", storageApplicationCabinetDTO.getCabinetCode());
                storageApplicationCabinetDTO.setCargoCode(storageApplicationDTO.getCargoCode());
                storageApplicationCabinetDTO.setReferenceNumber(storageApplicationDTO.getReferenceNumber());
                storageApplicationCabinetDTO.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            }

            long count = storageApplicationDTO.getStorageApplicationCabinetDTOList().stream().map(StorageApplicationCabinetDTO::getCabinetCode)
                    .map(String::toUpperCase)
                    .distinct().count();
            if (storageApplicationDTO.getStorageApplicationCabinetDTOList().size() > count) {
                throw ExceptionUtil.exceptionWithMessage("同一入库参考号中，柜号不允许重复");
            }
        }

        // 商品信息不能为空
        if (CollectionUtil.isEmpty(storageApplicationDTO.getStorageApplicationSkuDTOList())) {
            throw ExceptionUtil.exceptionWithMessage("商品信息不能为空");
        }
        long count = storageApplicationDTO.getStorageApplicationSkuDTOList().stream()
                .map(StorageApplicationSkuDTO::getSkuCode)
                .distinct().count();
        if (storageApplicationDTO.getStorageApplicationSkuDTOList().size() > count) {
            throw ExceptionUtil.exceptionWithMessage("同一sku，不允许建重复明细");
        }

        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(storageApplicationDTO.getCargoCode());
        skuParam.setCodeList(storageApplicationDTO.getStorageApplicationSkuDTOList().stream().map(StorageApplicationSkuDTO::getSkuCode).collect(Collectors.toList()));
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        for (SkuDTO skuDTO : skuDTOList) {
            if (StrUtil.isBlank(skuDTO.getItemCode())) {
                throw ExceptionUtil.exceptionWithMessage("sku对应的统一料号无值");
            }
        }

        for (StorageApplicationSkuDTO storageApplicationSkuDTO : storageApplicationDTO.getStorageApplicationSkuDTOList()) {
            storageApplicationSkuDTO.setCargoCode(storageApplicationDTO.getCargoCode());
            storageApplicationSkuDTO.setReferenceNumber(storageApplicationDTO.getReferenceNumber());
            storageApplicationSkuDTO.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            skuDTOList.stream()
                    .filter(it -> it.getCode().equalsIgnoreCase(storageApplicationSkuDTO.getSkuCode()))
                    .findFirst()
                    .ifPresent(skuDTO -> {
                        storageApplicationSkuDTO.setGoodCode(skuDTO.getGoodCode());
                        storageApplicationSkuDTO.setSkuName(skuDTO.getName());
                    });
        }

        // 正则校验
        regexCheck("提单号/调整单号", storageApplicationDTO.getLadingNumber());
        regexCheck("入库参考号", storageApplicationDTO.getReferenceNumber());

    }

    private void regexCheck(String subject, String content) {
        if (!CODE_PATTERN.matcher(content).matches())
            throw ExceptionUtil.exceptionWithMessage(subject + "需在50个字符以内，支持英文大小写、数字、英文中划线");
    }


    private void shouldNotBlank(String subject, String content) {
        if (StrUtil.isBlank(content)) throw ExceptionUtil.exceptionWithMessage(subject + "不能为空");
    }

    private void shouldNotEmpty(String subject, Object content) {
        if (null == content) throw ExceptionUtil.exceptionWithMessage(subject + "不能为空");
    }

    private void shouldExist(String subject, Object content) {
        if (null == content) throw ExceptionUtil.exceptionWithMessage(subject + "不存在");
    }

    @Override
    public Result<Boolean> modify(StorageApplicationDTO storageApplicationDTO) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;
            }
            shouldNotEmpty("参数", storageApplicationDTO);
            shouldNotEmpty("ID", storageApplicationDTO.getId());

            StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
            storageApplicationParam.setId(storageApplicationDTO.getId());
            StorageApplicationDTO old = remoteStorageApplicationClient.get(storageApplicationParam);
            shouldExist("入库申请", old);

            storageApplicationDTO.setCargoCode(old.getCargoCode());
            storageApplicationDTO.setStorageApplicationCode(old.getStorageApplicationCode());

            commonCheckAndDataFill(storageApplicationDTO);

            StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
            storageApplicationCabinetParam.setStorageApplicationCode(old.getStorageApplicationCode());
            List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);

            StorageApplicationSkuParam storageApplicationSkuParam = new StorageApplicationSkuParam();
            storageApplicationSkuParam.setStorageApplicationCode(old.getStorageApplicationCode());
            List<StorageApplicationSkuDTO> storageApplicationSkuDTOList = remoteStorageApplicationSkuClient.getList(storageApplicationSkuParam);

            storageApplicationDTO.getStorageApplicationSkuDTOList().forEach(it -> {
                it.setId(null);
                it.setReferenceNumber(old.getReferenceNumber());
                it.setCargoCode(old.getCargoCode());
                it.setStorageApplicationCode(old.getStorageApplicationCode());
            });
            if (CollectionUtil.isNotEmpty(storageApplicationDTO.getStorageApplicationCabinetDTOList())) {
                storageApplicationDTO.getStorageApplicationCabinetDTOList().forEach(it -> {
                    it.setId(null);
                    it.setReferenceNumber(old.getReferenceNumber());
                    it.setCargoCode(old.getCargoCode());
                    it.setStorageApplicationCode(old.getStorageApplicationCode());
                });
            }


            modifyCheck(storageApplicationDTO, old);

            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            storageApplicationBO.setRemoveCabinetList(storageApplicationCabinetDTOList);
            storageApplicationBO.setRemoveSkuList(storageApplicationSkuDTOList);
            storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);
            storageApplicationBO.setStorageApplicationCabinetDTOList(storageApplicationDTO.getStorageApplicationCabinetDTOList());
            storageApplicationBO.setStorageApplicationSkuDTOList(storageApplicationDTO.getStorageApplicationSkuDTOList());
            Boolean persist = remoteStorageApplicationClient.persist(storageApplicationBO);

            log(storageApplicationDTO, "编辑入库申请");
            return Result.success(persist);
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    public Result<StorageApplicationDTO> get(StorageApplicationParam param) {
        if (null == param || null == param.getId()) throw ExceptionUtil.ARG_ERROR;
        StorageApplicationDTO storageApplicationDTO = remoteStorageApplicationClient.get(param);
        if (null == storageApplicationDTO) throw ExceptionUtil.DATA_ERROR;

        storageApplicationDTO.setFirstLineDesc(FirstLineEnum.desc(storageApplicationDTO.getFirstLine()));
        storageApplicationDTO.setStatusDesc(StorageApplicationStatusEnum.desc(storageApplicationDTO.getStatus()));

        StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
        storageApplicationCabinetParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
        List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);
        for (StorageApplicationCabinetDTO storageApplicationCabinetDTO : storageApplicationCabinetDTOList) {
            storageApplicationCabinetDTO.setStatusDesc(StorageApplicationCabinetStatusEnum.desc(storageApplicationCabinetDTO.getStatus()));
            storageApplicationCabinetDTO.setUnloadTimeDesc(timeStr(storageApplicationCabinetDTO.getUnloadTime()));
            storageApplicationCabinetDTO.setCollectEndTimeDesc(timeStr(storageApplicationCabinetDTO.getCollectEndTime()));
        }
        storageApplicationDTO.setStorageApplicationCabinetDTOList(storageApplicationCabinetDTOList);

        List<StorageApplicationSkuDTO> storageApplicationSkuDTOList = storageApplicationSkuDTOList(storageApplicationDTO);
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(storageApplicationDTO.getCargoCode());
        skuParam.setCodeList(storageApplicationSkuDTOList.stream().map(StorageApplicationSkuDTO::getSkuCode).collect(Collectors.toList()));
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        for (StorageApplicationSkuDTO storageApplicationSkuDTO : storageApplicationSkuDTOList) {
            skuDTOList.stream()
                    .filter(it -> it.getCode().equalsIgnoreCase(storageApplicationSkuDTO.getSkuCode()))
                    .findFirst()
                    .ifPresent(skuDTO -> {
                        storageApplicationSkuDTO.setSkuName(skuDTO.getName());
                        storageApplicationSkuDTO.setSkuNameDesc(skuDTO.getName());
                        if (SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.CW_API_SKU)) {
                            storageApplicationSkuDTO.setSkuNameDesc(skuDTO.getName() + "【" + SkuTagEnum.CW_API_SKU.getDesc() + "】");
                        }
                    });
        }
        storageApplicationDTO.setStorageApplicationSkuDTOList(storageApplicationSkuDTOList);
        return Result.success(storageApplicationDTO);
    }

    @Override
    public Result<StorageApplicationDTO> changeAbleCabinetList(StorageApplicationDTO param) {
        if (null == param || null == param.getId()) throw ExceptionUtil.ARG_ERROR;
        StorageApplicationParam applicationParam = new StorageApplicationParam();
        applicationParam.setId(param.getId());
        StorageApplicationDTO storageApplicationDTO = get(applicationParam).getData();
        if (null == storageApplicationDTO) throw ExceptionUtil.DATA_ERROR;

        storageApplicationDTO.setFirstLineDesc(FirstLineEnum.desc(storageApplicationDTO.getFirstLine()));

        StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
        storageApplicationCabinetParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
        storageApplicationCabinetParam.setStatus(StorageApplicationCabinetStatusEnum.UNLOADED.getCode());
        List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);
        for (StorageApplicationCabinetDTO storageApplicationCabinetDTO : storageApplicationCabinetDTOList) {
            storageApplicationCabinetDTO.setStatusDesc(StorageApplicationCabinetStatusEnum.desc(storageApplicationCabinetDTO.getStatus()));
            storageApplicationCabinetDTO.setUnloadTimeDesc(dateStr(storageApplicationCabinetDTO.getUnloadTime()));
            storageApplicationCabinetDTO.setCollectEndTimeDesc(dateStr(storageApplicationCabinetDTO.getCollectEndTime()));
        }
        storageApplicationDTO.setStorageApplicationCabinetDTOList(storageApplicationCabinetDTOList);

        return Result.success(storageApplicationDTO);
    }

    @Override
    public Result<Boolean> changeCabinet(StorageApplicationDTO param) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;
            }
            if (null == param || null == param.getId()) throw ExceptionUtil.ARG_ERROR;
            StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
            storageApplicationParam.setId(param.getId());
            StorageApplicationDTO storageApplicationDTO = get(storageApplicationParam).getData();
            if (null == storageApplicationDTO) throw ExceptionUtil.DATA_ERROR;

            StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
            storageApplicationCabinetParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            List<StorageApplicationCabinetDTO> oldList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);

            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            storageApplicationBO.setStorageApplicationCabinetDTOList(new ArrayList<>());
            for (StorageApplicationCabinetDTO storageApplicationCabinetDTO : param.getStorageApplicationCabinetDTOList()) {
                StorageApplicationCabinetDTO toChange = storageApplicationDTO.getStorageApplicationCabinetDTOList().stream()
                        .filter(it -> it.getCabinetCode().equalsIgnoreCase(storageApplicationCabinetDTO.getCabinetCode()))
                        .filter(it -> StorageApplicationCabinetStatusEnum.UNLOADED.getCode().equals(it.getStatus()))
                        .findFirst().orElse(null);
                if (null == toChange) {
                    throw ExceptionUtil.exceptionWithMessage("只有卸货中/卸货完成状态入库申请才允许更改卸货库位，请确认状态");
                }

                locationCheck(storageApplicationCabinetDTO.getLocationCode());

                toChange.setLocationCode(storageApplicationCabinetDTO.getLocationCode());
                storageApplicationBO.getStorageApplicationCabinetDTOList().add(toChange);
            }

            remoteStorageApplicationClient.persist(storageApplicationBO);

            for (StorageApplicationCabinetDTO storageApplicationCabinetDTO : param.getStorageApplicationCabinetDTOList()) {
                StorageApplicationCabinetDTO cabinetDTO = oldList.stream().filter(it -> it.getCabinetCode().equalsIgnoreCase(storageApplicationCabinetDTO.getCabinetCode()))
                        .findFirst().orElse(null);
                // 库位没变不加日志
                if (cabinetDTO != null && cabinetDTO.getLocationCode().equalsIgnoreCase(storageApplicationCabinetDTO.getLocationCode())) {
                    continue;
                }
                log(storageApplicationDTO, String.format("更改%s、%s柜号的卸货库位", storageApplicationDTO.getReferenceNumber(), storageApplicationCabinetDTO.getCabinetCode()));
            }

            return Result.success(true);
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    public Result<Boolean> confirm(StorageApplicationParam param) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;
            }
            if (null == param || null == param.getId()) throw ExceptionUtil.ARG_ERROR;
            StorageApplicationDTO storageApplicationDTO = remoteStorageApplicationClient.get(param);
            if (null == storageApplicationDTO) throw ExceptionUtil.DATA_ERROR;
            if (!StorageApplicationStatusEnum.CREATE.getCode().equals(storageApplicationDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("只有创建状态入库申请才允许确认，请确认状态");
            }

            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);
            storageApplicationDTO.setStatus(StorageApplicationStatusEnum.CONFIRM.getCode());
            if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
                StorageApplicationCabinetParam search = new StorageApplicationCabinetParam();
                search.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
                List<StorageApplicationCabinetDTO> applicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(search);
                if (CollectionUtil.isEmpty(applicationCabinetDTOList)) throw ExceptionUtil.DATA_ERROR;
                for (StorageApplicationCabinetDTO storageApplicationCabinetDTO : applicationCabinetDTOList) {
                    storageApplicationCabinetDTO.setStatus(StorageApplicationCabinetStatusEnum.UNLOAD_WAIT.getCode());
                }
                storageApplicationBO.setStorageApplicationCabinetDTOList(applicationCabinetDTOList);
            }

            MessageMqDTO messageMqDTO = new MessageMqDTO();
            messageMqDTO.setWarehouseCode(storageApplicationDTO.getWarehouseCode());
            messageMqDTO.setCargoCode(storageApplicationDTO.getCargoCode());
            messageMqDTO.setBillNo(storageApplicationDTO.getStorageApplicationCode());
            messageMqDTO.setOperationType(MessageTypeEnum.CW_IN_CONFIRM.getType());
            messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_CW_IN.getType());
            messageMqDTO.setCreatedTime(System.currentTimeMillis());
            messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
            if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
                storageApplicationBO.setMessageMqDTOList(ListUtil.toList(messageMqDTO));
            }
            remoteStorageApplicationClient.persist(storageApplicationBO);

            log(storageApplicationDTO, "确认入库申请");

            if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
                sync(messageMqDTO);
            }
            return Result.success(true);
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    public Result<Boolean> customsClearanceDone(StorageApplicationParam param) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;
            }
            if (null == param) throw ExceptionUtil.ARG_ERROR;
            if (StrUtil.isBlank(param.getReferenceNumber()))
                throw ExceptionUtil.exceptionWithMessage("入库参考号不能为空");
            StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
            storageApplicationParam.setReferenceNumber(param.getReferenceNumber());
            storageApplicationParam.setNoStatus(StorageApplicationStatusEnum.CANCEL.getCode());
            StorageApplicationDTO storageApplicationDTO = remoteStorageApplicationClient.get(storageApplicationParam);
            if (storageApplicationDTO == null) throw ExceptionUtil.DATA_ERROR;
            storageApplicationDTO.setCustomsClearanceStatus(ShipmentCustomsClearanceStatusEnum.CLEARANCE_DONE.getCode());
            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);
            remoteStorageApplicationClient.persist(storageApplicationBO);
            return Result.success();
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    public Result<Boolean> cancel(StorageApplicationParam param) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;
            }
            if (null == param || null == param.getId()) throw ExceptionUtil.ARG_ERROR;
            StorageApplicationDTO storageApplicationDTO = remoteStorageApplicationClient.get(param);
            if (null == storageApplicationDTO) throw ExceptionUtil.DATA_ERROR;
            if (!StorageApplicationStatusEnum.CREATE.getCode().equals(storageApplicationDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("只有创建状态入库申请才允许取消，请确认状态");
            }

            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);
            storageApplicationDTO.setStatus(StorageApplicationStatusEnum.CANCEL.getCode());
            if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
                StorageApplicationCabinetParam search = new StorageApplicationCabinetParam();
                search.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
                List<StorageApplicationCabinetDTO> applicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(search);
                if (CollectionUtil.isEmpty(applicationCabinetDTOList)) throw ExceptionUtil.DATA_ERROR;
                for (StorageApplicationCabinetDTO storageApplicationCabinetDTO : applicationCabinetDTOList) {
                    storageApplicationCabinetDTO.setStatus(StorageApplicationCabinetStatusEnum.CANCEL.getCode());
                }
                storageApplicationBO.setStorageApplicationCabinetDTOList(applicationCabinetDTOList);
            }

            remoteStorageApplicationClient.persist(storageApplicationBO);

            log(storageApplicationDTO, "取消入库申请");

            return Result.success(true);
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    public Result<Page<StorageApplicationDTO>> getPage(StorageApplicationParam param) {
        cabinetCondition(param);
        collectCondition(param);
        Page<StorageApplicationDTO> page = remoteStorageApplicationClient.getPage(param);
        for (StorageApplicationDTO record : page.getRecords()) {
            record.setStatusDesc(StorageApplicationStatusEnum.desc(record.getStatus()));
            record.setFirstLineDesc(FirstLineEnum.desc(record.getFirstLine()));
            ShipmentCustomsClearanceStatusEnum.getByCode(record.getCustomsClearanceStatus()).ifPresent(it -> record.setCustomsClearanceStatusDesc(it.getName()));
            record.setCreatedTimeDesc(timeStr(record.getCreatedTime()));
            record.setCollectEndTimeDesc(timeStr(record.getCollectEndTime()));
        }
        return Result.success(page);
    }

    @Override
    public Result<Page<StorageApplicationCollectDTO>> getCollectPage(StorageApplicationParam param) {
        StorageApplicationDTO storageApplicationDTO = get(param).getData();
        StorageApplicationCollectParam storageApplicationCollectParam = new StorageApplicationCollectParam();
        storageApplicationCollectParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
        Page<StorageApplicationCollectDTO> page = remoteStorageApplicationCollectClient.getPage(storageApplicationCollectParam);
        for (StorageApplicationCollectDTO record : page.getRecords()) {
            CWCollectTypeEnum collectTypeEnum = CWCollectTypeEnum.getEnum(record.getCollectType());
            if (null != collectTypeEnum) {
                record.setCollectTypeDesc(collectTypeEnum.getMessage());
            }
            record.setManufDateDesc(cwDateStr(record.getManufDate()));
            record.setExpireDateDesc(cwDateStr(record.getExpireDate()));
        }
        return Result.success(page);
    }

    @Override
    public Result<Page<RegRecordDTO>> getRegRecordPage(RegRecordParam param) {
        return Result.success(remoteRegRecordClient.getPage(param));
    }

    @ApiOperation("处理柜号信息查询条件")
    private void cabinetCondition(StorageApplicationParam param) {
        if (CollectionUtil.isNotEmpty(param.getCabinetCodeList())) {
            StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
            storageApplicationCabinetParam.setCabinetCodeList(param.getCabinetCodeList());
            List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);
            if (CollectionUtil.isEmpty(storageApplicationCabinetDTOList)) {
                param.setId(-1L);
            }
            List<String> storageApplicationCodeList = storageApplicationCabinetDTOList.stream().map(StorageApplicationCabinetDTO::getStorageApplicationCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(param.getStorageApplicationCodeList())) {
                param.setStorageApplicationCodeList(storageApplicationCodeList);
            } else {
                Collection<String> intersection = CollectionUtil.intersection(param.getStorageApplicationCodeList(), storageApplicationCodeList);
                if (CollectionUtil.isEmpty(intersection)) {
                    param.setId(-1L);
                } else {
                    param.setStorageApplicationCodeList(new ArrayList<>(intersection));
                }
            }
        }
    }

    @ApiOperation("处理采集信息查询条件")
    private void collectCondition(StorageApplicationParam param) {
        if (CollectionUtil.isNotEmpty(param.getGoodCodeList())) {
            StorageApplicationCollectParam storageApplicationCollectParam = new StorageApplicationCollectParam();
            storageApplicationCollectParam.setGoodCodeList(param.getGoodCodeList());
            List<StorageApplicationCollectDTO> storageApplicationCollectDTOList = remoteStorageApplicationCollectClient.getList(storageApplicationCollectParam);
            if (CollectionUtil.isEmpty(storageApplicationCollectDTOList)) {
                param.setId(-1L);
            }
            List<String> storageApplicationCodeList = storageApplicationCollectDTOList.stream().map(StorageApplicationCollectDTO::getStorageApplicationCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(param.getStorageApplicationCodeList())) {
                param.setStorageApplicationCodeList(storageApplicationCodeList);
            } else {
                Collection<String> intersection = CollectionUtil.intersection(param.getStorageApplicationCodeList(), storageApplicationCodeList);
                if (CollectionUtil.isEmpty(intersection)) {
                    param.setId(-1L);
                } else {
                    param.setStorageApplicationCodeList(new ArrayList<>(intersection));
                }
            }
        }
    }

    @Override
    @ApiOperation("待卸货的柜号")
    public Result<List<String>> unloadCabinetCodeList() {
        StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
        storageApplicationParam.setStatusList(ListUtil.toList(StorageApplicationStatusEnum.CONFIRM.getCode(), StorageApplicationStatusEnum.UNLOADING.getCode()));
        storageApplicationParam.setFirstLine(FirstLineEnum.YES.getCode());
        List<StorageApplicationDTO> storageApplicationDTOList = remoteStorageApplicationClient.getList(storageApplicationParam);
        if (CollectionUtil.isEmpty(storageApplicationDTOList)) return Result.success(ListUtil.empty());

        List<String> storageApplicationCodeList = storageApplicationDTOList.stream().map(StorageApplicationDTO::getStorageApplicationCode).collect(Collectors.toList());
        StorageApplicationCabinetParam storageApplicationDetailParam = new StorageApplicationCabinetParam();
        storageApplicationDetailParam.setStorageApplicationCodeList(storageApplicationCodeList);
        storageApplicationDetailParam.setStatus(StorageApplicationCabinetStatusEnum.UNLOAD_WAIT.getCode());
        List<StorageApplicationCabinetDTO> storageApplicationDetailDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationDetailParam);
        if (CollectionUtil.isEmpty(storageApplicationDetailDTOList)) return Result.success(ListUtil.empty());

        return Result.success(storageApplicationDetailDTOList.stream().map(StorageApplicationCabinetDTO::getCabinetCode).collect(Collectors.toList()));
    }

    @Override
    @ApiOperation("确认卸货")
    public Result<StorageApplicationParam> unloadConfirm(StorageApplicationParam storageApplicationParam) {
        shouldNotEmpty("参数", storageApplicationParam);
        shouldNotBlank("柜号", storageApplicationParam.getCabinetCode());
        StorageApplicationCabinetParam storageApplicationDetailParam = new StorageApplicationCabinetParam();
        storageApplicationDetailParam.setCabinetCode(storageApplicationParam.getCabinetCode());
        storageApplicationDetailParam.setStatus(StorageApplicationCabinetStatusEnum.UNLOAD_WAIT.getCode());
        List<StorageApplicationCabinetDTO> storageApplicationDetailDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationDetailParam);
        if (CollectionUtil.isEmpty(storageApplicationDetailDTOList))
            throw ExceptionUtil.exceptionWithMessage("当前柜号已完成卸货，无法二次卸货");
        if (storageApplicationDetailDTOList.size() > 1) throw ExceptionUtil.DATA_ERROR;

        StorageApplicationCabinetDTO storageApplicationDetailDTO = storageApplicationDetailDTOList.get(0);
        StorageApplicationParam search = new StorageApplicationParam();
        search.setStorageApplicationCode(storageApplicationDetailDTO.getStorageApplicationCode());
        search.setFirstLine(FirstLineEnum.YES.getCode());
        StorageApplicationDTO storageApplicationDTO = remoteStorageApplicationClient.get(search);
        if (null == storageApplicationDTO) throw ExceptionUtil.DATA_ERROR;
        if (!StorageApplicationStatusEnum.CONFIRM.getCode().equals(storageApplicationDTO.getStatus()) && !StorageApplicationStatusEnum.UNLOADING.getCode().equals(storageApplicationDTO.getStatus())) {
            throw ExceptionUtil.exceptionWithMessage("入库申请状态非已确认/卸货中状态，无法执行卸货操作");
        }

        return Result.success(storageApplicationParam);
    }

    @Override
    @ApiOperation("卸货")
    public Result<Boolean> unload(StorageApplicationParam storageApplicationParam) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;

            }
            shouldNotEmpty("参数", storageApplicationParam);
            shouldNotBlank("库位编码", storageApplicationParam.getLocationCode());
            shouldNotBlank("柜号", storageApplicationParam.getCabinetCode());
            // 库位编码在系统内存在，且类型为【存储位】
            LocationDTO locationDTO = locationCheck(storageApplicationParam.getLocationCode());

            StorageApplicationCabinetParam storageApplicationDetailParam = new StorageApplicationCabinetParam();
            storageApplicationDetailParam.setCabinetCode(storageApplicationParam.getCabinetCode());
            storageApplicationDetailParam.setStatus(StorageApplicationCabinetStatusEnum.UNLOAD_WAIT.getCode());
            List<StorageApplicationCabinetDTO> storageApplicationDetailDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationDetailParam);
            if (CollectionUtil.isEmpty(storageApplicationDetailDTOList))
                throw ExceptionUtil.exceptionWithMessage("当前柜号已完成卸货，无法二次卸货");
            if (storageApplicationDetailDTOList.size() > 1) throw ExceptionUtil.DATA_ERROR;

            StorageApplicationCabinetDTO storageApplicationDetailDTO = storageApplicationDetailDTOList.get(0);
            StorageApplicationDTO storageApplicationDTO = findByReferenceNumber(storageApplicationDetailDTO.getReferenceNumber());
            if (!FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
                throw ExceptionUtil.DATA_ERROR;
            }

            List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = storageApplicationCabinetDTOList(storageApplicationDTO);
            StorageApplicationCabinetDTO storageApplicationCabinetDTO = storageApplicationCabinetDTOList.stream()
                    .filter(it -> it.getCabinetCode().equalsIgnoreCase(storageApplicationParam.getCabinetCode()))
                    .filter(it -> it.getStatus().equals(StorageApplicationCabinetStatusEnum.UNLOAD_WAIT.getCode()))
                    .findFirst().orElse(null);
            if (null == storageApplicationCabinetDTO) {
                throw ExceptionUtil.exceptionWithMessage("当前柜号已完成卸货，无法二次卸货");
            }

            if (!StorageApplicationStatusEnum.CONFIRM.getCode().equals(storageApplicationDTO.getStatus())
                    && !StorageApplicationStatusEnum.UNLOADING.getCode().equals(storageApplicationDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("入库申请状态非已确认/卸货中状态，无法执行卸货操作");
            }

            // 修改明细
            storageApplicationCabinetDTO.setStatus(StorageApplicationCabinetStatusEnum.UNLOADED.getCode());
            storageApplicationCabinetDTO.setUnloadTime(System.currentTimeMillis());
            storageApplicationCabinetDTO.setLocationCode(locationDTO.getCode());

            // 卸货中
            if (storageApplicationCabinetDTOList.stream().anyMatch(it -> it.getStatus().equals(StorageApplicationCabinetStatusEnum.UNLOADED.getCode()))) {
                storageApplicationDTO.setStatus(StorageApplicationStatusEnum.UNLOADING.getCode());
            }
            // 都卸货完成
            if (storageApplicationCabinetDTOList.stream().allMatch(it -> it.getStatus().equals(StorageApplicationCabinetStatusEnum.UNLOADED.getCode()))) {
                storageApplicationDTO.setStatus(StorageApplicationStatusEnum.UNLOADED.getCode());
            }

            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);
            storageApplicationBO.setStorageApplicationCabinetDTOList(ListUtil.toList(storageApplicationCabinetDTO));
            remoteStorageApplicationClient.persist(storageApplicationBO);

            log(storageApplicationDTO, String.format("%s柜号卸货至%s库位", storageApplicationCabinetDTO.getCabinetCode(), storageApplicationCabinetDTO.getLocationCode()));

            return Result.success(true);
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    @ApiOperation("待采集的柜号")
    public Result<List<StorageApplicationParam>> collectCabinetCodeList() {
        StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
        storageApplicationParam.setStatusList(ListUtil.toList(StorageApplicationStatusEnum.UNLOADED.getCode(), StorageApplicationStatusEnum.COLLECTING.getCode()));
        storageApplicationParam.setFirstLine(FirstLineEnum.YES.getCode());
        List<StorageApplicationDTO> storageApplicationDTOList = remoteStorageApplicationClient.getList(storageApplicationParam);
        if (CollectionUtil.isEmpty(storageApplicationDTOList)) return Result.success(ListUtil.empty());

        List<String> storageApplicationCodeList = storageApplicationDTOList.stream().map(StorageApplicationDTO::getReferenceNumber).collect(Collectors.toList());
        StorageApplicationCabinetParam storageApplicationDetailParam = new StorageApplicationCabinetParam();
        storageApplicationDetailParam.setReferenceNumberList(storageApplicationCodeList);
        storageApplicationDetailParam.setStatusList(ListUtil.toList(StorageApplicationCabinetStatusEnum.UNLOADED.getCode(), StorageApplicationCabinetStatusEnum.COLLECTING.getCode()));
        List<StorageApplicationCabinetDTO> storageApplicationDetailDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationDetailParam);
        if (CollectionUtil.isEmpty(storageApplicationDetailDTOList)) return Result.success(ListUtil.empty());


        return Result.success(storageApplicationDetailDTOList.stream()
                .map(it -> {
                    StorageApplicationParam param = new StorageApplicationParam();
                    param.setCabinetCode(it.getCabinetCode());
                    param.setReferenceNumber(it.getReferenceNumber());
                    return param;
                }).collect(Collectors.toList()));
    }

    @Override
    @ApiOperation("待采集的柜号")
    public Result<Boolean> collectCabinetCodeCheck(StorageApplicationParam param) {
        StorageApplicationParam storageApplicationParam = new StorageApplicationParam();
        shouldNotEmpty("参数", param);
        shouldNotBlank("入库参考号", param.getReferenceNumber());
        shouldNotBlank("柜号", param.getCabinetCode());
        storageApplicationParam.setReferenceNumber(param.getReferenceNumber());
        StorageApplicationDTO storageApplicationDTO = findByReferenceNumber(param.getReferenceNumber());

        if (!StorageApplicationStatusEnum.UNLOADED.getCode().equals(storageApplicationDTO.getStatus())
                && !StorageApplicationStatusEnum.COLLECTING.getCode().equals(storageApplicationDTO.getStatus())) {
            throw ExceptionUtil.exceptionWithMessage("入库申请状态非卸货完成状态，无法执行采集操作");
        }

        StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
        storageApplicationCabinetParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
        storageApplicationCabinetParam.setCabinetCode(param.getCabinetCode());
        StorageApplicationCabinetDTO storageApplicationCabinetDTO = remoteStorageApplicationCabinetClient.get(storageApplicationCabinetParam);
        shouldExist("柜号信息", storageApplicationCabinetDTO);
        if (StorageApplicationCabinetStatusEnum.COLLECTED.getCode().equals(storageApplicationCabinetDTO.getStatus())) {
            throw ExceptionUtil.exceptionWithMessage("此柜号已采集完成，请选择其他柜号进行采集");
        }
        return Result.success(true);
    }

    @Override
    @ApiOperation("采集输入入库参考号")
    public Result<StorageApplicationParam> collectScanReferenceNumber(StorageApplicationParam storageApplicationParam) {
        shouldNotEmpty("参数", storageApplicationParam);
        shouldNotBlank("入库参考号", storageApplicationParam.getReferenceNumber());

        StorageApplicationDTO storageApplicationDTO = findByReferenceNumber(storageApplicationParam.getReferenceNumber());
        if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine()))
            throw ExceptionUtil.exceptionWithMessage("当前入库参考号为一线入库，需要按柜采集，请退回选择【一线入境】后操作");

        if (!StorageApplicationStatusEnum.CONFIRM.getCode().equals(storageApplicationDTO.getStatus()) && !StorageApplicationStatusEnum.COLLECTING.getCode().equals(storageApplicationDTO.getStatus())) {
            throw ExceptionUtil.exceptionWithMessage("当前入库参考号的状态非【已确认、采集中】状态，不允许进行采集操作");
        }
        return Result.success(storageApplicationParam);
    }

    @Override
    @ApiOperation("采集扫描上架库位")
    public Result<StorageApplicationParam> collectScanLocation(StorageApplicationParam storageApplicationParam) {
        shouldNotEmpty("参数", storageApplicationParam);
        shouldNotBlank("入库参考号", storageApplicationParam.getReferenceNumber());
        shouldNotBlank("库位", storageApplicationParam.getLocationCode());

        locationCheck(storageApplicationParam.getLocationCode());

        StorageApplicationDTO storageApplicationDTO = findByReferenceNumber(storageApplicationParam.getReferenceNumber());
        if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
            shouldNotBlank("柜号", storageApplicationParam.getCabinetCode());
            if (!ListUtil.toList(StorageApplicationStatusEnum.UNLOADED.getCode(), StorageApplicationStatusEnum.COLLECTING.getCode()).contains(storageApplicationDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("入库申请状态非【卸货完成、采集中】状态，无法执行采集操作");
            }
        } else {
            if (!ListUtil.toList(StorageApplicationStatusEnum.CONFIRM.getCode(), StorageApplicationStatusEnum.COLLECTING.getCode()).contains(storageApplicationDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("入库申请状态非【已确认、采集中】状态，无法执行采集操作");
            }
        }
        return Result.success(storageApplicationParam);
    }

    @Override
    @ApiOperation("扫描托盘号/箱码")
    public Result<StorageApplicationParam> collectScanRegAndSerial(StorageApplicationParam storageApplicationParam) {
        shouldNotEmpty("参数", storageApplicationParam);
        shouldNotBlank("入库参考号", storageApplicationParam.getReferenceNumber());
        shouldNotBlank("库位", storageApplicationParam.getLocationCode());
        shouldNotBlank("托盘号/箱码", storageApplicationParam.getRegOrSerialOrUpc());

        // 解析托盘、箱码
        analyze(storageApplicationParam);

        StorageApplicationDTO storageApplicationDTO = findByReferenceNumber(storageApplicationParam.getReferenceNumber());
        if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
            shouldNotBlank("柜号", storageApplicationParam.getCabinetCode());
        }

        List<StorageApplicationSkuDTO> storageApplicationSkuDTOList = storageApplicationSkuDTOList(storageApplicationDTO);
        if (CollectionUtil.isEmpty(storageApplicationSkuDTOList)) {
            throw ExceptionUtil.exceptionWithMessage("当前采集商品再入库单中不存在，请确认");
        }

        String upc = storageApplicationParam.getUpc();
        if (StrUtil.isNotBlank(upc)) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCode(upc);
            skuUpcParam.setCargoCode(storageApplicationDTO.getCargoCode());
            SkuUpcDTO skuUpc = remoteSkuClient.getSkuUpc(skuUpcParam);
            shouldExist("商品条码", skuUpc);
            if (storageApplicationSkuDTOList.stream().noneMatch(storageApplicationSkuDTO -> storageApplicationSkuDTO.getSkuCode().equalsIgnoreCase(skuUpc.getSkuCode()))) {
                throw ExceptionUtil.exceptionWithMessage("当前采集商品再入库单中不存在，请确认");
            }

            SkuParam skuParam = new SkuParam();
            skuParam.setCargoCode(storageApplicationDTO.getCargoCode());
            skuParam.setCode(skuUpc.getSkuCode());
            SkuDTO skuDTO = remoteSkuClient.get(skuParam);
            shouldExist("商品", skuDTO);

            if (!SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.CW_API_SKU)) {
                throw ExceptionUtil.exceptionWithMessage("非API商品选择产品类型时请选择普通商品");
            }
        }

        return Result.success(storageApplicationParam);
    }

    @Override
    @ApiOperation("扫描条码")
    public Result<StorageApplicationParam> collectScanUpc(StorageApplicationParam storageApplicationParam) {
        shouldNotEmpty("参数", storageApplicationParam);
        shouldNotBlank("入库参考号", storageApplicationParam.getReferenceNumber());
        shouldNotBlank("库位", storageApplicationParam.getLocationCode());
        shouldNotBlank("条码", storageApplicationParam.getUpc());
        storageApplicationParam.setCollectType(CWCollectTypeEnum.SCAN_UPC.getCode());
        storageApplicationParam.setCollectTypeDesc(CWCollectTypeEnum.SCAN_UPC.getMessage());

        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.cwCargo();
        if (null == cargoOwnerDTO) throw ExceptionUtil.exceptionWithMessage("无法找到cw的商品，请确认后维护商品");

        StorageApplicationDTO storageApplicationDTO = findByReferenceNumber(storageApplicationParam.getReferenceNumber());
        if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
            shouldNotBlank("柜号", storageApplicationParam.getCabinetCode());
        }

        SkuUpcDTO skuUpcDTO = skuUpcDTO(storageApplicationDTO.getCargoCode(), storageApplicationParam.getUpc());
        shouldExist("条码", skuUpcDTO);

        SkuDTO skuDTO = skuDTO(storageApplicationDTO.getCargoCode(), skuUpcDTO.getSkuCode());
        if (null == skuDTO) throw ExceptionUtil.exceptionWithMessage("无法找到cw的商品，请确认后维护商品");
        if (SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.CW_API_SKU)) {
            throw ExceptionUtil.exceptionWithMessage("API商品需要采集托盘和箱码");
        }

        if (SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.CW_API_SKU)) {
            throw ExceptionUtil.exceptionWithMessage("API商品选择产品类型时请选择API商品");
        }

        List<StorageApplicationSkuDTO> storageApplicationSkuDTOList = storageApplicationSkuDTOList(storageApplicationDTO);
        if (CollectionUtil.isEmpty(storageApplicationSkuDTOList)) {
            throw ExceptionUtil.exceptionWithMessage("当前采集商品再入库单中不存在，请确认");
        }

        upcShouldInSkuInfo(skuUpcDTO.getSkuCode(), storageApplicationSkuDTOList);

        // 填充需要展示的数据
        storageApplicationParam.setGoodCode(skuDTO.getGoodCode());
        storageApplicationParam.setUpc(skuUpcDTO.getUpcCode());
        storageApplicationParam.setSkuCode(skuDTO.getCode());
        storageApplicationParam.setSkuName(skuDTO.getName());
        storageApplicationParam.setLifeCycle(skuDTO.getLifeCycle());


        return Result.success(storageApplicationParam);
    }

    @Override
    @ApiOperation("确认箱数")
    public Result<StorageApplicationParam> collectConfirmBoxCount(StorageApplicationParam storageApplicationParam) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;
            }
            shouldNotEmpty("参数", storageApplicationParam);
            shouldNotBlank("库位", storageApplicationParam.getLocationCode());
            shouldNotEmpty("扫描类型", storageApplicationParam.getCollectType());
            if (CWCollectTypeEnum.SCAN_PALLET.getCode().equals(storageApplicationParam.getCollectType())) {
                shouldNotBlank("托盘号", storageApplicationParam.getReg());
            }
            if (CWCollectTypeEnum.SCAN_CARTON.getCode().equals(storageApplicationParam.getCollectType())) {
                shouldNotBlank("箱码", storageApplicationParam.getSerial());
            }
            shouldNotBlank("条码", storageApplicationParam.getUpc());
            shouldNotBlank("入库参考号", storageApplicationParam.getReferenceNumber());
            StorageApplicationDTO storageApplicationDTO = findByReferenceNumber(storageApplicationParam.getReferenceNumber());
            if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
                shouldNotBlank("柜号", storageApplicationParam.getCabinetCode());
            }

            CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.cwCargo();
            if (null == cargoOwnerDTO) {
                throw ExceptionUtil.exceptionWithMessage("CW货主没配置");
            }
            if (!cargoOwnerDTO.getCode().equalsIgnoreCase(storageApplicationDTO.getCargoCode())) {
                throw ExceptionUtil.exceptionWithMessage("CW货主变更了");
            }

            // 当前托盘/箱码已有扫描记录，不允许再次扫描
            StorageApplicationCollectParam storageApplicationCollectParam = new StorageApplicationCollectParam();
            storageApplicationCollectParam.setReg(storageApplicationParam.getReg());
            storageApplicationCollectParam.setSerial(storageApplicationParam.getSerial());
            List<StorageApplicationCollectDTO> storageApplicationCollectDTOList = remoteStorageApplicationCollectClient.getList(storageApplicationCollectParam);
            if (CollectionUtil.isNotEmpty(storageApplicationCollectDTOList)) {
                throw ExceptionUtil.exceptionWithMessage("当前托盘/箱码已有扫描记录，不允许再次扫描");
            }

            List<StorageApplicationSkuDTO> storageApplicationSkuDTOList = storageApplicationSkuDTOList(storageApplicationDTO);

            SkuUpcDTO skuUpcDTO = skuUpcDTO(storageApplicationDTO.getCargoCode(), storageApplicationParam.getUpc());
            shouldExist("条码", skuUpcDTO);

            SkuUomDTO skuUomDTO = remoteSkuClient.querySkuUomBySkuCode(storageApplicationDTO.getCargoCode(), skuUpcDTO.getSkuCode(), skuUpcDTO.getPackageUnitCode());
            shouldExist("包装单位", skuUpcDTO);

            StorageApplicationSkuDTO storageApplicationSkuDTO = upcShouldInSkuInfo(skuUpcDTO.getSkuCode(), storageApplicationSkuDTOList);

            SkuDTO skuDTO = skuDTO(storageApplicationDTO.getCargoCode(), skuUpcDTO.getSkuCode());
            shouldExist("商品", skuUpcDTO);
            if (StrUtil.isBlank(skuDTO.getItemCode()))
                throw ExceptionUtil.exceptionWithMessage("当前商品的档案上统一料号缺失，请处理后再采集商品");

            storageApplicationCollectParam = new StorageApplicationCollectParam();
            storageApplicationCollectParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            storageApplicationCollectParam.setSkuCode(skuDTO.getCode());
            storageApplicationCollectDTOList = remoteStorageApplicationCollectClient.getList(storageApplicationCollectParam);
            Integer reduce = storageApplicationCollectDTOList.stream().map(StorageApplicationCollectDTO::getTotalCount).reduce(0, Integer::sum);
            Integer currentCount = skuUomDTO.getPackageQty().multiply(new BigDecimal(storageApplicationParam.getBoxCount())).intValue();
            if (storageApplicationSkuDTO.getQty() < reduce + currentCount) {
                throw ExceptionUtil.exceptionWithMessage(" 当前商品已采集总数+ 当前采集数已经超过计划数量，请确认数量是否正确");
            }
            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            storageApplicationSkuDTO.setCollectCount(reduce + currentCount);
            storageApplicationBO.setStorageApplicationSkuDTOList(ListUtil.toList(storageApplicationSkuDTO));

            if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
                StorageApplicationCabinetDTO storageApplicationCabinetDTO = storageApplicationCabinetDTO(storageApplicationDTO, storageApplicationParam.getCabinetCode());
                storageApplicationCabinetDTO.setStatus(StorageApplicationCabinetStatusEnum.COLLECTING.getCode());
                storageApplicationBO.setStorageApplicationCabinetDTOList(ListUtil.toList(storageApplicationCabinetDTO));
            }

            // 采集信息
            StorageApplicationCollectDTO collectDTO = new StorageApplicationCollectDTO();
            collectDTO.setWarehouseCode(storageApplicationDTO.getWarehouseCode());
            collectDTO.setCargoCode(storageApplicationDTO.getCargoCode());
            collectDTO.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            collectDTO.setReferenceNumber(storageApplicationDTO.getReferenceNumber());
            collectDTO.setCabinetCode(storageApplicationParam.getCabinetCode());
            collectDTO.setCollectType(storageApplicationParam.getCollectType());
            collectDTO.setLocationCode(storageApplicationParam.getLocationCode());
            collectDTO.setSkuCode(skuDTO.getCode());
            collectDTO.setSkuName(skuDTO.getName());
            collectDTO.setGoodCode(skuDTO.getGoodCode());
            collectDTO.setUpcCode(skuUpcDTO.getUpcCode());
            collectDTO.setItemCode(skuDTO.getItemCode());
            collectDTO.setReg(storageApplicationParam.getReg());
            collectDTO.setSerial(storageApplicationParam.getSerial());
            collectDTO.setLotNumber(storageApplicationParam.getLotNumber());
            collectDTO.setManufDate(storageApplicationParam.getManufDate());
            collectDTO.setExpireDate(storageApplicationParam.getExpireDate());
            collectDTO.setUnit("UTN");
            collectDTO.setUnitCount(1);
            collectDTO.setBoxCount(storageApplicationParam.getBoxCount());
            collectDTO.setTotalCount(currentCount);
            String batchSerialNo = IdUtil.simpleUUID();
            collectDTO.setBatchSerialNo(batchSerialNo);
            storageApplicationBO.setStorageApplicationCollectDTOList(ListUtil.toList(collectDTO));

            SkuLotCheckAndFormatCWParam skuLotCheckAndFormatCWParam = new SkuLotCheckAndFormatCWParam();
            skuLotCheckAndFormatCWParam.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
            skuLotCheckAndFormatCWParam.setBoxCode(collectDTO.getSerial());
            skuLotCheckAndFormatCWParam.setPalletCode(collectDTO.getReg());
            skuLotCheckAndFormatCWParam.setManufDate(collectDTO.getManufDate());
            skuLotCheckAndFormatCWParam.setExpireDate(collectDTO.getExpireDate());
            skuLotCheckAndFormatCWParam.setProductionNo(collectDTO.getLotNumber());
            skuLotCheckAndFormatCWParam.setExternalSkuLotNo(storageApplicationDTO.getCustomName());
            SkuLotDTO andFormatCWSkuLot = skuLotBiz.findAndFormatCWSkuLot(skuLotCheckAndFormatCWParam, skuDTO);
            if (null == andFormatCWSkuLot.getId()) {
                remoteSkuLotClient.saveBatch(ListUtil.toList(andFormatCWSkuLot));
            }
            collectDTO.setSkuLotNo(andFormatCWSkuLot.getCode());

            MessageMqDTO messageMqDTO = new MessageMqDTO();
            messageMqDTO.setWarehouseCode(storageApplicationDTO.getWarehouseCode());
            messageMqDTO.setCargoCode(storageApplicationDTO.getCargoCode());
            messageMqDTO.setBillNo(storageApplicationDTO.getStorageApplicationCode());
            messageMqDTO.setBatchSerialNo(batchSerialNo);
            messageMqDTO.setOperationType(MessageTypeEnum.OPERATION_CW_STORAGE_APPLICATION.getType());
            messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_CW_IN.getType());
            messageMqDTO.setCreatedTime(System.currentTimeMillis());
            messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
            storageApplicationBO.setMessageMqDTOList(ListUtil.toList(messageMqDTO));

            if (CWCollectTypeEnum.SCAN_PALLET.getCode().equals(storageApplicationParam.getCollectType())) {
                // 记录托盘记录
                RegRecordDTO regRecordDTO = new RegRecordDTO();
                regRecordDTO.setWarehouseCode(storageApplicationDTO.getWarehouseCode());
                regRecordDTO.setReg(storageApplicationParam.getReg());
                regRecordDTO.setBoxCount(storageApplicationParam.getBoxCount());
                regRecordDTO.setTotalCount(currentCount);
                storageApplicationBO.setRegRecordDTOList(ListUtil.toList(regRecordDTO));
            }

            // 统计采集数量
            StorageApplicationCollectParam byStorageApplication = new StorageApplicationCollectParam();
            byStorageApplication.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            List<StorageApplicationCollectDTO> collectByStorageApplication = remoteStorageApplicationCollectClient.getList(byStorageApplication);
            collectByStorageApplication.add(collectDTO);

            long skuCount = collectByStorageApplication.stream().map(StorageApplicationCollectDTO::getSkuCode).distinct().count();
            Integer totalCount = collectByStorageApplication.stream().map(StorageApplicationCollectDTO::getTotalCount).reduce(0, Integer::sum);

            storageApplicationDTO.setStatus(StorageApplicationStatusEnum.COLLECTING.getCode());
            storageApplicationDTO.setCollectSkuCount(((int) skuCount));
            storageApplicationDTO.setCollectCount(totalCount);
            storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);
            remoteStorageApplicationClient.persist(storageApplicationBO);

            try {
                StockOperationMessage stockOperationMessage = new StockOperationMessage();
                stockOperationMessage.setWarehouseCode(messageMqDTO.getWarehouseCode());
                stockOperationMessage.setCargoCode(messageMqDTO.getCargoCode());
                stockOperationMessage.setOperationType(messageMqDTO.getOperationType());
                stockOperationMessage.setBillNo(messageMqDTO.getBillNo());
                stockOperationMessage.setBatchSerialNo(batchSerialNo);
                remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
            } catch (Exception ignored) {
            }

            // 只保留部分字段
            StorageApplicationParam param = new StorageApplicationParam();
            param.setReferenceNumber(storageApplicationParam.getReferenceNumber());
            param.setCabinetCode(storageApplicationParam.getCabinetCode());
            param.setLocationCode(storageApplicationParam.getLocationCode());
            return Result.success(param);
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    @ApiOperation("输入数量")
    public Result<StorageApplicationParam> collectInputCount(StorageApplicationParam storageApplicationParam) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;
            }
            shouldNotEmpty("参数", storageApplicationParam);
            shouldNotBlank("入库参考号", storageApplicationParam.getReferenceNumber());
            shouldNotBlank("库位", storageApplicationParam.getLocationCode());
            shouldNotBlank("条码", storageApplicationParam.getUpc());
            shouldNotBlank("商品编码", storageApplicationParam.getSkuCode());
            shouldNotBlank("批次号", storageApplicationParam.getLotNumber());
            shouldNotEmpty("装箱日期", storageApplicationParam.getManufDate());
            shouldNotEmpty("到期日期", storageApplicationParam.getExpireDate());
            shouldNotEmpty("商品数量", storageApplicationParam.getTotalCount());
            storageApplicationParam.setCollectType(CWCollectTypeEnum.SCAN_UPC.getCode());

            checkLotNumber(storageApplicationParam.getLotNumber());

            if (storageApplicationParam.getManufDate() <= 0) {
                throw ExceptionUtil.exceptionWithMessage("生产日期不能小于19970101");
            }

            if (storageApplicationParam.getTotalCount() < 1 || storageApplicationParam.getTotalCount() > 1000000) {
                throw ExceptionUtil.exceptionWithMessage("请输入1-1000000的正整数");
            }

            StorageApplicationDTO storageApplicationDTO = findByReferenceNumber(storageApplicationParam.getReferenceNumber());
            if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
                shouldNotBlank("柜号", storageApplicationParam.getCabinetCode());
            }

            CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.cwCargo();
            if (null == cargoOwnerDTO) {
                throw ExceptionUtil.exceptionWithMessage("CW货主没配置");
            }
            if (!cargoOwnerDTO.getCode().equalsIgnoreCase(storageApplicationDTO.getCargoCode())) {
                throw ExceptionUtil.exceptionWithMessage("CW货主变更了");
            }

            // 同一柜+商品+批号+库位，只允许有一条记录
            StorageApplicationCollectParam storageApplicationCollectParam = new StorageApplicationCollectParam();
            storageApplicationCollectParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            storageApplicationCollectParam.setCabinetCode(storageApplicationParam.getCabinetCode());
            storageApplicationCollectParam.setSkuCode(storageApplicationParam.getSkuCode());
            storageApplicationCollectParam.setLotNumber(storageApplicationParam.getLotNumber());
            storageApplicationCollectParam.setLocationCode(storageApplicationParam.getLocationCode());
            List<StorageApplicationCollectDTO> storageApplicationCollectDTOList = remoteStorageApplicationCollectClient.getList(storageApplicationCollectParam);
            if (CollectionUtil.isNotEmpty(storageApplicationCollectDTOList)) {
                throw ExceptionUtil.exceptionWithMessage("当前商品当前库位数据已提交，不允许再提交");
            }

            List<StorageApplicationSkuDTO> storageApplicationSkuDTOList = storageApplicationSkuDTOList(storageApplicationDTO);

            SkuUpcDTO skuUpcDTO = skuUpcDTO(storageApplicationDTO.getCargoCode(), storageApplicationParam.getUpc());
            shouldExist("条码", skuUpcDTO);

            StorageApplicationSkuDTO storageApplicationSkuDTO = upcShouldInSkuInfo(skuUpcDTO.getSkuCode(), storageApplicationSkuDTOList);

            SkuDTO skuDTO = skuDTO(storageApplicationDTO.getCargoCode(), skuUpcDTO.getSkuCode());
            shouldExist("商品", skuUpcDTO);
            if (StrUtil.isBlank(skuDTO.getItemCode()))
                throw ExceptionUtil.exceptionWithMessage("当前商品的档案上统一料号缺失，请处理后再采集商品");

            long manufDate = DateUtil.parse(DateUtil.format(new Date(storageApplicationParam.getManufDate()), "yyyy-MM-dd"), "yyyy-MM-dd").getTime();
            storageApplicationParam.setManufDate(manufDate);
            long todayDate = DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd")).getTime();
            if (manufDate > todayDate) {
                throw new BaseException(BaseBizEnum.TIP, "生产日期不能大于当前天");
            }

            long expireDate = DateUtil.parse(DateUtil.format(new Date(storageApplicationParam.getExpireDate()), "yyyy-MM-dd"), "yyyy-MM-dd").getTime();
            storageApplicationParam.setExpireDate(expireDate);
            if (expireDate <= todayDate) {
                throw new BaseException(BaseBizEnum.TIP, "失效日期不能小于等于当前天");
            }
            //获取CW货主 不强校验生产+保质期天数=失效日期
            Boolean cwCargo = false;
            if (cargoOwnerDTO != null && CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.CW_CARGO)) {
                cwCargo = true;
            }

            Long expireDateNew = manufDate + Long.valueOf(skuDTO.getLifeCycle()) * CommonConstantUtil.DAY_MILLISECONDS;
            if (!cwCargo && !expireDateNew.equals(storageApplicationParam.getExpireDate())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品%s生产日期和失效日期计算异常", skuDTO.getCode()));
            }

            storageApplicationCollectParam = new StorageApplicationCollectParam();
            storageApplicationCollectParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            storageApplicationCollectParam.setSkuCode(skuDTO.getCode());
            storageApplicationCollectDTOList = remoteStorageApplicationCollectClient.getList(storageApplicationCollectParam);
            Integer reduce = storageApplicationCollectDTOList.stream().map(StorageApplicationCollectDTO::getTotalCount).reduce(0, Integer::sum);
            if (storageApplicationSkuDTO.getQty() < reduce + storageApplicationParam.getTotalCount()) {
                throw ExceptionUtil.exceptionWithMessage("当前商品已采集总数+ 当前采集数已经超过计划数量，请确认数量是否正确");
            }

            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            storageApplicationSkuDTO.setCollectCount(reduce + storageApplicationParam.getTotalCount());
            storageApplicationBO.setStorageApplicationSkuDTOList(ListUtil.toList(storageApplicationSkuDTO));

            if (FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine())) {
                StorageApplicationCabinetDTO storageApplicationCabinetDTO = storageApplicationCabinetDTO(storageApplicationDTO, storageApplicationParam.getCabinetCode());
                storageApplicationCabinetDTO.setStatus(StorageApplicationCabinetStatusEnum.COLLECTING.getCode());
                storageApplicationBO.setStorageApplicationCabinetDTOList(ListUtil.toList(storageApplicationCabinetDTO));
            }

            // 采集信息
            StorageApplicationCollectDTO collectDTO = new StorageApplicationCollectDTO();
            collectDTO.setWarehouseCode(storageApplicationDTO.getWarehouseCode());
            collectDTO.setCargoCode(storageApplicationDTO.getCargoCode());
            collectDTO.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            collectDTO.setReferenceNumber(storageApplicationDTO.getReferenceNumber());
            collectDTO.setCabinetCode(storageApplicationParam.getCabinetCode());
            collectDTO.setCollectType(storageApplicationParam.getCollectType());
            collectDTO.setLocationCode(storageApplicationParam.getLocationCode());
            collectDTO.setSkuCode(skuDTO.getCode());
            collectDTO.setSkuName(skuDTO.getName());
            collectDTO.setGoodCode(skuDTO.getGoodCode());
            collectDTO.setUpcCode(skuUpcDTO.getUpcCode());
            collectDTO.setItemCode(skuDTO.getItemCode());
            collectDTO.setReg(storageApplicationParam.getReg());
            collectDTO.setSerial(storageApplicationParam.getSerial());
            collectDTO.setLotNumber(storageApplicationParam.getLotNumber());
            collectDTO.setManufDate(storageApplicationParam.getManufDate());
            collectDTO.setExpireDate(storageApplicationParam.getExpireDate());
            collectDTO.setUnit("UTN");
            collectDTO.setUnitCount(1);
            collectDTO.setBoxCount(storageApplicationParam.getBoxCount());
            collectDTO.setTotalCount(storageApplicationParam.getTotalCount());
            String batchSerialNo = IdUtil.simpleUUID();
            collectDTO.setBatchSerialNo(batchSerialNo);
            storageApplicationBO.setStorageApplicationCollectDTOList(ListUtil.toList(collectDTO));

            SkuLotCheckAndFormatCWParam skuLotCheckAndFormatCWParam = new SkuLotCheckAndFormatCWParam();
            skuLotCheckAndFormatCWParam.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
            skuLotCheckAndFormatCWParam.setBoxCode(collectDTO.getSerial());
            skuLotCheckAndFormatCWParam.setPalletCode(collectDTO.getReg());
            skuLotCheckAndFormatCWParam.setManufDate(collectDTO.getManufDate());
            skuLotCheckAndFormatCWParam.setExpireDate(collectDTO.getExpireDate());
            skuLotCheckAndFormatCWParam.setProductionNo(collectDTO.getLotNumber());
            skuLotCheckAndFormatCWParam.setExternalSkuLotNo(storageApplicationDTO.getCustomName());
            SkuLotDTO andFormatCWSkuLot = skuLotBiz.findAndFormatCWSkuLot(skuLotCheckAndFormatCWParam, skuDTO);
            if (null == andFormatCWSkuLot.getId()) {
                remoteSkuLotClient.saveBatch(ListUtil.toList(andFormatCWSkuLot));
            }
            collectDTO.setSkuLotNo(andFormatCWSkuLot.getCode());

            MessageMqDTO messageMqDTO = new MessageMqDTO();
            messageMqDTO.setWarehouseCode(storageApplicationDTO.getWarehouseCode());
            messageMqDTO.setCargoCode(storageApplicationDTO.getCargoCode());
            messageMqDTO.setBillNo(storageApplicationDTO.getStorageApplicationCode());
            messageMqDTO.setBatchSerialNo(batchSerialNo);
            messageMqDTO.setOperationType(MessageTypeEnum.OPERATION_CW_STORAGE_APPLICATION.getType());
            messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_CW_IN.getType());
            messageMqDTO.setCreatedTime(System.currentTimeMillis());
            messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
            storageApplicationBO.setMessageMqDTOList(ListUtil.toList(messageMqDTO));
            // 统计采集数量
            StorageApplicationCollectParam byStorageApplication = new StorageApplicationCollectParam();
            byStorageApplication.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            List<StorageApplicationCollectDTO> collectByStorageApplication = remoteStorageApplicationCollectClient.getList(byStorageApplication);
            collectByStorageApplication.add(collectDTO);

            long skuCount = collectByStorageApplication.stream().map(StorageApplicationCollectDTO::getSkuCode).distinct().count();
            Integer totalCount = collectByStorageApplication.stream().map(StorageApplicationCollectDTO::getTotalCount).reduce(0, Integer::sum);

            storageApplicationDTO.setStatus(StorageApplicationStatusEnum.COLLECTING.getCode());
            storageApplicationDTO.setCollectSkuCount(((int) skuCount));
            storageApplicationDTO.setCollectCount(totalCount);

            storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);
            remoteStorageApplicationClient.persist(storageApplicationBO);

            try {
                StockOperationMessage stockOperationMessage = new StockOperationMessage();
                stockOperationMessage.setWarehouseCode(messageMqDTO.getWarehouseCode());
                stockOperationMessage.setCargoCode(messageMqDTO.getCargoCode());
                stockOperationMessage.setOperationType(messageMqDTO.getOperationType());
                stockOperationMessage.setBillNo(messageMqDTO.getBillNo());
                stockOperationMessage.setBatchSerialNo(batchSerialNo);
                remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
            } catch (Exception ignored) {
            }

            // 只保留部分字段
            StorageApplicationParam param = new StorageApplicationParam();
            param.setReferenceNumber(storageApplicationParam.getReferenceNumber());
            param.setCabinetCode(storageApplicationParam.getCabinetCode());
            param.setLocationCode(storageApplicationParam.getLocationCode());
            return Result.success(param);
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    @ApiOperation("完成采集")
    public Result<StorageApplicationParam> collectComplete(StorageApplicationParam storageApplicationParam) {
        RLock lock = redissonClient.getLock(String.format("CW_IN_%s", CurrentRouteHolder.getWarehouseCode()));
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!locked) {
                throw ExceptionUtil.SYSTEM_BUSY;
            }
            shouldNotEmpty("参数", storageApplicationParam);
            shouldNotBlank("入库参考号", storageApplicationParam.getReferenceNumber());

            StorageApplicationDTO storageApplicationDTO = findByReferenceNumber(storageApplicationParam.getReferenceNumber());
            boolean firstLine = FirstLineEnum.YES.getCode().equals(storageApplicationDTO.getFirstLine());

            if (!StorageApplicationStatusEnum.COLLECTING.getCode().equals(storageApplicationDTO.getStatus())) {
                throw ExceptionUtil.exceptionWithMessage("入库申请状态非采集中状态，无法执行采集操作");
            }

            StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
            storageApplicationCabinetParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);

            StorageApplicationCollectParam storageApplicationCollectParam = new StorageApplicationCollectParam();
            storageApplicationCollectParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
            List<StorageApplicationCollectDTO> storageApplicationCollectDTOList = remoteStorageApplicationCollectClient.getList(storageApplicationCollectParam);

            StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
            if (firstLine) {
                shouldNotBlank("柜号", storageApplicationParam.getCabinetCode());

                if (!ShipmentCustomsClearanceStatusEnum.CLEARANCE_DONE.getCode().equalsIgnoreCase(storageApplicationDTO.getCustomsClearanceStatus())) {
                    throw ExceptionUtil.exceptionWithMessage("当前入库单未完成清关，不允许采集完成，请联系关务");
                }

                if (CollectionUtil.isEmpty(storageApplicationCabinetDTOList)) {
                    throw ExceptionUtil.exceptionWithMessage("数据异常，一线入库申请柜号信息不存在");
                }

                StorageApplicationCabinetDTO storageApplicationCabinetDTO = storageApplicationCabinetDTOList.stream()
                        .filter(it -> it.getCabinetCode().equalsIgnoreCase(storageApplicationParam.getCabinetCode()))
                        .findFirst().orElse(null);
                if (null == storageApplicationCabinetDTO) {
                    throw ExceptionUtil.exceptionWithMessage("柜号信息不存在");
                }

                if (!StorageApplicationCabinetStatusEnum.COLLECTING.getCode().equals(storageApplicationCabinetDTO.getStatus())) {
                    throw ExceptionUtil.exceptionWithMessage("柜号状态非采集中状态，无法执行采集操作");
                }

                List<StorageApplicationCollectDTO> collect = storageApplicationCollectDTOList.stream()
                        .filter(it -> it.getCabinetCode().equalsIgnoreCase(storageApplicationParam.getCabinetCode()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    throw ExceptionUtil.exceptionWithMessage("当前柜号下无提交明细，无法完成");
                }

                storageApplicationCabinetDTO.setStatus(StorageApplicationCabinetStatusEnum.COLLECTED.getCode());
                storageApplicationCabinetDTO.setCollectEndTime(System.currentTimeMillis());
                if (storageApplicationCabinetDTOList.stream()
                        .allMatch(it -> it.getStatus().equals(StorageApplicationCabinetStatusEnum.COLLECTED.getCode()))) {
                    storageApplicationDTO.setStatus(StorageApplicationStatusEnum.COLLECTED.getCode());
                    storageApplicationDTO.setCollectEndTime(System.currentTimeMillis());
                }
                storageApplicationBO.setStorageApplicationCabinetDTOList(ListUtil.toList(storageApplicationCabinetDTO));
            } else {
                storageApplicationDTO.setStatus(StorageApplicationStatusEnum.COLLECTED.getCode());
                storageApplicationDTO.setCollectEndTime(System.currentTimeMillis());
            }
            storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);


            MessageMqDTO messageMqDTO = null;
            if (StorageApplicationStatusEnum.COLLECTED.getCode().equals(storageApplicationDTO.getStatus())) {
                if (firstLine) {
                    messageMqDTO = new MessageMqDTO();
                    messageMqDTO.setWarehouseCode(storageApplicationDTO.getWarehouseCode());
                    messageMqDTO.setCargoCode(storageApplicationDTO.getCargoCode());
                    messageMqDTO.setBillNo(storageApplicationDTO.getStorageApplicationCode());
                    messageMqDTO.setOperationType(MessageTypeEnum.CW_IN_CONFIRM_COMPLETE.getType());
                    messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_CW_IN.getType());
                    messageMqDTO.setCreatedTime(System.currentTimeMillis());
                    messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
                    storageApplicationBO.setMessageMqDTOList(ListUtil.toList(messageMqDTO));
                }

            }

            remoteStorageApplicationClient.persist(storageApplicationBO);
            if (firstLine && null != messageMqDTO) {
                sync(messageMqDTO);
            }

            return Result.success();
        } catch (InterruptedException e) {
            throw ExceptionUtil.SYSTEM_BUSY;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    public Result<Boolean> importInit(StorageApplicationDTO storageApplicationDTO, List<StorageApplicationCollectDTO> storageApplicationCollectDTOList, List<SkuDTO> skuDTOList) {
        if (CollectionUtil.isEmpty(storageApplicationCollectDTOList)) return Result.success();

        // 生成批次
        Map<String, List<StorageApplicationCollectDTO>> lotPropertiesGroup = storageApplicationCollectDTOList.stream().collect(Collectors.groupingBy(it -> StrUtil.join(StrUtil.COLON, it.getSkuCode(), it.getSerial(), it.getReg(), it.getManufDate(), it.getExpireDate(), it.getLotNumber())));
        for (List<StorageApplicationCollectDTO> value : lotPropertiesGroup.values()) {
            StorageApplicationCollectDTO collectDTO = value.get(0);
            SkuLotCheckAndFormatCWParam skuLotCheckAndFormatCWParam = new SkuLotCheckAndFormatCWParam();
            skuLotCheckAndFormatCWParam.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
            skuLotCheckAndFormatCWParam.setBoxCode(collectDTO.getSerial());
            skuLotCheckAndFormatCWParam.setPalletCode(collectDTO.getReg());
            skuLotCheckAndFormatCWParam.setManufDate(collectDTO.getManufDate());
            skuLotCheckAndFormatCWParam.setExpireDate(collectDTO.getExpireDate());
            skuLotCheckAndFormatCWParam.setProductionNo(collectDTO.getLotNumber());
            skuLotCheckAndFormatCWParam.setExternalSkuLotNo(storageApplicationDTO.getCustomName());
            SkuDTO skuDTO = skuDTOList.stream()
                    .filter(it -> it.getCode().equalsIgnoreCase(collectDTO.getSkuCode()))
                    .findFirst().orElseThrow(ExceptionUtil::dataError);
            SkuLotDTO andFormatCWSkuLot = skuLotBiz.findAndFormatCWSkuLot(skuLotCheckAndFormatCWParam, skuDTO);
            if (null == andFormatCWSkuLot.getId()) {
                remoteSkuLotClient.saveBatch(ListUtil.toList(andFormatCWSkuLot));
            }
            for (StorageApplicationCollectDTO storageApplicationCollectDTO : value) {
                storageApplicationCollectDTO.setSkuLotNo(andFormatCWSkuLot.getCode());
            }
        }

        StorageApplicationSkuParam storageApplicationSkuParam = new StorageApplicationSkuParam();
        storageApplicationSkuParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
        List<StorageApplicationSkuDTO> storageApplicationSkuDTOList = remoteStorageApplicationSkuClient.getList(storageApplicationSkuParam);
        for (StorageApplicationSkuDTO storageApplicationSkuDTO : storageApplicationSkuDTOList) {
            Integer reduce = storageApplicationCollectDTOList.stream()
                    .filter(it -> it.getSkuCode().equalsIgnoreCase(storageApplicationSkuDTO.getSkuCode()))
                    .map(StorageApplicationCollectDTO::getTotalCount)
                    .reduce(0, Integer::sum);
            storageApplicationSkuDTO.setCollectCount(reduce);
        }

        // 导入的商品必须在入库申请商品详情中
        for (StorageApplicationCollectDTO storageApplicationCollectDTO : storageApplicationCollectDTOList) {
            StorageApplicationSkuDTO storageApplicationSkuDTO = storageApplicationSkuDTOList.stream()
                    .filter(it -> StrUtil.equalsIgnoreCase(it.getSkuCode(), storageApplicationCollectDTO.getSkuCode()))
                    .findFirst().orElse(null);
            if (null == storageApplicationSkuDTO) {
                throw ExceptionUtil.exceptionWithMessage("无法找到cw的商品，请确认后维护商品");
            }
        }

        long currentTimeMillis = System.currentTimeMillis();
        StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
        storageApplicationCabinetParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
        List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);
        for (StorageApplicationCabinetDTO storageApplicationCabinetDTO : storageApplicationCabinetDTOList) {
            storageApplicationCabinetDTO.setStatus(StorageApplicationCabinetStatusEnum.COLLECTED.getCode());
            storageApplicationCabinetDTO.setCollectEndTime(currentTimeMillis);
        }

        // 导入的商品必须在入库申请商品详情中
        for (StorageApplicationCollectDTO storageApplicationCollectDTO : storageApplicationCollectDTOList) {
            if (StrUtil.isBlank(storageApplicationCollectDTO.getCabinetCode())) continue;
            StorageApplicationCabinetDTO storageApplicationCabinetDTO = storageApplicationCabinetDTOList.stream()
                    .filter(it -> it.getCabinetCode().equalsIgnoreCase(storageApplicationCollectDTO.getCabinetCode()))
                    .findFirst().orElse(null);
            if (null == storageApplicationCabinetDTO) {
                throw ExceptionUtil.exceptionWithMessage(String.format("柜号信息不存在%s", storageApplicationCollectDTO.getCabinetCode()));
            }
        }

        List<MessageMqDTO> messageMqDTOList = new ArrayList<>();
        for (StorageApplicationCollectDTO storageApplicationCollectDTO : storageApplicationCollectDTOList) {
            MessageMqDTO messageMqDTO = new MessageMqDTO();
            messageMqDTO.setWarehouseCode(storageApplicationDTO.getWarehouseCode());
            messageMqDTO.setCargoCode(storageApplicationDTO.getCargoCode());
            messageMqDTO.setBillNo(storageApplicationDTO.getStorageApplicationCode());
            messageMqDTO.setBatchSerialNo(storageApplicationCollectDTO.getBatchSerialNo());
            messageMqDTO.setOperationType(MessageTypeEnum.OPERATION_CW_STORAGE_APPLICATION.getType());
            messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_CW_IN.getType());
            messageMqDTO.setCreatedTime(System.currentTimeMillis());
            messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
            messageMqDTOList.add(messageMqDTO);
        }

        List<RegRecordDTO> regRecordDTOList = new ArrayList<>();
        for (StorageApplicationCollectDTO storageApplicationCollectDTO : storageApplicationCollectDTOList) {
            if (StrUtil.isBlank(storageApplicationCollectDTO.getReg())) {
                continue;
            }
            RegRecordDTO regRecordDTO = new RegRecordDTO();
            regRecordDTO.setWarehouseCode(storageApplicationCollectDTO.getWarehouseCode());
            regRecordDTO.setReg(storageApplicationCollectDTO.getReg());
            regRecordDTO.setBoxCount(storageApplicationCollectDTO.getBoxCount());
            regRecordDTO.setTotalCount(storageApplicationCollectDTO.getTotalCount());
            regRecordDTOList.add(regRecordDTO);
        }

        StorageApplicationBO storageApplicationBO = new StorageApplicationBO();
        storageApplicationDTO.setStatus(StorageApplicationStatusEnum.COLLECTED.getCode());
        storageApplicationDTO.setCollectEndTime(currentTimeMillis);
        storageApplicationBO.setStorageApplicationDTO(storageApplicationDTO);
        storageApplicationBO.setStorageApplicationSkuDTOList(storageApplicationSkuDTOList);
        storageApplicationBO.setStorageApplicationCabinetDTOList(storageApplicationCabinetDTOList);
        storageApplicationBO.setStorageApplicationCollectDTOList(storageApplicationCollectDTOList);
        storageApplicationBO.setMessageMqDTOList(messageMqDTOList);
        storageApplicationBO.setRegRecordDTOList(regRecordDTOList);

        return Result.success(remoteStorageApplicationClient.persist(storageApplicationBO));
    }


    @Override
    public Result<List<SkuDTO>> cwSkuList() {
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.cwCargo();
        if (null == cargoOwnerDTO) return Result.success(ListUtil.empty());

        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(cargoOwnerDTO.getCode());
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (!CollectionUtils.isEmpty(skuDTOList)) {
            skuDTOList = skuDTOList.stream().filter(a -> !StringUtils.isEmpty(a.getGoodCode())).collect(Collectors.toList());
        }
        return Result.success(skuDTOList);
    }

    private String dateStr(Long dateTime) {
        if (null == dateTime) return StrUtil.EMPTY;
        if (dateTime <= 0) return StrUtil.EMPTY;
        if (dateTime.equals(Long.MAX_VALUE)) return StrUtil.EMPTY;
        return DateTime.of(dateTime).toDateStr();
    }

    private String cwDateStr(Long dateTime) {
        if (null == dateTime) return StrUtil.EMPTY;
        if (dateTime <= 0) return StrUtil.EMPTY;
        if (dateTime.equals(Long.MAX_VALUE)) return StrUtil.EMPTY;
        return DateTime.of(dateTime).toString("yyMMdd");
    }

    private String timeStr(Long dateTime) {
        if (null == dateTime) return StrUtil.EMPTY;
        if (dateTime <= 0) return StrUtil.EMPTY;
        if (dateTime.equals(Long.MAX_VALUE)) return StrUtil.EMPTY;
        return DateTime.of(dateTime).toString();
    }

    private void log(StorageApplicationDTO storageApplicationDTO, String content) {
        BillLogDTO billLogDTO = new BillLogDTO();
        billLogDTO.setCargoCode(storageApplicationDTO.getCargoCode());
        billLogDTO.setBillNo(storageApplicationDTO.getStorageApplicationCode());
        billLogDTO.setBillType(BillLogTypeEnum.CW_STORAGE_APPLICATION.getType());
        billLogDTO.setOpBy(CurrentUserHolder.getUserName());
        billLogDTO.setOpDate(System.currentTimeMillis());
        billLogDTO.setOpContent(content);
        billLogDTO.setOpRemark(content);
        remoteBillLogClient.save(billLogDTO);
    }

    private StorageApplicationDTO findByReferenceNumber(String referenceNumber) {
        StorageApplicationParam search = new StorageApplicationParam();
        search.setReferenceNumber(referenceNumber);
        search.setNoStatus(StorageApplicationStatusEnum.CANCEL.getCode());
        List<StorageApplicationDTO> storageApplicationDTOList = remoteStorageApplicationClient.getList(search);
        if (CollectionUtil.isEmpty(storageApplicationDTOList))
            throw ExceptionUtil.exceptionWithMessage("入库参考号不存在");
        if (storageApplicationDTOList.size() > 1)
            throw ExceptionUtil.exceptionWithMessage("相同入库参考号存在多条有效数据");
        return storageApplicationDTOList.get(0);
    }

    private LocationDTO locationCheck(String locationCode) {
        LocationParam locationParam = new LocationParam();
        locationParam.setCode(locationCode);
        LocationDTO locationDTO = remoteLocationClient.get(locationParam);
        if (null == locationDTO) throw ExceptionUtil.exceptionWithMessage("库位必须存在且类型为【正品存储位】");
        if (!LocationTypeEnum.LOCATION_TYPE_STORE.getType().equalsIgnoreCase(locationDTO.getType())) {
            throw ExceptionUtil.exceptionWithMessage("库位必须存在且类型为【正品存储位】");
        }
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCode(locationDTO.getZoneCode());
        ZoneDTO zoneDTO = remoteZoneClient.get(zoneParam);
        if (null == zoneDTO) throw ExceptionUtil.exceptionWithMessage("库位必须存在且类型为【正品存储位】");
        if (!SkuQualityEnum.SKU_QUALITY_AVL.getLevel().equalsIgnoreCase(zoneDTO.getSkuQuality())) {
            throw ExceptionUtil.exceptionWithMessage("库位必须存在且类型为【正品存储位】");
        }
        return locationDTO;
    }

    private void analyze(StorageApplicationParam storageApplicationParam) {
        RegAnalyzeDTO analyze = regAnalyzeBiz.analyze(storageApplicationParam.getRegOrSerialOrUpc());
        storageApplicationParam.setCollectType(analyze.getCollectType());
        storageApplicationParam.setCollectTypeDesc(analyze.getCollectTypeDesc());
        storageApplicationParam.setReg(analyze.getReg());
        storageApplicationParam.setUpc(analyze.getUpc());
        storageApplicationParam.setManufDateDesc(analyze.getManufDateDesc());
        storageApplicationParam.setManufDate(analyze.getManufDate());
        storageApplicationParam.setExpireDateDesc(analyze.getExpireDateDesc());
        storageApplicationParam.setExpireDate(analyze.getExpireDate());
        storageApplicationParam.setLotNumber(analyze.getLotNumber());
        storageApplicationParam.setBoxCount(analyze.getBoxCount());
        storageApplicationParam.setSerial(analyze.getSerial());
    }

    @ApiOperation("是不是托盘号")
    private boolean isReg(String reg) {
        if (StrUtil.isBlank(reg)) return false;
        return reg.contains("(00)") && reg.contains("(37)");
    }

    @ApiOperation("是不是箱码")
    private boolean isSerial(String reg) {
        if (StrUtil.isBlank(reg)) return false;
        return reg.contains("(01)") && reg.contains("(21)");
    }

    private List<StorageApplicationSkuDTO> storageApplicationSkuDTOList(StorageApplicationDTO storageApplicationDTO) {
        StorageApplicationSkuParam storageApplicationSkuParam = new StorageApplicationSkuParam();
        storageApplicationSkuParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
        List<StorageApplicationSkuDTO> storageApplicationSkuDTOList = remoteStorageApplicationSkuClient.getList(storageApplicationSkuParam);
        if (CollectionUtil.isEmpty(storageApplicationSkuDTOList)) {
            throw ExceptionUtil.exceptionWithMessage("数据异常，商品信息不能为空");
        }
        return storageApplicationSkuDTOList;
    }

    private StorageApplicationCabinetDTO storageApplicationCabinetDTO(StorageApplicationDTO
                                                                              storageApplicationDTO, String cabinet) {
        StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
        storageApplicationCabinetParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
        storageApplicationCabinetParam.setCabinetCode(cabinet);
        StorageApplicationCabinetDTO storageApplicationCabinetDTO = remoteStorageApplicationCabinetClient.get(storageApplicationCabinetParam);
        if (null == storageApplicationCabinetDTO) throw ExceptionUtil.exceptionWithMessage("柜号信息不存在");
        return storageApplicationCabinetDTO;
    }

    private List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList(StorageApplicationDTO
                                                                                        storageApplicationDTO) {
        StorageApplicationCabinetParam storageApplicationCabinetParam = new StorageApplicationCabinetParam();
        storageApplicationCabinetParam.setStorageApplicationCode(storageApplicationDTO.getStorageApplicationCode());
        List<StorageApplicationCabinetDTO> storageApplicationCabinetDTOList = remoteStorageApplicationCabinetClient.getList(storageApplicationCabinetParam);
        if (CollectionUtil.isNotEmpty(storageApplicationCabinetDTOList)) {
            long count = storageApplicationCabinetDTOList.stream().map(StorageApplicationCabinetDTO::getCabinetCode).count();
            if (storageApplicationCabinetDTOList.size() > count) {
                throw ExceptionUtil.exceptionWithMessage("数据异常，入库申请柜号信息重复");
            }
        }
        return storageApplicationCabinetDTOList;
    }

    private StorageApplicationSkuDTO upcShouldInSkuInfo(String
                                                                skuCode, List<StorageApplicationSkuDTO> storageApplicationSkuDTOList) {
        StorageApplicationSkuDTO storageApplicationSkuDTO = storageApplicationSkuDTOList.stream()
                .filter(it -> it.getSkuCode().equalsIgnoreCase(skuCode))
                .findFirst().orElse(null);
        if (null == storageApplicationSkuDTO) {
            throw ExceptionUtil.exceptionWithMessage("当前采集商品再入库单中不存在，请确认");
        }
        return storageApplicationSkuDTO;
    }

    private SkuUpcDTO skuUpcDTO(String cargo, String upcCode) {
        SkuUpcParam skuUpcParam = new SkuUpcParam();
        skuUpcParam.setUpcCode(upcCode);
        skuUpcParam.setCargoCode(cargo);
        return remoteSkuClient.getSkuUpc(skuUpcParam);
    }

    private SkuDTO skuDTO(String cargo, String skuCode) {
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(cargo);
        skuParam.setCode(skuCode);
        return remoteSkuClient.get(skuParam);
    }

    private void checkLotNumber(String lotNumber) {
        if (!lotNumber.matches("^\\d{10}$")) {
            throw ExceptionUtil.exceptionWithMessage("批次号,请输入2位年+2位月+2为日+4位数字");
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        dateFormat.setLenient(false);
        try {
            String subStr = lotNumber.substring(0, 6);
            dateFormat.parse(subStr);
        } catch (Exception e) {
            throw ExceptionUtil.exceptionWithMessage("批次号,请输入2位年+2位月+2为日无效日期,请确认");
        }
    }
}
