package com.dt.platform.wms.client.rec;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.asn.AsnOutTypeEnum;
import com.dt.component.common.enums.asn.AsnStatusEnum;
import com.dt.component.common.enums.base.ContainerStatusEnum;
import com.dt.component.common.enums.rec.ReceiptError;
import com.dt.component.common.enums.rec.ReceiptExtraStatusEnum;
import com.dt.component.common.enums.rec.ReceiptStatusEnum;
import com.dt.component.common.enums.rec.ReceiptTypeEnum;
import com.dt.component.common.enums.tally.TallyExtraGoodsEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.ContainerDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.contLog.ContainerLogDTO;
import com.dt.domain.base.dto.log.TallyLogDTO;
import com.dt.domain.base.param.ContainerParam;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.domain.bill.dto.AsnDetailDTO;
import com.dt.domain.bill.dto.AsnLogDTO;
import com.dt.domain.bill.dto.rec.ReceiptExtraDTO;
import com.dt.domain.bill.dto.rec.ReceiptExtraDetailDTO;
import com.dt.domain.bill.dto.tally.TallyDTO;
import com.dt.domain.bill.dto.tally.TallyDetailDTO;
import com.dt.domain.bill.param.AsnModifyParam;
import com.dt.domain.bill.param.AsnParam;
import com.dt.domain.bill.param.rec.ReceiptExtraDetailParam;
import com.dt.domain.bill.param.rec.ReceiptExtraParam;
import com.dt.domain.bill.param.tally.TallyDetailParam;
import com.dt.domain.bill.param.tally.TallyParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.biz.ITallyBiz;
import com.dt.platform.wms.dto.rec.ReceiptExtraBizDTO;
import com.dt.platform.wms.dto.tally.TallyReceiveDetailBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.rec.IRemoteReceiptExtraClient;
import com.dt.platform.wms.integration.rec.IRemoteReceiptExtraDetailClient;
import com.dt.platform.wms.integration.tally.IRemoteTallyClient;
import com.dt.platform.wms.integration.tally.IRemoteTallyDetailClient;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.rec.ReceiptExtraBizParam;
import com.dt.platform.wms.transaction.IReceiptExtraGtsService;
import com.dt.platform.wms.transaction.IReceiptExtraImportCommitGtsService;
import com.dt.platform.wms.transaction.bo.CompleteReceiptExtraContBO;
import com.dt.platform.wms.transaction.bo.ReceiptExtraAndContCancelBO;
import com.dt.platform.wms.transaction.bo.ReceiptExtraCancelBO;
import com.dt.platform.wms.transaction.impl.ReceiptGtsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <p>
 * 收货作业批次 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-20
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class ReceiptExtraBizClientImpl implements IReceiptExtraBizClient {

    @Resource
    private IRemoteReceiptExtraClient remoteReceiptExtraClient;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IRemoteReceiptExtraDetailClient remoteReceiptExtraDetailClient;

    @Resource
    private ITallyBiz tallyBiz;

    @Resource
    IRemoteCargoOwnerClient iRemoteCargoOwnerClient;

    @Resource
    IRemoteWarehouseClient iRemoteWarehouseClient;

    @Resource
    IRemoteDecimalPlaceClient decimalPlaceClient;

    @Resource
    private ReceiptGtsService receiptGtsService;

    @Resource
    IRemoteContainerClient remoteContainerClient;

    @Resource
    IRemoteTallyClient remoteTallyClient;

    @Resource
    IRemoteTallyDetailClient remoteTallyDetailClient;

    @Resource
    IRemoteAsnClient iRemoteAsnClient;

    @Resource
    IReceiptExtraGtsService receiptExtraGtsService;

    @Resource
    IRemoteShelfClient remoteShelfClient;

    @Resource
    IRemoteAsnDetailClient remoteAsnDetailClient;

    @Resource
    IReceiptExtraImportCommitGtsService receiptExtraImportCommitGtsService;

    @Override
    public Result<Boolean> save(ReceiptExtraBizDTO receiptExtraBizDTO) {
        ReceiptExtraDTO receiptExtraDTO = ConverterUtil.convert(receiptExtraBizDTO, ReceiptExtraDTO.class);
        Boolean result = remoteReceiptExtraClient.save(receiptExtraDTO);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> saveBatch(List<ReceiptExtraBizDTO> receiptExtraBizDTOList) {
        List<ReceiptExtraDTO> receiptExtraDTOList = ConverterUtil.convertList(receiptExtraBizDTOList, ReceiptExtraDTO.class);
        Boolean result = remoteReceiptExtraClient.saveBatch(receiptExtraDTOList);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> modify(ReceiptExtraBizDTO receiptExtraBizDTO) {
        ReceiptExtraDTO receiptExtraDTO = ConverterUtil.convert(receiptExtraBizDTO, ReceiptExtraDTO.class);
        Boolean result = remoteReceiptExtraClient.modify(receiptExtraDTO);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> modifyBatch(List<ReceiptExtraBizDTO> receiptExtraBizDTOList) {
        List<ReceiptExtraDTO> receiptExtraDTOList = ConverterUtil.convertList(receiptExtraBizDTOList, ReceiptExtraDTO.class);
        Boolean result = remoteReceiptExtraClient.modifyBatch(receiptExtraDTOList);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> checkExits(ReceiptExtraBizParam param) {
        ReceiptExtraParam receiptExtraParam = ConverterUtil.convert(param, ReceiptExtraParam.class);
        Boolean result = remoteReceiptExtraClient.checkExits(receiptExtraParam);
        return Result.success(result);
    }

    @Override
    public Result<ReceiptExtraBizDTO> get(ReceiptExtraBizParam param) {
        ReceiptExtraParam receiptExtraParam = ConverterUtil.convert(param, ReceiptExtraParam.class);
        ReceiptExtraDTO receiptExtraDTO = remoteReceiptExtraClient.get(receiptExtraParam);
        ReceiptExtraBizDTO result = ConverterUtil.convert(receiptExtraDTO, ReceiptExtraBizDTO.class);

        WarehouseDTO warehouseDTO = iRemoteWarehouseClient.queryByCode(result.getWarehouseCode());
        if (!Objects.isNull(warehouseDTO)) {
            result.setWarehouseName(warehouseDTO.getName());
        } else {
            result.setWarehouseName("");
        }
        CargoOwnerDTO cargoOwnerDTO = iRemoteCargoOwnerClient.queryByCode(result.getCargoCode());
        if (!Objects.isNull(cargoOwnerDTO)) {
            result.setCargoName(cargoOwnerDTO.getName());
        } else {
            result.setCargoName("");
        }
        result.setNumberFormat(decimalPlaceClient.getNumberFormat(result.getWarehouseCode(), result.getCargoCode()));

        return Result.success(result);
    }

    @Override
    public Result<List<ReceiptExtraBizDTO>> getList(ReceiptExtraBizParam param) {
        ReceiptExtraParam receiptExtraParam = ConverterUtil.convert(param, ReceiptExtraParam.class);
        List<ReceiptExtraDTO> list = remoteReceiptExtraClient.getList(receiptExtraParam);
        List<ReceiptExtraBizDTO> result = ConverterUtil.convertList(list, ReceiptExtraBizDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<ReceiptExtraBizDTO>> getPage(ReceiptExtraBizParam param) {
        ReceiptExtraParam receiptExtraParam = ConverterUtil.convert(param, ReceiptExtraParam.class);
        Page<ReceiptExtraDTO> page = remoteReceiptExtraClient.getPage(receiptExtraParam);
        Page<ReceiptExtraBizDTO> result = ConverterUtil.convertPage(page, ReceiptExtraBizDTO.class);
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            result.setRecords(result.getRecords().stream().map(entity -> {
                WarehouseDTO warehouseDTO = iRemoteWarehouseClient.queryByCode(entity.getWarehouseCode());
                if (!Objects.isNull(warehouseDTO)) {
                    entity.setWarehouseName(warehouseDTO.getName());
                } else {
                    entity.setWarehouseName("");
                }
                CargoOwnerDTO cargoOwnerDTO = iRemoteCargoOwnerClient.queryByCode(entity.getCargoCode());
                if (!Objects.isNull(cargoOwnerDTO)) {
                    entity.setCargoName(cargoOwnerDTO.getName());
                } else {
                    entity.setCargoName("");
                }
                entity.setNumberFormat(decimalPlaceClient.getNumberFormat(entity.getWarehouseCode(), entity.getCargoCode()));
                return entity;
            }).collect(Collectors.toList()));
        }
        return Result.success(result);
    }

    @Override
    public Result<Boolean> remove(ReceiptExtraBizParam param) {
        ReceiptExtraParam receiptExtraParam = ConverterUtil.convert(param, ReceiptExtraParam.class);
        Boolean result = remoteReceiptExtraClient.remove(receiptExtraParam);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> cancel(ReceiptExtraBizParam param) {
        if (StringUtils.isEmpty(param.getRecExtraId()) || StringUtils.isEmpty(param.getContCode())) {
            throw new BaseException(BaseBizEnum.NULL_ARGUMENT);
        }
        ReceiptExtraParam receiptExtraParam = new ReceiptExtraParam();
        receiptExtraParam.setRecExtraId(param.getRecExtraId());
        ReceiptExtraDTO receiptExtraDTO = remoteReceiptExtraClient.get(receiptExtraParam);
        if (receiptExtraDTO == null) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        if (!receiptExtraDTO.getStatus().equalsIgnoreCase(ReceiptStatusEnum.CREATE_SHELF.getCode())) {
            throw new BaseException(ReceiptError.RECEIPT_CANCEL_STATUS_ERROR, receiptExtraDTO.getRecExtraId());
        }
        if (!receiptExtraDTO.getReceiptType().equalsIgnoreCase(ReceiptTypeEnum.IMPORT_RECEIPT.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "取消只能操作导入收货的数据");
        }
        receiptExtraDTO.setStatus(ReceiptStatusEnum.CANCLE_SHELF.code());
        ReceiptExtraDetailParam receiptExtraDetailParam = new ReceiptExtraDetailParam();
        receiptExtraDetailParam.setRecExtraId(receiptExtraDTO.getRecExtraId());
        List<ReceiptExtraDetailDTO> detailClientList = remoteReceiptExtraDetailClient.getList(receiptExtraDetailParam);
        if (!CollectionUtils.isEmpty(detailClientList)) {
            detailClientList.forEach(a -> a.setStatus(ReceiptStatusEnum.CANCLE_SHELF.code()));
        }

        ContainerDTO occupyContainer = remoteContainerClient.queryByCode(param.getContCode());

        //容器日志记录
        ContainerLogDTO containerLogDTO = new ContainerLogDTO();
        containerLogDTO.setWarehouseCode(occupyContainer.getWarehouseCode());
        containerLogDTO.setContCode(occupyContainer.getCode());
        containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
        containerLogDTO.setCreatedTime(System.currentTimeMillis());
        containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
        containerLogDTO.setOpContent(String.format("多货收货作业批次%s释放容器:%s", receiptExtraDTO.getRecExtraId(), occupyContainer.getCode()));
        containerLogDTO.setOpDate(System.currentTimeMillis());
        containerLogDTO.setOccupyNo(occupyContainer.getOccupyNo());
        containerLogDTO.setOccupyType(occupyContainer.getOccupyType());

        occupyContainer.setStatus(ContainerStatusEnum.ENABLE.getValue());
        occupyContainer.setOccupyType("");
        occupyContainer.setOccupyNo("");
        occupyContainer.setRemark("");

        ReceiptExtraCancelBO receiptCancelBO = new ReceiptExtraCancelBO();
        receiptCancelBO.setContainer(occupyContainer);
        receiptCancelBO.setReceiptExtraDTO(receiptExtraDTO);
        receiptCancelBO.setContainerLogDTO(containerLogDTO);
        receiptCancelBO.setReceiptExtraDetailDTOList(detailClientList);
        receiptGtsService.receiptExtraCancelCommitContext(receiptCancelBO);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> cancelReceiptAndStock(CodeParam param) throws Exception {
        String recExtraId = param.getCode();
        String lockKey = StrUtil.join(StrUtil.COLON, CurrentRouteHolder.getWarehouseCode(), ReceiptExtraDTO.class.getSimpleName(), recExtraId);
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(1, 30, TimeUnit.SECONDS);
            if (locked) {
                ReceiptExtraParam receiptExtraParam = new ReceiptExtraParam();
                receiptExtraParam.setRecExtraId(recExtraId);
                ReceiptExtraDTO receiptExtraDTO = remoteReceiptExtraClient.get(receiptExtraParam);
                if (ObjectUtil.isEmpty(receiptExtraDTO)) {
                    throw new BaseException(BaseBizEnum.TIP, "多货收货作业批次不存在！receiptExtraDTO：" + receiptExtraDTO);
                }
                if (!Objects.equals(receiptExtraDTO.getStatus(), ReceiptStatusEnum.WAIT_SHELF.code())) {
                    throw new BaseException(BaseBizEnum.TIP, "非待上架状态码不能取消多货收货作业批次");
                }
                ReceiptExtraDetailParam receiptExtraDetailParam = new ReceiptExtraDetailParam();
                receiptExtraDetailParam.setRecExtraId(recExtraId);
                List<ReceiptExtraDetailDTO> receiptExtraDetailDTOList = remoteReceiptExtraDetailClient.getList(receiptExtraDetailParam);
                if (CollectionUtil.isEmpty(receiptExtraDetailDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, "多货收货作业批次明细不存在！recExtraId：" + recExtraId);
                }
                //取消bo
                ReceiptExtraAndContCancelBO receiptExtraAndContCancelBO = new ReceiptExtraAndContCancelBO();
                List<String> contCodeList = new ArrayList<>();
                // 取消收货作业批次、封装库存操作参数
                receiptExtraDTO.setStatus(ReceiptExtraStatusEnum.CANCLE_SHELF.getCode());
                contCodeList.add(receiptExtraDTO.getContCode());
                // 取消收货作业批次明细
                receiptExtraDetailDTOList.forEach(receiptDetailDTO -> {
                    receiptDetailDTO.setStatus(ReceiptExtraStatusEnum.CANCLE_SHELF.getCode());
                });
                List<ContainerDTO> containerDTOList = new ArrayList<>();
                List<ContainerLogDTO> containerLogDTOList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(contCodeList)) {
                    ContainerParam containerParam = new ContainerParam();
                    containerParam.setCodeList(contCodeList);
                    containerDTOList = remoteContainerClient.getList(containerParam);
                    containerDTOList.forEach(containerDTO -> {
                        // 记录容器释放日志
                        ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                        BeanUtils.copyProperties(containerDTO, containerLogDTO);
                        containerLogDTO.setId(null);
                        containerLogDTO.setContCode(containerDTO.getCode());
                        containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                        containerLogDTO.setOpContent(String.format("取消多货收货,绑定单号%s,释放容器:%s", containerDTO.getOccupyNo(), containerDTO.getCode()));
                        containerLogDTO.setOpDate(System.currentTimeMillis());
                        containerLogDTOList.add(containerLogDTO);
                        // 释放容器
                        containerDTO.setOccupyNo("");
                        containerDTO.setOccupyType("");
                        containerDTO.setRemark("");
                        containerDTO.setStatus(ContainerStatusEnum.ENABLE.getValue());
                    });
                }
                //asn明细会写数量削减
                AsnParam asnParam = new AsnParam();
                asnParam.setAsnId(receiptExtraDTO.getAsnId());
                AsnDTO asnDTO = iRemoteAsnClient.get(asnParam);
                if (asnDTO == null) {
                    throw new BaseException(BaseBizEnum.TIP, "入库单不存在");
                }
                //判定asn非完成状态才可以取消
                if (asnDTO.getStatus().equalsIgnoreCase(AsnStatusEnum.COMPLETE.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "入库单已完成,不允许取消收货");
                }
                List<AsnDetailDTO> asnDetailDTOList = iRemoteAsnClient.getDetailList(asnParam);
                if (CollectionUtil.isEmpty(asnDetailDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, "入库单明细不存在");
                }
                // 记录入库单日志
                AsnLogDTO asnLogDTO = new AsnLogDTO();
                BeanUtils.copyProperties(asnDTO, asnLogDTO);
                asnLogDTO.setMsg("取消多货收货记录：" + recExtraId);
                asnLogDTO.setOpBy(CurrentUserHolder.getUserName());
                receiptExtraAndContCancelBO.setAsnLogDTO(asnLogDTO);
                receiptExtraAndContCancelBO.setContainerDTOList(containerDTOList);
                receiptExtraAndContCancelBO.setContainerLogDTOList(containerLogDTOList);
                receiptExtraAndContCancelBO.setReceiptExtraDTO(receiptExtraDTO);
                receiptExtraAndContCancelBO.setReceiptExtraDetailDTOList(receiptExtraDetailDTOList);
                receiptExtraGtsService.cancelReceiptExtraAndCont(receiptExtraAndContCancelBO);
            } else {
                log.info("多货收货作业批次取消收货锁竞争失败");
            }
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> completeExtraContByImportRec(CodeListParam param) throws Exception {
        //只处理导入的收货作业批次
        ReceiptExtraParam receiptExtraParam = new ReceiptExtraParam();
        receiptExtraParam.setRecExtraIdList(param.getCodeList());
        receiptExtraParam.setReceiptType(ReceiptTypeEnum.IMPORT_RECEIPT.getCode());
        receiptExtraParam.setStatus(ReceiptExtraStatusEnum.CREATE_SHELF.getCode());
        List<ReceiptExtraDTO> receiptExtraDTOList = remoteReceiptExtraClient.getList(receiptExtraParam);
        if (CollectionUtils.isEmpty(receiptExtraDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到导入收货的数据");
        }
        ReceiptExtraDetailParam receiptExtraDetailParam = new ReceiptExtraDetailParam();
        receiptExtraDetailParam.setRecExtraIdList(receiptExtraDTOList.stream().map(ReceiptExtraDTO::getRecExtraId).distinct().collect(Collectors.toList()));
        List<ReceiptExtraDetailDTO> originReceiptExtraDetailDTOList = remoteReceiptExtraDetailClient.getList(receiptExtraDetailParam);
        if (CollectionUtils.isEmpty(originReceiptExtraDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到导入收货的数据");
        }
        //生成上架单数据
        for (ReceiptExtraDTO receiptExtraDTO : receiptExtraDTOList) {
            RLock lock = redissonClient.getLock("dt_wms_complete_cont_receipt_extra_submit_lock:" + receiptExtraDTO.getRecExtraId());
            try {
                boolean tryLock = lock.tryLock(0, 30, TimeUnit.SECONDS);
                if (!tryLock) {
                    continue;
                }
                //是否理货报告收货
                Boolean isTally = false;
                if (!StringUtils.isEmpty(receiptExtraDTO.getTallyCode())) {
                    isTally = true;
                }
                //提交BO
                CompleteReceiptExtraContBO completeReceiptExtraContBO = new CompleteReceiptExtraContBO();
                //当前多货收货作业批次
                List<ReceiptExtraDetailDTO> receiptExtraDetailDTOList = originReceiptExtraDetailDTOList.stream().filter(a -> a.getRecExtraId().equals(receiptExtraDTO.getRecExtraId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(receiptExtraDetailDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, "未找到导入多货收货的明细数据");
                }
                receiptExtraDTO.setStatus(ReceiptExtraStatusEnum.WAIT_SHELF.getCode());
                receiptExtraDetailDTOList.forEach(a -> a.setStatus(ReceiptExtraStatusEnum.WAIT_SHELF.getCode()));
                receiptExtraDTO.setDetailDTOList(receiptExtraDetailDTOList);


                //----
                AsnDTO asnDTO = iRemoteAsnClient.queryOneByAsnId(receiptExtraDTO.getAsnId());
                List<AsnDetailDTO> asnDetailDTOList = remoteAsnDetailClient.queryListByAsnId(receiptExtraDTO.getAsnId());
                if (CollectionUtils.isEmpty(asnDetailDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, "收货明细数据为空");
                }
                if (asnDTO.getStatus().equalsIgnoreCase(AsnStatusEnum.ARRIVAL.getCode())) {
                    AsnModifyParam asnModifyParam = new AsnModifyParam();
                    asnDTO.setStatus(AsnStatusEnum.RECEIVING.code());
                    asnModifyParam.setAsnDTO(asnDTO);
                    asnDetailDTOList.forEach(a -> a.setStatus(AsnStatusEnum.RECEIVING.getCode()));
                    asnModifyParam.setDetailDTOList(asnDetailDTOList);
                    completeReceiptExtraContBO.setAsnModifyParam(asnModifyParam);
                }
                if (iRemoteWarehouseClient.getTaoTianWarehouse(asnDTO.getWarehouseCode())
                        && AsnOutTypeEnum.CGRK.getCode().equals(asnDTO.getOutType())) {
                    //拆分行号--理货报告
                    splitReceiptExtraDTO(receiptExtraDTO, asnDetailDTOList);
                }
                AsnLogDTO asnLogDTO = ConverterUtil.convert(asnDTO, AsnLogDTO.class);
                asnLogDTO.setMsg(DateUtil.format(new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss") + "一键导入多货收货作业批次批量完成容器(" + receiptExtraDTO.getContCode() + ")");
                asnLogDTO.setOpBy(CurrentUserHolder.getUserName());
                completeReceiptExtraContBO.setReceiptExtraDTO(receiptExtraDTO);
                completeReceiptExtraContBO.setAsnLogDTO(asnLogDTO);
                //处理理货报告
                if (isTally) {
                    TallyDTO tallyDTO = checkBackTally(receiptExtraDTO);
                    //记录理货报告日志
                    TallyLogDTO tallyLogDTO = ConverterUtil.convert(tallyDTO, TallyLogDTO.class);
                    tallyLogDTO.setOpContent(DateUtil.format(new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss") + "一键导入(理货报告)多货收货作业批次批量完成容器(" + receiptExtraDTO.getContCode() + ")");
                    tallyLogDTO.setOpRemark(DateUtil.format(new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss") + "一键导入(理货报告)多货收货作业批次批量完成容器(" + receiptExtraDTO.getContCode() + ")");
                    tallyLogDTO.setOpBy(CurrentUserHolder.getUserName());
                    completeReceiptExtraContBO.setTallyLogDTO(tallyLogDTO);
                }
                receiptExtraImportCommitGtsService.commitImportReceiptByCont(completeReceiptExtraContBO);
            } catch (Exception e) {
                log.error("commitImportReceiptByCont recId:{}", receiptExtraDTO.getRecExtraId(), e);
                throw new BaseException(BaseBizEnum.TIP, e.getMessage() == null ? "批量确认异常" : e.getMessage());
            } finally {
                if (lock.isLocked()) {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }
        }
        return Result.success(Boolean.TRUE);
    }

    /**
     * @param receiptExtraDTO
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2024/3/21 10:30
     */
    private void splitReceiptExtraDTO(ReceiptExtraDTO receiptExtraDTO, List<AsnDetailDTO> asnDetailDTOList) {
        List<ReceiptExtraDetailDTO> detailDTOList = receiptExtraDTO.getDetailDTOList();
        ReceiptExtraDetailDTO receiptDetailDTO = detailDTOList.get(0);
        //淘天理货数据
        List<TallyReceiveDetailBizDTO> tallyToReceiveData = tallyBiz.getTallyToReceiveData(receiptExtraDTO.getTallyCode(), Arrays.asList(receiptDetailDTO.getSkuCode()), false, true);
        //组装收货作业批次明细
        //组装收货作业批次明细
        tallyToReceiveData = tallyToReceiveData.stream()
                .filter(a -> !Objects.equals(a.getExtraGoods(), TallyExtraGoodsEnum.NORMAL.getCode()))
                .filter(a -> a.getWaitQty().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        BigDecimal commitRecQty = receiptDetailDTO.getSkuQty();
        for (TallyReceiveDetailBizDTO entity : tallyToReceiveData) {
            if (commitRecQty.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            if (!Objects.equals(entity.getSkuQuality(), receiptDetailDTO.getSkuQuality())
                    || !Objects.equals(entity.getSkuCode(), receiptDetailDTO.getSkuCode())
                    || !Objects.equals(entity.getInventoryType(), receiptDetailDTO.getInventoryType())
                    || !Objects.equals(entity.getCallBackUpper(), receiptDetailDTO.getCallBackUpper())
                    || !Objects.equals(entity.getManufDate(), receiptDetailDTO.getManufDate())
                    || !Objects.equals(entity.getValidityCode(), receiptDetailDTO.getValidityCode())
                    || !Objects.equals(entity.getExpireDate(), receiptDetailDTO.getExpireDate())) {
                continue;
            }
            //待收数量
            BigDecimal waitQty = entity.getWaitQty();
            if (waitQty.compareTo(commitRecQty) >= 0) {
                receiptDetailDTO.setExtNo(entity.getLineNo());
            } else {
                ReceiptExtraDetailDTO receiptDetailDTONew = ObjectUtil.cloneByStream(receiptDetailDTO);
                receiptDetailDTONew.setId(null);
                receiptDetailDTONew.setSkuQty(waitQty);
                receiptDetailDTONew.setLineSeq(RandomUtil.randomNumbers(6));
                receiptDetailDTONew.setExtNo(entity.getLineNo());

                receiptDetailDTO.setSkuQty(receiptDetailDTO.getSkuQty().subtract(waitQty));
                commitRecQty = commitRecQty.subtract(waitQty);
                detailDTOList.add(receiptDetailDTONew);
            }
        }
        receiptExtraDTO.setDetailDTOList(detailDTOList);
    }

    /**
     * @param receiptExtraDTO
     * @param receiptExtraDTO
     * @return com.dt.domain.bill.dto.tally.TallyDTO
     * @author: WuXian
     * description:
     * create time: 2022/3/3 15:12
     */
    private TallyDTO checkBackTally(ReceiptExtraDTO receiptExtraDTO) {
        TallyParam tallyParam = new TallyParam();
        tallyParam.setTallyCode(receiptExtraDTO.getTallyCode());
        TallyDTO tallyDTO = remoteTallyClient.get(tallyParam);
        if (tallyDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("理货报告%s不存在", receiptExtraDTO.getTallyCode()));
        }
        TallyDetailParam tallyDetailParam = new TallyDetailParam();
        tallyDetailParam.setTallyCode(tallyDTO.getTallyCode());
        List<TallyDetailDTO> tallyDetailDTOList = remoteTallyDetailClient.getList(tallyDetailParam);
        if (CollectionUtils.isEmpty(tallyDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("理货报告明细%s不存在", receiptExtraDTO.getTallyCode()));
        }
        return tallyDTO;
    }

}
