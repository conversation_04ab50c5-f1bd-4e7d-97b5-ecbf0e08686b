package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.SalePlatformStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SalePlatformDTO;
import com.dt.domain.base.param.SalePlatformParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.dto.sale.SalePlatformBizDTO;
import com.dt.platform.wms.integration.IRemoteSalePlatform;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.sale.SalePlatformBizParam;
import com.dt.platform.wms.param.sale.SalePlatformQueryBizParam;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@DubboService(version = "${dubbo.service.version}")
public class SalePlatformBizClient implements ISalePlatformBizClient {
    @Resource
    IRemoteSalePlatform remoteSalePlatform;

    @Override
    public Result<Boolean> save(SalePlatformBizParam param) {
        param.setStatus(SalePlatformStatusEnum.STATUS_ENABLED.value().toString());
        SalePlatformParam salePlatformParam = ConverterUtil.convert(param, SalePlatformParam.class);
        SalePlatformParam __salePlatformParam = new SalePlatformParam();
        __salePlatformParam.setCode(param.getCode());

        if (remoteSalePlatform.get(__salePlatformParam) != null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("销售平台代码重复:%s", param.getCode()));
        }
        return Result.success(remoteSalePlatform.save(salePlatformParam));
    }

    @Override
    public Result<Boolean> modify(SalePlatformBizParam param) {
        SalePlatformParam salePlatformParam = ConverterUtil.convert(param, SalePlatformParam.class);
        SalePlatformParam __salePlatformParam = new SalePlatformParam();
        __salePlatformParam.setCode(param.getCode());
        SalePlatformDTO salePlatformDTO = remoteSalePlatform.get(__salePlatformParam);
        if (salePlatformDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("参数错误:%s", param.getCode()));
        }
        salePlatformParam.setId(salePlatformDTO.getId());
        return Result.success(remoteSalePlatform.modify(salePlatformParam));
    }

    @Override
    public Result<SalePlatformBizDTO> get(CodeParam param) {
        SalePlatformParam salePlatformParam = ConverterUtil.convert(param, SalePlatformParam.class);
        return Result.success(ConverterUtil.convert(remoteSalePlatform.get(salePlatformParam), SalePlatformBizDTO.class));
    }

    @Override
    public Result<Page<SalePlatformBizDTO>> getPage(SalePlatformQueryBizParam param) {
        SalePlatformParam salePlatformParam = ConverterUtil.convert(param, SalePlatformParam.class);
        return Result.success(ConverterUtil.convertPage(remoteSalePlatform.getPage(salePlatformParam), SalePlatformBizDTO.class));
    }

    @Override
    public Result<List<SalePlatformBizDTO>> getList(SalePlatformQueryBizParam param) {
        SalePlatformParam salePlatformParam = ConverterUtil.convert(param, SalePlatformParam.class);
        return Result.success(ConverterUtil.convertList(remoteSalePlatform.getList(salePlatformParam), SalePlatformBizDTO.class));
    }

    @Override
    public Result<Map<String, SalePlatformDTO>> salePlatformMap(List<String> salePlatformList) {
        return Result.success(remoteSalePlatform.salePlatformMap(salePlatformList));
    }
}
