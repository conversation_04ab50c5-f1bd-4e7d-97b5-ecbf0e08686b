package com.dt.platform.wms.biz.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.SourceCodeScanTypeEnum;
import com.dt.component.common.enums.SystemEventEnum;
import com.dt.component.common.enums.allocation.AllocationIsPreEnum;
import com.dt.component.common.enums.base.IsHttpEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.enums.cargo.CargoConfigStatusEnum;
import com.dt.component.common.enums.material.Is4PLEnum;
import com.dt.component.common.enums.material.MaterialTagEnum;
import com.dt.component.common.enums.material.MaterialTypeEnum;
import com.dt.component.common.enums.material.MaterialUseRecordStatusEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pkg.PackCheckEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.pkg.PackIsPreEnum;
import com.dt.component.common.enums.pkg.PackageMaterialStatusEnum;
import com.dt.component.common.enums.sku.SkuMaterialAddWeightEnum;
import com.dt.component.common.enums.sku.SkuMaterialScanTagEnum;
import com.dt.component.common.enums.sku.SkuTagEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.sku.SkuMaterialDTO;
import com.dt.domain.base.param.PackageMaterialParam;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.sku.SkuMaterialParam;
import com.dt.domain.bill.bo.CommitPackBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.material.MaterialUseRecordDTO;
import com.dt.domain.bill.dto.material.ShipmentDetailMaterialOtherDTO;
import com.dt.domain.bill.dto.performance.SystemEventDTO;
import com.dt.domain.bill.dto.pick.GiftPickSnapshotDTO;
import com.dt.domain.bill.dto.pre.PrePackageSkuDetailDTO;
import com.dt.domain.bill.dto.pre.PreSkuLotDTO;
import com.dt.domain.bill.dto.sourceCode.OutSourceCodeDTO;
import com.dt.domain.bill.param.AllocationOrderParam;
import com.dt.domain.bill.param.PickDetailParam;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.domain.bill.param.pick.GiftPickSnapshotParam;
import com.dt.domain.bill.param.pre.PrePackageSkuDetailParam;
import com.dt.domain.bill.param.pre.PreSkuLotParam;
import com.dt.domain.stock.dto.sn.SnStockDTO;
import com.dt.domain.stock.param.sn.SnStockParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.PatternCompareUtil;
import com.dt.platform.wms.biz.*;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.dto.PackCheckSkuAndAllocationDTO;
import com.dt.platform.wms.client.config.WmsTenantHelper;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.performance.IRemoteSystemEventClient;
import com.dt.platform.wms.integration.pick.IRemoteGiftPickSnapshotClient;
import com.dt.platform.wms.integration.pre.IRemotePrePackageSkuDetailClient;
import com.dt.platform.wms.integration.pre.IRemotePreSkuLotClient;
import com.dt.platform.wms.integration.sku.IRemoteSkuMaterialClient;
import com.dt.platform.wms.integration.sn.IRemoteSnStockClient;
import com.dt.platform.wms.param.check.*;
import com.dt.platform.wms.param.material.MaterialCalculateParam;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/12/4 16:46
 */
@Slf4j
@Service
public class CommitPackBizImpl implements ICommitPackBiz {

    @Resource
    IRemotePackageClient iRemotePackageClient;

    @Resource
    IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    IRemoteSeqRuleClient iRemoteSeqRuleClient;

    @Resource
    IRemoteSnStockClient remoteSnStockClient;

    @Resource
    IRemoteShipmentOrderClient iRemoteShipmentOrderClient;

    @Resource
    IRemoteCarrierClient iRemoteCarrierClient;

    @Resource
    IRemoteSkuClient iRemoteSkuClient;

    @Resource
    IRemotePackageMaterialClient iRemotePackageMaterialClient;

    @Resource
    IRemoteBillContextClient iRemoteBillContextClient;

    @Resource
    IRemoteExpressClient iRemoteExpressClient;

    @Resource
    IBusinessLogBiz iBusinessLogBiz;

    @Resource
    IRemoteAllocationOrderClient iRemoteAllocationOrderClient;

    @Resource
    ISkuStockAndLotBiz skuStockAndLotBiz;

    @Resource
    IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private ICheckPackWeightAndVolumeBiz iCheckPackWeightAndVolumeBiz;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ICheckBillNoBiz checkBillNoBiz;

    @Resource
    private IRemoteSystemEventClient remoteSystemEventClient;

    @Resource
    private IRemotePrePackageSkuDetailClient remotePrePackageSkuDetailClient;

    @Resource
    private IRemoteSkuMaterialClient remoteSkuMaterialClient;

    @Resource
    private IRemotePickDetailClient remotePickDetailClient;

    @Resource
    private IRemotePickClient remotePickClient;

    @Resource
    private IWeightBiz weightBiz;

    @Resource
    WmsOtherConfig wmsOtherConfig;

    @Resource
    WmsTenantHelper wmsTenantHelper;

    @Resource
    IRemotePreSkuLotClient remotePreSkuLotClient;

    @Resource
    private IRemoteGiftPickSnapshotClient remoteGiftPickSnapshotClient;

    @Resource
    IRemoteCargoConfigClient remoteCargoConfigClient;

    //使用正则表达式 只保留中文段
    private static Pattern pattern = Pattern.compile("[^\u4E00-\u9FA5]");

    @Override
    public String commitB2CPack(PackSubmitParam param, PickDTO pickDTO, List<PickDetailDTO> pickDetailDTOList, PackageDTO packageDTO) throws Exception {
        String key = packageDTO.getWarehouseCode() + "" + pickDTO.getPickCode() + "" + packageDTO.getPackageCode();
        RLock lock = redissonClient.getLock("dt_wms_back_pack_submit_lock:" + key);
        Boolean tryLock = false;
        String newPackageCode = "";
        try {
            tryLock = lock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "当前有包裹正在组包");
            }
            //原明细数据
            List<PackageDetailDTO> originPackDetailList = packageDTO.getListDetail().stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
            PackSubmitParam sealingPckSubmitParam = ObjectUtil.cloneByStream(param);
            //前端提交数据
            List<PackSubExpNoParam> skuBackParamList = param.getSkuBackParamList();
            //分配单原始数据
            List<AllocationOrderDTO> originAllocationList = getAllocationOrderByPackAndWaveCode(packageDTO.getPackageCode(), packageDTO.getWaveCode());
            //分配单新数据
            List<AllocationOrderDTO> newAllocationList = new ArrayList<>();
            //组包包裹明细
            List<PackageDetailDTO> newPackDetailList = new ArrayList<>();
            PackageDTO newPack = ConverterUtil.convert(packageDTO, PackageDTO.class);
            newPack.setId(null);
            newPack.setVersion(null);
            newPack.setExpressNo(null);
            newPack.setActualPackUpc(packageDTO.getActualPackUpc());
            packageDTO.setActualPackUpc(null);
            newPack.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
            //新包裹号
            newPack.setPackageCode(iRemoteSeqRuleClient.findSequence(SeqEnum.PACK_CODE_000001));
            //拆分分配和包裹明细
            splitPackDetailAndAllocation(skuBackParamList, newPackDetailList, originPackDetailList, newAllocationList, originAllocationList, packageDTO, newPack);
            //修正包裹数据
            packageDTO.setPackageStruct(analysisPackageStruct(originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList())));
            packageDTO.setListDetail(originPackDetailList);
            packageDTO.setPackageSkuQty(originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            packageDTO.setSkuTypeQty((int) originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).map(PackageDetailDTO::getSkuCode).distinct().count());
            //新包裹所有的明细
            List<PackageDetailDTO> tempPackDetailList = new ArrayList<>();
            tempPackDetailList.addAll(newPackDetailList);
            tempPackDetailList.addAll(originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), newPack.getPackageCode())).collect(Collectors.toList()));
            newPack.setPackageStruct(analysisPackageStruct(tempPackDetailList));

            newPack.setListDetail(newPackDetailList);
            newPack.setPackageSkuQty(newPackDetailList.stream().map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add).add(originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), newPack.getPackageCode())).map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add)));
            newPack.setSkuTypeQty((int) tempPackDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().count());
            newPack.setOriginPackageCode(packageDTO.getPackageCode());

            //回写出库订单数据
            StringBuilder saveShipmentLog = new StringBuilder();
            ShipmentOrderDTO shipmentOrderDTO = modifyShipment(newPack.getShipmentOrderCode(), tempPackDetailList, saveShipmentLog);
            //回写拣选单数据 ------------- 有个合单拣选单
            List<PickDetailDTO> commitPickDetailList = new ArrayList<>();
            List<PickDTO> commitPickList = new ArrayList<>();
            PickDetailDTO newPickDetailDTO = buildPickData(pickDTO, commitPickDetailList, commitPickList, packageDTO, newPack, pickDetailDTOList);
            //回写拣选单数据 -------------
            PackageMaterialDTO packageMaterialDTO = iRemotePackageMaterialClient.queryByUpcCode(newPack.getCargoCode(), newPack.getActualPackUpc());
            newPack.setActualPackNum(1);
            newPack.setActualPackWeight(packageMaterialDTO.getGrossWeight());
            //包裹重量计算
            List<String> skuCodeList = tempPackDetailList.stream().flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList());
            SkuParam skuParam = new SkuParam();
            skuParam.setCodeList(skuCodeList);
            skuParam.setCargoCode(packageDTO.getCargoCode());
            List<SkuDTO> skuList = iRemoteSkuClient.getList(skuParam);
            //扫描记录
            List<OutSourceCodeDTO> outSourceCodeDTOList = new ArrayList<>();
            //封口贴
            List<OutSourceCodeDTO> sourceCodeSealingTapeDTOList = this.checkSealingTape(sealingPckSubmitParam, newPack, skuList);

            //计算重量
            iCheckPackWeightAndVolumeBiz.calculationVolumetricAndWeight(newPack, tempPackDetailList, skuList);

            CarrierDTO carrierDTO = iRemoteCarrierClient.queryByCode(packageDTO.getCarrierCode());
            if (carrierDTO != null && carrierDTO.getIsHttp().equals(IsHttpEnum.ENABLE.getValue())) {
                //TODO 组装快递参数
                newPack.setExpressNo("");
                BigDecimal weight = weightBiz.weight(MaterialCalculateParam.builder()
                        .warehouseCode(newPack.getWarehouseCode())
                        .cargoCode(newPack.getCargoCode())
                        .is4PL(OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.FOUR_TAG) ? Is4PLEnum.YES : Is4PLEnum.NO)
                        .appointMaterialWeight(packageMaterialDTO.getGrossWeight())
                        .skuDetailList(tempPackDetailList.stream()
                                .filter(it -> it.getIsPre().equals(PackIsPreEnum.NORMAL.getCode()))
                                .map(it -> MaterialCalculateParam.SkuDetail.builder()
                                        .skuCode(it.getSkuCode())
                                        .quantity(it.getSkuQty())
                                        .build()).collect(Collectors.toList())).build());

                wmsTenantHelper.setTenantId(shipmentOrderDTO.getWarehouseCode(), shipmentOrderDTO.getCargoCode());
                ExpressResultDTO expressResultDTO = iRemoteExpressClient.getExpressResult(shipmentOrderDTO.getShipmentOrderCode(), iRemoteExpressClient.getExpressParamV2(shipmentOrderDTO, carrierDTO, newPack, tempPackDetailList, weight));
                if (StringUtils.isEmpty(expressResultDTO)) {
                    throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
                }
                if (!expressResultDTO.getSuccess()) {
                    String errorMessage = StringUtils.isEmpty(expressResultDTO.getMessage()) ? "未获取到快递单号" : expressResultDTO.getMessage();
                    //[\u4E00-\u9FA5]是unicode2的中文区间
                    Matcher matcher = pattern.matcher(errorMessage);
                    //提取中文字符
                    errorMessage = matcher.replaceAll("");
                    errorMessage = StringUtils.isEmpty(errorMessage) ? "未获取到快递单号" : errorMessage;
                    throw new BaseException(BaseBizEnum.TIP, errorMessage);
                }
                if (CollectionUtils.isEmpty(expressResultDTO.getWaybill_apply_info())) {
                    throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
                }
                if (StringUtils.isEmpty(expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code())) {
                    throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
                }
                //校验运单号是否重复
                String billNo = checkBillNoBiz.checkOutboundExpressNoEffective(carrierDTO.getCode(), expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code());
                if (!StringUtils.isEmpty(billNo)) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("拉取运单号,系统已存在有效状态的运单%s包裹，不允许拆包", expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code()));
                }
                newPack.setExpressNo(expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code());
                newPack.setExpressBranch(expressResultDTO.getWaybill_apply_info().get(0).getExpressBranchCode());
                newPack.setExpressBranchName(expressResultDTO.getWaybill_apply_info().get(0).getExpressBranchName());
            } else {
                String expressNo = packageDTO.getExpressNo() + "-" + newPack.getPackageCode().substring(newPack.getPackageCode().length() - 4);
                String billNo = checkBillNoBiz.checkOutboundExpressNoEffective(carrierDTO.getCode(), expressNo);
                if (!StringUtils.isEmpty(billNo)) {
                    throw new BaseException(BaseBizEnum.TIP, "非电子面单,系统已存在有效状态的运单包裹，不允许拆包");
                }
                newPack.setExpressNo(expressNo);
            }
            newPickDetailDTO.setExpressNo(newPack.getExpressNo());
            commitPickDetailList.forEach(a -> {
                if (StringUtils.isEmpty(a.getExpressNo())) {
                    a.setExpressNo(newPack.getExpressNo());
                }
            });
            commitPickDetailList.add(newPickDetailDTO);
            //溯源码
            CommitPackBO commitPackBO = new CommitPackBO();
            commitPackBO.setOriginAllocations(originAllocationList);
            commitPackBO.setNewAllocations(newAllocationList);
            commitPackBO.setOriginPack(packageDTO);
            newPack.setCheckCompleteDate(System.currentTimeMillis());
            newPackageCode = newPack.getPackageCode();
            //计算体积重
            iCheckPackWeightAndVolumeBiz.calculationVolumetricWeight(newPack);

            commitPackBO.setNewPack(newPack);
            commitPackBO.setPickDTOList(commitPickList);
            commitPackBO.setPickDetailDTOList(commitPickDetailList);
            commitPackBO.setShipmentOrderDTO(shipmentOrderDTO);

            // SN  check
            if (skuList.stream().anyMatch(skuDTO -> skuDTO.getSNMgmtOutNeed())) {
                if (CollectionUtils.isEmpty(param.getSNCodeParamList())) {
                    throw new BaseException(BaseBizEnum.TIP, "当前包裹有需要扫描SN,请核查");
                }
                List<OutSourceCodeDTO> snOutSourceCodeDTOList = this.checkSN(skuList, packageDTO, param);
                if (!CollectionUtils.isEmpty(snOutSourceCodeDTOList)) {
                    outSourceCodeDTOList.addAll(snOutSourceCodeDTOList);
                    //包裹和出库单都加上 扫描SN
                    Set<OrderTagEnum> orderTagEnumShipmentList = OrderTagEnum.NumToEnum(commitPackBO.getShipmentOrderDTO().getOrderTag());
                    orderTagEnumShipmentList.add(OrderTagEnum.SCAN_SN);
                    commitPackBO.getShipmentOrderDTO().setOrderTag(OrderTagEnum.enumToNum(orderTagEnumShipmentList.stream().collect(Collectors.toList())));

                    Set<OrderTagEnum> orderTagEnumPackList = OrderTagEnum.NumToEnum(commitPackBO.getNewPack().getOrderTag());
                    orderTagEnumPackList.add(OrderTagEnum.SCAN_SN);
                    commitPackBO.getNewPack().setOrderTag(OrderTagEnum.enumToNum(orderTagEnumPackList.stream().collect(Collectors.toList())));
                }
            }

//            if (isCheck) {
//                for (PackSourceCodeParam packSourceCodeParam : param.getSourceCodeParamList()) {
//                    OutSourceCodeDTO outSourceCodeDTO = ConverterUtil.convert(newPack, OutSourceCodeDTO.class);
//                    outSourceCodeDTO.setSkuCode(packSourceCodeParam.getSkuCode());
//                    outSourceCodeDTO.setUpcCode(packSourceCodeParam.getUpcCode());
//                    skuList.stream().filter(a -> Objects.equals(a.getCode(), packSourceCodeParam.getSkuCode()))
//                            .filter(a -> Objects.equals(a.getCargoCode(), packageDTO.getCargoCode()))
//                            .findFirst().ifPresent(a -> outSourceCodeDTO.setSkuName(a.getName()));
//                    outSourceCodeDTO.setScanType(SourceCodeScanTypeEnum.SOURCE_CODE.getCode());
//                    outSourceCodeDTO.setSnCode(packSourceCodeParam.getSourceCode());
//                    outSourceCodeDTO.setQty(new BigDecimal("1"));
//                    outSourceCodeDTO.setId(null);
//                    outSourceCodeDTOList.add(outSourceCodeDTO);
//                }
//            }
            if (!CollectionUtils.isEmpty(sourceCodeSealingTapeDTOList)) {
                sourceCodeSealingTapeDTOList.forEach(a -> a.setExpressNo(newPack.getExpressNo()));
                outSourceCodeDTOList.addAll(sourceCodeSealingTapeDTOList);
            }
            if (!CollectionUtils.isEmpty(outSourceCodeDTOList)) {
                commitPackBO.setOutSourceCodeDTOList(outSourceCodeDTOList);
            }
            //组装复核数据
            PackageCheckDTO packageCheckDTO = buildPackageCheck(newPack, tempPackDetailList, param.getWorkbenchCode(), newPickDetailDTO.getPickCode(), newPickDetailDTO.getBasketNo());
            commitPackBO.setPackageCheckDTO(packageCheckDTO);
            //--订单耗材记录
            List<MaterialUseRecordDTO> materialUseRecordDTOList = buildMaterialUseRecord(newPack, tempPackDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList()), skuList);
            //冷链耗材
            if (!StringUtils.isEmpty(param.getHcUpcCode())) {
                MaterialUseRecordDTO materialUseRecordDTO = this.buildMaterialUseRecordByColdChainBox(newPack, param.getHcUpcCode(), skuList);
                materialUseRecordDTOList.add(materialUseRecordDTO);
            }
            commitPackBO.setMaterialUseRecordDTOList(materialUseRecordDTOList);

            SystemEventDTO systemEventDTO = buildSystemEventDTO(commitPackBO.getNewPack());
            commitPackBO.setSystemEventDTO(systemEventDTO);
            log.info("commitPackBO:{}", JSONUtil.toJsonStr(commitPackBO));
            iRemoteBillContextClient.commitPackBO(commitPackBO);

            List<PackageLogDTO> packageLogDTOList = new ArrayList<>();
            //原包裹日志
            PackageLogDTO packageLogDTO = new PackageLogDTO();
            packageLogDTO.setCargoCode(packageDTO.getCargoCode());
            packageLogDTO.setPackageCode(packageDTO.getPackageCode());
            packageLogDTO.setWarehouseCode(packageDTO.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setOpContent(String.format("包裹完成拆包,单号:%s,新包裹号:%s", packageDTO.getPackageCode(), newPack.getPackageCode()));
            packageLogDTOList.add(packageLogDTO);

            packageLogDTO = new PackageLogDTO();
            packageLogDTO.setCargoCode(newPack.getCargoCode());
            packageLogDTO.setPackageCode(newPack.getPackageCode());
            packageLogDTO.setWarehouseCode(newPack.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setOpContent(String.format("拆包包裹创建,单号:%s", newPack.getPackageCode()));
            packageLogDTOList.add(packageLogDTO);
            iBusinessLogBiz.savePackLogList(packageLogDTOList);
            if (saveShipmentLog.toString().equals("30")) {
                iBusinessLogBiz.saveShipmentLog(packageDTO.getWarehouseCode(),
                        packageDTO.getCargoCode(),
                        packageDTO.getShipmentOrderCode(),
                        CurrentUserHolder.getUserName(),
                        String.format("出库单复核完成,单号:%s", packageDTO.getShipmentOrderCode()));
            }
        } catch (Exception e) {
            log.error("包裹复核异常：e:{}", e.getMessage());
            throw e;
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
        return newPackageCode;
    }

    /**
     * @param pickDTO
     * @param commitPickDetailList
     * @param commitPickList
     * @param packageDTO
     * @param newPack
     * @param pickDetailDTOList
     * @return com.dt.domain.bill.dto.PickDetailDTO
     * <AUTHOR>
     * @describe:
     * @date 2023/8/2 9:56
     */
    private PickDetailDTO buildPickData(PickDTO pickDTO, List<PickDetailDTO> commitPickDetailList, List<PickDTO> commitPickList, PackageDTO packageDTO, PackageDTO newPack, List<PickDetailDTO> pickDetailDTOList) {
        PickDetailDTO newPickDetailDTO;
        if (pickDTO.getPickFlag().equalsIgnoreCase(PickEnum.PickFlagEnum.ORIGIN.getCode())) {
            pickDTO.setPackageQty(pickDTO.getPackageQty() + 1);
            commitPickList.add(pickDTO);
            List<PickDetailDTO> currentPickDetailDTOList = pickDetailDTOList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList());
            currentPickDetailDTOList.forEach(a -> {
                a.setQty(packageDTO.getPackageSkuQty());
                a.setPickQty(packageDTO.getPackageSkuQty());
            });
            commitPickDetailList.addAll(currentPickDetailDTOList);

            newPickDetailDTO = ConverterUtil.convert(currentPickDetailDTOList.get(0), PickDetailDTO.class);
            newPickDetailDTO.setId(null);
            newPickDetailDTO.setVersion(null);
            newPickDetailDTO.setPackageCode(newPack.getPackageCode());
            newPickDetailDTO.setPackageStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
            newPickDetailDTO.setQty(newPack.getPackageSkuQty());
            newPickDetailDTO.setPickQty(newPack.getPackageSkuQty());
            newPickDetailDTO.setCheckQty(newPack.getPackageSkuQty());
        } else {
            pickDTO.setPackageQty(pickDTO.getPackageQty() + 1);
            commitPickList.add(pickDTO);
            List<PickDetailDTO> currentPickDetailDTOList = pickDetailDTOList.stream()
                    .filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList());
            currentPickDetailDTOList.forEach(a -> {
                a.setQty(packageDTO.getPackageSkuQty());
                a.setPickQty(packageDTO.getPackageSkuQty());
            });
            commitPickDetailList.addAll(currentPickDetailDTOList);

            newPickDetailDTO = ConverterUtil.convert(currentPickDetailDTOList.get(0), PickDetailDTO.class);
            newPickDetailDTO.setId(null);
            newPickDetailDTO.setVersion(null);
            newPickDetailDTO.setPackageCode(newPack.getPackageCode());
            newPickDetailDTO.setPackageStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
            newPickDetailDTO.setQty(newPack.getPackageSkuQty());
            newPickDetailDTO.setPickQty(newPack.getPackageSkuQty());
            newPickDetailDTO.setCheckQty(newPack.getPackageSkuQty());
            //获取原始拣选单号和拣选单明细
            PickDetailParam pickDetailParam = new PickDetailParam();
            pickDetailParam.setPackageCode(packageDTO.getPackageCode());
            pickDetailParam.setDetailFlag(PickEnum.PickDetailFlagEnum.ORIGIN.getCode());
            pickDetailParam.setPackageStatus(PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode());
            List<PickDetailDTO> detailClientList = remotePickDetailClient.getList(pickDetailParam);
            if (CollectionUtils.isEmpty(detailClientList)) {
                throw new BaseException(BaseBizEnum.TIP, "聚合拣选单未找到合单原始明细");
            }
            detailClientList.forEach(a -> {
                a.setQty(packageDTO.getPackageSkuQty());
                a.setPickQty(packageDTO.getPackageSkuQty());
            });
            commitPickDetailList.addAll(detailClientList);

            PickDetailDTO newOriginPickDetailDTO = ConverterUtil.convert(detailClientList.get(0), PickDetailDTO.class);
            newOriginPickDetailDTO.setId(null);
            newOriginPickDetailDTO.setVersion(null);
            newOriginPickDetailDTO.setPackageCode(newPack.getPackageCode());
            newOriginPickDetailDTO.setPackageStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
            newOriginPickDetailDTO.setQty(newPack.getPackageSkuQty());
            newOriginPickDetailDTO.setPickQty(newPack.getPackageSkuQty());
            newOriginPickDetailDTO.setCheckQty(newPack.getPackageSkuQty());
            newOriginPickDetailDTO.setExpressNo("");
            commitPickDetailList.add(newOriginPickDetailDTO);
            //原始拣选单号
            PickDTO originPickDTO = remotePickClient.queryPickByPickCode(newOriginPickDetailDTO.getPickCode());
            originPickDTO.setPackageQty(originPickDTO.getPackageQty() + 1);
            commitPickList.add(originPickDTO);
        }
        return newPickDetailDTO;
    }

    /**
     * @param skuBackParamList
     * @param newPackDetailList
     * @param originPackDetailList
     * @param newAllocationList
     * @param originAllocationList
     * @param packageDTO
     * @param newPack
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2023/8/1 14:47
     */
    private void splitPackDetailAndAllocation(List<PackSubExpNoParam> skuBackParamList, List<PackageDetailDTO> newPackDetailList, List<PackageDetailDTO> originPackDetailList, List<AllocationOrderDTO> newAllocationList, List<AllocationOrderDTO> originAllocationList, PackageDTO packageDTO, PackageDTO newPack) {
        for (PackSubExpNoParam packSubExpNoParam : skuBackParamList) {
            List<AllocationOrderDTO> currentAllocationDTOList = originAllocationList.stream()
                    .filter(a -> a.getExpireDate().equals(packSubExpNoParam.getExpireDate()))
                    .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                    .filter(a -> a.getSkuCode().equals(packSubExpNoParam.getSkuCode()))
                    .filter(a -> a.getProductionNo().equalsIgnoreCase(packSubExpNoParam.getProductionNo()))
                    .filter(a -> a.getValidityCode().equalsIgnoreCase(packSubExpNoParam.getValidityCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currentAllocationDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "提交效期异常");
            }
            BigDecimal qty = packSubExpNoParam.getQty();
            for (AllocationOrderDTO allocationOrderDTO : currentAllocationDTOList) {
                String uuID = UUID.randomUUID().toString();
                if (qty.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                //TODO 分配记录用于小于包裹明细记录(同一个包裹同一个商品同一条明细可以占用多个批次，就有多个分配记录)
                if (allocationOrderDTO.getExpQty().compareTo(qty) > 0) {
                    //创建新的分配记录
                    AllocationOrderDTO newAllocationOrderDTO = buildNewAllocationOrderDTO(allocationOrderDTO, newPack, uuID, qty);
                    newAllocationList.add(newAllocationOrderDTO);
                    //修改明细
                    allocationOrderDTO.setPickQty(allocationOrderDTO.getPickQty().subtract(qty));
                    allocationOrderDTO.setRealQty(allocationOrderDTO.getRealQty().subtract(qty));
                    allocationOrderDTO.setExpQty(allocationOrderDTO.getExpQty().subtract(qty));
                    if (allocationOrderDTO.getSplitQty().compareTo(BigDecimal.ZERO) > 0) {
                        allocationOrderDTO.setSplitQty(allocationOrderDTO.getSplitQty().subtract(qty));
                    }
                    //获取当前包裹明细
                    PackageDetailDTO packageDetailDTO = originPackDetailList.stream()
                            .filter(a -> a.getSkuCode().equalsIgnoreCase(allocationOrderDTO.getSkuCode()))
                            .filter(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode()))
                            .filter(a -> a.getPackUid().equalsIgnoreCase(allocationOrderDTO.getPackUid())).findFirst().orElse(null);
                    packageDetailDTO.setExpQty(packageDetailDTO.getExpQty().subtract(qty));
                    packageDetailDTO.setSkuQty(packageDetailDTO.getSkuQty().subtract(qty));
                    packageDetailDTO.setPickQty(packageDetailDTO.getPickQty().subtract(qty));
                    packageDetailDTO.setAssignQty(packageDetailDTO.getAssignQty().subtract(qty));
                    //新生产一个包裹明细
                    PackageDetailDTO newDetail = buildNewPackDetailDTO(qty, newPack, uuID, packageDetailDTO);
                    newPackDetailList.add(newDetail);
                    qty = BigDecimal.ZERO;
                } else {
                    //包裹明细二次拆分
                    //获取当前包裹明细
                    PackageDetailDTO packageDetailDTO = originPackDetailList.stream()
                            .filter(a -> a.getSkuCode().equalsIgnoreCase(allocationOrderDTO.getSkuCode()))
                            .filter(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode()))
                            .filter(a -> a.getPackUid().equalsIgnoreCase(allocationOrderDTO.getPackUid())).findFirst().orElse(null);

                    allocationOrderDTO.setPackageCode(newPack.getPackageCode());

                    if (packageDetailDTO.getExpQty().compareTo(allocationOrderDTO.getExpQty()) > 0) {
                        packageDetailDTO.setExpQty(packageDetailDTO.getExpQty().subtract(allocationOrderDTO.getExpQty()));
                        packageDetailDTO.setSkuQty(packageDetailDTO.getSkuQty().subtract(allocationOrderDTO.getExpQty()));
                        packageDetailDTO.setPickQty(packageDetailDTO.getPickQty().subtract(allocationOrderDTO.getExpQty()));
                        packageDetailDTO.setAssignQty(packageDetailDTO.getAssignQty().subtract(allocationOrderDTO.getExpQty()));
                        //新生产一个包裹明细
                        PackageDetailDTO newDetail = buildNewPackDetailDTO(allocationOrderDTO.getExpQty(), newPack, allocationOrderDTO.getPackUid(), packageDetailDTO);
                        newPackDetailList.add(newDetail);
                    } else {
                        packageDetailDTO.setPackageCode(newPack.getPackageCode());
                        packageDetailDTO.setCheckQty(packageDetailDTO.getPickQty());
                        packageDetailDTO.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                        packageDetailDTO.setCreatedBy(CurrentUserHolder.getUserName());
                        packageDetailDTO.setCreatedTime(System.currentTimeMillis());
                        packageDetailDTO.setUpdatedBy(CurrentUserHolder.getUserName());
                        packageDetailDTO.setUpdatedTime(System.currentTimeMillis());
                    }
                    qty = qty.subtract(allocationOrderDTO.getExpQty());
                }
            }
        }
    }

    /**
     * @param allocationOrderDTO
     * @param newPack
     * @param uuID
     * @param qty
     * @return com.dt.domain.bill.dto.AllocationOrderDTO
     * <AUTHOR>
     * @describe:
     * @date 2023/8/2 9:46
     */
    private AllocationOrderDTO buildNewAllocationOrderDTO(AllocationOrderDTO allocationOrderDTO, PackageDTO newPack, String uuID, BigDecimal qty) {
        AllocationOrderDTO newAllocationOrderDTO = ObjectUtil.cloneByStream(allocationOrderDTO);
        newAllocationOrderDTO.setId(null);
        newAllocationOrderDTO.setVersion(null);
        newAllocationOrderDTO.setAllocationOrderCode(iRemoteSeqRuleClient.findSequence(SeqEnum.ALLOCATION_CODE_000001));
        newAllocationOrderDTO.setPackageCode(newPack.getPackageCode());
        newAllocationOrderDTO.setPackUid(uuID);
        newAllocationOrderDTO.setExpQty(qty);
        newAllocationOrderDTO.setRealQty(qty);
        newAllocationOrderDTO.setPickQty(qty);
        newAllocationOrderDTO.setSplitQty(qty);
        return newAllocationOrderDTO;
    }

    /**
     * @param qty
     * @param newPack
     * @param uuID
     * @return com.dt.domain.bill.dto.PackageDetailDTO
     * <AUTHOR>
     * @describe:
     * @date 2023/8/2 9:42
     */
    private PackageDetailDTO buildNewPackDetailDTO(BigDecimal qty, PackageDTO newPack, String uuID, PackageDetailDTO packageDetailDTO) {
        PackageDetailDTO newDetail = ObjectUtil.cloneByStream(packageDetailDTO);
        newDetail.setId(null);
        newDetail.setVersion(null);
        newDetail.setSkuQty(qty);
        newDetail.setPackUid(uuID);
        newDetail.setExpQty(qty);
        newDetail.setAssignQty(qty);
        newDetail.setPickQty(qty);
        newDetail.setCheckQty(qty);
        newDetail.setCreatedBy(CurrentUserHolder.getUserName());
        newDetail.setCreatedTime(System.currentTimeMillis());
        newDetail.setUpdatedBy(CurrentUserHolder.getUserName());
        newDetail.setUpdatedTime(System.currentTimeMillis());
        newDetail.setPackageCode(newPack.getPackageCode());
        newDetail.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        return newDetail;
    }

    /**
     * 获取当前包裹的分配数据
     *
     * @param packageCode
     * @param waveCode
     * @return
     */
    private List<AllocationOrderDTO> getAllocationOrderByPackAndWaveCode(String packageCode, String waveCode) {
        List<AllocationOrderDTO> allocationOrderDTOList = iRemoteAllocationOrderClient.getPackList(Arrays.asList(packageCode), waveCode);
        if (CollectionUtils.isEmpty(allocationOrderDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包裹未找到库存分配数据"));
        }
        //获取批次信息
        //获取当前批次信息
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(allocationOrderDTOList.get(0).getCargoCode());
        skuLotParam.setCodeList(allocationOrderDTOList.stream().map(AllocationOrderDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
        if (CollectionUtils.isEmpty(skuLotDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "包裹分配批次异常");
        }
        allocationOrderDTOList.forEach(it -> {
            skuLotDTOList.stream().filter(a -> a.getCode().equalsIgnoreCase(it.getSkuLotNo()))
                    .findFirst().ifPresent(a -> {
                        it.setProductionNo(a.getProductionNo());
                        it.setExpireDate(a.getExpireDate());
                        it.setValidityCode(a.getValidityCode());
                    });
        });
        return allocationOrderDTOList;
    }

    @Override
    public String commitB2BPack(PackageCheckDTO packageCheckDTO, PickDTO pickDTO, PackageDTO packageDTO) {
        //原明细数据
        List<PackageDetailDTO> originPackDetails = packageDTO.getListDetail();

        //分配单原始数据
        List<AllocationOrderDTO> originAllocations = getAllocationOrderByPackAndWaveCode(packageDTO.getPackageCode(), packageDTO.getWaveCode());
        //分配单新数据
        List<AllocationOrderDTO> newAllocations = new ArrayList<>();
        List<PackageCheckDetailDTO> packageCheckDetailDTOS = packageCheckDTO.getDetailDTOList();
        //组包包裹明细
        List<PackageDetailDTO> newPackDetails = new ArrayList<>();
        //组包新包裹主表
        PackageDTO newPack = ConverterUtil.convert(packageDTO, PackageDTO.class);
        newPack.setId(null);
        newPack.setVersion(null);
        newPack.setActualPackUpc(packageDTO.getActualPackUpc());
        packageDTO.setActualPackUpc(null);
        newPack.setRealWeight(packageDTO.getRealWeight());
        packageDTO.setRealWeight(null);
        newPack.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        //生成新包裹主编码
        newPack.setPackageCode(iRemoteSeqRuleClient.findSequence(SeqEnum.PACK_CODE_000001));
//        分配包裹明细--TODO --分配逻辑异常 -add by wuxian 2021-03-04
        for (PackageDetailDTO entity : originPackDetails) {
            PackageCheckDetailDTO packageCheckDetailDTO = packageCheckDetailDTOS.stream()
                    .filter(a -> a.getSkuCode().equals(entity.getSkuCode()))
                    .filter(a -> a.getPUid().equals(entity.getId()))
                    .findFirst().orElse(null);
            if (packageCheckDetailDTO == null) {
                continue;
            }
            BigDecimal qty = packageCheckDetailDTO.getQty();
            if (qty.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            //当前包裹明细数量大于提交数量
            String uuID = UUID.randomUUID().toString();
            Boolean isNew = false;
            if (entity.getPickQty().compareTo(qty) > 0) {
                isNew = true;
                entity.setExpQty(entity.getExpQty().subtract(qty));
                entity.setSkuQty(entity.getSkuQty().subtract(qty));
                entity.setPickQty(entity.getPickQty().subtract(qty));
                entity.setAssignQty(entity.getAssignQty().subtract(qty));
                //新生产一个包裹明细
                PackageDetailDTO newDetail = ConverterUtil.convert(entity, PackageDetailDTO.class);
                newDetail.setId(null);
                newDetail.setVersion(null);
                newDetail.setPackUid(uuID);
                newDetail.setSkuQty(qty);
                newDetail.setExpQty(qty);
                newDetail.setAssignQty(qty);

                newDetail.setCreatedBy(CurrentUserHolder.getUserName());
                newDetail.setCreatedTime(System.currentTimeMillis());
                newDetail.setUpdatedBy(CurrentUserHolder.getUserName());
                newDetail.setUpdatedTime(System.currentTimeMillis());

                newDetail.setPickQty(qty);
                newDetail.setCheckQty(qty);
                newDetail.setPackageCode(newPack.getPackageCode());
                newDetail.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                newPackDetails.add(newDetail);

            } else {
                entity.setPackageCode(newPack.getPackageCode());
                entity.setCheckQty(entity.getPickQty());
                entity.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                entity.setCreatedBy(CurrentUserHolder.getUserName());
                entity.setCreatedTime(System.currentTimeMillis());
                entity.setUpdatedBy(CurrentUserHolder.getUserName());
                entity.setUpdatedTime(System.currentTimeMillis());
            }
            //拆分分配记录
            List<AllocationOrderDTO> allocationOrderDTOList = originAllocations.stream()
                    .filter(a -> Objects.equals(a.getPackUid(), entity.getPackUid()))
                    .collect(Collectors.toList());
            for (AllocationOrderDTO allocationOrderDTO : allocationOrderDTOList) {
                if (qty.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                if (allocationOrderDTO.getExpQty().compareTo(qty) > 0) {
                    //创建新的分配记录
                    AllocationOrderDTO newAllocationOrderDTO = ConverterUtil.convert(allocationOrderDTO, AllocationOrderDTO.class);
                    newAllocationOrderDTO.setId(null);
                    newAllocationOrderDTO.setVersion(null);
                    newAllocationOrderDTO.setAllocationOrderCode(iRemoteSeqRuleClient.findSequence(SeqEnum.ALLOCATION_CODE_000001));
                    newAllocationOrderDTO.setPackageCode(newPack.getPackageCode());
                    if (isNew) {
                        newAllocationOrderDTO.setPackUid(uuID);
                    }
                    newAllocationOrderDTO.setExpQty(qty);
                    newAllocationOrderDTO.setRealQty(qty);
                    newAllocationOrderDTO.setPickQty(qty);
                    newAllocationOrderDTO.setSplitQty(qty);
                    newAllocations.add(newAllocationOrderDTO);
                    //修改明细
                    allocationOrderDTO.setPickQty(allocationOrderDTO.getPickQty().subtract(qty));
                    allocationOrderDTO.setRealQty(allocationOrderDTO.getRealQty().subtract(qty));
                    allocationOrderDTO.setExpQty(allocationOrderDTO.getExpQty().subtract(qty));
                    allocationOrderDTO.setSplitQty(allocationOrderDTO.getSplitQty().subtract(qty));
                    qty = BigDecimal.ZERO;
                } else {
                    allocationOrderDTO.setPackageCode(newPack.getPackageCode());
                    if (isNew) {
                        allocationOrderDTO.setPackUid(uuID);
                    }
                    qty = qty.subtract(allocationOrderDTO.getExpQty());
                }
            }
        }
        log.info("originAllocations:{}", JSONUtil.toJsonStr(originAllocations));
        log.info("newAllocations:{}", JSONUtil.toJsonStr(newAllocations));
//        --TODO --分配逻辑异常-add by wuxian 2021-03-04

        //修正包裹数据
        packageDTO.setPackageStruct(analysisPackageStruct(originPackDetails.stream()
                .filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList())));
        packageDTO.setListDetail(originPackDetails);
        packageDTO.setPackageSkuQty(originPackDetails.stream()
                .filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode()))
                .map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        packageDTO.setSkuTypeQty((int) originPackDetails.stream()
                .filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode()))
                .map(PackageDetailDTO::getSkuCode).distinct().count());

        List<PackageDetailDTO> tempList = new ArrayList<>();
        tempList.addAll(newPackDetails);
        tempList.addAll(originPackDetails.stream()
                .filter(a -> Objects.equals(a.getPackageCode(), newPack.getPackageCode())).collect(Collectors.toList()));
        newPack.setPackageStruct(analysisPackageStruct(tempList));

        newPack.setListDetail(newPackDetails);
        newPack.setPackageSkuQty(newPackDetails.stream()
                .map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add)
                .add(originPackDetails.stream()
                        .filter(a -> Objects.equals(a.getPackageCode(), newPack.getPackageCode()))
                        .map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add)));
        newPack.setSkuTypeQty((int) tempList.stream()
                .map(PackageDetailDTO::getSkuCode).distinct().count());
        newPack.setOriginPackageCode(packageDTO.getPackageCode());

        StringBuilder saveShipmentLog = new StringBuilder();
        //回写出库订单数据
        ShipmentOrderDTO shipmentOrderDTO = modifyShipment(newPack.getShipmentOrderCode(), tempList, saveShipmentLog);

        //回写拣选单数据
        pickDTO.setPackageQty(pickDTO.getPackageQty() + 1);
        List<PickDetailDTO> pickDetailDTOList = pickDTO.getDetailList().stream()
                .filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList());
        pickDetailDTOList.forEach(a -> {
            a.setQty(packageDTO.getPackageSkuQty());
            a.setPickQty(packageDTO.getPackageSkuQty());
        });
        PickDetailDTO pickDetailDTO = ConverterUtil.convert(pickDetailDTOList.get(0), PickDetailDTO.class);
        pickDetailDTO.setId(null);
        pickDetailDTO.setVersion(null);
        pickDetailDTO.setPackageCode(newPack.getPackageCode());
        pickDetailDTO.setPackageStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        pickDetailDTO.setQty(newPack.getPackageSkuQty());
        pickDetailDTO.setPickQty(newPack.getPackageSkuQty());
        pickDetailDTO.setCheckQty(newPack.getPackageSkuQty());


        PackageMaterialDTO packageMaterialDTO = iRemotePackageMaterialClient.queryByUpcCode(newPack.getCargoCode(), newPack.getActualPackUpc());
        newPack.setActualPackNum(1);
        newPack.setActualPackWeight(packageMaterialDTO.getGrossWeight());

        CarrierDTO carrierDTO = iRemoteCarrierClient.queryByCode(packageDTO.getCarrierCode());
        if (carrierDTO != null && carrierDTO.getIsHttp().equals(IsHttpEnum.ENABLE.getValue())) {
            //TODO 组装快递参数
            newPack.setExpressNo("");
            BigDecimal weight = weightBiz.weight(MaterialCalculateParam.builder()
                    .warehouseCode(newPack.getWarehouseCode())
                    .cargoCode(newPack.getCargoCode())
                    .is4PL(OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.FOUR_TAG) ? Is4PLEnum.YES : Is4PLEnum.NO)
                    .appointMaterialWeight(packageMaterialDTO.getGrossWeight())
                    .skuDetailList(tempList.stream()
                            .filter(it -> it.getIsPre().equals(PackIsPreEnum.NORMAL.getCode()))
                            .map(it -> MaterialCalculateParam.SkuDetail.builder()
                                    .skuCode(it.getSkuCode())
                                    .quantity(it.getSkuQty())
                                    .build()).collect(Collectors.toList()))
                    .build());
//            ExpressResultDTO expressResultDTO = iRemoteExpressClient.getExpressResult(shipmentOrderDTO.getShipmentOrderCode(), iRemoteExpressClient.getExpressParam(shipmentOrderDTO, carrierDTO, newPack, tempList));
            wmsTenantHelper.setTenantId(shipmentOrderDTO.getWarehouseCode(), shipmentOrderDTO.getCargoCode());
            ExpressResultDTO expressResultDTO = iRemoteExpressClient.getExpressResult(shipmentOrderDTO.getShipmentOrderCode(), iRemoteExpressClient.getExpressParamV2(shipmentOrderDTO, carrierDTO, newPack, tempList, weight));
            if (StringUtils.isEmpty(expressResultDTO)) {
                throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
            }
            if (!expressResultDTO.getSuccess()) {
                String errorMessage = StringUtils.isEmpty(expressResultDTO.getMessage()) ? "未获取到快递单号" : expressResultDTO.getMessage();
                //[\u4E00-\u9FA5]是unicode2的中文区间
                Matcher matcher = pattern.matcher(errorMessage);
                //提取中文字符
                errorMessage = matcher.replaceAll("");
                errorMessage = StringUtils.isEmpty(errorMessage) ? "未获取到快递单号" : errorMessage;
                throw new BaseException(BaseBizEnum.TIP, errorMessage);
            }
            if (CollectionUtils.isEmpty(expressResultDTO.getWaybill_apply_info())) {
                throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
            }
            if (StringUtils.isEmpty(expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code())) {
                throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
            }
            String billNo = checkBillNoBiz.checkOutboundExpressNoEffective(carrierDTO.getCode(), expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code());
            if (!StringUtils.isEmpty(billNo)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拉取运单号,系统已存在有效状态的运单%s包裹，不允许拆包", expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code()));
            }
            newPack.setExpressNo(expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code());
            newPack.setExpressBranch(expressResultDTO.getWaybill_apply_info().get(0).getExpressBranchCode());
            newPack.setExpressBranchName(expressResultDTO.getWaybill_apply_info().get(0).getExpressBranchName());
        } else {
//            String expressNo = packageDTO.getExpressNo() + "-" + packageCheckDTO.getBoxNo();
            String expressNo = packageDTO.getExpressNo() + "-" + newPack.getPackageCode().substring(newPack.getPackageCode().length() - 4);
            String billNo = checkBillNoBiz.checkOutboundExpressNoEffective(carrierDTO.getCode(), expressNo);
            if (!StringUtils.isEmpty(billNo)) {
                throw new BaseException(BaseBizEnum.TIP, "非电子面单,系统已存在有效状态的运单包裹，不允许拆包");
            }
            newPack.setExpressNo(expressNo);
        }
        pickDetailDTO.setExpressNo(newPack.getExpressNo());
        pickDetailDTOList.add(pickDetailDTO);

        packageCheckDTO.setStatus(PackCheckEnum.STATUS.COMPLETE_STATUS.getCode());
        packageCheckDTO.getDetailDTOList().forEach(a -> {
            a.setStatus(PackCheckEnum.STATUS.COMPLETE_STATUS.getCode());
            a.setCheckQty(a.getQty());
        });

        //包裹重量计算
        List<String> skuCodeList = tempList.stream()
                .filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode()))
                .flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList());
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(packageDTO.getCargoCode());
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuList = iRemoteSkuClient.getList(skuParam);
        //包裹理论重量信息
        newPack.setWeight(
                tempList.stream()
                        .filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode()))
                        .flatMap(a -> {
                            SkuDTO sku = skuList.stream().filter(b -> b.getCode().equals(a.getSkuCode())).findAny().get();
                            return Stream.of(a.getSkuQty().multiply(sku.getGrossWeight()));
                        }).reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        //包裹理论体积信息
        newPack.setVolume(
                tempList.stream()
                        .filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode()))
                        .flatMap(a -> {
                            SkuDTO sku = skuList.stream().filter(b -> b.getCode().equals(a.getSkuCode())).findAny().get();
                            BigDecimal _bigDecimal = a.getSkuQty().multiply(sku.getLength().multiply(sku.getWidth()).multiply(sku.getHeight()));
                            if (_bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
                                _bigDecimal = sku.getVolume().multiply(a.getSkuQty());
                            }
                            return Stream.of(_bigDecimal == null ? BigDecimal.ZERO : _bigDecimal);
                        }).reduce(BigDecimal.ZERO, BigDecimal::add));
        iRemotePackageClient.handlePackageVolume(newPack);

        //校验重量
        iCheckPackWeightAndVolumeBiz.checkPackageWeightAndVolumeNew(newPack, tempList, CommonConstantUtil.WMS_WEB_WEIGHT);

        CommitPackBO commitPackBO = new CommitPackBO();
        commitPackBO.setOriginAllocations(originAllocations);
        commitPackBO.setNewAllocations(newAllocations);
        commitPackBO.setOriginPack(packageDTO);
        newPack.setBoxNo(packageCheckDTO.getBoxNo());
        newPack.setCheckCompleteDate(System.currentTimeMillis());

        //计算体积重
        iCheckPackWeightAndVolumeBiz.calculationVolumetricWeight(newPack);

        commitPackBO.setNewPack(newPack);
        commitPackBO.setPickDTOList(Arrays.asList(pickDTO));
        commitPackBO.setPickDetailDTOList(pickDetailDTOList);
        commitPackBO.setShipmentOrderDTO(shipmentOrderDTO);
        //组装复核数据
        packageCheckDTO.setPackageCode(newPack.getPackageCode());
        packageCheckDTO.setExpressNo(newPack.getExpressNo());
        packageCheckDTO.setActualPackUpc(newPack.getActualPackUpc());
        packageCheckDTO.setCheckQty(packageCheckDTO.getQty());
        commitPackBO.setPackageCheckDTO(packageCheckDTO);

        String key = pickDTO.getWarehouseCode() + "" + pickDTO.getPickCode() + "" + packageDTO.getPackageCode();
        RLock lock = redissonClient.getLock("dt_wms_back_pack_submit_lock:" + key);
        try {
            lock.lock(10, TimeUnit.SECONDS);
            SystemEventDTO systemEventDTO = buildSystemEventDTO(commitPackBO.getNewPack());
            commitPackBO.setSystemEventDTO(systemEventDTO);
            iRemoteBillContextClient.commitPackBO(commitPackBO);
            List<PackageLogDTO> packageLogDTOS = new ArrayList<>();
            //原包裹日志
            PackageLogDTO packageLogDTO = new PackageLogDTO();
            packageLogDTO.setCargoCode(packageDTO.getCargoCode());
            packageLogDTO.setPackageCode(packageDTO.getPackageCode());
            packageLogDTO.setWarehouseCode(packageDTO.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setOpContent(String.format("包裹完成拆包,单号:%s,新包裹号:%s", packageDTO.getPackageCode(), newPack.getPackageCode()));
            packageLogDTOS.add(packageLogDTO);

            packageLogDTO = new PackageLogDTO();
            packageLogDTO.setCargoCode(newPack.getCargoCode());
            packageLogDTO.setPackageCode(newPack.getPackageCode());
            packageLogDTO.setWarehouseCode(newPack.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setOpContent(String.format("拆包包裹创建,单号:%s", newPack.getPackageCode()));
            packageLogDTOS.add(packageLogDTO);
            iBusinessLogBiz.savePackLogList(packageLogDTOS);
            if (saveShipmentLog.toString().equals("30")) {
                iBusinessLogBiz.saveShipmentLog(packageDTO.getWarehouseCode(),
                        packageDTO.getCargoCode(),
                        packageDTO.getShipmentOrderCode(),
                        CurrentUserHolder.getUserName(),
                        String.format("出库单复核完成,单号:%s", packageDTO.getShipmentOrderCode()));
            }
        } catch (Exception e) {
            log.error("包裹复核异常：e:{}", e.getMessage());
            throw e;
        } finally {
            lock.unlock();
        }
        return newPack.getPackageCode();
    }

    @Override
    public String commitB2CPrePack(PackSubmitParam param, PickDTO pickDTO, List<PickDetailDTO> pickDetailDTOList, PackageDTO packageDTO) throws Exception {
        String key = packageDTO.getWarehouseCode() + "" + pickDTO.getPickCode() + "" + packageDTO.getPackageCode();
        RLock lock = redissonClient.getLock("dt_wms_back_pack_submit_lock:" + key);
        Boolean tryLock = false;
        String newPackageCode = "";
        try {
            tryLock = lock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "当前有包裹正在组包");
            }
            PackSubmitParam sealingPckSubmitParam = ObjectUtil.cloneByStream(param);
            //前端提交数据
            List<PackSubExpNoParam> skuBackParamList = ObjectUtil.cloneByStream(param.getSkuBackParamList());
            //分配单原始数据
            List<AllocationOrderDTO> originAllocationList = getAllocationOrderByPackAndWaveCode(packageDTO.getPackageCode(), packageDTO.getWaveCode());
            //分配单新数据
            List<AllocationOrderDTO> newAllocationList = new ArrayList<>();
            //组包包裹明细
            List<PackageDetailDTO> newPackDetailList = new ArrayList<>();
            PackageDTO newPack = ConverterUtil.convert(packageDTO, PackageDTO.class);
            newPack.setId(null);
            newPack.setVersion(null);
            newPack.setExpressNo(null);
            newPack.setActualPackUpc(packageDTO.getActualPackUpc());
            packageDTO.setActualPackUpc(null);
            newPack.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
            newPack.setPackageCode(iRemoteSeqRuleClient.findSequence(SeqEnum.PACK_CODE_000001));
            //预包拆分分配和包裹明细
            splitPrePackDetailAndAllocation(skuBackParamList, newPackDetailList, newAllocationList, originAllocationList, packageDTO, newPack);
            //修正包裹数据(预包+剩余)
            //修正旧包裹的结构和数量
            List<PackageDetailDTO> oldPackageStructDetailList = packageDTO.getListDetail().stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode()))
                    .filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList());
            packageDTO.setPackageStruct(analysisPackageStruct(oldPackageStructDetailList));

            packageDTO.setPackageSkuQty(oldPackageStructDetailList.stream().map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            packageDTO.setSkuTypeQty((int) oldPackageStructDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().count());
            //新包裹所有的明细(预包+剩余)
            //修正新包裹的结构和数量
            List<PackageDetailDTO> newPackageStructDetailList = new ArrayList<>();
            //新明细
            newPackageStructDetailList.addAll(newPackDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList()));
            //旧明细
            newPackageStructDetailList.addAll(packageDTO.getListDetail().stream().filter(a -> Objects.equals(a.getPackageCode(), newPack.getPackageCode())).filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList()));
            newPack.setPackageStruct(analysisPackageStruct(newPackageStructDetailList));
            newPack.setListDetail(newPackDetailList);
            newPack.setPackageSkuQty(newPackageStructDetailList.stream().map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            newPack.setSkuTypeQty((int) newPackageStructDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().count());
            newPack.setOriginPackageCode(packageDTO.getPackageCode());

            //回写出库订单数据
            List<PackageDetailDTO> newPackageDetailToShipment = new ArrayList<>();
            newPackageDetailToShipment.addAll(newPackDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList()));
            newPackageDetailToShipment.addAll(packageDTO.getListDetail().stream().filter(a -> a.getPackageCode().equalsIgnoreCase(newPack.getPackageCode())).filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList()));
            StringBuilder saveShipmentLog = new StringBuilder();
            ShipmentOrderDTO shipmentOrderDTO = modifyShipment(newPack.getShipmentOrderCode(), newPackageDetailToShipment, saveShipmentLog);

            //回写拣选单数据 -------------
            List<PickDetailDTO> commitPickDetailList = new ArrayList<>();
            List<PickDTO> commitPickList = new ArrayList<>();
            PickDetailDTO newPickDetailDTO = buildPickData(pickDTO, commitPickDetailList, commitPickList, packageDTO, newPack, pickDetailDTOList);
            //回写拣选单数据 -------------
            PackageMaterialDTO packageMaterialDTO = iRemotePackageMaterialClient.queryByUpcCode(newPack.getCargoCode(), newPack.getActualPackUpc());
            newPack.setActualPackNum(1);
            newPack.setActualPackWeight(packageMaterialDTO.getGrossWeight());
            //校验封口贴
            List<OutSourceCodeDTO> sourceCodeSealingTapeDTOList = new ArrayList<>();
            List<String> skuCodeSourceCodeSealingTapeList = newPackageStructDetailList.stream()
                    .filter(a -> a.getIsPre().equals(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equals(PackIsPreEnum.LAST.getCode()))
                    .map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
            SkuParam skuNewParam = new SkuParam();
            skuNewParam.setCodeList(skuCodeSourceCodeSealingTapeList);
            skuNewParam.setCargoCode(packageDTO.getCargoCode());
            List<SkuDTO> skuNewList = iRemoteSkuClient.getList(skuNewParam);
            //扫码记录
            List<OutSourceCodeDTO> outSourceCodeDTOList = new ArrayList<>();

            if (!CollectionUtils.isEmpty(skuNewList)) {
                sourceCodeSealingTapeDTOList = this.checkSealingTape(sealingPckSubmitParam, newPack, skuNewList);
            }
            //计算理论体积和理论重量
            iCheckPackWeightAndVolumeBiz.calculationVolumetricAndWeight(newPack, newPackageStructDetailList, skuNewList);

            CarrierDTO carrierDTO = iRemoteCarrierClient.queryByCode(packageDTO.getCarrierCode());
            if (carrierDTO != null && carrierDTO.getIsHttp().equals(IsHttpEnum.ENABLE.getValue())) {
                //TODO 组装快递参数
                newPack.setExpressNo("");
                BigDecimal weight = weightBiz.weight(MaterialCalculateParam.builder()
                        .warehouseCode(newPack.getWarehouseCode())
                        .cargoCode(newPack.getCargoCode())
                        .is4PL(OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.FOUR_TAG) ? Is4PLEnum.YES : Is4PLEnum.NO)
                        .appointMaterialWeight(packageMaterialDTO.getGrossWeight())
                        .skuDetailList(newPackageStructDetailList.stream()
                                .filter(it -> it.getIsPre().equals(PackIsPreEnum.PRE.getCode()) || it.getIsPre().equals(PackIsPreEnum.LAST.getCode()))
                                .map(it -> MaterialCalculateParam.SkuDetail.builder()
                                        .skuCode(it.getSkuCode())
                                        .quantity(it.getSkuQty())
                                        .build()).collect(Collectors.toList()))
                        .build());
                wmsTenantHelper.setTenantId(shipmentOrderDTO.getWarehouseCode(), shipmentOrderDTO.getCargoCode());
                ExpressResultDTO expressResultDTO = iRemoteExpressClient.getExpressResult(shipmentOrderDTO.getShipmentOrderCode(),
                        iRemoteExpressClient.getExpressParamV2(shipmentOrderDTO, carrierDTO, newPack, newPackageStructDetailList, weight));
                if (StringUtils.isEmpty(expressResultDTO)) {
                    throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
                }
                if (!expressResultDTO.getSuccess()) {
                    String errorMessage = StringUtils.isEmpty(expressResultDTO.getMessage()) ? "未获取到快递单号" : expressResultDTO.getMessage();
                    //[\u4E00-\u9FA5]是unicode2的中文区间
                    Matcher matcher = pattern.matcher(errorMessage);
                    //提取中文字符
                    errorMessage = matcher.replaceAll("");
                    errorMessage = StringUtils.isEmpty(errorMessage) ? "未获取到快递单号" : errorMessage;
                    throw new BaseException(BaseBizEnum.TIP, errorMessage);
                }
                if (CollectionUtils.isEmpty(expressResultDTO.getWaybill_apply_info())) {
                    throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
                }
                if (StringUtils.isEmpty(expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code())) {
                    throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
                }
                //校验运单号是否重复
                String billNo = checkBillNoBiz.checkOutboundExpressNoEffective(carrierDTO.getCode(), expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code());
                if (!StringUtils.isEmpty(billNo)) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("拉取运单号,系统已存在有效状态的运单%s包裹，不允许拆包", expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code()));
                }
                newPack.setExpressNo(expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code());
                newPack.setExpressBranch(expressResultDTO.getWaybill_apply_info().get(0).getExpressBranchCode());
                newPack.setExpressBranchName(expressResultDTO.getWaybill_apply_info().get(0).getExpressBranchName());
            } else {
                String expressNo = packageDTO.getExpressNo() + "-" + newPack.getPackageCode().substring(newPack.getPackageCode().length() - 4);
                String billNo = checkBillNoBiz.checkOutboundExpressNoEffective(carrierDTO.getCode(), expressNo);
                if (!StringUtils.isEmpty(billNo)) {
                    throw new BaseException(BaseBizEnum.TIP, "非电子面单,系统已存在有效状态的运单包裹，不允许拆包");
                }
                newPack.setExpressNo(expressNo);
            }
            newPickDetailDTO.setExpressNo(newPack.getExpressNo());
            commitPickDetailList.forEach(a -> {
                if (StringUtils.isEmpty(a.getExpressNo())) {
                    a.setExpressNo(newPack.getExpressNo());
                }
            });
            commitPickDetailList.add(newPickDetailDTO);
            //溯源码
            CommitPackBO commitPackBO = new CommitPackBO();
            commitPackBO.setOriginAllocations(originAllocationList);
            commitPackBO.setNewAllocations(newAllocationList);
            commitPackBO.setOriginPack(packageDTO);
            newPack.setCheckCompleteDate(System.currentTimeMillis());
            //--订单耗材记录
            List<MaterialUseRecordDTO> materialUseRecordDTOList = buildMaterialUseRecord(newPack, newPackageStructDetailList.stream().filter(a -> a.getIsPre().equals(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equals(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList()), skuNewList);
            //冷链耗材
            if (!StringUtils.isEmpty(param.getHcUpcCode())) {
                MaterialUseRecordDTO materialUseRecordDTO = this.buildMaterialUseRecordByColdChainBox(newPack, param.getHcUpcCode(), skuNewList);
                materialUseRecordDTOList.add(materialUseRecordDTO);
            }
            commitPackBO.setMaterialUseRecordDTOList(materialUseRecordDTOList);

            newPackageCode = newPack.getPackageCode();
            //计算体积重
            iCheckPackWeightAndVolumeBiz.calculationVolumetricWeight(newPack);

            commitPackBO.setNewPack(newPack);
            commitPackBO.setPickDTOList(commitPickList);
            commitPackBO.setPickDetailDTOList(commitPickDetailList);
            commitPackBO.setShipmentOrderDTO(shipmentOrderDTO);

//            if (isCheck) {
//                for (PackSourceCodeParam packSourceCodeParam : param.getSourceCodeParamList()) {
//                    OutSourceCodeDTO outSourceCodeDTO = ConverterUtil.convert(newPack, OutSourceCodeDTO.class);
//                    outSourceCodeDTO.setSkuCode(packSourceCodeParam.getSkuCode());
//                    outSourceCodeDTO.setUpcCode(packSourceCodeParam.getUpcCode());
//                    skuNewList.stream()
//                            .filter(a -> Objects.equals(a.getCode(), packSourceCodeParam.getSkuCode()))
//                            .filter(a -> Objects.equals(a.getCargoCode(), packageDTO.getCargoCode()))
//                            .findFirst().ifPresent(a -> outSourceCodeDTO.setSkuName(a.getName()));
//                    outSourceCodeDTO.setScanType(SourceCodeScanTypeEnum.SOURCE_CODE.getCode());
//                    outSourceCodeDTO.setSnCode(packSourceCodeParam.getSourceCode());
//                    outSourceCodeDTO.setQty(new BigDecimal("1"));
//                    outSourceCodeDTO.setId(null);
//                    outSourceCodeDTOList.add(outSourceCodeDTO);
//                }
//            }
            if (!CollectionUtils.isEmpty(sourceCodeSealingTapeDTOList)) {
                sourceCodeSealingTapeDTOList.forEach(a -> a.setExpressNo(newPack.getExpressNo()));
                outSourceCodeDTOList.addAll(sourceCodeSealingTapeDTOList);
            }
            // SN  check
            if (skuNewList.stream().anyMatch(skuDTO -> skuDTO.getSNMgmtOutNeed())) {
                if (CollectionUtils.isEmpty(param.getSNCodeParamList())) {
                    throw new BaseException(BaseBizEnum.TIP, "当前包裹有需要扫描SN,请核查");
                }
                List<OutSourceCodeDTO> snOutSourceCodeDTOList = this.checkSN(skuNewList, packageDTO, param);
                if (!CollectionUtils.isEmpty(snOutSourceCodeDTOList)) {
                    outSourceCodeDTOList.addAll(snOutSourceCodeDTOList);
                    //包裹和出库单都加上 扫描SN
                    Set<OrderTagEnum> orderTagEnumShipmentList = OrderTagEnum.NumToEnum(commitPackBO.getShipmentOrderDTO().getOrderTag());
                    orderTagEnumShipmentList.add(OrderTagEnum.SCAN_SN);
                    commitPackBO.getShipmentOrderDTO().setOrderTag(OrderTagEnum.enumToNum(orderTagEnumShipmentList.stream().collect(Collectors.toList())));

                    Set<OrderTagEnum> orderTagEnumPackList = OrderTagEnum.NumToEnum(commitPackBO.getNewPack().getOrderTag());
                    orderTagEnumPackList.add(OrderTagEnum.SCAN_SN);
                    commitPackBO.getNewPack().setOrderTag(OrderTagEnum.enumToNum(orderTagEnumPackList.stream().collect(Collectors.toList())));
                }
            }
            if (!CollectionUtils.isEmpty(outSourceCodeDTOList)) {
                commitPackBO.setOutSourceCodeDTOList(outSourceCodeDTOList);
            }

            //组装复核数据
            PackageCheckDTO packageCheckDTO = buildPackageCheck(newPack, newPackageStructDetailList, param.getWorkbenchCode(), newPickDetailDTO.getPickCode(), newPickDetailDTO.getBasketNo());
            commitPackBO.setPackageCheckDTO(packageCheckDTO);

            log.info("commitPackPreBO:{}", JSONUtil.toJsonStr(commitPackBO));
            SystemEventDTO systemEventDTO = buildSystemEventDTO(commitPackBO.getNewPack());
            commitPackBO.setSystemEventDTO(systemEventDTO);
//            //----------------------
//            CommitPackBO cloneByStream = ObjectUtil.cloneByStream(commitPackBO);
//
//            List<PackageDetailDTO> oldPackageDetailDTOList = new ArrayList<>();
//            oldPackageDetailDTOList.addAll(cloneByStream.getOriginPack().getListDetail().stream().filter(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode())).collect(Collectors.toList()));
//            oldPackageDetailDTOList.addAll(cloneByStream.getNewPack().getListDetail().stream().filter(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode())).collect(Collectors.toList()));
//            log.info("oldPackageDetailDTOList:{}", JSONUtil.toJsonStr(oldPackageDetailDTOList));
//
//            List<PackageDetailDTO> newPackageDetailDTOList = new ArrayList<>();
//            newPackageDetailDTOList.addAll(cloneByStream.getOriginPack().getListDetail().stream().filter(a -> a.getPackageCode().equalsIgnoreCase(newPack.getPackageCode())).collect(Collectors.toList()));
//            newPackageDetailDTOList.addAll(cloneByStream.getNewPack().getListDetail().stream().filter(a -> a.getPackageCode().equalsIgnoreCase(newPack.getPackageCode())).collect(Collectors.toList()));
//            log.info("newPackageDetailDTOList:{}", JSONUtil.toJsonStr(newPackageDetailDTOList));
//
//            List<AllocationOrderDTO> oldAllocationOrderDTOList = new ArrayList<>();
//            oldAllocationOrderDTOList.addAll(cloneByStream.getOriginAllocations().stream().filter(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode())).collect(Collectors.toList()));
//            oldAllocationOrderDTOList.addAll(cloneByStream.getNewAllocations().stream().filter(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode())).collect(Collectors.toList()));
//            log.info("oldAllocationOrderDTOList:{}", JSONUtil.toJsonStr(oldAllocationOrderDTOList));
//
//            List<AllocationOrderDTO> newAllocationOrderDTOList = new ArrayList<>();
//            newAllocationOrderDTOList.addAll(cloneByStream.getOriginAllocations().stream().filter(a -> a.getPackageCode().equalsIgnoreCase(newPack.getPackageCode())).collect(Collectors.toList()));
//            newAllocationOrderDTOList.addAll(cloneByStream.getNewAllocations().stream().filter(a -> a.getPackageCode().equalsIgnoreCase(newPack.getPackageCode())).collect(Collectors.toList()));
//            log.info("newAllocationOrderDTOList:{}", JSONUtil.toJsonStr(newAllocationOrderDTOList));
//
//            //-------------------------
            iRemoteBillContextClient.commitPackBO(commitPackBO);
            List<PackageLogDTO> packageLogDTOS = new ArrayList<>();
            //原包裹日志
            PackageLogDTO packageLogDTO = new PackageLogDTO();
            packageLogDTO.setCargoCode(packageDTO.getCargoCode());
            packageLogDTO.setPackageCode(packageDTO.getPackageCode());
            packageLogDTO.setWarehouseCode(packageDTO.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setOpContent(String.format("包裹完成拆包,单号:%s,新包裹号:%s", packageDTO.getPackageCode(), newPack.getPackageCode()));
            packageLogDTOS.add(packageLogDTO);

            packageLogDTO = new PackageLogDTO();
            packageLogDTO.setCargoCode(newPack.getCargoCode());
            packageLogDTO.setPackageCode(newPack.getPackageCode());
            packageLogDTO.setWarehouseCode(newPack.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setOpContent(String.format("拆包包裹创建,单号:%s", newPack.getPackageCode()));
            packageLogDTOS.add(packageLogDTO);
            iBusinessLogBiz.savePackLogList(packageLogDTOS);
            if (saveShipmentLog.toString().equals("30")) {
                iBusinessLogBiz.saveShipmentLog(packageDTO.getWarehouseCode(),
                        packageDTO.getCargoCode(),
                        packageDTO.getShipmentOrderCode(),
                        CurrentUserHolder.getUserName(),
                        String.format("出库单复核完成,单号:%s", packageDTO.getShipmentOrderCode()));
            }
        } catch (Exception e) {
            log.error("包裹(预包)复核异常：e:{}", e.getMessage());
            throw e;
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
        return newPackageCode;
    }

    /**
     * @param skuBackParamList
     * @param newPackDetailList
     * @param newAllocationList
     * @param originAllocationList
     * @param packageDTO
     * @param newPack
     * @return void
     * <AUTHOR>
     * @describe: 预包拆分批次信息
     * @date 2023/8/2 13:39
     */
    private void splitPrePackDetailAndAllocation(List<PackSubExpNoParam> skuBackParamList, List<PackageDetailDTO> newPackDetailList, List<AllocationOrderDTO> newAllocationList, List<AllocationOrderDTO> originAllocationList, PackageDTO packageDTO, PackageDTO newPack) {
        List<PackageDetailDTO> originPackDetailList = packageDTO.getListDetail();
        List<String> preSkuCodeList = originPackDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode())).map(PackageDetailDTO::getSkuCode).collect(Collectors.toList());
        //包裹预包商品和剩余商品拆分 - 有分配记录
        List<PackageDetailDTO> originPrePackDetailList = originPackDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList());
        //原始商品明细 --无分配记录
        List<PackageDetailDTO> originNormalPackDetailList = originPackDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
        //预包商品对应的子商品明细 --有分配记录
        List<PackageDetailDTO> originPreChildPackDetailList = originPackDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE_CHILD.getCode())).collect(Collectors.toList());
        //预包商品对应的子商品明细分配记录
        List<AllocationOrderDTO> originPreChildAllocationOrderDTOList = originAllocationList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(AllocationIsPreEnum.PRE_CHILD.getCode())).collect(Collectors.toList());
        //=================================================================================
        List<String> allocationPreCodeList = Arrays.asList(AllocationIsPreEnum.PRE.getCode(), AllocationIsPreEnum.LAST.getCode());
        //-------------------------只操作预包和剩余商品明细--------------------------------------
        for (PackSubExpNoParam packSubExpNoParam : skuBackParamList) {
            List<AllocationOrderDTO> currentAllocationDTOList = originAllocationList.stream()
                    .filter(a -> a.getSkuCode().equals(packSubExpNoParam.getSkuCode()))
                    .filter(a -> a.getExpireDate().equals(packSubExpNoParam.getExpireDate()))
                    .filter(a -> allocationPreCodeList.contains(a.getIsPre()))
                    .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                    .filter(a -> a.getProductionNo().equalsIgnoreCase(packSubExpNoParam.getProductionNo())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currentAllocationDTOList)) {
                log.info("packSubExpNoParam-error:{}", JSONUtil.toJsonStr(packSubExpNoParam));
                throw new BaseException(BaseBizEnum.TIP, "提交效期异常");
            }
            //预包商品
            Boolean isPreSku = false;
            Map<String, BigDecimal> skuNeedQtyMap = new HashMap<>();
            if (preSkuCodeList.contains(packSubExpNoParam.getSkuCode())) {
                isPreSku = true;
                //获取预报条码的库存结构
                skuNeedQtyMap = getSkuNeedQty(packageDTO.getCargoCode(), packSubExpNoParam.getSkuCode());
                if (CollectionUtils.isEmpty(skuNeedQtyMap)) {
                    throw new BaseException(BaseBizEnum.TIP, "未找到预包条码的子商品数据");
                }
            }
            BigDecimal qty = packSubExpNoParam.getQty();
            for (AllocationOrderDTO allocationOrderDTO : currentAllocationDTOList) {
                String uuID = UUID.randomUUID().toString();
                if (qty.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                List<PreSkuLotDTO> preSkuLotDTOList = new ArrayList<>();
                List<AllocationOrderDTO> originSplitPreChildAllocationOrderDTOList = new ArrayList<>();
                if (isPreSku) {
                    preSkuLotDTOList = queryPreSkuLotDTO(packageDTO.getCargoCode(), allocationOrderDTO.getSkuCode(), allocationOrderDTO.getSkuLotNo());
                    List<String> preChildSkuLotCodeList = preSkuLotDTOList.stream().map(PreSkuLotDTO::getOriginSkuLotNo).distinct().collect(Collectors.toList());
                    originSplitPreChildAllocationOrderDTOList = originPreChildAllocationOrderDTOList.stream()
                            .filter(a -> preChildSkuLotCodeList.contains(a.getSkuLotNo()))
                            .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                            .filter(a -> allocationOrderDTO.getLocationCode().equalsIgnoreCase(a.getLocationCode()))
                            .collect(Collectors.toList());
                }
                //TODO 分配记录用于小于包裹明细记录(同一个包裹同一个商品同一条明细可以占用多个批次，就有多个分配记录)
                if (allocationOrderDTO.getExpQty().compareTo(qty) > 0) {
                    //创建新的分配记录
                    AllocationOrderDTO newAllocationOrderDTO = buildNewAllocationOrderDTO(allocationOrderDTO, newPack, uuID, qty);
                    newAllocationList.add(newAllocationOrderDTO);
                    //修改明细
                    allocationOrderDTO.setPickQty(allocationOrderDTO.getPickQty().subtract(qty));
                    allocationOrderDTO.setRealQty(allocationOrderDTO.getRealQty().subtract(qty));
                    allocationOrderDTO.setExpQty(allocationOrderDTO.getExpQty().subtract(qty));
                    if (allocationOrderDTO.getSplitQty().compareTo(BigDecimal.ZERO) > 0) {
                        allocationOrderDTO.setSplitQty(allocationOrderDTO.getSplitQty().subtract(qty));
                    }
                    //获取当前包裹明细
                    PackageDetailDTO packageDetailDTO = originPrePackDetailList.stream()
                            .filter(a -> a.getSkuCode().equalsIgnoreCase(allocationOrderDTO.getSkuCode()))
                            .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                            .filter(a -> a.getPackUid().equalsIgnoreCase(allocationOrderDTO.getPackUid())).findFirst().orElse(null);
                    packageDetailDTO.setExpQty(packageDetailDTO.getExpQty().subtract(qty));
                    packageDetailDTO.setSkuQty(packageDetailDTO.getSkuQty().subtract(qty));
                    packageDetailDTO.setPickQty(packageDetailDTO.getPickQty().subtract(qty));
                    packageDetailDTO.setAssignQty(packageDetailDTO.getAssignQty().subtract(qty));
                    //新生产一个包裹明细
                    PackageDetailDTO newDetail = buildNewPackDetailDTO(qty, newPack, uuID, packageDetailDTO);
                    newPackDetailList.add(newDetail);
                    //拆分子商品明细
                    if (isPreSku) {
                        //---------------------------------- 预包对应子商品拆分-----------------------------------
                        BigDecimal finalQty = ObjectUtil.cloneByStream(qty);
                        //当前预包批次--对应子商品批次的同库位分配记录
                        List<AllocationOrderDTO> finalOriginSplitPreChildAllocationOrderDTOList = originSplitPreChildAllocationOrderDTOList;
                        skuNeedQtyMap.entrySet().forEach(map -> {
                            String currentSkuChildCode = map.getKey();
                            BigDecimal currentNeedQty = finalQty.multiply(map.getValue());
                            List<AllocationOrderDTO> allocationOrderDTOList = finalOriginSplitPreChildAllocationOrderDTOList.stream()
                                    .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                                    .filter(a -> a.getSkuCode().equals(currentSkuChildCode)).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(currentAllocationDTOList)) {
                                log.info("packSubExpNoParam-error:{}", JSONUtil.toJsonStr(currentSkuChildCode));
                                throw new BaseException(BaseBizEnum.TIP, "提交效期异常");
                            }
                            for (AllocationOrderDTO allocationOrderDTOChild : allocationOrderDTOList) {
                                String uuIDChild = UUID.randomUUID().toString();
                                if (currentNeedQty.compareTo(BigDecimal.ZERO) == 0) {
                                    break;
                                }
                                if (allocationOrderDTOChild.getExpQty().compareTo(currentNeedQty) > 0) {
                                    //创建新的分配记录
                                    AllocationOrderDTO newAllocationOrderDTOChild = buildNewAllocationOrderDTO(allocationOrderDTOChild, newPack, uuIDChild, currentNeedQty);
                                    newAllocationList.add(newAllocationOrderDTOChild);
                                    //修改明细
                                    allocationOrderDTOChild.setPickQty(allocationOrderDTOChild.getPickQty().subtract(currentNeedQty));
                                    allocationOrderDTOChild.setRealQty(allocationOrderDTOChild.getRealQty().subtract(currentNeedQty));
                                    allocationOrderDTOChild.setExpQty(allocationOrderDTOChild.getExpQty().subtract(currentNeedQty));
                                    if (allocationOrderDTO.getSplitQty().compareTo(BigDecimal.ZERO) > 0) {
                                        allocationOrderDTO.setSplitQty(allocationOrderDTO.getSplitQty().subtract(currentNeedQty));
                                    }
                                    //获取当前包裹明细
                                    PackageDetailDTO packageDetailDTOChild = originPreChildPackDetailList.stream()
                                            .filter(a -> a.getSkuCode().equalsIgnoreCase(allocationOrderDTOChild.getSkuCode()))
                                            .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                                            .filter(a -> a.getPackUid().equalsIgnoreCase(allocationOrderDTOChild.getPackUid())).findFirst().orElse(null);
                                    packageDetailDTOChild.setExpQty(packageDetailDTOChild.getExpQty().subtract(currentNeedQty));
                                    packageDetailDTOChild.setSkuQty(packageDetailDTOChild.getSkuQty().subtract(currentNeedQty));
                                    packageDetailDTOChild.setPickQty(packageDetailDTOChild.getPickQty().subtract(currentNeedQty));
                                    packageDetailDTOChild.setAssignQty(packageDetailDTOChild.getAssignQty().subtract(currentNeedQty));
                                    //新生产一个包裹明细
                                    PackageDetailDTO newDetailChild = buildNewPackDetailDTO(currentNeedQty, newPack, uuIDChild, packageDetailDTOChild);
                                    newPackDetailList.add(newDetailChild);
                                    currentNeedQty = BigDecimal.ZERO;
                                } else {
                                    //包裹明细二次拆分
                                    //获取当前包裹明细
                                    PackageDetailDTO packageDetailDTOChild = originPreChildPackDetailList.stream()
                                            .filter(a -> a.getSkuCode().equalsIgnoreCase(allocationOrderDTOChild.getSkuCode()))
                                            .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                                            .filter(a -> a.getPackUid().equalsIgnoreCase(allocationOrderDTOChild.getPackUid())).findFirst().orElse(null);

                                    allocationOrderDTOChild.setPackageCode(newPack.getPackageCode());
                                    if (packageDetailDTOChild.getExpQty().compareTo(allocationOrderDTOChild.getExpQty()) > 0) {
                                        packageDetailDTOChild.setExpQty(packageDetailDTOChild.getExpQty().subtract(allocationOrderDTOChild.getExpQty()));
                                        packageDetailDTOChild.setSkuQty(packageDetailDTOChild.getSkuQty().subtract(allocationOrderDTOChild.getExpQty()));
                                        packageDetailDTOChild.setPickQty(packageDetailDTOChild.getPickQty().subtract(allocationOrderDTOChild.getExpQty()));
                                        packageDetailDTOChild.setAssignQty(packageDetailDTOChild.getAssignQty().subtract(allocationOrderDTOChild.getExpQty()));
                                        //新生产一个包裹明细
                                        PackageDetailDTO newDetailChild = buildNewPackDetailDTO(allocationOrderDTOChild.getExpQty(), newPack, allocationOrderDTOChild.getPackUid(), packageDetailDTOChild);
                                        newPackDetailList.add(newDetailChild);
                                    } else {
                                        packageDetailDTOChild.setPackageCode(newPack.getPackageCode());
                                        packageDetailDTOChild.setCheckQty(packageDetailDTOChild.getPickQty());
                                        packageDetailDTOChild.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                                        packageDetailDTOChild.setCreatedBy(CurrentUserHolder.getUserName());
                                        packageDetailDTOChild.setCreatedTime(System.currentTimeMillis());
                                        packageDetailDTOChild.setUpdatedBy(CurrentUserHolder.getUserName());
                                        packageDetailDTOChild.setUpdatedTime(System.currentTimeMillis());
                                    }
                                    currentNeedQty = currentNeedQty.subtract(allocationOrderDTOChild.getExpQty());
                                }
                            }
                        });
                        //---------------------------------- 预包对应子商品拆分-----------------------------------
                    }
                    qty = BigDecimal.ZERO;
                } else {
                    //------------------TTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT
                    if (isPreSku) {
                        //---------------------------------- 预包对应子商品拆分-----------------------------------
                        BigDecimal finalQty = ObjectUtil.cloneByStream(allocationOrderDTO.getExpQty());
                        //当前预包批次--对应子商品批次的同库位分配记录
                        List<AllocationOrderDTO> finalOriginSplitPreChildAllocationOrderDTOList = originSplitPreChildAllocationOrderDTOList;
                        skuNeedQtyMap.entrySet().forEach(map -> {
                            String currentSkuChildCode = map.getKey();
                            BigDecimal currentNeedQty = finalQty.multiply(map.getValue());
                            List<AllocationOrderDTO> allocationOrderDTOList = finalOriginSplitPreChildAllocationOrderDTOList.stream()
                                    .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                                    .filter(a -> a.getSkuCode().equals(currentSkuChildCode)).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(currentAllocationDTOList)) {
                                log.info("packSubExpNoParam-error:{}", JSONUtil.toJsonStr(currentSkuChildCode));
                                throw new BaseException(BaseBizEnum.TIP, "提交效期异常");
                            }
                            for (AllocationOrderDTO allocationOrderDTOChild : allocationOrderDTOList) {
                                String uuIDChild = UUID.randomUUID().toString();
                                if (currentNeedQty.compareTo(BigDecimal.ZERO) == 0) {
                                    break;
                                }
                                if (allocationOrderDTOChild.getExpQty().compareTo(currentNeedQty) > 0) {
                                    //创建新的分配记录
                                    AllocationOrderDTO newAllocationOrderDTOChild = buildNewAllocationOrderDTO(allocationOrderDTOChild, newPack, uuIDChild, currentNeedQty);
                                    newAllocationList.add(newAllocationOrderDTOChild);
                                    //修改明细
                                    allocationOrderDTOChild.setPickQty(allocationOrderDTOChild.getPickQty().subtract(currentNeedQty));
                                    allocationOrderDTOChild.setRealQty(allocationOrderDTOChild.getRealQty().subtract(currentNeedQty));
                                    allocationOrderDTOChild.setExpQty(allocationOrderDTOChild.getExpQty().subtract(currentNeedQty));
                                    if (allocationOrderDTO.getSplitQty().compareTo(BigDecimal.ZERO) > 0) {
                                        allocationOrderDTO.setSplitQty(allocationOrderDTO.getSplitQty().subtract(currentNeedQty));
                                    }
                                    //获取当前包裹明细
                                    PackageDetailDTO packageDetailDTOChild = originPreChildPackDetailList.stream()
                                            .filter(a -> a.getSkuCode().equalsIgnoreCase(allocationOrderDTOChild.getSkuCode()))
                                            .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                                            .filter(a -> a.getPackUid().equalsIgnoreCase(allocationOrderDTOChild.getPackUid())).findFirst().orElse(null);
                                    packageDetailDTOChild.setExpQty(packageDetailDTOChild.getExpQty().subtract(currentNeedQty));
                                    packageDetailDTOChild.setSkuQty(packageDetailDTOChild.getSkuQty().subtract(currentNeedQty));
                                    packageDetailDTOChild.setPickQty(packageDetailDTOChild.getPickQty().subtract(currentNeedQty));
                                    packageDetailDTOChild.setAssignQty(packageDetailDTOChild.getAssignQty().subtract(currentNeedQty));
                                    //新生产一个包裹明细
                                    PackageDetailDTO newDetailChild = buildNewPackDetailDTO(currentNeedQty, newPack, uuIDChild, packageDetailDTOChild);
                                    newPackDetailList.add(newDetailChild);
                                    currentNeedQty = BigDecimal.ZERO;
                                } else {
                                    //包裹明细二次拆分
                                    //获取当前包裹明细
                                    PackageDetailDTO packageDetailDTOChild = originPreChildPackDetailList.stream()
                                            .filter(a -> a.getSkuCode().equalsIgnoreCase(allocationOrderDTOChild.getSkuCode()))
                                            .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                                            .filter(a -> a.getPackUid().equalsIgnoreCase(allocationOrderDTOChild.getPackUid())).findFirst().orElse(null);

                                    allocationOrderDTOChild.setPackageCode(newPack.getPackageCode());
                                    if (packageDetailDTOChild.getExpQty().compareTo(allocationOrderDTOChild.getExpQty()) > 0) {
                                        packageDetailDTOChild.setExpQty(packageDetailDTOChild.getExpQty().subtract(allocationOrderDTOChild.getExpQty()));
                                        packageDetailDTOChild.setSkuQty(packageDetailDTOChild.getSkuQty().subtract(allocationOrderDTOChild.getExpQty()));
                                        packageDetailDTOChild.setPickQty(packageDetailDTOChild.getPickQty().subtract(allocationOrderDTOChild.getExpQty()));
                                        packageDetailDTOChild.setAssignQty(packageDetailDTOChild.getAssignQty().subtract(allocationOrderDTOChild.getExpQty()));
                                        //新生产一个包裹明细
                                        PackageDetailDTO newDetailChild = buildNewPackDetailDTO(allocationOrderDTOChild.getExpQty(), newPack, allocationOrderDTOChild.getPackUid(), packageDetailDTOChild);
                                        newPackDetailList.add(newDetailChild);
                                    } else {
                                        packageDetailDTOChild.setPackageCode(newPack.getPackageCode());
                                        packageDetailDTOChild.setCheckQty(packageDetailDTOChild.getPickQty());
                                        packageDetailDTOChild.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                                        packageDetailDTOChild.setCreatedBy(CurrentUserHolder.getUserName());
                                        packageDetailDTOChild.setCreatedTime(System.currentTimeMillis());
                                        packageDetailDTOChild.setUpdatedBy(CurrentUserHolder.getUserName());
                                        packageDetailDTOChild.setUpdatedTime(System.currentTimeMillis());
                                    }
                                    currentNeedQty = currentNeedQty.subtract(allocationOrderDTOChild.getExpQty());
                                }
                            }
                        });
                    }
                    //----------------TTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT
                    //包裹明细二次拆分
                    //获取当前包裹明细
                    PackageDetailDTO packageDetailDTO = originPrePackDetailList.stream()
                            .filter(a -> a.getSkuCode().equalsIgnoreCase(allocationOrderDTO.getSkuCode()))
                            .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                            .filter(a -> a.getPackUid().equalsIgnoreCase(allocationOrderDTO.getPackUid())).findFirst().orElse(null);

                    allocationOrderDTO.setPackageCode(newPack.getPackageCode());
                    if (packageDetailDTO.getExpQty().compareTo(allocationOrderDTO.getExpQty()) > 0) {
                        packageDetailDTO.setExpQty(packageDetailDTO.getExpQty().subtract(allocationOrderDTO.getExpQty()));
                        packageDetailDTO.setSkuQty(packageDetailDTO.getSkuQty().subtract(allocationOrderDTO.getExpQty()));
                        packageDetailDTO.setPickQty(packageDetailDTO.getPickQty().subtract(allocationOrderDTO.getExpQty()));
                        packageDetailDTO.setAssignQty(packageDetailDTO.getAssignQty().subtract(allocationOrderDTO.getExpQty()));
                        //新生产一个包裹明细
                        PackageDetailDTO newDetail = buildNewPackDetailDTO(allocationOrderDTO.getExpQty(), newPack, allocationOrderDTO.getPackUid(), packageDetailDTO);
                        newPackDetailList.add(newDetail);
                    } else {
                        packageDetailDTO.setPackageCode(newPack.getPackageCode());
                        packageDetailDTO.setCheckQty(packageDetailDTO.getPickQty());
                        packageDetailDTO.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                        packageDetailDTO.setCreatedBy(CurrentUserHolder.getUserName());
                        packageDetailDTO.setCreatedTime(System.currentTimeMillis());
                        packageDetailDTO.setUpdatedBy(CurrentUserHolder.getUserName());
                        packageDetailDTO.setUpdatedTime(System.currentTimeMillis());
                    }
                    qty = qty.subtract(allocationOrderDTO.getExpQty());
                }
            }
        }
        //=================================================================================

        //==============拆分原始明细=======
        //新包裹的 子商品明细（预包商品对应子商品明细 + 剩余商品）
        List<PackageDetailDTO> newOriginPackDetailList = new ArrayList<>();
        newOriginPackDetailList.addAll(originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), newPack.getPackageCode())).filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE_CHILD.getCode())).collect(Collectors.toList()));
        newOriginPackDetailList.addAll(newPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), newPack.getPackageCode())).filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE_CHILD.getCode())).collect(Collectors.toList()));
        List<PackageDetailDTO> finalNewOriginPackDetailList = ObjectUtil.cloneByStream(newOriginPackDetailList);
        //原始明细拆分
        for (PackageDetailDTO packageDetailDTO : originNormalPackDetailList) {
            String skuCode = packageDetailDTO.getSkuCode();
            BigDecimal currentQty = finalNewOriginPackDetailList.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode))
                    .map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (currentQty.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            String uuIDThird = UUID.randomUUID().toString();
            //当前包裹明细数量大于提交数量
            BigDecimal changeQty;
            if (packageDetailDTO.getPickQty().compareTo(currentQty) > 0) {
                changeQty = currentQty;
                packageDetailDTO.setExpQty(packageDetailDTO.getExpQty().subtract(currentQty));
                packageDetailDTO.setSkuQty(packageDetailDTO.getSkuQty().subtract(currentQty));
                packageDetailDTO.setPickQty(packageDetailDTO.getPickQty().subtract(currentQty));
                packageDetailDTO.setAssignQty(packageDetailDTO.getAssignQty().subtract(currentQty));
                //新生产一个包裹明细
                PackageDetailDTO newDetail = buildNewPackDetailDTO(currentQty, newPack, uuIDThird, packageDetailDTO);
                newPackDetailList.add(newDetail);
            } else {
                changeQty = packageDetailDTO.getPickQty();
                packageDetailDTO.setPackageCode(newPack.getPackageCode());
                packageDetailDTO.setCheckQty(packageDetailDTO.getPickQty());
                packageDetailDTO.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                packageDetailDTO.setCreatedBy(CurrentUserHolder.getUserName());
                packageDetailDTO.setCreatedTime(System.currentTimeMillis());
                packageDetailDTO.setUpdatedBy(CurrentUserHolder.getUserName());
                packageDetailDTO.setUpdatedTime(System.currentTimeMillis());
            }
            //消减原始数量
            for (PackageDetailDTO packageDetail : finalNewOriginPackDetailList) {
                if (packageDetail.getSkuCode().equalsIgnoreCase(skuCode)) {
                    if (changeQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (packageDetail.getPickQty().compareTo(changeQty) >= 0) {
                        packageDetail.setPickQty(packageDetail.getPickQty().subtract(changeQty));
                        changeQty = BigDecimal.ZERO;
                    } else {
                        changeQty = changeQty.subtract(packageDetail.getPickQty());
                        packageDetail.setPickQty(BigDecimal.ZERO);
                    }
                }
            }
        }
        //==============拆分原始明细=======
        packageDTO.setListDetail(originPackDetailList);
    }

    /**
     * @param cargoCode
     * @param skuCode
     * @param skuLotNo
     * @return java.util.List<com.dt.domain.bill.dto.pre.PreSkuLotDTO>
     * <AUTHOR>
     * @describe:
     * @date 2023/8/3 13:21
     */
    private List<PreSkuLotDTO> queryPreSkuLotDTO(String cargoCode, String skuCode, String skuLotNo) {
        PreSkuLotParam preSkuLotParam = new PreSkuLotParam();
        preSkuLotParam.setCargoCode(cargoCode);
        preSkuLotParam.setSkuCode(skuCode);
        preSkuLotParam.setSkuLotNo(skuLotNo);
        List<PreSkuLotDTO> preSkuLotDTOList = remotePreSkuLotClient.getList(preSkuLotParam);
        if (CollectionUtils.isEmpty(preSkuLotDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "预包区预包条码库存异常");
        }
        return preSkuLotDTOList;
    }

    @Override
    public List<MaterialUseRecordDTO> buildMaterialUseRecord(PackageDTO packageDTO, List<PackageDetailDTO> packageDetailDTOList, List<SkuDTO> skuList) {
        Boolean is4PL = Boolean.FALSE;
        if (!StringUtils.isEmpty(packageDTO.getOrderTag()) && OrderTagEnum.NumToEnum(packageDTO.getOrderTag())
                .stream().anyMatch(a -> a.getCode().equals(OrderTagEnum.FOUR_TAG.code()))) {
            is4PL = Boolean.TRUE;
        }
        //只处理预包商品+预包剩余明细
        if (packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode())) {
            packageDetailDTOList = packageDetailDTOList.stream()
                    .filter(a -> Arrays.asList(PackIsPreEnum.PRE.getCode(), PackIsPreEnum.LAST.getCode()).contains(a.getIsPre())).collect(Collectors.toList());
        } else {
            //只处理原始商品
            packageDetailDTOList = packageDetailDTOList.stream()
                    .filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
        }
        List<SkuMaterialDTO> skuMaterialDTOList = new ArrayList<>();
        List<String> skuCodeList = packageDetailDTOList.stream().map(PackageDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        //如果是淘天的订单、组装 skuMaterialDTO  后续逻辑通用。
        if (!StringUtils.isEmpty(wmsOtherConfig.getTaotainWarehouseCodeList()) && wmsOtherConfig.getTaotainWarehouseCodeList().contains(packageDTO.getWarehouseCode())) {
//            List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = iRemoteShipmentOrderClient.getDetailList(packageDTO.getShipmentOrderCode());
//            for (ShipmentOrderDetailDTO shipmentOrderDetailDTO : shipmentOrderDetailDTOList) {
//                if (skuCodeList.contains(shipmentOrderDetailDTO.getSkuCode())
//                        && skuMaterialDTOList.stream().noneMatch(a -> Objects.equals(a.getSkuCode(), shipmentOrderDetailDTO.getSkuCode()))) {
//                    //拿到里面的外部编码查询PackageMaterialParam  然后拿Code反过来拼装skuMaterialDTOList
//                    PackageMaterialParam packageMaterialParam = new PackageMaterialParam();
//                    packageMaterialParam.setCargoCode(packageDTO.getCargoCode());
//                    ShipmentOrderDetailExtraJsonDTO extraJson = shipmentOrderDetailDTO.getExtraJsonObject();
//                    if (ObjectUtil.isNotEmpty(extraJson)) {
//                        List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> consumablesMaterials = extraJson.getConsumablesMaterials();
//                        if (CollectionUtil.isEmpty(consumablesMaterials)) {
//                            continue;
//                        }
//                        packageMaterialParam.setOutCodeList(consumablesMaterials.stream().map(ShipmentOrderDetailExtraJsonConsumableInfoDTO::getConsumablesCode).collect(Collectors.toList()));
//                        List<PackageMaterialDTO> packageMaterialList = iRemotePackageMaterialClient.getList(packageMaterialParam);
//                        Map<String, ShipmentOrderDetailExtraJsonConsumableInfoDTO> map = consumablesMaterials.stream()
//                                .collect(Collectors.toMap(ShipmentOrderDetailExtraJsonConsumableInfoDTO::getConsumablesCode, dto -> dto));
//                        for (PackageMaterialDTO packageMaterialDTO : packageMaterialList) {
//                            SkuMaterialDTO skuMaterialDTO = new SkuMaterialDTO();
//                            skuMaterialDTO.setWarehouseCode(packageMaterialDTO.getWarehouseCode());
//                            skuMaterialDTO.setCargoCode(packageMaterialDTO.getCargoCode());
//                            skuMaterialDTO.setMaterialCode(packageMaterialDTO.getCode());
//                            skuMaterialDTO.setBarCode(packageMaterialDTO.getBarCode());
//                            skuMaterialDTO.setSkuCode(shipmentOrderDetailDTO.getSkuCode());
//                            skuMaterialDTO.setMaterialNum(Integer.valueOf(map.get(packageMaterialDTO.getOutCode()).getConsumablesNum()));
//                            skuMaterialDTOList.add(skuMaterialDTO);
//                        }
//                    }
//                }
//            }
            List<ShipmentDetailMaterialOtherDTO> detailMaterialOtherDTOList = this.buildTaoTianMaterialInfo(packageDTO);
            skuMaterialDTOList = this.buildTaoTianMaterial(packageDTO, skuCodeList, detailMaterialOtherDTOList);
        } else {
            SkuMaterialParam skuMaterialParam = new SkuMaterialParam();
            skuMaterialParam.setCargoCode(packageDTO.getCargoCode());
            skuMaterialParam.setSkuCodeList(skuCodeList);
            skuMaterialDTOList = remoteSkuMaterialClient.getList(skuMaterialParam);
            if (!CollectionUtils.isEmpty(skuMaterialDTOList)) {
                skuMaterialDTOList.forEach(it -> {
                    it.setSkuMaterialTip(true);
                });
            }
        }

        if (CollectionUtils.isEmpty(skuMaterialDTOList)) {
            return new ArrayList<>();
        }
        //查询包材
        PackageMaterialParam packageMaterialParam = new PackageMaterialParam();
        packageMaterialParam.setBarCodeList(skuMaterialDTOList.stream().map(SkuMaterialDTO::getBarCode).distinct().collect(Collectors.toList()));
        packageMaterialParam.setCargoCode(packageDTO.getCargoCode());
        List<PackageMaterialDTO> packageMaterialDTOList = iRemotePackageMaterialClient.getList(packageMaterialParam);
        if (CollectionUtils.isEmpty(packageMaterialDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "耗材未找到");
        }
        List<MaterialUseRecordDTO> materialUseRecordDTOList = new ArrayList<>();
        Boolean finalIs4PL = is4PL;
        skuMaterialDTOList.stream().forEach(a -> {
            PackageMaterialDTO packageMaterialDTO = packageMaterialDTOList.stream().filter(it -> it.getBarCode().equalsIgnoreCase(a.getBarCode())).findFirst().orElse(null);
            if (packageMaterialDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("耗材%s未找到", a.getBarCode()));
            }
            if (finalIs4PL) {
                if (!packageMaterialDTO.getType().equals(MaterialTypeEnum.HC.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("订单的商品绑定%s的有有包材，请移除或者更改后重试", a.getBarCode()));
                }
            }
        });
        List<PackageDetailDTO> finalPackageDetailDTOList = packageDetailDTOList;
        List<SkuMaterialDTO> finalSkuMaterialDTOList = skuMaterialDTOList;
        skuCodeList.stream().forEach(skuCode -> {
//            包裹明细商品总数量
            BigDecimal reduceQty = finalPackageDetailDTOList.stream()
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode))
                    .map(PackageDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            List<SkuMaterialDTO> materialDTOList = finalSkuMaterialDTOList.stream().filter(a -> a.getSkuCode().equalsIgnoreCase(skuCode)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(materialDTOList)) {
                materialDTOList.forEach(materialDTO -> {
                    MaterialUseRecordDTO materialUseRecordDTO = new MaterialUseRecordDTO();
                    materialUseRecordDTO.setWarehouseCode(packageDTO.getWarehouseCode());
                    materialUseRecordDTO.setCargoCode(packageDTO.getCargoCode());
                    materialUseRecordDTO.setPackageCode(packageDTO.getPackageCode());
                    materialUseRecordDTO.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
                    materialUseRecordDTO.setPoNo(packageDTO.getPoNo());
                    materialUseRecordDTO.setSoNo(packageDTO.getSoNo());
                    materialUseRecordDTO.setSkuCode(skuCode);
                    materialUseRecordDTO.setScanTag(SkuMaterialScanTagEnum.NO_SCAN.getCode());
                    materialUseRecordDTO.setMaterialCode(materialDTO.getMaterialCode());
                    materialUseRecordDTO.setBarCode(materialDTO.getBarCode());
                    PackageMaterialDTO packageMaterialDTO = packageMaterialDTOList.stream().filter(it -> it.getBarCode().equalsIgnoreCase(materialDTO.getBarCode())).findFirst().orElse(null);
                    materialUseRecordDTO.setIs4pl(packageMaterialDTO.getIs4pl());

//                    SkuDTO dto = skuList.stream().filter(skuDTO -> skuDTO.getCode().equals(skuCode)).findFirst().orElse(null);
//                    if (dto != null && dto.getMaterialAddWeight().equals(SkuMaterialAddWeightEnum.SKU_ADD_WEIGHT_YES.getCode())) {
//                        materialUseRecordDTO.setMaterialAddWeight(dto.getMaterialAddWeight());
//                    }
                    materialUseRecordDTO.setMaterialAddWeight(SkuMaterialAddWeightEnum.SKU_ADD_WEIGHT_YES.getCode());
                    materialUseRecordDTO.setStatus(MaterialUseRecordStatusEnum.ENABLED.getCode());
                    //商品维度需要乘以商品数量
                    if (materialDTO.getSkuMaterialTip()) {
                        int num = reduceQty.multiply(new BigDecimal(materialDTO.getMaterialNum() + "")).intValue();
                        materialUseRecordDTO.setMaterialNum(num);
                    } else {
                        materialUseRecordDTO.setMaterialNum(materialDTO.getMaterialNum());
                    }
                    materialUseRecordDTOList.add(materialUseRecordDTO);
                });
            }
        });
        return materialUseRecordDTOList;
    }

    @Override
    public MaterialUseRecordDTO buildMaterialUseRecordByColdChainBox(PackageDTO packageDTO, String
            hcUpcCode, List<SkuDTO> skuList) {
        Boolean is4PL = Boolean.FALSE;
        if (!StringUtils.isEmpty(packageDTO.getOrderTag()) && OrderTagEnum.NumToEnum(packageDTO.getOrderTag())
                .stream().anyMatch(a -> a.getCode().equals(OrderTagEnum.FOUR_TAG.code()))) {
            is4PL = Boolean.TRUE;
        }
        //查询包材
        PackageMaterialDTO materialDTO = iRemotePackageMaterialClient.queryByUpcCode(packageDTO.getCargoCode(), hcUpcCode);
        if (materialDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材未找到");
        }
        if (Objects.equals(materialDTO.getStatus(), PackageMaterialStatusEnum.DISABLE.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材禁用,请确认");
        }
        if (!materialDTO.getType().equalsIgnoreCase(MaterialTypeEnum.HC.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材冷链泡沫箱属性必须是耗材");
        }
        if (MaterialTagEnum.NumToEnum(materialDTO.getMaterialTag()).stream().noneMatch(a -> a.getCode().equals(MaterialTagEnum.COLD_CHAIN_BOX.getCode()))) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材未维护未冷链泡沫箱");
        }
        if (is4PL && !materialDTO.getIs4pl().equals(Is4PLEnum.YES.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材4PL订单只能扫 4PL的耗材");
        }
        if (!is4PL && !materialDTO.getIs4pl().equals(Is4PLEnum.NO.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材非4PL订单只能扫非4PL耗材");
        }
        MaterialUseRecordDTO materialUseRecordDTO = new MaterialUseRecordDTO();
        materialUseRecordDTO.setWarehouseCode(packageDTO.getWarehouseCode());
        materialUseRecordDTO.setCargoCode(packageDTO.getCargoCode());
        materialUseRecordDTO.setPackageCode(packageDTO.getPackageCode());
        materialUseRecordDTO.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
        materialUseRecordDTO.setPoNo(packageDTO.getPoNo());
        materialUseRecordDTO.setSoNo(packageDTO.getSoNo());
        materialUseRecordDTO.setScanTag(SkuMaterialScanTagEnum.IS_SCAN.getCode());
        SkuDTO skuDTO = skuList.stream()
                .filter(sku -> SkuTagEnum.NumToEnum(sku.getSkuTag()).stream().anyMatch(a -> a.getCode().equals(SkuTagEnum.COLD_CHAIN_BOX.getCode())))
                .findFirst().orElse(null);
        if (skuDTO != null) {
            materialUseRecordDTO.setSkuCode(skuDTO.getCode());
        }
        materialUseRecordDTO.setBarCode(materialDTO.getBarCode());
        materialUseRecordDTO.setMaterialCode(materialDTO.getCode());
        materialUseRecordDTO.setIs4pl(materialDTO.getIs4pl());

        materialUseRecordDTO.setMaterialAddWeight(SkuMaterialAddWeightEnum.SKU_ADD_WEIGHT_YES.getCode());
        materialUseRecordDTO.setStatus(MaterialUseRecordStatusEnum.ENABLED.getCode());
        materialUseRecordDTO.setMaterialNum(1);
        return materialUseRecordDTO;
    }

    @Override
    public List<OutSourceCodeDTO> checkSealingTape(PackSubmitParam param, PackageDTO packageDTO, List<SkuDTO> skuList) {
        List<OutSourceCodeDTO> outSourceCodeDTOList = new ArrayList<>();
        List<SkuDTO> skuDTOList = skuList.stream().filter(a -> a.getNeedUptracSourceCodeTag() || a.getPullTapeTag() || a.getAntiCounterfeitingBuckleTag()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return new ArrayList<>();
        }
        Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity()));
        List<String> sealingTapeSkuCodeList = skuDTOList.stream().map(SkuDTO::getCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(param.getSealingTapeParamList())) {
            throw new BaseException(BaseBizEnum.TIP, "当前提交的商品中,需要扫描【溯源码】或【易撕贴】或【防伪扣】");
        }
        //校验数量是否一致
        BigDecimal expQty = param.getSkuBackParamList().stream().filter(a -> sealingTapeSkuCodeList.contains(a.getSkuCode())).map(PackSubExpNoParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (expQty.intValue() != param.getSealingTapeParamList().size()) {
            throw new BaseException(BaseBizEnum.TIP, "封口贴条码提交的数量和需要扫描【溯源码】或【易撕贴】或【防伪扣】的商品数量不一致");
        }
        Map<String, List<SealingTapeCodeParam>> sealingTapeMap = param.getSealingTapeParamList().stream().collect(Collectors.groupingBy(a -> String.format("%s#%s", a.getSkuCode(), a.getSealingTapeCode())));


        Set<String> sourceCodeSet = new HashSet<>();

        for (Map.Entry<String, List<SealingTapeCodeParam>> sealingMap : sealingTapeMap.entrySet()) {

            String skuCode = sealingMap.getKey().split("#")[0];
            String sealingTapeCode = sealingMap.getKey().split("#")[1];
            List<SealingTapeCodeParam> codeParamList = sealingMap.getValue();
            if (!skuDTOMap.containsKey(skuCode)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品编码%s不需要【溯源码】或【易撕贴】或【防伪扣】", skuCode));
            }
            SkuDTO skuDTO = skuDTOMap.get(skuCode);
            SourceCodeScanTypeEnum codeScanTypeEnum = SourceCodeScanTypeEnum.SEALING_TAPE;
            //溯源码
            if (skuDTO.getNeedUptracSourceCodeTag()) {
                codeScanTypeEnum = SourceCodeScanTypeEnum.SOURCE_CODE;
            }
            //防伪扣
            if (skuDTO.getAntiCounterfeitingBuckleTag()) {
                codeScanTypeEnum = SourceCodeScanTypeEnum.ANTI_COUNTERFEITING_BUCKLE;
            }
            //易撕贴
            if (skuDTO.getPullTapeTag()) {
                codeScanTypeEnum = SourceCodeScanTypeEnum.PULL_TAPE_CODE;
            }
            if (codeScanTypeEnum.equals(SourceCodeScanTypeEnum.SOURCE_CODE)) {
                sourceCodeSet.add(skuCode);
            }

            OutSourceCodeDTO outSourceCodeDTO = ConverterUtil.convert(packageDTO, OutSourceCodeDTO.class);
            outSourceCodeDTO.setSkuCode(skuCode);
            outSourceCodeDTO.setUpcCode(codeParamList.get(0).getUpcCode());
            outSourceCodeDTO.setSkuName(skuDTO.getName());
            outSourceCodeDTO.setScanType(codeScanTypeEnum.getCode());
            outSourceCodeDTO.setSnCode(sealingTapeCode);
            outSourceCodeDTO.setQty(new BigDecimal(codeParamList.size() + ""));
            outSourceCodeDTO.setId(null);
            outSourceCodeDTOList.add(outSourceCodeDTO);

        }
        //溯源码规则校验
        checkSourceCodeRule(packageDTO, outSourceCodeDTOList, sourceCodeSet);

        return outSourceCodeDTOList;
    }

    private void checkSourceCodeRule(PackageDTO packageDTO, List<OutSourceCodeDTO> outSourceCodeDTOListOrigin, Set<String> sourceCodeSet) {

        if (CollectionUtils.isEmpty(sourceCodeSet)) {
            return;
        }
        List<OutSourceCodeDTO> outSourceCodeDTOList = outSourceCodeDTOListOrigin.stream().filter(a -> sourceCodeSet.contains(a.getSkuCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outSourceCodeDTOList)) {
            return;
        }
        CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), CargoConfigParamEnum.SOURCE_CODE_REPEATED.getCode());

        //1:允许同商品的溯源码相同,隐含包裹内溯源码可重复,2:同商品溯源码不允许重复,包裹内不同商品允许重复,3:同商品溯源码不允许重复,包裹内不同商品之间也不允许重复
        if (cargoConfigDTO != null && cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue())) {
            if (Objects.equals(cargoConfigDTO.getPropValue(), "2")) {
                Map<String, List<OutSourceCodeDTO>> map = outSourceCodeDTOList.stream()
                        .collect(Collectors.groupingBy(it -> StrUtil.join("#", it.getSkuCode(), it.getSnCode(), it.getUpcCode())));
                map.forEach((key, value) -> {
                    String[] split = key.split("#");
                    String skuCode = split[0];
                    String snCode = split[1];
                    String upcCode = split[2];
                    if (value.size() > 1) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("【商品条码:%s,溯源码:%s】当前包裹重复,请核查", upcCode, snCode));
                    }
                });
            }
            if (Objects.equals(cargoConfigDTO.getPropValue(), "3")) {
                Map<String, List<OutSourceCodeDTO>> map = outSourceCodeDTOList.stream()
                        .collect(Collectors.groupingBy(OutSourceCodeDTO::getSnCode));
                map.forEach((snCode, value) -> {
                    if (value.size() > 1) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("【溯源码:%s】当前包裹重复,请核查", snCode));
                    }
                });

            }
        }

    }

    @Override
    public List<PackCheckSkuAndAllocationDTO> getPackCheckSkuAndAllocation(PackageDTO packageDTO, Map<String, SkuDTO> skuDTOMap) {
        AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
        allocationOrderParam.setPackageCode(packageDTO.getPackageCode());
        allocationOrderParam.setWaveCode(packageDTO.getWaveCode());
        if (Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.PRE.getCode())) {
            allocationOrderParam.setIsPreList(Arrays.asList(AllocationIsPreEnum.PRE.getCode(), AllocationIsPreEnum.LAST.getCode()));
        }
        List<AllocationOrderDTO> allocationOrderDTOList = iRemoteAllocationOrderClient.getList(allocationOrderParam);
        if (CollectionUtils.isEmpty(allocationOrderDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "包裹分配记录参数异常");
        }
        //获取当前批次信息
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCargoCode(packageDTO.getCargoCode());
        skuLotParam.setCodeList(allocationOrderDTOList.stream().map(AllocationOrderDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
        if (CollectionUtils.isEmpty(skuLotDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "包裹分配批次异常");
        }
        //获取出库单明细
        List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = new ArrayList<>();
        if (remoteWarehouseClient.getTaoTianWarehouse(packageDTO.getWarehouseCode())) {
            ShipmentOrderDetailParam shipmentOrderDetailParam = new ShipmentOrderDetailParam();
            shipmentOrderDetailParam.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
            shipmentOrderDetailDTOList = iRemoteShipmentOrderClient.getDetailList(shipmentOrderDetailParam);
        }
        List<PackCheckSkuAndAllocationDTO> packCheckSkuAndAllocationDTOList = new ArrayList<>();
        //按失效日期和生产批次号排序
        Map<String, List<SkuLotDTO>> allMap = skuLotDTOList.stream().collect(Collectors.groupingBy(it ->
                StrUtil.join("#", it.getSkuCode(), it.getExpireDate(), it.getProductionNo(), it.getValidityCode())));

        List<ShipmentOrderDetailDTO> finalShipmentOrderDetailDTOList = shipmentOrderDetailDTOList;
        allMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(it -> {
            List<SkuLotDTO> lotDTOList = it.getValue();
            List<String> skuLotNoList = lotDTOList.stream().map(SkuLotDTO::getCode).collect(Collectors.toList());
            PackCheckSkuAndAllocationDTO packCheckSkuAndAllocationDTO = new PackCheckSkuAndAllocationDTO();
            packCheckSkuAndAllocationDTO.setSkuCode(lotDTOList.get(0).getSkuCode());
            BigDecimal expQty = allocationOrderDTOList.stream().filter(a -> skuLotNoList.contains(a.getSkuLotNo())).map(AllocationOrderDTO::getExpQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            packCheckSkuAndAllocationDTO.setExpQty(expQty);
            String productionNo = StringUtils.isEmpty(lotDTOList.get(0).getProductionNo()) ? "" : lotDTOList.get(0).getProductionNo();
            packCheckSkuAndAllocationDTO.setProductionNo(productionNo);

            String validityCode = StringUtils.isEmpty(lotDTOList.get(0).getValidityCode()) ? "" : lotDTOList.get(0).getValidityCode();
            packCheckSkuAndAllocationDTO.setValidityCode(validityCode);

            Long expireDate = StringUtils.isEmpty(lotDTOList.get(0).getExpireDate()) ? 0L : lotDTOList.get(0).getExpireDate();
            packCheckSkuAndAllocationDTO.setExpireDate(expireDate);
            packCheckSkuAndAllocationDTO.setSkuTagList(new ArrayList<>());
            if (remoteWarehouseClient.getTaoTianWarehouse(packageDTO.getWarehouseCode())) {
                packCheckSkuAndAllocationDTO.setSkuTagList(buildSpSkuTagList(lotDTOList.get(0).getSkuCode(), finalShipmentOrderDetailDTOList));
            }
            packCheckSkuAndAllocationDTOList.add(packCheckSkuAndAllocationDTO);
        });
        List<PackCheckSkuAndAllocationDTO> checkSkuAndAllocationDTOList = packCheckSkuAndAllocationDTOList.stream()
                .sorted(Comparator.comparing(PackCheckSkuAndAllocationDTO::getSkuCode, Comparator.naturalOrder())
                        .thenComparing(PackCheckSkuAndAllocationDTO::getExpireDate, Comparator.naturalOrder())
                        .thenComparing(PackCheckSkuAndAllocationDTO::getProductionNo, Comparator.naturalOrder())
                        .thenComparing(PackCheckSkuAndAllocationDTO::getValidityCode, Comparator.naturalOrder())
                ).collect(Collectors.toList());
        return checkSkuAndAllocationDTOList;
    }

    @Override
    public List<OutSourceCodeDTO> checkSN(List<SkuDTO> skuList, PackageDTO packageDTO, PackSubmitParam param) {
        List<OutSourceCodeDTO> outSourceCodeDTOList = new ArrayList<>();
        List<SkuDTO> skuDTOList = skuList.stream().filter(a -> a.getSNMgmtOutNeed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return new ArrayList<>();
        }
        List<String> snSkuCodeList = skuDTOList.stream().map(SkuDTO::getCode).collect(Collectors.toList());
        //校验数量是否一致
        BigDecimal expQty = param.getSkuBackParamList().stream().filter(a -> snSkuCodeList.contains(a.getSkuCode())).map(PackSubExpNoParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (expQty.intValue() != param.getSNCodeParamList().size()) {
            throw new BaseException(BaseBizEnum.TIP, "SN提交的数量和需要扫描SN的商品数量不一致");
        }
        List<String> snCodeList = param.getSNCodeParamList().stream().map(SNCodeParam::getSnCode).distinct().collect(Collectors.toList());
        if (snCodeList.size() != param.getSNCodeParamList().size()) {
            throw new BaseException(BaseBizEnum.TIP, "提交SN码存在重复,请核查");
        }
        Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity()));

        //校验库存
        SnStockParam snStockParam = new SnStockParam();
        snStockParam.setSnList(snCodeList);
        snStockParam.setHasQty(true);
        List<SnStockDTO> snStockDTOList = remoteSnStockClient.getList(snStockParam);
        param.getSNCodeParamList().forEach(snCodeParam -> {
            this.checkSkuSnRule(skuDTOMap.get(snCodeParam.getSkuCode()), snCodeParam.getSnCode(), snStockDTOList);
        });
        for (SNCodeParam snCodeParam : param.getSNCodeParamList()) {
            OutSourceCodeDTO outSourceCodeDTO = ConverterUtil.convert(packageDTO, OutSourceCodeDTO.class);
            outSourceCodeDTO.setSkuCode(snCodeParam.getSkuCode());
            outSourceCodeDTO.setUpcCode(snCodeParam.getUpcCode());
            SkuDTO skuDTO = skuDTOMap.getOrDefault(snCodeParam.getSkuCode(), null);
            if (skuDTO != null) {
                outSourceCodeDTO.setSkuName(skuDTO.getName());
            }
            outSourceCodeDTO.setScanType(SourceCodeScanTypeEnum.SN.getCode());
            outSourceCodeDTO.setSnCode(snCodeParam.getSnCode());
            outSourceCodeDTO.setQty(BigDecimal.ONE);
            outSourceCodeDTO.setId(null);
            outSourceCodeDTOList.add(outSourceCodeDTO);
        }
        return outSourceCodeDTOList;
    }

    @Override
    public void checkSkuSnRule(SkuDTO skuDTO, String snCode, List<SnStockDTO> snStockDTOList) {
        if (skuDTO == null || StringUtils.isEmpty(snCode)) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数为空,【商品/SN码】");
        }
        if (CollectionUtils.isEmpty(skuDTO.getRuleRegularExpressionList())) {
            throw new BaseException(BaseBizEnum.TIP, "商品开启出库扫描SN模式,规则为空,请核查");
        }
        if (!PatternCompareUtil.anyMatchRegex(snCode, skuDTO.getRuleRegularExpressionList())) {
            throw new BaseException(BaseBizEnum.TIP, "商品开启出库扫描SN模式,规则不满足,请核查");
        }
        //出入库都有
        if (skuDTO.getSNMgmtOutAndIn()) {
            if (CollectionUtils.isEmpty(snStockDTOList)
                    || snStockDTOList.stream()
                    .filter(it -> it.getCargoCode().equalsIgnoreCase(skuDTO.getCargoCode()))
                    .filter(it -> it.getSkuCode().equalsIgnoreCase(skuDTO.getCode()))
                    .noneMatch(a -> Objects.equals(snCode, a.getSn()))) {
                throw new BaseException(BaseBizEnum.TIP, "当前货主商品下,SN开启出入库模式,当前SN无库存,请核查");
            }
        }
        //出库有 无入库
        if (skuDTO.getSNMgmtOutAndXT()) {

        }
    }

    @Override
    public List<MaterialUseRecordDTO> buildMaterialUseRecordByGift(String pickCode, PackageDTO packageDTO) {
        GiftPickSnapshotParam giftPickSnapshotParam = new GiftPickSnapshotParam();
        giftPickSnapshotParam.setPickCode(pickCode);
        giftPickSnapshotParam.setPackageCode(packageDTO.getPackageCode());
        List<GiftPickSnapshotDTO> giftPickSnapshotDTOList = remoteGiftPickSnapshotClient.getList(giftPickSnapshotParam);
        if (CollectionUtils.isEmpty(giftPickSnapshotDTOList)) {
            return Lists.newArrayList();
        }
        Map<String, List<GiftPickSnapshotDTO>> giftPickMap = giftPickSnapshotDTOList.stream()
                .collect(Collectors.groupingBy(it -> StrUtil.join("#", it.getCargoCode(), it.getSkuCode(), it.getBarCode())));
        List<MaterialUseRecordDTO> materialUseRecordDTOList = new ArrayList<>();
        for (Map.Entry<String, List<GiftPickSnapshotDTO>> entity : giftPickMap.entrySet()) {

            List<GiftPickSnapshotDTO> pickSnapshotDTOList = entity.getValue();

            GiftPickSnapshotDTO giftPickSnapshotDTO = pickSnapshotDTOList.get(0);

            MaterialUseRecordDTO materialUseRecordDTO = new MaterialUseRecordDTO();
            materialUseRecordDTO.setWarehouseCode(giftPickSnapshotDTO.getWarehouseCode());
            materialUseRecordDTO.setCargoCode(giftPickSnapshotDTO.getCargoCode());
            materialUseRecordDTO.setPackageCode(giftPickSnapshotDTO.getPackageCode());
            materialUseRecordDTO.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
            materialUseRecordDTO.setPoNo(packageDTO.getPoNo());
            materialUseRecordDTO.setSoNo(packageDTO.getSoNo());
            materialUseRecordDTO.setSkuCode(giftPickSnapshotDTO.getSkuCode());
            materialUseRecordDTO.setScanTag(SkuMaterialScanTagEnum.NO_SCAN.getCode());
            materialUseRecordDTO.setMaterialCode(giftPickSnapshotDTO.getMaterialCode());
            materialUseRecordDTO.setBarCode(giftPickSnapshotDTO.getBarCode());
            materialUseRecordDTO.setIs4pl(Is4PLEnum.NO.getCode());
            materialUseRecordDTO.setMaterialAddWeight(SkuMaterialAddWeightEnum.SKU_ADD_WEIGHT_YES.getCode());
            materialUseRecordDTO.setStatus(MaterialUseRecordStatusEnum.ENABLED.getCode());
            BigDecimal needQty = pickSnapshotDTOList.stream().map(GiftPickSnapshotDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            materialUseRecordDTO.setMaterialNum(needQty.intValue());
            materialUseRecordDTOList.add(materialUseRecordDTO);
        }
        return materialUseRecordDTOList;
    }

    @Override
    public List<ShipmentDetailMaterialOtherDTO> buildTaoTianMaterialInfo(PackageDTO packageDTO) {

        List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = iRemoteShipmentOrderClient.getDetailList(packageDTO.getShipmentOrderCode());

        List<ShipmentDetailMaterialOtherDTO> shipmentDetailMaterialDTOList = buildShipmentDetailMaterial(shipmentOrderDetailDTOList);

        Map<String, List<ShipmentDetailMaterialOtherDTO>> skuShipDetailMap = shipmentDetailMaterialDTOList.stream().collect(Collectors.groupingBy(ShipmentDetailMaterialOtherDTO::getSkuCode));
        //处理数据
        skuShipDetailMap.forEach((skuCode, shipmentDetailMaterialList) -> {
            List<ShipmentDetailMaterialOtherDTO> shipmentDetailMaterialCurrentList = shipmentDetailMaterialList;
            Set<String> sameLineCargoMaterialMap = new HashSet<>();
            // ========================= 第一步 先找出货主级别的========================
            shipmentDetailMaterialCurrentList.stream().forEach(it -> {
                List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> cargoMaterialList = new ArrayList<>();
                List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> skuConsumablesMaterials = it.getSkuConsumablesMaterials();
                skuConsumablesMaterials.forEach(skuMaterial -> {
                    if (StrUtil.isNotBlank(skuMaterial.getLeftNum())
                            && Integer.valueOf(skuMaterial.getLeftNum()) > 0) {
//                        ShipmentOrderDetailExtraJsonConsumableInfoDTO shipmentOrderDetailExtraJsonConsumableInfoDTO = new ShipmentOrderDetailExtraJsonConsumableInfoDTO();
//                        shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesCode(skuMaterial.getConsumablesCode());
//                        Integer sumQty = Integer.valueOf(skuMaterial.getConsumablesNum()) * it.getExpSkuQty().intValue() + Integer.valueOf(skuMaterial.getLeftNum());
//                        shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesNum(sumQty + "");
//                        shipmentOrderDetailExtraJsonConsumableInfoDTO.setLeftNum("0");
//                        cargoMaterialList.add(shipmentOrderDetailExtraJsonConsumableInfoDTO);
                        String sameLineCargoMaterialKey = StrUtil.join("#", it.getLineSeq(), skuMaterial.getConsumablesCode());
                        if (sameLineCargoMaterialMap.contains(sameLineCargoMaterialKey)) {
                            ShipmentOrderDetailExtraJsonConsumableInfoDTO shipmentOrderDetailExtraJsonConsumableInfoDTO = new ShipmentOrderDetailExtraJsonConsumableInfoDTO();
                            shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesCode(skuMaterial.getConsumablesCode());
                            Integer sumQty = Integer.valueOf(skuMaterial.getConsumablesNum()) * it.getExpSkuQty().intValue();
                            shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesNum(sumQty + "");
                            shipmentOrderDetailExtraJsonConsumableInfoDTO.setLeftNum("0");
                            cargoMaterialList.add(shipmentOrderDetailExtraJsonConsumableInfoDTO);
                        } else {
                            ShipmentOrderDetailExtraJsonConsumableInfoDTO shipmentOrderDetailExtraJsonConsumableInfoDTO = new ShipmentOrderDetailExtraJsonConsumableInfoDTO();
                            shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesCode(skuMaterial.getConsumablesCode());
                            Integer sumQty = Integer.valueOf(skuMaterial.getConsumablesNum()) * it.getExpSkuQty().intValue() + Integer.valueOf(skuMaterial.getLeftNum());
                            shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesNum(sumQty + "");
                            shipmentOrderDetailExtraJsonConsumableInfoDTO.setLeftNum("0");
                            cargoMaterialList.add(shipmentOrderDetailExtraJsonConsumableInfoDTO);
                            sameLineCargoMaterialMap.add(sameLineCargoMaterialKey);
                        }
                    }
                });
                //移除货主级别
                skuConsumablesMaterials.removeIf(removeSkuMaterial -> (StrUtil.isNotBlank(removeSkuMaterial.getLeftNum())
                        && Integer.valueOf(removeSkuMaterial.getLeftNum()) > 0));
                //
                if (CollectionUtils.isEmpty(skuConsumablesMaterials)) {
                    it.setSkuConsumablesMaterials(new ArrayList<>());
                } else {
                    it.setMaterialCodeAndNum(skuConsumablesMaterials.stream().map(ShipmentOrderDetailExtraJsonConsumableInfoDTO::getConsumablesCodeAndNum).collect(Collectors.toList()));
                }
                it.setCargoConsumablesMaterials(cargoMaterialList);
            });
            // ========================= 第一步 先找出货主级别的========================

            // ========================= 第二步 同行号货主维度合并========================
//            System.out.println("_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+");
//            System.out.println(JSONUtil.toJsonStr(shipmentDetailMaterialCurrentList));
            //行号集合
            Map<String, List<ShipmentDetailMaterialOtherDTO>> lineSeqMap = shipmentDetailMaterialCurrentList.stream().collect(Collectors.groupingBy(ShipmentDetailMaterialOtherDTO::getLineSeq));
            lineSeqMap.forEach((lineSeq, lineSeqList) -> {
                List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> lineSeqMaterialList = new ArrayList<>();
                lineSeqList.forEach(it -> {
                    if (!CollectionUtils.isEmpty(it.getCargoConsumablesMaterials())) {
                        lineSeqMaterialList.addAll(it.getCargoConsumablesMaterials());
                    }
                });
                //相同耗材编码的求和
                List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> cargoLineSeqList = new ArrayList<>();
                lineSeqMaterialList.stream().map(ShipmentOrderDetailExtraJsonConsumableInfoDTO::getConsumablesCode).distinct().forEach(otherLine -> {
                    ShipmentOrderDetailExtraJsonConsumableInfoDTO shipmentOrderDetailExtraJsonConsumableInfoDTO = new ShipmentOrderDetailExtraJsonConsumableInfoDTO();
                    shipmentOrderDetailExtraJsonConsumableInfoDTO.setLeftNum("0");
                    shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesCode(otherLine);
                    int sum = lineSeqMaterialList.stream().filter(a -> Objects.equals(a.getConsumablesCode(), otherLine))
                            .map(ShipmentOrderDetailExtraJsonConsumableInfoDTO::getConsumablesNum).mapToInt(Integer::parseInt).sum();
                    shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesNum(sum + "");
                    shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesCodeAndNum("");
                    cargoLineSeqList.add(shipmentOrderDetailExtraJsonConsumableInfoDTO);
                });
                for (int i = 0; i < lineSeqList.size(); i++) {
                    if (i == 0) {
                        lineSeqList.get(i).setCargoConsumablesMaterials(cargoLineSeqList);
                    } else {
                        lineSeqList.get(i).setCargoConsumablesMaterials(new ArrayList<>());
                    }
                }
            });
            //
//            System.out.println("_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+_+");
            // ========================= 第二步 同行号货主维度合并========================

            // ========================= 第三步 同商品需要找出有差异的耗材========================
            Set<String> sameMaterialUnit = new HashSet<>();
            if (shipmentDetailMaterialCurrentList.size() > 1) {
                int j = 0;
                for (int i = 0; i < shipmentDetailMaterialCurrentList.size(); i++) {
                    //同商品存在空,全是货主级别
                    if (CollectionUtils.isEmpty(shipmentDetailMaterialCurrentList.get(i).getMaterialCodeAndNum())) {
                        sameMaterialUnit = new HashSet<>();
                        break;
                    }
                    if (!CollectionUtils.isEmpty(shipmentDetailMaterialCurrentList.get(i).getMaterialCodeAndNum())) {
                        if (j == 0) {
                            sameMaterialUnit.addAll(shipmentDetailMaterialCurrentList.get(i).getMaterialCodeAndNum());
                        } else {
                            sameMaterialUnit.retainAll(shipmentDetailMaterialCurrentList.get(i).getMaterialCodeAndNum());
                        }
                        j++;
                    }
                }
                //全是货主级别
                if (CollectionUtils.isEmpty(sameMaterialUnit)) {
                    shipmentDetailMaterialCurrentList.forEach(it -> {
                        if (!CollectionUtils.isEmpty(it.getSkuConsumablesMaterials())) {
                            List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> cargoConsumablesMaterials = it.getCargoConsumablesMaterials();
                            it.getSkuConsumablesMaterials().forEach(ms -> {
                                Integer sumQty = Integer.valueOf(ms.getConsumablesNum()) * it.getExpSkuQty().intValue();
                                ms.setConsumablesNum(sumQty + "");
                                ms.setLeftNum("0");
                            });
                            cargoConsumablesMaterials.addAll(it.getSkuConsumablesMaterials());
                            it.setCargoConsumablesMaterials(cargoConsumablesMaterials);
                        }
                        it.setSkuConsumablesMaterials(new ArrayList<>());
                    });
                } else {
                    Set<String> finalSameMaterialUnit = sameMaterialUnit;
                    shipmentDetailMaterialCurrentList.forEach(it -> {
                        //商品维度
                        List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> skuConsumablesMaterials = it.getSkuConsumablesMaterials();
                        it.setSkuConsumablesMaterials(it.getSkuConsumablesMaterials()
                                .stream().filter(a -> finalSameMaterialUnit.contains(a.getConsumablesCodeAndNum())).collect(Collectors.toList()));
                        //货主维度
                        List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> cargoConsumablesMaterials = it.getCargoConsumablesMaterials();

                        List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> otherCargoList = skuConsumablesMaterials
                                .stream().filter(a -> !finalSameMaterialUnit.contains(a.getConsumablesCodeAndNum())).collect(Collectors.toList());
                        otherCargoList.forEach(otherCargo -> {
                            ShipmentOrderDetailExtraJsonConsumableInfoDTO shipmentOrderDetailExtraJsonConsumableInfoDTO = new ShipmentOrderDetailExtraJsonConsumableInfoDTO();
                            shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesCode(otherCargo.getConsumablesCode());
                            Integer sumQty = Integer.valueOf(otherCargo.getConsumablesNum()) * it.getExpSkuQty().intValue();
                            shipmentOrderDetailExtraJsonConsumableInfoDTO.setConsumablesNum(sumQty + "");
                            shipmentOrderDetailExtraJsonConsumableInfoDTO.setLeftNum("0");
                            cargoConsumablesMaterials.add(shipmentOrderDetailExtraJsonConsumableInfoDTO);
                        });
                        it.setCargoConsumablesMaterials(cargoConsumablesMaterials);
                    });
                }
            }
            // ========================= 第三步 同商品需要找出有差异的耗材========================
        });
        log.info("shipmentDetailMaterialDTOList:{}", JSONUtil.toJsonStr(shipmentDetailMaterialDTOList));
        return shipmentDetailMaterialDTOList;
    }

    @Override
    public List<SkuMaterialDTO> buildTaoTianMaterial(PackageDTO packageDTO, List<String> skuCodeList, List<ShipmentDetailMaterialOtherDTO> detailMaterialOtherDTOList) {
        List<SkuMaterialDTO> skuMaterialDTOList = new ArrayList<>();
        for (ShipmentDetailMaterialOtherDTO shipmentOrderDetailDTO : detailMaterialOtherDTOList) {
            //拿到里面的外部编码查询PackageMaterialParam  然后拿Code反过来拼装skuMaterialDTOList
            PackageMaterialParam packageMaterialParam = new PackageMaterialParam();
            packageMaterialParam.setCargoCode(packageDTO.getCargoCode());

            List<String> outCodeList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(shipmentOrderDetailDTO.getCargoConsumablesMaterials())) {
                outCodeList.addAll(shipmentOrderDetailDTO.getCargoConsumablesMaterials().stream().map(ShipmentOrderDetailExtraJsonConsumableInfoDTO::getConsumablesCode).distinct().collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(shipmentOrderDetailDTO.getSkuConsumablesMaterials())) {
                outCodeList.addAll(shipmentOrderDetailDTO.getSkuConsumablesMaterials().stream().map(ShipmentOrderDetailExtraJsonConsumableInfoDTO::getConsumablesCode).distinct().collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(outCodeList)) {
                packageMaterialParam.setOutCodeList(outCodeList);
                List<PackageMaterialDTO> packageMaterialList = iRemotePackageMaterialClient.getList(packageMaterialParam);
                if (!CollectionUtils.isEmpty(packageMaterialList)) {
                    Map<String, PackageMaterialDTO> materialDTOMap = packageMaterialList.stream().collect(Collectors.toMap(PackageMaterialDTO::getOutCode, Function.identity(), BinaryOperator.minBy(Comparator.comparing(PackageMaterialDTO::getCode))));
                    //货主维度
                    if (!CollectionUtils.isEmpty(shipmentOrderDetailDTO.getCargoConsumablesMaterials())) {
                        for (ShipmentOrderDetailExtraJsonConsumableInfoDTO extraJsonConsumableInfoDTO : shipmentOrderDetailDTO.getCargoConsumablesMaterials()) {
                            if (materialDTOMap.containsKey(extraJsonConsumableInfoDTO.getConsumablesCode())) {
                                PackageMaterialDTO packageMaterialDTO = materialDTOMap.get(extraJsonConsumableInfoDTO.getConsumablesCode());
                                //暂不考虑淘天货主和商品级别同时存在
                                SkuMaterialDTO skuMaterialDTOCargo = skuMaterialDTOList.stream().filter(a -> Objects.equals(a.getSkuCode(), shipmentOrderDetailDTO.getSkuCode()))
                                        .filter(a -> Objects.equals(a.getMaterialCode(), packageMaterialDTO.getCode()))
                                        .findFirst().orElse(null);
                                if (skuMaterialDTOCargo == null) {
                                    SkuMaterialDTO skuMaterialDTO = new SkuMaterialDTO();
                                    skuMaterialDTO.setWarehouseCode(packageDTO.getWarehouseCode());
                                    skuMaterialDTO.setCargoCode(packageDTO.getCargoCode());
                                    skuMaterialDTO.setSkuMaterialTip(false);
                                    skuMaterialDTO.setMaterialCode(packageMaterialDTO.getCode());
                                    skuMaterialDTO.setBarCode(packageMaterialDTO.getBarCode());
                                    skuMaterialDTO.setSkuCode(shipmentOrderDetailDTO.getSkuCode());
                                    skuMaterialDTO.setMaterialNum(Integer.valueOf(extraJsonConsumableInfoDTO.getConsumablesNum()));
                                    skuMaterialDTOList.add(skuMaterialDTO);
                                } else {
                                    skuMaterialDTOCargo.setMaterialNum(skuMaterialDTOCargo.getMaterialNum() + Integer.valueOf(extraJsonConsumableInfoDTO.getConsumablesNum()));
                                }
                            }
                        }
                    }
                    //商品维度
                    if (!CollectionUtils.isEmpty(shipmentOrderDetailDTO.getSkuConsumablesMaterials())) {
                        for (ShipmentOrderDetailExtraJsonConsumableInfoDTO extraJsonConsumableInfoDTO : shipmentOrderDetailDTO.getSkuConsumablesMaterials()) {
                            if (materialDTOMap.containsKey(extraJsonConsumableInfoDTO.getConsumablesCode())) {
                                PackageMaterialDTO packageMaterialDTO = materialDTOMap.get(extraJsonConsumableInfoDTO.getConsumablesCode());
                                if (skuMaterialDTOList.stream().noneMatch(a -> Objects.equals(a.getSkuCode(), shipmentOrderDetailDTO.getSkuCode())
                                        && Objects.equals(a.getMaterialCode(), packageMaterialDTO.getCode()))) {
                                    SkuMaterialDTO skuMaterialDTO = new SkuMaterialDTO();
                                    skuMaterialDTO.setWarehouseCode(packageDTO.getWarehouseCode());
                                    skuMaterialDTO.setCargoCode(packageDTO.getCargoCode());
                                    skuMaterialDTO.setSkuMaterialTip(true);
                                    skuMaterialDTO.setMaterialCode(packageMaterialDTO.getCode());
                                    skuMaterialDTO.setBarCode(packageMaterialDTO.getBarCode());
                                    skuMaterialDTO.setSkuCode(shipmentOrderDetailDTO.getSkuCode());
                                    skuMaterialDTO.setMaterialNum(Integer.valueOf(extraJsonConsumableInfoDTO.getConsumablesNum()));
                                    skuMaterialDTOList.add(skuMaterialDTO);
                                }
                            }
                        }
                    }
                }
            }

        }
        return skuMaterialDTOList;
    }

    @Override
    public String commitB2BPackNew(PackageCheckDTO packageCheckDTO, PickDTO pickDTO, PackageDTO packageDTO) {
        //原明细数据
        List<PackageDetailDTO> originPackDetailList = packageDTO.getListDetail().stream()
                .filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode()))
                .collect(Collectors.toList());
        //复核记录转换成前端提交数据
        List<PackSubExpNoParam> skuBackParamList = buildPackSubExpNoParamB2B(packageCheckDTO.getDetailDTOList());
        //分配单原始数据
        List<AllocationOrderDTO> originAllocationList = getAllocationOrderByPackAndWaveCode(packageDTO.getPackageCode(), packageDTO.getWaveCode());
        //分配单新数据
        List<AllocationOrderDTO> newAllocationList = new ArrayList<>();
        //组包包裹明细
        List<PackageDetailDTO> newPackDetailList = new ArrayList<>();
        PackageDTO newPack = ConverterUtil.convert(packageDTO, PackageDTO.class);
        newPack.setId(null);
        newPack.setVersion(null);
        newPack.setExpressNo(null);
        newPack.setActualPackUpc(packageDTO.getActualPackUpc());
        packageDTO.setActualPackUpc(null);
        packageDTO.setActualPackWeight(null);
        newPack.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        //新包裹号
        newPack.setPackageCode(iRemoteSeqRuleClient.findSequence(SeqEnum.PACK_CODE_000001));
        //拆分分配和包裹明细
        splitPackDetailAndAllocation(skuBackParamList, newPackDetailList, originPackDetailList, newAllocationList, originAllocationList, packageDTO, newPack);
        //修正包裹数据
        packageDTO.setPackageStruct(analysisPackageStruct(originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList())));
        packageDTO.setListDetail(originPackDetailList);
        packageDTO.setPackageSkuQty(originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        packageDTO.setSkuTypeQty((int) originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).map(PackageDetailDTO::getSkuCode).distinct().count());
        //新包裹所有的明细
        List<PackageDetailDTO> tempPackDetailList = new ArrayList<>();
        tempPackDetailList.addAll(newPackDetailList);
        tempPackDetailList.addAll(originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), newPack.getPackageCode())).collect(Collectors.toList()));
        newPack.setPackageStruct(analysisPackageStruct(tempPackDetailList));

        newPack.setListDetail(newPackDetailList);
        newPack.setPackageSkuQty(newPackDetailList.stream().map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add).add(originPackDetailList.stream().filter(a -> Objects.equals(a.getPackageCode(), newPack.getPackageCode())).map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add)));
        newPack.setSkuTypeQty((int) tempPackDetailList.stream().map(PackageDetailDTO::getSkuCode).distinct().count());
        newPack.setOriginPackageCode(packageDTO.getPackageCode());

        //回写出库订单数据
        StringBuilder saveShipmentLog = new StringBuilder();
        ShipmentOrderDTO shipmentOrderDTO = modifyShipment(newPack.getShipmentOrderCode(), tempPackDetailList, saveShipmentLog);
        //回写拣选单数据 -------------
        PackageMaterialDTO packageMaterialDTO = iRemotePackageMaterialClient.queryByUpcCode(newPack.getCargoCode(), newPack.getActualPackUpc());
        newPack.setActualPackNum(1);
        newPack.setActualPackWeight(packageMaterialDTO.getGrossWeight());
        //包裹重量计算
        List<String> skuCodeList = tempPackDetailList.stream().flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList());
        SkuParam skuParam = new SkuParam();
        skuParam.setCodeList(skuCodeList);
        skuParam.setCargoCode(packageDTO.getCargoCode());
        List<SkuDTO> skuList = iRemoteSkuClient.getList(skuParam);

        //回写拣选单数据
        pickDTO.setPackageQty(pickDTO.getPackageQty() + 1);
        List<PickDetailDTO> pickDetailDTOList = pickDTO.getDetailList().stream()
                .filter(a -> Objects.equals(a.getPackageCode(), packageDTO.getPackageCode())).collect(Collectors.toList());
        pickDetailDTOList.forEach(a -> {
            a.setQty(packageDTO.getPackageSkuQty());
            a.setPickQty(packageDTO.getPackageSkuQty());
        });
        PickDetailDTO pickDetailDTO = ConverterUtil.convert(pickDetailDTOList.get(0), PickDetailDTO.class);
        pickDetailDTO.setId(null);
        pickDetailDTO.setVersion(null);
        pickDetailDTO.setPackageCode(newPack.getPackageCode());
        pickDetailDTO.setPackageStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        pickDetailDTO.setQty(newPack.getPackageSkuQty());
        pickDetailDTO.setPickQty(newPack.getPackageSkuQty());
        pickDetailDTO.setCheckQty(newPack.getPackageSkuQty());

        //计算重量
        iCheckPackWeightAndVolumeBiz.calculationVolumetricAndWeight(newPack, tempPackDetailList, skuList);

        CarrierDTO carrierDTO = iRemoteCarrierClient.queryByCode(packageDTO.getCarrierCode());
        if (carrierDTO != null && carrierDTO.getIsHttp().equals(IsHttpEnum.ENABLE.getValue())) {
            //TODO 组装快递参数
            newPack.setExpressNo("");
            BigDecimal weight = weightBiz.weight(MaterialCalculateParam.builder()
                    .warehouseCode(newPack.getWarehouseCode())
                    .cargoCode(newPack.getCargoCode())
                    .is4PL(OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.FOUR_TAG) ? Is4PLEnum.YES : Is4PLEnum.NO)
                    .appointMaterialWeight(packageMaterialDTO.getGrossWeight())
                    .skuDetailList(tempPackDetailList.stream()
                            .filter(it -> it.getIsPre().equals(PackIsPreEnum.NORMAL.getCode()))
                            .map(it -> MaterialCalculateParam.SkuDetail.builder()
                                    .skuCode(it.getSkuCode())
                                    .quantity(it.getSkuQty())
                                    .build()).collect(Collectors.toList()))
                    .build());
            wmsTenantHelper.setTenantId(shipmentOrderDTO.getWarehouseCode(), shipmentOrderDTO.getCargoCode());
            ExpressResultDTO expressResultDTO = iRemoteExpressClient.getExpressResult(shipmentOrderDTO.getShipmentOrderCode(), iRemoteExpressClient.getExpressParamV2(shipmentOrderDTO, carrierDTO, newPack, tempPackDetailList, weight));
            if (StringUtils.isEmpty(expressResultDTO)) {
                throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
            }
            if (!expressResultDTO.getSuccess()) {
                String errorMessage = StringUtils.isEmpty(expressResultDTO.getMessage()) ? "未获取到快递单号" : expressResultDTO.getMessage();
                //[\u4E00-\u9FA5]是unicode2的中文区间
                Matcher matcher = pattern.matcher(errorMessage);
                //提取中文字符
                errorMessage = matcher.replaceAll("");
                errorMessage = StringUtils.isEmpty(errorMessage) ? "未获取到快递单号" : errorMessage;
                throw new BaseException(BaseBizEnum.TIP, errorMessage);
            }
            if (CollectionUtils.isEmpty(expressResultDTO.getWaybill_apply_info())) {
                throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
            }
            if (StringUtils.isEmpty(expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code())) {
                throw new BaseException(BaseBizEnum.TIP, "未获取到快递单号");
            }
            String billNo = checkBillNoBiz.checkOutboundExpressNoEffective(carrierDTO.getCode(), expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code());
            if (!StringUtils.isEmpty(billNo)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拉取运单号,系统已存在有效状态的运单%s包裹，不允许拆包", expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code()));
            }
            newPack.setExpressNo(expressResultDTO.getWaybill_apply_info().get(0).getWaybill_code());
            newPack.setExpressBranch(expressResultDTO.getWaybill_apply_info().get(0).getExpressBranchCode());
            newPack.setExpressBranchName(expressResultDTO.getWaybill_apply_info().get(0).getExpressBranchName());
        } else {
            String expressNo = packageDTO.getExpressNo() + "-" + newPack.getPackageCode().substring(newPack.getPackageCode().length() - 4);
            String billNo = checkBillNoBiz.checkOutboundExpressNoEffective(carrierDTO.getCode(), expressNo);
            if (!StringUtils.isEmpty(billNo)) {
                throw new BaseException(BaseBizEnum.TIP, "非电子面单,系统已存在有效状态的运单包裹，不允许拆包");
            }
            newPack.setExpressNo(expressNo);
        }
        pickDetailDTO.setExpressNo(newPack.getExpressNo());
        pickDetailDTOList.add(pickDetailDTO);

        packageCheckDTO.setStatus(PackCheckEnum.STATUS.COMPLETE_STATUS.getCode());
        packageCheckDTO.getDetailDTOList().forEach(a -> {
            a.setStatus(PackCheckEnum.STATUS.COMPLETE_STATUS.getCode());
            a.setCheckQty(a.getQty());
        });
        //包裹理论重量信息
        newPack.setWeight(
                tempPackDetailList.stream()
                        .filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode()))
                        .flatMap(a -> {
                            SkuDTO sku = skuList.stream().filter(b -> b.getCode().equals(a.getSkuCode())).findAny().get();
                            return Stream.of(a.getSkuQty().multiply(sku.getGrossWeight()));
                        }).reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        //包裹理论体积信息
        newPack.setVolume(
                tempPackDetailList.stream()
                        .filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode()))
                        .flatMap(a -> {
                            SkuDTO sku = skuList.stream().filter(b -> b.getCode().equals(a.getSkuCode())).findAny().get();
                            BigDecimal _bigDecimal = a.getSkuQty().multiply(sku.getLength().multiply(sku.getWidth()).multiply(sku.getHeight()));
                            if (_bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
                                _bigDecimal = sku.getVolume().multiply(a.getSkuQty());
                            }
                            return Stream.of(_bigDecimal == null ? BigDecimal.ZERO : _bigDecimal);
                        }).reduce(BigDecimal.ZERO, BigDecimal::add));
        iRemotePackageClient.handlePackageVolume(newPack);

        //校验重量
        iCheckPackWeightAndVolumeBiz.checkPackageWeightAndVolumeNew(newPack, tempPackDetailList, CommonConstantUtil.WMS_WEB_WEIGHT);

        CommitPackBO commitPackBO = new CommitPackBO();
        commitPackBO.setOriginAllocations(originAllocationList);
        commitPackBO.setNewAllocations(newAllocationList);
        commitPackBO.setOriginPack(packageDTO);
        newPack.setBoxNo(packageCheckDTO.getBoxNo());
        newPack.setCheckCompleteDate(System.currentTimeMillis());

        //计算体积重
        iCheckPackWeightAndVolumeBiz.calculationVolumetricWeight(newPack);

        commitPackBO.setNewPack(newPack);
        commitPackBO.setPickDTOList(Arrays.asList(pickDTO));
        commitPackBO.setPickDetailDTOList(pickDetailDTOList);
        commitPackBO.setShipmentOrderDTO(shipmentOrderDTO);
        //组装复核数据
        packageCheckDTO.setPackageCode(newPack.getPackageCode());
        packageCheckDTO.setExpressNo(newPack.getExpressNo());
        packageCheckDTO.setActualPackUpc(newPack.getActualPackUpc());
        packageCheckDTO.setCheckQty(packageCheckDTO.getQty());
        commitPackBO.setPackageCheckDTO(packageCheckDTO);

        log.info("B2B Check Submit:{}", JSONUtil.toJsonStr(commitPackBO));

        String key = pickDTO.getWarehouseCode() + "" + pickDTO.getPickCode() + "" + packageDTO.getPackageCode();
        RLock lock = redissonClient.getLock("dt_wms_back_pack_submit_lock:" + key);
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(1, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "单据操作中,请稍后");
            }
            SystemEventDTO systemEventDTO = buildSystemEventDTO(commitPackBO.getNewPack());
            commitPackBO.setSystemEventDTO(systemEventDTO);
            iRemoteBillContextClient.commitPackBO(commitPackBO);
            List<PackageLogDTO> packageLogDTOS = new ArrayList<>();
            //原包裹日志
            PackageLogDTO packageLogDTO = new PackageLogDTO();
            packageLogDTO.setCargoCode(packageDTO.getCargoCode());
            packageLogDTO.setPackageCode(packageDTO.getPackageCode());
            packageLogDTO.setWarehouseCode(packageDTO.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setOpContent(String.format("包裹完成拆包,单号:%s,新包裹号:%s", packageDTO.getPackageCode(), newPack.getPackageCode()));
            packageLogDTOS.add(packageLogDTO);

            packageLogDTO = new PackageLogDTO();
            packageLogDTO.setCargoCode(newPack.getCargoCode());
            packageLogDTO.setPackageCode(newPack.getPackageCode());
            packageLogDTO.setWarehouseCode(newPack.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setOpContent(String.format("拆包包裹创建,单号:%s", newPack.getPackageCode()));
            packageLogDTOS.add(packageLogDTO);
            iBusinessLogBiz.savePackLogList(packageLogDTOS);
            if (saveShipmentLog.toString().equals("30")) {
                iBusinessLogBiz.saveShipmentLog(packageDTO.getWarehouseCode(),
                        packageDTO.getCargoCode(),
                        packageDTO.getShipmentOrderCode(),
                        CurrentUserHolder.getUserName(),
                        String.format("出库单复核完成,单号:%s", packageDTO.getShipmentOrderCode()));
            }
        } catch (Exception e) {
            log.error("包裹复核异常：e:{}", e.getMessage());
            String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage();
            throw new BaseException(BaseBizEnum.TIP, errorMsg);
        } finally {
            lock.unlock();
        }
        return newPack.getPackageCode();
    }

    private List<PackSubExpNoParam> buildPackSubExpNoParamB2B(List<PackageCheckDetailDTO> detailDTOList) {
        List<PackSubExpNoParam> packSubExpNoParamList = new ArrayList<>();
        for (PackageCheckDetailDTO entity : detailDTOList) {
            PackSubExpNoParam packSubExpNoParam = new PackSubExpNoParam();
            packSubExpNoParam.setQty(entity.getQty());
            packSubExpNoParam.setExpireDate(entity.getExpireDate());
            packSubExpNoParam.setProductionNo(entity.getProductionNo());
            packSubExpNoParam.setValidityCode(entity.getValidityCode());
            packSubExpNoParam.setSkuCode(entity.getSkuCode());
            packSubExpNoParamList.add(packSubExpNoParam);
        }
        return packSubExpNoParamList;
    }

    private static List<ShipmentDetailMaterialOtherDTO> buildShipmentDetailMaterial(List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList) {
        List<ShipmentDetailMaterialOtherDTO> shipmentDetailMaterialDTOList = new ArrayList<>();
        shipmentOrderDetailDTOList.forEach(shipmentDetailDTO -> {
            ShipmentDetailMaterialOtherDTO shipmentDetailMaterialDTO = new ShipmentDetailMaterialOtherDTO();
            shipmentDetailMaterialDTO.setSkuCode(shipmentDetailDTO.getSkuCode());
            shipmentDetailMaterialDTO.setLineSeq(shipmentDetailDTO.getLineSeq());
            shipmentDetailMaterialDTO.setExpSkuQty(shipmentDetailDTO.getExpSkuQty());
            shipmentDetailMaterialDTO.setSkuConsumablesMaterials(new ArrayList<>());
            if (shipmentDetailDTO.getExtraJsonObject() != null && !CollectionUtils.isEmpty(shipmentDetailDTO.getExtraJsonObject().getConsumablesMaterials())) {
                List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> consumablesMaterials = shipmentDetailDTO.getExtraJsonObject().getConsumablesMaterials();
                consumablesMaterials.forEach(it -> {
                    if (StrUtil.isBlank(it.getLeftNum())) {
                        it.setLeftNum("0");
                    }
                    it.setConsumablesCodeAndNum(StrUtil.join("#", it.getConsumablesCode(), it.getConsumablesNum()));
                });
                shipmentDetailMaterialDTO.setSkuConsumablesMaterials(consumablesMaterials);
            }
            shipmentDetailMaterialDTO.setSkuCode(shipmentDetailDTO.getSkuCode());
            shipmentDetailMaterialDTOList.add(shipmentDetailMaterialDTO);
        });
        return shipmentDetailMaterialDTOList;
    }

    private List<String> buildSpSkuTagList(String skuCode, List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList) {
        //【coldChain=是否冷链】、【precious=是否贵品】、需要在商品编码旁打标记 【冷链】【贵品】
        List<String> spTagList = new ArrayList<>();
        shipmentOrderDetailDTOList.stream().filter(a -> Objects.equals(a.getSkuCode(), skuCode))
                .findFirst().ifPresent(it -> {
                    if (it.getColdChainTag()) {
                        spTagList.add("冷链");
                    }
                    if (it.getPreciousTag()) {
                        spTagList.add("贵品");
                    }
                });
        return spTagList;
    }

    /**
     * @param cargoCode
     * @param preUpcCode
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     * @author: WuXian
     * description: 获取预包条码的子商品需要的结构数量
     * create time: 2021/8/27 15:42
     */
    private Map<String, BigDecimal> getSkuNeedQty(String cargoCode, String preUpcCode) {
        PrePackageSkuDetailParam prePackageSkuDetailParam = new PrePackageSkuDetailParam();
        prePackageSkuDetailParam.setCargoCode(cargoCode);
        prePackageSkuDetailParam.setPreUpcCode(preUpcCode);
        List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOList = remotePrePackageSkuDetailClient.getList(prePackageSkuDetailParam);
        if (CollectionUtils.isEmpty(prePackageSkuDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到预包条码的子商品数据");
        }
        //使用有序Map
        Map<String, BigDecimal> skuNeedQtyMap = new TreeMap<>();
        prePackageSkuDetailDTOList.stream().sorted(Comparator.comparing(PrePackageSkuDetailDTO::getSkuQty, Comparator.naturalOrder())
                .thenComparing(PrePackageSkuDetailDTO::getSkuCode, Comparator.naturalOrder())).forEach(entity -> {
            skuNeedQtyMap.putIfAbsent(entity.getSkuCode(), entity.getSkuQty());
        });
        if (CollectionUtils.isEmpty(skuNeedQtyMap)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到预包条码的子商品数据");
        }
        return skuNeedQtyMap;
    }

    /**
     * 组装系统事件对象
     *
     * @param packageDTO
     * @return
     */
    private SystemEventDTO buildSystemEventDTO(PackageDTO packageDTO) {
        SystemEventDTO systemEventDTO = new SystemEventDTO();
        systemEventDTO.setType(SystemEventEnum.PACK_REVIEW.getCode());
        systemEventDTO.setWarehouseCode(packageDTO.getWarehouseCode());
        systemEventDTO.setCargoCode(packageDTO.getCargoCode());
        systemEventDTO.setWorker(CurrentUserHolder.getUserName());
        systemEventDTO.setWorkTime(System.currentTimeMillis());
        systemEventDTO.setWorkDate(DateUtil.parse(DateUtil.date(System.currentTimeMillis()).toDateStr()).getTime());
        systemEventDTO.setBillNo(packageDTO.getPackageCode());
        return systemEventDTO;
    }

    /**
     * 组装拣选单复核
     *
     * @param packageDTO
     * @return
     */
    private PackageCheckDTO buildPackageCheck(PackageDTO
                                                      packageDTO, List<PackageDetailDTO> packageDetailDTOS, String workbenchCode, String pickCode, String basketNo) {
        PackageCheckDTO packageCheckDTO = new PackageCheckDTO();
        packageCheckDTO.setWarehouseCode(packageDTO.getWarehouseCode());
        packageCheckDTO.setCargoCode(packageDTO.getCargoCode());
        packageCheckDTO.setPackageCode(packageDTO.getPackageCode());
        packageCheckDTO.setPackageCheckCode(iRemoteSeqRuleClient.findSequence(SeqEnum.PACK_CHECK_CODE_000001));
        packageCheckDTO.setBenchCode(workbenchCode);
        packageCheckDTO.setCheckBy(CurrentUserHolder.getUserName());
        packageCheckDTO.setPickCode(pickCode);
        packageCheckDTO.setStatus(PackCheckEnum.STATUS.COMPLETE_STATUS.getCode());
        packageCheckDTO.setPoNo(packageDTO.getPoNo());
        packageCheckDTO.setSoNo(packageDTO.getSoNo());
        packageCheckDTO.setExpressNo(packageDTO.getExpressNo());
        packageCheckDTO.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
        packageCheckDTO.setActualPackUpc(packageDTO.getActualPackUpc());
        packageCheckDTO.setBusinessType(packageDTO.getBusinessType());
        packageCheckDTO.setSalePlatform(packageDTO.getSalePlatform());
        packageCheckDTO.setSaleShopId(packageDTO.getSaleShopId());
        packageCheckDTO.setCarrierCode(packageDTO.getCarrierCode());
        packageCheckDTO.setCarrierName(packageDTO.getCarrierName());
        packageCheckDTO.setBasketNo(basketNo);

        packageCheckDTO.setExpressBranch(packageDTO.getExpressBranch());
        packageCheckDTO.setExpressBranchName(packageDTO.getExpressBranchName());
        packageCheckDTO.setExpressAccount(packageDTO.getExpressAccount());

        List<PackageCheckDetailDTO> detailDTOList = new ArrayList<>();
        for (PackageDetailDTO entity : packageDetailDTOS) {
            PackageCheckDetailDTO packageCheckDetailDTO = new PackageCheckDetailDTO();
            packageCheckDetailDTO.setPackageCheckCode(packageCheckDTO.getPackageCheckCode());
            packageCheckDetailDTO.setWarehouseCode(entity.getWarehouseCode());
            packageCheckDetailDTO.setPUid(entity.getId());
            packageCheckDetailDTO.setCargoCode(entity.getCargoCode());
            packageCheckDetailDTO.setCheckQty(entity.getCheckQty());
            packageCheckDetailDTO.setQty(entity.getSkuQty());
            packageCheckDetailDTO.setSkuCode(entity.getSkuCode());
            packageCheckDetailDTO.setUpcCode(entity.getUpcCode());
            packageCheckDetailDTO.setSkuName(entity.getSkuName());
            packageCheckDetailDTO.setSkuQuality(entity.getSkuQuality());
            packageCheckDetailDTO.setStatus(PackCheckEnum.STATUS.COMPLETE_STATUS.getCode());
            detailDTOList.add(packageCheckDetailDTO);
        }
        packageCheckDTO.setQty(detailDTOList.stream().map(PackageCheckDetailDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        packageCheckDTO.setCheckQty(detailDTOList.stream().map(PackageCheckDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        packageCheckDTO.setDetailDTOList(detailDTOList);
        return packageCheckDTO;
    }

    /**
     * 修改出库单状态码
     *
     * @param shipmentOrderCode
     * @param packageDetailDTOS
     * @return
     */
    private ShipmentOrderDTO modifyShipment(String shipmentOrderCode, List<PackageDetailDTO> packageDetailDTOS, StringBuilder saveShipmentLog) {
        ShipmentOrderDTO shipmentOrderDTO = iRemoteShipmentOrderClient.getShipmentOrderByCode(shipmentOrderCode);
        if (StringUtils.isEmpty(shipmentOrderDTO)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("出库单数据查询异常!!"));
        }
        if (shipmentOrderDTO.getStatus().equals(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode())) {
            saveShipmentLog.append("30");
        }
        if (shipmentOrderDTO.getStatus().equals(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode())) {
            shipmentOrderDTO.setStatus(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode());
        }
        shipmentOrderDTO.setPackageQty(shipmentOrderDTO.getPackageQty() + 1);
        List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOS = iRemoteShipmentOrderClient.queryShipmentOrderDetailList(shipmentOrderCode);
        if (CollectionUtils.isEmpty(shipmentOrderDetailDTOS)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("出库单数据查询异常!!"));
        }
        for (ShipmentOrderDetailDTO entity : shipmentOrderDetailDTOS) {
            BigDecimal writeBackQty = packageDetailDTOS.stream()
                    .filter(a -> Objects.equals(a.getPUid(), entity.getId()))
                    .filter(a -> a.getCheckQty().compareTo(BigDecimal.ZERO) > 0)
                    .map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (writeBackQty != null) {
                entity.setCheckQty(entity.getCheckQty().add(writeBackQty));

                //TODO add 2021-04-12 复核数量不能大于计划数量
                if (entity.getCheckQty().compareTo(entity.getExpSkuQty()) > 0) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s商品:%s复核数量不能大于计划数量", entity.getShipmentOrderCode(), entity.getSkuCode()));
                }

                if (entity.getStatus().equals(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode())
                        && entity.getAssignQty().compareTo(entity.getCheckQty()) == 0) {
                    entity.setStatus(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode());
                }
            }
        }
        shipmentOrderDTO.setListShipmentOrderDetailDTO(shipmentOrderDetailDTOS);
        return shipmentOrderDTO;
    }

    /**
     * 单品单件：一个SKU，商品数量等于1，
     * 单品多件：一个SKU，商品数量大于1，
     * 多品单件：多个SKU ,且每个SKU对应商品数量等于1,
     * 多品多件：多个SKU，且有一个SKU对应商品数量大于1
     * 通过物料明细计算出订单类型
     *
     * @param detailDTOList
     * @return
     */
    private String analysisPackageStruct(List<PackageDetailDTO> detailDTOList) {
        Map<String, BigDecimal> result =
                detailDTOList.stream()
                        .collect(Collectors.groupingBy(PackageDetailDTO::getSkuCode,
                                Collectors.mapping(PackageDetailDTO::getAssignQty,
                                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        BigDecimal count = BigDecimal.ZERO;
        if (result.size() > 1) {
            if (result.entrySet().stream().filter(s -> s.getValue().doubleValue() > 1).collect(Collectors.toList()).size() > 0) {
                return ShipmentOrderEnum.PACKAGE_STRUCT.MORE_MORE_ORDER_SKU_TYPE.getCode();
            } else {
                return ShipmentOrderEnum.PACKAGE_STRUCT.MORE_ONE_ORDER_SKU_TYPE.getCode();
            }
        } else {
            if (result.entrySet().stream().filter(s -> s.getValue().doubleValue() > 1).collect(Collectors.toList()).size() > 0) {
                return ShipmentOrderEnum.PACKAGE_STRUCT.ONE_MORE_ORDER_SKU_TYPE.getCode();
            } else {
                return ShipmentOrderEnum.PACKAGE_STRUCT.ONE_ONE_ORDER_SKU_TYPE.getCode();
            }
        }
    }
}
