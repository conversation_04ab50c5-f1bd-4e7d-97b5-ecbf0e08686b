package com.dt.platform.wms.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.dt.component.common.enums.base.LocationTypeEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuLifeCtrlEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.param.*;
import com.dt.domain.bill.dto.ReplenishSkuDTO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.domain.bill.param.PackageDetailParam;
import com.dt.domain.bill.param.ReplenishTaskParam;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.biz.IReplenishTaskBiz;
import com.dt.platform.wms.biz.ISkuStockAndLotBiz;
import com.dt.platform.wms.dto.replenish.ReplenishRecommendB2BNewDTO;
import com.dt.platform.wms.dto.replenish.ReplenishRecommendDTO;
import com.dt.platform.wms.dto.replenish.ReplenishTaskNewDTO;
import com.dt.platform.wms.dto.wave.SkuLotAndStockDTO;
import com.dt.platform.wms.integration.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/5 17:11
 */
@Service
@Slf4j
public class ReplenishTaskBizImpl implements IReplenishTaskBiz {

    @Resource
    private IRemoteZoneClient remoteZoneClient;

    @Resource
    private IRemoteLocationClient remoteLocationClient;

    @Resource
    private IRemotePackageDetailClient remotePackageDetailClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private ISkuStockAndLotBiz skuStockAndLotBiz;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteShipmentOrderClient shipmentOrderClient;

    @Resource
    private IRemoteShipmentDetailClient remoteShipmentDetailClient;

    @Resource
    IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    IRemoteSkuLotClient remoteSkuLotClient;

    @Override
    public List<ReplenishRecommendDTO> getReplenishRecommendList(List<ReplenishTaskNewDTO> replenishTaskNewList) {
        if (CollectionUtils.isEmpty(replenishTaskNewList)) {
            return null;
        }
        //货主集合
        List<String> cargoCodeList = replenishTaskNewList.stream().map(ReplenishTaskNewDTO::getCargoCode).distinct().collect(Collectors.toList());
        //商品集合
        List<String> skuCodeList = replenishTaskNewList.stream().map(ReplenishTaskNewDTO::getSkuCode).distinct().collect(Collectors.toList());
        //获取当前的商品存储区的库存集合
        List<SkuLotAndStockDTO> skuLotAndStockList = skuStockAndLotBiz.getSkuLotAndStockReplenishFromStorage(cargoCodeList, skuCodeList);
        //未获取到库存数据
        if (CollectionUtils.isEmpty(skuLotAndStockList)) {
            replenishTaskNewList.forEach(it->it.setDiffQty(it.getQty()));
            //无存储区库存，全部缺货
            List<ReplenishRecommendDTO> replenishRecommendDiffDTOList = buildReplenishRecommendDiffDTOList(replenishTaskNewList);
            replenishRecommendDiffDTOList = replenishRecommendDiffDTOList.stream()
                    .sorted(Comparator.comparing(ReplenishRecommendDTO::getCargoCode, Comparator.naturalOrder())
                            .thenComparing(ReplenishRecommendDTO::getSkuCode, Comparator.reverseOrder())
                            .thenComparing(ReplenishRecommendDTO::getOrder, Comparator.naturalOrder())
                            .thenComparing(ReplenishRecommendDTO::getAppointTypeOrder, Comparator.naturalOrder())
                            .thenComparing(ReplenishRecommendDTO::getAppointType, Comparator.reverseOrder())
                            .thenComparing(ReplenishRecommendDTO::getAppointTypeAttr, Comparator.reverseOrder())
                            .thenComparing(ReplenishRecommendDTO::getSkuLotNo, Comparator.reverseOrder())
                            .thenComparing(ReplenishRecommendDTO::getExternalSkuLotNo, Comparator.reverseOrder())
                            .thenComparing(ReplenishRecommendDTO::getExpireDate, Comparator.reverseOrder())
                    ).collect(Collectors.toList());
            return replenishRecommendDiffDTOList;
        }
        //获取所有sku
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCodeList(cargoCodeList);
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return null;
        }
        List<ReplenishRecommendDTO> replenishRecommendDTOList = analysisReplenishRecommend(skuDTOList, replenishTaskNewList, skuLotAndStockList);
        //格式化数据
        List<ReplenishRecommendDTO> replenishRecommendDTOS = replenishRecommendDTOList.stream().map(replenishSkuLotAndStockDTO -> {

            ReplenishRecommendDTO recommendDTO = ConverterUtil.convert(replenishSkuLotAndStockDTO, ReplenishRecommendDTO.class);

            recommendDTO.setSkuQualityName(SkuQualityEnum.getEnum(recommendDTO.getSkuQuality()).getMessage());

            if (!StringUtils.isEmpty(recommendDTO.getInventoryType())) {
                recommendDTO.setInventoryTypeDesc(InventoryTypeEnum.getEnum(recommendDTO.getInventoryType()).getMessage());
            }
            recommendDTO.setManufDate(replenishSkuLotAndStockDTO.getManufDate());
            recommendDTO.setManufDateFormat(ConverterUtil.convertVoTime(replenishSkuLotAndStockDTO.getManufDate(), "yyyy-MM-dd"));

            recommendDTO.setExpireDate(replenishSkuLotAndStockDTO.getExpireDate());
            recommendDTO.setExpireDateFormat(ConverterUtil.convertVoTime(replenishSkuLotAndStockDTO.getExpireDate(), "yyyy-MM-dd"));

            recommendDTO.setReceiveDate(replenishSkuLotAndStockDTO.getReceiveDate());
            recommendDTO.setReceiveDateFormat(ConverterUtil.convertVoTime(replenishSkuLotAndStockDTO.getReceiveDate(), "yyyy-MM-dd"));
            return recommendDTO;
        }).collect(Collectors.toList());
        // 库区名称
        List<String> zoneCodeList = replenishRecommendDTOS.stream().map(ReplenishRecommendDTO::getZoneCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(zoneCodeList)) {
            ZoneParam zoneParam = new ZoneParam();
            zoneParam.setCodeList(zoneCodeList);
            List<ZoneDTO> zoneDTOS = remoteZoneClient.getList(zoneParam);
            replenishRecommendDTOS.forEach(replenishRecommendDTO -> zoneDTOS.stream().filter(zoneDTO -> zoneDTO.getCode().equals(replenishRecommendDTO.getZoneCode())).findAny().ifPresent(zoneDTO -> replenishRecommendDTO.setZoneName(zoneDTO.getName())));
        }
        // 巷道
        List<String> locationCodes = replenishRecommendDTOS.stream().map(ReplenishRecommendDTO::getLocationCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(locationCodes)) {
            LocationParam locationParam = new LocationParam();
            locationParam.setCodeList(locationCodes);
            List<LocationDTO> locationDTOS = remoteLocationClient.getList(locationParam);
            replenishRecommendDTOS.forEach(replenishRecommendDTO -> locationDTOS.stream().filter(locationDTO -> locationDTO.getCode().equals(replenishRecommendDTO.getLocationCode())).findAny().ifPresent(locationDTO -> replenishRecommendDTO.setTunnelCode(locationDTO.getTunnelCode())));
        }
        if (!CollectionUtils.isEmpty(cargoCodeList)) {
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(cargoCodeList);
            List<CargoOwnerDTO> allCargoOwner = remoteCargoOwnerClient.getAllCargoOwner(cargoOwnerParam);
            replenishRecommendDTOS.forEach(replenishRecommendDTO -> allCargoOwner.stream().filter(cargoOwnerDTO -> cargoOwnerDTO.getCode().equals(replenishRecommendDTO.getCargoCode())).findAny().ifPresent(cargoOwnerDTO -> replenishRecommendDTO.setCargoName(cargoOwnerDTO.getName())));
        }
        //商品名
        replenishRecommendDTOS.forEach(replenishRecommendDTO ->
                skuDTOList.stream()
                        .filter(skuDTO -> skuDTO.getCode().equals(replenishRecommendDTO.getSkuCode()))
                        .filter(skuDTO -> skuDTO.getCargoCode().equals(replenishRecommendDTO.getCargoCode()))
                        .findAny().ifPresent(skuDTO -> replenishRecommendDTO.setSkuName(skuDTO.getName())));
        return replenishRecommendDTOS;
    }

    @Override
    public List<ReplenishRecommendB2BNewDTO> getReplenishTaskB2BNewList(List<String> shipmentOrderCodeList) {
        List<ReplenishRecommendB2BNewDTO> replenishRecommendB2BNewDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(shipmentOrderCodeList)) {
            return replenishRecommendB2BNewDTOList;
        }
        //获取出库单
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setStatus(ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode());
        shipmentOrderParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString());
        shipmentOrderParam.setShipmentOrderCodeList(shipmentOrderCodeList);
        List<ShipmentOrderDTO> shipmentOrderDTOList = shipmentOrderClient.getList(shipmentOrderParam);
        if (CollectionUtils.isEmpty(shipmentOrderDTOList)) {
            return replenishRecommendB2BNewDTOList;
        }
        //获取出库单明细
        ShipmentOrderDetailParam shipmentOrderDetailParam = new ShipmentOrderDetailParam();
        shipmentOrderDetailParam.setShipmentOrderCodeList(shipmentOrderDTOList.stream().map(ShipmentOrderDTO::getShipmentOrderCode).collect(Collectors.toList()));
        List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = remoteShipmentDetailClient.getList(shipmentOrderDetailParam);
        if (CollectionUtils.isEmpty(shipmentOrderDetailDTOList)) {
            return replenishRecommendB2BNewDTOList;
        }
        //货主集合
        List<String> cargoCodeList = shipmentOrderDTOList.stream().map(ShipmentOrderDTO::getCargoCode).distinct().collect(Collectors.toList());
        //商品集合
        List<String> skuCodeList = shipmentOrderDetailDTOList.stream().map(ShipmentOrderDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        //获取拣选区库存数据
        List<SkuLotAndStockDTO> skuLotAndStockPickList = skuStockAndLotBiz.getSkuLotAndStockFromReplenishTaskForPickB2B(skuCodeList, cargoCodeList, Arrays.asList(LocationTypeEnum.LOCATION_TYPE_PICK.getType()));
        //获取存储区库存数据
        List<SkuLotAndStockDTO> skuLotAndStockStorageList = skuStockAndLotBiz.getSkuLotAndStockFromReplenishTaskForPickB2B(skuCodeList, cargoCodeList, Arrays.asList(LocationTypeEnum.LOCATION_TYPE_STORE.getType()));

        //获取所有sku
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCodeList(cargoCodeList);
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return replenishRecommendB2BNewDTOList;
        }
        Map<String, CargoOwnerDTO> cargoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(cargoCodeList)) {
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(cargoCodeList);
            List<CargoOwnerDTO> allCargoOwner = remoteCargoOwnerClient.getAllCargoOwner(cargoOwnerParam);
            if (!CollectionUtils.isEmpty(allCargoOwner)) {
                Map<String, CargoOwnerDTO> cargoOwnerDTOMap = allCargoOwner.stream().collect(Collectors.toMap(CargoOwnerDTO::getCode, Function.identity()));
                if (!CollectionUtils.isEmpty(cargoOwnerDTOMap)) {
                    cargoMap.putAll(cargoOwnerDTOMap);
                }
            }
        }

        for (ShipmentOrderDTO shipmentOrderDTO : shipmentOrderDTOList) {
            //获取出库单明细-按照商品——批次降序(同一商品有批次优先处理)
            List<ShipmentOrderDetailDTO> orderDetailDTOList = shipmentOrderDetailDTOList.stream()
                    .filter(a -> a.getShipmentOrderCode().equalsIgnoreCase(shipmentOrderDTO.getShipmentOrderCode()))
                    .sorted(Comparator.comparing(ShipmentOrderDetailDTO::getSkuCode, Comparator.naturalOrder())
                            .thenComparing(ShipmentOrderDetailDTO::getSkuLotNo, Comparator.reverseOrder())
                            .thenComparing(ShipmentOrderDetailDTO::getExpireDate, Comparator.reverseOrder())
                            .thenComparing(ShipmentOrderDetailDTO::getExpireDateStart, Comparator.reverseOrder())
                            .thenComparing(ShipmentOrderDetailDTO::getExpireDateEnd, Comparator.reverseOrder())
                            .thenComparing(ShipmentOrderDetailDTO::getExternalSkuLotNo, Comparator.reverseOrder())
                            .thenComparing(ShipmentOrderDetailDTO::getInventoryType, Comparator.reverseOrder())
                    ).collect(Collectors.toList());
            //是否校验禁售 true 是  false 非 是销毁出库 不校验禁售
            Boolean isIgnorePeriod = false;
            //订单标记忽略效期
            if (OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag())
                    .stream().anyMatch(a -> a.getCode().equals(OrderTagEnum.IGNORE_VALIDITY_PERIOD.getCode()))) {
                isIgnorePeriod = true;
            }
            if (shipmentOrderDTO.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())
                    && shipmentOrderDTO.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.DESTROY_OUT_STOCK_TYPE.getCode())) {
                isIgnorePeriod = true;
            }
            List<SkuLotAndStockDTO> skuLotAndStockTempPickList;
            List<SkuLotAndStockDTO> skuLotAndStockTempStorageList;
            //isIgnorePeriod = true 不过滤禁售
            if (isIgnorePeriod) {
                skuLotAndStockTempPickList = skuLotAndStockPickList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(shipmentOrderDTO.getCargoCode()))
                        .collect(Collectors.toList());
                skuLotAndStockTempStorageList = skuLotAndStockStorageList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(shipmentOrderDTO.getCargoCode()))
                        .collect(Collectors.toList());
            } else {
                skuLotAndStockTempPickList = skuLotAndStockPickList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(shipmentOrderDTO.getCargoCode()))
                        .filter(a -> !a.getIsWithdraw())
                        .collect(Collectors.toList());
                skuLotAndStockTempStorageList = skuLotAndStockStorageList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(shipmentOrderDTO.getCargoCode()))
                        .filter(a -> !a.getIsWithdraw())
                        .collect(Collectors.toList());
            }
            //过滤拣选区已有的库存
            List<ShipmentOrderDetailDTO> orderNeedDetailDTOList = calculationNeedShipmentDetail(orderDetailDTOList, skuLotAndStockTempPickList, shipmentOrderDTO);
            if (CollectionUtils.isEmpty(orderNeedDetailDTOList)
                    || orderDetailDTOList.stream().allMatch(a -> a.getExpSkuQty().compareTo(BigDecimal.ZERO) <= 0)) {
                continue;
            }
            //计算存储补货数据
            List<ReplenishRecommendB2BNewDTO> replenishRecommendB2BNewDTOS = calculationNeedFromStorage(orderNeedDetailDTOList, skuLotAndStockTempStorageList, skuDTOList, cargoMap);
            if (!CollectionUtils.isEmpty(replenishRecommendB2BNewDTOS)) {
                replenishRecommendB2BNewDTOList.addAll(replenishRecommendB2BNewDTOS);
            }
        }
        if (CollectionUtils.isEmpty(replenishRecommendB2BNewDTOList)) {
            return replenishRecommendB2BNewDTOList;
        }
        // 库区名称
        List<String> zoneCodeList = replenishRecommendB2BNewDTOList.stream().map(ReplenishRecommendB2BNewDTO::getZoneCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(zoneCodeList)) {
            ZoneParam zoneParam = new ZoneParam();
            zoneParam.setCodeList(zoneCodeList);
            List<ZoneDTO> zoneDTOS = remoteZoneClient.getList(zoneParam);
            replenishRecommendB2BNewDTOList.forEach(replenishRecommendDTO -> zoneDTOS.stream().filter(zoneDTO -> zoneDTO.getCode().equals(replenishRecommendDTO.getZoneCode())).findAny().ifPresent(zoneDTO -> replenishRecommendDTO.setZoneName(zoneDTO.getName())));
        }
        // 巷道
        List<String> locationCodes = replenishRecommendB2BNewDTOList.stream().map(ReplenishRecommendB2BNewDTO::getLocationCode).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(locationCodes)) {
            LocationParam locationParam = new LocationParam();
            locationParam.setCodeList(locationCodes);
            List<LocationDTO> locationDTOS = remoteLocationClient.getList(locationParam);
            replenishRecommendB2BNewDTOList.forEach(replenishRecommendDTO -> locationDTOS.stream().filter(locationDTO -> locationDTO.getCode().equals(replenishRecommendDTO.getLocationCode())).findAny().ifPresent(locationDTO -> replenishRecommendDTO.setTunnelCode(locationDTO.getTunnelCode())));
        }
        if (!CollectionUtils.isEmpty(cargoMap)) {
            replenishRecommendB2BNewDTOList.forEach(replenishRecommendDTO -> {
                if (cargoMap.containsKey(replenishRecommendDTO.getCargoCode())) {
                    replenishRecommendDTO.setCargoName(cargoMap.get(replenishRecommendDTO.getCargoCode()).getName());
                }
                if (StringUtils.isEmpty(replenishRecommendDTO.getAppointTypeOrder())) {
                    replenishRecommendDTO.setAppointTypeOrder(9999);
                }
            });
        }
        //商品名
        replenishRecommendB2BNewDTOList.forEach(replenishRecommendDTO ->
                skuDTOList.stream()
                        .filter(skuDTO -> skuDTO.getCode().equals(replenishRecommendDTO.getSkuCode()))
                        .filter(skuDTO -> skuDTO.getCargoCode().equals(replenishRecommendDTO.getCargoCode()))
                        .findAny().ifPresent(skuDTO -> replenishRecommendDTO.setSkuName(skuDTO.getName())));
        return replenishRecommendB2BNewDTOList;
    }

    @Override
    public List<ReplenishTaskNewDTO> getReplenishTaskNewV2List(ReplenishTaskParam replenishTaskParam) {
        String uuId = CurrentRouteHolder.getWarehouseCode() + "," + RandomUtil.randomNumbers(15);
        log.info("getReplenishTaskNewV2List 1:{}", uuId);
        List<ReplenishSkuDTO> replenishSkuDTOList = getReplenishSkuOriginData(replenishTaskParam);
        if (CollectionUtils.isEmpty(replenishSkuDTOList)) {
            return Lists.newArrayList();
        }
        log.info("getReplenishTaskNewV2List 2:{}", uuId);
        //获取当前库存数据
        List<String> skuCodeList = replenishSkuDTOList.stream().map(ReplenishSkuDTO::getSkuCode).distinct().collect(Collectors.toList());
        List<String> cargoCodeList = replenishSkuDTOList.stream().map(ReplenishSkuDTO::getCargoCode).distinct().collect(Collectors.toList());
        //查询商品
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCodeList(cargoCodeList);
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return Lists.newArrayList();
        }
        log.info("getReplenishTaskNewV2List 3:{}", uuId);
        StockLocationParam stockLocationParam = new StockLocationParam();
        stockLocationParam.setSkuCodeList(skuCodeList);
        stockLocationParam.setCargoCodeList(cargoCodeList);
        stockLocationParam.setLocationTypeList(Arrays.asList(LocationTypeEnum.LOCATION_TYPE_PICK.getType()));
        stockLocationParam.setHasAvailableQty(true);
        List<StockLocationDTO> stockLocationDTOList = remoteStockLocationClient.getListByPage(stockLocationParam);
        if (CollectionUtils.isEmpty(stockLocationDTOList)) {
            return buildReplenishData(replenishSkuDTOList, skuDTOList);
        }
        log.info("getReplenishTaskNewV2List 4:{}", uuId);
        //获取批次数据
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(stockLocationDTOList.stream().map(StockLocationDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getListByPage(skuLotParam);
        if (CollectionUtils.isEmpty(skuLotDTOList) || CollectionUtils.isEmpty(stockLocationDTOList)) {
            return buildReplenishData(replenishSkuDTOList, skuDTOList);
        }
        log.info("getReplenishTaskNewV2List 5:{}", uuId);
        List<SkuLotAndStockDTO> skuLotAndStockDTOList = skuStockAndLotBiz.buildSkuLotAndStock(skuLotDTOList, stockLocationDTOList, skuDTOList);
        if (CollectionUtils.isEmpty(skuLotAndStockDTOList)) {
            return buildReplenishData(replenishSkuDTOList, skuDTOList);
        }
        //分析补货指引,不需要过期的商品
        skuLotAndStockDTOList = skuLotAndStockDTOList.stream().filter(a -> !a.getOverExpire()).collect(Collectors.toList());
        log.info("getReplenishTaskNewV2List 6:{}", uuId);
        //分析数据
        List<ReplenishTaskNewDTO> replenishTaskNewDTOList = analysisReplenishStock(replenishSkuDTOList, skuLotAndStockDTOList, skuDTOList);
        log.info("getReplenishTaskNewV2List 7:{}", uuId);
        return replenishTaskNewDTOList;
    }

    /**
     * @param replenishSkuDTOList
     * @param skuDTOList
     * @return java.util.List<com.dt.platform.wms.dto.replenish.ReplenishTaskNewDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/12/16 15:34
     */
    private List<ReplenishTaskNewDTO> buildReplenishData(List<ReplenishSkuDTO> replenishSkuDTOList,
                                                         List<SkuDTO> skuDTOList) {
        String taskCodeID = UUID.randomUUID().toString();
        List<ReplenishTaskNewDTO> replenishTaskNewDTOList = new ArrayList<>();
        for (ReplenishSkuDTO it : replenishSkuDTOList) {
            ReplenishTaskNewDTO replenishTaskNewDTO = new ReplenishTaskNewDTO();
            replenishTaskNewDTO.setCargoCode(it.getCargoCode());
            replenishTaskNewDTO.setSkuCode(it.getSkuCode());
            replenishTaskNewDTO.setUpcCode(it.getUpcCode());
            replenishTaskNewDTO.setSkuQuality(it.getSkuQuality());
            replenishTaskNewDTO.setQty(it.getExpQty());
            replenishTaskNewDTO.setDefQty(it.getExpQty());
            replenishTaskNewDTO.setTaskId(taskCodeID);
            replenishTaskNewDTO.setSkuLotNo(it.getSkuLotNo());

            replenishTaskNewDTO.setExternalSkuLotNo(it.getExternalSkuLotNo());
            replenishTaskNewDTO.setExpireDate(it.getExpireDate());
            replenishTaskNewDTO.setExpireDateStart(it.getExpireDateStart());
            replenishTaskNewDTO.setExpireDateEnd(it.getExpireDateEnd());
            skuDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(it.getCargoCode()))
                    .filter(a -> a.getCode().equalsIgnoreCase(it.getSkuCode()))
                    .findFirst().ifPresent(a -> replenishTaskNewDTO.setSkuName(a.getName()));
            //ADD BY 2024-12-18
            replenishTaskNewDTO.setWithdrawCompareDate(it.getWithdrawCompareDate());
            replenishTaskNewDTO.setInventoryType(it.getInventoryType());
            replenishTaskNewDTO.setTaoTianCargo(it.getTaoTianCargo());
            replenishTaskNewDTOList.add(replenishTaskNewDTO);
        }
        return replenishTaskNewDTOList;
    }


    /**
     * @param replenishSkuDTOList
     * @param skuLotAndStockOriginDTOList
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2022/12/7 9:14
     */
    private List<ReplenishTaskNewDTO> analysisReplenishStock(List<ReplenishSkuDTO> replenishSkuDTOList,
                                                             List<SkuLotAndStockDTO> skuLotAndStockOriginDTOList,
                                                             List<SkuDTO> skuDTOList) {
        String taskCodeID = UUID.randomUUID().toString();
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        replenishSkuDTOList = replenishSkuDTOList.stream().sorted(
                Comparator.comparing(ReplenishSkuDTO::getSkuCode, Comparator.naturalOrder())
                        .thenComparing(ReplenishSkuDTO::getSkuLotNo, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getExpireDate, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getExpireDateStart, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getExpireDateEnd, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getExternalSkuLotNo, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getWithdrawCompareDate, Comparator.reverseOrder())
        ).collect(Collectors.toList());
        //B单专用拣选位
        List<SkuLotAndStockDTO> stockB2BDTOList = skuLotAndStockOriginDTOList.stream().filter(a -> a.getIsB2BPick()).collect(Collectors.toList());
        //B单削减库存
        if (!CollectionUtils.isEmpty(stockB2BDTOList)) {
            reduceStockData(replenishSkuDTOList.stream().filter(a -> a.getBusinessType().equalsIgnoreCase(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())).collect(Collectors.toList()), stockB2BDTOList, taskCodeID, warehouseCode, skuDTOList);
        }
        List<ReplenishSkuDTO> replenishSkuDTOLastList = new ArrayList<>();
        //二次合并B单和C单的数据
        Map<String, List<ReplenishSkuDTO>> map = replenishSkuDTOList.stream().collect(Collectors.groupingBy(a ->
                StrUtil.join("#",
                        a.getWarehouseCode(),
                        a.getCargoCode(),
                        a.getSkuCode(),
                        a.getSkuQuality(),
                        a.getSkuLotNo(),
                        a.getExternalSkuLotNo(),
                        a.getExpireDate(),
                        a.getExpireDateStart(),
                        a.getExpireDateEnd(),
                        a.getWithdrawCompareDate(),
                        a.getInventoryType()
                )));
        map.entrySet().stream().forEach(entity -> {
            ReplenishSkuDTO replenishSkuDTO = ObjectUtil.cloneByStream(entity.getValue().get(0));
            replenishSkuDTO.setExpQty(entity.getValue().stream().map(ReplenishSkuDTO::getExpQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            replenishSkuDTO.setPackNum(entity.getValue().stream().mapToInt(ReplenishSkuDTO::getPackNum).sum());
            replenishSkuDTOLastList.add(replenishSkuDTO);
        });
        replenishSkuDTOLastList.removeIf(a -> a.getExpQty().compareTo(BigDecimal.ZERO) <= 0);
        if (CollectionUtils.isEmpty(replenishSkuDTOLastList)) {
            return Lists.newArrayList();
        }
        List<ReplenishSkuDTO> replenishSkuDTOLastNewList = replenishSkuDTOLastList.stream()
                .sorted(Comparator.comparing(ReplenishSkuDTO::getSkuCode, Comparator.naturalOrder())
                        .thenComparing(ReplenishSkuDTO::getSkuLotNo, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getExpireDate, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getExpireDateStart, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getExpireDateEnd, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getExternalSkuLotNo, Comparator.reverseOrder())
                        .thenComparing(ReplenishSkuDTO::getWithdrawCompareDate, Comparator.reverseOrder())
                ).collect(Collectors.toList());
        //普通拣选位
        List<SkuLotAndStockDTO> stockPickDTOList = skuLotAndStockOriginDTOList.stream().filter(a -> !a.getIsB2BPick()).collect(Collectors.toList());
        //剔除B单专用拣选位削减库存
        if (!CollectionUtils.isEmpty(stockPickDTOList)) {
            reduceStockData(replenishSkuDTOLastNewList, stockPickDTOList, taskCodeID, warehouseCode, skuDTOList);
        }
        replenishSkuDTOLastNewList.removeIf(a -> a.getExpQty().compareTo(BigDecimal.ZERO) <= 0);
        if (CollectionUtils.isEmpty(replenishSkuDTOLastNewList)) {
            return Lists.newArrayList();
        }
        //最终结果需求数量
        List<ReplenishTaskNewDTO> replenishTaskNewDTOList = new ArrayList<>();
        for (ReplenishSkuDTO it : replenishSkuDTOLastNewList) {
            ReplenishTaskNewDTO replenishTaskNewDTO = new ReplenishTaskNewDTO();
            replenishTaskNewDTO.setCargoCode(it.getCargoCode());
            replenishTaskNewDTO.setSkuCode(it.getSkuCode());
            replenishTaskNewDTO.setUpcCode(it.getUpcCode());
            replenishTaskNewDTO.setSkuQuality(it.getSkuQuality());
            replenishTaskNewDTO.setQty(it.getExpQty());
            replenishTaskNewDTO.setDefQty(it.getExpQty());
            replenishTaskNewDTO.setTaskId(taskCodeID);
            replenishTaskNewDTO.setSkuLotNo(it.getSkuLotNo());

            replenishTaskNewDTO.setExternalSkuLotNo(it.getExternalSkuLotNo());
            replenishTaskNewDTO.setExpireDate(it.getExpireDate());
            replenishTaskNewDTO.setExpireDateStart(it.getExpireDateStart());
            replenishTaskNewDTO.setExpireDateEnd(it.getExpireDateEnd());
            replenishTaskNewDTO.setWithdrawCompareDate(it.getWithdrawCompareDate());
            replenishTaskNewDTO.setInventoryType(it.getInventoryType());
            replenishTaskNewDTO.setTaoTianCargo(it.getTaoTianCargo());
            skuDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(it.getCargoCode()))
                    .filter(a -> a.getCode().equalsIgnoreCase(it.getSkuCode()))
                    .findFirst().ifPresent(a -> replenishTaskNewDTO.setSkuName(a.getName()));
            replenishTaskNewDTOList.add(replenishTaskNewDTO);
        }
        return replenishTaskNewDTOList;
    }

    /**
     * @param replenishSkuDTOList
     * @param stockDTOList
     * @param taskCodeID
     * @param warehouseCode
     * @param skuDTOList
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2022/12/7 10:18
     */
    private void reduceStockData(List<ReplenishSkuDTO> replenishSkuDTOList,
                                 List<SkuLotAndStockDTO> stockDTOList,
                                 String taskCodeID,
                                 String warehouseCode,
                                 List<SkuDTO> skuDTOList) {
        //分析库存
        for (ReplenishSkuDTO replenishSkuDTO : replenishSkuDTOList) {
            //B2C 库存全是【B单专用】 continue
            if (stockDTOList.stream().allMatch(a -> a.getIsB2BPick()) && Objects.equals(replenishSkuDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())) {
                continue;
            }
            //拣选位库存处理
            List<SkuLotAndStockDTO> skuLotAndStockList = stockDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(replenishSkuDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(replenishSkuDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(replenishSkuDTO.getSkuQuality()))
                    .filter(a -> a.getLocationType().equalsIgnoreCase(LocationTypeEnum.LOCATION_TYPE_PICK.getType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skuLotAndStockList)) {
                continue;
            }
            //指定批次 ,外部批次编码,失效日期
            skuLotAndStockList = skuLotAndStockList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(replenishSkuDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(replenishSkuDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(replenishSkuDTO.getSkuQuality()))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(skuLotAndStockList) && !StringUtils.isEmpty(replenishSkuDTO.getSkuLotNo())) {
                skuLotAndStockList = skuLotAndStockList.stream().filter(a -> Objects.equals(a.getSkuLotNo(), replenishSkuDTO.getSkuLotNo())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(skuLotAndStockList) && !StringUtils.isEmpty(replenishSkuDTO.getExternalSkuLotNo())) {
                skuLotAndStockList = skuLotAndStockList.stream().filter(a -> Objects.equals(a.getExternalSkuLotNo(), replenishSkuDTO.getExternalSkuLotNo())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(skuLotAndStockList) && !StringUtils.isEmpty(replenishSkuDTO.getExpireDate()) && replenishSkuDTO.getExpireDate() > 0L) {
                skuLotAndStockList = skuLotAndStockList.stream().filter(a -> Objects.equals(a.getExpireDate(), replenishSkuDTO.getExpireDate())).collect(Collectors.toList());
            }
            //效期范围
            if (!CollectionUtils.isEmpty(skuLotAndStockList) && !StringUtils.isEmpty(replenishSkuDTO.getExpireDateStart()) && replenishSkuDTO.getExpireDateStart() > 0L) {
                skuLotAndStockList = skuLotAndStockList.stream().filter(a -> a.getExpireDate() >= replenishSkuDTO.getExpireDateStart()).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(skuLotAndStockList) && !StringUtils.isEmpty(replenishSkuDTO.getExpireDateEnd()) && replenishSkuDTO.getExpireDateEnd() > 0L) {
                skuLotAndStockList = skuLotAndStockList.stream().filter(a -> a.getExpireDate() <= replenishSkuDTO.getExpireDateEnd()).collect(Collectors.toList());
            }
            //效期范围
            //指定残次等级  淘天仓强校验
            if (replenishSkuDTO.getTaoTianCargo() != null && replenishSkuDTO.getTaoTianCargo()) {
                if (!CollectionUtils.isEmpty(skuLotAndStockList) && !StringUtils.isEmpty(replenishSkuDTO.getInventoryType())) {
                    skuLotAndStockList = skuLotAndStockList.stream().filter(a -> Objects.equals(a.getInventoryType(), replenishSkuDTO.getInventoryType())).collect(Collectors.toList());
                }
            }
            //比对禁售时间
            if (!CollectionUtils.isEmpty(skuLotAndStockList) && !StringUtils.isEmpty(replenishSkuDTO.getWithdrawCompareDate()) && replenishSkuDTO.getWithdrawCompareDate() > 0L) {
                skuLotAndStockList = skuLotAndStockList.stream()
                        .filter(a -> !a.getOverExpire())
                        .filter(a -> a.getWithdrawDate() > replenishSkuDTO.getWithdrawCompareDate()).collect(Collectors.toList());
            } else {
                skuLotAndStockList = skuLotAndStockList.stream().filter(a -> !a.getIsWithdraw()).collect(Collectors.toList());
            }

            SkuDTO skuDTO = skuDTOList.stream().filter(a -> a.getCargoCode().equalsIgnoreCase(replenishSkuDTO.getCargoCode())).filter(a -> a.getCode().equalsIgnoreCase(replenishSkuDTO.getSkuCode())).findFirst().orElse(null);

            List<SkuLotAndStockDTO> skuLotAndStockDTOList = skuStockAndLotBiz.sortListStockLotAndLocation(taskCodeID, warehouseCode, skuLotAndStockList, skuDTO.getTurnoverRuleCode(), skuDTO.getAllocationRuleCode());
            //------
            BigDecimal actualQty = skuLotAndStockDTOList.stream()
                    .map(SkuLotAndStockDTO::getAvailableQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //库存足暂用库存
            if (actualQty.compareTo(replenishSkuDTO.getExpQty()) >= 0) {
                BigDecimal planQty = replenishSkuDTO.getExpQty();
                replenishSkuDTO.setExpQty(BigDecimal.ZERO);
                for (SkuLotAndStockDTO stockDTO : skuLotAndStockDTOList) {
                    if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                        //用于移除内存数据
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        planQty = planQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);
                    }
                }
            } else {
                //当前库存不满足分配
                BigDecimal planQty = actualQty;
                replenishSkuDTO.setExpQty(replenishSkuDTO.getExpQty().subtract(actualQty));
                for (SkuLotAndStockDTO stockDTO : skuLotAndStockDTOList) {
                    if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                        //用于移除内存数据
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        planQty = planQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);
                    }
                }
            }
            //-----
        }
    }

    /**
     * @param replenishTaskParam
     * @return java.util.List<com.dt.domain.bill.dto.ReplenishSkuDTO>
     * <AUTHOR>
     * @describe: 获取基础数据
     * @date 2022/12/7 9:49
     */
    private List<ReplenishSkuDTO> getReplenishSkuOriginData(ReplenishTaskParam replenishTaskParam) {
        List<ReplenishSkuDTO> replenishSkuDTOList = new ArrayList<>();
        PackageDetailParam packageDetailParam = new PackageDetailParam();
        if (!CollectionUtils.isEmpty(replenishTaskParam.getCargoCodeList())) {
            packageDetailParam.setCargoCodeList(replenishTaskParam.getCargoCodeList());
        }
        if (!StringUtils.isEmpty(replenishTaskParam.getCargoCode())) {
            packageDetailParam.setCargoCode(replenishTaskParam.getCargoCode());
        }
        if (!CollectionUtils.isEmpty(replenishTaskParam.getSkuCodeList())) {
            packageDetailParam.setSkuCodeList(replenishTaskParam.getSkuCodeList());
        }
        if (!StringUtils.isEmpty(replenishTaskParam.getSkuCode())) {
            packageDetailParam.setSkuCode(replenishTaskParam.getSkuCode());
        }
        if (!CollectionUtils.isEmpty(replenishTaskParam.getUpcCodeList())) {
            packageDetailParam.setUpcCodeList(replenishTaskParam.getUpcCodeList());
        }
        if (!StringUtils.isEmpty(replenishTaskParam.getSkuQuality())) {
            packageDetailParam.setSkuQuality(replenishTaskParam.getSkuQuality());
        }
        List<String> businessTypeList = Arrays.asList("B2B", "B2C");
        if (!StringUtils.isEmpty(replenishTaskParam.getBusinessType())) {
            businessTypeList = Arrays.asList(replenishTaskParam.getBusinessType());
        }

        packageDetailParam.setNoEqualOrderTag(OrderTagEnum.enumToNum(OrderTagEnum.OCCUPY_LOCATION_STOCK));

        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        List<ReplenishSkuDTO> replenishSkuDTOStartList = new ArrayList<>();
        List<String> finalBusinessTypeList = businessTypeList;
        finalBusinessTypeList.forEach(businessTypeEnum -> {
            RpcContextUtil.setWarehouseCode(warehouseCode);
            packageDetailParam.setBusinessType(businessTypeEnum.toString());
            //获取数据
            List<ReplenishSkuDTO> queryReplenishTaskData = remotePackageDetailClient.queryReplenishTaskData(packageDetailParam);
            if (!CollectionUtils.isEmpty(queryReplenishTaskData)) {
                queryReplenishTaskData.forEach(a -> a.setBusinessType(businessTypeEnum.toString()));
                replenishSkuDTOStartList.addAll(queryReplenishTaskData);
            }
        });
        RpcContextUtil.setWarehouseCode(warehouseCode);
        if (CollectionUtils.isEmpty(replenishSkuDTOStartList)) {
            return Lists.newArrayList();
        }
        //预包包裹的剩余商品------>合并数据
        Map<String, List<ReplenishSkuDTO>> map = replenishSkuDTOStartList.stream().collect(Collectors.groupingBy(replenishSkuDTO ->
                StrUtil.join("#", replenishSkuDTO.getWarehouseCode(), replenishSkuDTO.getCargoCode(),
                        replenishSkuDTO.getSkuCode(), replenishSkuDTO.getSkuQuality(),
                        replenishSkuDTO.getSkuLotNo(), replenishSkuDTO.getExternalSkuLotNo(),
                        replenishSkuDTO.getExpireDate(), replenishSkuDTO.getExpireDateStart(),
                        replenishSkuDTO.getExpireDateEnd(), replenishSkuDTO.getWithdrawCompareDate(),
                        replenishSkuDTO.getInventoryType(), replenishSkuDTO.getBusinessType())));
        map.entrySet().stream().forEach(entity -> {
            ReplenishSkuDTO replenishSkuDTO = ObjectUtil.cloneByStream(entity.getValue().get(0));
            replenishSkuDTO.setExpQty(entity.getValue().stream().map(ReplenishSkuDTO::getExpQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            replenishSkuDTO.setPackNum(entity.getValue().stream().mapToInt(ReplenishSkuDTO::getPackNum).sum());
            //数据格式化
            replenishSkuDTO.setSkuLotNo(StringUtils.isEmpty(replenishSkuDTO.getSkuLotNo()) ? "" : replenishSkuDTO.getSkuLotNo());
            replenishSkuDTO.setExternalSkuLotNo(StringUtils.isEmpty(replenishSkuDTO.getExternalSkuLotNo()) ? "" : replenishSkuDTO.getExternalSkuLotNo());
            replenishSkuDTO.setExpireDate(StringUtils.isEmpty(replenishSkuDTO.getExpireDate()) ? 0L : replenishSkuDTO.getExpireDate());
            replenishSkuDTO.setExpireDateStart(StringUtils.isEmpty(replenishSkuDTO.getExpireDateStart()) ? 0L : replenishSkuDTO.getExpireDateStart());
            replenishSkuDTO.setExpireDateEnd(StringUtils.isEmpty(replenishSkuDTO.getExpireDateEnd()) ? 0L : replenishSkuDTO.getExpireDateEnd());
            replenishSkuDTO.setWithdrawCompareDate(StringUtils.isEmpty(replenishSkuDTO.getWithdrawCompareDate()) ? 0L : replenishSkuDTO.getWithdrawCompareDate());
            replenishSkuDTOList.add(replenishSkuDTO);
        });
        if (!CollectionUtils.isEmpty(replenishSkuDTOList)) {
            //补充货主是否淘天
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(replenishSkuDTOList.stream().map(ReplenishSkuDTO::getCargoCode).distinct().collect(Collectors.toList()));
            List<CargoOwnerDTO> cargoOwnerDTOList = remoteCargoOwnerClient.getAllCargoOwner(cargoOwnerParam);
            if (!CollectionUtils.isEmpty(cargoOwnerDTOList)) {
                Map<String, CargoOwnerDTO> ownerDTOMap = cargoOwnerDTOList.stream().collect(Collectors.toMap(CargoOwnerDTO::getCode, Function.identity()));
                replenishSkuDTOList.forEach(it -> {
                    it.setTaoTianCargo(false);
                    if (ownerDTOMap.containsKey(it.getCargoCode())) {
                        CargoOwnerDTO cargoOwnerDTO = ownerDTOMap.get(it.getCargoCode());
                        if (CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO)) {
                            it.setTaoTianCargo(true);
                        }
                    }
                });
            }
        }
        return replenishSkuDTOList;
    }

    /**
     * @param orderNeedDetailDTOList
     * @param skuLotAndStockTempPickList
     * @return java.util.List<com.dt.platform.wms.dto.replenish.ReplenishRecommendB2BNewDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/10/27 15:44
     */
    private List<ReplenishRecommendB2BNewDTO> calculationNeedFromStorage(List<ShipmentOrderDetailDTO> orderNeedDetailDTOList,
                                                                         List<SkuLotAndStockDTO> skuLotAndStockTempPickList,
                                                                         List<SkuDTO> skuDTOList,
                                                                         Map<String, CargoOwnerDTO> cargoMap) {
        String taskId = UUID.randomUUID().toString();

        List<ReplenishRecommendB2BNewDTO> replenishRecommendB2BNewDTOList = new ArrayList<>();
        for (ShipmentOrderDetailDTO shipmentOrderDetailDTO : orderNeedDetailDTOList) {
            List<SkuLotAndStockDTO> lotAndStockDTOList;
            lotAndStockDTOList = skuLotAndStockTempPickList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(shipmentOrderDetailDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuQuality()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(shipmentOrderDetailDTO.getSkuLotNo())) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> Objects.equals(a.getSkuLotNo(), shipmentOrderDetailDTO.getSkuLotNo())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(shipmentOrderDetailDTO.getExternalSkuLotNo())) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> Objects.equals(a.getExternalSkuLotNo(), shipmentOrderDetailDTO.getExternalSkuLotNo())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(shipmentOrderDetailDTO.getExpireDate()) && shipmentOrderDetailDTO.getExpireDate() > 0L) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> Objects.equals(a.getExpireDate(), shipmentOrderDetailDTO.getExpireDate())).collect(Collectors.toList());
            }
            //效期范围
            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(shipmentOrderDetailDTO.getExpireDateStart()) && shipmentOrderDetailDTO.getExpireDateStart() > 0L) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> a.getExpireDate() >= shipmentOrderDetailDTO.getExpireDateStart()).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(shipmentOrderDetailDTO.getExpireDateEnd()) && shipmentOrderDetailDTO.getExpireDateEnd() > 0L) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> a.getExpireDate() <= shipmentOrderDetailDTO.getExpireDateEnd()).collect(Collectors.toList());
            }
            //淘天货主补货增加残次等级过滤
            CargoOwnerDTO cargoOwnerDTO = cargoMap.get(shipmentOrderDetailDTO.getCargoCode());
            if (cargoOwnerDTO != null && cargoOwnerDTO.getCargoTag() > 0 && CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO)) {
                if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(shipmentOrderDetailDTO.getInventoryType())) {
                    lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> Objects.equals(a.getInventoryType(), shipmentOrderDetailDTO.getInventoryType())).collect(Collectors.toList());
                }
            }
            Boolean isAppoint = false;
            //指定类型
            String appointType = "";
            //指定属性
            String appointTypeAttr = "";
            //指定字段排序
            Integer appointTypeOrder = 999;
            //批次编码,外部批次编码,失效日期不为空就为指定属性
            if (!StringUtils.isEmpty(shipmentOrderDetailDTO.getSkuLotNo())
                    || !StringUtils.isEmpty(shipmentOrderDetailDTO.getExternalSkuLotNo())
                    || shipmentOrderDetailDTO.getExpireDate() > 0
                    || shipmentOrderDetailDTO.getExpireDateStart() > 0
                    || shipmentOrderDetailDTO.getExpireDateEnd() > 0) {
                isAppoint = true;
            }
            if (shipmentOrderDetailDTO.getExpireDate() > 0) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDate(), "yyyy-MM-dd") + "至" + ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDate(), "yyyy-MM-dd");
            }
            //起止都有值
            if (shipmentOrderDetailDTO.getExpireDateStart() != null
                    && shipmentOrderDetailDTO.getExpireDateStart() > 0
                    && shipmentOrderDetailDTO.getExpireDateEnd() != null
                    && shipmentOrderDetailDTO.getExpireDateEnd() > 0) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDateStart(), "yyyy-MM-dd") + "至" + ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDateEnd(), "yyyy-MM-dd");
            }
            //起有值 止无值
            if (shipmentOrderDetailDTO.getExpireDateStart() != null
                    && shipmentOrderDetailDTO.getExpireDateStart() > 0
                    && (shipmentOrderDetailDTO.getExpireDateEnd() == null
                    || shipmentOrderDetailDTO.getExpireDateEnd() == 0)) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = "失效日期:" + ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDateStart(), "yyyy-MM-dd") + "及以后";
            }
            //止有值 起无值
            if (shipmentOrderDetailDTO.getExpireDateEnd() != null
                    && shipmentOrderDetailDTO.getExpireDateEnd() > 0
                    && (shipmentOrderDetailDTO.getExpireDateStart() == null
                    || shipmentOrderDetailDTO.getExpireDateStart() == 0)) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = "失效日期:" + ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDateEnd(), "yyyy-MM-dd") + "及以前";
            }
            if (!StringUtils.isEmpty(shipmentOrderDetailDTO.getExternalSkuLotNo())) {
                appointTypeOrder = 20;
                appointType = "指定外部批次ID";
                appointTypeAttr = shipmentOrderDetailDTO.getExternalSkuLotNo();
            }
            if (!StringUtils.isEmpty(shipmentOrderDetailDTO.getSkuLotNo())) {
                appointTypeOrder = 1;
                appointType = "指定批次ID";
                appointTypeAttr = shipmentOrderDetailDTO.getSkuLotNo();
            }
            //指定批次或者无库存 无指引生成
            if (CollectionUtils.isEmpty(lotAndStockDTOList)) {
                continue;
            }
            String turnoverRuleCode = "";
            String allocationRuleCode = "";
            SkuDTO skuDTO = skuDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(shipmentOrderDetailDTO.getCargoCode()))
                    .filter(a -> a.getCode().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuCode())).findFirst().orElse(null);
            if (skuDTO != null) {
                turnoverRuleCode = skuDTO.getTurnoverRuleCode();
                allocationRuleCode = skuDTO.getAllocationRuleCode();
            }
            lotAndStockDTOList = skuStockAndLotBiz.sortListStockLotAndLocation(taskId, shipmentOrderDetailDTO.getWarehouseCode(), lotAndStockDTOList, turnoverRuleCode, allocationRuleCode);
            //需求数量
            BigDecimal needQty = shipmentOrderDetailDTO.getExpSkuQty();
            //库存数量
            BigDecimal stockQty = lotAndStockDTOList.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            //库存足够
            if (stockQty.compareTo(needQty) >= 0) {
                for (SkuLotAndStockDTO stockDTO : lotAndStockDTOList) {
                    if (needQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    BigDecimal originLocationQty = stockDTO.getAvailableQty();
                    if (stockDTO.getAvailableQty().compareTo(needQty) >= 0) {
                        //用于移除内存数据
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(needQty));
                        //补货任务数据
                        buildReplenishRecommendB2BNewDTO(stockDTO, shipmentOrderDetailDTO, needQty, replenishRecommendB2BNewDTOList, needQty, appointType, appointTypeAttr, appointTypeOrder);
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        needQty = needQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);
                        //补货任务数据
                        buildReplenishRecommendB2BNewDTO(stockDTO, shipmentOrderDetailDTO, originLocationQty, replenishRecommendB2BNewDTOList, finalPlanQty, appointType, appointTypeAttr, appointTypeOrder);
                    }
                }
            } else {
                //库存不足
                for (SkuLotAndStockDTO stockDTO : lotAndStockDTOList) {
                    if (needQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    BigDecimal originLocationQty = stockDTO.getAvailableQty();
                    if (stockDTO.getAvailableQty().compareTo(needQty) >= 0) {
                        //用于移除内存数据
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(needQty));
                        //补货任务数据
                        buildReplenishRecommendB2BNewDTO(stockDTO, shipmentOrderDetailDTO, originLocationQty, replenishRecommendB2BNewDTOList, needQty, appointType, appointTypeAttr, appointTypeOrder);
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        needQty = needQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);
                        //补货任务数据
                        buildReplenishRecommendB2BNewDTO(stockDTO, shipmentOrderDetailDTO, originLocationQty, replenishRecommendB2BNewDTOList, finalPlanQty, appointType, appointTypeAttr, appointTypeOrder);
                    }
                }
            }
            //移除为0的数据
            skuLotAndStockTempPickList.removeIf(a -> a.getAvailableQty().compareTo(BigDecimal.ZERO) == 0);
        }

        List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = ObjectUtil.cloneByStream(orderNeedDetailDTOList);
        shipmentOrderDetailDTOList.removeIf(a -> a.getExpSkuQty().compareTo(BigDecimal.ZERO) == 0);
        //add 缺货数
        if (!CollectionUtils.isEmpty(shipmentOrderDetailDTOList)) {
            List<ReplenishRecommendB2BNewDTO> replenishRecommendDiffDTOList = buildReplenishRecommendB2BDiffDTOList(shipmentOrderDetailDTOList, cargoMap);
            replenishRecommendB2BNewDTOList.addAll(replenishRecommendDiffDTOList);
        }


        //相同商品+指定批次+补货批次+库位+正次品合并
        if (replenishRecommendB2BNewDTOList.size() > 1) {
            return mergeReplenishRecommendB2BNew(replenishRecommendB2BNewDTOList);
        } else {
            return replenishRecommendB2BNewDTOList;
        }
    }

    private List<ReplenishRecommendB2BNewDTO> buildReplenishRecommendB2BDiffDTOList(List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList, Map<String, CargoOwnerDTO> cargoMap) {
        List<ReplenishRecommendB2BNewDTO> replenishRecommendDTOList = new ArrayList<>();
        for (ShipmentOrderDetailDTO shipmentOrderDetailDTO : shipmentOrderDetailDTOList) {

            Boolean isAppoint = false;
            //指定类型
            String appointType = "";
            //指定属性
            String appointTypeAttr = "";
            Integer appointTypeOrder = 999;
            //批次编码,外部批次编码,失效日期不为空就为指定属性
            if (!StringUtils.isEmpty(shipmentOrderDetailDTO.getSkuLotNo())
                    || !StringUtils.isEmpty(shipmentOrderDetailDTO.getExternalSkuLotNo())
                    || shipmentOrderDetailDTO.getExpireDate() > 0
                    || shipmentOrderDetailDTO.getExpireDateStart() > 0
                    || shipmentOrderDetailDTO.getExpireDateEnd() > 0) {
                isAppoint = true;
            }
            if (shipmentOrderDetailDTO.getExpireDate() > 0) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDate(), "yyyy-MM-dd") + "至" + ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDate(), "yyyy-MM-dd");

            }
            //起止都有值
            if (shipmentOrderDetailDTO.getExpireDateStart() != null
                    && shipmentOrderDetailDTO.getExpireDateStart() > 0
                    && shipmentOrderDetailDTO.getExpireDateEnd() != null
                    && shipmentOrderDetailDTO.getExpireDateEnd() > 0) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDateStart(), "yyyy-MM-dd") + "至" + ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDateEnd(), "yyyy-MM-dd");
            }
            //起有值 止无值
            if (shipmentOrderDetailDTO.getExpireDateStart() != null
                    && shipmentOrderDetailDTO.getExpireDateStart() > 0
                    && (shipmentOrderDetailDTO.getExpireDateEnd() == null
                    || shipmentOrderDetailDTO.getExpireDateEnd() == 0)) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = "失效日期:" + ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDateStart(), "yyyy-MM-dd") + "及以后";
            }
            //止有值 起无值
            if (shipmentOrderDetailDTO.getExpireDateEnd() != null
                    && shipmentOrderDetailDTO.getExpireDateEnd() > 0
                    && (shipmentOrderDetailDTO.getExpireDateStart() == null
                    || shipmentOrderDetailDTO.getExpireDateStart() == 0)) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = "失效日期:" + ConverterUtil.convertVoTime(shipmentOrderDetailDTO.getExpireDateEnd(), "yyyy-MM-dd") + "及以前";
            }
            if (!StringUtils.isEmpty(shipmentOrderDetailDTO.getExternalSkuLotNo())) {
                appointTypeOrder = 20;
                appointType = "指定外部批次ID";
                appointTypeAttr = shipmentOrderDetailDTO.getExternalSkuLotNo();
            }
            if (!StringUtils.isEmpty(shipmentOrderDetailDTO.getSkuLotNo())) {
                appointTypeOrder = 1;
                appointType = "指定批次ID";
                appointTypeAttr = shipmentOrderDetailDTO.getSkuLotNo();
            }
            appointType = "【缺货】" + appointType;
            //补货任务数据 差异数
            buildReplenishRecommendB2BDiffDTO(shipmentOrderDetailDTO, replenishRecommendDTOList, isAppoint, appointType, appointTypeAttr, appointTypeOrder, cargoMap);

        }
        return replenishRecommendDTOList;
    }

    private void buildReplenishRecommendB2BDiffDTO(ShipmentOrderDetailDTO shipmentOrderDetailDTO, List<ReplenishRecommendB2BNewDTO> replenishRecommendDTOList, Boolean isAppoint, String appointType, String appointTypeAttr, Integer appointTypeOrder, Map<String, CargoOwnerDTO> cargoMap) {
        ReplenishRecommendB2BNewDTO replenishRecommendDTO;

        CargoOwnerDTO cargoOwnerDTO = cargoMap.get(shipmentOrderDetailDTO.getCargoCode());
        if (cargoOwnerDTO != null && cargoOwnerDTO.getCargoTag() > 0 && CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO)) {
            //查找相同货主,商品,正次品,库位,批次
            replenishRecommendDTO = replenishRecommendDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(shipmentOrderDetailDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuQuality()))
                    .filter(a -> a.getInventoryType().equalsIgnoreCase(shipmentOrderDetailDTO.getInventoryType()))
                    .filter(a -> a.getSkuLotNo().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuLotNo()))
                    .filter(a -> a.getAppointType().equalsIgnoreCase(appointType))
                    .filter(a -> a.getAppointTypeAttr().equalsIgnoreCase(appointTypeAttr))
                    .findFirst().orElse(null);
        } else {
            //查找相同货主,商品,正次品,库位,批次
            replenishRecommendDTO = replenishRecommendDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(shipmentOrderDetailDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuQuality()))
                    .filter(a -> a.getSkuLotNo().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuLotNo()))
                    .filter(a -> a.getAppointType().equalsIgnoreCase(appointType))
                    .filter(a -> a.getAppointTypeAttr().equalsIgnoreCase(appointTypeAttr))
                    .findFirst().orElse(null);
        }
        if (replenishRecommendDTO == null) {
            ReplenishRecommendB2BNewDTO recommendDTO = ConverterUtil.convert(shipmentOrderDetailDTO, ReplenishRecommendB2BNewDTO.class);
            Integer maxOrder = replenishRecommendDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(shipmentOrderDetailDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuQuality()))
                    .map(ReplenishRecommendB2BNewDTO::getOrder)
                    .max(Integer::compareTo).orElse(999);
            recommendDTO.setOrder(maxOrder + 1);
            recommendDTO.setAppointTypeOrder(appointTypeOrder);
            recommendDTO.setUpcCode(shipmentOrderDetailDTO.getUpcCode());
            recommendDTO.setAvailableQty(BigDecimal.ZERO);
            recommendDTO.setQty(BigDecimal.ZERO);
            recommendDTO.setNeedQty(shipmentOrderDetailDTO.getExpSkuQty());
            recommendDTO.setDefQty(shipmentOrderDetailDTO.getExpSkuQty());
            recommendDTO.setAppointType(appointType);
            recommendDTO.setAppointTypeAttr(appointTypeAttr);
            replenishRecommendDTOList.add(recommendDTO);
        } else {
            replenishRecommendDTO.setNeedQty(replenishRecommendDTO.getNeedQty().add(shipmentOrderDetailDTO.getExpSkuQty()));
            replenishRecommendDTO.setDefQty(replenishRecommendDTO.getDefQty().add(shipmentOrderDetailDTO.getExpSkuQty()));
        }
    }

    /**
     * @param stockDTO
     * @param shipmentOrderDetailDTO
     * @param originLocationQty
     * @param replenishRecommendB2BNewDTOList
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2022/10/28 9:23
     */
    private void buildReplenishRecommendB2BNewDTO(SkuLotAndStockDTO stockDTO,
                                                  ShipmentOrderDetailDTO shipmentOrderDetailDTO,
                                                  BigDecimal originLocationQty,
                                                  List<ReplenishRecommendB2BNewDTO> replenishRecommendB2BNewDTOList,
                                                  BigDecimal needQty,
                                                  String appointType,
                                                  String appointTypeAttr,
                                                  Integer appointTypeOrder) {
        //查找相同货主,商品,正次品,库位,批次
        ReplenishRecommendB2BNewDTO replenishRecommendDTO = replenishRecommendB2BNewDTOList.stream()
                .filter(a -> a.getCargoCode().equalsIgnoreCase(stockDTO.getCargoCode()))
                .filter(a -> a.getShipmentOrderCode().equalsIgnoreCase(shipmentOrderDetailDTO.getShipmentOrderCode()))
                .filter(a -> a.getAppointSkuLotNo().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuLotNo()))
                .filter(a -> a.getSkuCode().equalsIgnoreCase(stockDTO.getSkuCode()))
                .filter(a -> a.getSkuQuality().equalsIgnoreCase(stockDTO.getSkuQuality()))
                .filter(a -> a.getLocationCode().equalsIgnoreCase(stockDTO.getLocationCode()))
                .filter(a -> a.getSkuLotNo().equalsIgnoreCase(stockDTO.getSkuLotNo()))
                .filter(a -> a.getAppointType().equalsIgnoreCase(appointType))
                .filter(a -> a.getAppointTypeAttr().equalsIgnoreCase(appointTypeAttr))
                .findFirst().orElse(null);
        if (replenishRecommendDTO == null) {
            ReplenishRecommendB2BNewDTO recommendDTO = ConverterUtil.convert(stockDTO, ReplenishRecommendB2BNewDTO.class);
            Integer maxOrder = replenishRecommendB2BNewDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(stockDTO.getCargoCode()))
                    .filter(a -> a.getShipmentOrderCode().equalsIgnoreCase(shipmentOrderDetailDTO.getShipmentOrderCode()))
                    .filter(a -> a.getAppointSkuLotNo().equalsIgnoreCase(shipmentOrderDetailDTO.getSkuLotNo()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(stockDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(stockDTO.getSkuQuality()))
                    .map(ReplenishRecommendB2BNewDTO::getOrder)
                    .max(Integer::compareTo).orElse(0);
            recommendDTO.setOrder(maxOrder + 1);
            recommendDTO.setAppointTypeOrder(appointTypeOrder);
            recommendDTO.setUpcCode(shipmentOrderDetailDTO.getUpcCode());
            recommendDTO.setShipmentOrderCode(shipmentOrderDetailDTO.getShipmentOrderCode());
            recommendDTO.setAppointSkuLotNo(shipmentOrderDetailDTO.getSkuLotNo());
            recommendDTO.setAvailableQty(originLocationQty);
            recommendDTO.setQty(originLocationQty);
            recommendDTO.setNeedQty(needQty);
            recommendDTO.setDefQty(BigDecimal.ZERO);
            recommendDTO.setSkuQualityName(SkuQualityEnum.getEnum(recommendDTO.getSkuQuality()).getMessage());
            recommendDTO.setManufDate(stockDTO.getManufDate());
            recommendDTO.setManufDateFormat(ConverterUtil.convertVoTime(stockDTO.getManufDate(), "yyyy-MM-dd"));
            recommendDTO.setExpireDate(stockDTO.getExpireDate());
            recommendDTO.setExpireDateFormat(ConverterUtil.convertVoTime(stockDTO.getExpireDate(), "yyyy-MM-dd"));
            recommendDTO.setReceiveDate(stockDTO.getReceiveDate());
            recommendDTO.setReceiveDateFormat(ConverterUtil.convertVoTime(stockDTO.getReceiveDate(), "yyyy-MM-dd"));
            recommendDTO.setAppointType(appointType);
            recommendDTO.setAppointTypeAttr(appointTypeAttr);

            replenishRecommendB2BNewDTOList.add(recommendDTO);
        } else {
            replenishRecommendDTO.setQty(replenishRecommendDTO.getQty().add(originLocationQty));
            replenishRecommendDTO.setAvailableQty(replenishRecommendDTO.getAvailableQty().add(originLocationQty));
            replenishRecommendDTO.setNeedQty(replenishRecommendDTO.getNeedQty().add(needQty));
        }
    }

    /**
     * @param replenishRecommendB2BNewDTOList
     * @return java.util.List<com.dt.platform.wms.dto.replenish.ReplenishRecommendB2BNewDTO>
     * <AUTHOR>
     * @describe: 合并货主+商品+指定批次+补货批次+库位+正次品合并
     * @date 2022/10/27 15:48
     */
    private List<ReplenishRecommendB2BNewDTO> mergeReplenishRecommendB2BNew
    (List<ReplenishRecommendB2BNewDTO> replenishRecommendB2BNewDTOList) {
        List<ReplenishRecommendB2BNewDTO> recommendB2BNewDTOList = new ArrayList<>();
        Map<String, List<ReplenishRecommendB2BNewDTO>> map = replenishRecommendB2BNewDTOList.stream()
                .collect(Collectors.groupingBy(a -> StrUtil.join("#",
                        a.getCargoCode(),
                        a.getShipmentOrderCode(),
                        a.getSkuCode(),
                        a.getLocationCode(),
                        a.getSkuLotNo(),
                        a.getAppointSkuLotNo(),
                        a.getSkuQuality(),
                        a.getAppointType(),
                        a.getAppointTypeAttr())));
        map.entrySet().forEach(it -> {
            ReplenishRecommendB2BNewDTO replenishRecommendB2BNewDTO = ObjectUtil.cloneByStream(it.getValue().get(0));
            replenishRecommendB2BNewDTO.setQty(it.getValue().stream().map(ReplenishRecommendB2BNewDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            replenishRecommendB2BNewDTO.setNeedQty(it.getValue().stream().map(ReplenishRecommendB2BNewDTO::getNeedQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            replenishRecommendB2BNewDTO.setDefQty(it.getValue().stream().map(ReplenishRecommendB2BNewDTO::getDefQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            recommendB2BNewDTOList.add(replenishRecommendB2BNewDTO);
        });
        return recommendB2BNewDTOList;
    }

    /**
     * @param orderDetailDTOList
     * @param skuLotAndStockTempPickList
     * @return java.util.List<com.dt.domain.bill.dto.ShipmentOrderDetailDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/10/27 15:27
     */
    private List<ShipmentOrderDetailDTO> calculationNeedShipmentDetail(List<ShipmentOrderDetailDTO> orderDetailDTOList,
                                                                       List<SkuLotAndStockDTO> skuLotAndStockTempPickList,
                                                                       ShipmentOrderDTO shipmentOrderDTO) {
        for (ShipmentOrderDetailDTO entity : orderDetailDTOList) {
            List<SkuLotAndStockDTO> skuLotAndStockDTOS = getB2BSkuLotAndStock(entity, skuLotAndStockTempPickList, shipmentOrderDTO);
            if (CollectionUtils.isEmpty(skuLotAndStockDTOS)) {
                continue;
            }
            //获取当前商品库存最大数据
            BigDecimal actualQty = skuLotAndStockDTOS.stream()
                    .map(SkuLotAndStockDTO::getAvailableQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (actualQty.compareTo(entity.getExpSkuQty()) >= 0) {
                BigDecimal planQty = entity.getExpSkuQty();
                for (SkuLotAndStockDTO stockDTO : skuLotAndStockDTOS) {
                    if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = planQty;
                        entity.setExpSkuQty(entity.getExpSkuQty().subtract(finalPlanQty));
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                        planQty = BigDecimal.ZERO;
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        entity.setExpSkuQty(entity.getExpSkuQty().subtract(finalPlanQty));
                        planQty = planQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);

                    }
                }
            } else {
                //当前库存不满足分配
                BigDecimal planQty = actualQty;
                entity.setExpSkuQty(entity.getExpSkuQty().subtract(actualQty));
                for (SkuLotAndStockDTO stockDTO : skuLotAndStockDTOS) {
                    if (planQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    if (stockDTO.getAvailableQty().compareTo(planQty) >= 0) {
                        //用于移除内存数据
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(planQty));
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        planQty = planQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);
                    }
                }
            }
        }
        return orderDetailDTOList.stream().filter(a -> a.getExpSkuQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
    }

    /**
     * @param entity
     * @param skuLotAndStockTempPickList
     * @return java.util.List<com.dt.platform.wms.dto.wave.SkuLotAndStockDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/10/27 15:35
     */
    private List<SkuLotAndStockDTO> getB2BSkuLotAndStock(ShipmentOrderDetailDTO entity, List<SkuLotAndStockDTO> skuLotAndStockTempPickList, ShipmentOrderDTO shipmentOrderDTO) {
        List<SkuLotAndStockDTO> skuLotAndStockDTOTargetList;
        skuLotAndStockDTOTargetList = skuLotAndStockTempPickList.stream()
                .filter(a -> a.getSkuCode().equalsIgnoreCase(entity.getSkuCode()))
                .filter(a -> a.getSkuQuality().equalsIgnoreCase(entity.getSkuQuality()))
                .filter(a -> a.getCargoCode().equalsIgnoreCase(entity.getCargoCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(entity.getSkuLotNo())) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getSkuLotNo(), entity.getSkuLotNo())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(entity.getExternalSkuLotNo())) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getExternalSkuLotNo(), entity.getExternalSkuLotNo())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(entity.getExpireDate()) && entity.getExpireDate() > 0L) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getExpireDate(), entity.getExpireDate())).collect(Collectors.toList());
        }
        //效期范围
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(entity.getExpireDateStart()) && entity.getExpireDateStart() > 0L) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> a.getExpireDate() >= entity.getExpireDateStart()).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(entity.getExpireDateEnd()) && entity.getExpireDateEnd() > 0L) {
            skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> a.getExpireDate() <= entity.getExpireDateEnd()).collect(Collectors.toList());
        }
        //效期范围
        //指定残次等级  淘天仓强校验
        if (shipmentOrderDTO.getOrderTag() > 0 && OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.TAOTAIN)) {
            if (!CollectionUtils.isEmpty(skuLotAndStockDTOTargetList) && !StringUtils.isEmpty(entity.getInventoryType())) {
                skuLotAndStockDTOTargetList = skuLotAndStockDTOTargetList.stream().filter(a -> Objects.equals(a.getInventoryType(), entity.getInventoryType())).collect(Collectors.toList());
            }
        }
        return skuLotAndStockDTOTargetList;
    }

    /**
     * @param skuDTOList
     * @param replenishTaskNewList
     * @param skuLotAndStockList
     * @return java.util.List<com.dt.platform.wms.dto.replenish.ReplenishRecommendDTO>
     * @author: WuXian
     * description:  获取补货数据----20250409
     * create time: 2021/7/6 14:35
     */
    private List<ReplenishRecommendDTO> analysisReplenishRecommend(List<SkuDTO> skuDTOList,
                                                                   List<ReplenishTaskNewDTO> replenishTaskNewList,
                                                                   List<SkuLotAndStockDTO> skuLotAndStockList) {
        String taskId = UUID.randomUUID().toString();
        //获取当前补货任务 商品排序, 先处理批次补货
        replenishTaskNewList = replenishTaskNewList.stream()
                .sorted(Comparator.comparing(ReplenishTaskNewDTO::getSkuCode, Comparator.naturalOrder())
                        .thenComparing(ReplenishTaskNewDTO::getSkuLotNo, Comparator.reverseOrder())
                        .thenComparing(ReplenishTaskNewDTO::getExpireDate, Comparator.reverseOrder())
                        .thenComparing(ReplenishTaskNewDTO::getExpireDateStart, Comparator.reverseOrder())
                        .thenComparing(ReplenishTaskNewDTO::getExpireDateEnd, Comparator.reverseOrder())
                        .thenComparing(ReplenishTaskNewDTO::getExternalSkuLotNo, Comparator.reverseOrder())
                        .thenComparing(ReplenishTaskNewDTO::getWithdrawCompareDate, Comparator.reverseOrder())
                ).collect(Collectors.toList());
        List<ReplenishRecommendDTO> replenishRecommendDTOList = new ArrayList<>();
        for (ReplenishTaskNewDTO replenishTaskNewDTO : replenishTaskNewList) {

            replenishTaskNewDTO.setDiffQty(BigDecimal.ZERO);

            List<SkuLotAndStockDTO> lotAndStockDTOList;
            Boolean isAppoint = false;
            lotAndStockDTOList = skuLotAndStockList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(replenishTaskNewDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(replenishTaskNewDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(replenishTaskNewDTO.getSkuQuality()))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(replenishTaskNewDTO.getSkuLotNo())) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> Objects.equals(a.getSkuLotNo(), replenishTaskNewDTO.getSkuLotNo())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(replenishTaskNewDTO.getExternalSkuLotNo())) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> Objects.equals(a.getExternalSkuLotNo(), replenishTaskNewDTO.getExternalSkuLotNo())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(replenishTaskNewDTO.getExpireDate()) && replenishTaskNewDTO.getExpireDate() > 0L) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> Objects.equals(a.getExpireDate(), replenishTaskNewDTO.getExpireDate())).collect(Collectors.toList());
            }
            //效期范围
            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(replenishTaskNewDTO.getExpireDateStart()) && replenishTaskNewDTO.getExpireDateStart() > 0L) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> a.getExpireDate() >= replenishTaskNewDTO.getExpireDateStart()).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(replenishTaskNewDTO.getExpireDateEnd()) && replenishTaskNewDTO.getExpireDateEnd() > 0L) {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> a.getExpireDate() <= replenishTaskNewDTO.getExpireDateEnd()).collect(Collectors.toList());
            }
            //指定残次等级  淘天货主强校验
            if (replenishTaskNewDTO.getTaoTianCargo() != null && replenishTaskNewDTO.getTaoTianCargo()) {
                if (!CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(replenishTaskNewDTO.getInventoryType())) {
                    lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> Objects.equals(a.getInventoryType(), replenishTaskNewDTO.getInventoryType())).collect(Collectors.toList());
                }
            }

            String turnoverRuleCode = "";
            String allocationRuleCode = "";
            SkuDTO skuDTO = skuDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(replenishTaskNewDTO.getCargoCode()))
                    .filter(a -> a.getCode().equalsIgnoreCase(replenishTaskNewDTO.getSkuCode())).findFirst().orElse(null);
            if (skuDTO != null) {
                turnoverRuleCode = skuDTO.getTurnoverRuleCode();
                allocationRuleCode = skuDTO.getAllocationRuleCode();
            }
            //比对禁售时间 效期品比对
            if (Objects.equals(skuDTO.getIsLifeMgt(), SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode())
                    && !CollectionUtils.isEmpty(lotAndStockDTOList) && !StringUtils.isEmpty(replenishTaskNewDTO.getWithdrawCompareDate()) && replenishTaskNewDTO.getWithdrawCompareDate() > 0L) {
                lotAndStockDTOList = lotAndStockDTOList.stream()
                        .filter(a -> !a.getOverExpire())
                        .filter(a -> a.getWithdrawDate() > replenishTaskNewDTO.getWithdrawCompareDate()).collect(Collectors.toList());
            } else {
                lotAndStockDTOList = lotAndStockDTOList.stream().filter(a -> !a.getIsWithdraw()).collect(Collectors.toList());
            }
            //指定类型
            String appointType = "";
            //指定属性
            String appointTypeAttr = "";
            Integer appointTypeOrder = 999;
            //批次编码,外部批次编码,失效日期不为空就为指定属性
            if (!StringUtils.isEmpty(replenishTaskNewDTO.getSkuLotNo())
                    || !StringUtils.isEmpty(replenishTaskNewDTO.getExternalSkuLotNo())
                    || replenishTaskNewDTO.getExpireDate() > 0
                    || replenishTaskNewDTO.getExpireDateStart() > 0
                    || replenishTaskNewDTO.getExpireDateEnd() > 0) {
                isAppoint = true;
            }
            if (replenishTaskNewDTO.getExpireDate() > 0) {
//                appointType = "指定失效日期";
//                appointTypeAttr = ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDate(), "yyyy-MM-dd");

                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDate(), "yyyy-MM-dd") + "至" + ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDate(), "yyyy-MM-dd");
            }
            //起止都有值
            if (replenishTaskNewDTO.getExpireDateStart() != null
                    && replenishTaskNewDTO.getExpireDateStart() > 0
                    && replenishTaskNewDTO.getExpireDateEnd() != null
                    && replenishTaskNewDTO.getExpireDateEnd() > 0) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDateStart(), "yyyy-MM-dd") + "至" + ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDateEnd(), "yyyy-MM-dd");
            }
            //起有值 止无值
            if (replenishTaskNewDTO.getExpireDateStart() != null
                    && replenishTaskNewDTO.getExpireDateStart() > 0
                    && (replenishTaskNewDTO.getExpireDateEnd() == null
                    || replenishTaskNewDTO.getExpireDateEnd() == 0)) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = "失效日期:" + ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDateStart(), "yyyy-MM-dd") + "及以后";
            }
            //止有值 起无值
            if (replenishTaskNewDTO.getExpireDateEnd() != null
                    && replenishTaskNewDTO.getExpireDateEnd() > 0
                    && (replenishTaskNewDTO.getExpireDateStart() == null
                    || replenishTaskNewDTO.getExpireDateStart() == 0)) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = "失效日期:" + ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDateEnd(), "yyyy-MM-dd") + "及以前";
            }
            if (!StringUtils.isEmpty(replenishTaskNewDTO.getExternalSkuLotNo())) {
                appointTypeOrder = 20;
                appointType = "指定外部批次ID";
                appointTypeAttr = replenishTaskNewDTO.getExternalSkuLotNo();
            }
            if (!StringUtils.isEmpty(replenishTaskNewDTO.getSkuLotNo())) {
                appointTypeOrder = 1;
                appointType = "指定批次ID";
                appointTypeAttr = replenishTaskNewDTO.getSkuLotNo();
            }

            //指定批次或者无库存 无指引生成
            if (CollectionUtils.isEmpty(lotAndStockDTOList)) {
                replenishTaskNewDTO.setDiffQty(replenishTaskNewDTO.getDefQty());
                continue;
            }

            lotAndStockDTOList = skuStockAndLotBiz.sortListStockLotAndLocation(taskId, replenishTaskNewDTO.getWarehouseCode(), lotAndStockDTOList, turnoverRuleCode, allocationRuleCode);
            //需求数量
            BigDecimal needQty = replenishTaskNewDTO.getDefQty();
            //库存数量
            BigDecimal stockQty = lotAndStockDTOList.stream().map(SkuLotAndStockDTO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            //库存足够
            if (stockQty.compareTo(needQty) >= 0) {
                for (SkuLotAndStockDTO stockDTO : lotAndStockDTOList) {

                    if (needQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    BigDecimal originLocationQty = stockDTO.getAvailableQty();
                    if (stockDTO.getAvailableQty().compareTo(needQty) >= 0) {
                        //用于移除内存数据
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(needQty));
                        //补货任务数据
                        buildReplenishRecommendDTO(stockDTO, replenishTaskNewDTO.getUpcCode(), originLocationQty, replenishRecommendDTOList, isAppoint, needQty, appointType, appointTypeAttr, appointTypeOrder);
                        needQty = BigDecimal.ZERO;
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        needQty = needQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);
                        //补货任务数据
                        buildReplenishRecommendDTO(stockDTO, replenishTaskNewDTO.getUpcCode(), originLocationQty, replenishRecommendDTOList, isAppoint, finalPlanQty, appointType, appointTypeAttr, appointTypeOrder);
                    }
                }
            } else {
                //库存不足
                for (SkuLotAndStockDTO stockDTO : lotAndStockDTOList) {
                    if (needQty.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    if (stockDTO.getAvailableQty().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    BigDecimal originLocationQty = stockDTO.getAvailableQty();
                    if (stockDTO.getAvailableQty().compareTo(needQty) >= 0) {
                        //用于移除内存数据
                        stockDTO.setAvailableQty(stockDTO.getAvailableQty().subtract(needQty));
                        //补货任务数据
                        buildReplenishRecommendDTO(stockDTO, replenishTaskNewDTO.getUpcCode(), originLocationQty, replenishRecommendDTOList, isAppoint, needQty, appointType, appointTypeAttr, appointTypeOrder);
                        needQty = BigDecimal.ZERO;
                        break;
                    } else {
                        //用于移除内存数据
                        BigDecimal finalPlanQty = stockDTO.getAvailableQty();
                        needQty = needQty.subtract(finalPlanQty);
                        stockDTO.setAvailableQty(BigDecimal.ZERO);
                        //补货任务数据
                        buildReplenishRecommendDTO(stockDTO, replenishTaskNewDTO.getUpcCode(), originLocationQty, replenishRecommendDTOList, isAppoint, finalPlanQty, appointType, appointTypeAttr, appointTypeOrder);
                    }
                }
            }
            replenishTaskNewDTO.setDiffQty(needQty);
            //移除为0的数据
            skuLotAndStockList.removeIf(a -> a.getAvailableQty().compareTo(BigDecimal.ZERO) == 0);
        }
        List<ReplenishTaskNewDTO> replenishTaskNewDTOList = ObjectUtil.cloneByStream(replenishTaskNewList);
        replenishTaskNewDTOList.removeIf(a -> a.getDiffQty().compareTo(BigDecimal.ZERO) == 0);
        //add 缺货数
        if (!CollectionUtils.isEmpty(replenishTaskNewDTOList)) {
            List<ReplenishRecommendDTO> replenishRecommendDiffDTOList = buildReplenishRecommendDiffDTOList(replenishTaskNewDTOList);
            replenishRecommendDTOList.addAll(replenishRecommendDiffDTOList);
        }
        replenishRecommendDTOList = replenishRecommendDTOList.stream()
                .sorted(Comparator.comparing(ReplenishRecommendDTO::getCargoCode, Comparator.naturalOrder())
                        .thenComparing(ReplenishRecommendDTO::getSkuCode, Comparator.reverseOrder())
                        .thenComparing(ReplenishRecommendDTO::getOrder, Comparator.naturalOrder())
                        .thenComparing(ReplenishRecommendDTO::getAppointTypeOrder, Comparator.naturalOrder())
                        .thenComparing(ReplenishRecommendDTO::getAppointType, Comparator.reverseOrder())
                        .thenComparing(ReplenishRecommendDTO::getAppointTypeAttr, Comparator.reverseOrder())
                        .thenComparing(ReplenishRecommendDTO::getSkuLotNo, Comparator.reverseOrder())
                        .thenComparing(ReplenishRecommendDTO::getExternalSkuLotNo, Comparator.reverseOrder())
                        .thenComparing(ReplenishRecommendDTO::getExpireDate, Comparator.reverseOrder())
                ).collect(Collectors.toList());
        return replenishRecommendDTOList;
    }

    private List<ReplenishRecommendDTO> buildReplenishRecommendDiffDTOList(List<ReplenishTaskNewDTO> replenishTaskNewDTOList) {
        List<ReplenishRecommendDTO> replenishRecommendDTOList = new ArrayList<>();
        for (ReplenishTaskNewDTO replenishTaskNewDTO : replenishTaskNewDTOList) {

            Boolean isAppoint = false;
            //指定类型
            String appointType = "";
            //指定属性
            String appointTypeAttr = "";
            Integer appointTypeOrder = 999;
            //批次编码,外部批次编码,失效日期不为空就为指定属性
            if (!StringUtils.isEmpty(replenishTaskNewDTO.getSkuLotNo())
                    || !StringUtils.isEmpty(replenishTaskNewDTO.getExternalSkuLotNo())
                    || replenishTaskNewDTO.getExpireDate() > 0
                    || replenishTaskNewDTO.getExpireDateStart() > 0
                    || replenishTaskNewDTO.getExpireDateEnd() > 0) {
                isAppoint = true;
            }
            if (replenishTaskNewDTO.getExpireDate() > 0) {
//                appointType = "指定失效日期";
//                appointTypeAttr = ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDate(), "yyyy-MM-dd");
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDate(), "yyyy-MM-dd") + "至" + ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDate(), "yyyy-MM-dd");

            }
            //起止都有值
            if (replenishTaskNewDTO.getExpireDateStart() != null
                    && replenishTaskNewDTO.getExpireDateStart() > 0
                    && replenishTaskNewDTO.getExpireDateEnd() != null
                    && replenishTaskNewDTO.getExpireDateEnd() > 0) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDateStart(), "yyyy-MM-dd") + "至" + ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDateEnd(), "yyyy-MM-dd");
            }
            //起有值 止无值
            if (replenishTaskNewDTO.getExpireDateStart() != null
                    && replenishTaskNewDTO.getExpireDateStart() > 0
                    && (replenishTaskNewDTO.getExpireDateEnd() == null
                    || replenishTaskNewDTO.getExpireDateEnd() == 0)) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = "失效日期:" + ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDateStart(), "yyyy-MM-dd") + "及以后";
            }
            //止有值 起无值
            if (replenishTaskNewDTO.getExpireDateEnd() != null
                    && replenishTaskNewDTO.getExpireDateEnd() > 0
                    && (replenishTaskNewDTO.getExpireDateStart() == null
                    || replenishTaskNewDTO.getExpireDateStart() == 0)) {
                appointTypeOrder = 10;
                appointType = "指定失效日期范围";
                appointTypeAttr = "失效日期:" + ConverterUtil.convertVoTime(replenishTaskNewDTO.getExpireDateEnd(), "yyyy-MM-dd") + "及以前";
            }
            if (!StringUtils.isEmpty(replenishTaskNewDTO.getExternalSkuLotNo())) {
                appointTypeOrder = 20;
                appointType = "指定外部批次ID";
                appointTypeAttr = replenishTaskNewDTO.getExternalSkuLotNo();
            }
            if (!StringUtils.isEmpty(replenishTaskNewDTO.getSkuLotNo())) {
                appointTypeOrder = 1;
                appointType = "指定批次ID";
                appointTypeAttr = replenishTaskNewDTO.getSkuLotNo();
            }
            appointType = "【缺货】" + appointType;
            //补货任务数据 差异数
            buildReplenishRecommendDiffDTO(replenishTaskNewDTO, replenishRecommendDTOList, isAppoint, appointType, appointTypeAttr, appointTypeOrder);

        }
        return replenishRecommendDTOList;
    }

    private void buildReplenishRecommendDiffDTO(ReplenishTaskNewDTO replenishTaskNewDTO, List<ReplenishRecommendDTO> replenishRecommendDTOList, Boolean isAppoint, String appointType, String appointTypeAttr, Integer appointTypeOrder) {
        //查找相同货主,商品,正次品,库位,批次
        ReplenishRecommendDTO replenishRecommendDTO;
        if (replenishTaskNewDTO.getTaoTianCargo()) {
            replenishRecommendDTO = replenishRecommendDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(replenishTaskNewDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(replenishTaskNewDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(replenishTaskNewDTO.getSkuQuality()))
                    .filter(a -> a.getInventoryType().equalsIgnoreCase(replenishTaskNewDTO.getInventoryType()))
                    .filter(a -> a.getSkuLotNo().equalsIgnoreCase(replenishTaskNewDTO.getSkuLotNo()))
                    .filter(a -> a.getAppointType().equalsIgnoreCase(appointType))
                    .filter(a -> a.getAppointTypeAttr().equalsIgnoreCase(appointTypeAttr))
                    .findFirst().orElse(null);
        } else {
            replenishRecommendDTO = replenishRecommendDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(replenishTaskNewDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(replenishTaskNewDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(replenishTaskNewDTO.getSkuQuality()))
                    .filter(a -> a.getSkuLotNo().equalsIgnoreCase(replenishTaskNewDTO.getSkuLotNo()))
                    .filter(a -> a.getAppointType().equalsIgnoreCase(appointType))
                    .filter(a -> a.getAppointTypeAttr().equalsIgnoreCase(appointTypeAttr))
                    .findFirst().orElse(null);
        }
        if (replenishRecommendDTO == null) {
            ReplenishRecommendDTO recommendDTO = ConverterUtil.convert(replenishTaskNewDTO, ReplenishRecommendDTO.class);
            Integer maxOrder = replenishRecommendDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(replenishTaskNewDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(replenishTaskNewDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(replenishTaskNewDTO.getSkuQuality()))
                    .map(ReplenishRecommendDTO::getOrder)
                    .max(Integer::compareTo).orElse(999);
            recommendDTO.setOrder(maxOrder + 1);
            recommendDTO.setAppointTypeOrder(appointTypeOrder);
            recommendDTO.setUpcCode(replenishTaskNewDTO.getUpcCode());
            recommendDTO.setAvailableQty(BigDecimal.ZERO);
            recommendDTO.setNeedQty(replenishTaskNewDTO.getDiffQty());
            recommendDTO.setDefQty(replenishTaskNewDTO.getDiffQty());
            recommendDTO.setIsAppoint(isAppoint);
            recommendDTO.setAppointType(appointType);
            recommendDTO.setAppointTypeAttr(appointTypeAttr);
            replenishRecommendDTOList.add(recommendDTO);
        } else {
            replenishRecommendDTO.setNeedQty(replenishRecommendDTO.getNeedQty().add(replenishTaskNewDTO.getDiffQty()));
            replenishRecommendDTO.setDefQty(replenishRecommendDTO.getDefQty().add(replenishTaskNewDTO.getDiffQty()));
        }
    }

    /**
     * @param stockDTO
     * @param upcCode
     * @param originLocationQty
     * @param replenishRecommendDTOList
     * @return void
     * @author: WuXian
     * description:
     * create time: 2021/7/6 15:02
     */
    private void buildReplenishRecommendDTO(SkuLotAndStockDTO stockDTO,
                                            String upcCode,
                                            BigDecimal originLocationQty,
                                            List<ReplenishRecommendDTO> replenishRecommendDTOList,
                                            Boolean isAppoint,
                                            BigDecimal needQty,
                                            String appointType,
                                            String appointTypeAttr,
                                            Integer appointTypeOrder) {
        //查找相同货主,商品,正次品,库位,批次
        ReplenishRecommendDTO replenishRecommendDTO = replenishRecommendDTOList.stream()
                .filter(a -> a.getCargoCode().equalsIgnoreCase(stockDTO.getCargoCode()))
                .filter(a -> a.getSkuCode().equalsIgnoreCase(stockDTO.getSkuCode()))
                .filter(a -> a.getSkuQuality().equalsIgnoreCase(stockDTO.getSkuQuality()))
                .filter(a -> a.getLocationCode().equalsIgnoreCase(stockDTO.getLocationCode()))
                .filter(a -> a.getSkuLotNo().equalsIgnoreCase(stockDTO.getSkuLotNo()))
                .filter(a -> a.getAppointType().equalsIgnoreCase(appointType))
                .filter(a -> a.getAppointTypeAttr().equalsIgnoreCase(appointTypeAttr))
                .findFirst().orElse(null);
        if (replenishRecommendDTO == null) {
            ReplenishRecommendDTO recommendDTO = ConverterUtil.convert(stockDTO, ReplenishRecommendDTO.class);
            Integer maxOrder = replenishRecommendDTOList.stream()
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(stockDTO.getCargoCode()))
                    .filter(a -> a.getSkuCode().equalsIgnoreCase(stockDTO.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equalsIgnoreCase(stockDTO.getSkuQuality()))
                    .map(ReplenishRecommendDTO::getOrder)
                    .max(Integer::compareTo).orElse(0);
            recommendDTO.setOrder(maxOrder + 1);
            recommendDTO.setAppointTypeOrder(appointTypeOrder);
            recommendDTO.setUpcCode(upcCode);
            recommendDTO.setAvailableQty(originLocationQty);
            recommendDTO.setNeedQty(needQty);
            recommendDTO.setDefQty(BigDecimal.ZERO);
            recommendDTO.setIsAppoint(isAppoint);
            recommendDTO.setAppointType(appointType);
            recommendDTO.setAppointTypeAttr(appointTypeAttr);
            replenishRecommendDTOList.add(recommendDTO);
        } else {
            replenishRecommendDTO.setNeedQty(replenishRecommendDTO.getNeedQty().add(needQty));
        }
    }

}
