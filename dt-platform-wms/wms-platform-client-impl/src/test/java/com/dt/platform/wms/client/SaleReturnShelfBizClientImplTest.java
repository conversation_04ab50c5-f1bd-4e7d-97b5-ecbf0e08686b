package com.dt.platform.wms.client;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.shelf.ShelfTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.dto.ShelfDetailDTO;
import com.dt.platform.wms.biz.TestBase;
import com.dt.platform.wms.dto.shelf.SaleReturnBatchShelfTipDTO;
import com.dt.platform.wms.integration.IRemoteShelfClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.param.shelf.ShelfBizParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SaleReturnShelfBizClientImplTest extends TestBase<SaleReturnShelfBizClientImpl> {

    @Mock
    private IRemoteShelfClient remoteShelfClient;

    @Mock
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Test(expected = BaseException.class)
    public void day20240530() {
        ShelfBizParam param = new ShelfBizParam();
        param.setIdList(ListUtil.toList(1L));

        ShelfDetailDTO shelfDetailDTO = new ShelfDetailDTO();
        
        when(remoteShelfClient.getList(any())).thenReturn(ListUtil.toList(receiptShelf()));
        when(remoteShelfClient.getShelfDetailList(any())).thenReturn(ListUtil.toList(shelfDetailDTO));
        when(remoteSkuLotClient.skuLotMap(any())).thenReturn(emptyLotMap());
        

        Result<List<SaleReturnBatchShelfTipDTO>> listResult = target.saleReturnShelfTip(param);
        System.out.println(JSONUtil.toJsonPrettyStr(listResult));
    }
    
    private ShelfDTO receiptShelf() {
        ShelfDTO shelfDTO = new ShelfDTO();
        shelfDTO.setType(ShelfTypeEnum.SHELF_TYPE_RECEIPT.getType());
        return shelfDTO;
    }
    
    private HashMap<String, SkuLotDTO> emptyLotMap() {
        return new HashMap<>();
    }
}
