package com.dt.platform.wms.biz.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.sku.SkuNewOrOldCtrlEnum;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.sku.SkuImagesDTO;
import com.dt.domain.base.dto.sku.SkuImagesUrlDTO;
import com.dt.platform.wms.biz.TestBase;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.mq.rocket.listener.SkuMaintainConsumer;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import com.dt.platform.wms.integration.IRemoteWarehouseClient;
import com.dt.platform.wms.integration.sku.IRemoteSkuImagesClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatcher;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SkuMaintainConsumerTest extends TestBase<SkuMaintainConsumer> {

    @Mock
    private IRemoteSkuClient remoteSkuClient;
    @Mock
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;
    @Mock
    private IRemoteWarehouseClient remoteWarehouseClient;
    @Mock
    private IRemoteSkuImagesClient remoteSkuImagesClient;

    @Test
    public void sameSkuCodeOneLineReceiveMultiBatch() {

        SkuImagesDTO skuImagesDTO = new SkuImagesDTO();
        SkuImagesUrlDTO skuImagesUrlDTO = new SkuImagesUrlDTO();
        skuImagesUrlDTO.setSalesLengthPicUrl("url");
        skuImagesUrlDTO.setSalesWidthPicUrl("url");
        skuImagesUrlDTO.setSalesHeightPicUrl("url");
        skuImagesUrlDTO.setSalesWeightPicUrl("url");
        skuImagesUrlDTO.setCartonLengthPicUrl("url");
        skuImagesUrlDTO.setCartonWidthPicUrl("url");
        skuImagesUrlDTO.setCartonHeightPicUrl("url");
        skuImagesUrlDTO.setCartonWeightPicUrl("url");
        skuImagesUrlDTO.setCartonPcsPicUrl("url");
        skuImagesDTO.setExtraJson(JSONUtil.toJsonStr(skuImagesUrlDTO));
        when(remoteSkuImagesClient.get(any())).thenReturn(skuImagesDTO);
        
        when(defaultWarehouseCodeConfig.getWarehouseCodeList()).thenReturn(ListUtil.toList("WMS"));
        
        when(remoteWarehouseClient.getTaoTianWarehouse(anyString())).thenReturn(true);

        SkuDTO skuDTO = new SkuDTO();
        skuDTO.setWarehouseCode("WMS");
        skuDTO.setCargoCode("cargo");
        skuDTO.setCode("sku");
        skuDTO.setTag1(1L);
        skuDTO.setLength(BigDecimal.ONE);
        skuDTO.setWidth(BigDecimal.ONE);
        skuDTO.setHeight(BigDecimal.ONE);
        skuDTO.setStandardLength(BigDecimal.ONE);
        skuDTO.setStandardWidth(BigDecimal.ONE);
        skuDTO.setStandardHeight(BigDecimal.ONE);
        skuDTO.setGrossWeight(BigDecimal.ONE);
        skuDTO.setNetWeight(BigDecimal.ONE);
        skuDTO.setCartonWeight(BigDecimal.ONE);
        skuDTO.setCartonPcs(1);
        skuDTO.setBracketGauge(1);
        skuDTO.setSkuImagesUrlDTO(skuImagesUrlDTO);
        when(remoteSkuClient.get(any())).thenReturn(skuDTO);
        
        SkuMaintainConsumer.ErpGoodsManagementDTO after = new SkuMaintainConsumer.ErpGoodsManagementDTO();
        after.setWarehouseCode("WMS");
        after.setOwnerCode("cargo");
        after.setSku("sku");
        after.setTag1(1L);
        after.setLength(BigDecimal.ONE);
        after.setWidth(BigDecimal.ONE);
        after.setHeight(BigDecimal.ONE);
        after.setStandardLength(BigDecimal.ONE);
        after.setStandardWidth(BigDecimal.ONE);
        after.setStandardHeight(BigDecimal.ONE);
        after.setGrossWeight(BigDecimal.ONE);
        after.setNetWeight(BigDecimal.ONE);
        after.setFeature(JSONUtil.createObj().putOnce("cartonWeight",BigDecimal.ONE).toString());
        after.setCartonPcs(1);
        after.setBracketGauge(1);
        
        target.update(null, after);

        verify(remoteSkuClient).get(any());
        verify(remoteSkuClient).modify(argThat((ArgumentMatcher<SkuDTO>) t -> t.getIsNewRecord().equals(SkuNewOrOldCtrlEnum.SKU_OLD_CTRL_NO.getCode())));
    }

}
