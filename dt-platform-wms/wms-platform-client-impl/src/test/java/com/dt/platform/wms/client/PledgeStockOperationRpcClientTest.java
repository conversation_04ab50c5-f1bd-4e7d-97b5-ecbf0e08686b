package com.dt.platform.wms.client;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.platform.wms.biz.stock.biz.StockOperationContext;
import com.dt.platform.wms.biz.stock.biz.StockOperationHandler;
import com.dt.platform.wms.client.stock.PledgeStockOperationRpcClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.integration.IRemoteStockLocationClient;
import com.dt.platform.wms.rpc.dto.PledgeAvlStockRpcDTO;
import com.dt.platform.wms.rpc.param.PledgeSubmitParam;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatcher;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class PledgeStockOperationRpcClientTest extends TestBase<PledgeStockOperationRpcClient> {
    private static final String skuFirst = "sku1";
    private static final String skuSecond = "sku2";
    private static final String lotFirst = "lot1";
    private static final String lotSecond = "lot2";
    private static final String locationFirst = "location1";
    private static final String locationSecond = "location2";
    private static final String locationThird = "location3";
    private static final String locationFourth = "location4";
    private static final String expireDateFirst = "2025-12-04";
    private static final String expireDateSecond = "2025-12-05";

    @Mock
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Mock
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Mock
    private StockOperationHandler stockOperationHandler;

    @Test
    public void submitTest() {
        PledgeSubmitParam submitParam = new PledgeSubmitParam();
        submitParam.setWarehouseCode("warehouseCode");
        submitParam.setCargoCode("cargoCode");
        submitParam.setBillNo("billNo");
        submitParam.setUserName("userName");
        submitParam.setFinancialUserName("financialUserName");
        submitParam.setUserId(1L);
        submitParam.setFinancialUserId(2L);
        PledgeAvlStockRpcDTO pledgeAvlStockRpcDTO = new PledgeAvlStockRpcDTO();
        pledgeAvlStockRpcDTO.setSkuCode(skuFirst);
        pledgeAvlStockRpcDTO.setExpireDateDesc(expireDateFirst);
        pledgeAvlStockRpcDTO.setSubmitQty(BigDecimal.valueOf(29));
        submitParam.setPledgeAvlStockRpcDTOList(ListUtil.toList(pledgeAvlStockRpcDTO));

        Mockito.when(remoteSkuLotClient.getList(Mockito.any())).thenReturn(skuLotDTOList());
        Mockito.when(remoteStockLocationClient.getList(Mockito.any())).thenReturn(stockLocationDTOList());

        Result<Boolean> submit = target.submit(submitParam);
        Assert.assertTrue(submit.checkSuccess());

        Mockito.verify(stockOperationHandler).process(Mockito.argThat(new ArgumentMatcher<StockOperationContext>() {
            @Override
            public boolean matches(StockOperationContext stockOperationContext) {
                System.out.println(stockOperationContext.getStockLocationDTOList().size());
                for (StockLocationDTO stockLocationDTO : stockOperationContext.getStockLocationDTOList()) {
                    System.out.println(stockLocationDTO.getLocationCode() + " " +stockLocationDTO.getAvailableQty());
                }
                return true;
            }
        }));

    }

    private List<SkuLotDTO> skuLotDTOList() {
        ArrayList<SkuLotDTO> list = new ArrayList<>();
        SkuLotDTO skuLotDTO = getSkuLotDTO(lotFirst,expireDateFirst);
        list.add(skuLotDTO);
        return list;
    }

    private static SkuLotDTO getSkuLotDTO(String code,String expireDate) {
        SkuLotDTO skuLotDTO = new SkuLotDTO();
        skuLotDTO.setCode(code);
        skuLotDTO.setExpireDate(DateUtil.parseDate(expireDate).getTime());
        return skuLotDTO;
    }


    private List<StockLocationDTO> stockLocationDTOList () {
        List<StockLocationDTO> stockLocationDTOList = new ArrayList<>();
        stockLocationDTOList.add(getStockLocationDTO(skuFirst,lotFirst,ZoneTypeEnum.ZONE_TYPE_PICK,BigDecimal.valueOf(10000),locationFirst));
//        stockLocationDTOList.add(getStockLocationDTO(skuFirst,lotFirst,ZoneTypeEnum.ZONE_TYPE_PICK,BigDecimal.valueOf(30),locationThird));
//        stockLocationDTOList.add(getStockLocationDTO(skuFirst,lotFirst,ZoneTypeEnum.ZONE_TYPE_PICK,BigDecimal.valueOf(40),locationFourth));
        return stockLocationDTOList;
    }

    private static StockLocationDTO getStockLocationDTO(String skuCode,String skuLotNo,ZoneTypeEnum zoneTypeEnum,BigDecimal qty,String locationCode) {
        StockLocationDTO stockLocationDTO = new StockLocationDTO();
        stockLocationDTO.setSkuCode(skuCode);
        stockLocationDTO.setSkuLotNo(skuLotNo);
        stockLocationDTO.setZoneType(zoneTypeEnum.getType());
        stockLocationDTO.setAvailableQty(qty);
        stockLocationDTO.setLocationCode(locationCode);
        return stockLocationDTO;
    }
}
