package com.dt.platform.wms.client;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.stock.dto.stock.StockPledgeDTO;
import com.dt.platform.wms.biz.stock.biz.StockOperationContext;
import com.dt.platform.wms.biz.stock.biz.StockOperationHandler;
import com.dt.platform.wms.client.stock.RedeemStockOperationRpcClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.integration.stock.IRemoteStockPledgeClient;
import com.dt.platform.wms.rpc.dto.PledgeAvlStockRpcDTO;
import com.dt.platform.wms.rpc.param.PledgeSubmitParam;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatcher;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class PledgeRedeemStockOperationRpcClientTest extends TestBase<RedeemStockOperationRpcClient> {
    private static final String skuFirst = "sku1";
    private static final String skuSecond = "sku2";
    private static final String lotFirst = "lot1";
    private static final String lotSecond = "lot2";
    private static final String locationFirst = "location1";
    private static final String locationSecond = "location2";
    private static final String locationThird = "location3";
    private static final String locationFourth = "location4";
    private static final String locationFifth = "location5";
    private static final String locationSix = "location6";
    private static final String expireDateFirst = "2025-12-04";
    private static final String expireDateSecond = "2025-12-05";

    @Mock
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Mock
    private IRemoteStockPledgeClient remoteStockPledgeClient;

    @Mock
    private StockOperationHandler stockOperationHandler;

    @Test
    public void submitTest() {
        PledgeSubmitParam submitParam = new PledgeSubmitParam();
        submitParam.setWarehouseCode("warehouseCode");
        submitParam.setCargoCode("cargoCode");
        submitParam.setBillNo("billNo");
        submitParam.setUserName("userName");
        submitParam.setFinancialUserName("financialUserName");
        submitParam.setUserId(1L);
        submitParam.setFinancialUserId(2L);
        PledgeAvlStockRpcDTO pledgeAvlStockRpcDTO = new PledgeAvlStockRpcDTO();
        pledgeAvlStockRpcDTO.setSkuCode(skuFirst);
        pledgeAvlStockRpcDTO.setExpireDateDesc(expireDateFirst);
        pledgeAvlStockRpcDTO.setSubmitQty(BigDecimal.valueOf(60));
        submitParam.setPledgeAvlStockRpcDTOList(ListUtil.toList(pledgeAvlStockRpcDTO));

        Mockito.when(remoteSkuLotClient.getList(Mockito.any())).thenReturn(skuLotDTOList());
        Mockito.when(remoteStockPledgeClient.getList(Mockito.any())).thenReturn(stockPledgeDTOList());

        Result<Boolean> submit = target.submit(submitParam);
        Assert.assertTrue(submit.checkSuccess());

        Mockito.verify(stockOperationHandler).process(Mockito.argThat(stockOperationContext -> {
            System.out.println(stockOperationContext.getStockPledgeDTOList().size());
            for (StockPledgeDTO stockPledgeDTO : stockOperationContext.getStockPledgeDTOList()) {
                System.out.println(stockPledgeDTO.getLocationCode() + " " +stockPledgeDTO.getAvailableQty() + " " + stockPledgeDTO.getSkuLotNo());
            }
            return true;
        }));

    }

    private List<SkuLotDTO> skuLotDTOList() {
        ArrayList<SkuLotDTO> list = new ArrayList<>();
        list.add(getSkuLotDTO(lotFirst,expireDateFirst));
        list.add(getSkuLotDTO(lotSecond, expireDateFirst));
        return list;
    }

    private static SkuLotDTO getSkuLotDTO(String code,String expireDate) {
        SkuLotDTO skuLotDTO = new SkuLotDTO();
        skuLotDTO.setCode(code);
        skuLotDTO.setExpireDate(DateUtil.parseDate(expireDate).getTime());
        return skuLotDTO;
    }


    private List<StockPledgeDTO> stockPledgeDTOList() {
        List<StockPledgeDTO> stockPledgeDTOList = new ArrayList<>();
        stockPledgeDTOList.add(stockPledgeDTO(skuFirst,lotFirst,ZoneTypeEnum.ZONE_TYPE_STORE,BigDecimal.valueOf(20),locationFirst));
        stockPledgeDTOList.add(stockPledgeDTO(skuFirst,lotSecond,ZoneTypeEnum.ZONE_TYPE_STORE,BigDecimal.valueOf(30),locationSecond));
        stockPledgeDTOList.add(stockPledgeDTO(skuFirst,lotSecond,ZoneTypeEnum.ZONE_TYPE_STORE,BigDecimal.valueOf(40),locationThird));
        return stockPledgeDTOList;
    }

    private static StockPledgeDTO stockPledgeDTO(String skuCode, String skuLotNo, ZoneTypeEnum zoneTypeEnum, BigDecimal qty, String locationCode) {
        StockPledgeDTO stockPledgeDTO = new StockPledgeDTO();
        stockPledgeDTO.setSkuCode(skuCode);
        stockPledgeDTO.setSkuLotNo(skuLotNo);
        stockPledgeDTO.setZoneType(zoneTypeEnum.getType());
        stockPledgeDTO.setAvailableQty(qty);
        stockPledgeDTO.setLocationCode(locationCode);
        stockPledgeDTO.setLocationType(zoneTypeEnum.getType());
        return stockPledgeDTO;
    }
}
