package com.dt.platform.wms.client;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.rs.RSOrderStatusEnum;
import com.dt.domain.bill.client.rs.bo.SalesReturnOrderBO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.platform.wms.client.rs.SaleReturnOrderRpcClientImpl;
import com.dt.platform.wms.foroms.result.Result;
import com.dt.platform.wms.foroms.rs.SalesReturnOrderCancelRpcParam;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnWarehouseClient;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OMSSalesReturnCancelTest extends TestBase<SaleReturnOrderRpcClientImpl> {

    @Mock
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Mock
    private IRemoteSalesReturnWarehouseClient remoteSalesReturnWarehouseClient;

    @Mock
    private IRemoteBillLogClient remoteBillLogClient;

    @Test
    public void receiveWriteBackTest() {
        when(remoteSalesReturnWarehouseClient.salesReturnWarehouse(anyString(),"")).thenReturn("AAA");
        
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CREATED.getCode());
        when(remoteSalesReturnOrderClient.getList(any())).thenReturn(ListUtil.toList(salesReturnOrderDTO));
        
        SalesReturnOrderCancelRpcParam cancelRpcParam = new SalesReturnOrderCancelRpcParam();
        cancelRpcParam.setWarehouseCode("AAA");
        cancelRpcParam.setExpressNo("sss");
        cancelRpcParam.setAfterSalesTrackingNo("sss");
        target.cancelSalesReturnOrder(cancelRpcParam);

        verify(remoteSalesReturnOrderClient).getList(any());
        verify(remoteSalesReturnOrderClient).buildExtraJson(any(),anyLong());
        verify(remoteSalesReturnOrderClient).save((SalesReturnOrderBO) argThat(it -> {
            System.out.println(JSONUtil.toJsonPrettyStr(it));
            return true;
        }));
    }

}
