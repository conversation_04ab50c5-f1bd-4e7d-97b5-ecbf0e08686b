package com.dt.platform.wms.client;

import org.junit.Before;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

public class TestBase<T> {

    protected T target;

    @SuppressWarnings("all")
    public TestBase() {
        try {
            Type genericSuperclass = getClass().getGenericSuperclass();
            if (genericSuperclass instanceof ParameterizedType) {
                // 转换为ParameterizedType
                ParameterizedType paramType = (ParameterizedType) genericSuperclass;
                // 获取实际的类型参数
                Type[] actualTypeArguments = paramType.getActualTypeArguments();
                // 由于我们只有一个类型参数，所以我们取第一个
                if (actualTypeArguments.length > 0) {
                    Class<T> actualTypeArgument = (Class<T>) actualTypeArguments[0];
                    target = actualTypeArgument.newInstance();
                }
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    @Before
    public void init() {
        for (Field field : this.getClass().getDeclaredFields()) {
            try {
                field.setAccessible(true);
                Object object = field.get(this);
                Field declaredField = target.getClass().getDeclaredField(field.getName());

                declaredField.setAccessible(true);
                declaredField.set(target, object);
            } catch (Exception ignored) {
            }
        }
    }
}
