package com.dt.platform.wms.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.rs.RSBillSourceEnum;
import com.dt.component.common.enums.rs.RSOverdueEnum;
import com.dt.component.common.enums.rs.RSSecondEntryEnum;
import com.dt.component.common.enums.shelf.ShelfStatusEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.client.rs.ISalesReturnOrderClient;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDetailDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderReceiveDTO;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.biz.TestBase;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.taotian.CargoOwnerTaoTianDTO;
import com.dt.platform.wms.biz.taotian.TTReturnOrderConfirmRequest;
import com.dt.platform.wms.integration.IRemoteCargoOwnerClient;
import com.dt.platform.wms.integration.IRemoteShelfClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.integration.mercury.IRemoteMercuryClient;
import com.dt.platform.wms.integration.message.IRemoteMessageMqClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderDetailClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderReceiveClient;
import com.dt.platform.wms.integration.rs.impl.RemoteSalesReturnOrderClientImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShelfCallBackTest extends TestBase<RemoteSalesReturnOrderClientImpl> {

    @Mock
    private ISalesReturnOrderClient salesReturnOrderClient;

    @Mock
    private IRemoteMercuryClient remoteMercuryClient;

    @Mock
    private IRemoteSalesReturnOrderReceiveClient remoteSalesReturnOrderReceiveClient;

    @Mock
    private IRemoteMessageMqClient remoteMessageMqClient;

    @Mock
    private IRemoteSalesReturnOrderDetailClient remoteSalesReturnOrderDetailClient;

    @Mock
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Mock
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Mock
    private IRemoteShelfClient remoteShelfClient;

    @Mock
    private WmsOtherConfig wmsOtherConfig;

    @Test
    public void sendMessageTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        when(wmsOtherConfig.getTaoTianCallbackWarningMessageList()).thenReturn(ListUtil.toList("dddd","ccc"));
        Method sendMessage = RemoteSalesReturnOrderClientImpl.class.getDeclaredMethod("sendMessage", String.class);
        sendMessage.setAccessible(true);
        sendMessage.invoke(target, "aa");
        verify(wmsOtherConfig).getTaoTianCallbackSystemWarningUrlList();
        sendMessage.invoke(target, "dddd");
        verify(wmsOtherConfig).getTaoTianCallbackWarningUrlList();
    }

    @Test
    public void sameSkuCodeMultiLineSeqReceiveAll() {
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        CargoOwnerTaoTianDTO cargoOwnerTaoTianDTO = new CargoOwnerTaoTianDTO();
        salesReturnOrderDTO.setOverdue(RSOverdueEnum.NOT.getCode());

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOA = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOA.setSkuCode("ttitemCode-872978");
        salesReturnOrderDetailDTOA.setLineSeq("202404011406432");
        salesReturnOrderDetailDTOA.setExpectQty(BigDecimal.valueOf(4));

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOB = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOB.setSkuCode("hhttitemCode-054367");
        salesReturnOrderDetailDTOB.setExpectQty(BigDecimal.valueOf(2));
        salesReturnOrderDetailDTOB.setLineSeq("202404011406431");

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOC = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOC.setSkuCode("hhttitemCode-054367");
        salesReturnOrderDetailDTOC.setExpectQty(BigDecimal.valueOf(3));
        salesReturnOrderDetailDTOC.setLineSeq("202404011406431");

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOD = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOD.setSkuCode("hhttitemCode-054367");
        salesReturnOrderDetailDTOD.setExpectQty(BigDecimal.valueOf(1));
        salesReturnOrderDetailDTOD.setLineSeq("202404011406431");


        SalesReturnOrderReceiveDTO receiveDTOD = new SalesReturnOrderReceiveDTO();
        receiveDTOD.setSkuCode("ttitemCode-872978");
        receiveDTOD.setSkuLotNo("LOT240318000004");
        receiveDTOD.setQty(BigDecimal.valueOf(1));
        receiveDTOD.setSecondEntry(RSSecondEntryEnum.YES.getCode());


        SalesReturnOrderReceiveDTO receiveDTOC = new SalesReturnOrderReceiveDTO();
        receiveDTOC.setSkuCode("ttitemCode-872978");
        receiveDTOC.setSkuLotNo("LOT240318000004");
        receiveDTOC.setQty(BigDecimal.valueOf(1));
        receiveDTOC.setSecondEntry(RSSecondEntryEnum.YES.getCode());


        SalesReturnOrderReceiveDTO receiveDTOB = new SalesReturnOrderReceiveDTO();
        receiveDTOB.setSkuCode("ttitemCode-872978");
        receiveDTOB.setSkuLotNo("LOT240318000004");
        receiveDTOB.setQty(BigDecimal.valueOf(1));
        receiveDTOB.setSecondEntry(RSSecondEntryEnum.YES.getCode());


        SalesReturnOrderReceiveDTO receiveDTOA = new SalesReturnOrderReceiveDTO();
        receiveDTOA.setSkuCode("ttitemCode-872978");
        receiveDTOA.setSkuLotNo("LOT240318000004");
        receiveDTOA.setQty(BigDecimal.valueOf(1));
        receiveDTOA.setSecondEntry(RSSecondEntryEnum.YES.getCode());

        when(salesReturnOrderClient.get(any())).thenReturn(Result.success(salesReturnOrderDTO));
        ShelfDTO shelfDTO = new ShelfDTO();
        shelfDTO.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
        when(remoteShelfClient.getList(any())).thenReturn(ListUtil.toList(shelfDTO));
        when(remoteSalesReturnOrderReceiveClient.getList(any())).thenReturn(ListUtil.toList(receiveDTOA, receiveDTOB, receiveDTOC, receiveDTOD));
        when(remoteSalesReturnOrderDetailClient.getList(any())).thenReturn(ListUtil.toList(salesReturnOrderDetailDTOA, salesReturnOrderDetailDTOB, salesReturnOrderDetailDTOC, salesReturnOrderDetailDTOD));
        when(remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(any(), any())).thenReturn(cargoOwnerTaoTianDTO);

        target.shelfCallbackV2(new MessageMqDTO());

        verify(remoteSkuLotClient).getList(any());
        verify(remoteMercuryClient).shelfCallback(argThat(it -> {
            System.out.println(JSONUtil.toJsonPrettyStr(it));
            TTReturnOrderConfirmRequest request = JSONUtil.toBean(it, TTReturnOrderConfirmRequest.class);
            return Stream.of(request.getOrderLines().size() == 1,
                    request.getOrderLines().stream().allMatch(orderLine -> orderLine.getActualQty().equalsIgnoreCase(orderLine.getPlanQty().toString()))).allMatch(itt -> itt);
        }), any());
        verify(remoteMessageMqClient).modify(any());
    }

    /**
     * 同一商品编码多个行号
     * 收货数量少于计划数量
     */
    @Test
    public void sameSkuCodeMultiLineSeqReceivePart() {
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        CargoOwnerTaoTianDTO cargoOwnerTaoTianDTO = new CargoOwnerTaoTianDTO();
        salesReturnOrderDTO.setOverdue(RSOverdueEnum.NOT.getCode());

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOA = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOA.setSkuCode("skuCode");
        salesReturnOrderDetailDTOA.setLineSeq("1");
        salesReturnOrderDetailDTOA.setExpectQty(BigDecimal.valueOf(1));

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOD = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOD.setSkuCode("skuCode");
        salesReturnOrderDetailDTOD.setExpectQty(BigDecimal.valueOf(1));
        salesReturnOrderDetailDTOD.setLineSeq("2");


        SalesReturnOrderReceiveDTO receiveDTOA = new SalesReturnOrderReceiveDTO();
        receiveDTOA.setSkuCode("skuCode");
        receiveDTOA.setSkuLotNo("LOT240318000004");
        receiveDTOA.setQty(BigDecimal.valueOf(1));
        receiveDTOA.setSecondEntry(RSSecondEntryEnum.YES.getCode());

        when(salesReturnOrderClient.get(any())).thenReturn(Result.success(salesReturnOrderDTO));
        when(remoteSalesReturnOrderReceiveClient.getList(any())).thenReturn(ListUtil.toList(receiveDTOA));
        when(remoteSalesReturnOrderDetailClient.getList(any())).thenReturn(ListUtil.toList(salesReturnOrderDetailDTOA, salesReturnOrderDetailDTOD));
        when(remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(any(), any())).thenReturn(cargoOwnerTaoTianDTO);

        target.shelfCallbackV2(new MessageMqDTO());

        verify(remoteSkuLotClient).getList(any());
        verify(remoteMercuryClient).shelfCallback(argThat(it -> {
            TTReturnOrderConfirmRequest request = JSONUtil.toBean(it, TTReturnOrderConfirmRequest.class);
            return Stream.of(request.getOrderLines().size() == 1,
                    request.getOrderLines().stream().anyMatch(orderLine -> orderLine.getActualQty().equalsIgnoreCase(orderLine.getPlanQty().toString()))
            ).allMatch(itt -> itt);
        }), any());
        verify(remoteMessageMqClient).modify(any());
    }

    /**
     * 同一商品编码多个行号
     * 收货数量少于计划数量
     */
    @Test
    public void sameSkuCodeMultiLineSeqReceiveMore() {
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        CargoOwnerTaoTianDTO cargoOwnerTaoTianDTO = new CargoOwnerTaoTianDTO();
        salesReturnOrderDTO.setOverdue(RSOverdueEnum.NOT.getCode());

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOA = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOA.setSkuCode("skuCode");
        salesReturnOrderDetailDTOA.setLineSeq("1");
        salesReturnOrderDetailDTOA.setExpectQty(BigDecimal.valueOf(1));

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOD = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOD.setSkuCode("skuCode");
        salesReturnOrderDetailDTOD.setExpectQty(BigDecimal.valueOf(1));
        salesReturnOrderDetailDTOD.setLineSeq("2");


        SalesReturnOrderReceiveDTO receiveDTOA = new SalesReturnOrderReceiveDTO();
        receiveDTOA.setSkuCode("skuCode");
        receiveDTOA.setSkuLotNo("LOT240318000004");
        receiveDTOA.setQty(BigDecimal.valueOf(3));
        receiveDTOA.setSecondEntry(RSSecondEntryEnum.YES.getCode());

        when(salesReturnOrderClient.get(any())).thenReturn(Result.success(salesReturnOrderDTO));
        when(remoteSalesReturnOrderReceiveClient.getList(any())).thenReturn(ListUtil.toList(receiveDTOA));
        when(remoteSalesReturnOrderDetailClient.getList(any())).thenReturn(ListUtil.toList(salesReturnOrderDetailDTOA, salesReturnOrderDetailDTOD));
        when(remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(any(), any())).thenReturn(cargoOwnerTaoTianDTO);

        target.shelfCallbackV2(new MessageMqDTO());

        verify(remoteSkuLotClient).getList(any());
        verify(remoteMercuryClient).shelfCallback(argThat(it -> {
            TTReturnOrderConfirmRequest request = JSONUtil.toBean(it, TTReturnOrderConfirmRequest.class);
            return Stream.of(request.getOrderLines().size() == 2,
                    request.getOrderLines().stream().allMatch(orderLine -> orderLine.getActualQty().equalsIgnoreCase(orderLine.getPlanQty().toString()))
            ).allMatch(itt -> itt);
        }), any());
        verify(remoteMessageMqClient).modify(any());
    }


    @Test
    public void sameSkuCodeOneLineSeqReceiveAll() {
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        CargoOwnerTaoTianDTO cargoOwnerTaoTianDTO = new CargoOwnerTaoTianDTO();
        salesReturnOrderDTO.setOverdue(RSOverdueEnum.NOT.getCode());

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOA = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOA.setSkuCode("skuCode");
        salesReturnOrderDetailDTOA.setLineSeq("1");
        salesReturnOrderDetailDTOA.setExpectQty(BigDecimal.valueOf(1));

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOD = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOD.setSkuCode("skuCode");
        salesReturnOrderDetailDTOD.setExpectQty(BigDecimal.valueOf(1));
        salesReturnOrderDetailDTOD.setLineSeq("1");


        SalesReturnOrderReceiveDTO receiveDTOA = new SalesReturnOrderReceiveDTO();
        receiveDTOA.setSkuCode("skuCode");
        receiveDTOA.setSkuLotNo("LOT240318000004");
        receiveDTOA.setQty(BigDecimal.valueOf(2));
        receiveDTOA.setSecondEntry(RSSecondEntryEnum.YES.getCode());

        when(salesReturnOrderClient.get(any())).thenReturn(Result.success(salesReturnOrderDTO));
        when(remoteSalesReturnOrderReceiveClient.getList(any())).thenReturn(ListUtil.toList(receiveDTOA));
        when(remoteSalesReturnOrderDetailClient.getList(any())).thenReturn(ListUtil.toList(salesReturnOrderDetailDTOA, salesReturnOrderDetailDTOD));
        when(remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(any(), any())).thenReturn(cargoOwnerTaoTianDTO);

        target.shelfCallbackV2(new MessageMqDTO());

        verify(remoteSkuLotClient).getList(any());
        verify(remoteMercuryClient).shelfCallback(argThat(it -> {
            TTReturnOrderConfirmRequest request = JSONUtil.toBean(it, TTReturnOrderConfirmRequest.class);
            if (CollectionUtil.isEmpty(request.getOrderLines())) return false;
            if (request.getOrderLines().size() != 1) return false;
            Map<String, TTReturnOrderConfirmRequest.OrderLine> orderLineMap = request.getOrderLines().stream()
                    .collect(Collectors.toMap(TTReturnOrderConfirmRequest.OrderLine::getOrderLineNo, Function.identity()));
            TTReturnOrderConfirmRequest.OrderLine orderLine = orderLineMap.get("1");
            if (null != orderLine) {
                if (!orderLine.getPlanQty().equals(2L)) return false;
                return orderLine.getActualQty().equalsIgnoreCase("2");
            }
            return true;
        }), any());
        verify(remoteMessageMqClient).modify(any());
    }


    @Test
    public void sameSkuCodeOneLineReceiveMultiSomeBatch() {
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        CargoOwnerTaoTianDTO cargoOwnerTaoTianDTO = new CargoOwnerTaoTianDTO();
        salesReturnOrderDTO.setOverdue(RSOverdueEnum.OVERDUE.getCode());

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOA = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOA.setSkuCode("skuCode");
        salesReturnOrderDetailDTOA.setLineSeq("1");
        salesReturnOrderDetailDTOA.setExpectQty(BigDecimal.valueOf(2));

        SalesReturnOrderReceiveDTO secondReceive = new SalesReturnOrderReceiveDTO();
        secondReceive.setSkuCode("skuCode");
        secondReceive.setSkuLotNo("LOT240318000004");
        secondReceive.setQty(BigDecimal.valueOf(1));
        secondReceive.setSn("sn1");
        secondReceive.setSecondEntry(RSSecondEntryEnum.NO.getCode());

        SalesReturnOrderReceiveDTO firstReceive = new SalesReturnOrderReceiveDTO();
        firstReceive.setSkuCode("skuCode");
        firstReceive.setSn("");
        firstReceive.setSkuLotNo("LOT240318000004");
        firstReceive.setQty(BigDecimal.valueOf(1));
        firstReceive.setSecondEntry(RSSecondEntryEnum.NO.getCode());

        when(salesReturnOrderClient.get(any())).thenReturn(Result.success(salesReturnOrderDTO));
        when(remoteSalesReturnOrderReceiveClient.getList(any())).thenReturn(ListUtil.toList(firstReceive, secondReceive));
        when(remoteSalesReturnOrderDetailClient.getList(any())).thenReturn(ListUtil.toList(salesReturnOrderDetailDTOA));
        when(remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(any(), any())).thenReturn(cargoOwnerTaoTianDTO);

        target.shelfCallbackV2(new MessageMqDTO());

        verify(remoteSkuLotClient).getList(any());
        verify(remoteMercuryClient).shelfCallback(argThat(it -> {
            System.out.println(JSONUtil.toJsonPrettyStr(it));
            TTReturnOrderConfirmRequest request = JSONUtil.toBean(it, TTReturnOrderConfirmRequest.class);
            if (CollectionUtil.isEmpty(request.getOrderLines())) return false;
            if (request.getOrderLines().size() != 1) return false;
            Map<String, TTReturnOrderConfirmRequest.OrderLine> orderLineMap = request.getOrderLines().stream()
                    .collect(Collectors.toMap(TTReturnOrderConfirmRequest.OrderLine::getOrderLineNo, Function.identity()));
            TTReturnOrderConfirmRequest.OrderLine orderLine = orderLineMap.get("1");
            if (null != orderLine) {
                if (!orderLine.getPlanQty().equals(2L)) return false;
                if (CollectionUtil.isEmpty(orderLine.getBatchs()))
                    throw ExceptionUtil.exceptionWithMessage("多天相同批次收货信息应该合并");
                if (orderLine.getBatchs().size() != 1)
                    throw ExceptionUtil.exceptionWithMessage("多天相同批次收货信息应该合并");
                if (CollectionUtil.isEmpty(orderLine.getSnList().getSn()))
                    throw ExceptionUtil.exceptionWithMessage("此处SNList应该有值");
                if (orderLine.getSnList().getSn().size() != 1)
                    throw ExceptionUtil.exceptionWithMessage("此处SN列表数量应该为1");
                return orderLine.getActualQty().equalsIgnoreCase("2");
            }
            return true;
        }), any());
        verify(remoteMessageMqClient).modify(any());
    }


    @Test
    public void sameSkuCodeOneLineReceiveMultiBatch() {
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        CargoOwnerTaoTianDTO cargoOwnerTaoTianDTO = new CargoOwnerTaoTianDTO();
        salesReturnOrderDTO.setOverdue(RSOverdueEnum.OVERDUE.getCode());

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOA = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOA.setSkuCode("skuCode");
        salesReturnOrderDetailDTOA.setLineSeq("1");
        salesReturnOrderDetailDTOA.setExpectQty(BigDecimal.valueOf(1));

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOD = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOD.setSkuCode("skuCode");
        salesReturnOrderDetailDTOD.setExpectQty(BigDecimal.valueOf(1));
        salesReturnOrderDetailDTOD.setLineSeq("1");


        SalesReturnOrderReceiveDTO secondReceive = new SalesReturnOrderReceiveDTO();
        secondReceive.setSkuCode("skuCode");
        secondReceive.setSkuLotNo("LOT240318000004");
        secondReceive.setQty(BigDecimal.valueOf(1));
        secondReceive.setSn("sn1");
        secondReceive.setSecondEntry(RSSecondEntryEnum.NO.getCode());

        SalesReturnOrderReceiveDTO firstReceive = new SalesReturnOrderReceiveDTO();
        firstReceive.setSkuCode("skuCode");
        firstReceive.setSn("sn1");
        firstReceive.setSkuLotNo("LOT240318000005");
        firstReceive.setQty(BigDecimal.valueOf(1));
        firstReceive.setSecondEntry(RSSecondEntryEnum.NO.getCode());

        when(salesReturnOrderClient.get(any())).thenReturn(Result.success(salesReturnOrderDTO));
        when(remoteSalesReturnOrderReceiveClient.getList(any())).thenReturn(ListUtil.toList(firstReceive, secondReceive));
        when(remoteSalesReturnOrderDetailClient.getList(any())).thenReturn(ListUtil.toList(salesReturnOrderDetailDTOA, salesReturnOrderDetailDTOD));
        when(remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(any(), any())).thenReturn(cargoOwnerTaoTianDTO);

        target.shelfCallbackV2(new MessageMqDTO());

        verify(remoteSkuLotClient).getList(any());
        verify(remoteMercuryClient).shelfCallback(argThat(it -> {
            TTReturnOrderConfirmRequest request = JSONUtil.toBean(it, TTReturnOrderConfirmRequest.class);
            if (CollectionUtil.isEmpty(request.getOrderLines())) return false;
            if (request.getOrderLines().size() != 1) return false;
            Map<String, TTReturnOrderConfirmRequest.OrderLine> orderLineMap = request.getOrderLines().stream()
                    .collect(Collectors.toMap(TTReturnOrderConfirmRequest.OrderLine::getOrderLineNo, Function.identity()));
            TTReturnOrderConfirmRequest.OrderLine orderLine = orderLineMap.get("1");
            if (null != orderLine) {
                if (!orderLine.getPlanQty().equals(2L)) return false;
                if (CollectionUtil.isEmpty(orderLine.getBatchs())) throw ExceptionUtil.DATA_ERROR;
                if (orderLine.getBatchs().size() != 2) throw ExceptionUtil.DATA_ERROR;
                if (CollectionUtil.isEmpty(orderLine.getSnList().getSn())) throw ExceptionUtil.DATA_ERROR;
                if (orderLine.getSnList().getSn().size() != 1) throw ExceptionUtil.DATA_ERROR;
                return orderLine.getActualQty().equalsIgnoreCase("2");
            }
            return true;
        }), any());
        verify(remoteMessageMqClient).modify(any());
    }

    @Test
    public void day20240530() {
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        CargoOwnerTaoTianDTO cargoOwnerTaoTianDTO = new CargoOwnerTaoTianDTO();
        salesReturnOrderDTO.setOverdue(RSOverdueEnum.NOT.getCode());
        salesReturnOrderDTO.setBillSource(RSBillSourceEnum.MALL_DIRECT.getCode());

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOA = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOA.setSkuCode("758222049087");
        salesReturnOrderDetailDTOA.setLineSeq("1");
        salesReturnOrderDetailDTOA.setExpectQty(BigDecimal.valueOf(3));

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOB = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOB.setSkuCode("758508779798");
        salesReturnOrderDetailDTOB.setExpectQty(BigDecimal.valueOf(2));
        salesReturnOrderDetailDTOB.setLineSeq("2");

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTOD = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTOD.setSkuCode("758222049059");
        salesReturnOrderDetailDTOD.setExpectQty(BigDecimal.valueOf(2));
        salesReturnOrderDetailDTOD.setLineSeq("3");


        SalesReturnOrderReceiveDTO fifthReceive = new SalesReturnOrderReceiveDTO();
        fifthReceive.setSkuCode("758222049059");
        fifthReceive.setSkuLotNo("LOT240530000007");
        fifthReceive.setQty(BigDecimal.valueOf(1));
        fifthReceive.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
        fifthReceive.setInventoryType(InventoryTypeEnum.ZP.getCode());
        fifthReceive.setSecondEntry(RSSecondEntryEnum.NO.getCode());

        SalesReturnOrderReceiveDTO fourReceive = new SalesReturnOrderReceiveDTO();
        fourReceive.setSkuCode("758508779798");
        fourReceive.setSkuLotNo("LOT240530000009");
        fourReceive.setQty(BigDecimal.valueOf(2));
        fourReceive.setSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
        fourReceive.setInventoryType(InventoryTypeEnum.CC.getCode());
        fourReceive.setSecondEntry(RSSecondEntryEnum.NO.getCode());

        SalesReturnOrderReceiveDTO thirdReceive = new SalesReturnOrderReceiveDTO();
        thirdReceive.setSkuCode("758222049087");
        thirdReceive.setSkuLotNo("LOT240530000008");
        thirdReceive.setQty(BigDecimal.valueOf(2));
        thirdReceive.setSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
        thirdReceive.setInventoryType(InventoryTypeEnum.CC.getCode());
        thirdReceive.setSecondEntryFailReason("sdsd");
        thirdReceive.setSecondEntry(RSSecondEntryEnum.NO.getCode());

        SalesReturnOrderReceiveDTO secondReceive = new SalesReturnOrderReceiveDTO();
        secondReceive.setSkuCode("758222049087");
        secondReceive.setSkuLotNo("LOT240530000008");
        secondReceive.setQty(BigDecimal.valueOf(1));
        secondReceive.setSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
        secondReceive.setInventoryType(InventoryTypeEnum.CC.getCode());
        secondReceive.setSecondEntry(RSSecondEntryEnum.YES.getCode());

        SalesReturnOrderReceiveDTO firstReceive = new SalesReturnOrderReceiveDTO();
        firstReceive.setSkuCode("758222049059");
        firstReceive.setSkuLotNo("LOT240530000007");
        firstReceive.setQty(BigDecimal.valueOf(1));
        firstReceive.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
        firstReceive.setInventoryType(InventoryTypeEnum.ZP.getCode());
        firstReceive.setSecondEntry(RSSecondEntryEnum.NO.getCode());

        ShelfDTO shelfDTO = new ShelfDTO();
        shelfDTO.setStatus(ShelfStatusEnum.STATUS_COMPLETED.getStatus());
        when(remoteShelfClient.getList(any())).thenReturn(ListUtil.toList(shelfDTO));
        when(salesReturnOrderClient.get(any())).thenReturn(Result.success(salesReturnOrderDTO));
        when(remoteSalesReturnOrderReceiveClient.getList(any())).thenReturn(ListUtil.toList(firstReceive, secondReceive, thirdReceive, fourReceive,fifthReceive));
        when(remoteSalesReturnOrderDetailClient.getList(any())).thenReturn(ListUtil.toList(salesReturnOrderDetailDTOA, salesReturnOrderDetailDTOD, salesReturnOrderDetailDTOB));
        when(remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(any(), any())).thenReturn(cargoOwnerTaoTianDTO);

        target.shelfCallbackV2(new MessageMqDTO());

    }
}
