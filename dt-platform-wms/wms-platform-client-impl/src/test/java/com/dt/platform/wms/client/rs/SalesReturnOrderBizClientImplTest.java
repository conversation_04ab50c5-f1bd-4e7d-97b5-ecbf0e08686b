package com.dt.platform.wms.client.rs;

import com.dt.component.common.result.Result;
import com.dt.platform.wms.client.TestBase;
import com.dt.platform.wms.param.rs.SalesReturnOrderBizParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class SalesReturnOrderBizClientImplTest extends TestBase<SalesReturnOrderBizClientImpl> {

    @Test
    public void modifyExpress() {
        SalesReturnOrderBizParam param = new SalesReturnOrderBizParam();
        param.setId(0L);
        Result<Boolean> booleanResult = target.modifyExpress(param);
        log.info("{}",booleanResult);
    }

}