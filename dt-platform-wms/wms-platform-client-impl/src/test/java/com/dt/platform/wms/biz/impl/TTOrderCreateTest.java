package com.dt.platform.wms.biz.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.danding.business.oms.common.BO.ChannelOrderV2DTO;
import com.dt.component.common.enums.rs.RSOrderStatusEnum;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.bill.client.rs.bo.SalesReturnOrderBO;
import com.dt.domain.bill.dto.AllocationOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDetailDTO;
import com.dt.platform.wms.biz.TestBase;
import com.dt.platform.wms.biz.rs.TTReturnOrderBizImpl;
import com.dt.platform.wms.biz.taotian.TTReturnOrderCreateRequest;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.oms.IRemoteOmsOrderClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import com.dt.platform.wms.integration.warehouseManage.IRemoteLogicWarehouseClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatcher;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TTOrderCreateTest extends TestBase<TTReturnOrderBizImpl> {
    @Mock
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Mock
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Mock
    private IRemoteOmsOrderClient remoteOmsOrderClient;

    @Mock
    private IRemoteLogicWarehouseClient remoteLogicWarehouseClient;

    @Mock
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Mock
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Mock
    private IRemoteSkuClient remoteSkuClient;

    @Mock
    private IRemoteBillLogClient remoteBillLogClient;
    
    private final IRemoteLockSupportClient remoteLockSupportClient = Mockito.spy(new MockRemoteLockSupport());
    
    
    @Test
    public void returnOrderCreate() {
        String sku = "sku";
        WarehouseDTO returnWarehouse = new WarehouseDTO();
        ChannelOrderV2DTO channelOrderV2DTO = new ChannelOrderV2DTO();
        channelOrderV2DTO.setDeclareSuccessTime(DateTime.now());
        WarehouseDTO shipmentWarehouse = new WarehouseDTO();
        shipmentWarehouse.setCode("shipmentWarehouse");
        shipmentWarehouse.setName("shipmentWarehouse");
        CargoOwnerDTO cargoOwnerDTO = new CargoOwnerDTO();
        cargoOwnerDTO.setName("cargoOwnerDTO");
        ShipmentOrderDTO shipmentOrderDTO = new ShipmentOrderDTO();
        shipmentOrderDTO.setCargoCode("shipmentCargo");

        AllocationOrderDTO allocationOrderDTOSecond = new AllocationOrderDTO();
        allocationOrderDTOSecond.setSkuCode(sku);
        
        AllocationOrderDTO allocationOrderDTO = new AllocationOrderDTO();
        allocationOrderDTO.setSkuCode(sku);
        
        SkuDTO skuDTO = new SkuDTO();
        skuDTO.setCode(sku);
        SkuUpcDTO skuUpcDTO = new SkuUpcDTO();
        skuUpcDTO.setSkuCode(sku);

        when(remoteWarehouseClient.queryByCode(any())).thenReturn(returnWarehouse);
        when(remoteSalesReturnOrderClient.getList(any())).thenReturn(ListUtil.empty());
        when(remoteOmsOrderClient.channelOrder(any())).thenReturn(Optional.of(channelOrderV2DTO));
        when(remoteLogicWarehouseClient.entityWarehouseCode(any())).thenReturn(shipmentWarehouse);
        when(remoteCargoOwnerClient.queryByCode(any())).thenReturn(cargoOwnerDTO);
        when(remoteShipmentOrderClient.get(any())).thenReturn(shipmentOrderDTO);
        when(remoteSkuClient.getList(any())).thenReturn(ListUtil.toList(skuDTO));
        when(remoteSkuClient.getSkuUpcList(any())).thenReturn(ListUtil.toList(skuUpcDTO));
        when(remoteLogicWarehouseClient.getTaoTianSalePlatformNameByOwner(any())).thenReturn("PTTMKLDJK");

        when(remoteSalesReturnOrderClient.save(any(SalesReturnOrderBO.class))).thenAnswer(invocationOnMock -> {
            SalesReturnOrderBO argument = invocationOnMock.getArgument(0, SalesReturnOrderBO.class);
            for (SalesReturnOrderDTO salesReturnOrderDTO : argument.getSalesReturnOrderDTOList()) {
                System.out.println(JSONUtil.toJsonStr(salesReturnOrderDTO.salesReturnOrderExtraDTO().getShipmentCargoCode()));
                System.out.println(JSONUtil.toJsonStr(salesReturnOrderDTO.salesReturnOrderExtraDTO().getOrderType()));
                System.out.println(JSONUtil.toJsonStr(salesReturnOrderDTO.salesReturnOrderExtraDTO().getSenderInfo()));
                System.out.println(JSONUtil.toJsonStr(salesReturnOrderDTO.salesReturnOrderExtraDTO().getExtendProps()));
                System.out.println(JSONUtil.toJsonStr(salesReturnOrderDTO.salesReturnOrderExtraDTO().getPreDeliveryOrderCode()));
                System.out.println(JSONUtil.toJsonStr(salesReturnOrderDTO.salesReturnOrderExtraDTO().getHighRiskCustom()));
                System.out.println(JSONUtil.toJsonStr(salesReturnOrderDTO.salesReturnOrderExtraDTO().getHighRiskPackage()));
                System.out.println(JSONUtil.toJsonStr(salesReturnOrderDTO.salesReturnOrderExtraDTO().getFulfilOutBizCode()));
                System.out.println(salesReturnOrderDTO.getReturnReason());
            }
            for (SalesReturnOrderDetailDTO salesReturnOrderDetailDTO : argument.getSalesReturnOrderDetailDTOList()) {
                System.out.println(JSONUtil.toJsonStr(salesReturnOrderDetailDTO));
            }
            return true;
        });

        TTReturnOrderCreateRequest ttReturnOrderCreateRequest = new TTReturnOrderCreateRequest();

        TTReturnOrderCreateRequest.ReturnOrder returnOrder = new TTReturnOrderCreateRequest.ReturnOrder();
        returnOrder.setBuyerNick("买家高圆圆");
        returnOrder.setPreDeliveryOrderCode("MFu118301308061726851526");
        returnOrder.setReturnOrderCode("returnOrderCode");
        returnOrder.setWarehouseCode("wareshouseCode");
        returnOrder.setExpressCode("zt152123048556");
        returnOrder.setOwnerCode("ownerCode");
        Map<String, Object> orderExtendProps = new HashMap<>();
        orderExtendProps.put("reverseOrderBizType", "CUSTOMER_REFUSE");
        returnOrder.setExtendProps(orderExtendProps);

        TTReturnOrderCreateRequest.OrderLine a = new TTReturnOrderCreateRequest.OrderLine();
        a.setItemCode("A");
        a.setPlanQty(2L);
        a.setOrderLineNo("1");

        ttReturnOrderCreateRequest.setOrderLines(ListUtil.toList(a));

        ttReturnOrderCreateRequest.setReturnOrder(returnOrder);
        
        target.returnOrderCreate(JSONUtil.toJsonStr(ttReturnOrderCreateRequest));

        verify(remoteSalesReturnOrderClient).getList(any());
        verify(remoteSalesReturnOrderClient).buildExtraJson(any(), any());
        verify(remoteSalesReturnOrderClient).messageMqList(any(), any());
        verify(remoteSalesReturnOrderClient).save(argThat((ArgumentMatcher<SalesReturnOrderBO>) salesReturnOrderBO -> salesReturnOrderBO.getSalesReturnOrderDetailDTOList().size() == 1));
    }


    @Test
    public void additionalOrder() {
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();

        when(remoteSalesReturnOrderClient.get(any())).thenReturn(salesReturnOrderDTO);


        when(remoteSalesReturnOrderClient.modify(any(SalesReturnOrderDTO.class))).thenAnswer(invocationOnMock -> {
            SalesReturnOrderDTO argument = invocationOnMock.getArgument(0, SalesReturnOrderDTO.class);
            String instructionType = argument.salesReturnOrderExtraDTO().getInstructionType();
            System.out.println(instructionType);
            return true;
        });

        String s1 = target.returnOrderAdditional("{\"instructionType\":\"dddddd\",\"orderCode\":\"MFuu994853310023\",\"orderId\":\"MFU077882317817657137194\",\"ownerCode\":\"hh-ttth-xinzeng1\",\"warehouseCode\":\"DT_WMSTH20240220\"}");
        System.out.println(s1);
    }


    @Test
    public void cancelTest() {

        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CREATED.getCode());
        when(remoteSalesReturnOrderClient.get(any())).thenReturn(salesReturnOrderDTO);
        when(remoteSalesReturnOrderClient.billLogDTO(any(),any(),any(),any())).thenReturn(new BillLogDTO());
        
        JSONObject param = JSONUtil.createObj();
        target.returnOrderCancel(param.toString());

        verify(remoteLockSupportClient).execute(any(), any());
        verify(remoteSalesReturnOrderClient).buildExtraJson(any(), any());
        verify(remoteSalesReturnOrderClient).modify(argThat(it -> {
            System.out.println(JSONUtil.toJsonPrettyStr(it.getExtraJson()));
            return true;
        }));
        verify(remoteBillLogClient).save(any());
    }
}
