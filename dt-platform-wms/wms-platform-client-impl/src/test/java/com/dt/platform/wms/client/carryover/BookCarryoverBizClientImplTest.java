package com.dt.platform.wms.client.carryover;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.client.carryover.IBookCarryoverClient;
import com.dt.domain.bill.dto.carryover.BookCarryoverOriginDetailDTO;
import com.dt.domain.bill.dto.carryover.BookCarryoverTaskDTO;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.client.TestBase;
import com.dt.platform.wms.integration.carryover.IRemoteBookCarryoverOriginDetailClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BookCarryoverBizClientImplTest extends TestBase<BookCarryoverBizClientImpl> {

    @Mock
    private final IBookCarryoverClient bookCarryoverClient = mock(IBookCarryoverClient.class);
    
    @Mock
    private IRemoteBookCarryoverOriginDetailClient remoteBookCarryoverOriginDetailClient;

    @Mock
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Mock
    private Page<BookCarryoverOriginDetailDTO> page;
    @Mock
    private Page<BookCarryoverOriginDetailDTO> emptyPage;

    public BookCarryoverBizClientImplTest() {
        target = new BookCarryoverBizClientImpl();
    }

    @Test
    public void handleOriginDetailEnterprise() {
        BookCarryoverTaskDTO bookCarryoverTaskDTO = mock(BookCarryoverTaskDTO.class);
        BookCarryoverOriginDetailDTO bookCarryoverOriginDetailDTO = mock(BookCarryoverOriginDetailDTO.class);

        when(bookCarryoverTaskDTO.getControlContext()).thenReturn(StrUtil.EMPTY_JSON);
        doReturn(ListUtil.empty()).when(emptyPage).getRecords();
        doReturn(ListUtil.toList(bookCarryoverOriginDetailDTO)).when(page).getRecords();
        doReturn(1L).when(bookCarryoverOriginDetailDTO).getId();
        doReturn("a").when(bookCarryoverOriginDetailDTO).getOriginZoneCode();
        doReturn("a").when(bookCarryoverOriginDetailDTO).getTargetZoneCode();

        when(bookCarryoverClient.getTask(any())).thenReturn(Result.success(bookCarryoverTaskDTO));
        doReturn(emptyPage).when(remoteBookCarryoverOriginDetailClient).getPage(argThat(bookCarryoverOriginDetailParam -> bookCarryoverOriginDetailParam.getStartId() == 1L));
        doReturn(page).when(remoteBookCarryoverOriginDetailClient).getPage(argThat(bookCarryoverOriginDetailParam -> bookCarryoverOriginDetailParam.getStartId() == 0L));

        target.handleOriginDetailEnterprise();
    }
    
    @Test
    public void timeoutTest() {
        try {
            doReturn(ListUtil.toList("aaaa")).when(defaultWarehouseCodeConfig).getNoticeWarmMessageUrlList();
            Method timeoutCheck = target.getClass().getDeclaredMethod("timeoutCheck", BookCarryoverTaskDTO.class);
            timeoutCheck.setAccessible(true);
            BookCarryoverTaskDTO bookCarryoverTaskDTO = new BookCarryoverTaskDTO();
            bookCarryoverTaskDTO.setCreatedTime(System.currentTimeMillis() - 100000000);
            timeoutCheck.invoke(target, bookCarryoverTaskDTO);
        } catch (NoSuchMethodException | InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}