package com.dt.platform.wms.client;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.LotRuleDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.integration.IRemoteLotRuleClient;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.integration.IRemoteStockLocationClient;
import com.dt.platform.wms.param.transfer.TransferDetailUpdateBizParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class TransferBizClientTest extends TestBase<TransferBizClient> {
    @Mock
    private WmsOtherConfig wmsOtherConfig;
    @Mock
    private RedissonClient redissonClient;
    @Mock
    private RLock rLock;
    @Mock
    private IRemoteStockLocationClient remoteStockLocationClient;
    @Mock
    private IRemoteSkuLotClient remoteSkuLotClient;
    @Mock
    private IRemoteSkuClient remoteSkuClient;
    @Mock
    private IRemoteLotRuleClient remoteLotRuleClient;

    @Test
    public void withdrawAutoTransferTest() throws InterruptedException {
        String wms001 = "WMS001";
        when(rLock.tryLock(anyLong(), anyLong(), any())).thenReturn(true);
        when(redissonClient.getFairLock(any())).thenReturn(rLock);

        StockLocationDTO stockLocationDTO = new StockLocationDTO();
        String lot = "lot";
        String sku = "sku";
        stockLocationDTO.setSkuLotNo(lot);
        stockLocationDTO.setSkuCode(sku);
        when(remoteStockLocationClient.getList(any())).thenReturn(ListUtil.toList(stockLocationDTO));

        SkuLotDTO skuLotDTO = new SkuLotDTO();
        skuLotDTO.setCode(lot);
        skuLotDTO.setExpireDate(DateUtil.endOfDay(DateTime.now()).getTime());
        when(remoteSkuLotClient.getList(any())).thenReturn(ListUtil.toList(skuLotDTO));
        SkuDTO skuDTO = new SkuDTO();
        skuDTO.setTaotian(true);
        skuDTO.setWarehouseCode(wms001);
        skuDTO.setCode(sku);
        skuDTO.setWithdrawCycle(10);
        when(remoteSkuClient.getList(any())).thenReturn(ListUtil.toList(skuDTO));
        when(remoteLotRuleClient.getList(any())).thenReturn(ListUtil.toList(new LotRuleDTO()));

        RpcContextUtil.setWarehouseCode(wms001);
        when(wmsOtherConfig.getWithdrawAutoTransferWarehouseList()).thenReturn(ListUtil.toList(wms001));
        Map<String, List<String>> t = new HashMap<>();
        t.put(wms001, ListUtil.toList("ZP"));
        when(wmsOtherConfig.getWithdrawAutoTransferInventoryTypeConfig()).thenReturn(t);
        CargoOwnerDTO cargoOwnerDTO = new CargoOwnerDTO();
        cargoOwnerDTO.setWarehouseCode(wms001);
        Result<String> stringResult = target.withdrawAutoTransfer(cargoOwnerDTO);
        log.info("{}", stringResult);
    }

    @Test
    public void processDateTest() {
        TransferDetailUpdateBizParam param = new TransferDetailUpdateBizParam();
        param.setExpireDate(DateUtil.endOfDay(DateTime.now()).getTime());
        SkuDTO skuDTO = new SkuDTO();
        skuDTO.setLifeCycle(100);
        target.processDate(param, skuDTO);
        log.info("{}", param);
    }

}