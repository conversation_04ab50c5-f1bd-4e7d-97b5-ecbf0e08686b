package com.dt.platform.wms.biz.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.danding.business.oms.common.BO.LogisticsReturnScene;
import com.dt.component.common.enums.rs.RSDamageEnum;
import com.dt.component.common.enums.rs.RSHandoverStatusEnum;
import com.dt.component.common.enums.rs.RSRejectEnum;
import com.dt.component.common.enums.rs.RSReturnTypeEnum;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.rs.SalesReturnHandoverDTO;
import com.dt.domain.bill.dto.rs.SalesReturnHandoverDetailDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.platform.wms.biz.TestBase;
import com.dt.platform.wms.client.rs.SalesReturnHandoverBizClientImpl;
import com.dt.platform.wms.integration.IRemoteLockSupportClient;
import com.dt.platform.wms.integration.IRemoteShelfClient;
import com.dt.platform.wms.integration.IRemoteShipmentOrderClient;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.oms.IRemoteOmsLogisticClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnHandoverClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnHandoverDetailClient;
import com.dt.platform.wms.integration.rs.IRemoteSalesReturnOrderClient;
import com.dt.platform.wms.integration.warehouseManage.IRemoteLogicWarehouseClient;
import com.dt.platform.wms.integration.warehouseManage.IRemoteLogicWarehouseClient;
import com.dt.platform.wms.param.rs.SalesReturnHandoverBizParam;
import com.dt.platform.wms.param.rs.SalesReturnHandoverDetailBizParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HandoverDetailAddTest extends TestBase<SalesReturnHandoverBizClientImpl> {


    @Resource
    private IRemoteLockSupportClient remoteLockSupportClient = new MockRemoteLockSupport();

    @Mock
    private IRemoteSalesReturnHandoverClient remoteSalesReturnHandoverClient;

    @Mock
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Mock
    private IRemoteSalesReturnHandoverDetailClient remoteSalesReturnHandoverDetailClient;

    @Mock
    private IRemoteOmsLogisticClient remoteOmsLogisticClient;

    @Mock
    private IRemoteBillLogClient remoteBillLogClient;

    @Mock
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Mock
    private IRemoteLogicWarehouseClient remoteLogicWarehouseClient;

    @Mock
    private ShipmentOrderDTO shipmentOrderDTO;

    @Test
    public void confirm() {
        SalesReturnHandoverDTO salesReturnHandoverDTO = new SalesReturnHandoverDTO();
        salesReturnHandoverDTO.setStatus(RSHandoverStatusEnum.DOING.getCode());
        when(remoteSalesReturnHandoverClient.get(any())).thenReturn(salesReturnHandoverDTO);
        SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO = new SalesReturnHandoverDetailDTO();
        salesReturnHandoverDetailDTO.setReject(RSRejectEnum.NO.getCode());
        salesReturnHandoverDetailDTO.setExpressNo("a");
        when(remoteSalesReturnHandoverDetailClient.getList(any())).thenReturn(ListUtil.toList(salesReturnHandoverDetailDTO));
        LogisticsReturnScene logisticsReturnScene = new LogisticsReturnScene();
        logisticsReturnScene.setExpressNo("a");
        logisticsReturnScene.setAppCode("TTKJ");
        logisticsReturnScene.setIsIntercepted(true);
        logisticsReturnScene.setIsRejected(true);
        when(remoteOmsLogisticClient.logisticInfoList(anyList())).thenReturn(ListUtil.toList(logisticsReturnScene));

        SalesReturnHandoverBizParam param = new SalesReturnHandoverBizParam();
        param.setHandoverNo("asaa");
        target.confirmHandover(param);

        verify(remoteSalesReturnHandoverClient).persist(argThat(salesReturnHandoverBO -> salesReturnHandoverBO.getSalesReturnHandoverDetailDTOList().stream()
                .map(SalesReturnHandoverDetailDTO::getReturnType).map(RSReturnTypeEnum::fromInt).allMatch(RSReturnTypeEnum.CUSTOMER_RETURNS::equals)));
    }

    @Test
    public void addDetail() {
        SalesReturnHandoverDTO salesReturnHandoverDTO = new SalesReturnHandoverDTO();
        salesReturnHandoverDTO.setStatus(RSHandoverStatusEnum.DOING.getCode());
        when(remoteSalesReturnHandoverClient.get(any())).thenReturn(salesReturnHandoverDTO);
        when(remoteSalesReturnOrderClient.getExtraExpressNoList(any())).thenReturn(ListUtil.toList("aaa"));
        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        salesReturnOrderDTO.setReturnType(RSReturnTypeEnum.CUSTOMER_RETURNS.getCode());
        SalesReturnOrderDTO salesReturnOrderDTO1 = new SalesReturnOrderDTO();
        salesReturnOrderDTO1.setReturnType(RSReturnTypeEnum.TMS_CUT.getCode());
        when(remoteSalesReturnOrderClient.getList(any())).thenReturn(ListUtil.toList());
        LogisticsReturnScene logisticsReturnScene = new LogisticsReturnScene();
        logisticsReturnScene.setIsRejected(false);
        logisticsReturnScene.setIsIntercepted(true);
        logisticsReturnScene.setAppCode("TTKJ");
        when(remoteOmsLogisticClient.logisticInfoListNoFilter(any())).thenReturn(ListUtil.toList(logisticsReturnScene));
        when(remoteOmsLogisticClient.logisticInfoList(anyString())).thenReturn(ListUtil.toList(logisticsReturnScene));
        WarehouseDTO t = new WarehouseDTO();
        when(remoteLogicWarehouseClient.entityWarehouseCode(any())).thenReturn(t);
        when(shipmentOrderDTO.getTaotianPrintShipReturnInfo()).thenReturn("S");
        when(remoteShipmentOrderClient.getList(any())).thenReturn(ListUtil.toList(shipmentOrderDTO));
        when(remoteOmsLogisticClient.logisticInfoList(anyString())).thenReturn(ListUtil.toList(logisticsReturnScene));
        when(remoteLogicWarehouseClient.entityWarehouseCode(any())).thenReturn(t);
        when(shipmentOrderDTO.getTaotianPrintShipReturnInfo()).thenReturn("S");
        when(remoteShipmentOrderClient.getList(any())).thenReturn(ListUtil.toList(shipmentOrderDTO));

        SalesReturnHandoverDetailBizParam param = new SalesReturnHandoverDetailBizParam();
        param.setId(1L);
        param.setExpressNo("aaa");
        param.setDamage(RSDamageEnum.NO.getCode());
        param.setReject(RSRejectEnum.NO.getCode());
        target.addDetail(param);

        verify(remoteShipmentOrderClient).getList(any());
        verify(remoteOmsLogisticClient).logisticInfoListNoFilter(any());
        verify(remoteSalesReturnHandoverClient).persist(argThat(salesReturnHandoverBO -> {
            System.out.println(JSONUtil.toJsonStr(salesReturnHandoverBO));
            return salesReturnHandoverBO.getSalesReturnHandoverDetailDTOList().stream()
                    .map(SalesReturnHandoverDetailDTO::getReturnType)
                    .map(RSReturnTypeEnum::fromInt)
                    .allMatch(RSReturnTypeEnum.TMS_CUT::equals)
                    ;
        }));
    }
}




