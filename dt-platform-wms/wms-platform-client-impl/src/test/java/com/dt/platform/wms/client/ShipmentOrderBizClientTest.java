package com.dt.platform.wms.client;

import cn.hutool.core.collection.ListUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.platform.wms.integration.IRemoteShipmentOrderClient;
import com.dt.platform.wms.param.shipment.OutOfStockNotifyDetailParam;
import com.dt.platform.wms.param.shipment.OutOfStockNotifyParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;


@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class ShipmentOrderBizClientTest extends TestBase<ShipmentOrderBizClient>{

    @Mock
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Test
    public void outOfStockNotifyTest() {

        ShipmentOrderDTO shipmentOrderDTO = new ShipmentOrderDTO();
        shipmentOrderDTO.setShipmentOrderCode("");
        Mockito.when(remoteShipmentOrderClient.getShipmentOrderByCode(Mockito.any())).thenReturn(shipmentOrderDTO);
        ShipmentOrderDetailDTO shipmentOrderDetailDTO = new ShipmentOrderDetailDTO();
        shipmentOrderDetailDTO.setSkuCode("A");
        shipmentOrderDetailDTO.setExpSkuQty(BigDecimal.TEN);
        Mockito.when(remoteShipmentOrderClient.getDetailList(Mockito.anyString())).thenReturn(ListUtil.toList(shipmentOrderDetailDTO));

        OutOfStockNotifyParam param = new OutOfStockNotifyParam();
        OutOfStockNotifyDetailParam detailParam = new OutOfStockNotifyDetailParam();
        detailParam.setSkuCode("A");
        detailParam.setSkuQty(BigDecimal.TEN);
        detailParam.setRemark("aaaaaaaaaaaaaaa");
        param.setDetailParams(ListUtil.toList(detailParam));
        Result<Boolean> booleanResult = target.outOfStockNotify(param);
        log.info("{}",booleanResult);
        Assert.assertTrue(booleanResult.checkSuccess());
    }

}