package com.dt.platform.wms.client;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.RandomUtil;
import com.dt.component.common.enums.Deleted;
import com.dt.component.common.enums.FromSourceEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.pkg.PackageUnitEnum;
import com.dt.component.common.enums.sku.SkuStatusEnum;
import com.dt.component.common.enums.sku.SkuTagEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.platform.wms.biz.TestBase;
import com.dt.platform.wms.integration.IRemoteCargoOwnerClient;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import com.dt.platform.wms.integration.IRemoteWarehouseClient;
import com.dt.platform.wms.param.sku.SkuBizParam;
import com.dt.platform.wms.param.sku.SkuUomBizParam;
import com.dt.platform.wms.param.sku.SkuUpcBizParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class SkuBizClientTest extends TestBase<SkuBizClient> {

    @Mock
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Mock
    private IRemoteSkuClient remoteSkuClient;

    @Mock
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Test
    public void pdaModify() {
        SkuDTO update = new SkuDTO();
        update.setCargoCode("cargo");
        update.setCode("A");
        update.setCartonWeight(BigDecimal.TEN);
        update.setGrossWeight(BigDecimal.TEN);
        update.setNetWeight(BigDecimal.ONE);
        update.setStandardLength(BigDecimal.ONE);
        update.setStandardWidth(BigDecimal.ONE);
        update.setStandardHeight(BigDecimal.ONE);
        update.setLength(BigDecimal.ONE);
        update.setWidth(BigDecimal.ONE);
        update.setHeight(BigDecimal.ONE);
        update.setCartonPcs(1);
        update.setBracketGauge(100);
        Mockito.when(remoteWarehouseClient.getTaoTianWarehouse(Mockito.anyString())).thenReturn(false);
        Mockito.when(remoteSkuClient.get(Mockito.any())).thenReturn(update);
        target.pdaModify(update);
    }

    @Test
    public void modify() {
        SkuBizParam update = new SkuBizParam();
        update.setCargoCode("cargo");
        update.setCode("A");
        update.setGrossWeight(BigDecimal.ONE);
        update.setNetWeight(BigDecimal.ONE);
        update.setStandardLength(BigDecimal.ONE);
        update.setStandardWidth(BigDecimal.ONE);
        update.setStandardHeight(BigDecimal.ONE);
        update.setCartonWeight(BigDecimal.ONE);
        update.setLength(BigDecimal.ONE);
        update.setWidth(BigDecimal.ONE);
        update.setHeight(BigDecimal.ONE);
        update.setCartonPcs(1);
        update.setBracketGauge(100);
        update.setSkuTagList(ListUtil.empty());

        SkuUpcBizParam skuUpcBizParam = new SkuUpcBizParam();
        skuUpcBizParam.setIsDefault(SkuUpcDefaultEnum.YES.getStatus());
        skuUpcBizParam.setUpcCode("upcCode");
        skuUpcBizParam.setPackageUnitCode(PackageUnitEnum.PCS.getCode());
        skuUpcBizParam.setStatus(SkuStatusEnum.STATUS_ENABLED.getStatus());
        skuUpcBizParam.setDeleted(Deleted.NORMAL.getCode());
        update.setSkuUpcList(ListUtil.toList(skuUpcBizParam));

        SkuUomBizParam skuUomBizParam = new SkuUomBizParam();
        skuUomBizParam.setPackageUnitCode(PackageUnitEnum.PCS.getCode());
        skuUomBizParam.setPackageQty(BigDecimal.ONE);
        skuUomBizParam.setUomCode(RandomUtil.randomString(7));
        update.setSkuUomList(ListUtil.toList(skuUomBizParam));
        
        SkuDTO skuDTO = new SkuDTO();
        skuDTO.setSkuTag(SkuTagEnum.enumToNum(ListUtil.empty()));
        skuDTO.setFromSource(FromSourceEnum.ERP.value());
        
        Mockito.when(remoteWarehouseClient.getTaoTianWarehouse(Mockito.anyString())).thenReturn(false);
        Mockito.when(remoteSkuClient.get(Mockito.any())).thenReturn(skuDTO);
        CargoOwnerDTO cargoOwnerDTO = new CargoOwnerDTO();
        cargoOwnerDTO.setCargoTag(CargoTagEnum.enumToNum(ListUtil.empty()));
        Mockito.when(remoteCargoOwnerClient.queryByCode(Mockito.any())).thenReturn(cargoOwnerDTO);
        
        target.modify(update);
    }

}
