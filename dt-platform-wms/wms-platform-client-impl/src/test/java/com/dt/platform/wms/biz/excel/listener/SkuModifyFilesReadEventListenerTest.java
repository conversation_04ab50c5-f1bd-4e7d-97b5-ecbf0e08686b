package com.dt.platform.wms.biz.excel.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.platform.wms.biz.TestBase;
import com.dt.platform.wms.biz.excel.bo.ExcelImportSkuFilesBO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class SkuModifyFilesReadEventListenerTest extends TestBase {

    private SkuModifyFilesReadEventListener target = new SkuModifyFilesReadEventListener();

    @Test
    public void standHeightLtHeight() {
        ExcelImportSkuFilesBO excel = new ExcelImportSkuFilesBO();
        SkuDTO skuDTO = new SkuDTO();
        excel.setGrossWeight(BigDecimal.TEN);
        excel.setCartonWeight(BigDecimal.ONE);
        target.checkRowNotEmpty(excel, skuDTO);
        Assert.assertTrue(CollectionUtil.isNotEmpty(target.getErrorInfoList()));
    }


    @Test
    public void changeNetWeight() {
        ExcelImportSkuFilesBO excel = new ExcelImportSkuFilesBO();
        SkuDTO skuDTO = new SkuDTO();
        excel.setNetWeight(BigDecimal.TEN);
        excel.setGrossWeight(BigDecimal.ONE);
        target.checkRowNotEmpty(excel, skuDTO);
        Assert.assertTrue(CollectionUtil.isNotEmpty(target.getErrorInfoList()));
    }
    
    @Test
    public void changeNetWeightSecond() {
        ExcelImportSkuFilesBO excel = new ExcelImportSkuFilesBO();
        SkuDTO skuDTO = new SkuDTO();
        excel.setNetWeight(BigDecimal.TEN);
        skuDTO.setGrossWeight(BigDecimal.ONE);
        target.checkRowNotEmpty(excel, skuDTO);
        Assert.assertTrue(CollectionUtil.isNotEmpty(target.getErrorInfoList()));
    }
    
    @Test
    public void changeNetWeightThird() {
        ExcelImportSkuFilesBO excel = new ExcelImportSkuFilesBO();
        SkuDTO skuDTO = new SkuDTO();
        excel.setNetWeight(BigDecimal.TEN);
        target.checkRowNotEmpty(excel, skuDTO);
        Assert.assertTrue(CollectionUtil.isEmpty(target.getErrorInfoList()));
    }
    
    @Test
    public void changeNetWeightFour() {
        ExcelImportSkuFilesBO excel = new ExcelImportSkuFilesBO();
        SkuDTO skuDTO = new SkuDTO();
        excel.setNetWeight(BigDecimal.TEN);
        excel.setGrossWeight(BigDecimal.TEN);
        target.checkRowNotEmpty(excel, skuDTO);
        Assert.assertTrue(CollectionUtil.isEmpty(target.getErrorInfoList()));
    }
}
