package com.dt.platform.wms.client;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.SourceCodeScanTypeEnum;
import com.dt.component.common.enums.base.ContainerStatusEnum;
import com.dt.component.common.enums.base.ContainerTypeEnum;
import com.dt.component.common.enums.rs.OpInstructionTypeEnum;
import com.dt.component.common.enums.rs.RSBillSourceEnum;
import com.dt.component.common.enums.rs.RSOrderStatusEnum;
import com.dt.component.common.enums.rs.RSReturnTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.ContainerDTO;
import com.dt.domain.base.dto.LocationDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.bill.dto.AllocationOrderDTO;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.rs.*;
import com.dt.domain.bill.dto.sourceCode.OutSourceCodeDTO;
import com.dt.platform.wms.biz.ISkuLotBiz;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.client.rs.SalesReturnInspectBizClientImpl;
import com.dt.platform.wms.dto.rs.SalesReturnInspectResultDTO;
import com.dt.platform.wms.dto.rs.SalesReturnInspectSalesOrderBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.contLog.IRemoteContainerLogClient;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.rs.*;
import com.dt.platform.wms.integration.sourceCode.IRemoteOutSourceCodeClient;
import com.dt.platform.wms.param.rs.SalesReturnInspectBizParam;
import com.dt.platform.wms.param.rs.SalesReturnInspectExpressDetailBizParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class SalesReturnInspectionTest extends TestBase<SalesReturnInspectBizClientImpl> {
    @Mock
    private IRemoteContainerLogClient remoteContainerLogClient;

    @Mock
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Mock
    private IRemoteSalesReturnOrderClient remoteSalesReturnOrderClient;

    @Mock
    private IRemoteSalesReturnOrderDetailClient remoteSalesReturnOrderDetailClient;

    @Mock
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Mock
    private IRemotePackageClient remotePackageClient;

    @Mock
    private IRemoteAllocationOrderClient remoteAllocationOrderClient;

    @Mock
    private IRemoteSalesReturnInspectClient remoteSalesReturnInspectClient;

    @Mock
    private IRemoteSalesReturnInspectDetailClient remoteSalesReturnInspectDetailClient;

    @Mock
    private IRemoteSkuClient remoteSkuClient;

    @Mock
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Mock
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Mock
    private IRemoteOutSourceCodeClient remoteOutSourceCodeClient;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RLock rLock;

    @Mock
    private IRemoteContainerClient remoteContainerClient;

    @Mock
    private IRemoteBillLogClient remoteBillLogClient;

    @Mock
    private IRemoteOpExceptionClient remoteOpExceptionClient;

    @Mock
    private IRemoteOpExceptionReviewClient remoteOpExceptionReviewClient;

    @Mock
    private ISkuLotBiz skuLotBiz;
    @Mock
    private IRemoteSpecialLocationClient remoteSpecialLocationClient;

    @Mock
    private IRemoteErpCargoConfigClient remoteErpCargoConfigClient;

    @Mock
    private WmsOtherConfig wmsOtherConfig;

    @Test
    public void getSalesOrderInformationTest() {

        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        salesReturnOrderDTO.setPoNo("poNo");

        ShipmentOrderDTO shipmentOrderDTO = new ShipmentOrderDTO();

        PackageDTO packageDTO = new PackageDTO();

        AllocationOrderDTO allocationOrderDTO2 = new AllocationOrderDTO();
        allocationOrderDTO2.setSkuLotNo("lot2");
        allocationOrderDTO2.setPickQty(BigDecimal.TEN);

        AllocationOrderDTO allocationOrderDTO = new AllocationOrderDTO();
        allocationOrderDTO.setSkuLotNo("lot1");
        allocationOrderDTO.setPickQty(BigDecimal.TEN);
        when(remoteAllocationOrderClient.getList(any())).thenReturn(ListUtil.toList(allocationOrderDTO, allocationOrderDTO2));


        OutSourceCodeDTO outSourceCodeDTO = new OutSourceCodeDTO();
        outSourceCodeDTO.setScanType(SourceCodeScanTypeEnum.SN.getCode());
        outSourceCodeDTO.setSkuCode("A");
        outSourceCodeDTO.setSnCode("AAA");
        outSourceCodeDTO.setQty(BigDecimal.TEN);

        when(remoteSalesReturnOrderClient.get(any())).thenReturn(salesReturnOrderDTO);
        when(remoteShipmentOrderClient.get(any())).thenReturn(shipmentOrderDTO);
        when(remotePackageClient.getList(any())).thenReturn(ListUtil.toList(packageDTO));
        when(remoteSalesReturnInspectDetailClient.getList(any())).thenReturn(ListUtil.empty());
        when(remoteOutSourceCodeClient.getList(any())).thenReturn(ListUtil.toList(outSourceCodeDTO));


        SalesReturnInspectBizParam returnInspectBizParam = new SalesReturnInspectBizParam();
        SalesReturnInspectExpressDetailBizParam salesReturnInspectExpressDetailBizParam = new SalesReturnInspectExpressDetailBizParam();
        salesReturnInspectExpressDetailBizParam.setQty(BigDecimal.TEN);
        returnInspectBizParam.setPackList(ListUtil.toList(salesReturnInspectExpressDetailBizParam));

        SalesReturnInspectSalesOrderBizDTO data = target.getSalesOrderInformation(returnInspectBizParam).getData();
        System.out.println(JSONUtil.toJsonPrettyStr(data));
    }

    @Test
    public void completeExpressTest() throws InterruptedException {

        when(wmsOtherConfig.getPartReturnCanary()).thenReturn(false);
        when(redissonClient.getLock(any())).thenReturn(rLock);
        when(rLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);

        OpExceptionReviewDTO opExceptionReviewDTO = new OpExceptionReviewDTO();
        opExceptionReviewDTO.setInstructionType(OpInstructionTypeEnum.RECEIVE_CONTINUE_INSPECTE.getCode());
        when(remoteOpExceptionReviewClient.getList(any())).thenReturn(ListUtil.toList(opExceptionReviewDTO));

        ContainerDTO containerDTO = new ContainerDTO();
        containerDTO.setStatus(ContainerStatusEnum.OCCUPY.getValue());
        containerDTO.setOccupyType(ContainerTypeEnum.XT_RECEIVE.getCode());
        String occupyNo = "NO";
        containerDTO.setOccupyNo(occupyNo);
        when(remoteContainerClient.queryByCode(anyString())).thenReturn(containerDTO);

        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        salesReturnOrderDTO.setPoNo("PO");
        salesReturnOrderDTO.setStatus(RSOrderStatusEnum.HANDOVER.getCode());
        salesReturnOrderDTO.setBillSource(RSBillSourceEnum.MALL_DIRECT.getCode());
        salesReturnOrderDTO.setReturnType(RSReturnTypeEnum.CUSTOMER_RETURNS.getCode());
        when(remoteSalesReturnOrderClient.get(any())).thenReturn(salesReturnOrderDTO);
        when(remoteSalesReturnOrderClient.buildExtraJson(any(), any())).thenReturn(salesReturnOrderDTO);

        SalesReturnOrderDetailDTO salesReturnOrderDetailDTO = getReturnOrderDetailDTO("A");
        when(remoteSalesReturnOrderDetailClient.getList(any())).thenReturn(ListUtil.toList(salesReturnOrderDetailDTO));

        OutSourceCodeDTO outSourceCodeDTO = new OutSourceCodeDTO();
        outSourceCodeDTO.setSkuCode("A");
        outSourceCodeDTO.setSnCode("sn");
        outSourceCodeDTO.setQty(BigDecimal.TEN);
        outSourceCodeDTO.setScanType("");
        when(remoteOutSourceCodeClient.getList(any())).thenReturn(ListUtil.toList(outSourceCodeDTO));

        SalesReturnInspectBizParam returnInspectBizParam = new SalesReturnInspectBizParam();
        returnInspectBizParam.setPackList(ListUtil.toList(
                getSalesReturnInspectExpressDetailBizParam("A", BigDecimal.TEN,SkuQualityEnum.SKU_QUALITY_AVL),
                getSalesReturnInspectExpressDetailBizParam("B", BigDecimal.TEN,SkuQualityEnum.SKU_QUALITY_AVL)
        ));
        returnInspectBizParam.setContCode("container");
        returnInspectBizParam.setInspectOrderNo(occupyNo);

        SkuUpcDTO skuUpcDTO = new SkuUpcDTO();
        skuUpcDTO.setSkuCode("A");
        skuUpcDTO.setUpcCode("B");
        when(remoteSkuClient.getSkuUpcList(any())).thenReturn(ListUtil.toList(skuUpcDTO));

        SkuLotDTO skuLotDTO = new SkuLotDTO();
        skuLotDTO.setCode("LOT1");
        when(skuLotBiz.findAndFormatSkuLot(any(), any(), any())).thenReturn(skuLotDTO);

        when(remoteContainerClient.findOrCreateUsableContainer()).thenReturn(new ContainerDTO());
        when(remoteSpecialLocationClient.getLocationByReceivingSkuQuality(anyString())).thenReturn(new LocationDTO());

        target.completeExpressV2(returnInspectBizParam);


    }



    @Test
    public void receiveWriteBackTest() {
        SalesReturnOrderDetailDTO secondDetail = new SalesReturnOrderDetailDTO();
        secondDetail.setSkuCode("A");
        secondDetail.setExpectQty(BigDecimal.TEN);
        secondDetail.setAvlQty(BigDecimal.ZERO);
        secondDetail.setDamageQty(BigDecimal.ZERO);

        SalesReturnOrderDetailDTO firstDetail = new SalesReturnOrderDetailDTO();
        firstDetail.setSkuCode("A");
        firstDetail.setExpectQty(BigDecimal.TEN);
        firstDetail.setAvlQty(BigDecimal.ZERO);
        firstDetail.setDamageQty(BigDecimal.ZERO);

        SalesReturnOrderReceiveDTO secondReceive = new SalesReturnOrderReceiveDTO();
        secondReceive.setSkuCode("A");
        secondReceive.setQty(BigDecimal.valueOf(6));
        secondReceive.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());

        SalesReturnOrderReceiveDTO firstReceive = new SalesReturnOrderReceiveDTO();
        firstReceive.setSkuCode("A");
        firstReceive.setQty(BigDecimal.valueOf(6));
        firstReceive.setSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());

        ArrayList<SalesReturnOrderDetailDTO> detailDTOList = ListUtil.toList(firstDetail, secondDetail);
        ArrayList<SalesReturnOrderReceiveDTO> receiveDTOList = ListUtil.toList(firstReceive, secondReceive);
        target.receiveWriteBack(detailDTOList, receiveDTOList);

        System.out.println(JSONUtil.toJsonPrettyStr(detailDTOList));
    }

    @Test
    public void casePartInspectionPass() throws InterruptedException {
        when(wmsOtherConfig.getPartReturnCanary()).thenReturn(false);
        when(redissonClient.getLock(any())).thenReturn(rLock);
        when(rLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);

        OpExceptionReviewDTO opExceptionReviewDTO = new OpExceptionReviewDTO();
        opExceptionReviewDTO.setInstructionType(OpInstructionTypeEnum.RECEIVE_CONTINUE_INSPECTE.getCode());
        when(remoteOpExceptionReviewClient.getList(any())).thenReturn(ListUtil.toList(opExceptionReviewDTO));

        ContainerDTO containerDTO = new ContainerDTO();
        containerDTO.setStatus(ContainerStatusEnum.OCCUPY.getValue());
        containerDTO.setOccupyType(ContainerTypeEnum.XT_RECEIVE.getCode());
        String occupyNo = "NO";
        containerDTO.setOccupyNo(occupyNo);
        when(remoteContainerClient.queryByCode(anyString())).thenReturn(containerDTO);

        SalesReturnOrderDTO salesReturnOrderDTO = new SalesReturnOrderDTO();
        salesReturnOrderDTO.setPoNo("PO");
        salesReturnOrderDTO.setStatus(RSOrderStatusEnum.HANDOVER.getCode());
        salesReturnOrderDTO.setBillSource(RSBillSourceEnum.OMS.getCode());
        salesReturnOrderDTO.setReturnType(RSReturnTypeEnum.CUSTOMER_RETURNS.getCode());
        when(remoteSalesReturnOrderClient.get(any())).thenReturn(salesReturnOrderDTO);
        when(remoteSalesReturnOrderClient.buildExtraJson(any(), any())).thenReturn(salesReturnOrderDTO);

        when(remoteSalesReturnOrderDetailClient.getList(any())).thenReturn(ListUtil.toList(
                getReturnOrderDetailDTO("A"),
                getReturnOrderDetailDTO("B")
        ));

        SalesReturnInspectBizParam returnInspectBizParam = new SalesReturnInspectBizParam();
        returnInspectBizParam.setPackList(ListUtil.toList(
                getSalesReturnInspectExpressDetailBizParam("A", BigDecimal.ONE,SkuQualityEnum.SKU_QUALITY_AVL),
                getSalesReturnInspectExpressDetailBizParam("A", BigDecimal.ONE,SkuQualityEnum.SKU_QUALITY_DAMAGE)
        ));
        returnInspectBizParam.setContCode("container");
        returnInspectBizParam.setInspectOrderNo(occupyNo);

        SkuUpcDTO skuUpcDTO = new SkuUpcDTO();
        skuUpcDTO.setSkuCode("A");
        skuUpcDTO.setUpcCode("B");
        when(remoteSkuClient.getSkuUpcList(any())).thenReturn(ListUtil.toList(skuUpcDTO));

        SkuLotDTO skuLotDTO = new SkuLotDTO();
        skuLotDTO.setCode("LOT1");
        when(skuLotBiz.findAndFormatSkuLot(any(), any(), any())).thenReturn(skuLotDTO);

        when(remoteContainerClient.findOrCreateUsableContainer()).thenReturn(new ContainerDTO());
        when(remoteSpecialLocationClient.getLocationByReceivingSkuQuality(anyString())).thenReturn(new LocationDTO());

        Result<SalesReturnInspectResultDTO> salesReturnInspectResultDTOResult = target.completeExpress(returnInspectBizParam);
        log.info("{}",JSONUtil.toJsonPrettyStr(salesReturnInspectResultDTOResult));

    }
    private static SalesReturnOrderDetailDTO getReturnOrderDetailDTO(String skuCode) {
        SalesReturnOrderDetailDTO salesReturnOrderDetailDTO = new SalesReturnOrderDetailDTO();
        salesReturnOrderDetailDTO.setSkuCode(skuCode);
        salesReturnOrderDetailDTO.setExpectQty(BigDecimal.TEN);
        salesReturnOrderDetailDTO.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
        salesReturnOrderDetailDTO.setUpcCode(skuCode);
        salesReturnOrderDetailDTO.setAllowEntry("");
        salesReturnOrderDetailDTO.setAvlQty(BigDecimal.ZERO);
        salesReturnOrderDetailDTO.setDamageQty(BigDecimal.ZERO);
        return salesReturnOrderDetailDTO;
    }

    private static SalesReturnInspectExpressDetailBizParam getSalesReturnInspectExpressDetailBizParam(String skuCode,BigDecimal qty,SkuQualityEnum skuQualityEnum) {
        SalesReturnInspectExpressDetailBizParam salesReturnInspectExpressDetailBizParam = new SalesReturnInspectExpressDetailBizParam();
        salesReturnInspectExpressDetailBizParam.setSkuCode(skuCode);
        salesReturnInspectExpressDetailBizParam.setSkuQuality(skuQualityEnum.getLevel());
        salesReturnInspectExpressDetailBizParam.setQty(qty);
        salesReturnInspectExpressDetailBizParam.setTraceCode("sn");
        salesReturnInspectExpressDetailBizParam.setUpcCode(skuCode);
        salesReturnInspectExpressDetailBizParam.setSkuName(skuCode);
        return salesReturnInspectExpressDetailBizParam;
    }

}
