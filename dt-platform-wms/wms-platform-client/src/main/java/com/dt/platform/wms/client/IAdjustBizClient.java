package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.bill.param.AdjustParam;
import com.dt.platform.wms.dto.adjust.AdjustBizDTO;
import com.dt.platform.wms.dto.adjust.AdjustCreateSkuLotBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.adjust.AdjustBizParam;
import com.dt.platform.wms.param.adjust.AdjustCreateSkuLotParam;

import java.util.List;

public interface IAdjustBizClient {

    Result<Boolean> createFromOPException(AdjustBizParam param);

    Result<Boolean> createV2(AdjustBizParam param);

    /**
     * 新增
     *
     * @param param
     * @return
     */
    Result<Boolean> create(AdjustBizParam param);

    /**
     * 修改
     *
     * @param param
     * @return
     */
    Result<Boolean> modify(AdjustBizParam param);

    /**
     * 分页列表
     *
     * @param param
     * @return
     */
    Result<Page<AdjustBizDTO>> getPage(AdjustBizParam param);

    /**
     * 获取详情
     *
     * @param param
     * @return
     */
    Result<AdjustBizDTO> getDetail(AdjustBizParam param);

    /**
     * 完成
     */
    Result<Boolean> complete(CodeParam param);

    /**
     * 审核
     *
     * @param param
     * @return
     */
    @Deprecated
    Result<Boolean> audit(CodeParam param);

    /**
     * 取消
     *
     * @param param
     * @return
     */
    Result<Boolean> cancel(CodeParam param);

    /**
     * 提交审核
     *
     * @param param
     * @return
     */
    Result<Boolean> commitAudit(CodeParam param);

    /**
     * 仓内审核
     *
     * @param param
     * @return
     */
    Result<Boolean> innerAudit(CodeParam param);

    /**
     * @param adjustCreateSkuLotParam
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.adjust.AdjustCreateSkuLotBizDTO>
     * <AUTHOR>
     * @describe:
     * @date 2024/6/6 9:29
     */
    Result<AdjustCreateSkuLotBizDTO> querySkuInfoByAdjust(AdjustCreateSkuLotParam adjustCreateSkuLotParam);

    /**
     * @param adjustCreateSkuLotParam
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @describe:
     * @date 2024/6/6 9:29
     */
    Result<String> createSkuLotByAdjust(AdjustCreateSkuLotParam adjustCreateSkuLotParam);

    Result<List<IdNameVO>> queryTag(AdjustParam param);

    Result<Boolean> modifyTag(AdjustParam param);
}
