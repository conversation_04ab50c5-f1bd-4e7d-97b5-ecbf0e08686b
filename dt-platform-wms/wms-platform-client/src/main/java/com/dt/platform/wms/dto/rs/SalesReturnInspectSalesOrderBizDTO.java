package com.dt.platform.wms.dto.rs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
* <p>
    * 销退质检单
    * </p>
*
* <AUTHOR>
* @since 2024-01-25
*/
@Data
public class SalesReturnInspectSalesOrderBizDTO implements Serializable {

private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "销退单")
    private String salesReturnOrderNo;

    @ApiModelProperty(value = "清关天数")
    private BigDecimal declareTime;

    @ApiModelProperty(value = "保税类型")
    private String taxType;
    @ApiModelProperty(value = "保税类型")
    private String taxTypeDesc;

    private Long skuType;
    private BigDecimal skuQty;

    @ApiModelProperty(value = "销售店铺ID")
    private String saleShopId;

    @ApiModelProperty(value = "销售店铺名称")
    private String saleShopName;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "货主名称")
    private String cargoName;

    @ApiModelProperty(value = "不支持二次入区")
    private Boolean notAllowEntry;
    @ApiModelProperty(value = "不支持二次入区")
    private String notAllowEntryDesc;

    List<SalesReturnInspectSalesOrderSkuBizDTO> skuList;


    @ApiModelProperty(value = "单据来源")
    private Integer billSource;
    @ApiModelProperty(value = "单据来源")
    private String billSourceDesc;

    @ApiModelProperty(value = "附加指令")
    private String instructionType;
    private String instructionTypeDesc;

    @ApiModelProperty(value = "退货原因")
    private String returnReason;
    private String returnReasonDesc;
    @ApiModelProperty(value = "高危客户")
    private String highRiskCustom;
    private String highRiskCustomDesc;
    @ApiModelProperty(value = "高危包裹")
    private String highRiskPackage;
    private String highRiskPackageDesc;
    @ApiModelProperty(value = "退货类型")
    private Integer returnType;
    private String returnTypeDesc;
    
    @ApiModelProperty(value = "退回仓编码")
    private String warehouseCode;
    
    @ApiModelProperty(value = "实体仓编码")
    private String realWarehouseCode;

    @ApiModelProperty(value = "是否淘天")
    private Boolean isTaoTian;

    @ApiModelProperty(value = "防伪码等信息")
    private List<SkuTraceCode> skuTraceCodeList;

    @ApiModelProperty(value = "SN")
    private List<SkuSnCode> skuSnCodeList;
    @ApiModelProperty(value = "溯源码")
    private List<SkuSourceCode> sourceCodeList;

    private List<SalesReturnOrderDetailBizDTO> salesReturnOrderDetailBizDTOList;
}
