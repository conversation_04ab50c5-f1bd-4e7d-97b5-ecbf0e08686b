package com.dt.platform.wms.param.sku;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/27 13:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SkuLot对象", description = "货品批次表")
public class SkuLotBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "生产批次")
    private String productionNo;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "货主编码列表")
    private List<String> cargoCodeList;

    @ApiModelProperty(value = "商品编码列表")
    private List<String> skuCodeList;

    @ApiModelProperty(value = "商品条码列表")
    private List<String> upcCodeList;

    @ApiModelProperty(value = "批次列表")
    private List<String> skuLotNoList;

    @ApiModelProperty(value = "入库日期-开始")
    private Long receiveDateStart;
    @ApiModelProperty(value = "入库日期-结束")
    private Long receiveDateEnd;

    @ApiModelProperty(value = "生产日期-开始")
    private Long manufDateStart;
    @ApiModelProperty(value = "生产日期-结束")
    private Long manufDateEnd;

    @ApiModelProperty(value = "失效/过期日期 -开始")
    private Long expireDateStart;
    @ApiModelProperty(value = "失效/过期日期-结束")
    private Long expireDateEnd;

    @ApiModelProperty(value = "禁售日期-开始")
    private Long withdrawDateStart;
    @ApiModelProperty(value = "禁售日期-结束")
    private Long withdrawDateEnd;

    @ApiModelProperty(value = "外部批次编码")
    private String externalSkuLotNo;
    private List<String> externalSkuLotNoList;

    @ApiModelProperty(value = "入库关联单号(拓展单号)")
    private String externalLinkBillNo;
    private List<String> externalLinkBillNoList;

    @ApiModelProperty(value = "关联批次号")
    private String linkSkuLotNo;
    private List<String> linkSkuLotNoList;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;
    private List<String> palletCodeList;

    @ApiModelProperty(value = "箱码")
    private String boxCode;
    private List<String> boxCodeList;

    @ApiModelProperty(value = "效期码")
    private String validityCode;
    private List<String> validityCodeList;
}