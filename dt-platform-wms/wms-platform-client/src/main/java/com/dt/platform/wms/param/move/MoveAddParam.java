package com.dt.platform.wms.param.move;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/3 11:24
 */
@Data
public class MoveAddParam implements Serializable {

    @ApiModelProperty(value = "货主编码")
    @NotEmpty(message = "货主参数不能为空")
    private String cargoCode;

    @ApiModelProperty(value = "操作方式 字典组：OP_TYPE")
    private String opType;

    @ApiModelProperty(value = "移位单列表")
    private List<MoveAddDetailParam> detailList;

}
