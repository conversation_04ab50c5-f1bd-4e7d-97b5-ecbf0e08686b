package com.dt.platform.wms.dto.pkg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 出库单分析结果DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("包裹分析结果DTO")
public class PackAnalysisBizDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "序号")
    private Integer id;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "包裹类型")
    private String isPre;
    private String isPreDesc;

    @ApiModelProperty(value = "销售平台")
    private String salePlatform;
    private String salePlatformName;

    @ApiModelProperty(value = "订单品种类型")
    private String packageStruct;
    private String packageStructName;

    @ApiModelProperty(value = "预计出库时间（天）")
    private String expOutStockDate_day;

    @ApiModelProperty(value = "预计出库时间（小时）")
    private String expOutStockDate_hour;

    @ApiModelProperty(value = "创建时间（天）")
    private String createdTime_day;

    @ApiModelProperty(value = "创建时间（小时）")
    private String createdTime_hour;

    @ApiModelProperty(value = "付款时间（天）")
    private String payDate_day;

    @ApiModelProperty(value = "付款时间（小时）")
    private String payDate_hour;

    @ApiModelProperty(value = "出库时间（天）")
    private String outStockDate_day;

    @ApiModelProperty(value = "出库时间（小时）")
    private String outStockDate_hour;

    @ApiModelProperty(value = "拣选完成时间（天）")
    private String pickCompleteSkuDate_day;

    @ApiModelProperty(value = "拣选完成时间（小时）")
    private String pickCompleteSkuDate_hour;

    @ApiModelProperty(value = "复核完成时间（天）")
    private String checkCompleteDate_day;

    @ApiModelProperty(value = "复核完成时间（小时）")
    private String checkCompleteDate_hour;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;
    private String carrierName;

    @ApiModelProperty(value = "省份")
    private String receiverProvName;

    @ApiModelProperty(value = "城市")
    private String receiverCityName;

    @ApiModelProperty(value = "区/县")
    private String receiverAreaName;

    @ApiModelProperty(value = "分组值映射，key: 分组字段名, value: 分组字段值")
    private Map<String, String> dimensionValues;

    @ApiModelProperty(value = "订单总数")
    private Integer orderCount;

    @ApiModelProperty(value = "商品件数")
    private BigDecimal skuQtySum;

    @ApiModelProperty(value = "创建状态订单数")
    private Integer createdOrderCount;

    @ApiModelProperty(value = "预处理失败订单数")
    private Integer pretreatmentFailOrderCount;

    @ApiModelProperty(value = "预处理完成订单数")
    private Integer pretreatmentCompleteOrderCount;

    @ApiModelProperty(value = "汇单失败订单数")
    private Integer collectedFailOrderCount;

    @ApiModelProperty(value = "已汇总订单数")
    private Integer collectedOrderCount;

    @ApiModelProperty(value = "拣选开始订单数")
    private Integer pickStartOrderCount;

    @ApiModelProperty(value = "拣选完成订单数")
    private Integer pickEndOrderCount;

    @ApiModelProperty(value = "复核开始订单数")
    private Integer checkStartOrderCount;

    @ApiModelProperty(value = "复核完成订单数")
    private Integer checkCompleteOrderCount;

    @ApiModelProperty(value = "已出库订单数")
    private Integer outOrderCount;

    @ApiModelProperty(value = "拦截订单数")
    private Integer interceptOrderCount;

    @ApiModelProperty(value = "取消订单数")
    private Integer cancelOrderCount;

    @ApiModelProperty(value = "拦截取消订单数")
    private Integer interceptCancelOrderCount;

    @ApiModelProperty(value = "缺货出库订单数")
    private Integer shortageOutOrderCount;
}
