package com.dt.platform.wms.param.check;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/10/26 16:47
 */
@Data
public class PackSubExpNoParam implements Serializable {


    @ApiModelProperty(value = "商品编码")
    @NotEmpty(message = "商品编码不能为空")
    private String skuCode;

    @ApiModelProperty(value = "数量")
    @NotNull(message = "数量不能为空")
    private BigDecimal qty;

    @ApiModelProperty(value = "失效/过期日期")
    private Long expireDate;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

}
