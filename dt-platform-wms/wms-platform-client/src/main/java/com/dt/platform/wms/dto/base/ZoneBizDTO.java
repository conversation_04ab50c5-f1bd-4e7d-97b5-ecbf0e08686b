package com.dt.platform.wms.dto.base;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库区管理
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Zone对象", description="库区管理")
public class ZoneBizDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "库区编码 不允许修改，唯一")
    private String code;

    @ApiModelProperty(value = "库区名称")
    private String name;

    @ApiModelProperty(value = "拣货序号")
    private Long pickSeq;

    @ApiModelProperty(value = "库区类型 字典组:ZONE_TYPE")
    private String type;

    @ApiModelProperty(value = "存放规则 字典组:LOCATION_MIX_RULE")
    private String storageRule;

    @ApiModelProperty(value = "正残属性")
    private String skuQuality;

    @ApiModelProperty(value = "状态码 字典组：ABLE_STATUS")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "防火物理分区")
    private String physicalPartition;


}