package com.dt.platform.wms.dto.lot;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 批次库存
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "StockLot对象", description = "批次库存")
public class StockLotBizDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;
    private String warehouseName;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private String skuName;
    private String upcCode;

    /**
     * 实物数量
     */
    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    /**
     * 冻结数量
     */
    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    /**
     * 占用数量
     */
    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    /**
     * 可用数量 实物库存-冻结-占用=可用数
     */
    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用=可用数")
    private BigDecimal availableQty;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;
    private String skuQualityName;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    /**
     *
     */
    @ApiModelProperty(value = "库存商品类型")
    private String stockSkuType;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "外部批次编码")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "入库关联单号")
    private String externalLinkBillNo;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;
    private String inventoryTypeDesc;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;


}