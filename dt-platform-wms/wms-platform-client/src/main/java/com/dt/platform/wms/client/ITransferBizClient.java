package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.bill.dto.TransferDTO;
import com.dt.domain.bill.param.TransferParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.transfer.*;

import java.util.List;

/**
 * 转移单
 * Created by nobody on 2020/12/28 17:03
 */
public interface ITransferBizClient {

    /**
     * 增加转移单信息
     * @return
     */
    Result<String> addTransfer(TransferAddBizParam param);

    /**
     * 转移单分页
     * @return
     */
    Result<Page<TransferDTO>>  page(TransferParam param);

    /**
     * 转移单明细
     * @param param
     * @return
     */
    Result<TransferDTO> detail(TransferParam param);

    Result<Boolean> maintainLocation(TransferUpdateBizParam param);

    /**
     * 添加转移明细
     * @param param
     * @return
     */
    Result<Boolean> updateTransferDetail(TransferUpdateBizParam param);

    /**
     * 审核
     * @param transferExamineBizParam
     * @return
     */
    @Deprecated
    Result<Boolean> examine(TransferExamineBizParam transferExamineBizParam);

    /**
     * 提交审核
     * @param transferExamineBizParam
     * @return
     */
    Result<Boolean> submit(TransferExamineBizParam transferExamineBizParam);

    /**
     * 取消
     * @param param
     * @return
     */
    Result<Boolean> cancel(TransferCancelBizParam param);

    /**
     * 确认
     * @param transferConfirmBizParam
     * @return
     */
    Result<Boolean> confirm(TransferConfirmBizParam transferConfirmBizParam);

    /**
     * 转移单提交审核
     * @param param
     * @return
     */
    Result<Boolean> commitAudit(TransferExamineBizParam param);

    /**
     * 转移单仓内审核
     * @param param
     * @return
     */
    Result<Boolean> innerAudit(TransferExamineBizParam param);

    Result<String> withdrawAutoTransferRetry();

    Result<String> withdrawAutoTransfer() throws InterruptedException;

    Result<String> withdrawAutoTransfer(String param) throws InterruptedException;

    Result<String> withdrawAutoTransfer(CargoOwnerDTO cargoOwnerDTO) throws InterruptedException;

    Result<List<IdNameVO>> queryTag(TransferParam param);

    Result<Boolean> modifyTag(TransferParam param);
}
