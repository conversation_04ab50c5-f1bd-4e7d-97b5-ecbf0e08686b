package com.dt.platform.wms.param.rec;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/12/31 13:46
 */
@Data
public class CheckSkuLotParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "skuCode")
    @NotEmpty(message = "商品条形码不能为空")
    private String skuCode;

    @ApiModelProperty(value = "asnId 转移单不需要")
    @NotEmpty(message = "到货通知单号不能为空")
    private String asnId;

    @ApiModelProperty(value = "容器编码 转移单不需要")
    @NotEmpty(message = "容器编码不能为空")
    private String contCode;

    @ApiModelProperty(value = "工作台号 转移单不需要")
    private String workBenchCode;

    @ApiModelProperty(value = "sku属性")
    @NotEmpty(message = "sku属性不能为空")
    private String skuQuality;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "入库关联单号(拓展单号)")
    private String externalLinkBillNo;

    @ApiModelProperty(value = "次品等级")
    private String inventoryType;

    @ApiModelProperty(value = "托盘")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

}
