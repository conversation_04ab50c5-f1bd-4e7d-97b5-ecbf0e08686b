package com.dt.platform.wms.param.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 库区管理
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-16
 */
@Data
@Accessors(chain = true)
@ApiModel(value="Zone对象", description="库区管理")
public class ZoneBizModifyParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "库区编码 不允许修改，唯一")
    @NotEmpty(message = "库区不能为空")
    private String code;

    @ApiModelProperty(value = "库区名称")
    private String name;

    @ApiModelProperty(value = "拣货序号")
    private Long pickSeq;

    @ApiModelProperty(value = "库区类型 ZoneTypeEnum")
    private String type;

    @ApiModelProperty(value = "存放规则")
    private String storageRule;

    @ApiModelProperty(value = "正残属性: AVL 正品 次品 DAMAGE")
    private String skuQuality;

    @ApiModelProperty(value = "物理防火分区编码列表")
    private String physicalPartition;

}
