package com.dt.platform.wms.dto.shelf;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 上架管理明细
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ShelfDetail对象", description="上架管理明细")
public class ShelfDetailBizDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;
    private String warehouseName;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "商品属性")
    private String skuQualityName;

    @ApiModelProperty(value = "容器编码")
    private String contCode;

    @ApiModelProperty(value = "行号")
    private String lineSeq;

    @ApiModelProperty(value = "单据明细ID")
    private String uid;

    @ApiModelProperty(value = "上架单号")
    private String shelfCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "上架数量")
    private BigDecimal shelfSkuQty;

    @ApiModelProperty(value = "来源库位")
    private String originLocationCode;

    @ApiModelProperty(value = "推荐目标库位")
    private String recLocationCode;

    @ApiModelProperty(value = "实际目标库位")
    private String targetLocationCode;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;
    private String receiveDateFormat;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;
    private String manufDateFormat;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;
    private String expireDateFormat;

    @ApiModelProperty(value = "禁售日期")
    private Long withdrawDate;
    private String withdrawDateFormat;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "状态码 ")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "外部批次编码")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "批次属性(单据号)")
    private String externalLinkBillNo;

    @ApiModelProperty(value = "标记")
    private Integer mark;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "效期码")
    private String validityCode;
}