package com.dt.platform.wms.param.transfer;

import com.dt.domain.bill.dto.TransferDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 增加转移单
 * Created by nobody on 2020/12/28 17:29
 */
@Data
@ApiModel(description="库存转移单新增参数")
@NotNull
public class TransferUpdateBizParam implements Serializable {

    @ApiModelProperty("转移单编码")
    @NotBlank
    private String transferCode;

    @ApiModelProperty("转移单明细")
    private List<TransferDetailUpdateBizParam> transferDetailUpdateBizParamList;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("货主编码")
    private String cargoCode;

    @ApiModelProperty("业务场景")
    private String businessType;

    @ApiModelProperty("转移原因")
    private String reason;

    @ApiModelProperty("转移描述")
    private String note;

    private FlagEnum flag;

    private TransferDTO transferDTO;
    
    public enum FlagEnum {
        UPDATE,
        IMPORT_ADD,
        IMPORT_UPDATE,
    }
}
