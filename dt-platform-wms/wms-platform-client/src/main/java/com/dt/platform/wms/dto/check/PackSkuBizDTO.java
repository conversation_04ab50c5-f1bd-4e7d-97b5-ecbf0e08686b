package com.dt.platform.wms.dto.check;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/25 14:18
 */
@Data
public class PackSkuBizDTO implements Serializable {


    @ApiModelProperty(value = "行标识")
    private Integer id;

    @ApiModelProperty(value = "sku")
    private String skuCode;
    private String upcCode;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "待复核数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "计划商品数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "upcCode")
    private List<PackUpcBizDTO> upcCodeS;

    @ApiModelProperty(value = "商品耗材显示")
    private String hcDesc;

    @ApiModelProperty(value = "是否需要扫描冷链泡沫箱 true 扫描 false不扫描 --整包裹只需要扫描一个")
    private Boolean isScanColdChainBox;

    @ApiModelProperty(value = "是否需要扫描封口贴 true 扫描 false不扫描 每个商品都需要扫描")
    private Boolean isScanSealingTape;

    @ApiModelProperty(value = "是否扫描溯源码 true 开启 false 不开启")
    private Boolean isSource;
    //正则表达式
    private String isScanSealingTapeRule;

    @ApiModelProperty(value = "失效/过期日期")
    private Long expireDate;
    private String expireDateDesc;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    @ApiModelProperty(value = "商品展示小标记【冷链】【贵品】")
    private List<String> skuTagList;

    @ApiModelProperty(value = "提示语")
    private String showMesg;

    @ApiModelProperty(value = "是否开启SN管理 true 开启 false 不开启")
    private Boolean isSNMgmt;
}
