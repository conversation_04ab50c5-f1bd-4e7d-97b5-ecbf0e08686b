package com.dt.platform.wms.dto.replenish;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/9/22 17:25
 */
@Data
public class ReplenishTaskNewDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "包裹号")
    private String packageCode;

    @ApiModelProperty(value = "包裹数字")
    private Integer packageCount;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    @ApiModelProperty(value = "商品名")
    private String skuName;

    @ApiModelProperty(value = "商品质量属性(正残)")
    private String skuQuality;
    private String skuQualityName;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;
    private String inventoryTypeDesc;

    @ApiModelProperty(value = "差额数量")
    private BigDecimal defQty;

    @ApiModelProperty(value = "缺货数量")
    private BigDecimal diffQty;

    @ApiModelProperty(value = "需求数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDateStart;

    @ApiModelProperty(value = "失效日期")
    private Long expireDateEnd;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "禁售比对时间")
    private Long withdrawCompareDate;

    @ApiModelProperty(value = "是否淘天货主 true 淘天  false 非淘天")
    private Boolean taoTianCargo;
}
