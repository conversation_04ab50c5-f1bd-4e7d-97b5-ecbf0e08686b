package com.dt.platform.wms.param.replenish;

import com.dt.component.common.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 补货指引推荐请求参数
 * Created by nobody on 2021/1/27 9:55
 */
@Data
public class ReplenishRecommendParam extends BaseParam  implements java.io.Serializable  {

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品质量")
    private String skuQuality;

    @ApiModelProperty(value = "查询任务ID")
    private String taskId;

}