package com.dt.platform.wms.dto.rs;

import com.dt.component.common.dto.BaseDTO;
import com.dt.domain.bill.dto.rs.SalesReturnExtraDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 多货登记
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SalesReturnExtra对象", description = "多货登记")
public class SalesReturnExtraBizDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 销退单号,多个使用逗号分开
     */
    @ApiModelProperty(value = "销退单号,多个使用逗号分开")
    private String salesReturnOrderNo;

    /**
     * 多货单号
     */
    @ApiModelProperty(value = "多货单号")
    private String salesReturnExtraOrderNo;

    /**
     * 快递公司编码
     */
    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String carrierName;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    /**
     * 多货状态
     */
    @ApiModelProperty(value = "多货状态")
    private Integer status;

    /**
     * 是否破损 1 是 2 否
     */
    @ApiModelProperty(value = "是否破损 1 是 2 否")
    private Integer damage;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String receiveName;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String receiveTel;

    /**
     * 暂存位
     */
    @ApiModelProperty(value = "暂存位")
    private String locationCode;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String opBy;

    /**
     * 拓展字段
     */
    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

    /**
     * 登记时间
     */
    @ApiModelProperty(value = "登记时间")
    private Long handoverTime;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "原返单号")
    private String backNo;

    List<SalesReturnExtraDetailBizDTO> detailList;
    @ApiModelProperty(value = "正向运单号")
    private String originExpressNo;
    @ApiModelProperty(value = "正向货主")
    private String originCargoName;
    @ApiModelProperty(value = "外部订单号")
    private String originOrderNo;
    @ApiModelProperty(value = "正向发货仓")
    private String originWarehouseName;
    @ApiModelProperty("工作台")
    private String workbenchCode;
}