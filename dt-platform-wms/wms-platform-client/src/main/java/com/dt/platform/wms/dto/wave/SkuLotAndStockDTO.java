package com.dt.platform.wms.dto.wave;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/29 13:11
 */
@Data
public class SkuLotAndStockDTO implements Serializable {

    private Integer id;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "是否淘天货主")
    private Boolean cargoTaoTian;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "库位拣货序号")
    private Long pickSeq;

    @ApiModelProperty(value = "库区编码")
    private String tunnelCode;

    @ApiModelProperty(value = "库区类型")
    private String zoneType;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "库位类型")
    private String locationType;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    @ApiModelProperty(value = "待上架数量")
    private BigDecimal waitShelfQty;

    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用-待上架数量=可用数")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "失效/过期日期")
    private Long expireDate;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "入库关联单号(拓展单号)")
    private String externalLinkBillNo;

    @ApiModelProperty(value = "禁售日期")
    private Long withdrawDate;

    @ApiModelProperty(value = "是否禁售 true 禁售 false 非禁售")
    private Boolean isWithdraw;

    @ApiModelProperty(value = "是否效期商品 true 是 false 否")
    private Boolean isLifeCycle;

    @ApiModelProperty(value = "禁售时限(天) 如果保质期管理为是，必须大于0.[失效时期-禁售天数]")
    private Integer withdrawCycle;

    @ApiModelProperty(value = "是否预包商品【预包启用才会处理】 true 是 false 否")
    private Boolean isPre;

    @ApiModelProperty(value = "子商品对应预包商品的批次ID【预包才有值】")
    private String preSkuLotNo;
    private String preUpcCode;
    //预包的外部批次编码
    private String preExternalSkuLotNo = "";
    //预包的生产批次号
    private String preProductionNo = "";

    @ApiModelProperty(value = "预包商品对应子商品的配比【预包才有值】")
    private Map<String, BigDecimal> skuNeedMap;

    @ApiModelProperty(value = "库位是否标记【B单专用】")
    private Boolean isB2BPick;

    @ApiModelProperty(value = "特定场景排序,值越大,优先级越高")
    private Integer specialSort = 1;

    @ApiModelProperty(value = "销售单分配数量")
    private BigDecimal allocationQty;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "是否过期 true 过期 false 未过期")
    private Boolean overExpire;

    @ApiModelProperty(value = "预包前占使用")
    List<SkuLotAndStockDTO> preChildSkuStockList;

    @ApiModelProperty(value = "预包前占的包裹号")
    private String packageCode;

//    @ApiModelProperty(value = "是否CW货主")
//    private Boolean cwCargo;

    @ApiModelProperty(value = "CW货主分配")
    private BigDecimal allocatedQty;
}
