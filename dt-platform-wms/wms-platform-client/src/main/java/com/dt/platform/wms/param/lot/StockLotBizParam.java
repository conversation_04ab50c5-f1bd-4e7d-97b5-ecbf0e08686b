package com.dt.platform.wms.param.lot;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 批次库存
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "StockLot对象", description = "批次库存")
public class StockLotBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private List<String> skuCodeList;
    private List<String> upcCodeList;

    /**
     * 实物数量
     */
    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    /**
     * 冻结数量
     */
    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    /**
     * 占用数量
     */
    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    /**
     * 可用数量 实物库存-冻结-占用=可用数
     */
    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用=可用数")
    private BigDecimal availableQty;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
    private List<String> skuLotNoList;

    /**
     *
     */
    @ApiModelProperty(value = "库存商品类型")
    private String stockSkuType;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "生产批次号")
    private List<String> productionNoList;

    @ApiModelProperty(value = "外部批次ID")
    private List<String> externalSkuLotNoList;

    @ApiModelProperty(value = "失效开始")
    private Long expireDateStart;
    private Long expireDateEnd;

    @ApiModelProperty(value = "托盘号")
    private List<String> palletCodeList;

    @ApiModelProperty(value = "箱码")
    private List<String> boxCodeList;

    @ApiModelProperty(value = "效期码")
    private String validityCode;
    private List<String> validityCodeList;
}