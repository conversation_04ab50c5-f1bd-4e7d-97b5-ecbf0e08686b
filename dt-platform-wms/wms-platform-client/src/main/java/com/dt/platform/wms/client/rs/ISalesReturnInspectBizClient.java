package com.dt.platform.wms.client.rs;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.rs.*;
import com.dt.platform.wms.param.rs.SalesReturnInspectBizParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

public interface ISalesReturnInspectBizClient {
    /**
     * @param returnInspectBizParam
     * @return com.dt.component.common.result.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.dt.platform.wms.dto.rs.SalesReturnInspectBizDTO>>
     * <AUTHOR>
     * @describe: 分页
     * @date 2024/1/26 10:47
     */
    Result<Page<SalesReturnInspectBizDTO>> getPage(SalesReturnInspectBizParam returnInspectBizParam);

    /**
     * @param returnInspectBizParam
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.rs.SalesReturnInspectBizDTO>
     * <AUTHOR>
     * @describe: 详情
     * @date 2024/1/26 13:18
     */
    Result<SalesReturnInspectBizDTO> getDetail(SalesReturnInspectBizParam returnInspectBizParam);

    /**
     * @param returnInspectBizParam
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 扫描月台
     * @date 2024/1/26 13:23
     */
    Result<Boolean> scanWorkbenchByInspect(SalesReturnInspectBizParam returnInspectBizParam);

    /**
     * @param returnInspectBizParam
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 扫描容器
     * @date 2024/1/26 13:23
     */

    Result<String> scanContByInspect(SalesReturnInspectBizParam returnInspectBizParam);

    /**
     * @param returnInspectBizParam
     * @return com.dt.component.common.result.Result<java.lang.Object>
     * <AUTHOR>
     * @describe: 扫描运单
     * @date 2024/1/26 13:23
     */
    Result<List<SalesReturnInspectScanExpressBizDTO>> scanExpressByInspect(SalesReturnInspectBizParam returnInspectBizParam);

    Result<SalesReturnInspectResultDTO> completeExpressV2(SalesReturnInspectBizParam returnInspectBizParam);

    /**
     * @param returnInspectBizParam
     * @return com.dt.component.common.result.Result<java.lang.Object>
     * <AUTHOR>
     * @describe: 完成运单
     * @date 2024/1/26 13:23
     */
    Result<SalesReturnInspectResultDTO> completeExpress(SalesReturnInspectBizParam returnInspectBizParam);

    @ApiOperation("判断是否少件")
    Result<String> checkReceiveLess(SalesReturnInspectBizParam returnInspectBizParam);

    /**
     * @param returnInspectBizParam
     * @return com.dt.component.common.result.Result<java.lang.Object>
     * <AUTHOR>
     * @describe: 完成容器
     * @date 2024/1/26 13:23
     */
    Result<Boolean> completeCont(SalesReturnInspectBizParam returnInspectBizParam);
    /**
     * @param returnInspectBizParam
     * @return com.dt.component.common.result.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.dt.platform.wms.dto.rs.SalesReturnInspectDetailBizDTO>>
     * <AUTHOR>
     * @describe: 质检明细分页
     * @date 2024/1/26 13:28
     */
    Result<Page<SalesReturnInspectDetailBizDTO>> getPageToDetail(SalesReturnInspectBizParam returnInspectBizParam);
    /**
     * @param returnInspectBizParam
     * @return com.dt.component.common.result.Result<java.util.List<com.dt.platform.wms.dto.rs.SalesReturnInspectSalesOrderSkuBizDTO>>
     * <AUTHOR>
     * @describe:
     * @date 2024/1/27 12:19
     */
    Result<SalesReturnInspectSalesOrderBizDTO> getSalesOrderInformation(SalesReturnInspectBizParam returnInspectBizParam);

    @ApiOperation("拒收当前运单")
    Result<String> rejectCurrentExpressNo(SalesReturnInspectBizParam returnInspectBizParam);

    Result<SalesReturnInspectCheckBizDto> checkSalesReturnBefore(SalesReturnInspectBizParam returnInspectBizParam);
}
