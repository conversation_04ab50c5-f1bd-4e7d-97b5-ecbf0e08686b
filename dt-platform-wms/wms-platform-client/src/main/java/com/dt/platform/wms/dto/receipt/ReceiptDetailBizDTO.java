package com.dt.platform.wms.dto.receipt;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/9/21 16:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ReceiptDetail对象", description="收货作业批次容器明细")
public class ReceiptDetailBizDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收货作业批次")
    private String recId;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;
    private String warehouseName;

    @ApiModelProperty(value = "拓展行号")
    private String extNo;

    @ApiModelProperty(value = "明细行号")
    private Long pUid;

    @ApiModelProperty(value = "行号")
    private String lineSeq;

    @ApiModelProperty(value = "到货通知单号")
    private String asnId;

    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    @ApiModelProperty(value = "容器号")
    private String contCode;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "总体积(cm³)")
    private BigDecimal volume;

    @ApiModelProperty(value = "总毛重(KG)")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "总净重(KG)")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "批次ID 不允许为空，默认标准规则(批次规则档案)")
    private String skuLotNo;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;
    private String receiveDateFormat;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;
    private String manufDateFormat;

    @ApiModelProperty(value = "生产日期")
    private Long expireDate;
    private String expireDateFormat;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "批次属性(单据号)")
    private String externalLinkBillNo;

    @ApiModelProperty(value = "禁售日期")
    private Long withdrawDate;
    private String withdrawDateFormat;

    @ApiModelProperty(value = "收货库位")
    private String locationCode;

    @ApiModelProperty(value = "收货上架目标库位")
    private String targetLocationCode;

    @ApiModelProperty(value = "状态码")
    private String status;
    private String statusName;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "长宽高 默认1位")
    private Integer lengthWidthHeightFormat;

    @ApiModelProperty(value = "体积 默认3位*")
    private Integer volumeFormat;

    @ApiModelProperty(value = "高 默认3位")
    private Integer weightFormat;

    @ApiModelProperty(value = "数量 默认0位")
    private Integer numberFormat;

    @ApiModelProperty(value = "外部批次编码")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;
}