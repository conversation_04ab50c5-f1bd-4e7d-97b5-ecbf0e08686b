package com.dt.platform.wms.dto.adjust;

import com.dt.platform.wms.dto.rec.CodeNameControlBizDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class AdjustCreateSkuLotBizDTO implements Serializable {

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    @ApiModelProperty(value = "商品名")
    private String skuName;

    @ApiModelProperty(value = "是否开启效期管理 true 开启 false 不开启")
    private Boolean isLifeMgt;

    @ApiModelProperty(value = "保质期天数")
    private Integer lifeCycle;

    @ApiModelProperty(value = "保质期禁收天数")
    private Integer rejectCycle;

    @ApiModelProperty(value = "禁售天数")
    private Integer withdrawCycle;

    @ApiModelProperty(value = "批次属性规则")
    List<CodeNameControlBizDTO> propList;

    @ApiModelProperty(value = "CW货主")
    private Boolean cwCargo;
}
