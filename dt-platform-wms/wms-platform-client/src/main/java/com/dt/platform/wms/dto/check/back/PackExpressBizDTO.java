package com.dt.platform.wms.dto.check.back;

import com.dt.component.common.vo.IdNameVO;
import com.dt.platform.wms.dto.check.PackCheckPackMaterialBizDTO;
import com.dt.platform.wms.dto.check.PackSkuBizDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/25 14:15
 */
@Data
public class PackExpressBizDTO implements Serializable {

    @ApiModelProperty(value = "拣选单号")
    private String pickCode;

    @ApiModelProperty(value = "计划总数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "已复核总数量")
    private BigDecimal checkQty;

    @ApiModelProperty(value = "已复核包裹数量")
    private Integer checkPackNum;

    @ApiModelProperty(value = "计划总包裹数量")
    private Integer expPackNum;

    @ApiModelProperty(value = "出库单号")
    private String  shipmentOrderCode;

    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;

    @ApiModelProperty(value = "订单备注")
    private String remark;

    @ApiModelProperty(value = "是否扫描溯源码")
    private Boolean checkSourceCode;

    @ApiModelProperty(value = "展示前端防伪标签数据 前端展示F9")
    private Boolean showScanSealingTapeData;

    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    @ApiModelProperty(value = "篮号")
    private String basketNo;

    @ApiModelProperty(value = "运单号")
    private String expressNo;


    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;


    @ApiModelProperty(value = "淘天标识")
    private Boolean taotainTag;


    @ApiModelProperty(value = "包裹号")
    private String packageCode;

    @ApiModelProperty(value = "当前包裹需要复核数量-(即商品明细待复核的商品数量总和)")
    private BigDecimal waitQty;

    @ApiModelProperty(value = "当前包裹复合数")
    private BigDecimal expressCheckSkuQty;

    @ApiModelProperty(value = "商品明细")
    private List<PackSkuBizDTO> packSkuVOS;

    @ApiModelProperty(value = "包材")
    PackCheckPackMaterialBizDTO packMaterialVO;

    @ApiModelProperty(value = "复核工作台展示")
    private String deliveryRequirement;

    @ApiModelProperty(value = "溯源码规则")
    private String sourceCodeNewRule;
    private String sourceCodeNewMsg;

    @ApiModelProperty(value = "复核赠品展示")
    List<IdNameVO> showGiftList;

    @ApiModelProperty(value = "复核包裹维度耗材展示")
    List<IdNameVO> showPackMaterialList;

}
