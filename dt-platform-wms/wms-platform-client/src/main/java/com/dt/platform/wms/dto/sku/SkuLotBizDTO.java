package com.dt.platform.wms.dto.sku;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 商品档案
 * </p>
 *
 * <AUTHOR> x<PERSON>
 * @since 2020-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SKULOT", description="商品批次")
public class SkuLotBizDTO extends BaseDTO  implements java.io.Serializable  {

    @ApiModelProperty(value = "批次ID")
    private String code;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;
    private String receiveDateFormat;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;
    private String manufDateFormat;

    @ApiModelProperty(value = "失效/过期日期")
    private Long expireDate;
    private String expireDateFormat;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "禁售日期")
    private Long withdrawDate;
    private String withdrawDateFormat;

    @ApiModelProperty(value = "入库关联单号(拓展单号)")
    private String externalLinkBillNo;

    @ApiModelProperty(value = "关联批次号")
    private String linkSkuLotNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

}