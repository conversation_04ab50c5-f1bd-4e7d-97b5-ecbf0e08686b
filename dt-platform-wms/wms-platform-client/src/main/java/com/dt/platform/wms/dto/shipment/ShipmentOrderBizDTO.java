package com.dt.platform.wms.dto.shipment;

import java.math.BigDecimal;

import com.dt.component.common.dto.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "出库单", description = "出库单")
public class ShipmentOrderBizDTO extends BaseDTO  implements java.io.Serializable  {

    @ApiModelProperty(value = "相当于主键id 用于es转换")
    private Long key;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "全局单号-在但丁云系统中唯一单号")
    private String globalNo;

    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;

    @ApiModelProperty(value = "交易单号")
    private String tradeNo;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "单据状态")
    private String status;

    @ApiModelProperty(value = "单据来源")
    private String fromSource;

    @ApiModelProperty(value = "单据类型")
    private String orderType;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "商品正/次品属性")
    private String skuQuality;
    @ApiModelProperty(value = "销售平台")
    private String salePlatform;

    @ApiModelProperty(value = "销售店铺ID")
    private String saleShopId;

    @ApiModelProperty(value = "销售店铺")
    private String saleShop;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    private String carrierName;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "包裹类型")
    private String packageStruct;

    @ApiModelProperty(value = "预计出库时间")
    private Long expOutStockDate;

    @ApiModelProperty(value = "交易下单时间")
    private Long placeTradeOrderDate;

    @ApiModelProperty(value = "付款时间")
    private Long payDate;

    @ApiModelProperty(value = "拣货开始时间")
    private Long pickSkuDate;

    @ApiModelProperty(value = "拣货完成时间")
    private Long pickCompleteSkuDate;

    @ApiModelProperty(value = "复核开始时间")
    private Long checkStartDate;

    @ApiModelProperty(value = "复核完成时间")
    private Long checkCompleteDate;

    @ApiModelProperty(value = "首包裹出库时间")
    private Long firstPackOutStockDate;

    @ApiModelProperty(value = "出库时间")
    private Long outStockDate;

    @ApiModelProperty(value = "拦截取消时间")
    private Long interceptCancelDate;

    @ApiModelProperty(value = "商品品种数")
    private Integer skuTypeQty;

    @ApiModelProperty(value = "订单商品数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "出库商品数量")
    private BigDecimal outSkuQty;

    @ApiModelProperty(value = "包裹数量")
    private Integer packageQty;

    @ApiModelProperty(value = "出库包裹数")
    private Integer outPackageQty;

    @ApiModelProperty(value = "理论重量")
    private BigDecimal weight;

    @ApiModelProperty(value = "收货人姓名")
    private String receiverMan;

    @ApiModelProperty(value = "联系电话")
    private String receiverTel;

    @ApiModelProperty(value = "收货国家")
    private String receiverCountry;

    @ApiModelProperty(value = "收货省份")
    private String receiverProv;
    @ApiModelProperty(value = "收货市")
    private String receiverCity;
    @ApiModelProperty(value = "收货区")
    private String receiverArea;

    private String receiverProvName;
    private String receiverCityName;
    private String receiverAreaName;
    private String receiverStreetName;

    private String senderProvName;
    private String senderCityName;
    private String senderAreaName;

    @ApiModelProperty(value = "邮政编码")
    private String receiverZipcode;
    @ApiModelProperty(value = "收获地址")
    private String receiverAddress;
    @ApiModelProperty(value = "回传通知状态")
    private Integer notifyStatus;
    @ApiModelProperty(value = "回传通知时间")
    private Long notifyTime;
    @ApiModelProperty(value = "发货人姓名")
    private String senderMan;
    @ApiModelProperty(value = "发货联系电话")
    private String senderTel;
    @ApiModelProperty(value = "发货国家")
    private String senderCountry;
    @ApiModelProperty(value = "发货省份")
    private String senderProv;
    @ApiModelProperty(value = "发货市")
    private String senderCity;
    @ApiModelProperty(value = "发货区")
    private String senderArea;
    @ApiModelProperty(value = "发货邮政编码")
    private String senderZipcode;
    @ApiModelProperty(value = "发货地址")
    private String senderAddress;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "数量 默认0位")
    private Integer numberFormat;
    @ApiModelProperty(value = "汇单时间")
    private Long collectTime;
    @ApiModelProperty(value = "出库明细")
    private java.util.List<ShipmentOrderDetailBizDTO> listShipmentOrderDetailBizDTO;
    @ApiModelProperty(value = "出库日志")
    private java.util.List<ShipmentOrderLogBizDTO> listShipmentOrderLogBizDTO;
    @ApiModelProperty(value = "更新时间")
    private String updateBy;
    @ApiModelProperty(value = "加密方式 0 - 未加密  1 - 奇门隐私保护")
    private Integer secureType;
    @ApiModelProperty(value = "奇门oa id 解密标识")
    private String oaId;
    @ApiModelProperty(value = "包材")
    private String materialUpcCode;

    @ApiModelProperty(value = "预售类型")
    private Integer preSaleType;

    @ApiModelProperty(value = "预处理状态")
    private String pretreatmentStatus;

    @ApiModelProperty(value = "发货超时时间")
    private Long expShipTime;

    @ApiModelProperty(value = "订单标记")
    private Integer orderTag;

    @ApiModelProperty(value = "拓传json")
    private String extraJson;

    @ApiModelProperty(value = "收货公司")
    private String receivingCompany;

    /**
     * 清关状态
     */
    private String customsClearanceStatus;
    /**
     * 清关类型
     */
    private String customsClearanceType;

    @ApiModelProperty(value = "订单显示标记")
    private String mergeCustomsClearanceTip;

    @ApiModelProperty(value = "是否合并清关")
    private String mergeCustomsClearanceOpen;

    @ApiModelProperty(value = "合并清关包裹数量")
    private Integer mergeCustomsClearanceNum;

    @ApiModelProperty(value = "合并清关包裹")
    private String mergeCustomsClearanceExpressNoStr;
}