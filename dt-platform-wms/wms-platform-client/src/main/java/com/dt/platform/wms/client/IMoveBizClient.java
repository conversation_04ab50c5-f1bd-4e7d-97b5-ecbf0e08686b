package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.move.MoveBizDTO;
import com.dt.platform.wms.dto.stock.StockLocationWithLotBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.move.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

public interface IMoveBizClient {


//    /**
//     * 新增
//     * @param param
//     * @return
//     */
//    Result<Boolean> create(MoveBizParam param);
//    /**
//     * 修改
//     * @param param
//     * @return
//     */
//    Result<Boolean> modify(MoveBizParam param);

    /**
     * 修改上架方式
     * @param param
     * @return
     */
    Result<Boolean> modifyOpType(MoveOpTypeParam param);

    /**
     * 分页列表
     * @param param
     * @return
     */
    Result<Page<MoveBizDTO>> getPage(MoveBizParam param);

    /**
     * 获取详情
     * @param param
     * @return
     */
    Result<MoveBizDTO> getDetail(MoveBizParam param);

    /**
     * 完成
     */
    Result<Boolean> complete(CodeParam param);

    /**
     * 完成明细
     * @param param
     */
    Result<Boolean> completeRetainDetail(CodeParam param);

    /**
     * 扫描来源库位信息
     * @param param
     * @return
     */
    Result<List<StockLocationWithLotBizDTO>> scanOriginLocation(ScanOriginLocationParam param);

    /**
     * 扫描来源库位信息
     * @param param
     * @return
     */
    @Deprecated
    Result<StockLocationWithLotBizDTO> scanUpcCode(ScanUpcCodeParam param);

    /**
     * 扫描来源库位商品信息
     * @param param
     * @return
     */
    Result<List<StockLocationWithLotBizDTO>> scanUpcCodeNew(ScanUpcCodeParam param);


    /**
     * 扫描来源库位信息
     * @param param
     * @return
     */
    Result<Boolean> scanTargetLocation(ScanTargetLocationParam param);

    /**
     * 新增移位单
     * @param param
     * @return
     */
    Result<Boolean> moveAdd(MoveAddParam param);

    /**
     * 修改移位单
     * @param param
     * @return
     */
    Result<Boolean> moveUpdate(MoveUpdateParam param);

    /**
     * 整库位移位
     * @param param
     * @return
     */
    Result<Boolean> moveWholeLocation(MoveWholeLocationParam param);
    
    @ApiOperation("移位校验")
    Result<Boolean> moveCheck(MoveBizParam param);
}
