package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.pkg.PackAnalysisBillDTO;
import com.dt.domain.bill.param.pkg.PackAnalysisBillParam;
import com.dt.platform.wms.dto.pkg.PackageBizDTO;
import com.dt.platform.wms.dto.pkg.PackageDetailBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.pkg.*;

import java.util.List;
import java.util.Map;

public interface IPackageBizClient {

    /**
     * 功能描述:  日汇总包裹初始化
     * 创建时间:  2021/3/16 4:05 下午
     *
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.platform.wms.dto.pkg.PackageBizDTO>>
     * <AUTHOR>
     */
    Result<List<PackageBizDTO>> initCount();

    /**
     * @param searchPackageParam
     * @return com.dt.component.common.result.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.dt.platform.wms.dto.pkg.PackageBizDTO>>
     * <AUTHOR>
     * @describe: 分页查询
     * @date 2022/8/19 17:55
     */
    Result<Page<PackageBizDTO>> queryPage(PackageBizParam searchPackageParam);

    /**
     * 查询包裹明细
     *
     * @param searchPackageParam
     * @return
     */
    Result<PackageBizDTO> getDetail(PackageBizParam searchPackageParam);

    Result<List<PackageDetailBizDTO>> getPackageDetailListByCode(CodeParam param);

    /**
     * 同步包裹信息到WCS
     *
     * @param packageDTO
     */
    void wcsSync(PackageDTO packageDTO);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.param.pkg.FileUploadApplyResponse>
     * <AUTHOR>
     * @describe: 视频上传文件申请
     * @date 2024/4/29 16:22
     */
    Result<FileUploadApplyResponse> fileUploadApply(FileUploadParam param);

    /**
     * @param packageVideoReportBizParam
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.param.pkg.FileUploadApplyResponse>
     * <AUTHOR>
     * @describe: 视频地址上传
     * @date 2024/4/29 16:22
     */
    Result videoReport(PackageVideoReportBizParam packageVideoReportBizParam);

    /**
     * @param warehouseCode
     * @param param
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2024/4/24 11:37
     */
    void packageNoVideoWarningJob(String warehouseCode, String param);
    /**
     * @param warehouseCode
     * @param param
     * @return void
     * <AUTHOR>
     * @describe: 禁售比对发货预警
     * @date 2024/8/7 13:23
     */
    void shipWithdrawCompareDateWarn(String warehouseCode, String param);
    /**
     * @param packageBizParam
     * @return com.dt.component.common.result.Result<java.util.Map<java.lang.String,java.lang.Long>>
     * <AUTHOR>
     * @describe: 获取包裹打印次数
     * @date 2025/3/24 13:20
     */
    Result<Map<String,Long>> getPackPrintNum(PackageBizParam packageBizParam);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @describe:
     * @date 2025/4/3 9:32
     */
    Result<String> videoReportBySelf(PackageVideoReportBySelfBizParam param);

    /**
     *  包裹分析
     * @param param
     * @return
     */
    Result<Page<PackAnalysisBillDTO>> getPackAnalysisPage(PackAnalysisBillParam param);
}
