package com.dt.platform.wms.param.shipment;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "出库单", description = "出库单")
public class ShipmentOrderBizParam extends BaseSearchParam  implements java.io.Serializable  {

    @ApiModelProperty(value = "出库单号")
    private List<String> shipmentOrderCodeList;
    private String shipmentOrderCode;

    @ApiModelProperty(value = "指定批次修改")
    private List<AssignSkuLotNoParam> assignSkuLotNoParamList;

    @ApiModelProperty(value = "客户单号")
    private List<String> poNoList;

    @ApiModelProperty(value = "客户单号")
    private List<String> soNoList;
    @ApiModelProperty(value = "拣选单号")
    private List<String> pickCodeList;
    @ApiModelProperty(value = "包裹号")
    private List<String> packageCodeList;

    @ApiModelProperty(value = "包裹类型")
    private List<String> packageStructList;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "货主")
    private List<String> cargoCodeList;

    @ApiModelProperty(value = "单据状态")
    private String status;
    @ApiModelProperty(value = "单据状态")
    private List<String> statusList;

    @ApiModelProperty(value = "理货报告单据状态")
    private List<String> tallyStatusList;

    @ApiModelProperty(value = "单据类型")
    private String orderType;

    @ApiModelProperty(value = "全局单号-在但丁云系统中唯一单号")
    private String globalNo;
    private List<String> globalNoList;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    @ApiModelProperty(value = "快递单号")
    private List<String> expressNoList;

    @ApiModelProperty(value = "订单品种类型")
    private List<String> orderSkuType;

    @ApiModelProperty(value = "商品代码")
    private List<String> skuCodeList;
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private List<String> upcCodeList;

    @ApiModelProperty(value = "交易单号")
    private List<String> tradeNoList;

    @ApiModelProperty(value = "销售平台")
    private String salePlatform;

    @ApiModelProperty(value = "销售店铺")
    private String saleShop;

    @ApiModelProperty(value = "预计出库时间(开始)")
    private Long startExpOutStockDate;

    @ApiModelProperty(value = "预计出库时间(结束)")
    private Long endExpOutStockDate;

    @ApiModelProperty(value = "付款时间(开始)")
    private Long startPayTime;

    @ApiModelProperty(value = "付款时间(结束)")
    private Long endPayTime;

    @ApiModelProperty(value = "付款时间(开始)")
    private Long startTradeOrderDate;

    @ApiModelProperty(value = "付款时间(结束)")
    private Long endTradeOrderDate;

    @ApiModelProperty(value = "品种数(开始)")
    private Integer startSkuTypeCount;

    @ApiModelProperty(value = "品种数(结束)")
    private Integer endSkuTypeCount;

    @ApiModelProperty(value = "商品数(开始)")
    private Integer startSkuCount;

    @ApiModelProperty(value = "商品数(结束)")
    private Integer endSkuCount;

    @ApiModelProperty(value = "出库时间(开始)")
    private Long startOutStockDate;

    @ApiModelProperty(value = "出库时间(结束)")
    private Long endOutStockDate;

    @ApiModelProperty(value = "单据来源")
    private String fromSource;

    @ApiModelProperty(value = "收货人姓名")
    private String receiverMan;
    @ApiModelProperty(value = "联系电话")
    private String receiverTel;
    @ApiModelProperty(value = "收货省份")
    private String receiverProv;
    @ApiModelProperty(value = "收货市")
    private String receiverCity;
    @ApiModelProperty(value = "收货区")
    private String receiverArea;

    //省
    private List<String> receiverProvList;
    //市
    private List<String> receiverCityList;
    //区
    private List<String> receiverAreaList;

    @ApiModelProperty(value = "包材")
    private String materialUpcCode;

    @ApiModelProperty(value = "回传通知状态")
    private Integer notifyStatus;

    @ApiModelProperty(value = "订单标记")
    private Integer orderTag;
    private List<Integer> orderTagList;

    private List<Integer> orderTagNoContainList;

    @ApiModelProperty(value = "预售类型")
    private Integer preSaleType;

    @ApiModelProperty(value = "发货超时时间")
    private Long expShipTimeStart;
    private Long expShipTimeEnd;

    @ApiModelProperty(value = "理论重量")
    private BigDecimal weightStart;
    private BigDecimal weightEnd;
    /**
     * 清关状态
     */
    private String customsClearanceStatus;
    private List<String> customsClearanceStatusList;
    /**
     * 清关类型
     */
    private String customsClearanceType;

    @ApiModelProperty(value = "复核完成查询条件")
    private Long checkCompleteDateStart;
    private Long checkCompleteDateEnd;
    @ApiModelProperty(value = "拣选完成查询条件")
    private Long pickCompleteSkuDateStart;
    private Long pickCompleteSkuDateEnd;

    @ApiModelProperty(value = "签收时间")
    private Long signNotifyTime;

    private String packageStruct;

}