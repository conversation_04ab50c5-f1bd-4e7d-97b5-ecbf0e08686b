package com.dt.platform.wms.param.check;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/26 16:44
 */
@Data
public class PackSubmitParam implements Serializable {

    @ApiModelProperty(value = "拣选单号")
    @NotEmpty(message = "拣选单号不能为空")
    private String pickCode;

    @ApiModelProperty(value = "工作台号编码")
    @NotEmpty(message = "工作台号不能为空")
    private String workbenchCode;

    @ApiModelProperty(value = "包裹单号")
    @NotEmpty(message = "包裹单号不能为空")
    private String packageCode;

    @ApiModelProperty(value = "运单号")
    @NotEmpty(message = "运单号不能为空")
    private String expressNo;

    @ApiModelProperty(value = "包材编码")
    private String packageMaterialCode;

    @ApiModelProperty(value = "冷链泡沫箱条码")
    private String hcUpcCode;

    @ApiModelProperty(value = "拣选单复核的标识 = pick")
    private String flag;

    @ApiModelProperty(value = "拣选单是否批量复核（只针对秒杀单） batch")
    private String isBatchCheck;
//
//    @ApiModelProperty(value = "溯源码")
//    private List<PackSourceCodeParam> sourceCodeParamList;

    @ApiModelProperty(value = "商品数据")
    private List<PackSubExpNoParam> skuBackParamList;

    @ApiModelProperty(value = "封口贴")
    //溯源码 封口贴 防伪扣 易撕贴 TODO 2024-03-19 都改成商品级别
    private List<SealingTapeCodeParam> sealingTapeParamList;

    @ApiModelProperty(value = "SN码")
    private List<SNCodeParam> sNCodeParamList;
}
