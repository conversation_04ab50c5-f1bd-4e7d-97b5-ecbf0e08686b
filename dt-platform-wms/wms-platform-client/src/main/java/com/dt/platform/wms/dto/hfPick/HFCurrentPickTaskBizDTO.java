package com.dt.platform.wms.dto.hfPick;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class HFCurrentPickTaskBizDTO implements Serializable {

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "拣选单号")
    private String pickCode;

    @ApiModelProperty(value = "平台")
    private String salePlatform;
    private String salePlatformName;

    @ApiModelProperty(value = "承运商信息")
    private String carrierCode;
    private String carrierName;

    @ApiModelProperty(value = "库区")
    private String zoneCode;
    private String zoneName;

    @ApiModelProperty(value = "容器")
    private String contCode;

    @ApiModelProperty(value = "拣货方式")
    private String pickMethod;

    @ApiModelProperty(value = "拣货方式")
    private String locationCode;

    @ApiModelProperty(value = "包裹数量")
    private Long packageQty;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

}
