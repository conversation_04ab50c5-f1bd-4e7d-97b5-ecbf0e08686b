<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dt</groupId>
        <artifactId>dt-platform-wms</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.dt</groupId>
    <artifactId>wms-platform-integration</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>wms-platform-integration</name>
    <description>服务聚合</description>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.danding.mercury</groupId>
            <artifactId>mercury-rpc-client</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-mercury-rpc-client</artifactId>
            <version>3.2.7-RELEASE</version>
        </dependency>
        <dependency>
            <artifactId>ares-inventory-rpc-client</artifactId>
            <groupId>com.danding</groupId>
            <version>${ares.version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-order-rpc-client</artifactId>
            <version>${ares.version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-rpc-trade-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-config-rpc-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-rpc-configuration-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-goods-rpc-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-base-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-bill-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-stock-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-uid</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Rocket Mq -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-statistics-client</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.dt</groupId>-->
<!--            <artifactId>tms-rpc-waybill</artifactId>-->
<!--            <version>2.1.9-RELEASE</version>-->
<!--        </dependency>-->


        <dependency>
            <groupId>com.danding.x.tms</groupId>
            <artifactId>danding-x-tms-rpc-api</artifactId>
            <version>2.1.5.5-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-out-rpc</artifactId>
            <version>3.1.7-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding.hms</groupId>
            <artifactId>danding-hms-server-rpc-api</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-goods-center-rpc-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>oms-explorer-rpc-client</artifactId>
            <version>1.2.6-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>oms-order-rpc-client</artifactId>
            <version>1.2.6-RELEASE</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.dt</groupId>-->
<!--            <artifactId>dt-component-imm</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>danding-snapshots</id>
            <name>danding-snapshots</name>
            <url>http://mvn.yang800.cn/repository/maven-snapshots/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
            <repository>
                <id>danding</id>
                <name>danding</name>
                <url>http://mvn.yang800.cn/repository/maven-public/</url>
                <releases>
                    <enabled>true</enabled>
                </releases>
                <snapshots>
                    <enabled>true</enabled>
                    <updatePolicy>always</updatePolicy>
                </snapshots>
            </repository>

    </repositories>

</project>
