package com.dt.platform.wms.integration.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.domain.base.client.ISkuLotClient;
import com.dt.domain.bill.client.IAllocationOrderClient;
import com.dt.domain.bill.client.IPackageClient;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.pkg.PackAnalysisBillDTO;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.pkg.PackAnalysisBillParam;
import com.dt.platform.wms.integration.IRemotePackageClient;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class RemotePackageClient implements IRemotePackageClient {

    @DubboReference
    private IPackageClient packageClient;

    @DubboReference
    private ISkuLotClient skuLotClient;

    @DubboReference
    private IAllocationOrderClient allocationOrderClient;


    @Override
    public List<PackageDTO> initCount() {
        return packageClient.initCount().getData();
    }


    @Override
    public Boolean save(PackageParam param) {
        return packageClient.save(param).getData();
    }

    @Override
    public Boolean saveOrUpdate(PackageParam param) {
        return packageClient.saveOrUpdate(param).getData();
    }

    @Override
    public Boolean modify(PackageParam param) {
        return packageClient.modify(param).getData();
    }

    @Override
    public List<PackageDTO> getPackageCodeOrExpressNo(PackageParam param) {
        return packageClient.getPackageCodeOrExpressNo(param).getData();
    }

    @Override
    public Boolean modifyBatch(PackageBatchParam param) {
        return packageClient.modifyBatch(param).getData();
    }

    @Override
    public Boolean modifyBatch(List<PackageDTO> packageDTOList) {
        return packageClient.modifyBatch(packageDTOList).getData();
    }

    @Override
    public Boolean checkExits(PackageParam param) {
        return packageClient.checkExits(param).getData();
    }

    @Override
    public PackageDTO get(PackageParam param) {
        return packageClient.get(param).getData();
    }

    @Override
    public List<PackageDTO> getList(PackageParam param) {
        return packageClient.getList(param).getData();
    }

    @Override
    public List<PackageDTO> listByShipment(String shipmentOrderCode) {
        if (StrUtil.isBlank(shipmentOrderCode)) {
            return ListUtil.empty();
        }
        PackageParam packageParam = new PackageParam();
        packageParam.setShipmentOrderCode(shipmentOrderCode);
        return packageClient.getList(packageParam).getData();
    }

    @Override
    public Page<PackageDTO> getExportPage(PackageParam param) {
        return packageClient.getExportPage(param).getData();
    }

    @Override
    public Page<PackageDTO> getPage(PackageParam param) {
        return packageClient.getPage(param).getData();
    }

    @Override
    public PackageDTO getDetail(PackageParam param) {
        return packageClient.getDetail(param).getData();
    }

    @Override
    public Page<PackageDTO> queryPage(PackageParam searchPackageParam) {
        return packageClient.queryPage(searchPackageParam).getData();
    }

    @Override
    public PackageDTO getDetailAndEntityDetail(PackageParam searchPackageParam) {
        return packageClient.getDetailAndEntityDetail(searchPackageParam).getData();
    }

    @Override
    public List<CollectWaveDTO> getCollectWaveList(CollectWaveBillParam param) {
        return packageClient.getCollectWaveList(param).getData();
    }

//    @Override
//    public List<PackageDetailDTO> getCollectWavePackageDetailList(PackageDetailParam param) {
//        return packageClient.getCollectWavePackageDetailList(param).getData();
//    }
//
//    @Override
//    public List<PackageDTO> getCollectWaveCommitList(CollectWaveBillParam param) {
//        return packageClient.getCollectWaveCommitList(param).getData();
//    }

    @Override
    public List<PackageDetailDTO> getPackageDetailListByCode(String packageCode) {
        PackageParam param = new PackageParam();
        param.setPackageCode(packageCode);
        return packageClient.getPackageDetailListByCode(param).getData();
    }

    @Override
    public PackageDTO getPackageByCode(String packageCode) {
        PackageParam param = new PackageParam();
        param.setPackageCode(packageCode);
        return packageClient.getPackageByCode(param).getData();
    }

    @Override
    public List<PackageDTO> getCollectWaveList(PackageParam param) {
        return packageClient.getCollectWaveList(param).getData();
    }

    @Override
    public Boolean updatePackage(PackageDTO packageDTO) {
        return packageClient.updatePackageDTO(packageDTO).getData();
    }

    @Override
    public Boolean updatePackageDetailList(List<PackageDetailDTO> packageDetailDTOS) {
        return packageClient.updatePackageDetailDTOS(packageDetailDTOS).getData();
    }

    @Override
    public Boolean submitUpdateCollect(List<PackageDTO> packageDTOS) {
        return packageClient.submitUpdateCollect(packageDTOS).getData();
    }

    @Override
    public List<CollectWaveDTO> querySpikeCollectWave(CollectWaveBillParam param) {
        return packageClient.querySpikeCollectWave(param).getData();
    }


    @Override
    public List<PackageDetailDTO> getCollectWaveToPick(PackageDetailParam param) {
        return packageClient.getCollectWaveToPick(param).getData();
    }

    @Override
    public Boolean updateCollectPackage(List<PackageDTO> packageDTOS) {
        return packageClient.updateCollectPackage(packageDTOS).getData();
    }

    @Override
    public List<PackageDTO> queryList(PackageParam packageParam) {
        return packageClient.queryList(packageParam).getData();
    }

    @Override
    public List<PackageDetailDTO> getPackageDetailListByListCode(PackageDetailParam param) {
        return packageClient.getPackageDetailListByListCode(param).getData();
    }

    @Override
    public List<PackageDTO> getAllocationWave(PackageParam param) {
        return packageClient.getAllocationWave(param).getData();
    }

    @Override
    public Boolean savePackageLog(PackageLogDTO packageLogDTO) {
        return packageClient.savePackageLog(packageLogDTO).getData();
    }

    @Override
    public Boolean savePackLogList(List<PackageLogDTO> packageLogDTOList) {
        return packageClient.savePackLogList(packageLogDTOList).getData();
    }

    @Override
    public List<PackageDTO> getListByPage(PackageParam packageParam) {
        List<PackageDTO> packageDTOList = new ArrayList<>();
        packageParam.setSize(2000);
        Page<PackageDTO> locationClientPage = packageClient.getPage(packageParam).getData();
        for (int i = 1; i <= locationClientPage.getPages(); i++) {
            packageParam.setCurrent(i);
            packageParam.setSize(2000);
            Page<PackageDTO> clientPage = packageClient.getPage(packageParam).getData();
            packageDTOList.addAll(clientPage.getRecords());
        }
        return packageDTOList;
    }

    @Override
    public List<PackageDTO> getListByPageNew(PackageParam packageParam) {
        List<PackageDTO> packageDTOList = new ArrayList<>();
        packageParam.setSize(2000);
        Page<PackageDTO> locationClientPage = packageClient.getPageNew(packageParam).getData();
        for (int i = 1; i <= locationClientPage.getPages(); i++) {
            packageParam.setCurrent(i);
            packageParam.setSize(2000);
            Page<PackageDTO> clientPage = packageClient.getPageNew(packageParam).getData();
            packageDTOList.addAll(clientPage.getRecords());
        }
        return packageDTOList;
    }

    @Override
    public Boolean modifyPretreatmentStatus(PackageParam packageParam) {
        return packageClient.modifyPretreatmentStatus(packageParam).getData();
    }

    @Override
    public List<PackageDTO> getAppointMultipleParam(PackageParam packageParam, List<String> tableFields) {
        return packageClient.getAppointMultipleParam(packageParam, tableFields).getData();
    }

    @Override
    public List<PackageDetailDTO> getCollectWavePackageDetailListAppointColumn(PackageDetailParam packageDetailParam, List<String> filedList) {
        return packageClient.getCollectWavePackageDetailListAppointColumn(packageDetailParam, filedList).getData();
    }

    @Override
    public Integer getWaveNavigationHead(PackageParam packageParam) {
        return packageClient.getWaveNavigationHead(packageParam).getData();
    }

    @Override
    public List<Map<String, Object>> getWaveNavigationBodyGroup(WaveNavigationParam waveNavigationParam) {
        return packageClient.getWaveNavigationBodyGroup(waveNavigationParam).getData();
    }

    @Override
    public Integer getWaveNavigationBodyByLastShipTime(WaveNavigationParam waveNavigationParam) {
        return packageClient.getWaveNavigationBodyByLastShipTime(waveNavigationParam).getData();
    }

    @Override
    public PackageDTO getWaveNavigationByAnalysisSkuLimitOne(PackageParam packageParam) {
        return packageClient.getWaveNavigationByAnalysisSkuLimitOne(packageParam).getData();
    }

    @Override
    public List<CollectWaveDTO> getWaveNavigationCollectGroup(PackageParam packageParam) {
        return packageClient.getWaveNavigationCollectGroup(packageParam).getData();
    }

    @Override
    public List<PackageDTO> getWaveNavigationCollectWaveList(PackageParam packageParam) {
        return packageClient.getWaveNavigationCollectWaveList(packageParam).getData();
    }

    @Override
    public List<CollectWaveAnalysisDTO> getWaveCollectFrontAnalysisGroupBy(PackageParam packageParam) {
        return packageClient.getWaveCollectFrontAnalysisGroupBy(packageParam).getData();
    }

    @Override
    public Integer getExportCountNum(PackageParam packageParam) {
        return packageClient.getExportCountNum(packageParam).getData();
    }

    @Override
    public Page<PackageDTO> getPageNew(PackageParam packageParam) {
        return packageClient.getPageNew(packageParam).getData();
    }

    @Override
    public List<CollectWaveAnalysisPackStructDTO> getCollectWaveAnalysisPackStruct(PackageParam packageParam) {
        return packageClient.getCollectWaveAnalysisPackStruct(packageParam).getData();
    }

    @Override
    public List<PackageLogDTO> getPackLog(PackageParam packageParam) {
        return packageClient.getPackLog(packageParam).getData();
    }

    @Override
    public void handlePackageVolume(PackageDTO packageDTO) {
        if (ShipmentOrderEnum.BUSSINESS_TYPE.B2B.name().equalsIgnoreCase(packageDTO.getBusinessType())) {
            if (packageDTO.getVolume() != null && packageDTO.getVolume().compareTo(BigDecimal.valueOf(999999999)) > 0) {
                packageDTO.setVolume(BigDecimal.ZERO);
            }
        }
    }

    @Override
    public Map<String, Long> getPackPrintNum(PackageParam packageParam) {
        return packageClient.getPackPrintNum(packageParam).getData();
    }

    @Override
    public Page<PackAnalysisBillDTO> getPackAnalysisPage(PackAnalysisBillParam param) {
        return packageClient.getPackAnalysisPage(param).getData();
    }
}
