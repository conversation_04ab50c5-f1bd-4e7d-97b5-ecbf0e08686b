package com.dt.platform.wms.integration.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.business.client.rpc.config.facade.EntityWarehouseRpcFacade;
import com.danding.business.client.rpc.config.result.EntityWarehouseRpcResult;
import com.danding.business.hms.rpc.resources.api.IEntityPartitionRpcService;
import com.danding.business.hms.rpc.resources.dto.EntityPartitionRpcDTO;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.ZoneStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.client.IZoneClient;
import com.dt.domain.base.dto.ZoneDTO;
import com.dt.domain.base.param.ZoneBatchParam;
import com.dt.domain.base.param.ZoneParam;
import com.dt.platform.wms.biz.config.WmsTenantWarehouseHelper;
import com.dt.platform.wms.biz.dto.PhysicalPartitionBindDTO;
import com.dt.platform.wms.integration.IRemoteZoneClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class RemoteZoneClient implements IRemoteZoneClient {

    @DubboReference
    private IZoneClient zoneClient;

    @DubboReference
    EntityWarehouseRpcFacade entityWarehouseRpcFacade;

    @Resource
    WmsTenantWarehouseHelper wmsTenantWarehouseHelper;

    @DubboReference
    IEntityPartitionRpcService entityPartitionRpcService;

    @Override
    public Boolean save(ZoneParam param) {
        return zoneClient.save(param).getData();
    }

    @Override
    public Boolean saveBatch(ZoneBatchParam param) {
        return zoneClient.saveBatch(param).getData();
    }

    @Override
    public Boolean modify(ZoneParam param) {
        return zoneClient.modify(param).getData();
    }

    @Override
    public Boolean checkExits(ZoneParam param) {
        return zoneClient.checkExits(param).getData();
    }

    @Override
    public ZoneDTO get(ZoneParam param) {
        return zoneClient.get(param).getData();
    }

    @Override
    public List<ZoneDTO> getList(ZoneParam param) {
        return zoneClient.getList(param).getData();
    }

    @Override
    public void checkStatus(List<ZoneDTO> zoneDTOList) {
        for (ZoneDTO zoneDTO : zoneDTOList) {
            if (Objects.equals(zoneDTO.getStatus(), ZoneStatusEnum.STATUS_DISABLED.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("库位的库区:%s被禁用，不允许使用该库位。", zoneDTO.getCode()));
            }
        }
    }

    @Override
    public Page<ZoneDTO> getPage(ZoneParam param) {
        return zoneClient.getPage(param).getData();
    }

    @Override
    public ZoneDTO queryTemporaryByTypeAndSkuQuality(String type, String skuQuality) {
        ZoneParam param = new ZoneParam();
        param.setType(type);
        param.setSkuQuality(skuQuality);
        List<ZoneDTO> zoneDTOS = zoneClient.getList(param).getData();
        if (CollectionUtils.isEmpty(zoneDTOS)) {
            return null;
        }
        //有多个默认区第一个
        return zoneDTOS.get(0);
    }

    @Override
    public List<ZoneDTO> queryZoneByType(String type) {
        ZoneParam param = new ZoneParam();
        param.setType(type);
        return zoneClient.getList(param).getData();
    }

    @Override
    public List<IdNameVO> getAllZoneList() {
        return zoneClient.getAllZoneList().getData();
    }

    @Override
    public List<IdNameVO> getZonePickAndStorageList() {
        return zoneClient.getZonePickAndStorageList().getData();
    }

    @Override
    public List<PhysicalPartitionBindDTO> getPhysicalPartitionList(String warehouseCode) {
        //设置租户信息
        wmsTenantWarehouseHelper.setTenantId(warehouseCode);
        //从ERP获取ERP实体仓编码信息
        EntityWarehouseRpcResult warehouseRpcResult = entityWarehouseRpcFacade.getWarehouseByCode(warehouseCode);
        if (warehouseRpcResult == null) {
            return Lists.newArrayList();
        }
        String erpWarehouseCode = warehouseRpcResult.getEntityWarehouseCode();
        //查询oms
        List<EntityPartitionRpcDTO> entityPartitionRpcDTOList = entityPartitionRpcService.listByEntityWarehouse(erpWarehouseCode);
        if (CollectionUtils.isEmpty(entityPartitionRpcDTOList)) {
            return Lists.newArrayList();
        }
        List<PhysicalPartitionBindDTO> physicalPartitionBindDTOList = new ArrayList<>();

        entityPartitionRpcDTOList.forEach(it -> {
            PhysicalPartitionBindDTO physicalPartitionBindDTO = new PhysicalPartitionBindDTO();
            physicalPartitionBindDTO.setPhysicalPartition(it.getPartitionCode());
            physicalPartitionBindDTO.setPhysicalPartitionName(it.getPartitionCode());
            physicalPartitionBindDTO.setLesseeEnterprise(it.getLessee());
            physicalPartitionBindDTOList.add(physicalPartitionBindDTO);
        });
        log.info("getPhysicalPartitionList:{}", JSONUtil.toJsonStr(physicalPartitionBindDTOList));
        return physicalPartitionBindDTOList;
    }

    @Override
    public List<PhysicalPartitionBindDTO> getPhysicalPartitionList(String warehouseCode, List<String> zoneCodeList) {
        log.info("zoneCodeList {}",zoneCodeList);
        if (StringUtils.isEmpty(warehouseCode) || CollectionUtils.isEmpty(zoneCodeList)) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        //设置租户信息
        wmsTenantWarehouseHelper.setTenantId(warehouseCode);
        //从ERP获取ERP实体仓编码信息
        EntityWarehouseRpcResult warehouseRpcResult = entityWarehouseRpcFacade.getWarehouseByCode(warehouseCode);
        if (warehouseRpcResult == null) {
            return Lists.newArrayList();
        }
        String erpWarehouseCode = warehouseRpcResult.getEntityWarehouseCode();
        //查询oms
        List<EntityPartitionRpcDTO> entityPartitionRpcDTOList = entityPartitionRpcService.listByEntityWarehouse(erpWarehouseCode);
        log.info("entityPartitionRpcDTOList {}",JSONUtil.toJsonStr(entityPartitionRpcDTOList));
        if (CollectionUtils.isEmpty(entityPartitionRpcDTOList)) {
            return Lists.newArrayList();
        }
        List<PhysicalPartitionBindDTO> physicalPartitionBindDTOList = new ArrayList<>();
        ZoneParam zoneParam = new ZoneParam();
        zoneParam.setCodeList(zoneCodeList);
        List<ZoneDTO> zoneDTOList = zoneClient.getList(zoneParam).getData();
        if (CollectionUtils.isEmpty(zoneDTOList)) {
            return Lists.newArrayList();
        }
        zoneDTOList.forEach(zoneDTO -> {
            log.info("zone code {} {}",zoneDTO.getCode(),JSONUtil.toJsonStr(zoneDTO));
            EntityPartitionRpcDTO entityPartitionRpcDTO = entityPartitionRpcDTOList
                    .stream().filter(a -> a.getPartitionCode().equalsIgnoreCase(zoneDTO.getPhysicalPartition()))
                    .findFirst().orElse(null);
            if (entityPartitionRpcDTO != null) {
                PhysicalPartitionBindDTO physicalPartitionBindDTO = new PhysicalPartitionBindDTO();
                physicalPartitionBindDTO.setPhysicalPartition(entityPartitionRpcDTO.getPartitionCode());
                physicalPartitionBindDTO.setPhysicalPartitionName(entityPartitionRpcDTO.getPartitionCode());
                physicalPartitionBindDTO.setZoneCode(zoneDTO.getCode());
                physicalPartitionBindDTO.setLesseeEnterprise(entityPartitionRpcDTO.getLessee());
                physicalPartitionBindDTOList.add(physicalPartitionBindDTO);
            }else {
                log.info("entityPartitionRpcDTONotFound {} ",zoneDTO.getCode());
            }
        });
        log.info("physicalPartitionBindDTOList {}",JSONUtil.toJsonStr(physicalPartitionBindDTOList));
        return physicalPartitionBindDTOList;
    }
}
