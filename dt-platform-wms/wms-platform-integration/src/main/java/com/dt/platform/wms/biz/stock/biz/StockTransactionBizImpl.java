package com.dt.platform.wms.biz.stock.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.NamedThreadFactory;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.stock.OperationTypeEnum;
import com.dt.component.common.enums.stock.StockLevelEnum;
import com.dt.component.common.enums.stock.StockTransactionStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.exceptions.WmsStockOperationException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.core.stock.bo.StockOperationBO;
import com.dt.domain.core.stock.dto.*;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.domain.core.stock.param.StockParam;
import com.dt.domain.core.stock.param.StockTransactionCancelParam;
import com.dt.domain.core.stock.param.StockTransactionProcessParam;
import com.dt.platform.wms.biz.stock.core.*;
import com.dt.platform.wms.integration.IRemoteStockClient;
import com.dt.platform.wms.integration.IRemoteStockLocationClient;
import com.dt.platform.wms.integration.stock.IRemoteStockOperationClient;
import lombok.extern.slf4j.Slf4j;
import org.redisson.RedissonMultiLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StockTransactionBizImpl implements IStockTransactionBiz {

    public static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(32, 128,
            60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(16),
            new NamedThreadFactory("stockTransaction", false),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Resource
    private IRemoteStockClient remoteStockClient;

    @Resource
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    private StockHelper stockHelper;

    @Resource
    private ReceiptStockTransactionHelper receiptStockTransactionHelper;

    @Resource
    private PretreatmentStockTransactionHelper pretreatmentStockTransactionHelper;

    @Resource
    private PrePackageStockTransactionHelper prePackageStockTransactionHelper;

    @Resource
    private ReturnStockTransactionHelper returnStockTransactionHelper;

    @Autowired
    private AllocationStockTransactionHelper allocationStockTransactionHelper;

    @Autowired
    private OutStockTransactionHelper outStockTransactionHelper;

    @Resource
    private CircleGoodsOutStockTransactionHelper circleGoodsOutStockTransactionHelper;

    @Resource
    private CircleGoodsSaleOutStockTransactionHelper circleGoodsSaleOutStockTransactionHelper;

    @Resource
    private CircleGoodsReceiptTransactionHelper circleGoodsReceiptTransactionHelper;

    @Autowired
    private ShelfStockTransactionHelper shelfStockTransactionHelper;

    @Autowired
    private CWStorageApplicationTransactionHelper cwStorageApplicationTransactionHelper;

    @Autowired
    private CWTransferConfirmTransactionHelper cwTransferConfirmTransactionHelper;

    @Autowired
    private PickStockTransactionHelper pickStockTransactionHelper;

    @Resource
    private IRemoteStockOperationClient remoteStockOperationClient;

    @Resource
    private StockOperationHandler stockOperationHandler;

    @Resource
    private FinanceStockTransactionHelper financeStockTransactionHelper;

    @Resource
    private MoveStockTransactionHelper moveStockTransactionHelper;

    @Resource
    private UpstreamStockTransactionHelper upstreamStockTransactionHelper;

    @Resource
    private AdjustStockTransactionHelper adjustStockTransactionHelper;

    @Resource
    private TransferStockTransactionHelper transferStockTransactionHelper;

    @Resource
    private SaleReturnReceiptStockTransactionHelper saleReturnReceiptStockTransactionHelper;

    @Resource
    private PledgeStockTransactionHelper pledgeStockTransactionHelper;

    @Resource
    private BomStockTransactionHelper bomStockTransactionHelper;

    @Override
    public boolean saveStockTransaction(List<StockTransactionDTO> stockTransactionDTOList) {
        stockTransactionDTOList = stockTransactionDTOList.stream()
                .filter(stockTransactionDTO -> !stockTransactionDTO.getStockLevel().equalsIgnoreCase(StockLevelEnum.LEVEL_STOCK_ZONE.getLevel())).collect(Collectors.toList());
        stockHelper.initStockTransaction(stockTransactionDTOList);
        stockHelper.basicCheck(stockTransactionDTOList);
        StockOperationBO stockOperationBO = new StockOperationBO();
        stockOperationBO.setStockTransactionDTOList(stockTransactionDTOList);
        remoteStockOperationClient.stockOperation(stockOperationBO);
        return true;
    }

    @Override
    public void processAllLevelAsync(StockTransactionProcessParam param) {
        Map<String, List<StockTransactionDTO>> collect = param.getStockTransactionDTOList()
                .parallelStream()
                .collect(Collectors.groupingBy(stockTransactionDTO -> StrUtil.join(StrUtil.COLON, stockTransactionDTO.getCargoCode(), stockTransactionDTO.getSkuCode(), stockTransactionDTO.getStockLevel())));

        List<CompletableFuture<Void>> completableFutureStream = collect.values().stream().map(stockTransactionDTOList -> CompletableFuture.runAsync(() -> {
            RpcContextUtil.setWarehouseCode(stockTransactionDTOList.get(0).getWarehouseCode());
            StockTransactionProcessParam stockTransactionProcessParam = new StockTransactionProcessParam();
            stockTransactionProcessParam.setStockTransactionDTOList(stockTransactionDTOList);
            stockTransactionProcessParam.setThrowOnFail(false);
            stockTransactionProcessParam.setSendMessage(true);
            processAllLevel(stockTransactionProcessParam);
        }, threadPoolExecutor)).collect(Collectors.toList());
        CompletableFuture.allOf(completableFutureStream.toArray(new CompletableFuture[0])).join();

        // send message
        List<String> batchSerialNos = param.getStockTransactionDTOList().stream().map(StockTransactionDTO::getBatchSerialNo).distinct().collect(Collectors.toList());
        for (String batchSerialNo : batchSerialNos) {
            param.getStockTransactionDTOList().stream()
                    .filter(stockTransactionDTO -> stockTransactionDTO.getBatchSerialNo().equals(batchSerialNo))
                    .findFirst()
                    .ifPresent(stockTransactionDTO -> stockOperationHandler.callback(StockOperationContext.builder()
                            .warehouseCode(stockTransactionDTO.getWarehouseCode())
                            .cargoCode(stockTransactionDTO.getCargoCode())
                            .operationType(stockTransactionDTO.getOperationType())
                            .parentBillNo(stockTransactionDTO.getParentBillNo())
                            .stockTransactionDTOList(ListUtil.toList(stockTransactionDTO))
                            .stockLevel(stockTransactionDTO.getStockLevel())
                            .build()));
        }
    }

    /**
     * 正向处理一级库存请求
     *
     * @param param param
     */
    @Override
    public Boolean processAllLevel(StockTransactionProcessParam param) {
        RedissonMultiLock lock = stockHelper.lock(stockHelper.getStockLock(param.getStockTransactionDTOList()));
        boolean locked = false;
        try {
            locked = lock.tryLock(10, 60 * 5, TimeUnit.SECONDS);
            if (locked) {
                return processAllLevelInner(param);
            } else {
                throw new BaseException(BaseBizEnum.TIP, "stock core lock fail");
            }
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }


    private Boolean processAllLevelInner(StockTransactionProcessParam param) {

        StockOperationBO stockOperationBO = new StockOperationBO();
        log.info("processPositive {}", JSONUtil.toJsonStr(param));
        List<StockTransactionDTO> stockTransactionList = param.getStockTransactionDTOList();
        if (CollectionUtil.isEmpty(stockTransactionList)) {
            return true;
        }

        stockHelper.initStockTransaction(stockTransactionList);
        stockHelper.basicCheck(stockTransactionList);

        stockTransactionList = stockTransactionList.stream()
                .filter(it -> !it.getStockLevel().equalsIgnoreCase(StockLevelEnum.LEVEL_STOCK_ZONE.getLevel()))
                .filter(it -> it.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.CREATED.getCode())
                        || it.getStatus().equalsIgnoreCase(StockTransactionStatusEnum.RETRY.getCode()))
                .collect(Collectors.toList());

        // stock in db
        List<StockDTO> stockList = new ArrayList<>();
        List<StockLocationDTO> stockLocationList = new ArrayList<>();

        List<String> levelStockSkuCodeList = stockTransactionList.stream()
                .filter(stockTransaction -> StockLevelEnum.LEVEL_STOCK.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel()))
                .map(StockTransactionDTO::getSkuCode)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(levelStockSkuCodeList)) {
            StockParam stockParam = new StockParam();
            stockParam.setSkuCodeList(levelStockSkuCodeList);
            stockList = remoteStockClient.getList(stockParam);
        }

        List<String> levelLocationSkuCodeList = stockTransactionList.stream()
                .filter(stockTransaction -> StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel()))
                .map(StockTransactionDTO::getSkuCode)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(levelLocationSkuCodeList)) {
            List<String> locationCodeList = stockTransactionList.stream()
                    .filter(stockTransaction -> StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel()))
                    .map(StockTransactionDTO::getLocationCode)
                    .collect(Collectors.toList());
            List<String> skuLotNoList = stockTransactionList.stream()
                    .filter(stockTransaction -> StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel()))
                    .map(StockTransactionDTO::getSkuLotNo)
                    .collect(Collectors.toList());
            StockLocationParam stockLocationParam = new StockLocationParam();
            stockLocationParam.setSkuCodeList(levelLocationSkuCodeList);
            stockLocationParam.setLocationCodeList(locationCodeList);
            stockLocationParam.setSkuLotNoList(skuLotNoList);
            stockLocationList = remoteStockLocationClient.getList(stockLocationParam);
        }

        // to update
        List<StockDTO> stockSaveOrUpdateList = new ArrayList<>();
        List<StockLocationDTO> stockLocationSaveOrUpdateList = new ArrayList<>();
        List<StockSerialDTO> stockSerialList = new ArrayList<>();

        for (StockTransactionDTO stockTransaction : stockTransactionList) {
            if (StockLevelEnum.LEVEL_STOCK.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel())) {
                StockDTO stock = stockHelper.getNewStock(stockTransaction, stockSaveOrUpdateList, stockList);
                OperationTypeEnum operationTypeEnum = OperationTypeEnum.getEnum(stockTransaction.getOperationType());
                switch (operationTypeEnum) {
                    case OPERATION_SHIPMENT:
                        pretreatmentStockTransactionHelper.shipmentOccupyLevelOne(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_PACKAGE_LOCK_ORIGIN:
                        prePackageStockTransactionHelper.prePackageFrozen(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_PACKAGE_REVIEW:
                        prePackageStockTransactionHelper.prePackageReview(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_UPC_SHELF:
                        prePackageStockTransactionHelper.prePackageShelfNewUpc(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_STOCK:
                        outStockTransactionHelper.outStock(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_SPLIT_LOCK_ORIGIN:
                        prePackageStockTransactionHelper.splitFrozen(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_SPLIT_SUBMIT:
                        prePackageStockTransactionHelper.splitSubmit(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_RETURN_COMPLETE_CONTAINER:
                        returnStockTransactionHelper.ret(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CIRCLE_GOODS_SALE_STOCK:
                        circleGoodsSaleOutStockTransactionHelper.outStock(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_COMPLETE_CONTAINER:
                        receiptStockTransactionHelper.receipt(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CIRCLE_GOODS_COMPLETE_CONTAINER:
                        circleGoodsReceiptTransactionHelper.receipt(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_FINANCE_SUPERVISION_PREPARE:
                        financeStockTransactionHelper.supervisionPrepare(stockTransaction, stock, stockSerialList);
                        break;
                    case OPERATION_FINANCE_REDEEM_THAW:
                        financeStockTransactionHelper.redeem(stockTransaction, stock, stockSerialList);
                        break;
                    case OPERATION_FINANCE_DISPOSAL:
                        financeStockTransactionHelper.disposal(stockTransaction, stock, stockSerialList);
                        break;
                    case OPERATION_UPSTREAM_OCCUPY:
                        upstreamStockTransactionHelper.occupyWarehouseLevel(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_RECEIPT_SHELF:
                    case OPERATION_RETURN_SHELF:
                    case OPERATION_PRE_SPLIT_SHELF:
                    case OPERATION_SALE_RETURN_SHELF:
                    case OPERATION_BOM_ASSEMBLE_SHELF:
                    case OPERATION_BOM_DISASSEMBLE_SHELF:
                        shelfStockTransactionHelper.shelf(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_ADJUST_SUBMIT:
                        adjustStockTransactionHelper.submit(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_ADJUST_CANCEL:
                        adjustStockTransactionHelper.cancel(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_ADJUST_CONFIRM:
                        adjustStockTransactionHelper.confirm(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_TRANSFER_SUBMIT:
                        transferStockTransactionHelper.submit(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_TRANSFER_CANCEL:
                        transferStockTransactionHelper.cancel(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_TRANSFER_CONFIRM:
                        transferStockTransactionHelper.confirm(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_SHELF_RELEASE:
                        shelfStockTransactionHelper.release(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_OFF_SHELF:
                        shelfStockTransactionHelper.off(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CW_STORAGE_APPLICATION:
                        cwStorageApplicationTransactionHelper.collect(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CW_TRANSFER_CONFIRM:
                        cwTransferConfirmTransactionHelper.confirm(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CW_TRANSFER_SHELF:
                        cwTransferConfirmTransactionHelper.shelf(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CW_TRANSFER_OUT:
                        cwTransferConfirmTransactionHelper.out(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_SALE_RETURN_RECEIPT:
                        saleReturnReceiptStockTransactionHelper.receipt(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PLEDGE:
                        stockOperationBO.setHasPledgeOperation(true);
                        pledgeStockTransactionHelper.pledge(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PLEDGE_CANCEL:
                        stockOperationBO.setHasPledgeOperation(true);
                        pledgeStockTransactionHelper.cancel(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PLEDGE_CONFIRM:
                        stockOperationBO.setHasPledgeOperation(true);
                        break;
                    case OPERATION_REDEEM:
                        pledgeStockTransactionHelper.redeem(stock, stockTransaction, stockSerialList);
                        stockOperationBO.setHasPledgeOperation(true);
                        break;
                    case OPERATION_BOM_ASSEMBLE_LOCK:
                    case OPERATION_BOM_DISASSEMBLE_LOCK:
                        bomStockTransactionHelper.lock(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_BOM_ASSEMBLE:
                        bomStockTransactionHelper.assemble(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_BOM_DISASSEMBLE:
                        bomStockTransactionHelper.disassemble(stock, stockTransaction, stockSerialList);
                        break;
                    default:
                        throw new BaseException(BaseBizEnum.TIP, "操作类型不支持");
                }

                if (!stockSaveOrUpdateList.contains(stock)) {
                    stockSaveOrUpdateList.add(stock);
                }
            } else if (StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel())) {
                StockLocationDTO stockLocation = stockHelper.getNewStockLocation(stockTransaction, stockLocationSaveOrUpdateList, stockLocationList);

                OperationTypeEnum operationTypeEnum = OperationTypeEnum.getEnum(stockTransaction.getOperationType());
                switch (operationTypeEnum) {
                    case OPERATION_SHIPMENT:
                    case OPERATION_CONVERGE:
                        allocationStockTransactionHelper.allocation(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_PACKAGE_LOCK_ORIGIN:
                        prePackageStockTransactionHelper.prePackageFrozen(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_PACKAGE_REVIEW:
                        prePackageStockTransactionHelper.prePackageReview(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_PACKAGE_SHELF:
                        prePackageStockTransactionHelper.prePackageShelf(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_UPC_SHELF:
                        prePackageStockTransactionHelper.prePackageShelfNewUpc(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_STOCK:
                        outStockTransactionHelper.outStock(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_RECEIPT_SHELF:
                    case OPERATION_RETURN_SHELF:
                    case OPERATION_PRE_SPLIT_SHELF:
                    case OPERATION_SALE_RETURN_SHELF:
                    case OPERATION_BOM_ASSEMBLE_SHELF:
                    case OPERATION_BOM_DISASSEMBLE_SHELF:
                        shelfStockTransactionHelper.shelf(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PICK_RETURN:
                        pickStockTransactionHelper.pick(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_SPLIT_LOCK_ORIGIN:
                        prePackageStockTransactionHelper.splitFrozen(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PRE_SPLIT_SUBMIT:
                        prePackageStockTransactionHelper.splitSubmit(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_RETURN_COMPLETE_CONTAINER:
                        returnStockTransactionHelper.ret(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CIRCLE_GOODS_STOCK:
                        circleGoodsOutStockTransactionHelper.outStock(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CIRCLE_GOODS_SALE_STOCK:
                        circleGoodsSaleOutStockTransactionHelper.outStock(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_COMPLETE_CONTAINER:
                        receiptStockTransactionHelper.receipt(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CIRCLE_GOODS_COMPLETE_CONTAINER:
                        circleGoodsReceiptTransactionHelper.receipt(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_FINANCE_SUPERVISION_PREPARE:
                        financeStockTransactionHelper.supervisionPrepare(stockTransaction, stockLocation, stockSerialList);
                        break;
                    case OPERATION_FINANCE_SUPERVISION_MOVE:
                        financeStockTransactionHelper.supervisionMove(stockTransaction, stockLocation, stockSerialList);
                        break;
                    case OPERATION_FINANCE_REDEEM_THAW:
                        financeStockTransactionHelper.redeem(stockTransaction, stockLocation, stockSerialList);
                        break;
                    case OPERATION_FINANCE_REDEEM_MOVE:
                        financeStockTransactionHelper.redeemMove(stockTransaction, stockLocation, stockSerialList);
                        break;
                    case OPERATION_FINANCE_DISPOSAL:
                        financeStockTransactionHelper.disposal(stockTransaction, stockLocation, stockSerialList);
                        break;
                    case OPERATION_FINANCE_DISPOSAL_MOVE:
                        financeStockTransactionHelper.disposalMove(stockTransaction, stockLocation, stockSerialList);
                        break;
                    case OPERATION_MOVE:
                        moveStockTransactionHelper.move(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_UPSTREAM_OCCUPY:
                        upstreamStockTransactionHelper.occupyLotLevel(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_ADJUST_SUBMIT:
                        adjustStockTransactionHelper.submit(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_ADJUST_CANCEL:
                        adjustStockTransactionHelper.cancel(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_ADJUST_CONFIRM:
                        adjustStockTransactionHelper.confirm(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_TRANSFER_SUBMIT:
                        transferStockTransactionHelper.submit(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_TRANSFER_CANCEL:
                        transferStockTransactionHelper.cancel(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_TRANSFER_CONFIRM:
                        transferStockTransactionHelper.confirm(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_SHELF_RELEASE:
                        shelfStockTransactionHelper.release(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_OFF_SHELF:
                        shelfStockTransactionHelper.off(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CW_STORAGE_APPLICATION:
                        cwStorageApplicationTransactionHelper.collect(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CW_TRANSFER_COLLECT:
                        cwTransferConfirmTransactionHelper.collect(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CW_TRANSFER_OUT:
                        cwTransferConfirmTransactionHelper.out(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CW_TRANSFER_SHELF:
                        cwTransferConfirmTransactionHelper.shelf(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_SALE_RETURN_RECEIPT:
                        saleReturnReceiptStockTransactionHelper.receipt(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PLEDGE:
                        stockOperationBO.setHasPledgeOperation(true);
                        pledgeStockTransactionHelper.pledge(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PLEDGE_CANCEL:
                        stockOperationBO.setHasPledgeOperation(true);
                        pledgeStockTransactionHelper.cancel(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_PLEDGE_CONFIRM:
                        stockOperationBO.setHasPledgeOperation(true);
                        break;
                    case OPERATION_REDEEM:
                        pledgeStockTransactionHelper.redeem(stockLocation,stockTransaction,stockSerialList);
                        stockOperationBO.setHasPledgeOperation(true);
                        break;
                    case OPERATION_BOM_ASSEMBLE_LOCK:
                    case OPERATION_BOM_DISASSEMBLE_LOCK:
                        bomStockTransactionHelper.lock(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_BOM_ASSEMBLE:
                        bomStockTransactionHelper.assemble(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_BOM_DISASSEMBLE:
                        bomStockTransactionHelper.disassemble(stockLocation, stockTransaction, stockSerialList);
                        break;
                    default:
                        throw new BaseException(BaseBizEnum.TIP, "操作类型不支持");
                }

                if (!stockLocationSaveOrUpdateList.contains(stockLocation)) {
                    stockLocationSaveOrUpdateList.add(stockLocation);
                }
            } else {
                throw new BaseException(BaseBizEnum.TIP, "库存层级不支持");
            }

            if (!StockTransactionStatusEnum.DONE.getCode().equalsIgnoreCase(stockTransaction.getStatus())) {
                if (param.getThrowOnFail()) {
                    log.error("stockProcessFail {}", JSONUtil.toJsonStr(stockTransaction));
                    throw new WmsStockOperationException(BaseBizEnum.TIP, String.format("%s仓库%s货主%s商品库存不足，库区：%s 批次：%s 库位：%s Level:%s"
                            , stockTransaction.getWarehouseCode()
                            , stockTransaction.getCargoCode()
                            , stockTransaction.getSkuCode()
                            , ZoneTypeEnum.zoneTypeName(stockTransaction.getZoneType())
                            , stockTransaction.getSkuLotNo()
                            , stockTransaction.getLocationCode()
                            , StockLevelEnum.stockLevelName(stockTransaction.getStockLevel())
                    ));
                }
            }
        }

        stockOperationBO.setStockDTOList(stockSaveOrUpdateList);
        stockOperationBO.setStockLocationDTOList(stockLocationSaveOrUpdateList);
        stockOperationBO.setStockSerialDTOList(stockSerialList);
        stockOperationBO.setStockTransactionDTOList(stockTransactionList);
        remoteStockOperationClient.stockOperation(stockOperationBO);
        return true;
    }

    /**
     * @param param param
     * @return false if stock transaction list is empty
     */
    private Boolean cancelAllLevelInner(StockTransactionCancelParam param) {

        StockOperationBO stockOperationBO = new StockOperationBO();

        log.info("cancel all level {}", JSONUtil.toJsonStr(param));
        List<StockTransactionDTO> stockTransactionList = param.getStockTransactionDTOList();

        stockTransactionList = stockTransactionList.stream()
                // 已取消的不用处理
                .filter(it -> !it.getStockLevel().equalsIgnoreCase(StockLevelEnum.LEVEL_STOCK_ZONE.getLevel()))
                .filter(it -> !StockTransactionStatusEnum.CANCELLED.getCode().equalsIgnoreCase(it.getStatus()))
                .collect(Collectors.toList());

        // stock in db
        List<StockDTO> stockList = new ArrayList<>();
        List<StockLocationDTO> stockLocationList = new ArrayList<>();

        List<String> levelStockSkuCodeList = stockTransactionList.stream()
                .filter(stockTransaction -> StockLevelEnum.LEVEL_STOCK.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel()))
                .map(StockTransactionDTO::getSkuCode)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(levelStockSkuCodeList)) {
            StockParam stockParam = new StockParam();
            stockParam.setSkuCodeList(levelStockSkuCodeList);
            stockList = remoteStockClient.getList(stockParam);
        }

        List<String> levelLocationSkuCodeList = stockTransactionList.stream()
                .filter(stockTransaction -> StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel()))
                .map(StockTransactionDTO::getSkuCode)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(levelLocationSkuCodeList)) {
            List<String> locationCodeList = stockTransactionList.stream()
                    .filter(stockTransaction -> StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel()))
                    .map(StockTransactionDTO::getLocationCode)
                    .collect(Collectors.toList());
            List<String> skuLotNoList = stockTransactionList.stream()
                    .filter(stockTransaction -> StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel()))
                    .map(StockTransactionDTO::getSkuLotNo)
                    .collect(Collectors.toList());
            StockLocationParam stockLocationParam = new StockLocationParam();
            stockLocationParam.setSkuCodeList(levelLocationSkuCodeList);
            stockLocationParam.setLocationCodeList(locationCodeList);
            stockLocationParam.setSkuLotNoList(skuLotNoList);
            stockLocationList = remoteStockLocationClient.getList(stockLocationParam);
        }

        // to update
        List<StockDTO> stockSaveOrUpdateList = new ArrayList<>();
        List<StockZoneDTO> stockZoneSaveOrUpdateList = new ArrayList<>();
        List<StockLocationDTO> stockLocationSaveOrUpdateList = new ArrayList<>();
        List<StockSerialDTO> stockSerialList = new ArrayList<>();

        for (StockTransactionDTO stockTransaction : stockTransactionList) {
            if (StockLevelEnum.LEVEL_STOCK.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel())) {
                StockDTO stock = stockHelper.getNewStock(stockTransaction, stockSaveOrUpdateList, stockList);
                OperationTypeEnum operationTypeEnum = OperationTypeEnum.getEnum(stockTransaction.getOperationType());
                switch (operationTypeEnum) {
                    case OPERATION_SHIPMENT:
                        pretreatmentStockTransactionHelper.cancelShipmentOccupyLevelOne(stock, stockTransaction, stockSerialList, param.getCustomerTradeType(), param.getCustomerOperationType());
                        break;
                    case OPERATION_COMPLETE_CONTAINER:
                        receiptStockTransactionHelper.cancelReceipt(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CIRCLE_GOODS_COMPLETE_CONTAINER:
                        circleGoodsReceiptTransactionHelper.cancelReceipt(stock, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_FINANCE_SUPERVISION_PREPARE:
                        financeStockTransactionHelper.supervisionCancel(stockTransaction, stock, stockSerialList);
                        break;
                    case OPERATION_UPSTREAM_OCCUPY:
                        upstreamStockTransactionHelper.releaseWarehouseLevel(stock, stockTransaction, stockSerialList);
                        break;
                    default:
                        throw new BaseException(BaseBizEnum.TIP, "操作类型不支持");
                }

                if (!stockSaveOrUpdateList.contains(stock)) {
                    stockSaveOrUpdateList.add(stock);
                }
            } else if (StockLevelEnum.LEVEL_STOCK_LOCATION.getLevel().equalsIgnoreCase(stockTransaction.getStockLevel())) {
                StockLocationDTO stockLocation = stockHelper.getNewStockLocation(stockTransaction, stockLocationSaveOrUpdateList, stockLocationList);

                OperationTypeEnum operationTypeEnum = OperationTypeEnum.getEnum(stockTransaction.getOperationType());
                switch (operationTypeEnum) {
                    case OPERATION_SHIPMENT:
                    case OPERATION_CONVERGE:
                        allocationStockTransactionHelper.cancelAllocation(stockLocation, stockTransaction, stockSerialList, param.getCustomerTradeType(), param.getCustomerOperationType());
                        break;
                    case OPERATION_PICK_RETURN:
                        pickStockTransactionHelper.cancelPick(stockLocation, stockTransaction, stockSerialList, param.getCustomerTradeType(), param.getCustomerOperationType());
                        break;
                    case OPERATION_COMPLETE_CONTAINER:
                        receiptStockTransactionHelper.cancelReceipt(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_CIRCLE_GOODS_COMPLETE_CONTAINER:
                        circleGoodsReceiptTransactionHelper.cancelReceipt(stockLocation, stockTransaction, stockSerialList);
                        break;
                    case OPERATION_FINANCE_SUPERVISION_PREPARE:
                        financeStockTransactionHelper.supervisionCancel(stockTransaction, stockLocation, stockSerialList);
                        break;
                    case OPERATION_UPSTREAM_OCCUPY:
                        upstreamStockTransactionHelper.releaseLotLevel(stockLocation, stockTransaction, stockSerialList);
                        break;
                    default:
                        throw new BaseException(BaseBizEnum.TIP, "操作类型不支持");
                }

                if (!stockLocationSaveOrUpdateList.contains(stockLocation)) {
                    stockLocationSaveOrUpdateList.add(stockLocation);
                }
            } else {
                throw new BaseException(BaseBizEnum.TIP, "库存层级不支持");
            }

            if (!StockTransactionStatusEnum.CANCELLED.getCode().equalsIgnoreCase(stockTransaction.getStatus())) {
                if (param.getThrowOnFail()) {
                    log.error("stockCancelFail {}", JSONUtil.toJsonStr(stockTransaction));
                    throw new WmsStockOperationException(BaseBizEnum.TIP, String.format("%s仓库%s货主%s商品库存不足"
                            , stockTransaction.getWarehouseCode()
                            , stockTransaction.getCargoCode()
                            , stockTransaction.getSkuCode()));
                }
            }
        }

        stockOperationBO.setStockDTOList(stockSaveOrUpdateList);
        stockOperationBO.setStockZoneDTOList(stockZoneSaveOrUpdateList);
        stockOperationBO.setStockLocationDTOList(stockLocationSaveOrUpdateList);
        stockOperationBO.setStockSerialDTOList(stockSerialList);
        stockOperationBO.setStockTransactionDTOList(stockTransactionList);
        remoteStockOperationClient.stockOperation(stockOperationBO);
        return true;
    }


    @Override
    public void cancelAllLevelAsync(StockTransactionCancelParam param) {
        log.info("processCancel {}", JSONUtil.toJsonStr(param));
        cancelAllLevel(param);
    }

    @Override
    public Boolean cancelAllLevel(StockTransactionCancelParam param) {
        RedissonMultiLock lock = stockHelper.lock(stockHelper.getStockLock(param.getStockTransactionDTOList()));
        boolean locked = false;
        try {
            locked = lock.tryLock(10, 60 * 5, TimeUnit.SECONDS);
            if (locked) {
                StockTransactionCancelParam stockTransactionCancelParam = new StockTransactionCancelParam();
                stockTransactionCancelParam.setStockTransactionDTOList(param.getStockTransactionDTOList());
                stockTransactionCancelParam.setCustomerTradeType(param.getCustomerTradeType());
                stockTransactionCancelParam.setCustomerOperationType(param.getCustomerOperationType());
                return cancelAllLevelInner(stockTransactionCancelParam);
            } else {
                throw new RuntimeException("stock lock fail");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }
}
