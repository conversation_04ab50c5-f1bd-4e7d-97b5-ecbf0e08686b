package com.dt.platform.wms.integration.rs.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.business.client.rpc.goods.center.facade.IGoodsManagementRpcReadFacade;
import com.danding.business.client.rpc.goods.center.result.GoodsManagementRpcResult;
import com.danding.business.oms.common.BO.LogisticsReturnScene;
import com.danding.business.rpc.client.ares.order.facade.IInOrderRpcFacade;
import com.danding.business.rpc.client.ares.order.param.InOrderAddRpcParam;
import com.danding.business.rpc.client.ares.order.param.InOrderDetailAddRpcParam;
import com.danding.business.rpc.client.ares.order.param.ReceiveSendInfoAddRpcParam;
import com.danding.cds.out.api.CalloffOrderRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.RefundWarehouseCancelReqVO;
import com.danding.cds.out.common.constant.ResultCodeCons;
import com.danding.mercury.rpc.client.sales.SalesReturnCallback;
import com.danding.mercury.rpc.client.sales.SalesReturnCallbackDetail;
import com.danding.mercury.rpc.client.sales.SalesReturnCallbackRequest;
import com.danding.mercury.rpc.common.Result;
import com.danding.soul.client.common.exception.BusinessException;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.bill.MessageMqStatusEnum;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.component.common.enums.pre.BillLogTypeEnum;
import com.dt.component.common.enums.rs.*;
import com.dt.component.common.enums.shelf.ShelfStatusEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.log.BillLogDTO;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.bill.client.rs.ISalesReturnOrderClient;
import com.dt.domain.bill.client.rs.bo.SalesReturnOrderBO;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.rs.*;
import com.dt.domain.bill.param.ShelfParam;
import com.dt.domain.bill.param.message.MessageMqParam;
import com.dt.domain.bill.param.rs.*;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.DateDescUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.taotian.CargoOwnerTaoTianDTO;
import com.dt.platform.wms.biz.taotian.TTDefaultOrderPullRequest;
import com.dt.platform.wms.biz.taotian.TTReturnOrderConfirmRequest;
import com.dt.platform.wms.biz.taotian.TTReturnOrderConfirmRequest.OrderLine;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.impl.CallOtherSupport;
import com.dt.platform.wms.integration.impl.CallUnderOtherContext;
import com.dt.platform.wms.integration.log.IRemoteBillLogClient;
import com.dt.platform.wms.integration.mercury.IRemoteMercuryClient;
import com.dt.platform.wms.integration.message.IRemoteMessageMqClient;
import com.dt.platform.wms.integration.oms.IRemoteOmsLogisticClient;
import com.dt.platform.wms.integration.rs.*;
import com.dt.platform.wms.integration.warehouseManage.IRemoteLogicWarehouseClient;
import com.dt.tms.rpc.waybill.client.ITmsTrackClient;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.danding.business.common.ares.utils.HttpRequestUtils.postForm;

/**
 * <p>
 * 销退单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Slf4j
@Service("remoteSalesReturnOrderClientImpl")
public class RemoteSalesReturnOrderClientImpl implements IRemoteSalesReturnOrderClient {

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private WmsOtherConfig wmsOtherConfig;

    @Resource
    private IRemoteShelfClient remoteShelfClient;

    @Resource
    private IRemoteSalesReturnHandoverClient remoteSalesReturnHandoverClient;

    @Resource
    private IRemoteSalesReturnHandoverDetailClient remoteSalesReturnHandoverDetailClient;

    @DubboReference
    private ISalesReturnOrderClient salesReturnOrderClient;

    @DubboReference
    IGoodsManagementRpcReadFacade goodsManagementRpcReadFacade;

    @Resource
    private IRemoteSalesReturnExtraClient remoteSalesReturnExtraClient;

    @Resource
    private IRemoteMessageMqClient remoteMessageMqClient;

    @Resource
    private IRemoteLockSupportClient remoteLockSupportClient;

    @Resource
    private IRemoteSalesReturnOrderReceiveClient remoteSalesReturnOrderReceiveClient;

    @DubboReference
    private IInOrderRpcFacade inOrderRpcFacade;

    @Resource
    private RemoteTenantHelper remoteTenantHelper;

    @Resource
    private IRemoteBillLogClient remoteBillLogClient;

    @Resource
    private IRemoteMercuryClient remoteMercuryClient;

    @Resource
    private IRemoteOmsLogisticClient remoteOmsLogisticClient;

    @Resource
    private IRemoteLogicWarehouseClient remoteLogicWarehouseClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteSalesReturnOrderDetailClient remoteSalesReturnOrderDetailClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @DubboReference
    private ITmsTrackClient tmsTrackClient;

    @DubboReference
    private SalesReturnCallback salesReturnCallback;

    @Override
    public Boolean save(SalesReturnOrderDTO salesReturnOrderDTO) {
        return salesReturnOrderClient.save(salesReturnOrderDTO).getData();
    }

    @Override
    public Boolean save(SalesReturnOrderBO salesReturnOrderBO) {
        return salesReturnOrderClient.save(salesReturnOrderBO).getData();
    }

    @Override
    public Boolean saveBatch(List<SalesReturnOrderDTO> salesReturnOrderDTOList) {
        return salesReturnOrderClient.saveBatch(salesReturnOrderDTOList).getData();
    }

    @Override
    public Boolean modify(SalesReturnOrderDTO salesReturnOrderDTO) {
        return salesReturnOrderClient.modify(salesReturnOrderDTO).getData();
    }

    @Override
    public Boolean modifyBatch(List<SalesReturnOrderDTO> salesReturnOrderDTOList) {
        return salesReturnOrderClient.modifyBatch(salesReturnOrderDTOList).getData();
    }

    @Override
    public Boolean checkExits(SalesReturnOrderParam param) {
        return salesReturnOrderClient.checkExits(param).getData();
    }

    @Override
    public SalesReturnOrderDTO get(SalesReturnOrderParam param) {
        return salesReturnOrderClient.get(param).getData();
    }

    @Override
    public List<SalesReturnOrderDTO> getList(SalesReturnOrderParam param) {
        return salesReturnOrderClient.getList(param).getData();
    }

    @Override
    public Page<SalesReturnOrderDTO> getPage(SalesReturnOrderParam param) {
        return salesReturnOrderClient.getPage(param).getData();
    }

    @Override
    public Boolean remove(SalesReturnOrderParam param) {
        return salesReturnOrderClient.remove(param).getData();
    }

    @Override
    public SalesReturnOrderDTO buildExtraJson(SalesReturnOrderDTO salesReturnOrderDTO, Long time) {
        if (salesReturnOrderDTO == null || salesReturnOrderDTO.getStatus() == null) {
            return null;
        }

        SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();

        RSOrderStatusEnum rsOrderStatusEnum = RSOrderStatusEnum.fromInt(salesReturnOrderDTO.getStatus());
        SalesReturnOrderNodeDTO salesReturnOrderNodeDTO = new SalesReturnOrderNodeDTO();
        salesReturnOrderNodeDTO.setOrder(rsOrderStatusEnum.getCode());
        salesReturnOrderNodeDTO.setTime(time);
        salesReturnOrderNodeDTO.setTimeDesc(ConverterUtil.convertVoTime(time));
        salesReturnOrderNodeDTO.setTitle(rsOrderStatusEnum.getMessage());

        if (CollectionUtil.isEmpty(salesReturnOrderExtraDTO.getNodes())) {
            salesReturnOrderExtraDTO.setNodes(ListUtil.toList(salesReturnOrderNodeDTO));
        } else {
            salesReturnOrderExtraDTO.getNodes().add(salesReturnOrderNodeDTO);
        }

        salesReturnOrderDTO.salesReturnOrderExtraDTO(salesReturnOrderExtraDTO);
        return salesReturnOrderDTO;
    }

    @Override
    public SalesReturnOrderDTO removeNode(SalesReturnOrderDTO salesReturnOrderDTO, RSOrderStatusEnum rsOrderStatusEnum) {
        if (salesReturnOrderDTO == null || salesReturnOrderDTO.getStatus() == null) {
            return null;
        }

        try {
            SalesReturnOrderExtraDTO bean = salesReturnOrderDTO.salesReturnOrderExtraDTO();
            if (CollectionUtil.isEmpty(bean.getNodes())) {
                return salesReturnOrderDTO;
            }
            List<SalesReturnOrderNodeDTO> collect = bean.getNodes().stream().filter(it -> !it.getTitle().equalsIgnoreCase(rsOrderStatusEnum.getMessage()))
                    .collect(Collectors.toList());
            bean.setNodes(collect);
            salesReturnOrderDTO.salesReturnOrderExtraDTO(bean);

        } catch (Exception ignored) {

        }
        return salesReturnOrderDTO;
    }

    @Override
    public SalesReturnOrderDTO recordOutAtSameTime(SalesReturnOrderDTO salesReturnOrderDTO, List<String> noList) {
        if (salesReturnOrderDTO == null) {
            return null;
        }
        try {
            SalesReturnOrderExtraDTO bean = salesReturnOrderDTO.salesReturnOrderExtraDTO();
            bean.setNoListOutAtSameTime(noList);
            salesReturnOrderDTO.salesReturnOrderExtraDTO(bean);
        } catch (Exception ignored) {

        }
        return salesReturnOrderDTO;
    }

    @Override
    public List<GoodsManagementRpcResult> getERPSkuInfo(String upcCode) {
        List<GoodsManagementRpcResult> goodsManagementRpcResultList = goodsManagementRpcReadFacade.listRpcGoodsByReturnWarehouseCode(upcCode, CurrentRouteHolder.getWarehouseCode());
        if (CollectionUtils.isEmpty(goodsManagementRpcResultList)) {
            return Lists.newArrayList();
        }
        return goodsManagementRpcResultList;
    }

    @Override
    public List<String> getExtraExpressNoList(String expressNo) {
        if (StringUtils.isEmpty(expressNo)) {
            return ListUtil.toList(expressNo);
        }
        String expressNoOrigin = expressNo.toUpperCase();
        expressNo = expressNo.toUpperCase();
        if (expressNo.startsWith("T") && expressNo.length() > 1) {
            expressNoOrigin = expressNoOrigin.substring(1);
        }
        if (expressNo.startsWith("R02T") && expressNo.length() > 4) {
            expressNoOrigin = expressNoOrigin.substring(4);
        }
        if (expressNo.startsWith("R02Z") && expressNo.length() > 4) {
            expressNoOrigin = expressNoOrigin.substring(4);
        }
        return Arrays.asList(expressNoOrigin, "T" + expressNoOrigin, "R02T" + expressNoOrigin, "R02Z" + expressNoOrigin);
    }

    @Override
    public SalesReturnOrderContext link(SalesReturnOrderDTO salesReturnOrderDTO, String opBy) {

        SalesReturnOrderContext salesReturnOrderContext = new SalesReturnOrderContext();
        if (null == salesReturnOrderDTO) return salesReturnOrderContext;

        List<String> extraExpressNoList = getExtraExpressNoList(salesReturnOrderDTO.getReverseExpressNo());
        SalesReturnHandoverDetailParam detailParam = new SalesReturnHandoverDetailParam();
        detailParam.setExpressNoList(extraExpressNoList);
        SalesReturnHandoverDetailDTO salesReturnHandoverDetailDTO = remoteSalesReturnHandoverDetailClient.get(detailParam);
        if (null == salesReturnHandoverDetailDTO) {
            // 从已登记回到创建
            if (RSOrderStatusEnum.REGISTERED.getCode().equals(salesReturnOrderDTO.getStatus())) {
                salesReturnOrderDTO.setStatus(RSOrderStatusEnum.CREATED.getCode());
                removeNode(salesReturnOrderDTO, RSOrderStatusEnum.REGISTERED);
                salesReturnOrderContext.setSalesReturnOrderDTOList(ListUtil.toList(salesReturnOrderDTO));
            }
            return salesReturnOrderContext;
        }

        SalesReturnHandoverParam salesReturnHandoverParam = new SalesReturnHandoverParam();
        salesReturnHandoverParam.setHandoverNo(salesReturnHandoverDetailDTO.getHandoverNo());
        SalesReturnHandoverDTO salesReturnHandoverDTO = remoteSalesReturnHandoverClient.get(salesReturnHandoverParam);
        if (null == salesReturnHandoverDTO) return salesReturnOrderContext;


        List<BillLogDTO> billLogDTOList = new ArrayList<>();
        List<MessageMqDTO> messageMqDTOList = new ArrayList<>();

        // 创建到登记
        if (RSOrderStatusEnum.CREATED.getCode().equals(salesReturnOrderDTO.getStatus())
                && RSHandoverStatusEnum.DOING.getCode().equals(salesReturnHandoverDTO.getStatus())) {

            salesReturnOrderDTO.setStatus(RSOrderStatusEnum.REGISTERED.getCode());
            buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());

            billLogDTOList.add(billLogDTO(salesReturnOrderDTO, "到仓登记", "运单号：" + salesReturnHandoverDetailDTO.getExpressNo(), opBy));

            salesReturnOrderContext.setBillLogDTOList(billLogDTOList);
            salesReturnOrderContext.setSalesReturnOrderDTOList(ListUtil.toList(salesReturnOrderDTO));
            return salesReturnOrderContext;
        }

        // 创建到已交接
        if (RSOrderStatusEnum.CREATED.getCode().equals(salesReturnOrderDTO.getStatus())
                && RSHandoverStatusEnum.DONE.getCode().equals(salesReturnHandoverDTO.getStatus())) {

            // 匹配到交接完的也要增加操作记录和节点展示
            salesReturnOrderDTO.setStatus(RSOrderStatusEnum.REGISTERED.getCode());
            buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
            billLogDTOList.add(billLogDTO(salesReturnOrderDTO, "到仓登记", "运单号：" + salesReturnHandoverDetailDTO.getExpressNo(), opBy));

            salesReturnOrderDTO.setStatus(RSOrderStatusEnum.HANDOVER.getCode());
            salesReturnOrderDTO.setHandoverTime(System.currentTimeMillis());
            buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
            List<MessageMqDTO> handoverMessage = messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_ARRIVE_CALLBACK);
            messageMqDTOList.addAll(handoverMessage);
            if (RSRejectEnum.YES.getCode().equals(salesReturnHandoverDetailDTO.getReject())) {
                salesReturnOrderDTO.setStatus(RSOrderStatusEnum.REJECT.getCode());
                List<MessageMqDTO> rejectMessage = messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_REJECT_CALLBACK);
                messageMqDTOList.addAll(rejectMessage);
                List<MessageMqDTO> rejectNotifyOrigin = messageMqList(salesReturnOrderDTO, MessageTypeEnum.OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN);
                messageMqDTOList.addAll(rejectNotifyOrigin);
                buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
                billLogDTOList.add(billLogDTO(salesReturnOrderDTO, "已拒收", "", opBy));
            }

            billLogDTOList.add(billLogDTO(salesReturnOrderDTO, "交接完成", StrUtil.join(StrUtil.EMPTY, "交接单号：", salesReturnHandoverDTO.getHandoverNo(),
                    "，是否破损：", RSDamageEnum.desc(salesReturnHandoverDetailDTO.getDamage()), "，是否拒收：",
                    RSRejectEnum.desc(salesReturnHandoverDetailDTO.getReject())), opBy));

            // 还需要判断多货登记
            SalesReturnExtraParam salesReturnExtraParam = new SalesReturnExtraParam();
            salesReturnExtraParam.setExpressNoList(extraExpressNoList);
            SalesReturnExtraDTO salesReturnExtraDTO = remoteSalesReturnExtraClient.get(salesReturnExtraParam);
            if (null != salesReturnExtraDTO) {
                if (SalesReturnExtraStatusEnum.RETURN.getCode().equals(salesReturnExtraDTO.getStatus())) {
                    salesReturnOrderDTO.setStatus(RSOrderStatusEnum.OUT.getCode());
                    salesReturnOrderDTO.setOutType(RSOutEnum.RETURN.getCode());
                    buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
                    billLogDTOList.add(billLogDTO(salesReturnOrderDTO, "退货仓出库", "出库类型：" + RSOutEnum.RETURN.getMessage(), opBy));
                }
                if (SalesReturnExtraStatusEnum.DUMP.getCode().equals(salesReturnExtraDTO.getStatus())) {
                    salesReturnOrderDTO.setStatus(RSOrderStatusEnum.OUT.getCode());
                    salesReturnOrderDTO.setOutType(RSOutEnum.SCRAP.getCode());
                    buildExtraJson(salesReturnOrderDTO, System.currentTimeMillis());
                    billLogDTOList.add(billLogDTO(salesReturnOrderDTO, "退货仓出库", "出库类型：" + RSOutEnum.SCRAP.getMessage(), opBy));
                }
            }

            salesReturnOrderContext.setBillLogDTOList(billLogDTOList);
            salesReturnOrderContext.setMessageMqDTOList(messageMqDTOList);
            salesReturnOrderContext.setSalesReturnOrderDTOList(ListUtil.toList(salesReturnOrderDTO));
            return salesReturnOrderContext;
        }

        return salesReturnOrderContext;
    }

    @Override
    public BillLogDTO billLogDTO(SalesReturnOrderDTO salesReturnOrderDTO, String content, String remark, String opBy) {
        BillLogDTO billLogDTO = new BillLogDTO();
        billLogDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        billLogDTO.setBillType(BillLogTypeEnum.RS_SALES_ORDER.getType());
        billLogDTO.setOpBy(opBy);
        billLogDTO.setOpDate(System.currentTimeMillis());
        billLogDTO.setOpContent(content);
        billLogDTO.setOpRemark(remark);
        return billLogDTO;
    }

    @Override
    public void syncToERP(SalesReturnOrderDTO salesReturnOrderDTO) {

        if (null == salesReturnOrderDTO) return;
        RpcContextUtil.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());

        SalesReturnOrderParam salesReturnOrderParam1 = new SalesReturnOrderParam();
        salesReturnOrderParam1.setId(salesReturnOrderDTO.getId());
        salesReturnOrderDTO = get(salesReturnOrderParam1);

        if (salesReturnOrderDTO == null) return;
        if (StrUtil.isNotBlank(salesReturnOrderDTO.getAsnId())) return;

        // 插入本地消息
        MessageMqParam messageMqParam = new MessageMqParam();
        messageMqParam.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        messageMqParam.setOperationType(MessageTypeEnum.OPERATION_SALE_RETURN_SYNC_ERP.getType());
        List<MessageMqDTO> messageMqDTOList = remoteMessageMqClient.getList(messageMqParam);
        if (CollectionUtil.isEmpty(messageMqDTOList)) {
            MessageMqDTO messageMqDTO = new MessageMqDTO();
            messageMqDTO.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
            messageMqDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
            messageMqDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
            messageMqDTO.setOperationType(MessageTypeEnum.OPERATION_SALE_RETURN_SYNC_ERP.getType());
            messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RS_ORDER.getType());
            messageMqDTO.setCreatedTime(System.currentTimeMillis());
            messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
            remoteMessageMqClient.save(messageMqDTO);
        }

        String warehouseCode = salesReturnOrderDTO.getWarehouseCode();
        String logicWarehouseCode = salesReturnOrderDTO.getLogicWarehouseCode();
        String cargoCode = salesReturnOrderDTO.getCargoCode();
        String saleShopName = salesReturnOrderDTO.getSaleShopName();
        String salesReturnOrderNo = salesReturnOrderDTO.getSalesReturnOrderNo();
        String poNo = salesReturnOrderDTO.getPoNo();

        SalesReturnOrderExtraDTO salesReturnOrderExtraDTO = salesReturnOrderDTO.salesReturnOrderExtraDTO();
        List<String> noList = salesReturnOrderExtraDTO.getNoListOutAtSameTime();
        if (CollectionUtil.isEmpty(noList)) noList = ListUtil.toList(salesReturnOrderNo);
        String taxType = salesReturnOrderDTO.getTaxType();
        if (TaxTypeEnum.TYPE_BONDED_TAX.getCode().equalsIgnoreCase(taxType))
            noList = ListUtil.toList(salesReturnOrderNo);
        List<String> finalNoList = noList;

        if (null == salesReturnOrderExtraDTO.getNoListOutAtSameTime()) {
            salesReturnOrderExtraDTO.setNoListOutAtSameTime(ListUtil.toList(salesReturnOrderNo));
        }
        List<String> lockKeys = salesReturnOrderExtraDTO.getNoListOutAtSameTime().stream()
                .map(it -> StrUtil.join(StrUtil.COLON, warehouseCode, it)).collect(Collectors.toList());
        String expressNo = salesReturnOrderDTO.getExpressNo();
        String reverseExpressNo = salesReturnOrderDTO.getReverseExpressNo();
        String reverseCarrierName = salesReturnOrderDTO.getReverseCarrierName();
        String reverseCarrierCode = salesReturnOrderDTO.getReverseCarrierCode();
        remoteLockSupportClient.execute(() -> {
            SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
            salesReturnOrderParam.setSalesReturnOrderNoList(finalNoList);
            salesReturnOrderParam.setBillSource(RSBillSourceEnum.OMS.getCode());
            List<SalesReturnOrderDTO> salesReturnOrderDTOList = getList(salesReturnOrderParam);
            if (salesReturnOrderDTOList.stream().anyMatch(it -> StrUtil.isNotBlank(it.getAsnId()))) return null;

            SalesReturnOrderReceiveParam salesReturnOrderReceiveParam = new SalesReturnOrderReceiveParam();
            salesReturnOrderReceiveParam.setSalesReturnOrderNoList(finalNoList);
            List<SalesReturnOrderReceiveDTO> list = remoteSalesReturnOrderReceiveClient.getList(salesReturnOrderReceiveParam);
            if (CollectionUtil.isEmpty(list)) return null;


            InOrderAddRpcParam inOrderAddRpcParam = new InOrderAddRpcParam();
            inOrderAddRpcParam.setOrigSystem("WMS");
            inOrderAddRpcParam.setBusinessNo(poNo);
            inOrderAddRpcParam.setExternalNo(salesReturnOrderNo);
            inOrderAddRpcParam.setType(3);
            inOrderAddRpcParam.setLogicWarehouseCode(logicWarehouseCode);
            inOrderAddRpcParam.setOwnerCode(cargoCode);
            inOrderAddRpcParam.setIsPush(1);
            inOrderAddRpcParam.setBackStatus(0);
            inOrderAddRpcParam.setApprovalStatus(20);
            inOrderAddRpcParam.setShopName(saleShopName);
            if (TaxTypeEnum.TYPE_BONDED_TAX.getCode().equalsIgnoreCase(taxType)) {
                inOrderAddRpcParam.setLogisticsNo(expressNo);
            }
            ReceiveSendInfoAddRpcParam receiveSendInfoParam = new ReceiveSendInfoAddRpcParam();
            receiveSendInfoParam.setLogisticsNo(reverseExpressNo);
            receiveSendInfoParam.setLogisticsCompany(reverseCarrierName);
            receiveSendInfoParam.setLogisticsCompanyCode(reverseCarrierCode);
            inOrderAddRpcParam.setReceiveSendInfoParam(receiveSendInfoParam);

            List<InOrderDetailAddRpcParam> inOrderDetailParamList = new ArrayList<>();
            for (SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO : list) {
                if (TaxTypeEnum.TYPE_BONDED_TAX.getCode().equalsIgnoreCase(taxType) && RSSecondEntryEnum.NO.getCode().equals(salesReturnOrderReceiveDTO.getSecondEntry())) {
                    continue;
                }
                InOrderDetailAddRpcParam inOrderDetailAddRpcParam = new InOrderDetailAddRpcParam();
                inOrderDetailAddRpcParam.setGoodsName(salesReturnOrderReceiveDTO.getSkuName());
                inOrderDetailAddRpcParam.setSku(salesReturnOrderReceiveDTO.getSkuCode());
                inOrderDetailAddRpcParam.setPlanQuantity(salesReturnOrderReceiveDTO.getQty().intValue());
                inOrderDetailParamList.add(inOrderDetailAddRpcParam);
            }
            inOrderAddRpcParam.setInOrderDetailParamList(inOrderDetailParamList);
            String asnId;
            try {
                log.info("salesReturnOrderNotifyERP param {}", JSONUtil.toJsonStr(inOrderAddRpcParam));
                remoteTenantHelper.setTenantIdV2(warehouseCode, "");
                asnId = inOrderRpcFacade.addInOrder(inOrderAddRpcParam);
                log.info("salesReturnOrderNotifyERP result {}", asnId);
            } catch (BusinessException exception) {
                log.error(exception.getMessage(), exception);
                throw exception;
            }

            List<BillLogDTO> billLogDTOList = new ArrayList<>();
            if (StrUtil.isNotBlank(asnId)) {
                for (SalesReturnOrderDTO returnOrderDTO : salesReturnOrderDTOList) {
                    returnOrderDTO.setAsnId(asnId);
                    if (TaxTypeEnum.TYPE_BONDED_TAX.getCode().equalsIgnoreCase(returnOrderDTO.getTaxType())) {
                        returnOrderDTO.setOutType(RSOutEnum.BONDED.getCode());
                        returnOrderDTO.setShelfType(RSShelfEnum.BONDED_WAREHOUSE_SHELF.getCode());
                    }
                    returnOrderDTO.setStatus(RSOrderStatusEnum.OUT.getCode());
                    buildExtraJson(returnOrderDTO, System.currentTimeMillis());
                    billLogDTOList.add(billLogDTO(returnOrderDTO, "退货仓出库", "出库类型：" + RSOutEnum.fromInt(returnOrderDTO.getOutType()).getMessage(), "系统"));
                }
                modifyBatch(salesReturnOrderDTOList);
                if (CollectionUtil.isNotEmpty(billLogDTOList)) {
                    remoteBillLogClient.saveBatch(billLogDTOList);
                }
            }

            return null;
        }, lockKeys);
    }

    @Override
    public void syncToCCSMessage(SalesReturnOrderDTO salesReturnOrderDTO) {
        if (salesReturnOrderDTO == null) return;
        RpcContextUtil.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
        if (TaxTypeEnum.TYPE_DUTY_TAX.getCode().equalsIgnoreCase(salesReturnOrderDTO.getTaxType())) return;
        if (!RSOrderStatusEnum.CHECK_END.getCode().equals(salesReturnOrderDTO.getStatus())) return;
        if (RSInspectionEnum.FAIL.getCode().equals(salesReturnOrderDTO.getInspectionResult())) return;
        if (!RSBillSourceEnum.OMS.getCode().equals(salesReturnOrderDTO.getBillSource())) return;
        // 插入本地消息
        MessageMqParam messageMqParam = new MessageMqParam();
        messageMqParam.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        messageMqParam.setOperationType(MessageTypeEnum.OPERATION_SALE_RETURN_SYNC_CCS.getType());
        List<MessageMqDTO> messageMqDTOList = remoteMessageMqClient.getList(messageMqParam);
        if (CollectionUtil.isEmpty(messageMqDTOList)) {
            MessageMqDTO messageMqDTO = new MessageMqDTO();
            messageMqDTO.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
            messageMqDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
            messageMqDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
            messageMqDTO.setOperationType(MessageTypeEnum.OPERATION_SALE_RETURN_SYNC_CCS.getType());
            messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RS_ORDER.getType());
            messageMqDTO.setCreatedTime(System.currentTimeMillis());
            messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
            remoteMessageMqClient.save(messageMqDTO);
        }
    }

    @Override
    public List<MessageMqDTO> messageMqList(SalesReturnOrderDTO salesReturnOrderDTO, MessageTypeEnum messageTypeEnum) {
        RSBillSourceEnum billSource = RSBillSourceEnum.fromInt(salesReturnOrderDTO.getBillSource());
        switch (billSource) {
            case MALL_DIRECT:
            case MALL_PLATFORM:
                return messageMqListForTaoTian(salesReturnOrderDTO, messageTypeEnum);
            case DY_MARKET:
                return messageMqListForDyMarket(salesReturnOrderDTO, messageTypeEnum);
            default:
                return ListUtil.empty();
        }
    }

    public List<MessageMqDTO> messageMqListForDyMarket(SalesReturnOrderDTO salesReturnOrderDTO, MessageTypeEnum messageTypeEnum) {
        if (null == salesReturnOrderDTO) return ListUtil.empty();
        if (Objects.requireNonNull(messageTypeEnum) == MessageTypeEnum.OPERATION_SALE_RETURN_SHELF_CALLBACK) {
            return ListUtil.toList(messageMqDTO(salesReturnOrderDTO, messageTypeEnum));
        }
        return ListUtil.empty();

    }

    private MessageMqDTO messageMqDTO(SalesReturnOrderDTO salesReturnOrderDTO, MessageTypeEnum messageTypeEnum) {
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
        messageMqDTO.setCargoCode(salesReturnOrderDTO.getCargoCode());
        messageMqDTO.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        messageMqDTO.setOperationType(messageTypeEnum.getType());
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RS_ORDER.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        return messageMqDTO;
    }

    public List<MessageMqDTO> messageMqListForTaoTian(SalesReturnOrderDTO salesReturnOrderDTO, MessageTypeEnum messageTypeEnum) {
        if (null == salesReturnOrderDTO) return ListUtil.empty();
        // 只有配拦截或拒签才需要回告正向
        if (MessageTypeEnum.OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN.equals(messageTypeEnum)
                || MessageTypeEnum.OPERATION_SALE_RETURN_RECEIVE_CALLBACK_ORIGIN.equals(messageTypeEnum)) {
            if (Stream.of(RSReturnTypeEnum.CUSTOMER_REJECT.getCode(), RSReturnTypeEnum.TMS_CUT.getCode()).noneMatch(it -> it.equals(salesReturnOrderDTO.getReturnType()))) {
                return ListUtil.empty();
            }
        }

        return ListUtil.toList(messageMqDTO(salesReturnOrderDTO, messageTypeEnum));
    }

    @Override
    public List<MessageMqDTO> messageMqList(String warehouseCode, String expressNo, MessageTypeEnum messageTypeEnum) {
        if (StrUtil.isBlank(expressNo)) return ListUtil.empty();
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(warehouseCode);
        messageMqDTO.setCargoCode("");
        messageMqDTO.setBillNo(expressNo);
        messageMqDTO.setOperationType(messageTypeEnum.getType());
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RS_ORDER.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        return ListUtil.toList(messageMqDTO);

    }

    @Override
    public void callback(MessageMqDTO messageMqDTO) {
        try {
            CallUnderOtherContext.execute(() -> {
                MessageMqParam messageMqParam = new MessageMqParam();
                messageMqParam.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
                messageMqParam.setBillNo(messageMqDTO.getBillNo());
                messageMqParam.setOperationType(messageMqDTO.getOperationType());
                List<MessageMqDTO> messageMqDTOS = remoteMessageMqClient.getList(messageMqParam);
                for (MessageMqDTO mqDTO : messageMqDTOS) {
                    Optional<MessageTypeEnum> optional = MessageTypeEnum.optional(messageMqDTO.getOperationType());
                    optional.ifPresent(messageTypeEnum -> {
                        switch (messageTypeEnum) {
                            case OPERATION_SALE_RETURN_CREATE_CALLBACK:
                                createCallback(mqDTO);
                                break;
                            case OPERATION_SALE_RETURN_PULL:
                                pull(mqDTO);
                                break;
                            case OPERATION_SALE_RETURN_ARRIVE_CALLBACK:
                                arriveCallback(mqDTO);
                                break;
                            case OPERATION_SALE_RETURN_REJECT_CALLBACK:
                                rejectCallback(mqDTO);
                                break;
                            case OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN:
                                rejectCallbackOrigin(mqDTO);
                                break;
                            case OPERATION_SALE_RETURN_RECEIVE_CALLBACK_ORIGIN:
                                receiveCallbackOrigin(mqDTO);
                                break;
                            case OPERATION_SALE_RETURN_SHELF_CALLBACK:
                                shelfCallbackV2(mqDTO);
                                break;
                        }
                    });

                }
                return null;
            }, messageMqDTO.getWarehouseCode());
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
        }
    }

    @Override
    public void callback(List<MessageMqDTO> messageMqDTOList) {
        if (CollectionUtil.isEmpty(messageMqDTOList)) return;
        try {
            RpcContextUtil.setWarehouseCode(messageMqDTOList.get(0).getWarehouseCode());
            MessageMqParam messageMqParam = new MessageMqParam();
            messageMqParam.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
            messageMqParam.setBillNoList(messageMqDTOList.stream().map(MessageMqDTO::getBillNo).distinct().collect(Collectors.toList()));
            List<MessageMqDTO> messageMqDTOS = remoteMessageMqClient.getList(messageMqParam);
            for (MessageMqDTO messageMqDTO : messageMqDTOS) {
                Optional<MessageTypeEnum> optional = MessageTypeEnum.optional(messageMqDTO.getOperationType());
                optional.ifPresent(messageTypeEnum -> {
                    switch (messageTypeEnum) {
                        case OPERATION_SALE_RETURN_CREATE_CALLBACK:
                            createCallback(messageMqDTO);
                            break;
                        case OPERATION_SALE_RETURN_PULL:
                            pull(messageMqDTO);
                            break;
                        case OPERATION_SALE_RETURN_ARRIVE_CALLBACK:
                            arriveCallback(messageMqDTO);
                            break;
                        case OPERATION_SALE_RETURN_REJECT_CALLBACK:
                            rejectCallback(messageMqDTO);
                            break;
                        case OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN:
                            rejectCallbackOrigin(messageMqDTO);
                            break;
                        case OPERATION_SALE_RETURN_RECEIVE_CALLBACK_ORIGIN:
                            receiveCallbackOrigin(messageMqDTO);
                            break;
                        case OPERATION_SALE_RETURN_SHELF_CALLBACK:
                            shelfCallbackV2(messageMqDTO);
                            break;
                    }
                });
            }
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
        }
    }

    @ApiOperation("调用淘天拉单啊")
    public void pull(MessageMqDTO messageMqDTO) {
        List<LogisticsReturnScene> logisticsReturnScenes = remoteOmsLogisticClient.logisticInfoList(messageMqDTO.getBillNo());
        if (CollectionUtil.isEmpty(logisticsReturnScenes)) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        LogisticsReturnScene logisticsReturnScene = logisticsReturnScenes.get(0);

        WarehouseDTO warehouseDTO = remoteLogicWarehouseClient.entityWarehouseCode(logisticsReturnScene.getWarehouseCode());
        if (null == warehouseDTO) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        RSOrderPullRuleEnum rsOrderPullRuleEnum = remoteOmsLogisticClient.logisticInfo(logisticsReturnScenes);
        String warehouseCode = warehouseDTO.getCode();
        String cargoCode = logisticsReturnScene.getOwnerCode();

        // 查询正向发货货主
        CargoOwnerTaoTianDTO ownerByDtWmsWarehouseCode = remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(warehouseCode, cargoCode);
        if (null == ownerByDtWmsWarehouseCode) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        // 查询发货仓外部编码
//        CargoOwnerTaoTianDTO returnWarehouseOutInfo = CallOtherSupport.execute(it -> remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(messageMqDTO.getWarehouseCode(), it), cargoCode, "通过退货仓编码和正向货主编码查询退货仓外部编码");
        // 这地方有很大问题【没有捋清楚】
        TTDefaultOrderPullRequest ttDefaultOrderPullRequest = new TTDefaultOrderPullRequest();
        ttDefaultOrderPullRequest.setOutOtherWarehouseCode(messageMqDTO.getWarehouseCode());
        ttDefaultOrderPullRequest.setOutOtherCargoCode(ownerByDtWmsWarehouseCode.getOutOwnerCode());

        ttDefaultOrderPullRequest.setMailNo(messageMqDTO.getBillNo());
//        ttDefaultOrderPullRequest.setOrderType("THRK");
        ttDefaultOrderPullRequest.setOrderType("XTRK");
        ttDefaultOrderPullRequest.setLogisticsOwner(ownerByDtWmsWarehouseCode.getOutOwnerCode());
        ttDefaultOrderPullRequest.setWarehouseCode(messageMqDTO.getWarehouseCode());
        switch (rsOrderPullRuleEnum) {
            case PACKAGE_REJECT:
                ttDefaultOrderPullRequest.setPullScene("PACKAGE_REJECT");
                break;
            case PACKAGE_INTERCEPT:
                ttDefaultOrderPullRequest.setPullScene("PACKAGE_INTERCEPT");
                break;
            case PACKAGE_ARRIVE_STORE:
                // 这种场景不需要去拉单
                messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                remoteMessageMqClient.modify(messageMqDTO);
                return;
        }
        //设置租户 TODO ADD 2024-12-05
        ttDefaultOrderPullRequest.setTenantId(remoteTenantHelper.queryTenantId(warehouseCode, cargoCode));

        remoteMercuryClient.returnOrderPull(JSONUtil.toJsonStr(ttDefaultOrderPullRequest), messageMqDTO.getWarehouseCode());

        // 接口调通就OK
        messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
        remoteMessageMqClient.modify(messageMqDTO);
    }

    @ApiOperation("上架回调")
    public void shelfCallbackV2(MessageMqDTO messageMqDTO) {
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(messageMqDTO.getBillNo());
        SalesReturnOrderDTO salesReturnOrderDTO = get(salesReturnOrderParam);
        messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
        if (null == salesReturnOrderDTO) {
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        RSBillSourceEnum billSource = RSBillSourceEnum.fromInt(salesReturnOrderDTO.getBillSource());
        switch (billSource) {
            case DY_MARKET:
                shelfCallbackForDyMarket(messageMqDTO, salesReturnOrderDTO);
                return;
            case MALL_DIRECT:
            case MALL_PLATFORM:
                shelfCallback(messageMqDTO);
                return;
            default:
        }
    }

    public void shelfCallbackForDyMarket(MessageMqDTO messageMqDTO, SalesReturnOrderDTO salesReturnOrderDTO) {
        SalesReturnCallbackRequest request = new SalesReturnCallbackRequest();
        request.setInboundOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        request.setStatus(3);
        request.setWarehouseCode(salesReturnOrderDTO.salesReturnOrderExtraDTO().getDyMarketWarehouseCode());
        request.setOperator(salesReturnOrderDTO.salesReturnOrderExtraDTO().getInspectionBy());
        request.setOperateTime(DateTime.of(salesReturnOrderDTO.getInspectionTime()).getTime() / 1000);
        request.setOwnerCode(salesReturnOrderDTO.getCargoCode());

        SalesReturnOrderReceiveParam receiveParam = new SalesReturnOrderReceiveParam();
        receiveParam.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        List<SalesReturnOrderReceiveDTO> receiveDTOList = remoteSalesReturnOrderReceiveClient.getList(receiveParam);
        if (CollectionUtil.isEmpty(receiveDTOList)) {
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        ShelfParam param = new ShelfParam();
        param.setBillNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        List<ShelfDTO> list = remoteShelfClient.getList(param);
        if (CollectionUtil.isEmpty(list)) return;
        if (list.stream().anyMatch(shelfDTO -> !shelfDTO.getStatus().equalsIgnoreCase(ShelfStatusEnum.STATUS_COMPLETED.getStatus()))) {
            return;
        }

        SalesReturnOrderDetailParam detailParam = new SalesReturnOrderDetailParam();
        detailParam.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        List<SalesReturnOrderDetailDTO> detailDTOList = remoteSalesReturnOrderDetailClient.getList(detailParam);
        if (CollectionUtil.isEmpty(detailDTOList)) {
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        ArrayList<SalesReturnCallbackDetail> detailList = new ArrayList<>();
        List<String> skuCodeList = detailDTOList.stream().map(SalesReturnOrderDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        for (String skuCode : skuCodeList) {
                List<SalesReturnOrderReceiveDTO> receiveGrouped = receiveDTOList.stream()
                        .filter(it -> it.getSkuCode().equalsIgnoreCase(skuCode))
                        .collect(Collectors.toList());
                List<SalesReturnOrderDetailDTO> detailGrouped = detailDTOList.stream()
                        .filter(it -> it.getSkuCode().equalsIgnoreCase(skuCode))
                        .collect(Collectors.toList());
                Stack<SalesReturnOrderReceiveDTO> receiveDTOStack = new Stack<>();
                receiveDTOStack.addAll(receiveGrouped);
                for (SalesReturnOrderDetailDTO salesReturnOrderDetailDTO : detailGrouped) {
                    BigDecimal totalQty = salesReturnOrderDetailDTO.getExpectQty();
                    SalesReturnCallbackDetail callbackDetail = new SalesReturnCallbackDetail();
                    callbackDetail.setInboundOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
                    callbackDetail.setInventoryStatus(1);
                    callbackDetail.setOwnerCode(salesReturnOrderDTO.getCargoCode());
                    callbackDetail.setLineNo(salesReturnOrderDetailDTO.getLineSeq());
                    callbackDetail.setTotalQty(totalQty);
                    while (totalQty.compareTo(BigDecimal.ZERO) > 0) {
                        SalesReturnCallbackDetail detail = new SalesReturnCallbackDetail();
                        BeanUtil.copyProperties(callbackDetail, detail);
                        if (receiveDTOStack.isEmpty()) {
                            break;
                        }
                        SalesReturnOrderReceiveDTO receive = receiveDTOStack.pop();
                        Integer quality = receive.getSkuQuality().equals(SkuQualityEnum.SKU_QUALITY_AVL.getLevel()) ? 1 : 2;
                        detail.setInventoryType(quality);
                        if (SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel().equalsIgnoreCase(receive.getSkuQuality())) {
                            detail.setDefectReasonMessage(receive.getDamageReason());
                        }
                        BigDecimal qty = receive.getQty();
                        if (totalQty.compareTo(qty) > 0) {
                            detail.setReceivedQty(qty);
                            totalQty = totalQty.subtract(qty);
                            detailList.add(detail);
                        } else if (totalQty.compareTo(qty) < 0) {
                            detail.setReceivedQty(totalQty);
                            receive.setQty(receive.getQty().subtract(totalQty));
                            totalQty = BigDecimal.ZERO;
                            detailList.add(detail);
                            receiveDTOStack.push(receive);
                        }else {
                            detail.setReceivedQty(totalQty);
                            totalQty = BigDecimal.ZERO;
                            detailList.add(detail);
                        }
                    }
                    // 没有收回告零
                    if (totalQty.compareTo(salesReturnOrderDetailDTO.getExpectQty()) == 0) {
                        SalesReturnCallbackDetail detail = new SalesReturnCallbackDetail();
                        BeanUtil.copyProperties(callbackDetail, detail);
                        detail.setInventoryType(1);
                        detail.setReceivedQty(BigDecimal.ZERO);
                        detailList.add(detail);
                    }
                }
            }

        for (SalesReturnCallbackDetail salesReturnCallbackDetail : detailList) {
            BigDecimal reduce = detailList.stream()
                    .filter(it -> it.getLineNo().equalsIgnoreCase(salesReturnCallbackDetail.getLineNo()))
                    .map(SalesReturnCallbackDetail::getReceivedQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            salesReturnCallbackDetail.setTotalQty(reduce);
        }
        request.setDetailList(detailList);

        log.info("dy market callback {}", JSONUtil.toJsonStr(request));
        Result<String> callback = salesReturnCallback.callback(request);
        log.info("dy market callback result {}", JSONUtil.toJsonStr(callback));
        if (callback.checkSuccess()) {
            BillLogDTO billLogDTO = billLogDTO(salesReturnOrderDTO, "销退单上架回告成功", "销退单上架回告成功", "系统");
            remoteBillLogClient.save(billLogDTO);
            remoteMessageMqClient.modify(messageMqDTO);
        }

    }

    private void shelfCallback(MessageMqDTO messageMqDTO) {
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(messageMqDTO.getBillNo());
        SalesReturnOrderDTO salesReturnOrderDTO = get(salesReturnOrderParam);
        messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
        if (null == salesReturnOrderDTO) {
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        SalesReturnOrderReceiveParam salesReturnOrderReceiveParam = new SalesReturnOrderReceiveParam();
        salesReturnOrderReceiveParam.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        List<SalesReturnOrderReceiveDTO> receiveDTOList = remoteSalesReturnOrderReceiveClient.getList(salesReturnOrderReceiveParam);

        SalesReturnOrderDetailParam salesReturnOrderDetailParam = new SalesReturnOrderDetailParam();
        salesReturnOrderDetailParam.setSalesReturnOrderNo(salesReturnOrderDTO.getSalesReturnOrderNo());
        List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList = remoteSalesReturnOrderDetailClient.getList(salesReturnOrderDetailParam);

        if (CollectionUtil.isEmpty(salesReturnOrderDetailDTOList) || CollectionUtils.isEmpty(receiveDTOList)) {
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        CargoOwnerTaoTianDTO ownerByDtWmsWarehouseCode = remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(salesReturnOrderDTO.getWarehouseCode(), salesReturnOrderDTO.getCargoCode());

        TTReturnOrderConfirmRequest request = new TTReturnOrderConfirmRequest();
        request.setDtWmsWarehouseCode(messageMqDTO.getWarehouseCode());
        request.setDtWmsCargoCode(messageMqDTO.getCargoCode());

        request.setTenantId(remoteTenantHelper.queryTenantId(salesReturnOrderDTO.getWarehouseCode(), salesReturnOrderDTO.getCargoCode()));

        TTReturnOrderConfirmRequest.ReturnOrder returnOrder = new TTReturnOrderConfirmRequest.ReturnOrder();
        returnOrder.setReturnOrderCode(salesReturnOrderDTO.getSalesReturnOrderNo());
        returnOrder.setReturnOrderId(salesReturnOrderDTO.getSalesReturnOrderNo());
        returnOrder.setWarehouseCode(ownerByDtWmsWarehouseCode.getOutWarehouseCode());
        returnOrder.setOutBizCode(salesReturnOrderDTO.getSalesReturnOrderNo());
        returnOrder.setOrderType("THRK");
        returnOrder.setOrderConfirmTime(DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
        returnOrder.setLogisticsCode(salesReturnOrderDTO.getReverseCarrierCode());
        returnOrder.setOwnerCode(ownerByDtWmsWarehouseCode.getOutOwnerCode());
        returnOrder.setConfirmType(0L);

        TTReturnOrderConfirmRequest.SenderInfo senderInfo = new TTReturnOrderConfirmRequest.SenderInfo();
        senderInfo.setConfirmType(0L);
        returnOrder.setSenderInfo(senderInfo);
        request.setReturnOrder(returnOrder);

        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(receiveDTOList.stream().map(SalesReturnOrderReceiveDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
        Map<String, SkuLotDTO> skuLotDTOMap = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity()));

        ArrayList<OrderLine> orderLines = new ArrayList<>();

        // 销退明细按商品分组
        Map<String, List<SalesReturnOrderDetailDTO>> detailGroupBySku = salesReturnOrderDetailDTOList.stream()
                .collect(Collectors.groupingBy(SalesReturnOrderDetailDTO::getSkuCode));
        Map<String, List<SalesReturnOrderReceiveDTO>> receiveGroupBySku = receiveDTOList.stream()
                .collect(Collectors.groupingBy(SalesReturnOrderReceiveDTO::getSkuCode));
        for (String skuCode : detailGroupBySku.keySet()) {
            Optional.ofNullable(receiveGroupBySku.get(skuCode)).ifPresent(returnOrderReceiveDTOS -> {
                Stack<SalesReturnOrderReceiveDTO> stack = new Stack<>();
                stack.addAll(returnOrderReceiveDTOS);
                for (SalesReturnOrderDetailDTO salesReturnOrderDetailDTO : detailGroupBySku.get(skuCode)) {
                    List<TTReturnOrderConfirmRequest.Batch> batchList = new ArrayList<>();
                    //sn
                    TTReturnOrderConfirmRequest.SnList snList = new TTReturnOrderConfirmRequest.SnList();
                    List<String> sn = new ArrayList<>();
                    OrderLine orderLine = new OrderLine();
                    orderLine.setBatchCode(salesReturnOrderDetailDTO.getSkuLotNo());
                    orderLine.setOwnerCode(ownerByDtWmsWarehouseCode.getOutOwnerCode());
                    orderLine.setItemCode(salesReturnOrderDetailDTO.getSkuCode());
                    orderLine.setOrderLineNo(salesReturnOrderDetailDTO.getLineSeq());
                    orderLine.setPlanQty(salesReturnOrderDetailDTO.getExpectQty().longValue());
                    orderLine.setActualQty(BigDecimal.ZERO.toPlainString());

                    // 收货明细还有 且 明细计划数量大于实收数量
                    while (!stack.isEmpty() && BigDecimal.valueOf(orderLine.getPlanQty()).compareTo(new BigDecimal(orderLine.getActualQty())) > 0) {
                        SalesReturnOrderReceiveDTO salesReturnOrderReceiveDTO = stack.pop();
                        sn.add(salesReturnOrderReceiveDTO.getSn());
                        TTReturnOrderConfirmRequest.Batch batch = new TTReturnOrderConfirmRequest.Batch();
                        Map<String, Object> extendProps = new HashMap<>();
                        batch.setBatchCode(salesReturnOrderReceiveDTO.getSkuLotNo());
                        // 当前明细还需要的数量
                        BigDecimal need = BigDecimal.valueOf(orderLine.getPlanQty()).subtract(new BigDecimal(orderLine.getActualQty()));
                        if (salesReturnOrderReceiveDTO.getQty().compareTo(need) == 0) {
                            batch.setActualQty(need.longValue());
                            orderLine.setActualQty(orderLine.getPlanQty().toString());
                        } else if (salesReturnOrderReceiveDTO.getQty().compareTo(need) > 0) {
                            batch.setActualQty(need.longValue());
                            orderLine.setActualQty(orderLine.getPlanQty().toString());
                            salesReturnOrderReceiveDTO.setQty(salesReturnOrderReceiveDTO.getQty().subtract(need));
                            stack.push(salesReturnOrderReceiveDTO);
                        } else {
                            batch.setActualQty(salesReturnOrderReceiveDTO.getQty().longValue());
                            orderLine.setActualQty(new BigDecimal(orderLine.getActualQty()).add(salesReturnOrderReceiveDTO.getQty()).toPlainString());
                        }
                        Optional.ofNullable(skuLotDTOMap.get(salesReturnOrderReceiveDTO.getSkuLotNo())).ifPresent(skuLotDTO -> {
                            batch.setBatchCode(skuLotDTO.getCode());
                            batch.setProductDate(DateDescUtil.normalDateStr(skuLotDTO.getManufDate()));
                            batch.setExpireDate(DateDescUtil.normalDateStr(skuLotDTO.getExpireDate()));
                            batch.setProduceCode(skuLotDTO.getProductionNo());
                        });

                        batch.setInventoryType(salesReturnOrderReceiveDTO.getInventoryType());
                        if (RSSecondEntryEnum.YES.getCode().equals(salesReturnOrderReceiveDTO.getSecondEntry())) {
                            extendProps.put("planEntryBondedQty", batch.getActualQty());
                            extendProps.put("unbondedReason", "");
                        } else {
                            extendProps.put("planEntryBondedQty", BigDecimal.ZERO);
                            extendProps.put("unbondedReason", salesReturnOrderReceiveDTO.getSecondEntryFailReason());
                        }
                        batch.setExtendProps(extendProps);
                        batchList.add(batch);
                    }
                    snList.setSn(sn.stream().distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList()));
                    orderLine.setSnList(snList);
                    orderLine.setBatchs(mergeBatchs(batchList));
                    orderLines.add(orderLine);
                }
            });
        }

        // 合并
        List<OrderLine> collect = orderLines.stream()
                .collect(Collectors.groupingBy(it -> StrUtil.join(StrUtil.COLON, it.getItemCode(), it.getOrderLineNo()), Collectors.reducing(this::merge))).values().stream().filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        // 过滤掉实收为零的行
        collect = collect.stream()
                .filter(orderLine -> !orderLine.getActualQty().equalsIgnoreCase(BigDecimal.ZERO.toPlainString()))
                .collect(Collectors.toList());

        request.setOrderLines(collect);

        try {
            //调用mercury
            CallOtherSupport.execute(it -> remoteMercuryClient.shelfCallback(JSONUtil.toJsonStr(it), messageMqDTO.getWarehouseCode()), request, "销退单上架回告");
            RpcContextUtil.setWarehouseCode(salesReturnOrderDTO.getWarehouseCode());
            // 回告失败会抛出异常
            BillLogDTO billLogDTO = billLogDTO(salesReturnOrderDTO, "销退单上架回告成功", "销退单上架回告成功", "系统");
            remoteBillLogClient.save(billLogDTO);

            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
        } catch (Exception exception) {
            if (null != wmsOtherConfig && CollectionUtil.isNotEmpty(wmsOtherConfig.getTaoTianCallbackWarningUrlList())) {
                String message = StrUtil.join(StrUtil.EMPTY, salesReturnOrderDTO.getWarehouseCode(), "淘天销退单", salesReturnOrderDTO.getSalesReturnOrderNo(), "销退单上架回告", "淘天返回:", exception.getMessage());
                sendMessage(message);
            }
            log.error(exception.getMessage(), exception);
        }
    }

    private boolean sendMessage(String message) {
        if (CollectionUtil.isNotEmpty(wmsOtherConfig.getTaoTianCallbackWarningMessageList()) && wmsOtherConfig.getTaoTianCallbackWarningMessageList().stream().anyMatch(message::contains)) {
            if (CollectionUtil.isNotEmpty(wmsOtherConfig.getTaoTianCallbackExcludeWarehouseList()) && wmsOtherConfig.getTaoTianCallbackExcludeWarehouseList().contains(CurrentRouteHolder.getWarehouseCode())) {
                return true;
            }
            WechatUtil.sendMessage(message, wmsOtherConfig.getTaoTianCallbackWarningUrlList(), String.join(StrUtil.COMMA, wmsOtherConfig.getTaoTianCallbackWarningMobile()));
            return true;
        } else {
            WechatUtil.sendMessage(message, wmsOtherConfig.getTaoTianCallbackSystemWarningUrlList());
        }
        return false;
    }

    private OrderLine merge(OrderLine first, OrderLine second) {
        first.setPlanQty(BigDecimal.valueOf(first.getPlanQty()).add(BigDecimal.valueOf(second.getPlanQty())).longValue());
        first.setActualQty(new BigDecimal(first.getActualQty()).add(new BigDecimal(second.getActualQty())).toPlainString());
        // 批次合并
        List<TTReturnOrderConfirmRequest.Batch> batches = CollectionUtil.unionAll(first.getBatchs(), second.getBatchs());
        List<TTReturnOrderConfirmRequest.Batch> batchList = mergeBatchs(batches);

        first.setBatchs(batchList);
        // sn 合并
        TTReturnOrderConfirmRequest.SnList snList = new TTReturnOrderConfirmRequest.SnList();
        snList.setSn(CollectionUtil.unionAll(first.getSnList().getSn(), second.getSnList().getSn()).stream().distinct().collect(Collectors.toList()));
        first.setSnList(snList);
        return first;
    }

    private List<TTReturnOrderConfirmRequest.Batch> mergeBatchs(List<TTReturnOrderConfirmRequest.Batch> batches) {
        List<TTReturnOrderConfirmRequest.Batch> batchList = batches.stream()
                .collect(Collectors.groupingBy(it -> StrUtil.join(StrUtil.COLON, it.getBatchCode(), it.getInventoryType()),
                        Collectors.reducing((batch, batch2) -> {
                            batch.setActualQty(batch.getActualQty() + batch2.getActualQty());
                            BigDecimal planEntryBondedQty = new BigDecimal(batch.getExtendProps().get("planEntryBondedQty").toString());
                            BigDecimal planEntryBondedQty1 = new BigDecimal(batch2.getExtendProps().get("planEntryBondedQty").toString());
                            batch.getExtendProps().put("planEntryBondedQty", planEntryBondedQty.add(planEntryBondedQty1));
                            String unbondedReason = JSONUtil.parseObj(batch.getExtendProps()).getStr("unbondedReason", StrUtil.EMPTY);
                            String unbondedReason2 = JSONUtil.parseObj(batch2.getExtendProps()).getStr("unbondedReason", StrUtil.EMPTY);
                            batch.getExtendProps().put("unbondedReason", Stream.of(unbondedReason, unbondedReason2)
                                    .filter(StrUtil::isNotBlank).collect(Collectors.joining(StrUtil.COMMA)));
                            return batch;
                        }))).values().stream().filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        return batchList;
    }

    @ApiOperation("创建回调")
    public void createCallback(MessageMqDTO messageMqDTO) {
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(messageMqDTO.getBillNo());
        SalesReturnOrderDTO salesReturnOrderDTO = get(salesReturnOrderParam);
        if (null == salesReturnOrderDTO) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }


        Map<String, Object> orderMap = new HashMap<>();

        CargoOwnerTaoTianDTO ownerByDtWmsWarehouseCode = remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(salesReturnOrderDTO.getWarehouseCode(),
                salesReturnOrderDTO.getCargoCode());
        orderMap.put("dtWmsWarehouseCode", ownerByDtWmsWarehouseCode.getWmsWarehouseCode());
        orderMap.put("dtWmsCargoCode", ownerByDtWmsWarehouseCode.getWmsOwnerCode());

        orderMap.put("orderCode", salesReturnOrderDTO.getSalesReturnOrderNo());
        orderMap.put("orderId", salesReturnOrderDTO.getSalesReturnOrderNo());
        orderMap.put("orderType", "THRK");
        orderMap.put("warehouseCode", ownerByDtWmsWarehouseCode.getOutWarehouseCode());

        Map<String, Object> extendPropsMap = new HashMap<>();
        extendPropsMap.put("ownerCode", ownerByDtWmsWarehouseCode.getOutOwnerCode());
        extendPropsMap.put("returnWarehouseCode", ownerByDtWmsWarehouseCode.getOutWarehouseCode());
        orderMap.put("extendProps", extendPropsMap);

        Map<String, Object> processMap = new HashMap<>();
        processMap.put("processStatus", "ACCEPT");
        processMap.put("operateTime", DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
        processMap.put("extendProps", extendPropsMap);

        Map<String, Object> backMap = new HashMap<>();
        backMap.put("order", orderMap);
        backMap.put("process", processMap);
        backMap.put("tenantId", remoteTenantHelper.queryTenantId(salesReturnOrderDTO.getWarehouseCode(), salesReturnOrderDTO.getCargoCode()));
        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSONUtil.toJsonStr(backMap));
        requestMap.put("method", "order.process.report");

        String request = CallOtherSupport.execute(it -> postForm(it, wmsOtherConfig.getCallBackMercuryCallbackV3Url()), requestMap, "销退单创建回告");

        boolean success = JSONUtil.parseObj(request).getBool("success", false);
        if (success) {
            BillLogDTO billLogDTO = billLogDTO(salesReturnOrderDTO, "销退单创建回告淘天成功", "销退单创建回告淘天成功", "系统");
            remoteBillLogClient.save(billLogDTO);
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
        } else {
            if (null != wmsOtherConfig && CollectionUtil.isNotEmpty(wmsOtherConfig.getTaoTianCallbackWarningUrlList())) {
                String errorMessage = JSONUtil.parseObj(request).getStr("errorMessage", StrUtil.EMPTY);
                String message = StrUtil.join(StrUtil.EMPTY, salesReturnOrderDTO.getWarehouseCode(), "淘天销退单", salesReturnOrderDTO.getSalesReturnOrderNo(), "创建回告失败", "淘天返回:", errorMessage);
                sendMessage(message);
            }
        }
    }

    @ApiOperation("到仓登记")
    public void arriveCallback(MessageMqDTO messageMqDTO) {

        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(messageMqDTO.getBillNo());
        SalesReturnOrderDTO salesReturnOrderDTO = get(salesReturnOrderParam);
        if (null == salesReturnOrderDTO) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        Map<String, Object> orderMap = new HashMap<>();
        Map<String, Object> orderExtend = new HashMap<>();
        Map<String, Object> processExtend = new HashMap<>();
        Map<String, Object> processMap = new HashMap<>();


        CargoOwnerTaoTianDTO ownerByDtWmsWarehouseCode = remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(salesReturnOrderDTO.getWarehouseCode(),
                salesReturnOrderDTO.getCargoCode());
        orderMap.put("dtWmsWarehouseCode", ownerByDtWmsWarehouseCode.getWmsWarehouseCode());
        orderMap.put("dtWmsCargoCode", ownerByDtWmsWarehouseCode.getWmsOwnerCode());

        orderExtend.put("ownerCode", ownerByDtWmsWarehouseCode.getOutOwnerCode());
        processExtend.put("returnWarehouseCode", ownerByDtWmsWarehouseCode.getOutWarehouseCode());

        orderMap.put("orderCode", salesReturnOrderDTO.getSalesReturnOrderNo());
        orderMap.put("orderType", "THRK");
        orderMap.put("orderId", salesReturnOrderDTO.getSalesReturnOrderNo());
        orderMap.put("warehouseCode", ownerByDtWmsWarehouseCode.getOutWarehouseCode());
        orderMap.put("extendProps", orderExtend);


        processMap.put("processStatus", "WMS_ARRIVE");
        processMap.put("operateTime", DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
        processMap.put("extendProps", processExtend);

        Map<String, Object> backMap = new HashMap<>();
        backMap.put("order", orderMap);
        backMap.put("process", processMap);
        backMap.put("tenantId", remoteTenantHelper.queryTenantId(salesReturnOrderDTO.getWarehouseCode(), salesReturnOrderDTO.getCargoCode()));
        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSONUtil.toJsonStr(backMap));
        requestMap.put("method", "order.process.report");

        String request = CallOtherSupport.execute(it -> postForm(it, wmsOtherConfig.getCallBackMercuryCallbackV3Url()), requestMap, "销退单到仓回告");
        boolean success = JSONUtil.parseObj(request).getBool("success", false);
        if (success) {
            BillLogDTO billLogDTO = billLogDTO(salesReturnOrderDTO, "到仓登记回告淘天成功", "到仓登记回告淘天成功", "系统");
            remoteBillLogClient.save(billLogDTO);
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
        } else {
            if (null != wmsOtherConfig && CollectionUtil.isNotEmpty(wmsOtherConfig.getTaoTianCallbackWarningUrlList())) {
                String errorMessage = JSONUtil.parseObj(request).getStr("errorMessage", StrUtil.EMPTY);
                String message = StrUtil.join(StrUtil.EMPTY, salesReturnOrderDTO.getWarehouseCode(), "淘天销退单", salesReturnOrderDTO.getSalesReturnOrderNo(), "到仓登记回告失败", "淘天返回:", errorMessage);
                sendMessage(message);
            }
        }
    }

    @ApiOperation("拒收登记")
    public void rejectCallback(MessageMqDTO messageMqDTO) {

        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(messageMqDTO.getBillNo());
        SalesReturnOrderDTO salesReturnOrderDTO = get(salesReturnOrderParam);
        if (null == salesReturnOrderDTO) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }


        Map<String, Object> orderMap = new HashMap<>();
        Map<String, Object> processMap = new HashMap<>();
        Map<String, Object> orderExtendPropsMap = new HashMap<>();

        CargoOwnerTaoTianDTO ownerByDtWmsWarehouseCode = remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(salesReturnOrderDTO.getWarehouseCode(),
                salesReturnOrderDTO.getCargoCode());
        orderMap.put("dtWmsWarehouseCode", ownerByDtWmsWarehouseCode.getWmsWarehouseCode());
        orderMap.put("dtWmsCargoCode", ownerByDtWmsWarehouseCode.getWmsOwnerCode());

        orderExtendPropsMap.put("ownerCode", ownerByDtWmsWarehouseCode.getOutOwnerCode());
        orderExtendPropsMap.put("returnWarehouseCode", ownerByDtWmsWarehouseCode.getWmsWarehouseCode());
        orderExtendPropsMap.put("rejectReason", salesReturnOrderDTO.salesReturnOrderExtraDTO().getRejectReason());
        orderExtendPropsMap.put("imgUrls", salesReturnOrderDTO.salesReturnOrderExtraDTO().getRejectImageList());

        orderMap.put("orderCode", salesReturnOrderDTO.getSalesReturnOrderNo());
        orderMap.put("orderType", "THRK");
        orderMap.put("orderId", salesReturnOrderDTO.getSalesReturnOrderNo());
        orderMap.put("warehouseCode", ownerByDtWmsWarehouseCode.getOutWarehouseCode());
        orderMap.put("extendProps", orderExtendPropsMap);

        processMap.put("processStatus", "WMS_REJECT");
        processMap.put("operateTime", DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
        processMap.put("extendProps", orderExtendPropsMap);

        Map<String, Object> backMap = new HashMap<>();
        backMap.put("order", orderMap);
        backMap.put("process", processMap);
        backMap.put("tenantId", remoteTenantHelper.queryTenantId(salesReturnOrderDTO.getWarehouseCode(), salesReturnOrderDTO.getCargoCode()));
        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSONUtil.toJsonStr(backMap));
        // taobao.qimen.orderprocess.report
        requestMap.put("method", "order.process.report");

        String request = CallOtherSupport.execute(it -> postForm(it, wmsOtherConfig.getCallBackMercuryCallbackV3Url()), requestMap, "销退单拒收回告");
        boolean success = JSONUtil.parseObj(request).getBool("success", false);
        if (success) {
            BillLogDTO billLogDTO = billLogDTO(salesReturnOrderDTO, "拒收回告淘天成功", "拒收回告淘天成功", "系统");
            remoteBillLogClient.save(billLogDTO);
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
        } else {
            if (null != wmsOtherConfig && CollectionUtil.isNotEmpty(wmsOtherConfig.getTaoTianCallbackWarningUrlList())) {
                String errorMessage = JSONUtil.parseObj(request).getStr("errorMessage", StrUtil.EMPTY);
                String message = StrUtil.join(StrUtil.EMPTY, salesReturnOrderDTO.getWarehouseCode(), "淘天销退单", salesReturnOrderDTO.getSalesReturnOrderNo(), "拒收登记回告失败", "淘天返回:", errorMessage);
                sendMessage(message);
            }
        }
    }

    @ApiOperation("拒收登记回告正向单据")
    public void rejectCallbackOrigin(MessageMqDTO messageMqDTO) {

        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(messageMqDTO.getBillNo());
        SalesReturnOrderDTO salesReturnOrderDTO = get(salesReturnOrderParam);
        if (null == salesReturnOrderDTO) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        Map<String, Object> extendPropsMap = new HashMap<>();
        Map<String, Object> orderMap = new HashMap<>();
        Map<String, Object> processMap = new HashMap<>();

        extendPropsMap.put("ownerCode", salesReturnOrderDTO.salesReturnOrderExtraDTO().getShipmentCargoCode());

        orderMap.put("extendProps", extendPropsMap);
        orderMap.put("warehouseCode", salesReturnOrderDTO.getRealWarehouseCode());
        orderMap.put("orderCode", salesReturnOrderDTO.getPoNo());
        orderMap.put("orderType", "JYCK");
        orderMap.put("orderId", salesReturnOrderDTO.getPoNo());

        processMap.put("processStatus", "TMS_REVERSE_REJECT");
        processMap.put("operateTime", DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
        processMap.put("extendProps", extendPropsMap);
        processMap.put("expressCode", salesReturnOrderDTO.getExpressNo());

        Map<String, Object> backMap = new HashMap<>();
        backMap.put("order", orderMap);
        backMap.put("process", processMap);
        backMap.put("tenantId", remoteTenantHelper.queryTenantId(salesReturnOrderDTO.getWarehouseCode(), salesReturnOrderDTO.getCargoCode()));

        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSONUtil.toJsonStr(backMap));
        requestMap.put("method", "order.process.report");

        String request = CallOtherSupport.execute(it -> postForm(it, wmsOtherConfig.getCallBackMercuryCallbackV3Url()), requestMap, "销退单拒收回告正向单据");
        boolean success = JSONUtil.parseObj(request).getBool("success", false);
        if (success) {
            BillLogDTO billLogDTO = billLogDTO(salesReturnOrderDTO, "拒收回告正向单据淘天成功", "拒收回告正向单据淘天成功", "系统");
            remoteBillLogClient.save(billLogDTO);
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
        } else {
            RBucket<String> bucket = redissonClient.getBucket(StrUtil.join(StrUtil.COLON, "OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN", salesReturnOrderDTO.getSalesReturnOrderNo()));
            bucket.expireAt(DateTime.now().offset(DateField.DAY_OF_YEAR, 7));
            if (bucket.isExists()) return;
            if (null != wmsOtherConfig && CollectionUtil.isNotEmpty(wmsOtherConfig.getTaoTianCallbackWarningUrlList())) {
                String errorMessage = JSONUtil.parseObj(request).getStr("errorMessage", StrUtil.EMPTY);
                String message = StrUtil.join(StrUtil.EMPTY, salesReturnOrderDTO.getWarehouseCode(), "淘天销退单", salesReturnOrderDTO.getSalesReturnOrderNo(), "拒收登记回告正向单据失败对应正向运单", salesReturnOrderDTO.getExpressNo(), "淘天返回:", errorMessage);
                if (sendMessage(message)) {
                    bucket.set(salesReturnOrderDTO.getSalesReturnOrderNo());
                }
            }
        }
    }

    @ApiOperation("收货回告正向单据")
    public void receiveCallbackOrigin(MessageMqDTO messageMqDTO) {

        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setSalesReturnOrderNo(messageMqDTO.getBillNo());
        SalesReturnOrderDTO salesReturnOrderDTO = get(salesReturnOrderParam);
        if (null == salesReturnOrderDTO) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        if (RSReturnTypeEnum.CUSTOMER_REJECT.getCode().equals(salesReturnOrderDTO.getReturnType())
                || RSReturnTypeEnum.TMS_CUT.getCode().equals(salesReturnOrderDTO.getReturnType())) {
            Boolean data = tmsTrackClient.hasBuyerSigned(salesReturnOrderDTO.getExpressNo()).getData();
            if (data != null && data) {
                messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                remoteMessageMqClient.modify(messageMqDTO);
                return;
            }
        }

        Map<String, Object> extendPropsMap = new HashMap<>();
        Map<String, Object> orderMap = new HashMap<>();
        Map<String, Object> processMap = new HashMap<>();


        extendPropsMap.put("ownerCode", salesReturnOrderDTO.salesReturnOrderExtraDTO().getShipmentCargoCode());

        orderMap.put("orderCode", salesReturnOrderDTO.getPoNo());
        orderMap.put("orderType", "JYCK");
        orderMap.put("orderId", salesReturnOrderDTO.getPoNo());
        orderMap.put("warehouseCode", salesReturnOrderDTO.getRealWarehouseCode());
        orderMap.put("extendProps", extendPropsMap);

        processMap.put("processStatus", "TMS_REVERSE_SIGN");
        processMap.put("operateTime", DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
        processMap.put("extendProps", extendPropsMap);
        processMap.put("expressCode", salesReturnOrderDTO.getExpressNo());

        Map<String, Object> backMap = new HashMap<>();
        backMap.put("order", orderMap);
        backMap.put("process", processMap);
        backMap.put("tenantId", remoteTenantHelper.queryTenantId(salesReturnOrderDTO.getWarehouseCode(), salesReturnOrderDTO.getCargoCode()));
        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSONUtil.toJsonStr(backMap));
        requestMap.put("method", "order.process.report");

        String request = CallOtherSupport.execute(it -> postForm(it, wmsOtherConfig.getCallBackMercuryCallbackV3Url()), requestMap, "销退单签收回告正向单据");
        boolean success = JSONUtil.parseObj(request).getBool("success", false);
        if (success) {
            BillLogDTO billLogDTO = billLogDTO(salesReturnOrderDTO, "销退单签收回告正向单据成功", "销退单签收回告正向单据成功", "系统");
            remoteBillLogClient.save(billLogDTO);
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
        } else {
            RBucket<String> bucket = redissonClient.getBucket(StrUtil.join(StrUtil.COLON, "OPERATION_SALE_RETURN_RECEIVE_CALLBACK_ORIGIN", salesReturnOrderDTO.getSalesReturnOrderNo()));
            bucket.expireAt(DateTime.now().offset(DateField.DAY_OF_YEAR, 7));
            if (bucket.isExists()) return;
            if (null != wmsOtherConfig && CollectionUtil.isNotEmpty(wmsOtherConfig.getTaoTianCallbackWarningUrlList())) {
                String errorMessage = JSONUtil.parseObj(request).getStr("errorMessage", StrUtil.EMPTY);
                String message = StrUtil.join(StrUtil.EMPTY, salesReturnOrderDTO.getWarehouseCode(), "淘天销退单", salesReturnOrderDTO.getSalesReturnOrderNo(), "收货回告正向单据异常对应正向运单", salesReturnOrderDTO.getExpressNo(), "淘天返回:", errorMessage);
                if (sendMessage(message)) {
                    bucket.set(salesReturnOrderDTO.getSalesReturnOrderNo());
                }
            }
        }
    }

    @Override
    public void handoverCheckAdditionalOrder(String additionalOrder, RSRejectEnum rsRejectEnum) {
        if (StrUtil.isBlank(additionalOrder)) return;
        if (RSAdditionalOrderEnum.WAREHOUSE_REJECT.getCode().equalsIgnoreCase(additionalOrder)
                && RSRejectEnum.NO.getCode().equals(rsRejectEnum.getCode())) {
            throw ExceptionUtil.exceptionWithMessage("销退单存在拒收指令，必须拒收");
        }
        if (RSAdditionalOrderEnum.WAREHOUSE_RECEIVE.getCode().equalsIgnoreCase(additionalOrder)
                && RSRejectEnum.YES.getCode().equals(rsRejectEnum.getCode())) {
            throw ExceptionUtil.exceptionWithMessage("销退单存在签收指令，不能拒收");
        }
    }

    @Override
    public boolean holdTag(SalesReturnOrderDTO salesReturnOrderDTO) {
        String hold = salesReturnOrderDTO.salesReturnOrderExtraDTO().getHold();
        if (StrUtil.isBlank(hold)) return false;
        if (hold.trim().equalsIgnoreCase("RETURN_ON_SHELF_HOLD")) {
            return true;
        }
        return false;
    }
}
