package com.dt.platform.wms.integration.tally.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.asn.AsnStatusEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.tally.TallyAbnormalTypeEnum;
import com.dt.component.common.enums.tally.TallyMarkDetailEnum;
import com.dt.component.common.enums.tally.TallyMarkEnum;
import com.dt.component.common.enums.tally.TallyStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.base.client.ISkuClient;
import com.dt.domain.base.client.log.ITallyLogClient;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.log.TallyLogDTO;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.bill.client.IAsnBillClient;
import com.dt.domain.bill.client.tally.ITallyClient;
import com.dt.domain.bill.client.tally.ITallyDetailClient;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.domain.bill.dto.tally.TallyDTO;
import com.dt.domain.bill.dto.tally.TallyDetailDTO;
import com.dt.domain.bill.param.AsnParam;
import com.dt.domain.bill.param.tally.TallyDetailParam;
import com.dt.domain.bill.param.tally.TallyParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.wms.biz.taotian.TallyAbnormalRequest;
import com.dt.platform.wms.integration.tally.IRemoteTallyClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
@Slf4j
@Service
public class RemoteTallyClientImpl implements IRemoteTallyClient {

    @DubboReference
    private ITallyClient tallyClient;

    @DubboReference
    private ITallyLogClient tallyLogClient;

    @DubboReference
    private ITallyDetailClient tallyDetailClient;

    @DubboReference
    private ISkuClient skuClient;

    @DubboReference
    private IAsnBillClient asnBillClient;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public Boolean save(TallyDTO tallyDTO) {
        return tallyClient.save(tallyDTO).getData();
    }

    @Override
    public Boolean saveBatch(List<TallyDTO> tallyDTOList) {
        return tallyClient.saveBatch(tallyDTOList).getData();
    }

    @Override
    public Boolean modify(TallyDTO tallyDTO) {
        return tallyClient.modify(tallyDTO).getData();
    }

    @Override
    public Boolean modifyAndCancelCarryover(TallyDTO tallyDTO) {
        return tallyClient.modifyAndCancelCarryover(tallyDTO).getData();
    }

    @Override
    public Boolean modifyBatch(List<TallyDTO> tallyDTOList) {
        return tallyClient.modifyBatch(tallyDTOList).getData();
    }

    @Override
    public Boolean checkExits(TallyParam param) {
        return tallyClient.checkExits(param).getData();
    }

    @Override
    public TallyDTO get(TallyParam param) {
        return tallyClient.get(param).getData();
    }

    @Override
    public List<TallyDTO> getList(TallyParam param) {
        return tallyClient.getList(param).getData();
    }

    @Override
    public Page<TallyDTO> getPage(TallyParam param) {
        return tallyClient.getPage(param).getData();
    }

    @Override
    public Boolean remove(TallyParam param) {
        return tallyClient.remove(param).getData();
    }

    @Override
    public Boolean updateTallyDTOAndRemoveDetail(TallyDTO tallyDTO) {
        return tallyClient.updateTallyDTOAndRemoveDetail(tallyDTO).getData();
    }

    @Override
    public Integer getExportCountNum(TallyParam tallyParam) {
        return tallyClient.getExportCountNum(tallyParam).getData();
    }

    @Override
    public Boolean modifyTallyAndValidity(TallyDTO tallyDTO) {
        return tallyClient.modifyTallyAndValidity(tallyDTO).getData();
    }

    @Override
    public Boolean removeDetailAndModify(TallyDTO tallyDTO) {
        return tallyClient.removeDetailAndModify(tallyDTO).getData();
    }

    @Override
    public String tallyAbnormal(String businessNo, String sourceData) {
        TallyAbnormalRequest tallyAbnormalRequest = JSONUtil.toBean(sourceData, TallyAbnormalRequest.class);
        RLock lock = redissonClient.getLock("tallyAbnormal:" + tallyAbnormalRequest.getWarehouseCode() + tallyAbnormalRequest.getEntryOrderCode());
        try {
            boolean tryLock = lock.tryLock(1, 5, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后");
            }
            if (StringUtils.isEmpty(tallyAbnormalRequest)
                    || StringUtils.isEmpty(tallyAbnormalRequest.getAbnormalId())
                    || StringUtils.isEmpty(tallyAbnormalRequest.getInventoryType())
                    || StringUtils.isEmpty(tallyAbnormalRequest.getEntryOrderCode())) {
                throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
            }
            //设置数据源
            RpcContextUtil.setWarehouseCode(tallyAbnormalRequest.getWarehouseCode());
            //
            TallyDTO tallyDTO = getTallyDTO(tallyAbnormalRequest.getEntryOrderCode());

            //
            TallyDetailParam tallyDetailParam = new TallyDetailParam();
            tallyDetailParam.setTallyCode(tallyDTO.getTallyCode());
            List<TallyDetailDTO> tallyDetailDTOList = tallyDetailClient.getList(tallyDetailParam).getData();
            if (CollectionUtils.isEmpty(tallyDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "未找到理货数据明细:" + tallyAbnormalRequest.getEntryOrderCode() + tallyAbnormalRequest.getAbnormalId());
            }
            TallyDetailDTO tallyDetailDTO = tallyDetailDTOList.stream()
                    .filter(a -> a.getLineNo().equals(tallyAbnormalRequest.getAbnormalId()))
                    .findFirst().orElse(null);
            if (tallyDetailDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "未找到理货数据明细:" + tallyAbnormalRequest.getEntryOrderCode() + tallyAbnormalRequest.getAbnormalId());
            }
            //获取sku
            SkuParam skuParam = new SkuParam();
            skuParam.setCargoCode(tallyDetailDTO.getCargoCode());
            skuParam.setCode(tallyDetailDTO.getSkuCode());
            SkuDTO skuDTO = skuClient.get(skuParam).getData();
            if (skuDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "未找到理货数据商品明细:" + tallyAbnormalRequest.getEntryOrderCode() + tallyAbnormalRequest.getAbnormalId());
            }
            if (!Objects.equals(tallyDetailDTO.getAbnormalType(), TallyAbnormalTypeEnum.DEW_GOODS.getCode()) && !Objects.equals(tallyDTO.getTallyCode(), "TALLY240926000094")) {
                if (StringUtils.isEmpty(tallyAbnormalRequest.getAbnormalHandleType())) {
                    throw new BaseException(BaseBizEnum.TIP, "异常非【少货】处理类型不能为空:" + tallyAbnormalRequest.getEntryOrderCode() + ":" + tallyAbnormalRequest.getAbnormalId());
                }
                if (Objects.equals(30L, tallyAbnormalRequest.getAbnormalHandleType())) {
                    throw new BaseException(BaseBizEnum.TIP, "异常非【少货】处理类型不能为30:" + tallyAbnormalRequest.getEntryOrderCode() + ":" + tallyAbnormalRequest.getAbnormalId());
                }
            }
            //异常处理类型（8=等待仓库复核、12=收货上架、13=按良收货上架、14=按残收货上架、15=按收货暂存良品上架、16=按收货暂存残次上架、20=折价入、30=物流侧自行处理）
            List<Long> abnormalHandleType = Arrays.asList(8L, 12L, 13L, 14L, 15L, 16L, 20L, 30L);
            if (!StringUtils.isEmpty(tallyAbnormalRequest.getAbnormalHandleType()) && !abnormalHandleType.contains(tallyAbnormalRequest.getAbnormalHandleType())) {
                throw new BaseException(BaseBizEnum.TIP, "异常处理类型不在【8L,12L,13L,14L,15L,16L,20L,30L】:" + tallyAbnormalRequest.getEntryOrderCode() + ":" + tallyAbnormalRequest.getAbnormalId());
            }
            //按照生产日期 + 保质期天数 = 失效日期， 填充 理货报告中的 生产日期、
            // 失效日期，明细行标记【待差异处理回告】/【待解码】去除；
            // 主表上的【待解码】需明细无【待解码】标记才移除；增加日志“上游下发解读效期，数据覆盖，效期码xxx，生产日期xxx”
            String message = "";
            if (Objects.equals(tallyDetailDTO.getAbnormalType(), TallyAbnormalTypeEnum.LOT_NO_READ.getCode())) {
                if (!StringUtils.isEmpty(tallyAbnormalRequest.getProductDate())) {
//                    throw new BaseException(BaseBizEnum.TIP, "效期不能解读生产日期不能为空:" + tallyAbnormalRequest.getEntryOrderCode() + tallyAbnormalRequest.getAbnormalId());

                    Long manufDate = DateUtil.parse(tallyAbnormalRequest.getProductDate(), "yyyy-MM-dd").getTime();
                    Long startDate = DateUtil.parse("1970-01-01", "yyyy-MM-dd").getTime();
                    if (manufDate < startDate) {
                        throw new BaseException(BaseBizEnum.TIP, "效期不能解读不能小于1970-01-01:" + tallyAbnormalRequest.getEntryOrderCode() + tallyAbnormalRequest.getAbnormalId());
                    }
                    if (manufDate > System.currentTimeMillis()) {
                        throw new BaseException(BaseBizEnum.TIP, "生产日期不能大于当前时间" + tallyAbnormalRequest.getEntryOrderCode() + tallyAbnormalRequest.getAbnormalId());
                    }
                    Long expireDate = manufDate + skuDTO.getLifeCycle() * CommonConstantUtil.DAY_MILLISECONDS;

                    //校验当前理货报告中，同一商品同一效期码多行记录的生产日期和失效日期是否已有记录（保证同一个商品的同一个效期码的生产和失效是一样的，不会多份）
                    //无记录正常移除标记
                    //有记录且和已有记录生产日期和失效日期一致，标记移除
                    //有记录且和已有记录的生产和失效日期不一致，标记不移除，返回上游报错，企微报警
                    TallyDetailDTO tallyDetailDTOCheckValidity = tallyDetailDTOList.stream()
                            .filter(a -> a.getSkuCode().equalsIgnoreCase(tallyDetailDTO.getSkuCode()))
                            .filter(a -> a.getValidityCode().equalsIgnoreCase(tallyDetailDTO.getValidityCode()))
                            .filter(a -> a.getManufDate() > 0L)
                            .filter(a -> a.getExpireDate() > 0L).findFirst().orElse(null);
                    if (tallyDetailDTOCheckValidity != null) {
                        if (!Objects.equals(manufDate, tallyDetailDTOCheckValidity.getManufDate()) || !Objects.equals(expireDate, tallyDetailDTOCheckValidity.getExpireDate())) {
                            throw new BaseException(BaseBizEnum.TIP, "当前理货报告:%s,同一商品:%s同一效期码:%s,失效日期解析有多个,请核查",
                                    tallyAbnormalRequest.getEntryOrderCode() + ":" + tallyAbnormalRequest.getAbnormalId(),
                                    tallyDetailDTO.getSkuCode(),
                                    tallyDetailDTO.getValidityCode());
                        }
                    }
                    tallyDetailDTO.setManufDate(manufDate);
                    tallyDetailDTO.setExpireDate(expireDate);
                    Set<TallyMarkDetailEnum> tallyMarkDetailEnums = TallyMarkDetailEnum.NumToEnum(tallyDetailDTO.getMark());
                    tallyMarkDetailEnums.remove(TallyMarkDetailEnum.WAIT_DECODE);
                    tallyMarkDetailEnums.remove(TallyMarkDetailEnum.WAIT_DIFF_HANDLE);
                    if (CollectionUtils.isEmpty(tallyMarkDetailEnums)) {
                        tallyDetailDTO.setMark(0);
                    } else {
                        tallyDetailDTO.setMark(TallyMarkDetailEnum.enumToNum(tallyMarkDetailEnums.stream().collect(Collectors.toList())));
                    }
                    message = "上游下发解读效期,数据覆盖,效期码" + tallyDetailDTO.getValidityCode() + ",生产日期" + tallyAbnormalRequest.getProductDate();
                }
            }

            /**
             处理方式：
             除去【等待仓库复核=8】，所有收到异常明细回告的明细，均去除【等待差异处理回告】标记
             物流侧自行处理=30 ： 少货的场景下，会下发此；
             空： 只有少货会下发空，其他异常若下发空，wms需接口报错给淘天“枚举异常”
             待复核=8： 明细行标记【待复核】
             */

            List<String> zpList = Arrays.asList(InventoryTypeEnum.ZP.getCode(), InventoryTypeEnum.ZCZP.getCode());

            if (Objects.equals(tallyAbnormalRequest.getAbnormalHandleType(), 8L)) {
                if (Objects.equals(tallyDetailDTO.getAbnormalType(), TallyAbnormalTypeEnum.DEW_GOODS.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "【少货】的异常类型,不能接受8=等待仓库复核的指令" + tallyAbnormalRequest.getEntryOrderCode() + tallyAbnormalRequest.getAbnormalId());
                }
                //等待仓库复核=8 待复核=8： 明细行标记【待复核】
                Set<TallyMarkDetailEnum> tallyMarkDetailEnums = TallyMarkDetailEnum.NumToEnum(tallyDetailDTO.getMark());
                tallyMarkDetailEnums.remove(TallyMarkDetailEnum.WAIT_DIFF_HANDLE);
                tallyMarkDetailEnums.add(TallyMarkDetailEnum.WAIT_CHECK);
                if (CollectionUtils.isEmpty(tallyMarkDetailEnums)) {
                    tallyDetailDTO.setMark(0);
                } else {
                    tallyDetailDTO.setMark(TallyMarkDetailEnum.enumToNum(tallyMarkDetailEnums.stream().collect(Collectors.toList())));
                }
                message = message + "上游下发处理方式【等待仓库复核=8】";
            } else {
                if (Objects.equals(tallyAbnormalRequest.getAbnormalHandleType(), 12L)) {
                    //.....收货上架=12： 不改变明细行属性值
                    message = message + "上游下发处理方式【收货上架=12】";
                }
                if (Objects.equals(tallyAbnormalRequest.getAbnormalHandleType(), 13L)) {
                    //.....按良收货上架=13：修改商品属性为【正品】，【残次等级】为【正品】；理货报告增加日志“上游下发处理方式xxx，原【次品】属性改为【正品】
                    message = message + "上游下发处理方式【良收货上架=13】,原【次品】属性改为【正品】";
                }
                if (Objects.equals(tallyAbnormalRequest.getAbnormalHandleType(), 14L)) {
                    //.....按残收货上架=14： 修改商品属性为【次品】，【残次等级】为【残次】；理货报告增加日志“上游下发处理方式xxx，原【正品】属性改为【次品】
                    message = message + "上游下发处理方式【按残收货上架=14】,原【正品】属性改为【次品】";
                }
                if (Objects.equals(tallyAbnormalRequest.getAbnormalHandleType(), 15L)) {
                    //.....按收货暂存良品上架=15： 明细行商品属性改为【正品】，【残次等级】改为【收货暂存良-待退废】；理货报告增加日志“上游下发处理方式xxx，，
                    // 【残次等级】变更为【收货暂存良-待退废】”
                    message = message + "上游下发处理方式【按收货暂存良品上架=15】,原【次品】属性改为【正品】,【残次等级】变更为【收货暂存良-待退废】";
                }
                if (Objects.equals(tallyAbnormalRequest.getAbnormalHandleType(), 16L)) {
                    //..... 按收货暂存残次上架=16： 明细行商品属性改为【次品】，【残次等级】改为【收货暂存残-待退废】；理货报告增加日志“上游下发处理方式xxx，
                    // 【残次等级】变更为【收货暂存-待退废】”
                    message = message + "上游下发处理方式【按收货暂存残次上架=16】,原【正品】属性改为【次品】,【残次等级】变更为【收货暂存-待退废】";
                }
                if (Objects.equals(tallyAbnormalRequest.getAbnormalHandleType(), 20L)) {
                    //.....  折价入20： 同12
                    message = message + "上游下发处理方式【折价入20】";
                }
                if (Objects.equals(tallyAbnormalRequest.getAbnormalHandleType(), 30L)) {
                    //.....  物流侧自行处理=30
                    message = message + "上游下发处理方式【物流侧自行处理=30】";
                }
                if (StringUtils.isEmpty(tallyAbnormalRequest.getAbnormalHandleType())) {
                    //.....  物流侧自行处理=30
                    message = message + "上游下发处理方式【空】";
                }
                //属性以淘天为准
                tallyDetailDTO.setInventoryType(tallyAbnormalRequest.getInventoryType().toUpperCase());
                if (zpList.contains(tallyAbnormalRequest.getInventoryType().toUpperCase())) {
                    tallyDetailDTO.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
                } else {
                    tallyDetailDTO.setSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
                }
                if (Arrays.stream(InventoryTypeEnum.values()).anyMatch(a -> Objects.equals(a.getCode(), tallyAbnormalRequest.getInventoryType().toUpperCase()))) {
                    message = message + ",下发处理残次等级:" + InventoryTypeEnum.getEnum(tallyAbnormalRequest.getInventoryType().toUpperCase()).getMessage();
                } else {
                    message = message + ",下发处理残次等级--:" + tallyAbnormalRequest.getInventoryType();
                }
                //效期批次不能解读 生成日期不传 不移除差异处理回告
                if (Objects.equals(tallyDetailDTO.getAbnormalType(), TallyAbnormalTypeEnum.LOT_NO_READ.getCode())
                        && StringUtils.isEmpty(tallyAbnormalRequest.getProductDate())) {
                    //不移除
                } else {
                    Set<TallyMarkDetailEnum> tallyMarkDetailEnums = TallyMarkDetailEnum.NumToEnum(tallyDetailDTO.getMark());
                    tallyMarkDetailEnums.remove(TallyMarkDetailEnum.WAIT_DIFF_HANDLE);
                    if (CollectionUtils.isEmpty(tallyMarkDetailEnums)) {
                        tallyDetailDTO.setMark(0);
                    } else {
                        tallyDetailDTO.setMark(TallyMarkDetailEnum.enumToNum(tallyMarkDetailEnums.stream().collect(Collectors.toList())));
                    }
                }
            }

            //明细无解码 主单异常待解码
            if (tallyDetailDTOList.stream().allMatch(a -> !TallyMarkDetailEnum.NumToEnum(a.getMark()).contains(TallyMarkDetailEnum.WAIT_DECODE))) {
                Set<TallyMarkEnum> tallyMarkEnums = TallyMarkEnum.NumToEnum(tallyDTO.getMark());
                tallyMarkEnums.remove(TallyMarkEnum.WAIT_DECODE);
                if (CollectionUtils.isEmpty(tallyMarkEnums)) {
                    tallyDetailDTO.setMark(0);
                } else {
                    tallyDTO.setMark(TallyMarkEnum.enumToNum(tallyMarkEnums.stream().collect(Collectors.toList())));
                }
            }
            //明细无解码 主单异常待解码
            if (tallyDetailDTOList.stream().anyMatch(a -> TallyMarkDetailEnum.NumToEnum(a.getMark()).contains(TallyMarkDetailEnum.WAIT_CHECK))) {
                Set<TallyMarkEnum> tallyMarkEnums = TallyMarkEnum.NumToEnum(tallyDTO.getMark());
                tallyMarkEnums.add(TallyMarkEnum.WAIT_CHECK);
                if (CollectionUtils.isEmpty(tallyMarkEnums)) {
                    tallyDetailDTO.setMark(0);
                } else {
                    tallyDTO.setMark(TallyMarkEnum.enumToNum(tallyMarkEnums.stream().collect(Collectors.toList())));
                }
            }
            //明细无解码 主单异常待解码
            if (tallyDetailDTOList.stream().allMatch(a -> !TallyMarkDetailEnum.NumToEnum(a.getMark()).contains(TallyMarkDetailEnum.WAIT_CHECK))) {
                Set<TallyMarkEnum> tallyMarkEnums = TallyMarkEnum.NumToEnum(tallyDTO.getMark());
                tallyMarkEnums.remove(TallyMarkEnum.WAIT_CHECK);
                if (CollectionUtils.isEmpty(tallyMarkEnums)) {
                    tallyDetailDTO.setMark(0);
                } else {
                    tallyDTO.setMark(TallyMarkEnum.enumToNum(tallyMarkEnums.stream().collect(Collectors.toList())));
                }
            }
            //明细无待差异处理回告 主单移除待差异处理回告
            if (tallyDetailDTOList.stream().allMatch(a -> !TallyMarkDetailEnum.NumToEnum(a.getMark()).contains(TallyMarkDetailEnum.WAIT_DIFF_HANDLE))) {
                Set<TallyMarkEnum> tallyMarkEnums = TallyMarkEnum.NumToEnum(tallyDTO.getMark());
                tallyMarkEnums.remove(TallyMarkEnum.WAIT_DIFF_HANDLE);
                if (CollectionUtils.isEmpty(tallyMarkEnums)) {
                    tallyDetailDTO.setMark(0);
                } else {
                    tallyDTO.setMark(TallyMarkEnum.enumToNum(tallyMarkEnums.stream().collect(Collectors.toList())));
                }
            }
            tallyDTO.setDetailList(Arrays.asList(tallyDetailDTO));
            tallyClient.modify(tallyDTO);
            //add 日志
            TallyLogDTO logDTO = new TallyLogDTO();
            logDTO.setOpBy("System");
            logDTO.setOpContent(message + ",异常ID:" + tallyAbnormalRequest.getAbnormalId());
            logDTO.setTallyCode(tallyDTO.getTallyCode());
            logDTO.setOpDate(System.currentTimeMillis());
            logDTO.setCargoCode(tallyDTO.getCargoCode());
            logDTO.setOpRemark("报文:" + JSON.toJSONString(tallyAbnormalRequest));
            tallyLogClient.save(logDTO);
        } catch (Exception e) {
            e.printStackTrace();
            String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage();
            throw new BaseException(BaseBizEnum.TIP, errorMsg);
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return "ok";
    }


    /**
     * @param entryOrderCode
     * @return com.dt.domain.bill.dto.tally.TallyDTO
     * <AUTHOR>
     * @describe:
     * @date 2024/3/13 16:11
     */
    private TallyDTO getTallyDTO(String entryOrderCode) {
        AsnParam asnParam = new AsnParam();
        asnParam.setPoNo(entryOrderCode);
        List<AsnDTO> asnDTOList = asnBillClient.getList(asnParam).getData();
        if (CollectionUtils.isEmpty(asnDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到理货数据:" + entryOrderCode);
        }
        AsnDTO asnDTO = asnDTOList.stream().filter(a -> !a.getStatus().equals(AsnStatusEnum.CANCEL.getCode())).findFirst().orElse(null);
        if (asnDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "未找到理货数据:" + entryOrderCode);
        }
        TallyParam tallyParam = new TallyParam();
        tallyParam.setBillNo(asnDTO.getAsnId());
        List<TallyDTO> tallyDTOList = tallyClient.getList(tallyParam).getData();
        if (CollectionUtils.isEmpty(tallyDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "未找到理货数据:" + entryOrderCode);
        }
        TallyDTO tallyDTO = tallyDTOList.stream().filter(a -> a.getStatus().equals(TallyStatusEnum.SUCCESS_AUTH.getCode())).findFirst().orElse(null);
        if (tallyDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "未找到理货数据:" + entryOrderCode);
        }
        return tallyDTO;
    }
}
