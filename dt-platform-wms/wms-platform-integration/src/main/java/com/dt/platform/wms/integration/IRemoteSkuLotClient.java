package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.param.SkuLotParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;

/**
 * 货品(sku)批次信息查询
 *
 * <AUTHOR>
 * @date 2020/9/27 14:13
 */
public interface IRemoteSkuLotClient {
    /**
     * 查询指定规则Sku批次 TODO 此方法有一定的逻辑，不清楚逻辑慎重修改
     *
     * @param skuLotParam
     * @return
     */
    SkuLotDTO queryAllocateSkuLotByParam(SkuLotParam skuLotParam);

    /**
     * id查询货品批次
     *
     * @param id
     * @return
     */
    SkuLotDTO queryAllocateSkuLotById(Long id);

    /**
     * 编码查询货品批次
     *
     * @param code
     * @return
     */
    SkuLotDTO queryAllocateSkuLotByCode(String code);

    /**
     * 收货作业批次生产货品批次
     *
     * @param skuLotDTOs
     * @return
     */
    Boolean commitSkuLot(List<SkuLotDTO> skuLotDTOs);

    /**
     * 查询复合条件的skuLot编码列表
     *
     * @param skuLotParam
     * @return
     */
    List<String> getAllSkuLotNoList(SkuLotParam skuLotParam);

    /**
     * 查询批次信息列表
     *
     * @param skuLotParam
     * @return
     */
    SkuLotDTO get(SkuLotParam skuLotParam);

    /**
     * 查询批次信息列表
     *
     * @param skuLotParam
     * @return
     */
    List<SkuLotDTO> getList(SkuLotParam skuLotParam);
    List<SkuLotDTO> getAppointMultipleParam(SkuLotParam skuLotParam, List<String> tableFields);

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    IPage<SkuLotDTO> getPage(SkuLotParam param);

    /**
     * 查询多个批次号
     *
     * @param collect
     * @return
     */
    List<SkuLotDTO> getSkuLotNoList(List<String> collect);

    /**
     * 批量插入货品批次
     *
     * @param skuLotDTOList
     */
    Boolean saveBatch(List<SkuLotDTO> skuLotDTOList);

    /**
     * 分页获取
     *
     * @param skuLotParam
     * @return
     */
    List<SkuLotDTO> getListByPage(SkuLotParam skuLotParam);

    /**
     * @param skuLotParam
     * @return com.dt.domain.base.dto.SkuLotDTO
     * <AUTHOR>
     * @describe: CW业务 TODO 此方法有一定的逻辑，不清楚逻辑慎重修改
     * @date 2023/8/31 11:47
     */
    SkuLotDTO queryAllocateSkuLotCWByParam(SkuLotParam skuLotParam);
    
    @ApiOperation("获取批次信息")
    Map<String,SkuLotDTO> skuLotMap(List<String> skuLotNoList);

    /**
     * @param skuLotDTO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 补充溯源码信息
     * @date 2024/3/26 15:24
     */
    void supplySourceInfomation(SkuLotDTO skuLotDTO);
}
