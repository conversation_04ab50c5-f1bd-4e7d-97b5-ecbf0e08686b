package com.dt.platform.wms.integration.mercury;


import com.dt.component.common.result.ResponseMercury;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.bill.dto.AdjustDTO;
import com.dt.domain.bill.dto.AdjustDetailDTO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.device.DeviceRecordDTO;
import com.dt.platform.wms.biz.taotian.FileUploadApplyDTO;
import com.dt.platform.wms.biz.taotian.FileUploadBizParam;
import com.dt.platform.wms.biz.taotian.ItemlackReportRequest;
import com.dt.platform.wms.biz.taotian.PackageVideoReportParam;

import com.dt.domain.bill.param.ShelfParam;

import java.util.List;


public interface IRemoteMercuryClient {

    /**
     * @param param
     * @param warehouseCode
     * @return void
     * <AUTHOR>
     * @describe: callBack 淘天
     * @date 2024/3/8 16:23
     */
    void skuCallBackTaoTian(String param, String warehouseCode);


    /**
     * 申请文件上传
     *
     * @param param
     */
    FileUploadApplyDTO fileUploadApply(FileUploadBizParam param);

    boolean outOfStockNotify(ItemlackReportRequest param);

    /**
     * 包裹视频回告
     * @param param
     * @return
     */
    boolean packageVideoReport(PackageVideoReportParam param);
    /**
     * @param param
     * @param warehouseCode
     * @return void
     * <AUTHOR>
     * @describe: 理货复核异常回传淘天
     * @date 2024/3/15 16:49
     */
    void tallyDetailCallBackTaoTian(String param, String warehouseCode);
    void returnOrderPull(String param, String warehouseCode);

    Boolean shelfCallback(String param, String warehouseCode);
    /**
     * @param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 上架申请
     * @date 2024/3/15 16:49
     */
    Boolean commitTaoTianShelf(ShelfParam param);


    /**
     * 7.4【仓内作业异常处理指令】回告淘天
     *
     * @return
     */
    ResponseMercury warehouseJobExceptionCallbackSync(String param, String warehouseCode);

    /**
     * 3.18 回告淘天
     * @param param
     * @param warehouseCode
     */
    void taoBaoQiMenInventoryReportCallback(String param, String warehouseCode);

    Boolean commitTaoTianShelfCheckSku(ShelfParam convert);

    void commitTaoTianAdjustCheckSku(AdjustDTO adjustDTO, List<AdjustDetailDTO> adjustDetailDTOList);

    /**
     * @param deviceRecordDTO
     * @return void
     * <AUTHOR>
     * @describe: 温湿度回传淘天
     * @date 2024/6/13 15:36
     */
    Result<String> scanDeviceEventCallBack(DeviceRecordDTO deviceRecordDTO);
//
//    void callBackTaoTianByHand(String cargoCode, String skuCode);

    /**
     * @param cargoOwnerDTO
     * @param shipmentOrderDTO
     * @return void
     * <AUTHOR>
     * @describe: ziwi溯源
     * @date 2024/12/26 13:25
     */
    void ziwiSourceCallBack(CargoOwnerDTO cargoOwnerDTO, ShipmentOrderDTO shipmentOrderDTO);
}
