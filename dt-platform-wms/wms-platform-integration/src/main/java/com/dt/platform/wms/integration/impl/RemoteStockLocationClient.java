package com.dt.platform.wms.integration.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.core.stock.client.IStockLocationClient;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.dto.StockStatisticDTO;
import com.dt.domain.core.stock.param.*;
import com.dt.domain.statistics.client.snapshot.IStockLocationSnapshotClient;
import com.dt.domain.statistics.dto.snapshot.StockLocationSnapshotDTO;
import com.dt.domain.statistics.param.snapshot.StockLocationSnapshotParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.integration.IRemoteStockLocationClient;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class RemoteStockLocationClient implements IRemoteStockLocationClient {

    @DubboReference
    private IStockLocationClient stockLocationClient;

    @DubboReference
    private IStockLocationSnapshotClient stockLocationSnapshotClient;

    @Override
    public Boolean saveOrUpdate(StockLocationBatchParam param) {
        return stockLocationClient.saveOrUpdate(param).getData();
    }


    @Override
    public Boolean save(StockLocationParam param) {
        return stockLocationClient.save(param).getData();
    }
    @Override
    public Boolean modify(StockLocationDTO param) {
        return stockLocationClient.modify(param).getData();
    }
    
    @Override
    public Boolean remove(StockLocationDTO param) {
        return stockLocationClient.remove(param).getData();
    }

    @Override
    public Boolean checkExits(StockLocationParam param) {
        return stockLocationClient.checkExits(param).getData();
    }

    @Override
    public StockLocationDTO get(StockLocationParam param) {
        return stockLocationClient.get(param).getData();
    }

    @Override
    public List<StockLocationDTO> getList(StockLocationParam param) {
        return stockLocationClient.getList(param).getData();
    }
//    @Override
//    public List<StockLocationDTO> getListForPDA(StockLocationParam param) {
//        return stockLocationClient.getListForPDA(param).getData();
//    }

    @Override
    public Page<StockLocationDTO> getPageForPDA(StockLocationParam param,String type) {
        param.setSkuQuality(type);
        return  stockLocationClient.getCustomsPageForPDA(param).getData();
    }

    @Override
    public Map findLocationSumQty(String locationCode) {
        return  stockLocationClient.findLocationSumQty(locationCode).getData();
    }

    @Override
    public List<StockLocationDTO> getListAccount(StockLocationParam param) {
        return stockLocationClient.getListAccount(param).getData();
    }

    @Override
    public Page<StockLocationDTO> getPage(StockLocationParam param) {
        return stockLocationClient.getPage(param).getData();
    }

    @Override
    public StockStatisticDTO getStatistic(StockLocationParam param) {
        return stockLocationClient.getStatistic(param).getData();
    }

    @Override
    public List<StockLocationDTO> getChargingStaticGroupBy(StockLocationParam param) {
        StockLocationSnapshotParam snapshotParam = new StockLocationSnapshotParam();
        snapshotParam.setSnapshotTime(param.getUpdatedTimeEnd());
        List<StockLocationSnapshotDTO> res =  stockLocationSnapshotClient.getChargingStaticGroupBy(snapshotParam).getData();
        return ConverterUtil.convertList(res,StockLocationDTO.class);
    }

    @Override
    public Page<StockLocationDTO> getStatisticsPage(LocationStatisticsParam param) {
        return stockLocationClient.getStatisticsPage(param).getData();

    }

    @Override
    public List<StockLocationDTO> getStatisticsList(LocationStatisticsParam param) {
        return stockLocationClient.getStatisticsList(param);
    }

    @Override
    public Page<StockLocationDTO> getStatisticsCargoLotPage(LocationStatisticsLotParam param) {
        return stockLocationClient.getStatisticsCargoLotPage(param).getData();
    }

    @Override
    public List<StockLocationDTO> queryEffectiveStockBySkuLotNo(String warehouseCode,String cargoCode,List<String> skuCodeList,
                                                                String quality,String skuLotNo, String locationType) {
        StockLocationParam param = new StockLocationParam();
        param.setSkuLotNo(skuLotNo);
        param.setWarehouseCode(warehouseCode);
        param.setCargoCode(cargoCode);
        param.setSkuCodeList(skuCodeList);
        param.setSkuQuality(quality);
        param.setLocationType(locationType);
        return stockLocationClient.queryEffectiveStockBySkuLotNo(param).getData();
    }

    @Override
    public List<StockLocationDTO> queryEffectiveStockByNoSkuLotNo(String warehouseCode, String cargoCode,
                                                                  List<String> skuCodeList, String quality,
                                                                  List<String> zoneCodeList,String locationType) {
        StockLocationParam param = new StockLocationParam();
        param.setWarehouseCode(warehouseCode);
        param.setCargoCode(cargoCode);
        param.setSkuCodeList(skuCodeList);
        param.setSkuQuality(quality);
        if(!CollectionUtils.isEmpty(zoneCodeList)){
            param.setZoneCodeList(zoneCodeList);
        }
        param.setLocationType(locationType);
        return stockLocationClient.queryEffectiveStockByNoSkuLotNo(param).getData();
    }

    @Override
    public List<StockLocationDTO> getListByPage(StockLocationParam stockLocationParam) {
        List<StockLocationDTO> stockLocationDTOS = new ArrayList<>();
        stockLocationParam.setSize(2000);
        Page<StockLocationDTO> locationClientPage = stockLocationClient.getPage(stockLocationParam).getData();
        for (int i = 1; i <= locationClientPage.getPages(); i++) {
            stockLocationParam.setCurrent(i);
            stockLocationParam.setSize(2000);
            Page<StockLocationDTO> clientPage = stockLocationClient.getPage(stockLocationParam).getData();
            stockLocationDTOS.addAll(clientPage.getRecords());
        }
        return stockLocationDTOS;
    }
}
