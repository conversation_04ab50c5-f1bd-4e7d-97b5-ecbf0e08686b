package com.dt.platform.wms.integration.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.domain.base.client.IWarehouseClient;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.param.WarehouseParam;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.integration.IRemoteWarehouseClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class RemoteWarehouseClient implements IRemoteWarehouseClient {

    @DubboReference(timeout = 600000)
    private IWarehouseClient warehouseClient;

    @Resource
    WmsOtherConfig wmsOtherConfig;

    @Override
    public WarehouseDTO queryById(Long id) {
        WarehouseParam param = new WarehouseParam();
        param.setId(id);
        return warehouseClient.get(param).getData();
    }

    @Override
    public Boolean disable(WarehouseParam param) {
        return warehouseClient.modify(param).getData();
    }

    @Override
    public Boolean enable(WarehouseParam param) {
        return warehouseClient.modify(param).getData();
    }

    @Override
    public Boolean update(WarehouseParam param) {
        return warehouseClient.modify(param).getData();
    }

    @Override
    public IPage<WarehouseDTO> queryPage(WarehouseParam param) {
        return warehouseClient.getPage(param).getData();
    }

    @Override
    public WarehouseDTO queryByCode(String code) {
        WarehouseParam param = new WarehouseParam();
        param.setCode(code);
        return warehouseClient.get(param).getData();
    }

    @Override
    public Boolean save(WarehouseParam param) {
        return warehouseClient.save(param).getData();
    }

    @Override
    public Boolean modify(WarehouseParam param) {
        return warehouseClient.modify(param).getData();
    }

    @Override
    public List<WarehouseDTO> queryList(WarehouseParam param) {
        return warehouseClient.getList(param).getData();
    }

    @Override
    public Integer createOrUpdateTable(String sql) {
        return warehouseClient.createOrUpdateTable(sql).getData();
    }

    @Override
    public Boolean getDeclarationWarehouse(String warehouseCode) {
        WarehouseParam param = new WarehouseParam();
        param.setCode(warehouseCode);
        WarehouseDTO warehouseDTO = warehouseClient.get(param).getData();
        //跨境默认开启关仓
        if (warehouseDTO != null && Objects.equals(warehouseDTO.getType(), TaxTypeEnum.TYPE_BONDED_TAX.getCode())) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean getTaoTianWarehouse(String warehouseCode) {
        if (!CollectionUtils.isEmpty(wmsOtherConfig.getTaotainWarehouseCodeList()) &&
                wmsOtherConfig.getTaotainWarehouseCodeList().contains(warehouseCode)) {
            return true;
        }
        return false;
    }


}
