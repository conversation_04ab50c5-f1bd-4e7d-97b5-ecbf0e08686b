package com.dt.platform.wms.integration.impl;

import cn.hutool.json.JSONUtil;
import com.dt.platform.utils.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Function;

@Slf4j
public class CallOtherSupport {

    public static  <T, R> R execute(Function<T, R> runnable, T param, String... logKey) {
        try {
            log.info("{} param {}", null == logKey ? "" : logKey[0], JSONUtil.toJsonStr(param));
            R apply = runnable.apply(param);
            log.info("{} result {}", null == logKey ? "" : logKey[0], JSONUtil.toJsonStr(apply));
            return apply;
        } catch (Exception exception) {
            log.error("{} error {}", null == logKey ? "" : logKey[0], exception.getMessage(), exception);
            throw ExceptionUtil.exceptionWithMessage(exception.getMessage());
        }
    }
}
