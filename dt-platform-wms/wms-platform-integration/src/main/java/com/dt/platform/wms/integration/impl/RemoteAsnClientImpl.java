package com.dt.platform.wms.integration.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.danding.business.client.rpc.goods.facade.ITallyReportRpcFacade;
import com.danding.business.client.rpc.goods.result.ResultResponse;
import com.danding.core.tenant.SimpleTenantHelper;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.FromSourceEnum;
import com.dt.component.common.enums.asn.AsnDetailMarkEnum;
import com.dt.component.common.enums.asn.AsnOrderTagEnum;
import com.dt.component.common.enums.asn.AsnStatusEnum;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.pkg.PackageUnitEnum;
import com.dt.component.common.enums.pre.SkuIsPreEnum;
import com.dt.component.common.enums.sku.SkuLifeCtrlEnum;
import com.dt.component.common.enums.sku.SkuNewOrOldCtrlEnum;
import com.dt.component.common.enums.sku.SkuTypeEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.enums.stock.OperationTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.CurrentUserInfoProviderFilter;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.msg.StockOperationMessage;
import com.dt.component.common.result.Result;
import com.dt.domain.base.client.ISkuClient;
import com.dt.domain.base.client.ISkuLotClient;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.config.CargoTenantDTO;
import com.dt.domain.base.dto.sku.SkuLogDTO;
import com.dt.domain.base.param.LotRuleParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUomParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.base.param.config.CargoTenantParam;
import com.dt.domain.bill.bo.*;
import com.dt.domain.bill.bo.asn.AsnZeroReceiveBO;
import com.dt.domain.bill.client.IAsnBillClient;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.tally.TallyDTO;
import com.dt.domain.bill.param.AsnModifyParam;
import com.dt.domain.bill.param.AsnParam;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.taotian.*;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.config.IRemoteCargoTenantClient;
import com.dt.platform.wms.integration.sku.IRemoteSkuLogClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/21 16:37
 */
@Service
@Slf4j
public class RemoteAsnClientImpl implements IRemoteAsnClient {

    @DubboReference
    IAsnBillClient iAsnBillClient;

    @DubboReference
    ISkuLotClient skuLotClient;

    @DubboReference
    ITallyReportRpcFacade tallyReportRpcFacade;

    @Resource
    IRemoteMessageClient remoteMessageClient;

    @Resource
    private RedissonClient redissonClient;

    @DubboReference
    ISkuClient skuClient;

    @Resource
    WmsOtherConfig wmsOtherConfig;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    IRemoteSkuLogClient remoteSkuLogClient;


    @Resource
    IRemoteAllocationRuleClient remoteAllocationRuleClient;

    @Resource
    IRemoteTurnoverRuleClient remoteTurnoverRuleClient;

    @Resource
    IRemoteLotRuleClient remoteLotRuleClient;


    @Resource
    IRemoteCargoTenantClient remoteCargoTenantClient;

    @Override
    public Boolean cancelAsn(AsnCancelBO asnCancelBO) {
        return iAsnBillClient.cancelAsn(asnCancelBO).getData();
    }

    @Override
    public IPage<AsnDTO> queryPage(AsnParam param) {
        return iAsnBillClient.getPage(param).getData();
    }

    @Override
    public AsnDTO queryOneByAsnId(String asnId) {
        AsnParam param = new AsnParam();
        param.setAsnId(asnId);
        return iAsnBillClient.get(param).getData();
    }

    @Override
    public AsnDTO get(AsnParam param) {
        return iAsnBillClient.get(param).getData();
    }

    @Override
    public Boolean modifyStatus(AsnModifyParam param) {
        return iAsnBillClient.modifyStatus(param).getData();
    }

    @Override
    public AsnDetailDataDTO queryAsnDetailByAsnId(String asnId) {
        AsnParam param = new AsnParam();
        param.setAsnId(asnId);
        return iAsnBillClient.queryAsnDetail(param).getData();
    }

    @Override
    public Boolean receiptAsn(AsnReceiptDataDTO dataDTO) {
        return iAsnBillClient.receiptAsn(dataDTO).getData();
    }

    @Override
    public Boolean checkSoNoExistsByCargoCode(String soNo, String cargoCode) {
        AsnParam param = new AsnParam();
        param.setCargoCode(cargoCode);
        param.setSoNo(soNo);
        return iAsnBillClient.checkSoNoExistsByCargoCode(param).getData();
    }

    @Override
    public AsnDTO queryAsnBySoNo(AsnParam asnParam) {
        return iAsnBillClient.queryAsnBySoNo(asnParam).getData();
    }

    @Override
    public Integer updateAsnPrintNum(AsnParam asnParam) {
        return iAsnBillClient.updateAsnPrintNum(asnParam).getData();
    }

    @Override
    public Integer updateRecPrintNum(AsnParam asnParam) {
        return iAsnBillClient.updateRecPrintNum(asnParam).getData();
    }

    @Override
    public Boolean commitAsn(AsnDTO asnDTO) {
        return iAsnBillClient.commitAsn(asnDTO).getData();
    }

    @Override
    public List<AsnDetailDTO> getDetailList(AsnParam param) {
        return iAsnBillClient.getDetailList(param).getData();
    }

    @Override
    public List<AsnDTO> getList(AsnParam param) {
        return iAsnBillClient.getList(param).getData();
    }

    @Override
    public Boolean saveAsnLog(AsnLogDTO asnLogDTO) {
        return iAsnBillClient.saveAsnLog(asnLogDTO).getData();
    }

    @Override
    public List<AsnLogDTO> getAsnLogList(AsnParam asnParam) {
        return iAsnBillClient.getAsnLogList(asnParam).getData();
    }

    @Override
    public IPage<AsnDetailDTO> queryDetailPage(AsnParam param) {
        return iAsnBillClient.queryDetailPage(param).getData();
    }

    @Override
    public IPage<AsnDetailDTO> getPageGroupDetail(AsnParam param) {
        return iAsnBillClient.getPageGroupDetail(param).getData();
    }

    @Override
    public Boolean modifyBatch(AsnBillCompleteBO asnCompleteBO) {
        return iAsnBillClient.modifyBatch(asnCompleteBO).getData();
    }

    @Override
    public IPage<AsnLogDTO> queryLogPage(AsnParam asnParam) {
        return iAsnBillClient.queryLogPage(asnParam).getData();
    }

    @Override
    public Boolean save(AsnDTO asnDTO) {
        return iAsnBillClient.save(asnDTO).getData();
    }

    @Override
    public Boolean modifyAndRomve(AsnDTO asnDTO) {
        return iAsnBillClient.modifyAndRomve(asnDTO).getData();
    }

    @Override
    public Boolean cancelBatch(AsnBillCancelBO asnBillCancelBO) {
        return iAsnBillClient.cancelBatch(asnBillCancelBO).getData();
    }

    @Override
    public Boolean saveImportBatch(AsnBillImportBO asnBillImportBO) {
        return iAsnBillClient.saveImportBatch(asnBillImportBO).getData();
    }

    @Override
    public Boolean modify(AsnDTO asnDTO) {
        return iAsnBillClient.modify(asnDTO).getData();
    }

    @Override
    public Boolean modifyArrivalBatch(AsnBatchArrivalBO asnBatchArrivalBO) {
        return iAsnBillClient.modifyArrivalBatch(asnBatchArrivalBO).getData();
    }

    @Override
    public Boolean modifyBatchAsnDTO(List<AsnDTO> asnDTOList) {
        return iAsnBillClient.modifyBatchAsnDTO(asnDTOList).getData();
    }

    @Override
    public Boolean saveAsnLogBatch(List<AsnLogDTO> asnLogDTOList) {
        return iAsnBillClient.saveAsnLogBatch(asnLogDTOList).getData();
    }

    @Override
    public List<AsnDTO> getAppointMultipleParam(AsnParam asnParam, List<String> tableFields) {
        return iAsnBillClient.getAppointMultipleParam(asnParam, tableFields).getData();
    }

    @Override
    public Boolean commitBoxDataAsn(AsnDTO asnDTO) {
        return iAsnBillClient.commitBoxDataAsn(asnDTO).getData();
    }

    @Override
    public Boolean modifyAndAddMessage(AsnDTO asnDTO) {
        return iAsnBillClient.modifyAndAddMessage(asnDTO).getData();
    }

    @Override
    public Boolean modifyLinkAsn(List<SkuLotDTO> modifySkuLotDTOList, AsnLinkModifyBO asnLinkModifyBO, TallyDTO tallyDTO) {
        //调用ERP
        setTenantId(tallyDTO.getWarehouseCode(), tallyDTO.getCargoCode());
        ResultResponse synTallyNo = tallyReportRpcFacade.synTallyNo(tallyDTO.getTallyCode(), asnLinkModifyBO.getLinkAsnDTO().getSoNo(), asnLinkModifyBO.getAsnDTO().getAsnId());
        log.info("modifyLinkAsn notice erp:{}", JSONUtil.toJsonStr(synTallyNo));
        if (!synTallyNo.isSuccess()) {
            throw new BaseException(BaseBizEnum.TIP, "ERP同步失败");
        }
        if (!CollectionUtils.isEmpty(modifySkuLotDTOList)) {
            skuLotClient.modifyBatch(modifySkuLotDTOList);
        }
        Boolean aBoolean = iAsnBillClient.modifyLinkAsn(asnLinkModifyBO).getData();
        if (asnLinkModifyBO.getMessageMqDTO() != null) {
            try {
                StockOperationMessage stockOperationMessage = new StockOperationMessage();
                stockOperationMessage.setWarehouseCode(asnLinkModifyBO.getMessageMqDTO().getWarehouseCode());
                stockOperationMessage.setBillNo(asnLinkModifyBO.getMessageMqDTO().getBillNo());
                stockOperationMessage.setOperationType(OperationTypeEnum.OPERATION_SHELF_RELEASE.getType());
                stockOperationMessage.setCargoCode(asnLinkModifyBO.getMessageMqDTO().getCargoCode());
                stockOperationMessage.setBillType(BillTypeEnum.BILL_TYPE_ASN_IN.getType());
                remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return aBoolean;
    }

    @Override
    public String originExpressNo(String extraJson) {
        if (StrUtil.isBlank(extraJson)) return StrUtil.EMPTY;
        JSONObject jsonObject = JSONUtil.parseObj(extraJson);
        return jsonObject.getStr("logisticsNo");
    }

    @Override
    public String asnPriority(String billNo, String sourceData) {
        TTOrderScheduleParam ttOrderScheduleParam = JSONUtil.toBean(sourceData, TTOrderScheduleParam.class);
        RLock lock = redissonClient.getLock("asnPriority:" + billNo);
        try {
            boolean tryLock = lock.tryLock(1, 5, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后");
            }
            for (TTOrderScheduleDetailParam detailParam : ttOrderScheduleParam.getDetailParamList()) {
                //设置数据源
                RpcContextUtil.setWarehouseCode(detailParam.getResourceCode());
                //
                AsnParam asnParam = new AsnParam();
                asnParam.setWarehouseCode(detailParam.getResourceCode());
                asnParam.setPoNo(detailParam.getOrderCode());
                List<AsnDTO> asnDTOList = iAsnBillClient.getList(asnParam).getData();
                if (CollectionUtils.isEmpty(asnDTOList)) {
                    continue;
                }
                asnDTOList = asnDTOList.stream().filter(a -> !Objects.equals(AsnStatusEnum.CANCEL.getCode(), a.getStatus())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(asnDTOList)) {
                    continue;
                }
                AsnDTO asnDTO = asnDTOList.get(0);
                asnDTO.setPriority(detailParam.getPriority());
                if (!StringUtils.isEmpty(detailParam.getExpectFinishTime())) {
                    asnDTO.setDesireCompleteTime(DateUtil.parse(detailParam.getExpectFinishTime(), "yyyy-MM-dd HH:mm:ss").getTime());
                }
                AsnLogDTO asnLogDTO = new AsnLogDTO();
                asnLogDTO.setWarehouseCode(asnDTO.getWarehouseCode());
                asnLogDTO.setCargoCode(asnDTO.getCargoCode());
                asnLogDTO.setAsnId(asnDTO.getAsnId());
                asnLogDTO.setOpBy("system");
                asnLogDTO.setMsg("淘天下发优先级指令:" + detailParam.getPriorityDesc());
                asnLogDTO.setRemark(JSONUtil.toJsonStr(detailParam));
                iAsnBillClient.modify(asnDTO);
                iAsnBillClient.saveAsnLog(asnLogDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage();
            throw new BaseException(BaseBizEnum.TIP, errorMsg);
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return "ok";
    }

    @Override
    public String asnApprove(String businessNo, String sourceData) {
        TTQualityInspectionApproveParam approveParam = JSONUtil.toBean(sourceData, TTQualityInspectionApproveParam.class);
        RLock lock = redissonClient.getLock("asnApprove:" + approveParam.getWarehouseCode() + businessNo);
        try {
            boolean tryLock = lock.tryLock(1, 5, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后");
            }
            //设置数据源
            RpcContextUtil.setWarehouseCode(approveParam.getWarehouseCode());
            //
            AsnParam asnParam = new AsnParam();
            asnParam.setWarehouseCode(approveParam.getWarehouseCode());
            asnParam.setPoNo(approveParam.getEntryOrderCode());
            List<AsnDTO> asnDTOList = iAsnBillClient.getList(asnParam).getData();
            if (CollectionUtils.isEmpty(asnDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "未找到仓库作业单");
            }
            asnDTOList = asnDTOList.stream().filter(a -> !Objects.equals(AsnStatusEnum.CANCEL.getCode(), a.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(asnDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "未找到仓库有效作业单");
            }
            /**
             接收 品控查验结果：
             验货通过： 入库单标记【验货通过】，去除【需验货】标记
             验货不通过： 入库单标记【验货不通过】，去除【需验货】标记
             */
            AsnDTO asnDTO = asnDTOList.get(0);
            if (!AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.WAIT_CHECK_GOODS)
                    && !AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.PART_CHECK_FAIL)) {
//                throw new BaseException(BaseBizEnum.TIP, "仓库作业单无【需验货】");
                return "仓库作业单无【需验货】";
            }

            //获取入库单明细
            AsnParam asnDetailParam = new AsnParam();
            asnDetailParam.setAsnId(asnDTO.getAsnId());
            List<AsnDetailDTO> asnDetailDTOList = iAsnBillClient.getDetailList(asnDetailParam).getData();
            if (CollectionUtils.isEmpty(asnDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "未找到仓库有效作业单明细");
            }
            List<TTQualityInspectionApproveItem> items = approveParam.getItems();
            //入库单接收验货结果，入库单 和 入库单明细上去除【待验货】标记
            for (AsnDetailDTO asnDetailDTO : asnDetailDTOList) {
                TTQualityInspectionApproveItem ttQualityInspectionInstructItem = items.stream()
                        .filter(a -> a.getItemCode().equals(asnDetailDTO.getSkuCode())).findFirst().orElse(null);
                if (ttQualityInspectionInstructItem != null && Objects.equals(ttQualityInspectionInstructItem.getIsApprove(), "true")) {
                    Set<AsnDetailMarkEnum> asnDetailMarkEnumSet = AsnDetailMarkEnum.NumToEnum(asnDetailDTO.getMark());
                    asnDetailMarkEnumSet.remove(AsnDetailMarkEnum.WAIT_INSPECTION);
                    asnDetailMarkEnumSet.remove(AsnDetailMarkEnum.INSPECTION_FAIL);
                    asnDetailMarkEnumSet.add(AsnDetailMarkEnum.INSPECTION_AUTH);
                    asnDetailDTO.setMark(AsnDetailMarkEnum.enumToNum(asnDetailMarkEnumSet.stream().collect(Collectors.toList())));
                }

                if (ttQualityInspectionInstructItem != null && Objects.equals(ttQualityInspectionInstructItem.getIsApprove(), "false")) {
                    Set<AsnDetailMarkEnum> asnDetailMarkEnumSet = AsnDetailMarkEnum.NumToEnum(asnDetailDTO.getMark());
                    asnDetailMarkEnumSet.remove(AsnDetailMarkEnum.WAIT_INSPECTION);
                    asnDetailMarkEnumSet.add(AsnDetailMarkEnum.INSPECTION_FAIL);
                    asnDetailDTO.setMark(AsnDetailMarkEnum.enumToNum(asnDetailMarkEnumSet.stream().collect(Collectors.toList())));
                }
            }
            //明细无待验货的标记 移除【需验货】
            if (asnDetailDTOList.stream().allMatch(a -> !AsnDetailMarkEnum.NumToEnum(a.getMark()).contains(AsnDetailMarkEnum.WAIT_INSPECTION))) {
                Set<AsnOrderTagEnum> asnOrderTagEnums = AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag());
                asnOrderTagEnums.remove(AsnOrderTagEnum.WAIT_CHECK_GOODS);
                if (CollectionUtils.isEmpty(asnOrderTagEnums)) {
                    asnDTO.setOrderTag(0);
                } else {
                    asnDTO.setOrderTag(AsnOrderTagEnum.enumToNum(asnOrderTagEnums.stream().collect(Collectors.toList())));
                }
            }
            //入库单主单： 明细行存在【验货不通过】，打标【部分验货失败】；否则不操作
            if (asnDetailDTOList.stream().anyMatch(a -> AsnDetailMarkEnum.NumToEnum(a.getMark()).contains(AsnDetailMarkEnum.INSPECTION_FAIL))) {
                Set<AsnOrderTagEnum> asnOrderTagEnums = AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag());
                asnOrderTagEnums.add(AsnOrderTagEnum.PART_CHECK_FAIL);
                if (CollectionUtils.isEmpty(asnOrderTagEnums)) {
                    asnDTO.setOrderTag(0);
                } else {
                    asnDTO.setOrderTag(AsnOrderTagEnum.enumToNum(asnOrderTagEnums.stream().collect(Collectors.toList())));
                }
            }
            //移除部分验货失败
            if (asnDetailDTOList.stream().noneMatch(a -> AsnDetailMarkEnum.NumToEnum(a.getMark()).contains(AsnDetailMarkEnum.INSPECTION_FAIL))) {
                Set<AsnOrderTagEnum> asnOrderTagEnums = AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag());
                asnOrderTagEnums.remove(AsnOrderTagEnum.PART_CHECK_FAIL);
                if (CollectionUtils.isEmpty(asnOrderTagEnums)) {
                    asnDTO.setOrderTag(0);
                } else {
                    asnDTO.setOrderTag(AsnOrderTagEnum.enumToNum(asnOrderTagEnums.stream().collect(Collectors.toList())));
                }
            }
            asnDTO.setAsnDetailDTOS(asnDetailDTOList);
            AsnLogDTO asnLogDTO = new AsnLogDTO();
            asnLogDTO.setWarehouseCode(asnDTO.getWarehouseCode());
            asnLogDTO.setCargoCode(asnDTO.getCargoCode());
            asnLogDTO.setAsnId(asnDTO.getAsnId());
            asnLogDTO.setOpBy("system");
            asnLogDTO.setMsg("品控入仓验货查验结果下发");
            String originParam = JSONUtil.toJsonStr(approveParam);
            if (originParam.length() > 1024) {
                originParam = originParam.substring(0, 1000);
            }
            asnLogDTO.setRemark(originParam);
            asnDTO.setAsnLogDTO(asnLogDTO);
            iAsnBillClient.commitAsn(asnDTO);
        } catch (Exception e) {
            e.printStackTrace();
            String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage();
            throw new BaseException(BaseBizEnum.TIP, errorMsg);
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return "ok";
    }

    @Override
    public String asnInstruct(String businessNo, String sourceData) {
        TTQualityInspectionInstructParam instructParam = JSONUtil.toBean(sourceData, TTQualityInspectionInstructParam.class);
        RLock lock = redissonClient.getLock("asnInstruct:" + instructParam.getWarehouseCode() + businessNo);
        try {
            boolean tryLock = lock.tryLock(1, 5, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在操作,请稍后");
            }
            //设置数据源
            RpcContextUtil.setWarehouseCode(instructParam.getWarehouseCode());
            //
            AsnParam asnParam = new AsnParam();
            asnParam.setWarehouseCode(instructParam.getWarehouseCode());
            asnParam.setPoNo(instructParam.getEntryOrderCode());
            List<AsnDTO> asnDTOList = iAsnBillClient.getList(asnParam).getData();
            if (CollectionUtils.isEmpty(asnDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "未找到仓库作业单");
            }
            asnDTOList = asnDTOList.stream().filter(a -> !Objects.equals(AsnStatusEnum.CANCEL.getCode(), a.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(asnDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "未找到仓库有效作业单");
            }
            /**
             * 入库单明细上根据淘天指令打标记
             *
             * 需要验货的打标【需验货】
             * 无需验货的商品不达标
             * 2. 入库单按照淘天的订单标记，需要验货的打标【需验货】，去除【等待验货指令】标记
             */
            AsnDTO asnDTO = asnDTOList.get(0);
            if (!AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag()).contains(AsnOrderTagEnum.WAIT_INSPECTION)) {
                return "无等待验货指令";
            }

            //获取入库单明细
            AsnParam asnDetailParam = new AsnParam();
            asnDetailParam.setAsnId(asnDTO.getAsnId());
            List<AsnDetailDTO> asnDetailDTOList = iAsnBillClient.getDetailList(asnDetailParam).getData();
            if (CollectionUtils.isEmpty(asnDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "未找到仓库有效作业单明细");
            }
            List<TTQualityInspectionInstructItem> items = instructParam.getItems();
            for (AsnDetailDTO asnDetailDTO : asnDetailDTOList) {
                TTQualityInspectionInstructItem ttQualityInspectionInstructItem = items.stream().filter(a -> a.getItemCode().equals(asnDetailDTO.getSkuCode())).findFirst().orElse(null);
                if (ttQualityInspectionInstructItem != null && Objects.equals(ttQualityInspectionInstructItem.getIsNeedQc(), "true")) {
                    asnDetailDTO.setMark(AsnDetailMarkEnum.enumToNum(AsnDetailMarkEnum.WAIT_INSPECTION));
                }
                if (ttQualityInspectionInstructItem != null && Objects.equals(ttQualityInspectionInstructItem.getIsNeedQc(), "false")) {
                    asnDetailDTO.setMark(AsnDetailMarkEnum.enumToNum(AsnDetailMarkEnum.INSPECTION_AUTH));
                }
            }
            Set<AsnOrderTagEnum> asnOrderTagEnums = AsnOrderTagEnum.NumToEnum(asnDTO.getOrderTag());
            asnOrderTagEnums.remove(AsnOrderTagEnum.WAIT_INSPECTION);
            //无需验货不需要 加上【需验货】标记
            if (asnDetailDTOList.stream().anyMatch(a -> AsnDetailMarkEnum.NumToEnum(a.getMark()).contains(AsnDetailMarkEnum.WAIT_INSPECTION))) {
                asnOrderTagEnums.add(AsnOrderTagEnum.WAIT_CHECK_GOODS);
            }
            asnDTO.setOrderTag(AsnOrderTagEnum.enumToNum(asnOrderTagEnums.stream().collect(Collectors.toList())));

            asnDTO.setAsnDetailDTOS(asnDetailDTOList);
            AsnLogDTO asnLogDTO = new AsnLogDTO();
            asnLogDTO.setWarehouseCode(asnDTO.getWarehouseCode());
            asnLogDTO.setCargoCode(asnDTO.getCargoCode());
            asnLogDTO.setAsnId(asnDTO.getAsnId());
            asnLogDTO.setOpBy("system");
            asnLogDTO.setMsg("品控入仓验货指令下发");
            String originParam = JSONUtil.toJsonStr(instructParam);
            if (originParam.length() > 1024) {
                originParam = originParam.substring(0, 1000);
            }
            asnLogDTO.setRemark(originParam);
            asnDTO.setAsnLogDTO(asnLogDTO);
            iAsnBillClient.commitAsn(asnDTO);
        } catch (Exception e) {
            e.printStackTrace();
            String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage();
            throw new BaseException(BaseBizEnum.TIP, errorMsg);
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return "ok";
    }

    @Override
    public IPage<AsnDetailDTO> getPageGroupDetailByReTurn(AsnParam param) {
        param.setDeleted(1);
        return iAsnBillClient.getPageGroupDetailByReTurn(param).getData();
    }

    @Override
    public IPage<AsnDetailDTO> getSkuGroupPage(AsnParam asnParam) {
        return iAsnBillClient.getSkuGroupPage(asnParam).getData();
    }

    @Override
    public Result<String> lyCreateSkuByAsnDetail(AsnDTO asnDTOFrom) {
        RLock lock = redissonClient.getLock("lyCreateSkuByAsnDetail:" + asnDTOFrom.getWarehouseCode() + asnDTOFrom.getCargoCode());
        try {
            boolean tryLock = lock.tryLock(1, 360, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "并发锁,请稍后");
            }
            AsnParam asnParamNew = new AsnParam();
            asnParamNew.setAsnId(asnDTOFrom.getAsnId());
            AsnDTO asnDTONew = iAsnBillClient.get(asnParamNew).getData();
            if (asnDTONew == null || !Objects.equals(asnDTONew.getStatus(), AsnStatusEnum.WAIT_HAND.getCode())) {
                return Result.success("ok");
            }
            AsnParam asnParam = new AsnParam();
            asnParam.setAsnId(asnDTONew.getAsnId());
            List<AsnDetailDTO> asnDetailDTOList = iAsnBillClient.getDetailList(asnParam).getData();
            if (CollectionUtils.isEmpty(asnDetailDTOList)) {
                return Result.failWithMessage("入库单明细为空");
            }
            List<String> skuCodeList = asnDetailDTOList.stream().map(AsnDetailDTO::getSkuCode).distinct().collect(Collectors.toList());

            SkuParam skuParam = new SkuParam();
            skuParam.setCargoCode(asnDTONew.getCargoCode());
            skuParam.setCodeList(skuCodeList);
            List<SkuDTO> skuDTOList = skuClient.getList(skuParam).getData();
            if (CollectionUtils.isEmpty(skuDTOList)) {
                skuDTOList = new ArrayList<>();
            }
            if (skuCodeList.size() != skuDTOList.size()) {
                Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity()));

                Map<String, AsnDetailDTO> asnDetailMap = asnDetailDTOList.stream().collect(Collectors.toMap(AsnDetailDTO::getSkuCode, Function.identity(), BinaryOperator.minBy(Comparator.comparing(AsnDetailDTO::getId))));
                asnDetailMap.forEach((skuCode, asnDetailDTO) -> {
                    if (!skuDTOMap.containsKey(skuCode)) {
                        buildAddSKUDTO(asnDTONew, asnDetailDTO);
                    }
                });
            }

            Boolean createSku = checkCreateSku(asnDTONew.getAsnId());
            if (!createSku) {
//                throw new BaseException(BaseBizEnum.TIP, "LY_CREATE_SKU_ERROR_NEED_CHECK");
                return Result.failWithMessage("LY_CREATE_SKU_ERROR_NEED_CHECK");
            }
        } catch (Exception e) {
            log.error("lyCreateSkuByAsnDetail error{}", e.getMessage(), e);
            String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage();
//            throw new BaseException(BaseBizEnum.TIP, errorMsg);
            return Result.failWithMessage(errorMsg);
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return Result.success("ok");
    }

    @Override
    public String zeroReceive(AsnZeroReceiveBO asnZeroReceiveBO) {
        return iAsnBillClient.zeroReceive(asnZeroReceiveBO).getData();
    }

    @Override
    public String modifyAsnByZeroReceive(AsnDTO asnDTO) {
        return iAsnBillClient.modifyAsnByZeroReceive(asnDTO).getData();
    }

    private Boolean checkCreateSku(String asnId) {

        AsnParam asnParam = new AsnParam();
        asnParam.setAsnId(asnId);
        AsnDTO asnDTO = iAsnBillClient.get(asnParam).getData();
        if (asnDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "入库单信息异常,不存在");
        }
        List<AsnDetailDTO> asnDetailDTOList = iAsnBillClient.getDetailList(asnParam).getData();
        if (CollectionUtils.isEmpty(asnDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "入库单明细信息异常,不存在");
        }
        List<String> skuCodeList = asnDetailDTOList.stream().map(AsnDetailDTO::getSkuCode).distinct().collect(Collectors.toList());

        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(asnDTO.getCargoCode());
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = skuClient.getList(skuParam).getData();
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return false;
        }
        if (skuCodeList.size() == skuDTOList.size()) {
            asnDTO.setStatus(AsnStatusEnum.CREATE.getCode());
            iAsnBillClient.modify(asnDTO);
        }
        return false;
    }

    private void buildAddSKUDTO(AsnDTO asnDTO, AsnDetailDTO asnDetailDTO) {
        // 商品记录添加
        SkuParam skuDTO = new SkuParam();
        skuDTO.setCargoCode(asnDTO.getCargoCode());
        skuDTO.setCode(asnDetailDTO.getSkuCode());
        skuDTO.setName(asnDetailDTO.getSkuName());
        skuDTO.setIsLifeMgt(SkuLifeCtrlEnum.SKU_LIFE_CTRL_NO.getCode());
        skuDTO.setType(SkuTypeEnum.TYPE_DUTY_TAX.getStatus() + "");
        //默认新品
        skuDTO.setIsNewRecord(SkuNewOrOldCtrlEnum.SKU_OLD_CTRL_NO.getCode());
        // 默认批次规则
        if (StringUtils.isEmpty(wmsOtherConfig.getDefaultLyLotRuleName())) {
            throw new BaseException(BaseBizEnum.TIP, "乐漾默认批次规则未配置");
        }
        LotRuleParam lotRuleParam = new LotRuleParam();
        lotRuleParam.setName(wmsOtherConfig.getDefaultLyLotRuleName());
        List<LotRuleDTO> lotRuleDTOList = remoteLotRuleClient.getList(lotRuleParam);
        if (CollectionUtils.isEmpty(lotRuleDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "乐漾默认批次规则名称未配置");
        }
        LotRuleDTO lotRuleDTO = lotRuleDTOList.stream().filter(a -> Objects.equals(a.getName(), wmsOtherConfig.getDefaultLyLotRuleName())).findFirst().orElse(null);
        if (lotRuleDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "乐漾默认批次规则名称未配置");
        }
        skuDTO.setLotRuleCode(lotRuleDTO.getCode());
        // 默认分配规则
        AllocationRuleDTO allocationRuleDTO = remoteAllocationRuleClient.queryAllocationDefaultRule();
        if (allocationRuleDTO != null) {
            skuDTO.setAllocationRuleCode(allocationRuleDTO.getCode());
        } else {
            skuDTO.setAllocationRuleCode("");
        }
        // 默认周转规则
        TurnoverRuleDTO turnoverRuleDTO = remoteTurnoverRuleClient.queryTurnoverDefaultRule();
        if (turnoverRuleDTO != null) {
            skuDTO.setTurnoverRuleCode(turnoverRuleDTO.getCode());
        } else {
            skuDTO.setTurnoverRuleCode("");
        }
        // 预包商品
        skuDTO.setOutCode(asnDetailDTO.getSkuCode());
        skuDTO.setIsPre(SkuIsPreEnum.NORMAL.getCode());
        skuDTO.setFromSource(FromSourceEnum.WMS.value());
        // 长宽高
        skuDTO.setLength(BigDecimal.ONE);
        skuDTO.setHeight(BigDecimal.ONE);
        skuDTO.setWidth(BigDecimal.ONE);
        skuDTO.setGrossWeight(new BigDecimal("0.5"));
        skuDTO.setNetWeight(new BigDecimal("0.5"));
        skuDTO.setCartonWeight(new BigDecimal("0.5"));

        skuDTO.setStandardLength(BigDecimal.ONE);
        skuDTO.setStandardHeight(BigDecimal.ONE);
        skuDTO.setStandardWidth(BigDecimal.ONE);
        skuDTO.setVolume(BigDecimal.ONE);
        skuDTO.setStandardVolume(BigDecimal.ONE);

        skuDTO.setCartonPcs(asnDetailDTO.getExportCartonPcs());
        // 商品条码
        SkuUpcParam upcParam = new SkuUpcParam();
        upcParam.setCargoCode(asnDTO.getCargoCode());
        upcParam.setSkuCode(asnDetailDTO.getSkuCode());
        upcParam.setUpcCode(asnDetailDTO.getSkuCode());
        upcParam.setIsDefault(SkuUpcDefaultEnum.YES.value());
        upcParam.setPackageUnitCode(PackageUnitEnum.CTN.getCode());
        // uom
        SkuUomParam uomParam = new SkuUomParam();
        uomParam.setCargoCode(asnDTO.getCargoCode());
        uomParam.setSkuCode(asnDetailDTO.getSkuCode());
        uomParam.setPackageUnitCode(PackageUnitEnum.CTN.getCode());
        uomParam.setPackageQty(BigDecimal.ONE);
        skuDTO.setSkuUomList(Collections.singletonList(uomParam));
        skuDTO.setSkuUpcList(Collections.singletonList(upcParam));
        skuDTO.setErpLog("(WMS新增【乐漾】商品)原始报文:" + JSON.toJSONString(skuDTO));


        RpcContext.getContext().setAttachment(CurrentUserInfoProviderFilter.DT_CURRENT_USER_ID, String.valueOf(asnDetailDTO.getExportUserId()));
        RpcContext.getContext().setAttachment(CurrentUserInfoProviderFilter.DT_CURRENT_USER_NAME, asnDetailDTO.getExportUserName());

        skuDTO.setCreatedBy(asnDetailDTO.getExportUserName());
        skuDTO.setCreatedUserId(asnDetailDTO.getExportUserId());


        remoteSkuClient.buildWMSAddSave(skuDTO);

        SkuLogDTO skuLogDTO = new SkuLogDTO();
        skuLogDTO.setCargoCode(skuDTO.getCargoCode());
        skuLogDTO.setSkuCode(skuDTO.getCode());
        skuLogDTO.setWarehouseCode(skuDTO.getWarehouseCode());
        skuLogDTO.setOpBy(CurrentUserHolder.getUserName());
        skuLogDTO.setOpDate(System.currentTimeMillis());
        skuLogDTO.setOpContent("【乐漾】新增商品档案:" + skuDTO.getCode());
        skuLogDTO.setOpRemark("原始报文:" + JSON.toJSONString(skuDTO));
        remoteSkuLogClient.save(skuLogDTO);
    }

    private void setTenantId(String warehouse, String cargoCode) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        CargoTenantParam param = new CargoTenantParam();
        param.setCargoCode(cargoCode);
        param.setWarehouse(warehouse);
        CargoTenantDTO cargoTenantDTO = remoteCargoTenantClient.get(param);
        if (cargoTenantDTO != null && cargoTenantDTO.getTenantId() != null) {
            SimpleTenantHelper.setTenantId(Long.valueOf(cargoTenantDTO.getTenantId()));
        }
        //数据源还回去
        if (warehouseCode != null) {
            RpcContextUtil.setWarehouseCode(warehouse);
        }
    }
}
