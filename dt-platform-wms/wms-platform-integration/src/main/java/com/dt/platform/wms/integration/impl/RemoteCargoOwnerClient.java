package com.dt.platform.wms.integration.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.danding.business.client.rpc.config.facade.OutOwnerRpcFacade;
import com.danding.business.client.rpc.config.result.OutOwnerRpcResult;
import com.danding.core.tenant.SimpleTenantHelper;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.domain.base.client.ICargoOwnerClient;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.config.CargoTenantDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.config.CargoTenantParam;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.biz.taotian.CargoOwnerTaoTianDTO;
import com.dt.platform.wms.integration.IRemoteCargoOwnerClient;
import com.dt.platform.wms.integration.config.IRemoteCargoTenantClient;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/15 15:33
 */
@Service
public class RemoteCargoOwnerClient implements IRemoteCargoOwnerClient {


    @DubboReference
    ICargoOwnerClient iCargoOwnerClient;

    @DubboReference
    OutOwnerRpcFacade outOwnerRpcFacade;

    @Resource
    IRemoteCargoTenantClient remoteCargoTenantClient;

    @Override
    public IPage<CargoOwnerDTO> queryPage(CargoOwnerParam cargoOwnerParam) {
        return iCargoOwnerClient.getPage(cargoOwnerParam).getData();
    }

    @Override
    public List<CargoOwnerDTO> queryList(CargoOwnerParam cargoOwnerParam) {
        return iCargoOwnerClient.getList(cargoOwnerParam).getData();
    }

    @Override
    public CargoOwnerDTO queryByCode(String cargoCode) {
        CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
        cargoOwnerParam.setCode(cargoCode);
        return iCargoOwnerClient.get(cargoOwnerParam).getData();
    }

    @Override
    public List<CargoOwnerDTO> getAllCargoOwner(CargoOwnerParam cargoOwnerParam) {
        return iCargoOwnerClient.getList(cargoOwnerParam).getData();
    }

    @Override
    public Boolean enable(CargoOwnerParam cargoOwnerParam) {
        return iCargoOwnerClient.modify(cargoOwnerParam).getData();
    }

    @Override
    public Boolean save(CargoOwnerParam param) {
        return iCargoOwnerClient.save(param).getData();
    }

    @Override
    public Boolean modify(CargoOwnerParam param) {
        return iCargoOwnerClient.modify(param).getData();
    }

    @Override
    public Boolean checkExist(CargoOwnerParam cargoOwnerParam) {
        return iCargoOwnerClient.checkExits(cargoOwnerParam).getData();
    }

    public Map<String, CargoOwnerDTO> cargoMap(List<String> cargoCodeList) {
        Map<String, CargoOwnerDTO> cargoMap = new HashMap<>();

        if (CollectionUtil.isEmpty(cargoCodeList)) {
            return cargoMap;
        }

        CargoOwnerParam cargoOwnerBizParam = new CargoOwnerParam();
        cargoOwnerBizParam.setCodeList(cargoCodeList);
        List<CargoOwnerDTO> cargoOwnerBizDTOList = queryList(cargoOwnerBizParam);
        for (CargoOwnerDTO cargoOwnerBizDTO : cargoOwnerBizDTOList) {
            cargoMap.put(cargoOwnerBizDTO.getCode(), cargoOwnerBizDTO);
        }

        return cargoMap;
    }

    @Override
    public Map<String, CargoOwnerDTO> cargoMapUpperCaseKey(List<String> cargoCodeList) {
        Map<String, CargoOwnerDTO> cargoMap = new HashMap<>();

        if (CollectionUtil.isEmpty(cargoCodeList)) {
            return cargoMap;
        }

        CargoOwnerParam cargoOwnerBizParam = new CargoOwnerParam();
        cargoOwnerBizParam.setCodeList(cargoCodeList);
        List<CargoOwnerDTO> cargoOwnerDTOList = queryList(cargoOwnerBizParam);
        for (CargoOwnerDTO cargoOwnerBizDTO : cargoOwnerDTOList) {
            cargoMap.put(cargoOwnerBizDTO.getCode().trim().toUpperCase(), cargoOwnerBizDTO);
        }

        return cargoMap;
    }

    @Override
    public Boolean modify(CargoOwnerDTO cargoOwnerDTO) {
        return iCargoOwnerClient.modify(cargoOwnerDTO).getData();
    }

    @Override
    public CargoOwnerDTO cwCargo() {
        CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
        cargoOwnerParam.setCargoTag(CargoTagEnum.enumToNum(CargoTagEnum.CW_CARGO));
        List<CargoOwnerDTO> cargoOwnerDTOList = queryList(cargoOwnerParam);
        if (CollectionUtil.isEmpty(cargoOwnerDTOList)) {
            return null;
        }
        if (cargoOwnerDTOList.size() > 1) {
            throw ExceptionUtil.exceptionWithMessage("存在多个CW货主");
        }

        return cargoOwnerDTOList.get(0);
    }

    @Override
    public CargoOwnerTaoTianDTO getOwnerByDtWmsWarehouseCode(String warehouseCode, String cargoCode) {
        setTenantId(warehouseCode, cargoCode);
        OutOwnerRpcResult outOwnerRpcResult = outOwnerRpcFacade.getOwnerByDtWmsWarehouseCode(cargoCode, warehouseCode);
        if (outOwnerRpcResult == null) {
            throw new BaseException(BaseBizEnum.TIP, "未找到外部配置货主映射关系");
        }
        CargoOwnerTaoTianDTO cargoOwnerTaoTianDTO = new CargoOwnerTaoTianDTO();
        if (outOwnerRpcResult != null) {
            cargoOwnerTaoTianDTO.setWmsOwnerCode(cargoCode);
            cargoOwnerTaoTianDTO.setWmsWarehouseCode(warehouseCode);
            cargoOwnerTaoTianDTO.setOutOwnerCode(outOwnerRpcResult.getOutOwnerCode());
            cargoOwnerTaoTianDTO.setOutWarehouseCode(outOwnerRpcResult.getOutWarehouseCode());
        }
        return cargoOwnerTaoTianDTO;
    }

    @Override
    public Map<String, Boolean> getCargoTaoTianMap(List<String> cargoCodeList) {
        Map<String, Boolean> cargoMap = new HashMap<>();

        if (CollectionUtil.isEmpty(cargoCodeList)) {
            return cargoMap;
        }
        CargoOwnerParam cargoOwnerBizParam = new CargoOwnerParam();
        cargoOwnerBizParam.setCodeList(cargoCodeList);
        List<CargoOwnerDTO> cargoOwnerDTOList = queryList(cargoOwnerBizParam);
        for (CargoOwnerDTO cargoOwnerBizDTO : cargoOwnerDTOList) {
            if (CargoTagEnum.NumToEnum(cargoOwnerBizDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO)) {
                cargoMap.put(cargoOwnerBizDTO.getCode(), true);
            } else {
                cargoMap.put(cargoOwnerBizDTO.getCode(), false);
            }
        }
        return cargoMap;
    }

    private void setTenantId(String warehouse, String cargoCode) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        CargoTenantParam param = new CargoTenantParam();
        param.setCargoCode(cargoCode);
        param.setWarehouse(warehouse);
        CargoTenantDTO cargoTenantDTO = remoteCargoTenantClient.get(param);
        if (cargoTenantDTO != null && cargoTenantDTO.getTenantId() != null) {
            SimpleTenantHelper.setTenantId(Long.valueOf(cargoTenantDTO.getTenantId()));
        }
        //数据源还回去
        if (warehouseCode != null) {
            RpcContextUtil.setWarehouseCode(warehouse);
        }
    }
}
