package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.dto.StockStatisticDTO;
import com.dt.domain.core.stock.param.LocationStatisticsLotParam;
import com.dt.domain.core.stock.param.LocationStatisticsParam;
import com.dt.domain.core.stock.param.StockLocationBatchParam;
import com.dt.domain.core.stock.param.StockLocationParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;

public interface IRemoteStockLocationClient {

    /**
     * 批量新增|更新库位库存信息
     * @param param
     * @return
     */
    Boolean saveOrUpdate(StockLocationBatchParam param);

    Boolean save(StockLocationParam param);

    Boolean modify(StockLocationDTO param);

    Boolean remove(StockLocationDTO param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Boolean checkExits(StockLocationParam param);

    /**
     * 获取库位库存信息
     * @param param
     * @return
     */
    StockLocationDTO get(StockLocationParam param);

    /**
     * 获取库位库存列表
     * @param param
     * @return
     */
    List<StockLocationDTO> getList(StockLocationParam param);

    Map findLocationSumQty(String locationCode);
    Page<StockLocationDTO> getPageForPDA(StockLocationParam param,String type);
    /**
     * 获取三级账库位库存列表
     * @param param
     * @return
     */
    List<StockLocationDTO> getListAccount(StockLocationParam param);

    /**
     * 分页获取库位库存
     * @param param
     * @return
     */
    Page<StockLocationDTO> getPage(StockLocationParam param);
    
    @ApiOperation("库存汇总")
    StockStatisticDTO getStatistic(StockLocationParam param);
    
    
    List<StockLocationDTO> getChargingStaticGroupBy(StockLocationParam param);
    /**
     * 货主、库位、批次、库存统计分页查询
     * @param bizParam
     * @return
     */
    Page<StockLocationDTO> getStatisticsPage(LocationStatisticsParam bizParam);

    /**
     * 货主货位商品导出统计
     * @param param
     * @return
     */
    List<StockLocationDTO> getStatisticsList(LocationStatisticsParam param);


    /**
     * 货主、商品、批次、库存统计分页查询
     * @param bizParam
     * @return
     */
    Page<StockLocationDTO> getStatisticsCargoLotPage(LocationStatisticsLotParam bizParam);

    /**
     * 查询指定批次库存--可用数大于0
     * @param warehouseCode
     * @param cargoCode
     * @param skuCodeList
     * @param quality
     * @param skuLotNo
     * @return
     */
    List<StockLocationDTO> queryEffectiveStockBySkuLotNo(String warehouseCode, String cargoCode, List<String> skuCodeList, String quality,String skuLotNo, String locationType);

    /**
     * 查询不指定批次的商品库存--可用数大于0
     * @param warehouseCode
     * @param cargoCode
     * @param skuCodeList
     * @param quality
     * @param locationType
     * @return
     */
    List<StockLocationDTO> queryEffectiveStockByNoSkuLotNo(String warehouseCode, String cargoCode, List<String> skuCodeList, String quality,List<String> zoneCodeList,String locationType);

    /**
     * 分页获取三级库存数据
     * @param stockLocationParam
     * @return
     */
    List<StockLocationDTO> getListByPage(StockLocationParam stockLocationParam);
}
