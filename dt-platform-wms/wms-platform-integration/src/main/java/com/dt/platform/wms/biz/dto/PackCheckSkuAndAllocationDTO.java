package com.dt.platform.wms.biz.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class PackCheckSkuAndAllocationDTO implements Serializable {

    @ApiModelProperty(value = "sku")
    private String skuCode;

    @ApiModelProperty(value = "计划商品数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "失效/过期日期")
    private Long expireDate;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    @ApiModelProperty(value = "商品展示小标记【冷链】【贵品】")
    private List<String> skuTagList;

}
