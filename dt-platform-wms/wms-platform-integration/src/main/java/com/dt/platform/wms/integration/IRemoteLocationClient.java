package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.LocationDTO;
import com.dt.domain.base.param.LocationBatchParam;
import com.dt.domain.base.param.LocationParam;

import java.util.List;

public interface IRemoteLocationClient {

    /**
     * 新增库位信息
     *
     * @param param
     * @return
     */
    Boolean save(LocationParam param);


    Boolean saveBatch(LocationBatchParam param);
    Boolean updateBatch(LocationBatchParam param);
    /**
     * 修改库位信息
     * ID | Code | idList | codeList 四选一
     *
     * @param param
     * @return
     */
    Boolean modify(LocationParam param);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Boolean checkExits(LocationParam param);

    /**
     * 获取库位信息
     *
     * @param param
     * @return
     */
    LocationDTO get(LocationParam param);

    /**
     * 获取库位列表
     *
     * @param param
     * @return
     */
    List<LocationDTO> getList(LocationParam param);

    /**
     * 功能描述:  校验数据是否被禁用
     * 创建时间:  2020/12/18 9:01 上午
     *
     * @param targetLocationList:
     * @return void
     * <AUTHOR>
     */
    void checkStatus(List<LocationDTO> targetLocationList);

    /**
     * 分页获取库位
     *
     * @param param
     * @return
     */
    Page<LocationDTO> getPage(LocationParam param);

    /**
     * 获取暂存库位
     *
     * @param type
     * @param zoneCode
     * @return
     */
    LocationDTO queryTemporaryByType(String type, String zoneCode);

    /**
     * 获取库区类型所有库位
     *
     * @param collect
     * @return
     */
    List<LocationDTO> queryLocationByType(List<String> collect);

    /**
     * 获取
     *
     * @param locationCode
     * @return
     */
    LocationDTO queryLocationByCode(String locationCode);

    List<IdNameVO> getAllLocationList();

}
