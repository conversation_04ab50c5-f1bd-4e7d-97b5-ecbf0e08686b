package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.SkuUomDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.param.*;

import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

public interface IRemoteSkuClient {


    /**
     * 新增sku保存操作
     * @param param
     * @return
     */
    Boolean buildWMSAddSave(SkuParam param);



    Boolean saveBatchLog(SkuBatchLogParam param);
    /**
     * 批量新增商品档案信息
     *
     * @param param
     * @return
     */
    Boolean saveBatch(SkuBatchParam param);

    Boolean modifyBatchSku(SkuBatchParam param);

    /**
     * 修改商品档案信息
     * ID | Code | idList | codeList 四选一
     *
     * @param param
     * @return
     */
    Boolean modify(SkuParam param);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Boolean checkExits(SkuParam param);

    /**
     * 获取商品档案信息
     *
     * @param param
     * @return
     */
    SkuDTO get(SkuParam param);

    /**
     * 获取商品档案列表
     *
     * @param param
     * @return
     */
    List<SkuDTO> getList(SkuParam param);


    /**
     * 分页获取商品档案
     *
     * @param param
     * @return
     */
    Page<SkuDTO> getPage(SkuParam param);


    /**
     * 获取单个Sku
     *
     * @param skuCode
     * @return
     */
    SkuDTO querySkuByCode(String cargoCode, String skuCode);
    
    /**
     * 获取upc
     *
     * @param skuCode
     * @return
     */
    List<SkuUpcDTO> querySkuUpcBySkuCode(String cargoCode, String skuCode);

    /**
     * 获取uom
     *
     * @param skuCode
     * @return
     */
    SkuUomDTO querySkuUomBySkuCode(String cargoCode, String skuCode, String packageUnitCode);

    /**
     * 查询Upc
     *
     * @param skuUpcParam
     * @return
     */
    SkuUpcDTO getSkuUpc(SkuUpcParam skuUpcParam);

    /**
     * 查询Upc列表
     *
     * @param skuUpcParam
     * @return
     */
    List<SkuUpcDTO> getSkuUpcList(SkuUpcParam skuUpcParam);

    /**
     * 功能描述:  批量查询uom List
     * 创建时间:  2021/4/14 11:23 上午
     *
     * @param skuUomParam:
     * @return java.util.List<com.dt.domain.base.dto.SkuUomDTO>
     * <AUTHOR>
     */
    List<SkuUomDTO> getSkuUomList(SkuUomParam skuUomParam);
    /**
     * @author: WuXian
     * description:
     * create time: 2021/12/20 13:13
     *
     * @param skuParam
     * @param filedList
     * @return java.util.List<com.dt.domain.base.dto.SkuDTO>
     */
    List<SkuDTO> getSkuListToAppointColumn(SkuParam skuParam, List<String> filedList);
    /**
     * @author: WuXian
     * description:
     * create time: 2021/12/20 13:13
     *
     * @param skuUpcParam
     * @param filedList
     * @return java.util.List<com.dt.domain.base.dto.SkuUpcDTO>
     */
    List<SkuUpcDTO> getSkuUpcListToAppointColumn(SkuUpcParam skuUpcParam, List<String> filedList);

    Map<String, SkuDTO> skuDTOMap(String cargoCode, List<String> skuCodeList);
    /**
     * @param skuDTO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe:
     * @date 2023/8/7 14:16
     */
    Boolean modify(SkuDTO skuDTO);
    /**
     * @param skuParam
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe:
     * @date 2023/8/7 14:16
     */
    Boolean dataSyncWms(SkuParam skuParam);
    /**
     * @param skuUpcDTOList
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe:
     * @date 2023/12/4 13:39
     */
    Boolean erpDeleteSkuRemoveWmsSku(List<SkuUpcDTO> skuUpcDTOList);
    /**
     * @param sourceData
     * @return java.lang.String
     * <AUTHOR>
     * @describe: 淘天下发重新测量
     * @date 2024/3/8 14:03
     */
    String skuRemeasure(String sourceData);

    void maintainSkuOldIfNeed(SkuDTO after);
    /**
     * @param skuDTO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 判定商品是否可以变成待回传【淘天】
     * @date 2025/1/6 16:50
     */
    Boolean maintainSkuOldIfNeedByCallBack(SkuDTO skuDTO);

    <T> void rich(List<T> list, Function<T, String> codeMapper, Function<T, String> cargoCodeMapper, BiConsumer<T, SkuDTO> consumer);

    <T> void richUpc(List<T> list, Function<T, String> codeMapper, Function<T, String> cargoCodeMapper, BiConsumer<T, SkuUpcDTO> consumer);
}
