package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.pkg.PackAnalysisBillDTO;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.pkg.PackAnalysisBillParam;

import java.util.List;
import java.util.Map;

public interface IRemotePackageClient {

    /**
     * 功能描述:  初始化包裹日汇总
     * 创建时间:  2021/3/2 5:35 下午
     *
     * @return java.util.List<com.dt.domain.bill.dto.PackageDTO>
     * <AUTHOR>
     */
    List<PackageDTO> initCount();

    /**
     * 新增商品档案信息
     *
     * @param param
     * @return
     */
    Boolean save(PackageParam param);

    Boolean saveOrUpdate(PackageParam param);

    /**
     * 修改商品档案信息
     * ID | Code | idList | codeList 四选一
     *
     * @param param
     * @return
     */
    Boolean modify(PackageParam param);

    Boolean modifyBatch(PackageBatchParam param);

    /**
     * 批量修改
     *
     * @param packageDTOList
     * @return
     */
    Boolean modifyBatch(List<PackageDTO> packageDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Boolean checkExits(PackageParam param);

    /**
     * 获取商品档案信息
     *
     * @param param
     * @return
     */
    PackageDTO get(PackageParam param);

    /**
     * 获取商品档案列表
     *
     * @param param
     * @return
     */
    List<PackageDTO> getList(PackageParam param);

    List<PackageDTO> listByShipment(String shipmentOrderCode);

    Page<PackageDTO> getExportPage(PackageParam param);

    /**
     * 分页获取商品档案
     *
     * @param param
     * @return
     */
    Page<PackageDTO> getPage(PackageParam param);

    /**
     * 获取详情
     *
     * @param param
     * @return
     */
    PackageDTO getDetail(PackageParam param);

    /**
     * 包裹分页查询
     *
     * @param searchPackageParam
     * @return
     */
    Page<PackageDTO> queryPage(PackageParam searchPackageParam);

    /**
     * 包裹号或快递单号
     *
     * @param param
     * @return
     */
    List<PackageDTO> getPackageCodeOrExpressNo(PackageParam param);

    /**
     * 查询包裹明细
     *
     * @param searchPackageParam
     * @return
     */
    PackageDTO getDetailAndEntityDetail(PackageParam searchPackageParam);

    /**
     * 波次汇总查询包裹---仅限于波次汇单使用
     *
     * @param param
     * @return
     */
    List<CollectWaveDTO> getCollectWaveList(CollectWaveBillParam param);

//    /**
//     * @param packageDetailParam
//     * @return java.util.List<com.dt.domain.bill.dto.PackageDetailDTO>
//     * @author: WuXian
//     * description:  波次汇单查询包裹明细---仅限于波次汇单使用
//     * create time: 2021/8/10 9:32
//     */
//    List<PackageDetailDTO> getCollectWavePackageDetailList(PackageDetailParam packageDetailParam);
//
//    /**
//     * 查询汇单的波次数据--仅限于波次汇单使用
//     *
//     * @param collectWaveBillParam
//     * @return
//     */
//    List<PackageDTO> getCollectWaveCommitList(CollectWaveBillParam collectWaveBillParam);

    /**
     * 获取包裹明细
     *
     * @param packageCode
     * @return
     */
    List<PackageDetailDTO> getPackageDetailListByCode(String packageCode);

    /**
     * 查询单个包裹
     *
     * @param packageCode
     * @return
     */
    PackageDTO getPackageByCode(String packageCode);


    /**
     * 波次汇单查询
     *
     * @param param
     * @return
     */
    List<PackageDTO> getCollectWaveList(PackageParam param);

    /**
     * 修改包裹
     *
     * @param packageDTO
     */
    Boolean updatePackage(PackageDTO packageDTO);

    /**
     * TODO 待修正
     * 修改包裹明细---修正
     *
     * @param packageDetailDTOS
     * @return
     */
    Boolean updatePackageDetailList(List<PackageDetailDTO> packageDetailDTOS);

    /**
     * 波次修改包裹
     *
     * @param packageDTOS
     * @return
     */
    Boolean submitUpdateCollect(List<PackageDTO> packageDTOS);

    /**
     * 秒杀波次汇拣选单
     *
     * @param param
     * @return
     */
    List<CollectWaveDTO> querySpikeCollectWave(CollectWaveBillParam param);


    /**
     * 波次组装拣选单
     *
     * @param param
     * @return
     */
    List<PackageDetailDTO> getCollectWaveToPick(PackageDetailParam param);

    /**
     * 波次汇拣选单修改包裹
     *
     * @param packageDTOS
     * @return
     */
    Boolean updateCollectPackage(List<PackageDTO> packageDTOS);

    /**
     * 波次汇总查询
     *
     * @param packageParam
     * @return
     */
    List<PackageDTO> queryList(PackageParam packageParam);

    /**
     * 获取包裹明细
     *
     * @param param
     * @return
     */
    List<PackageDetailDTO> getPackageDetailListByListCode(PackageDetailParam param);

    /**
     * 汇单分配查询包裹
     *
     * @param param
     * @return
     */
    List<PackageDTO> getAllocationWave(PackageParam param);

    /**
     * 插入包裹日志
     *
     * @param packageLogDTO
     * @return
     */
    Boolean savePackageLog(PackageLogDTO packageLogDTO);

    /**
     * 批量插入日志
     *
     * @param packageLogDTOList
     * @return
     */
    Boolean savePackLogList(List<PackageLogDTO> packageLogDTOList);

    /**
     * 分页查询
     *
     * @param packageParam
     * @return
     */
    List<PackageDTO> getListByPage(PackageParam packageParam);
    List<PackageDTO> getListByPageNew(PackageParam packageParam);

    /**
     * 功能描述:  批量修改预处理状态
     * 创建时间:  2021/6/9 6:23 下午
     *
     * @param packageParam:
     * @return void
     * <AUTHOR>
     */
    Boolean modifyPretreatmentStatus(PackageParam packageParam);

    /**
     * @param packageParam
     * @param tableFields
     * @return java.util.List<com.dt.domain.bill.dto.PackageDTO>
     * @author: WuXian
     * description:  包裹查询指定字段
     * create time: 2021/8/19 14:13
     */
    List<PackageDTO> getAppointMultipleParam(PackageParam packageParam, List<String> tableFields);

    /**
     * @param packageDetailParam
     * @param filedList
     * @return java.util.List<com.dt.domain.bill.dto.PackageDetailDTO>
     * @author: WuXian
     * description:
     * create time: 2021/8/10 9:44
     */
    List<PackageDetailDTO> getCollectWavePackageDetailListAppointColumn(PackageDetailParam packageDetailParam, List<String> filedList);

    /**
     * @param packageParam
     * @return java.util.List<java.lang.Integer>
     * @author: WuXian
     * description: 波次导航获取
     * create time: 2021/12/17 9:57
     */
    Integer getWaveNavigationHead(PackageParam packageParam);

    /**
     * @param waveNavigationParam
     * @return java.util.List<com.dt.domain.bill.dto.WaveNavigationQueryHeadDTO>
     * @author: WuXian
     * description:  波次导航获取
     * create time: 2021/12/17 10:29
     */
    List<Map<String, Object>> getWaveNavigationBodyGroup(WaveNavigationParam waveNavigationParam);

    /**
     * @param waveNavigationParam
     * @return java.util.List<java.lang.Integer>
     * @author: WuXian
     * description: 波次导航获取
     * create time: 2021/12/17 9:57
     */
    Integer getWaveNavigationBodyByLastShipTime(WaveNavigationParam waveNavigationParam);

    /**
     * @param packageParam
     * @return com.dt.domain.bill.dto.PackageDTO
     * @author: WuXian
     * description:
     * create time: 2021/12/20 15:14
     */
    PackageDTO getWaveNavigationByAnalysisSkuLimitOne(PackageParam packageParam);

    /**
     * @param packageParam
     * @return java.util.List<com.dt.domain.bill.dto.CollectWaveDTO>
     * @author: WuXian
     * description: 波次导航分组
     * create time: 2021/12/22 11:42
     */
    List<CollectWaveDTO> getWaveNavigationCollectGroup(PackageParam packageParam);

    /**
     * @param packageParam
     * @return java.util.List<com.dt.domain.bill.dto.CollectWaveDTO>
     * @author: WuXian
     * description: 波次导航分组查询包裹
     * create time: 2021/12/22 11:42
     */
    List<PackageDTO> getWaveNavigationCollectWaveList(PackageParam packageParam);

    /**
     * @param packageParam
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @author: WuXian
     * description:
     * create time: 2021/12/30 13:47
     */
    List<CollectWaveAnalysisDTO> getWaveCollectFrontAnalysisGroupBy(PackageParam packageParam);

    /**
     * @Description:
     * @Param:
     * @return: java.lang.Integer
     * @Author: wuxian
     * @Date: 2022/6/13
     */
    Integer getExportCountNum(PackageParam packageParam);

    Page<PackageDTO> getPageNew(PackageParam packageParam);

    /**
     * @param packageParam
     * @return java.util.List<com.dt.domain.bill.dto.CollectWaveAnalysisPackStructDTO>
     * <AUTHOR>
     * @describe:
     * @date 2023/3/28 16:04
     */
    List<CollectWaveAnalysisPackStructDTO> getCollectWaveAnalysisPackStruct(PackageParam packageParam);

    List<PackageLogDTO> getPackLog(PackageParam packageParam);

    void handlePackageVolume(PackageDTO packageDTO);

    /**
     * @param packageParam
     * @return java.util.Map<java.lang.String, java.lang.Long>
     * <AUTHOR>
     * @describe: 获取包裹打印次数
     * @date 2025/3/24 13:23
     */
    Map<String, Long> getPackPrintNum(PackageParam packageParam);

    /**
     *
     * @param param
     * @return
     */
    Page<PackAnalysisBillDTO> getPackAnalysisPage(PackAnalysisBillParam param);
}
