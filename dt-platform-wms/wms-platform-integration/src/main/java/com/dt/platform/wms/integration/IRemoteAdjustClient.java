package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.domain.bill.bo.AdjustBO;
import com.dt.domain.bill.dto.AdjustDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.param.AdjustParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

public interface IRemoteAdjustClient {
    
    @ApiOperation("完成调整单")
    boolean adjustComplete(AdjustBO adjustBO);

    /**
     * 新增移位单信息
     * @param param
     * @return
     */
    Boolean save(AdjustParam param);

    /**
     * 修改移位单信息
     * ID | Code | idList | codeList 四选一
     * @param param
     * @return
     */
    Boolean modify(AdjustParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Boolean checkExits(AdjustParam param);

    /**
     * 获取移位单信息
     * @param param
     * @return
     */
    AdjustDTO get(AdjustParam param);

    /**
     * 获取移位单列表
     * @param param
     * @return
     */
    List<AdjustDTO> getList(AdjustParam param);

    /**
     * 分页获取移位单
     * @param param
     * @return
     */
    Page<AdjustDTO> getPage(AdjustParam param);


    /**
     * 获取移位单信息
     * @param param
     * @return
     */
    AdjustDTO getDetail(AdjustParam param);


    List<MessageMqDTO> taoTianMessageMqDTO(AdjustDTO adjustDTO, MessageTypeEnum messageTypeEnum);

    void callback(List<MessageMqDTO> messageMqDTOList);
}
