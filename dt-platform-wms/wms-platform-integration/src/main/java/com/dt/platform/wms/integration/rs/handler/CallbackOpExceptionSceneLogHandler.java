package com.dt.platform.wms.integration.rs.handler;

import com.dt.component.common.enums.rs.OpExceptionCallbackStateEnum;
import com.dt.component.common.enums.rs.OpInstructionTypeEnum;
import com.dt.component.common.result.ResponseMercury;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.bill.dto.rs.OpExceptionDTO;
import com.dt.domain.bill.client.rs.IOpExceptionCallbackLogClient;
import com.dt.domain.bill.dto.rs.OpExceptionCallbackLogDTO;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.taotian.DefaultOpexceptionReport;
import com.dt.platform.wms.integration.IRemoteWarehouseClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class CallbackOpExceptionSceneLogHandler {

    @DubboReference
    private IOpExceptionCallbackLogClient opExceptionCallbackLogClient;

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    public String getCallbackLogDesc(OpExceptionCallbackStateEnum targetState, OpExceptionDTO opExceptionDTO, ResponseMercury result) {
        StringBuffer lastDesc = new StringBuffer("回告内容: ");
        switch (targetState) {
            case REGISTER:
                lastDesc.append("异常登记");
                break;
            case RECEIVE_INSTRUCTION:
                lastDesc.append("收到销退指令");
                break;
            case HANDLED:
                lastDesc.append("已处理");
                if (OpInstructionTypeEnum.ORG_RETURN.getCode().equals(opExceptionDTO.getInstructionType())) {
                    lastDesc.append(", 上传寄送的运单号: ").append(opExceptionDTO.getOrgReturnNo());
                }
                break;
            case ON_SHELF:
                lastDesc.append("已上架");
                break;
        }
        if(!result.isSuccess()){
            String errorMessage = "";
            if(StringUtils.isNotEmpty(result.getError()) && StringUtils.isEmpty(result.getErrorMessage())){
                errorMessage = result.getError();
            }else if(StringUtils.isNotEmpty(result.getErrorMessage()) && StringUtils.isEmpty(result.getError())){
                errorMessage = result.getErrorMessage();
            }else{
                errorMessage = result.getError() + "; " + result.getErrorMessage();
            }
            lastDesc.append(", 回告失败原因: ").append(errorMessage);
        }
        return lastDesc.toString();
    }

    public void saveCallbackLog(OpExceptionDTO opExceptionDTO, ResponseMercury result, String reportStr, OpExceptionCallbackStateEnum targetState) {
        // 保存日志
        OpExceptionCallbackLogDTO callbackLogDTO = new OpExceptionCallbackLogDTO();
        callbackLogDTO.setAbnormalOrderNo(opExceptionDTO.getAbnormalOrderNo());

        callbackLogDTO.setReq(reportStr);

        callbackLogDTO.setResut(result.isSuccess());

        // 不准确
//        callbackLogDTO.setResMessage(result.getErrorMessage());

        String logScene = result.isSuccess() ? "回告淘天成功" : "回告淘天失败";
        String logDesc = getCallbackLogDesc(targetState, opExceptionDTO, result);


        callbackLogDTO.setLogScene(logScene);
        callbackLogDTO.setLogDesc(logDesc);

        callbackLogDTO.setResMessage(logDesc);
        callbackLogDTO.setOpExceptionState(opExceptionDTO.getStatus());
        callbackLogDTO.setAbnormalType(opExceptionDTO.getAbnormalType());
        callbackLogDTO.setInstructionType(opExceptionDTO.getInstructionType());
        callbackLogDTO.setCallbackState(targetState.getCode());
        callbackLogDTO.setWarehouseCode(opExceptionDTO.getWarehouseCode());
        opExceptionCallbackLogClient.save(callbackLogDTO);
    }


    public void sentWebhookMessage(String warehouseCode, DefaultOpexceptionReport param, ResponseMercury result) {
        if (result != null && result.isSuccess()) {
            return;
        }
        // 发送webhook消息
        String warehouseTagStr = warehouseCode;
        WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(warehouseCode);
        if(warehouseDTO != null){
            warehouseTagStr = warehouseDTO.getName();
        }

        StringBuffer sb = new StringBuffer();
        sb.append("##   WMS-异常登记回告失败");
        sb.append("仓库: ").append(warehouseTagStr);
        sb.append("异常单号: ").append(param.getAbnormalOrderId());
        sb.append("异常内容: ").append(result.getErrorMessage());
        // http异常内部屏蔽了
        WechatUtil.sendMessage(sb.toString(), defaultWarehouseCodeConfig.getOpExceptionWebhookUrlList());
    }
}
