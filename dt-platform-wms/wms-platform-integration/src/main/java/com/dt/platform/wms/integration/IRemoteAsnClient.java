package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.bill.bo.*;
import com.dt.domain.bill.bo.asn.AsnZeroReceiveBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.tally.TallyDTO;
import com.dt.domain.bill.param.AsnModifyParam;
import com.dt.domain.bill.param.AsnParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/21 16:37
 */
public interface IRemoteAsnClient {


    @ApiOperation("取消收货")
    Boolean cancelAsn(AsnCancelBO asnCancelBO);

    /**
     * ASN分页
     *
     * @param param
     * @return
     */
    IPage<AsnDTO> queryPage(AsnParam param);

    /**
     * 查询单个ASN
     *
     * @param asnId
     * @return
     */
    AsnDTO queryOneByAsnId(String asnId);

    /**
     * @param param
     * @return
     */
    AsnDTO get(AsnParam param);

    /**
     * 修改状态码
     *
     * @param param
     * @return
     */
    Boolean modifyStatus(AsnModifyParam param);

    /**
     * 查询ASN明细
     *
     * @param asnId
     * @return
     */
    AsnDetailDataDTO queryAsnDetailByAsnId(String asnId);

    /**
     * 存储上游下发的ASN
     *
     * @param dataDTO
     * @return
     */
    Boolean receiptAsn(AsnReceiptDataDTO dataDTO);

    /**
     * 检查上游到货通知单号
     *
     * @param poNo
     * @param cargoCode
     * @return
     */
    Boolean checkSoNoExistsByCargoCode(String poNo, String cargoCode);

    /**
     * 按上游参数查询ASN
     *
     * @param asnParam
     * @return
     */
    AsnDTO queryAsnBySoNo(AsnParam asnParam);

    /**
     * 更新通知单打印次数
     *
     * @param asnParam
     * @return
     */
    Integer updateAsnPrintNum(AsnParam asnParam);

    /**
     * 更新收货通知单次数
     *
     * @param asnParam
     * @return
     */
    Integer updateRecPrintNum(AsnParam asnParam);

    /**
     * 收货作业批次提交容器修改ASN
     *
     * @param asnDTO
     */
    Boolean commitAsn(AsnDTO asnDTO);

    /**
     * 查询导出数据接口
     *
     * @param param
     * @return
     */
    List<AsnDetailDTO> getDetailList(AsnParam param);


    /**
     * 获取ASN列表
     *
     * @param param
     * @return
     */
    List<AsnDTO> getList(AsnParam param);

    /**
     * 插入ASN日志
     *
     * @param asnLogDTO
     * @return
     */
    Boolean saveAsnLog(AsnLogDTO asnLogDTO);

    List<AsnLogDTO> getAsnLogList(AsnParam asnParam);

    /**
     * 明细分页
     *
     * @param convert
     * @return
     */
    IPage<AsnDetailDTO> queryDetailPage(AsnParam convert);

    /**
     * 明细分组分页
     *
     * @param convert
     * @return
     */
    IPage<AsnDetailDTO> getPageGroupDetail(AsnParam convert);

    /**
     * 批量修改到货通知单
     *
     * @param asnCompleteBO
     * @return
     */
    Boolean modifyBatch(AsnBillCompleteBO asnCompleteBO);

    IPage<AsnLogDTO> queryLogPage(AsnParam asnParam);

    /**
     * @param asnDTO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2021/7/15 16:32
     */
    Boolean save(AsnDTO asnDTO);

    /**
     * @param asnDTO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2021/7/15 16:47
     */
    Boolean modifyAndRomve(AsnDTO asnDTO);

    /**
     * @param asnBillCancelBO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2021/7/16 14:39
     */
    Boolean cancelBatch(AsnBillCancelBO asnBillCancelBO);

    /**
     * @param asnBillImportBO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2021/7/20 15:27
     */
    Boolean saveImportBatch(AsnBillImportBO asnBillImportBO);

    /**
     * @param asnDTO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2021/12/15 10:05
     */
    Boolean modify(AsnDTO asnDTO);

    /**
     * @param asnBatchArrivalBO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2022/3/11 18:02
     */
    Boolean modifyArrivalBatch(AsnBatchArrivalBO asnBatchArrivalBO);

    /**
     * @param asnDTOList
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2022/3/16 14:21
     */
    Boolean modifyBatchAsnDTO(List<AsnDTO> asnDTOList);

    /**
     * @param asnLogDTOList
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2022/3/16 14:21
     */
    Boolean saveAsnLogBatch(List<AsnLogDTO> asnLogDTOList);

    /**
     * @param asnParam
     * @param tableFields
     * @return java.util.List<com.dt.domain.bill.dto.AsnDTO>
     * @author: WuXian
     * description: 获取指定字段
     * create time: 2022/3/29 9:32
     */
    List<AsnDTO> getAppointMultipleParam(AsnParam asnParam, List<String> tableFields);

    /**
     * @param asnDTO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe:
     * @date 2022/9/20 16:12
     */
    Boolean commitBoxDataAsn(AsnDTO asnDTO);

    Boolean modifyAndAddMessage(AsnDTO asnDTO);

    /**
     * @param modifySkuLotDTOList
     * @param asnLinkModifyBO
     * @param tallyDTO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe:
     * @date 2023/7/26 14:28
     */
    Boolean modifyLinkAsn(List<SkuLotDTO> modifySkuLotDTOList, AsnLinkModifyBO asnLinkModifyBO, TallyDTO tallyDTO);

    @ApiOperation("退货入库对应的正向运单号")
    String originExpressNo(String extraJson);

    /**
     * @param sourceData
     * @return java.lang.String
     * <AUTHOR>
     * @describe:
     * @date 2024/3/12 18:53
     */
    String asnPriority(String billNo, String sourceData);

    /**
     * @param businessNo
     * @param sourceData
     * @return java.lang.String
     * <AUTHOR>
     * @describe:
     * @date 2024/3/12 19:43
     */
    String asnApprove(String businessNo, String sourceData);

    /**
     * @param businessNo
     * @param sourceData
     * @return java.lang.String
     * <AUTHOR>
     * @describe:
     * @date 2024/3/12 19:43
     */
    String asnInstruct(String businessNo, String sourceData);

    IPage<AsnDetailDTO> getPageGroupDetailByReTurn(AsnParam convert);

    IPage<AsnDetailDTO> getSkuGroupPage(AsnParam asnParam);
    /**
     * @param asnDTO
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @describe:
     * @date 2024/9/21 15:50
     */
    Result<String> lyCreateSkuByAsnDetail(AsnDTO asnDTO);
    /**
     * @param asnZeroReceiveBO
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @describe:
     * @date 2025/2/13 9:34
     */
    String zeroReceive(AsnZeroReceiveBO asnZeroReceiveBO);

    String modifyAsnByZeroReceive(AsnDTO asnDTO);
}
