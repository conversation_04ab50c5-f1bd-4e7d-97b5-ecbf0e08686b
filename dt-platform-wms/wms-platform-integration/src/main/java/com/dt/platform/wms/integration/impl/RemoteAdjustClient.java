package com.dt.platform.wms.integration.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.bill.*;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.message.LargeMessageOperationTypeEnum;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.log.AdjustLogDTO;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.bill.bo.AdjustBO;
import com.dt.domain.bill.client.IAdjustClient;
import com.dt.domain.bill.dto.AdjustDTO;
import com.dt.domain.bill.dto.AdjustDetailDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.param.AdjustDetailParam;
import com.dt.domain.bill.param.AdjustParam;
import com.dt.domain.bill.param.message.MessageMqParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.DateDescUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.taotian.CargoOwnerTaoTianDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.log.IRemoteAdjustLogClient;
import com.dt.platform.wms.integration.message.IRemoteMessageMqClient;
import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.danding.business.common.ares.utils.HttpRequestUtils.postForm;

@Slf4j
@Service
public class RemoteAdjustClient implements IRemoteAdjustClient {

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private WmsOtherConfig wmsOtherConfig;

    @Resource
    private IRemoteAdjustDetailClient remoteAdjustDetailClient;

    @Resource
    private IRemoteMessageMqClient remoteMessageMqClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @DubboReference
    private IAdjustClient adjustClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteAdjustLogClient remoteAdjustLogClient;

    @Resource
    private RemoteTenantHelper remoteTenantHelper;

    @Override
    public boolean adjustComplete(AdjustBO adjustBO) {
        return adjustClient.adjustComplete(adjustBO).getData();
    }

    @Override
    public Boolean save(AdjustParam param) {
        return adjustClient.save(param).getData();
    }

    @Override
    public Boolean modify(AdjustParam param) {
        return adjustClient.modify(param).getData();
    }

    @Override
    public Boolean checkExits(AdjustParam param) {
        return adjustClient.checkExits(param).getData();
    }

    @Override
    public AdjustDTO get(AdjustParam param) {
        return adjustClient.get(param).getData();
    }

    @Override
    public List<AdjustDTO> getList(AdjustParam param) {
        return adjustClient.getList(param).getData();
    }

    @Override
    public Page<AdjustDTO> getPage(AdjustParam param) {
        return adjustClient.getPage(param).getData();
    }

    @Override
    public AdjustDTO getDetail(AdjustParam param) {
        return adjustClient.getDetail(param).getData();
    }

    @Override
    public List<MessageMqDTO> taoTianMessageMqDTO(AdjustDTO adjustDTO, MessageTypeEnum messageTypeEnum) {
        if (null == adjustDTO) return ListUtil.empty();
        if (AdjustTagEnum.NumToEnum(adjustDTO.getTag()).contains(AdjustTagEnum.NO_CALLBACK)) return ListUtil.empty();
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(adjustDTO.getCargoCode());
        if (null == cargoOwnerDTO) throw ExceptionUtil.exceptionWithMessage("货主不存在");
        if (CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO)) {
            return getMessageMqDTOS(adjustDTO, messageTypeEnum);
        }
        return ListUtil.empty();
    }

    private static ArrayList<MessageMqDTO> getMessageMqDTOS(AdjustDTO adjustDTO, MessageTypeEnum messageTypeEnum) {
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(adjustDTO.getWarehouseCode());
        messageMqDTO.setCargoCode(adjustDTO.getCargoCode());
        messageMqDTO.setBillNo(adjustDTO.getCode());
        messageMqDTO.setOperationType(messageTypeEnum.getType());
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_ADJUST.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        return ListUtil.toList(messageMqDTO);
    }

    @Override
    public void callback(List<MessageMqDTO> messageMqDTOList) {
        if (CollectionUtil.isEmpty(messageMqDTOList)) return;
        try {
            Disposable subscribe = Flowable.timer(1, TimeUnit.MICROSECONDS).observeOn(Schedulers.io()).subscribe(aLong -> {
                RpcContextUtil.setWarehouseCode(messageMqDTOList.get(0).getWarehouseCode());
                MessageMqParam messageMqParam = new MessageMqParam();
                messageMqParam.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
                messageMqParam.setBillNoList(messageMqDTOList.stream().map(MessageMqDTO::getBillNo).distinct().collect(Collectors.toList()));
                List<MessageMqDTO> messageMqDTOS = remoteMessageMqClient.getList(messageMqParam);

                for (MessageMqDTO messageMqDTO : messageMqDTOS) {

                    Long useTime = System.currentTimeMillis() - messageMqDTO.getCreatedTime();
                    if (useTime > 5 * 60 * 1000) {
                        if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                            String message = String.format("[流程:%s]仓库:%s,单号:%s,耗时:%s(s)", "调整单淘天回告", messageMqDTO.getWarehouseCode(), messageMqDTO.getBillNo(), useTime / 1000);
                            WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                        }
                    }
                    AdjustParam adjustParam = new AdjustParam();
                    adjustParam.setCode(messageMqDTO.getBillNo());
                    AdjustDTO adjustDTO = get(adjustParam);
                    if (null == adjustDTO) {
                        messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                        remoteMessageMqClient.modify(messageMqDTO);
                        continue;
                    }


                    JSONObject jsonObject = JSONUtil.createObj();

                    jsonObject.set("totalPage", 1);
                    jsonObject.set("pageSize", 1);
                    jsonObject.set("currentPage", 1);
                    jsonObject.set("checkOrderCode", adjustDTO.getCode());
                    jsonObject.set("checkOrderId", adjustDTO.getCode());

                    CargoOwnerTaoTianDTO ownerByDtWmsWarehouseCode = remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(adjustDTO.getWarehouseCode(), adjustDTO.getCargoCode());
                    jsonObject.set("warehouseCode", ownerByDtWmsWarehouseCode.getOutWarehouseCode()); // 设置外部仓库编码
                    jsonObject.set("ownerCode", ownerByDtWmsWarehouseCode.getOutOwnerCode()); // 设置成外部的仓库编码
                    jsonObject.set("dtWmsCargoCode", ownerByDtWmsWarehouseCode.getWmsOwnerCode());
                    jsonObject.set("dtWmsWarehouseCode", ownerByDtWmsWarehouseCode.getWmsWarehouseCode());

                    jsonObject.set("tenantId", remoteTenantHelper.queryTenantId(adjustDTO.getWarehouseCode(), adjustDTO.getCargoCode()));

                    jsonObject.set("checkTime", DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
                    jsonObject.set("outBizCode", adjustDTO.getCode());
                    jsonObject.set("remark", adjustDTO.getRemark());
                    jsonObject.set("adjustType", "CHECK");

                    AdjustDetailParam adjustDetailParam = new AdjustDetailParam();
                    adjustDetailParam.setAdjustCode(adjustDTO.getCode());
                    List<AdjustDetailDTO> adjustDetailDTOList = remoteAdjustDetailClient.getList(adjustDetailParam);
                    if (CollectionUtil.isEmpty(adjustDetailDTOList)) {
                        messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                        remoteMessageMqClient.modify(messageMqDTO);
                        continue;
                    }

                    SkuLotParam skuLotParam = new SkuLotParam();
                    skuLotParam.setCodeList(adjustDetailDTOList.stream().map(AdjustDetailDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
                    List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
                    Map<String, SkuLotDTO> skuLotDTOMap = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity()));

                    List<JSONObject> items = new ArrayList<>();
                    for (AdjustDetailDTO adjustDetailDTO : adjustDetailDTOList) {
                        JSONObject item = JSONUtil.createObj();
                        item.set("itemCode", adjustDetailDTO.getSkuCode());
                        item.set("itemId", adjustDetailDTO.getSkuCode());
                        item.set("quantity", adjustDetailDTO.getAdjustQty());
                        if (AdjustTypeEnum.SUBTRACT.getStatus().equalsIgnoreCase(adjustDTO.getType())) {
                            item.set("quantity", adjustDetailDTO.getAdjustQty().negate());
                        }
                        item.set("batchCode", adjustDetailDTO.getSkuLotNo());
                        Optional.ofNullable(skuLotDTOMap.get(adjustDetailDTO.getSkuLotNo())).ifPresent(skuLotDTO -> {
                            item.set("inventoryType", skuLotDTO.getInventoryType());
                            item.set("productDate", DateDescUtil.normalDateStr(skuLotDTO.getManufDate()));
                            item.set("expireDate", DateDescUtil.normalDateStr(skuLotDTO.getExpireDate()));
                            item.set("produceCode", skuLotDTO.getProductionNo());
                        });
                        item.set("remark", adjustDetailDTO.getRemark());
                        JSONObject lineExtra = JSONUtil.createObj();
                        lineExtra.set("orderLineId", adjustDetailDTO.getLineSeq());
                        item.set("extendProps", lineExtra);
                        items.add(item);
                    }
                    jsonObject.set("items", items);
                    JSONObject adjustExtra = JSONUtil.createObj();
                    adjustExtra.set("checkType", AdjustTypeEnum.ADD.getStatus().equalsIgnoreCase(adjustDTO.getType()) ? "INVPROFIT" : "INVLOSS");
                    jsonObject.set("extendProps", adjustExtra);


                    //请求参数
                    Map<String, Object> requestMap = new HashMap<>();
                    requestMap.put("bizData", JSONUtil.toJsonStr(jsonObject));
                    requestMap.put("method", "taobao.qimen.inventory.report");

                    String request = CallOtherSupport.execute(it -> postForm(it, wmsOtherConfig.getCallBackMercuryUrl()), requestMap, "调整单回告");
                    JSONObject response = JSONUtil.parseObj(request);
                    boolean success = response.getBool("success", false);

                    if (success) {
                        adjustDTO.checkOrderCode(response.getStr("checkOrderCode", StrUtil.EMPTY));
                        modify(ConverterUtil.convert(adjustDTO, AdjustParam.class));

                        // 添加日志
                        AdjustLogDTO logDTO = new AdjustLogDTO();
                        logDTO.setOpContent("调整单回告淘天成功");
                        logDTO.setOpRemark("调整单回告淘天成功");
                        logDTO.setOpDate(System.currentTimeMillis());
                        logDTO.setOpBy(CurrentUserHolder.getUserName());
                        logDTO.setAdjustCode(adjustDTO.getCode());
                        logDTO.setCargoCode(adjustDTO.getCargoCode());
                        logDTO.setOpType(LargeMessageOperationTypeEnum.BILL_OPERATION_INSERT.getType());
                        remoteAdjustLogClient.save(logDTO);

                        messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                        remoteMessageMqClient.modify(messageMqDTO);
                    }
                }
            }, e -> log.error(e.getMessage(), e));
            Runtime.getRuntime().addShutdownHook(new Thread(subscribe::dispose));
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
        }
    }
}
