package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.platform.wms.biz.taotian.CargoOwnerTaoTianDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/15 15:33
 */
public interface IRemoteCargoOwnerClient {
    /**
     * 分页查询
     *
     * @param cargoOwnerParam
     * @return
     */
    IPage<CargoOwnerDTO> queryPage(CargoOwnerParam cargoOwnerParam);

    /**
     * 货主编码列表
     * @param cargoOwnerParam
     * @return
     */
    List<CargoOwnerDTO> queryList(CargoOwnerParam cargoOwnerParam);

    /**
     * @param cargoCode
     * @return
     */
    CargoOwnerDTO queryByCode(String cargoCode);

    /**
     * 获取货主全部列表
     *
     * @param cargoOwnerParam
     * @return
     */
    List<CargoOwnerDTO> getAllCargoOwner(CargoOwnerParam cargoOwnerParam);


    /**
     * 启用
     *
     * @param cargoOwnerParam
     * @return
     */
    Boolean enable(CargoOwnerParam cargoOwnerParam);

    /**
     * 新增货主档案
     *
     * @param param
     * @return
     */
    Boolean save(CargoOwnerParam param);

    /**
     * 修改货主档案
     *
     * @param param
     * @return
     */
    Boolean modify(CargoOwnerParam param);

    /**
     *
     * @param cargoOwnerParam
     * @return
     */
    Boolean checkExist(CargoOwnerParam cargoOwnerParam);

    Map<String, CargoOwnerDTO> cargoMap(List<String> cargoCodeList);

    Map<String, CargoOwnerDTO> cargoMapUpperCaseKey(List<String> cargoCodeList);

    /**
     * @param cargoOwnerDTO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe:
     * @date 2023/1/12 14:26
     */
    Boolean modify(CargoOwnerDTO cargoOwnerDTO);
    
    CargoOwnerDTO cwCargo();
    /**
     * @param warehouseCode
     * @param cargoCode
     * @return
     * <AUTHOR>
     * @describe: wms仓库和货主获取淘天货主和仓库
     * @date 2024/3/11 13:24
     */
    CargoOwnerTaoTianDTO getOwnerByDtWmsWarehouseCode(String warehouseCode, String cargoCode);
    /**
     * @param cargoCodeList
     * @return java.util.Map<java.lang.String,java.lang.Boolean>
     * <AUTHOR>
     * @describe: 获取货主是否淘天
     * @date 2025/3/21 13:14
     */
    Map<String, Boolean> getCargoTaoTianMap(List<String> cargoCodeList);
}
