package com.dt.platform.wms.integration.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.bill.MessageMqStatusEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.component.common.enums.transfer.TransferTagEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.bill.bo.TransferPersistBO;
import com.dt.domain.bill.client.ITransferClient;
import com.dt.domain.bill.client.ITransferDetailClient;
import com.dt.domain.bill.dto.TransferDTO;
import com.dt.domain.bill.dto.TransferDetailDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.param.TransferDetailBatchParam;
import com.dt.domain.bill.param.TransferDetailParam;
import com.dt.domain.bill.param.TransferParam;
import com.dt.domain.bill.param.message.MessageMqParam;
import com.dt.platform.utils.DateDescUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.taotian.CargoOwnerTaoTianDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.message.IRemoteMessageMqClient;
import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.danding.business.common.ares.utils.HttpRequestUtils.postForm;

/**
 * Created by nobody on 2020/12/28 17:45
 */
@Slf4j
@Component
public class RemoteTransferClient implements IRemoteTransferClient {

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    private WmsOtherConfig wmsOtherConfig;


    @Resource
    private IRemoteMessageMqClient remoteMessageMqClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @DubboReference
    private ITransferClient transferClient;

    @DubboReference
    private ITransferDetailClient transferDetailClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private RemoteTenantHelper remoteTenantHelper;

    @Override
    public Boolean add(TransferParam transferParam) {
        return transferClient.add(transferParam).getData();
    }

    @Override
    public Boolean addDetail(TransferDetailBatchParam transferDetailBatchParam) {
        return transferDetailClient.batchAdd(transferDetailBatchParam).getData();
    }

    @Override
    public Boolean updateDetail(TransferDetailBatchParam transferDetailBatchParam) {
        return transferDetailClient.batchUpdate(transferDetailBatchParam).getData();
    }

    @Override
    public Boolean delDetail(String transferCode) {
        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(transferCode);
        return transferDetailClient.del(transferDetailParam).getData();
    }

    @Override
    public List<TransferDetailDTO> listDetail(TransferDetailParam transferDetailParam) {
        return transferDetailClient.list(transferDetailParam).getData();
    }

    @Override
    public Page<TransferDTO> page(TransferParam transferParam) {
        return transferClient.page(transferParam).getData();
    }

    @Override
    public List<TransferDTO> list(TransferParam transferParam) {
        return transferClient.list(transferParam).getData();

    }

    @Override
    public Boolean update(TransferDTO transferDTO) {
        return transferClient.update(transferDTO).getData();
    }

    @Override
    public Boolean update(TransferPersistBO transferBO) {
        return transferClient.update(transferBO).getData();
    }

    @Override
    public TransferDTO get(TransferParam transferParam) {
        return transferClient.get(transferParam).getData();
    }

    public Page<TransferDetailDTO> page(TransferDetailParam transferDetailParam) {
        return transferDetailClient.page(transferDetailParam).getData();
    }

    @Override
    public List<MessageMqDTO> taoTianMessageMqDTO(TransferDTO transferDTO, MessageTypeEnum messageTypeEnum) {
        if (null == transferDTO) return ListUtil.empty();
        if (TransferTagEnum.NumToEnum(transferDTO.getOrderTag()).contains(TransferTagEnum.NO_CALLBACK))
            return ListUtil.empty();
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(transferDTO.getCargoCode());
        if (cargoOwnerDTO == null) throw ExceptionUtil.exceptionWithMessage("货主不存在");
        if (CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO)) {
            return taoTianTransferMessage(transferDTO, messageTypeEnum);
        }
        return new ArrayList<>();
    }

    public List<MessageMqDTO> taoTianTransferMessage(TransferDTO transferDTO, MessageTypeEnum messageTypeEnum) {
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(transferDTO.getWarehouseCode());
        messageMqDTO.setCargoCode(transferDTO.getCargoCode());
        messageMqDTO.setBillNo(transferDTO.getCode());
        messageMqDTO.setOperationType(messageTypeEnum.getType());
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_TRANSFER.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        return ListUtil.toList(messageMqDTO);
    }

    @Override
    public void callback(List<MessageMqDTO> messageMqDTOList) {
        if (CollectionUtil.isEmpty(messageMqDTOList)) return;
        try {
            Disposable subscribe = Flowable.timer(1, TimeUnit.MICROSECONDS).observeOn(Schedulers.io()).subscribe(aLong -> {
                RpcContextUtil.setWarehouseCode(messageMqDTOList.get(0).getWarehouseCode());
                MessageMqParam messageMqParam = new MessageMqParam();
                messageMqParam.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
                messageMqParam.setBillNoList(messageMqDTOList.stream().map(MessageMqDTO::getBillNo).distinct().collect(Collectors.toList()));
                List<MessageMqDTO> messageMqDTOS = remoteMessageMqClient.getList(messageMqParam);

                for (MessageMqDTO messageMqDTO : messageMqDTOS) {
                    Long useTime = System.currentTimeMillis() - messageMqDTO.getCreatedTime();
                    if (useTime > 5 * 60 * 1000) {
                        if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                            String message = String.format("[流程:%s]仓库:%s,单号:%s,耗时:%s(s)", "转移淘天回告", messageMqDTO.getWarehouseCode(), messageMqDTO.getBillNo(), useTime / 1000);
                            WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                        }
                    }
                    TransferParam transferParam = new TransferParam();
                    transferParam.setCode(messageMqDTO.getBillNo());
                    TransferDTO transferDTO = get(transferParam);
                    if (null == transferDTO) {
                        messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
                        remoteMessageMqClient.modify(messageMqDTO);
                        continue;
                    }

                    if (MessageTypeEnum.OPERATION_TRANSFER_OCCUPY_CALLBACK.getType().equalsIgnoreCase(messageMqDTO.getOperationType())) {
                        occupyCallback(messageMqDTO, transferDTO);
                    }
                    if (MessageTypeEnum.OPERATION_TRANSFER_COMPLETE_CALLBACK.getType().equalsIgnoreCase(messageMqDTO.getOperationType())) {
                        completeCallback(messageMqDTO, transferDTO);
                    }
                    if (MessageTypeEnum.OPERATION_TRANSFER_OCCUPY_CANCEL_CALLBACK.getType().equalsIgnoreCase(messageMqDTO.getOperationType())) {
                        cancelOccupyCallback(messageMqDTO, transferDTO);
                    }
                }
            }, e -> log.error(e.getMessage(), e));
            Runtime.getRuntime().addShutdownHook(new Thread(subscribe::dispose));
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
        }
    }

    private void occupyCallback(MessageMqDTO messageMqDTO, TransferDTO transferDTO) {
        Map<String, Object> requestMap = new HashMap<>();

        // 占用
        JSONObject jsonObject = JSONUtil.createObj();
        jsonObject.set("occupyOrderId", transferDTO.getCode());
        jsonObject.set("adjustType", "ADJUST");
        jsonObject.set("outBizCode", transferDTO.getCode());
        jsonObject.set("operateTime", DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
        jsonObject.set("remark", transferDTO.getNote());
        
        CargoOwnerTaoTianDTO ownerByDtWmsWarehouseCode = remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(transferDTO.getWarehouseCode(), transferDTO.getCargoCode());
        jsonObject.set("warehouseCode", ownerByDtWmsWarehouseCode.getOutWarehouseCode()); // 设置外部仓库编码
        jsonObject.set("ownerCode", ownerByDtWmsWarehouseCode.getOutOwnerCode()); // 设置成外部的仓库编码
        jsonObject.set("dtWmsCargoCode", ownerByDtWmsWarehouseCode.getWmsOwnerCode());
        jsonObject.set("dtWmsWarehouseCode", ownerByDtWmsWarehouseCode.getWmsWarehouseCode());

        jsonObject.set("tenantId", remoteTenantHelper.queryTenantId(transferDTO.getWarehouseCode(), transferDTO.getCargoCode()));

        List<JSONObject> items = new ArrayList<>();

        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(messageMqDTO.getBillNo());
        List<TransferDetailDTO> transferDetailDTOList = listDetail(transferDetailParam);
        if (CollectionUtil.isEmpty(transferDetailDTOList)) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(transferDetailDTOList.stream().map(TransferDetailDTO::getOriginSkuLotNo).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
        Map<String, SkuLotDTO> skuLotDTOMap = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity()));

        for (TransferDetailDTO transferDetailDTO : transferDetailDTOList) {
            JSONObject item = JSONUtil.createObj();
            item.set("itemCode", transferDetailDTO.getOriginSkuCode());
            item.set("itemId", transferDetailDTO.getOriginSkuCode());
            item.set("inventoryType", transferDetailDTO.getOriginInventoryType());
            item.set("lockQuantity", transferDetailDTO.getChangeQty());
            item.set("batchCode", transferDetailDTO.getOriginSkuLotNo());
            item.set("orderLineId", transferDetailDTO.getLineSeq());
            Optional.ofNullable(skuLotDTOMap.get(transferDetailDTO.getOriginSkuLotNo())).ifPresent(skuLotDTO -> {
                item.set("productDate", DateDescUtil.normalDateStr(skuLotDTO.getManufDate()));
                item.set("expireDate", DateDescUtil.normalDateStr(skuLotDTO.getExpireDate()));
                item.set("produceCode", skuLotDTO.getProductionNo());
            });
            item.set("remark", transferDetailDTO.getRemark());
            items.add(item);
        }
        jsonObject.set("items", items);
        requestMap.put("method", "taobao.logistics.wms.inventory.imbalance.create");

        //请求参数
        requestMap.put("bizData", JSONUtil.toJsonStr(jsonObject));


        String request = CallOtherSupport.execute(it -> postForm(it, wmsOtherConfig.getCallBackMercuryUrl()), requestMap, "转移审核通过回告");
        JSONObject response = JSONUtil.parseObj(request);
        boolean success = response.getBool("success", false);
        if (success) {
            transferDTO.occupyOrderCode(response.getStr("occupyOrderCode",""));
            update(transferDTO);
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
        }
    }

    private void cancelOccupyCallback(MessageMqDTO messageMqDTO, TransferDTO transferDTO) {
        Map<String, Object> requestMap = new HashMap<>();
        // 没有占用不需要处理
        MessageMqParam messageMqParam = new MessageMqParam();
        messageMqParam.setBillNo(transferDTO.getCode());
        messageMqParam.setOperationType(MessageTypeEnum.OPERATION_TRANSFER_OCCUPY_CALLBACK.getType());
        List<MessageMqDTO> messageMqDTOList = remoteMessageMqClient.getList(messageMqParam);
        if (CollectionUtil.isEmpty(messageMqDTOList)) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }
        // 已经处理过也不需要处理
        messageMqParam.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
        messageMqParam.setOperationType(MessageTypeEnum.OPERATION_TRANSFER_OCCUPY_CANCEL_CALLBACK.getType());
        messageMqDTOList = remoteMessageMqClient.getList(messageMqParam);
        if (CollectionUtil.isNotEmpty(messageMqDTOList)) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        // 占用
        JSONObject jsonObject = JSONUtil.createObj();
        jsonObject.set("totalPage", 1);
        jsonObject.set("currentPage", 1);
        jsonObject.set("pageSize", 1);
        String code = StrUtil.join(StrUtil.EMPTY, transferDTO.getCode(), "TRF");
        jsonObject.set("adjustOrderCode", code);
        jsonObject.set("adjustOrderId", code);
        jsonObject.set("adjustTime", DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
        jsonObject.set("outBizCode", code);
        jsonObject.set("remark", transferDTO.getNote());
        jsonObject.set("adjustType", "ADJUST");
        jsonObject.set("relatedOrderId", transferDTO.getCode());

        CargoOwnerTaoTianDTO ownerByDtWmsWarehouseCode = remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(transferDTO.getWarehouseCode(), transferDTO.getCargoCode());
        jsonObject.set("warehouseCode", ownerByDtWmsWarehouseCode.getOutWarehouseCode()); // 设置外部仓库编码
        jsonObject.set("ownerCode", ownerByDtWmsWarehouseCode.getOutOwnerCode()); // 设置成外部的仓库编码
        jsonObject.set("dtWmsCargoCode", ownerByDtWmsWarehouseCode.getWmsOwnerCode());
        jsonObject.set("dtWmsWarehouseCode", ownerByDtWmsWarehouseCode.getWmsWarehouseCode());

        jsonObject.set("tenantId", remoteTenantHelper.queryTenantId(transferDTO.getWarehouseCode(), transferDTO.getCargoCode()));

        List<JSONObject> items = new ArrayList<>();

        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(messageMqDTO.getBillNo());
        List<TransferDetailDTO> transferDetailDTOList = listDetail(transferDetailParam);
        if (CollectionUtil.isEmpty(transferDetailDTOList)) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(transferDetailDTOList.stream().flatMap(it -> Stream.of(it.getOriginSkuLotNo(),it.getTargetSkuLotNo())).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
        Map<String, SkuLotDTO> skuLotDTOMap = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity()));

        for (TransferDetailDTO transferDetailDTO : transferDetailDTOList) {
            JSONObject item = JSONUtil.createObj();
            item.set("itemCode", transferDetailDTO.getOriginSkuCode());
            item.set("itemId", transferDetailDTO.getOriginSkuCode());
            item.set("quantity", BigDecimal.ZERO);
            item.set("sourceInventoryType", transferDetailDTO.getOriginInventoryType());
            item.set("targetInventoryType", transferDetailDTO.getTargetInventoryType());
            item.set("batchCode", transferDetailDTO.getOriginSkuLotNo());
            item.set("orderLineId", transferDetailDTO.getLineSeq());
            item.set("relatedOrderLineId", transferDetailDTO.getLineSeq());
            Optional.ofNullable(skuLotDTOMap.get(transferDetailDTO.getTargetSkuLotNo())).ifPresent(skuLotDTO -> {
                item.set("productDate", DateDescUtil.normalDateStr(skuLotDTO.getManufDate()));
                item.set("expireDate", DateDescUtil.normalDateStr(skuLotDTO.getExpireDate()));
                item.set("produceCode", skuLotDTO.getProductionNo());
            });
            item.set("remark", transferDetailDTO.getRemark());
            items.add(item);
        }

        JSONObject itemsObject = JSONUtil.createObj();
        itemsObject.set("item", items);
        jsonObject.set("items", itemsObject);

//        JSONObject extendProps = JSONUtil.createObj();
//        extendProps.set("relatedOrderId", transferDTO.occupyOrderCode());
//        jsonObject.set("extendProps", extendProps);

        requestMap.put("method", "taobao.logistics.wms.inventory.adjust.report");

        //请求参数
        requestMap.put("bizData", JSONUtil.toJsonStr(jsonObject));


        String request = CallOtherSupport.execute(it -> postForm(it, wmsOtherConfig.getCallBackMercuryUrl()), requestMap, "取消占用回告");
        boolean success = JSONUtil.parseObj(request).getBool("success", false);
        if (success) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
        }
    }
    private void completeCallback(MessageMqDTO messageMqDTO, TransferDTO transferDTO) {
        Map<String, Object> requestMap = new HashMap<>();

        // 占用
        JSONObject jsonObject = JSONUtil.createObj();
        jsonObject.set("totalPage", 1);
        jsonObject.set("currentPage", 1);
        jsonObject.set("pageSize", 1);
        String code = StrUtil.join(StrUtil.EMPTY, transferDTO.getCode(), "TRF");
        jsonObject.set("adjustOrderCode", code);
        jsonObject.set("adjustOrderId", code);
        jsonObject.set("adjustTime", DateDescUtil.normalTimeStr(messageMqDTO.getCreatedTime()));
        jsonObject.set("outBizCode", code);
        jsonObject.set("remark", transferDTO.getNote());
        jsonObject.set("adjustType", "ADJUST");
        jsonObject.set("relatedOrderId", transferDTO.getCode());

        CargoOwnerTaoTianDTO ownerByDtWmsWarehouseCode = remoteCargoOwnerClient.getOwnerByDtWmsWarehouseCode(transferDTO.getWarehouseCode(), transferDTO.getCargoCode());
        jsonObject.set("warehouseCode", ownerByDtWmsWarehouseCode.getOutWarehouseCode()); // 设置外部仓库编码
        jsonObject.set("ownerCode", ownerByDtWmsWarehouseCode.getOutOwnerCode()); // 设置成外部的仓库编码
        jsonObject.set("dtWmsCargoCode", ownerByDtWmsWarehouseCode.getWmsOwnerCode());
        jsonObject.set("dtWmsWarehouseCode", ownerByDtWmsWarehouseCode.getWmsWarehouseCode());

        jsonObject.set("tenantId", remoteTenantHelper.queryTenantId(transferDTO.getWarehouseCode(), transferDTO.getCargoCode()));

        List<JSONObject> items = new ArrayList<>();

        TransferDetailParam transferDetailParam = new TransferDetailParam();
        transferDetailParam.setTransferCode(messageMqDTO.getBillNo());
        List<TransferDetailDTO> transferDetailDTOList = listDetail(transferDetailParam);
        if (CollectionUtil.isEmpty(transferDetailDTOList)) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
            return;
        }

        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(transferDetailDTOList.stream().flatMap(it -> Stream.of(it.getOriginSkuLotNo(),it.getTargetSkuLotNo())).distinct().collect(Collectors.toList()));
        List<SkuLotDTO> skuLotDTOList = remoteSkuLotClient.getList(skuLotParam);
        Map<String, SkuLotDTO> skuLotDTOMap = skuLotDTOList.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity()));

        for (TransferDetailDTO transferDetailDTO : transferDetailDTOList) {
            JSONObject item = JSONUtil.createObj();
            item.set("itemCode", transferDetailDTO.getOriginSkuCode());
            item.set("itemId", transferDetailDTO.getOriginSkuCode());
            item.set("quantity", transferDetailDTO.getChangeQty());
            item.set("sourceInventoryType", transferDetailDTO.getOriginInventoryType());
            item.set("targetInventoryType", transferDetailDTO.getTargetInventoryType());
            item.set("batchCode", transferDetailDTO.getOriginSkuLotNo());
            item.set("orderLineId", transferDetailDTO.getLineSeq());
            item.set("relatedOrderLineId", transferDetailDTO.getLineSeq());
            Optional.ofNullable(skuLotDTOMap.get(transferDetailDTO.getTargetSkuLotNo())).ifPresent(skuLotDTO -> {
                item.set("productDate", DateDescUtil.normalDateStr(skuLotDTO.getManufDate()));
                item.set("expireDate", DateDescUtil.normalDateStr(skuLotDTO.getExpireDate()));
                item.set("produceCode", skuLotDTO.getProductionNo());
            });
            item.set("remark", transferDetailDTO.getRemark());
            items.add(item);
        }

        JSONObject itemsObject = JSONUtil.createObj();
        itemsObject.set("item", items);
        jsonObject.set("items", itemsObject);

//        JSONObject extendProps = JSONUtil.createObj();
//        extendProps.set("relatedOrderId", transferDTO.occupyOrderCode());
//        jsonObject.set("extendProps", extendProps);

        requestMap.put("method", "taobao.logistics.wms.inventory.adjust.report");

        //请求参数
        requestMap.put("bizData", JSONUtil.toJsonStr(jsonObject));


        String request = CallOtherSupport.execute(it -> postForm(it, wmsOtherConfig.getCallBackMercuryUrl()), requestMap, "转移确认回告");
        boolean success = JSONUtil.parseObj(request).getBool("success", false);
        if (success) {
            messageMqDTO.setStatus(MessageMqStatusEnum.COMPLETE_STATUS.getCode());
            remoteMessageMqClient.modify(messageMqDTO);
        }
    }

}
