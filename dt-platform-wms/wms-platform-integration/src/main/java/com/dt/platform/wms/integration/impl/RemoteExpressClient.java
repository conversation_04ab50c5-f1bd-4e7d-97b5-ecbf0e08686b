package com.dt.platform.wms.integration.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.HasChildEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CarrierDTO;
import com.dt.domain.base.dto.ExpressResultDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.param.ExpressParam;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.PackageDetailDTO;
import com.dt.domain.bill.dto.PackageWaybillDTO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.param.PackageWaybillParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.HttpClientUtil;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.wrapper.TmsWrapperV2;
import com.dt.tms.rpc.waybill.client.ITmsElectClient;
import com.dt.tms.rpc.waybill.client.ITmsWaybillClient;
import com.dt.tms.rpc.waybill.param.son.TradeOrderInfo;
import com.dt.tms.rpc.waybill.param.vote.RpcElectConditionListDTO;
import com.dt.tms.rpc.waybill.param.vote.RpcExpressVoteOperationV3ReqVO;
import com.dt.tms.rpc.waybill.param.vote.RpcVoteAddressReqVO;
import com.dt.tms.rpc.waybill.param.waybill.WaybillCreateV2Param;
import com.dt.tms.rpc.waybill.result.elect.oms.RpcElectExpressV3ResVO;
import com.dt.tms.rpc.waybill.result.waybill.RpcWaybillInfoResVO;
import com.dt.tms.tool.common.TmsResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.HttpEntity;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RefreshScope
public class RemoteExpressClient implements IRemoteExpressClient {

    @Value("${dt-wms.express.env}")
    private String env;
    @Value("${dt-wms.express.default.requestUrl}")
    private String requestUrl;
    @Value("${dt-wms.express.toTms}")
    private Boolean toJavaTms;
//    @Value("${dt-wms.express.faultInject}")
//    private String faultInject;


    @DubboReference
    private ITmsWaybillClient waybillClient;

    @DubboReference
    private ITmsElectClient tmsElectClient;

    @Autowired
    private IRemotePackageWaybillClient remotePackageWaybillClient;

    @Autowired
    private IRemoteAreaClient iRemoteAreaClient;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private WmsOtherConfig wmsOtherConfig;

    @Autowired
    private IRemoteShipmentOrderDecryptClient remoteShipmentOrderDecryptClientl;


//    @Override
//    public ExpressParam getExpressParam(ShipmentOrderDTO shipmentOrder, CarrierDTO carrier, PackageDTO pack, List<PackageDetailDTO> packageDetailList) {
//        return getExpressParamV2(shipmentOrder, carrier, pack, packageDetailList, BigDecimal.ZERO);
//    }

    @Override
    public ExpressParam getExpressParamV2(ShipmentOrderDTO shipmentOrder, CarrierDTO carrier, PackageDTO pack, List<PackageDetailDTO> packageDetailList, BigDecimal weight) {
        //深度复制，避免修改到同一个内存地址的数据
        ShipmentOrderDTO shipmentOrderDTO = new ShipmentOrderDTO();
        BeanUtils.copyProperties(shipmentOrder, shipmentOrderDTO);
        remoteShipmentOrderDecryptClientl.getDecryptResult(shipmentOrderDTO, false, "");
        WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(shipmentOrderDTO.getWarehouseCode());
        //订单明细
        List<ExpressParam.PackageItem> packageItemList = packageDetailList.stream().flatMap(a -> {
            //运单信息
            ExpressParam.PackageItem packageItem = new ExpressParam.PackageItem();
            packageItem.setItem_name(a.getSkuName());
            packageItem.setItem_qty(a.getSkuQty().stripTrailingZeros().toPlainString());
            return Stream.of(packageItem);
        }).collect(Collectors.toList());

        ExpressParam expressParam = new ExpressParam();
        //收货地址
        ExpressParam.ConsigneeAddress consigneeAddress = new ExpressParam.ConsigneeAddress();

        //发货地址
        ExpressParam.ShippingAddress shippingAddress = new ExpressParam.ShippingAddress();

//        //if (StringUtils.isEmpty(shipmentOrder.getReceiverProv()) && StringUtils.isEmpty(shipmentOrder.getReceiverCity())) {
//        if (StringUtils.isEmpty(shipmentOrderDTO.getReceiverProvName()) && StringUtils.isEmpty(shipmentOrderDTO.getReceiverCityName())) {
//            consigneeAddress.setProvince(shipmentOrderDTO.getReceiverProvName());
//            consigneeAddress.setCity(shipmentOrderDTO.getReceiverCityName());
//            consigneeAddress.setArea(shipmentOrderDTO.getReceiverAreaName());
//            consigneeAddress.setTown(shipmentOrderDTO.getReceiverStreetName());
//            consigneeAddress.setAddress_detail(shipmentOrderDTO.getReceiverAddress());
//
//            shippingAddress.setProvince("浙江省");
//            shippingAddress.setCity("杭州市");
//            shippingAddress.setArea("江干区");
//            shippingAddress.setAddress_detail("九堡东方");
//
//        } else {

        // 设置省
        String receiverPro = shipmentOrderDTO.getReceiverProvName();
        consigneeAddress.setProvince(receiverPro == null ? "" : receiverPro);

        //设置市
        String receiverCity = shipmentOrderDTO.getReceiverCityName();
        consigneeAddress.setCity(receiverCity == null ? "" : receiverCity);

        //设置区县
        String receiverArea = shipmentOrderDTO.getReceiverAreaName();
        consigneeAddress.setArea(receiverArea == null ? "" : receiverArea);

        consigneeAddress.setTown(shipmentOrderDTO.getReceiverStreetName());
        consigneeAddress.setAddress_detail(shipmentOrderDTO.getReceiverAddress());

        //设置省
        shippingAddress.setProvince(shipmentOrder.getSenderProvName());

        //设置市
        shippingAddress.setCity(shipmentOrder.getSenderCityName());

        //设置区县
        shippingAddress.setArea(shipmentOrder.getSenderAreaName());

        shippingAddress.setAddress_detail(shipmentOrder.getSenderAddress());


        ExpressParam.TradeOrderInfoCols tradeOrderInfoCols = new ExpressParam.TradeOrderInfoCols();
        tradeOrderInfoCols.setPackId(pack.getPackageCode());
        tradeOrderInfoCols.setTax_type(warehouseDTO.getType());
        tradeOrderInfoCols.setApp_type("1");
        tradeOrderInfoCols.setWeight(weight);
        tradeOrderInfoCols.setVolume(pack.getVolume());
        tradeOrderInfoCols.setConsignee_phone(shipmentOrderDTO.getReceiverTel());
        tradeOrderInfoCols.setConsignee_name(shipmentOrderDTO.getReceiverMan());
        if (HasChildEnum.ENABLE.getValue().equals(carrier.getHasChild())) {
            tradeOrderInfoCols.setOrder_no(shipmentOrderDTO.getShipmentOrderCode());
        } else {
            tradeOrderInfoCols.setOrder_no(pack.getPackageCode());
        }
        //交易单号，用户京东拉取面单时使用。
        tradeOrderInfoCols.setTrade_no(shipmentOrderDTO.getTradeNo());
        //TBTOP的原始平台TMGJZY取拓展字段的oaidOrderSourceCode
        if (Objects.equals(shipmentOrderDTO.getSalePlatform(), "TBTOP")
                && !StringUtils.isEmpty(shipmentOrderDTO.getExtraJson())
                && JSONUtil.isJson(shipmentOrderDTO.getExtraJson())) {
            tradeOrderInfoCols.setTrade_no(buildOaidSourceCode(shipmentOrderDTO));
        }
        tradeOrderInfoCols.setCompany_name(pack.getSaleShopId());
        tradeOrderInfoCols.setIs_cod("");
        tradeOrderInfoCols.setCod_value("");
        tradeOrderInfoCols.setPay_mothod("");
        tradeOrderInfoCols.setMonthly_account("");
        if (StrUtil.isNotBlank(carrier.getSettlementAccount())) {
            tradeOrderInfoCols.setPay_mothod("1");
            tradeOrderInfoCols.setMonthly_account(carrier.getSettlementAccount());
        }
        tradeOrderInfoCols.setWaybill_code(ObjectUtils.isEmpty(pack.getExpressNo()) ? "" : pack.getExpressNo());
        tradeOrderInfoCols.setOrder_source(shipmentOrderDTO.getSalePlatform());
        if ("SN".equals(carrier.getCode())) {
            tradeOrderInfoCols.setMarketing_product("18");
            tradeOrderInfoCols.setOrder_source("383");
            tradeOrderInfoCols.setWeight(new BigDecimal("1.1"));
            tradeOrderInfoCols.setVolume(new BigDecimal("3.1"));
        }
        String senderMan = shipmentOrderDTO.getSenderMan();
        if (StrUtil.isBlank(senderMan)) {
            senderMan = warehouseDTO.getContact();
        }
        tradeOrderInfoCols.setSend_name(senderMan);
        String senderTel = shipmentOrderDTO.getSenderTel();
        if (StrUtil.isBlank(senderTel)) {
            senderTel = warehouseDTO.getContactNumber();
        }
        tradeOrderInfoCols.setSend_phone(senderTel);
        tradeOrderInfoCols.setPackage_items(packageItemList);
        tradeOrderInfoCols.setConsignee_address(consigneeAddress);
        tradeOrderInfoCols.setTax_type(warehouseDTO.getType());
        tradeOrderInfoCols.setOaid(shipmentOrder.getOaId());

        expressParam.setStock_code(shipmentOrderDTO.getWarehouseCode());
        //TODO add 2020-12-25 暂将货主编码设置为空 快递
        // 接口默认使用仓库编码替代。后续如果要按货主级别结算，再加上
        if (StringUtils.isEmpty(shipmentOrder.getGlobalNo())) {
            expressParam.setGlobalNo(shipmentOrder.getSoNo());
        } else {
            expressParam.setGlobalNo(shipmentOrder.getGlobalNo());
        }
        expressParam.setOwner_code("");

        expressParam.setCp_code(shipmentOrderDTO.getCarrierCode());
        //设置平台Code

        expressParam.setPlatform_code(buildPlatformCode(shipmentOrderDTO.getSalePlatform(), shipmentOrderDTO.getCarrierCode()));
        expressParam.setShipping_address(shippingAddress);
        expressParam.setSend_phone(shipmentOrderDTO.getSenderTel());
        expressParam.setSend_name(shipmentOrderDTO.getSenderMan());
        expressParam.setTrade_order_info_cols(tradeOrderInfoCols);
        expressParam.setOrderTag(shipmentOrder.getOrderTag());

        List<String> strings = jumpStopExpressCodeList(shipmentOrderDTO);
        expressParam.setJumpStopExpressCodeList(strings);


        expressParam.setInternalShopCode(shipmentOrderDTO.getInternalShopCodeStr());

//        //京东平台 送货上门
//        if (Objects.equals(shipmentOrderDTO.getSalePlatform(), "JD")) {
//            if (!CollectionUtils.isEmpty(shipmentOrderDTO.getRpcServiceInfoListByExtraJson())
//                    && shipmentOrderDTO.getRpcServiceInfoListByExtraJson().stream().anyMatch(a -> Objects.equals(a.getServiceCode(), CommonConstantUtil.JDWJ_DELIVERY_TO_DOOR))) {
//                Map<String, Object> extraMap;
//                if (CollectionUtils.isEmpty(expressParam.getExtraMap())) {
//                    extraMap = new HashMap<>();
//                } else {
//                    extraMap = expressParam.getExtraMap();
//                }
//                extraMap.put(CommonConstantUtil.JDWJ_DELIVERY_TO_DOOR, CommonConstantUtil.JDWJ_DELIVERY_TO_DOOR);
//                expressParam.setExtraMap(extraMap);
//            }
//        }

        //拉单拓传信息
        if (!CollectionUtils.isEmpty(shipmentOrderDTO.getRpcServiceInfoListByExtraJson())) {
            Map<String, Object> extraMap;
            if (CollectionUtils.isEmpty(expressParam.getExtraMap())) {
                extraMap = new HashMap<>();
            } else {
                extraMap = expressParam.getExtraMap();
            }
            extraMap.put(CommonConstantUtil.SPECIAL_DELIVERY_TO_DOOR, JSONUtil.toJsonStr(shipmentOrderDTO.getRpcServiceInfoListByExtraJson()));
            expressParam.setExtraMap(extraMap);
        }
        if (!StringUtils.isEmpty(shipmentOrderDTO.getExtraJson())) {
            JSONObject shipJson = JSONUtil.parseObj(shipmentOrderDTO.getExtraJson());
            if (shipJson != null
                    && shipJson.containsKey("originalPlatform")
                    && Objects.equals(shipJson.getStr("originalPlatform"), "DouYinMarket")) {
                Map<String, Object> extraMap;
                if (CollectionUtils.isEmpty(expressParam.getExtraMap())) {
                    extraMap = new HashMap<>();
                } else {
                    extraMap = expressParam.getExtraMap();
                }
                extraMap.put(CommonConstantUtil.ORIGINAL_PLATFORM, shipJson.getStr("originalPlatform"));
                extraMap.put(CommonConstantUtil.DOUYIN_SHOP_ID, shipJson.getStr("douyinShopId"));
                expressParam.setExtraMap(extraMap);
            }

        }

        return expressParam;
    }

    /**
     * @param shipmentOrderDTO
     * @return java.lang.String
     * <AUTHOR>
     * @describe: TBTOP的原始平台TMGJZY取拓展字段的oaidSourceCode
     * @date 2024/2/27 15:18
     */
    private String buildOaidSourceCode(ShipmentOrderDTO shipmentOrderDTO) {
        JSONObject shipJson = JSONUtil.parseObj(shipmentOrderDTO.getExtraJson());
        if (shipJson != null && shipJson.containsKey("oaidSourceCode") && shipJson.containsKey("originalPlatform")) {
            String oaidSourceCode = shipJson.getStr("oaidSourceCode");
            String originalPlatform = shipJson.getStr("originalPlatform");
            List<String> oaidSalePlatformList = wmsOtherConfig.getOaidSalePlatformList();
            if (CollectionUtils.isEmpty(oaidSalePlatformList)) {
                oaidSalePlatformList = new ArrayList<>();
            }
            if (!StringUtils.isEmpty(oaidSourceCode) && !StringUtils.isEmpty(originalPlatform)
                    && oaidSalePlatformList.contains(originalPlatform)) {
                return oaidSourceCode;
            }
        }
        return shipmentOrderDTO.getTradeNo();
    }

    /**
     * 快递获取组装 PlatformCode
     *
     * @param salePlatform
     * @param carrierCode
     * @return
     */
    private String buildPlatformCode(String salePlatform, String carrierCode) {
        if (StringUtils.isEmpty(salePlatform)) {
            return carrierCode;
        }
//        if (salePlatform.equalsIgnoreCase("CAINIAO")
//                || salePlatform.equalsIgnoreCase("JD")
//                || salePlatform.equalsIgnoreCase("PDD")
//                || salePlatform.equalsIgnoreCase("VIP")
//                || salePlatform.equalsIgnoreCase("SN")
//                || salePlatform.equalsIgnoreCase("TBTOP")
//                || salePlatform.equalsIgnoreCase("DY")) {
        //忽略大小写
        if (wmsOtherConfig != null
                && !CollectionUtils.isEmpty(wmsOtherConfig.getPrintSalePlatformList())
                && wmsOtherConfig.getPrintSalePlatformList().stream().anyMatch(a -> a.equalsIgnoreCase(salePlatform))) {
            return salePlatform;
        }
        return carrierCode;
    }

    @Override
    public ExpressResultDTO getExpressResult(String shipmentOrderCode, ExpressParam param) {
        log.info("express param:{}", JSONUtil.toJsonStr(param));
        Assert.isTrue(!ObjectUtils.isEmpty(requestUrl), "快递面单获取地址不能为空，请配置：dt-wms.express.default.requestUrl");

        // 尝试走javaTms
        ExpressResultDTO result = null;
        try {
//                result = toJavaTms(param);
            result = toJavaTmsV2(param);
        } catch (Exception e) {
            log.error("[获取面单]TMS服务调用失败：orderNo： {} error： {}", param.getTrade_order_info_cols().getOrder_no(), e);
        }
        log.info("express Result:{}", JSONUtil.toJsonStr(result));
        try {
            //查询包裹面单
            if (!ObjectUtils.isEmpty(result) && result.getSuccess()) {
                List<ExpressResultDTO.WaybillApplyInfo> waybillApplyInfoList = result.getWaybill_apply_info();
                //TODO 拉取快递单，快递接口返回不一定是自己的包裹param.getTrade_order_info_cols().getOrder_no()
//                ExpressResultDTO.WaybillApplyInfo waybillApplyInfo = waybillApplyInfoList.stream()
//                        .filter(a -> a.getOrder_no().equals(param.getTrade_order_info_cols().getOrder_no()))
//                        .findFirst().get();

                ExpressResultDTO.WaybillApplyInfo waybillApplyInfo = waybillApplyInfoList.get(0);

                PackageWaybillParam packageWaybillParam = new PackageWaybillParam();
                packageWaybillParam.setShipmentCode(shipmentOrderCode);
                packageWaybillParam.setPackageCode(param.getTrade_order_info_cols().getOrder_no());
                packageWaybillParam.setWaybillNo(waybillApplyInfo.getWaybill_code());
                PackageWaybillDTO packageWaybill = remotePackageWaybillClient.get(packageWaybillParam);
                if (ObjectUtils.isEmpty(packageWaybill)) {
                    packageWaybill = new PackageWaybillDTO();
                    packageWaybill.setShipmentCode(shipmentOrderCode);
                    packageWaybill.setPackageCode(param.getTrade_order_info_cols().getOrder_no());
                }
                packageWaybill.setWaybillNo(waybillApplyInfo.getWaybill_code());
                packageWaybill.setShortAddress(waybillApplyInfo.getShort_address());
                packageWaybill.setPackageCenterCode(waybillApplyInfo.getPackage_center_code());
                packageWaybill.setPackageCenterName(waybillApplyInfo.getPackage_center_name());
                packageWaybill.setOneSectionCode(waybillApplyInfo.getOne_section_code());
                packageWaybill.setTwoSectionCode(waybillApplyInfo.getTwo_section_code());
                packageWaybill.setThreeSectionCode(waybillApplyInfo.getThree_section_code());
                if (ObjectUtil.isNotEmpty(waybillApplyInfo.getExtraJson())) {
                    packageWaybill.setExtraJson(waybillApplyInfo.getExtraJson());
                } else {
                    packageWaybill.setExtraJson("");
                }
                log.info("express packageWaybill:{}", JSONUtil.toJsonStr(packageWaybill));
                Boolean saveOrUpdateResult = remotePackageWaybillClient.saveOrUpdate(ConverterUtil.convert(packageWaybill, PackageWaybillParam.class));
                if (!saveOrUpdateResult) {
                    throw new BaseException(BaseBizEnum.TIP, "存储面单异常");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException(BaseBizEnum.TIP, "获取运单信息失败");
        }
        return result;
    }
//
//    @Override
//    public WaybillCreateParam getTMSExpressParam(ShipmentOrderDTO shipmentOrderDTO, CarrierDTO carrier, PackageDTO pack, List<PackageDetailDTO> packageDetailList) {
//        WaybillCreateParam waybillParam = new WaybillCreateParam();
//        waybillParam.setReqId(System.currentTimeMillis() + "_" + RandomUtils.nextInt());
//        waybillParam.setStock_code(shipmentOrderDTO.getWarehouseCode());
//        waybillParam.setOwner_code(shipmentOrderDTO.getCargoCode());
//        waybillParam.setTms_express_code(shipmentOrderDTO.getCarrierCode());
//        waybillParam.setEc_code(buildPlatformCode(shipmentOrderDTO.getSalePlatform(), shipmentOrderDTO.getCarrierCode()));
//
//        ShippingAddress shippingAddress = new ShippingAddress();
//        shippingAddress.setProvince(shipmentOrderDTO.getSenderProvName());
//        shippingAddress.setCity(shipmentOrderDTO.getSenderCityName());
//        shippingAddress.setArea(shipmentOrderDTO.getSenderAreaName());
//        shippingAddress.setAddress_detail(shipmentOrderDTO.getSenderAddress());
//        waybillParam.setShipping_address(shippingAddress);
//
//        TradeOrderInfo orderInfo = new TradeOrderInfo();
//        // 设置交易单号
//        orderInfo.setTrade_no(shipmentOrderDTO.getTradeNo());
//        orderInfo.setWeight(new BigDecimal(0));
//        orderInfo.setVolume(new BigDecimal(0));
//        orderInfo.setConsignee_phone(shipmentOrderDTO.getReceiverTel());
//        orderInfo.setConsignee_name(shipmentOrderDTO.getReceiverMan());
//        if (HasChildEnum.ENABLE.getValue().equals(carrier.getHasChild())) {
//            orderInfo.setOrder_no(shipmentOrderDTO.getShipmentOrderCode());
//        } else {
//            orderInfo.setOrder_no(pack.getPackageCode());
//        }
//        orderInfo.setWaybill_code(ObjectUtils.isEmpty(pack.getExpressNo()) ? "" : pack.getExpressNo());
//        orderInfo.setIs_cod("");
//        orderInfo.setPay_mothod("");
//        orderInfo.setMonthly_account("");
//        orderInfo.setCod_value("");
//
//        List<PackageItem> itemList = packageDetailList.stream().flatMap(data -> {
//            PackageItem item = new PackageItem();
//            item.setItem_name(data.getSkuName());
//            item.setItem_qty(data.getSkuQty().stripTrailingZeros().toPlainString());
//            return Stream.of(item);
//        }).collect(Collectors.toList());
//        orderInfo.setPackage_items(itemList);
//
//        ConsigneeAddress consigneeAddress = new ConsigneeAddress();
//        consigneeAddress.setProvince(shipmentOrderDTO.getReceiverProvName());
//        consigneeAddress.setCity(shipmentOrderDTO.getReceiverCityName());
//        consigneeAddress.setArea(shipmentOrderDTO.getReceiverAreaName());
//        consigneeAddress.setAddress_detail(shipmentOrderDTO.getReceiverAddress());
//        consigneeAddress.setTown(shipmentOrderDTO.getReceiverStreetName());
//        orderInfo.setConsignee_address(consigneeAddress);
//
//
//        orderInfo.setSend_name(shipmentOrderDTO.getSenderMan());
//        orderInfo.setSend_phone(shipmentOrderDTO.getSenderTel());
//        orderInfo.setApp_type("1");
//        orderInfo.setOrder_source(shipmentOrderDTO.getSalePlatform());
//        if ("SN".equals(carrier.getCode())) {
//            orderInfo.setMarketing_product("18");
//            orderInfo.setOrder_source("383");
//            orderInfo.setWeight(new BigDecimal("1.1"));
//            orderInfo.setVolume(new BigDecimal("3.1"));
//        }
//        //标识 音尊达
//        Set<OrderTagEnum> orderTagEnums = OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag());
//        List<RpcServiceInfo> rpcServiceInfoList = Lists.newArrayList();
//        if (orderTagEnums.contains(OrderTagEnum.YZD_TAG)) {
//            RpcServiceInfo rpcServiceInfo = new RpcServiceInfo();
//            rpcServiceInfo.setServiceCode("DY_YZD");
//            rpcServiceInfoList.add(rpcServiceInfo);
//        }
//        //业务标签
//        orderInfo.setServiceInfoList(rpcServiceInfoList);
//        waybillParam.setTrade_order_info_cols(orderInfo);
//        return waybillParam;
//    }

    @Override
    public Result<String> calculateCarrier(ShipmentOrderDTO shipmentOrder, BigDecimal weight) {
        ShipmentOrderDTO shipmentOrderDTO = new ShipmentOrderDTO();
        BeanUtils.copyProperties(shipmentOrder, shipmentOrderDTO);
        remoteShipmentOrderDecryptClientl.getDecryptResult(shipmentOrderDTO, false, "");

        RpcExpressVoteOperationV3ReqVO rpcExpressVoteOperationV3ReqVO = new RpcExpressVoteOperationV3ReqVO();
        rpcExpressVoteOperationV3ReqVO.setSysCode("WMS");
        rpcExpressVoteOperationV3ReqVO.setVoteMethod("V1");
        rpcExpressVoteOperationV3ReqVO.setWarehouseCode(shipmentOrderDTO.getWarehouseCode());
        rpcExpressVoteOperationV3ReqVO.setEcCode(buildPlatformCode(shipmentOrderDTO.getSalePlatform(), shipmentOrderDTO.getCarrierCode()));
        rpcExpressVoteOperationV3ReqVO.setOrderNo(shipmentOrderDTO.getShipmentOrderCode());

        RpcVoteAddressReqVO send = new RpcVoteAddressReqVO();
        send.setProvince(shipmentOrder.getSenderProvName());
        send.setCity(shipmentOrder.getSenderCityName());
        send.setArea(shipmentOrder.getSenderAreaName());
        send.setDetail(shipmentOrder.getSenderAddress());

        RpcVoteAddressReqVO receive = new RpcVoteAddressReqVO();
        receive.setProvince(shipmentOrder.getReceiverProvName());
        receive.setCity(shipmentOrder.getReceiverCityName());
        receive.setArea(shipmentOrder.getReceiverAreaName());
        receive.setDetail(shipmentOrder.getReceiverAddress());

        rpcExpressVoteOperationV3ReqVO.setSendAddress(send);
        rpcExpressVoteOperationV3ReqVO.setReceiveAddress(receive);

        rpcExpressVoteOperationV3ReqVO.setWeight(weight);

        RpcElectConditionListDTO rpcElectConditionListDTO = new RpcElectConditionListDTO();
        rpcElectConditionListDTO.setVerifyOutage(false);  // 这里不是所有的都不校验停发的意思，而是下面的列表中的快递不校验，不在下面列表中的快递还是要校验停发的
        rpcElectConditionListDTO.setSkipOutageExpressCodeList(jumpStopExpressCodeList(shipmentOrderDTO)); // 指定某些快递（tms支持的快递编码）跳过停发检查  verifyOutage=false时生效
        rpcExpressVoteOperationV3ReqVO.setElectConditionList(rpcElectConditionListDTO);

//        TmsResult<RpcElectV3ResVO> tmsResult = tmsElectClient.electV3(rpcExpressVoteOperationV3ReqVO);

        TmsResult<RpcElectExpressV3ResVO> tmsResult = tmsElectClient.tmsElectV3(rpcExpressVoteOperationV3ReqVO);

        if (tmsResult.isSuccess()
                && ObjectUtil.isNotEmpty(tmsResult.getData())
                && CollectionUtil.isNotEmpty(tmsResult.getData().getUsableList())) {
            return Result.success(tmsResult.getData().getUsableList().get(0).getTmsExpressCode());
        }

        return Result.failWithMessage("快递公司缺失");
    }


    private ExpressResultDTO toJavaTmsV2(ExpressParam param) {
        WaybillCreateV2Param waybillCreateV2Param = TmsWrapperV2.buildWaybillParam(param);
        // 处理交易单号
        String tradeNo = waybillCreateV2Param.getTrade_order_info_cols().getTrade_no();
        if (StrUtil.isNotBlank(tradeNo) && tradeNo.contains(",")) {
            waybillCreateV2Param.getTrade_order_info_cols().setTrade_no(tradeNo.split(",")[0]);
        }

        log.info("toJavaTmsV2 waybillCreateV2Param:{} ", JSONUtil.toJsonStr(waybillCreateV2Param));
        TmsResult<List<RpcWaybillInfoResVO>> tmsResult = waybillClient.createWaybillV2(waybillCreateV2Param);
        return TmsWrapperV2.buildWaybillResult(tmsResult);
    }


    /**
     * 走C#版 TMS
     *
     * @param param
     * @return
     */
    private ExpressResultDTO httpOldTms(ExpressParam param) {
        ExpressResultDTO result = new ExpressResultDTO();
        if ("dev".equals(env) || "test".equals(env)) {
            String text = "{\"stock_code\":\"DT_HNWMS\",\"owner_code\":\"\",\"cp_code\":\"YUNDA\",\"platform_code\":\"YUNDA\",\"shipping_address\":{\"province\":\"浙江省\",\"city\":\"金华市\",\"area\":\"金东区\",\"address_detail\":\"菜鸟网络浙江金义园区1期7号库\"},\"trade_order_info_cols\":{\"app_type\":\"1\",\"weight\":1,\"volume\":1,\"consignee_phone\":\"***********\",\"consignee_name\":\"林致\",\"order_no\":\"RELANG2010230650279511\",\"company_name\":\"\",\"is_cod\":\"\",\"pay_mothod\":\"\",\"monthly_account\":\"\",\"cod_value\":\"0\",\"package_items\":[{\"item_name\":\"UNO吾诺控油洁面乳黑色130g/支\",\"item_qty\":\"2\"}],\"consignee_address\":{\"province\":\"陕西省\",\"city\":\"渭南市\",\"area\":\"临渭区\",\"address_detail\":\"陕西省渭南市临渭区站南街道 朝阳大街渭南师范学院\",\"town\":\"站南街道 \"},\"send_name\":\"endocare安多可旗舰店\",\"send_phone\":\"***********\",\"marketing_product\":\"18\",\"order_source\":\"383\"}}";
            ExpressParam expressParam = JSON.parseObject(text, ExpressParam.class);
            try {
                HttpEntity httpEntity = EntityBuilder
                        .create()
                        .setContentEncoding("utf-8")
                        .setContentType(ContentType.APPLICATION_JSON)
                        .setText(JSON.toJSONString(expressParam))
                        .build();
                String strResult = HttpClientUtil.post(requestUrl, httpEntity);
                log.info(JSONUtil.toJsonStr(strResult));
                result = JSON.parseObject(strResult, ExpressResultDTO.class);
                result.getWaybill_apply_info().get(0).setOrder_no(param.getTrade_order_info_cols().getOrder_no());
                if (StringUtils.isEmpty(param.getTrade_order_info_cols().getWaybill_code())) {
                    String dataTimeKey = new DateTime().toString("yyMMdd");
                    String incrementKey = String.format("%s%s", "DT-WMS:EXPRESS_NO_DAY_INCREMENT:", dataTimeKey);
                    Long incrementValue = redisTemplate.opsForValue().increment(incrementKey);
                    String waybillNo = String.format("%s%s", dataTimeKey, String.format("%08d", incrementValue));
                    result.getWaybill_apply_info().get(0).setWaybill_code(waybillNo);
                } else {
                    result.getWaybill_apply_info().get(0).setWaybill_code(param.getTrade_order_info_cols().getWaybill_code());
                }
                if (param.getCp_code().equalsIgnoreCase("FAIL")) {
                    result.getWaybill_apply_info().get(0).setWaybill_code("");
                }
                if (param.getCp_code().equalsIgnoreCase("FAIL50")) {
                    if (RandomUtil.randomInt(0, 100) < 50) {
                        result.getWaybill_apply_info().get(0).setWaybill_code("");
                    }
                }
            } catch (Exception e) {
                log.error("获取物流面单号异常：", e.getMessage());
            }
        } else {
            try {
                //TODO 2020-01-05 金华金义仓库快递单不从正式环境拉取
//                if(Objects.equals(CurrentRouteHolder.getWarehouseCode(),"DT_HNWMS")){
//                    requestUrl = "http://tms.yang800.com:8090/test/api/getwaybillno";
//                }
                log.info("express param:{}" + JSONUtil.toJsonStr(param));
                HttpEntity httpEntity = EntityBuilder
                        .create()
                        .setContentEncoding("utf-8")
                        .setContentType(ContentType.APPLICATION_JSON)
                        .setText(JSON.toJSONString(param))
                        .build();
                String strResult = HttpClientUtil.post(requestUrl, httpEntity);
                log.info("express strResult:{}" + JSONUtil.toJsonStr(strResult));
                result = JSON.parseObject(strResult, ExpressResultDTO.class);
            } catch (Exception e) {
                log.error("获取物流面单号异常：", e.getMessage());
            }
        }
        return result;
    }

    public static List<String> jumpStopExpressCodeList(ShipmentOrderDTO shipmentOrderDTO) {
        String extraJson = shipmentOrderDTO.getExtraJson();
        if (StrUtil.isEmpty(extraJson)) {
            return new ArrayList<>();
        }
        JSONObject jsonObject = JSONUtil.parseObj(extraJson);
        boolean jumpStopExpressCodeList = jsonObject.containsKey("jumpStopExpressCodeList");
        if (!jumpStopExpressCodeList) {
            return new ArrayList<>();
        }
        JSONArray stopExpressCodeList = jsonObject.getJSONArray("jumpStopExpressCodeList");
        return stopExpressCodeList.toList(String.class);
    }
}
