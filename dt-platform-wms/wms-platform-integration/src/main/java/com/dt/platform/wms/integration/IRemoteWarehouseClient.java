package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.param.WarehouseParam;

import java.util.List;

public interface IRemoteWarehouseClient {
    /**
     * 通过ID查询仓库信息
     *
     * @param id
     * @return
     */
    WarehouseDTO queryById(Long id);

    /**
     * 禁用
     *
     * @param param
     * @return
     */
    Boolean disable(WarehouseParam param);

    /**
     * 启用
     *
     * @param param
     * @return
     */
    Boolean enable(WarehouseParam param);

    /**
     * 按ID修改
     *
     * @param param
     * @return
     */
    Boolean update(WarehouseParam param);

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    IPage<WarehouseDTO> queryPage(WarehouseParam param);

    /**
     * 通过编码查询仓库档案
     *
     * @param code
     * @return
     */
    WarehouseDTO queryByCode(String code);

    /**
     * 新增仓库档案
     *
     * @param warehouseParam1
     * @return
     */
    Boolean save(WarehouseParam warehouseParam1);

    /**
     * 修改仓库信息
     *
     * @param param
     * @return
     */
    Boolean modify(WarehouseParam param);

    /**
     * 查询仓库列表
     *
     * @param convert
     * @return
     */
    List<WarehouseDTO> queryList(WarehouseParam convert);

    /**
     * 功能描述:  所有仓库执行sql
     * 创建时间:  2021/5/10 10:18 上午
     *
     * @param sql:
     * @return com.dt.component.common.result.Result<java.lang.Integer>
     * <AUTHOR>
     */
    Integer createOrUpdateTable(String sql);
    /**
     * @param warehouseCode
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 查询仓库是否跨境还是保税
     * @date 2024/1/23 14:01
     */
    Boolean getDeclarationWarehouse(String warehouseCode);
    /**
     * @param warehouseCode
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe:
     * @date 2024/3/13 9:43
     */
    Boolean getTaoTianWarehouse(String warehouseCode);


}
