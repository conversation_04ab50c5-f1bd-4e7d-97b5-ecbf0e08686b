package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.message.MessageTypeEnum;
import com.dt.domain.bill.bo.TransferPersistBO;
import com.dt.domain.bill.dto.TransferDTO;
import com.dt.domain.bill.dto.TransferDetailDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.param.TransferDetailBatchParam;
import com.dt.domain.bill.param.TransferDetailParam;
import com.dt.domain.bill.param.TransferParam;

import java.util.List;

/**
 * Created by nobody on 2020/12/28 17:44
 */
public interface IRemoteTransferClient {

    /**
     * 增加转移单
     * @param transferParam
     * @return
     */
    Boolean add(TransferParam transferParam);

    /**
     * 审核
     * @return
     */
    Boolean update(TransferDTO transferDTO);

    Boolean update(TransferPersistBO transferBO);

    /**
     * 查找
     * @param transferParam
     * @return
     */
    TransferDTO get(TransferParam transferParam);

    /**
     * 增加转移单明细
     * @param transferDetailBatchParam
     * @return
     */
    Boolean addDetail(TransferDetailBatchParam transferDetailBatchParam);

    /**
     * 修改转移单明细
     * @param transferDetailBatchParam
     * @return
     */
    Boolean updateDetail(TransferDetailBatchParam transferDetailBatchParam);

    /**
     * 删除明细
     * @param transferCode
     * @return
     */
    Boolean delDetail(String transferCode);

    /**
     * 明细列表
     * @param transferDetailParam
     * @return
     */
    List<TransferDetailDTO> listDetail(TransferDetailParam transferDetailParam);

    /**
     * 分页
     * @param transferParam
     * @return
     */
    Page<TransferDTO> page(TransferParam transferParam);

    /**
     * 李彪
     * @param transferParam
     * @return
     */
    List<TransferDTO> list(TransferParam transferParam);

    /**
     * 明细分页
     *
     * @param transferDetailParam
     * @return
     */
    Page<TransferDetailDTO> page(TransferDetailParam transferDetailParam);

    List<MessageMqDTO> taoTianMessageMqDTO(TransferDTO transferDTO, MessageTypeEnum messageTypeEnum);

    void callback(List<MessageMqDTO> messageMqDTOList);
}
