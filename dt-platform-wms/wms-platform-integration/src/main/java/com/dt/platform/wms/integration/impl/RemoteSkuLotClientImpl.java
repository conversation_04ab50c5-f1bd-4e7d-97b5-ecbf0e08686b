package com.dt.platform.wms.integration.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.cds.out.api.InventoryOrderRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.res.TaotianLogisticsTraceInfoResVo;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.asn.AsnOutTypeEnum;
import com.dt.component.common.enums.asn.AsnStatusEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.base.client.ISkuLotClient;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.SkuLotSourceDTO;
import com.dt.domain.base.dto.SkuLotSourceExtraJsonDTO;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.bill.dto.AllocationOrderDTO;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.related.RelateOrdersTaoTianDTO;
import com.dt.domain.bill.param.AllocationOrderParam;
import com.dt.domain.bill.param.AsnParam;
import com.dt.domain.bill.param.PackageParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.integration.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/27 14:13
 */
@Service
@Slf4j
public class RemoteSkuLotClientImpl implements IRemoteSkuLotClient {

    @DubboReference
    ISkuLotClient iSkuLotClient;

    @DubboReference
    InventoryOrderRpc inventoryOrderRpc;

    @Resource
    IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    IRemoteAsnClient remoteAsnClient;

    @Resource
    IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    IRemotePackageClient remotePackageClient;

    @Resource
    IRemoteAllocationOrderClient remoteAllocationOrderClient;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Autowired
    private RemoteTenantHelper remoteTenantHelper;


    @Override
    public SkuLotDTO queryAllocateSkuLotByParam(SkuLotParam skuLotParam) {
        return iSkuLotClient.queryAllocateSkuLotByParam(skuLotParam).getData();
    }

    @Override
    public SkuLotDTO queryAllocateSkuLotById(Long id) {
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setId(id);
        return iSkuLotClient.get(skuLotParam).getData();
    }

    @Override
    public SkuLotDTO queryAllocateSkuLotByCode(String code) {
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCode(code);
        return iSkuLotClient.get(skuLotParam).getData();
    }

    @Override
    public Boolean commitSkuLot(List<SkuLotDTO> skuLotDTOs) {
        return iSkuLotClient.commitSkuLot(skuLotDTOs).getData();
    }

    @Override
    public List<String> getAllSkuLotNoList(SkuLotParam param) {
        return iSkuLotClient.getAllSkuLotNoList(param).getData();
    }

    @Override
    public List<SkuLotDTO> getList(SkuLotParam param) {
        return iSkuLotClient.getList(param).getData();
    }

    @Override
    public List<SkuLotDTO> getAppointMultipleParam(SkuLotParam skuLotParam, List<String> tableFields) {
        return iSkuLotClient.getAppointMultipleParam(skuLotParam,tableFields).getData();
    }

    @Override
    public SkuLotDTO get(SkuLotParam param) {
        return iSkuLotClient.get(param).getData();
    }

    @Override
    public IPage<SkuLotDTO> getPage(SkuLotParam param) {
        return iSkuLotClient.getPage(param).getData();
    }

    @Override
    public List<SkuLotDTO> getSkuLotNoList(List<String> skuLotNoList) {
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(skuLotNoList);
        return iSkuLotClient.getList(skuLotParam).getData();
    }

    @Override
    public Boolean saveBatch(List<SkuLotDTO> skuLotDTOList) {
        return iSkuLotClient.saveBatch(skuLotDTOList).getData();
    }

    @Override
    public List<SkuLotDTO> getListByPage(SkuLotParam skuLotParam) {
        List<SkuLotDTO> skuLotDTOList = new ArrayList<>();
        skuLotParam.setSize(2000);
        Page<SkuLotDTO> locationClientPage = iSkuLotClient.getPage(skuLotParam).getData();
        for (int i = 1; i <= locationClientPage.getPages(); i++) {
            skuLotParam.setCurrent(i);
            skuLotParam.setSize(2000);
            Page<SkuLotDTO> clientPage = iSkuLotClient.getPage(skuLotParam).getData();
            skuLotDTOList.addAll(clientPage.getRecords());
        }
        return skuLotDTOList;
    }

    @Override
    public SkuLotDTO queryAllocateSkuLotCWByParam(SkuLotParam skuLotParam) {
        return iSkuLotClient.queryAllocateSkuLotCWByParam(skuLotParam).getData();
    }

    @Override
    public Map<String, SkuLotDTO> skuLotMap(List<String> skuLotNoList) {
        if (CollectionUtil.isEmpty(skuLotNoList)) return new HashMap<>();
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCodeList(skuLotNoList);
        List<SkuLotDTO> data = iSkuLotClient.getList(skuLotParam).getData();
        return data.stream().collect(Collectors.toMap(SkuLotDTO::getCode, Function.identity(), BinaryOperator.maxBy(Comparator.comparing(SkuLotDTO::getId))));
    }

    @Override
    public void supplySourceInfomation(SkuLotDTO skuLotDTO) {
        RpcContextUtil.setWarehouseCode(skuLotDTO.getWarehouseCode());
        //非淘天不处理
        if (!remoteWarehouseClient.getTaoTianWarehouse(skuLotDTO.getWarehouseCode())) {
            return;
        }
        //入库关联单号 为空不处理
        if (StringUtils.isEmpty(skuLotDTO.getExternalLinkBillNo())) {
            return;
        }
        if (!StringUtils.isEmpty(skuLotDTO.getExtraJson()) && JSONUtil.isJson(skuLotDTO.getExtraJson())) {
            SkuLotSourceExtraJsonDTO skuLotSourceExtraJsonDTO = JSONUtil.toBean(skuLotDTO.getExtraJson(), SkuLotSourceExtraJsonDTO.class);
            if (skuLotSourceExtraJsonDTO != null && Objects.equals(skuLotSourceExtraJsonDTO.getFindSource(), "OK")) {
                return;
            }
        }
        RLock lock = redissonClient.getLock("dt_wms_supply_source_info_lock:" + skuLotDTO.getWarehouseCode() + skuLotDTO.getCode());
        Boolean isLock = false;
        try {
            isLock = lock.tryLock(1, 5, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BaseException(BaseBizEnum.TIP, "批次补充溯源信息消费,稍后会重试");
            }

            AsnParam asnParam = new AsnParam();
            asnParam.setPoNo(skuLotDTO.getExternalLinkBillNo());
            List<AsnDTO> asnDTOList = remoteAsnClient.getList(asnParam);
            if (CollectionUtils.isEmpty(asnDTOList)) {
                return;
            }
            List<String> outTypeList = Arrays.asList(AsnOutTypeEnum.BCRK.getCode(), AsnOutTypeEnum.KHZRK.getCode(), AsnOutTypeEnum.CGRK.getCode());
            AsnDTO asnDTO = asnDTOList.stream()
                    .filter(a -> !Objects.equals(a.getStatus(), AsnStatusEnum.CANCEL.getCode()))
                    .filter(a -> outTypeList.contains(a.getOutType()))
                    .findFirst().orElse(null);
            if (asnDTO == null || StringUtils.isEmpty(asnDTO.getExtraJson()) || !JSONUtil.isJson(asnDTO.getExtraJson())) {
                return;
            }
            //二次查询确认
            Boolean isCheck = checkSkuLotSupplyInfo(skuLotDTO.getCode());
            if (isCheck) {
                return;
            }
            AsnOutTypeEnum asnOutTypeEnum = AsnOutTypeEnum.getEnum(asnDTO.getOutType());
            switch (asnOutTypeEnum) {
                case CGRK:
                    handCGRKSource(skuLotDTO, asnDTO);
                    break;
                case BCRK:
                    handBCRKSource(skuLotDTO, asnDTO);
                    break;
                case KHZRK:
                    handKHZRKSource(skuLotDTO, asnDTO);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException(BaseBizEnum.TIP, "批次补充溯源信息消费,稍后会重试");
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    /**
     * @param skuLotNo
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 二次检验是否成功
     * @date 2024/3/27 9:31
     */
    private Boolean checkSkuLotSupplyInfo(String skuLotNo) {
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCode(skuLotNo);
        SkuLotDTO skuLotDTO = iSkuLotClient.get(skuLotParam).getData();
        if (skuLotDTO == null) {
            return true;
        }
        if (!StringUtils.isEmpty(skuLotDTO.getExtraJson()) && JSONUtil.isJson(skuLotDTO.getExtraJson())) {
            SkuLotSourceExtraJsonDTO skuLotSourceExtraJsonDTO = JSONUtil.toBean(skuLotDTO.getExtraJson(), SkuLotSourceExtraJsonDTO.class);
            if (skuLotSourceExtraJsonDTO != null && Objects.equals(skuLotSourceExtraJsonDTO.getFindSource(), "OK")) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param skuLotDTO
     * @param asnDTO
     * @return void
     * <AUTHOR>
     * @describe: 跨货主调拨入库补充溯源信息
     * 跨货主调拨入库：
     * 1. 跨货主调拨，出入都是在代塔wms做，出入库单都能在代塔体系查到；跨仓或者同仓都会有；是需要通过当前调拨入的调拨出库单来查询出库的溯源信息，填充入的溯源信息
     * 2. 通过当前入库单上relatedOrders中单据类型为【KHZCK】的orderCode（即调拨出库的淘天出库单号，也就是wms出库单中的上游单号），在erp找到调拨出库的当前商品的出库批次的溯源信息填充
     * @date 2024/3/26 15:56
     */
    private void handKHZRKSource(SkuLotDTO skuLotDTO, AsnDTO asnDTO) {
        //获取关联清关单号
        JSONObject jsonObject = JSONUtil.parseObj(asnDTO.getExtraJson());
        if (!jsonObject.containsKey("relatedOrders") || StringUtils.isEmpty(jsonObject.getStr("relatedOrders"))
                || !JSONUtil.isJsonArray(jsonObject.getStr("relatedOrders"))) {
            return;
        }
        String poNo = "";
        String realWarehouseCode = "";
        if (!jsonObject.containsKey("realWarehouseCode") || StringUtils.isEmpty(jsonObject.getStr("realWarehouseCode"))) {
            log.info("realWarehouseCode empty:{}", skuLotDTO.getCode());
            return;
        }
        realWarehouseCode = jsonObject.getStr("realWarehouseCode");
        JSONArray relatedOrders = JSONUtil.parseArray(jsonObject.getStr("relatedOrders"));
        List<RelateOrdersTaoTianDTO> relateOrders = JSONUtil.toList(relatedOrders, RelateOrdersTaoTianDTO.class);
        if (CollectionUtils.isEmpty(relatedOrders)) {
            return;
        }
        //跨货主调拨入库
        RelateOrdersTaoTianDTO relateOrder = relateOrders.stream().filter(a -> a.getOrderType().equals("KHZCK")).findFirst().orElse(null);
        if (relateOrder != null) {
            poNo = relateOrder.getOrderCode();
        }
        //获取出库单信息发货数据批次

        SkuLotDTO skuLotDTONew = queryKHZRKBySkuCode(realWarehouseCode, poNo, skuLotDTO);
        if (skuLotDTONew == null) {
            log.error("handKHZRKSource skuLotDTONew empty:{}", skuLotDTO.getCode() + ":" + skuLotDTO.getWarehouseCode());
            return;
        }
        //数据源还回去
        RpcContextUtil.setWarehouseCode(skuLotDTO.getWarehouseCode());

        SkuLotSourceExtraJsonDTO skuLotSourceExtraJsonDTO = JSONUtil.toBean(skuLotDTONew.getExtraJson(), SkuLotSourceExtraJsonDTO.class);
        skuLotSourceExtraJsonDTO.setFindSource("OK");
        skuLotDTO.setExtraJson(JSONUtil.toJsonStr(skuLotSourceExtraJsonDTO));
        iSkuLotClient.modifyBatch(Arrays.asList(skuLotDTO));
    }

    /**
     * @param realWarehouseCode
     * @param poNo
     * @param skuLotDTO
     * @return com.dt.domain.base.dto.SkuLotDTO
     * <AUTHOR>
     * @describe:
     * @date 2024/3/26 16:43
     */
    private SkuLotDTO queryKHZRKBySkuCode(String realWarehouseCode, String poNo, SkuLotDTO skuLotDTO) {
        //设置数据源
        RpcContextUtil.setWarehouseCode(realWarehouseCode);

        PackageParam packageParam = new PackageParam();
        packageParam.setPoNo(poNo);
        packageParam.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            if (!System.getenv("SPRING_PROFILES_ACTIVE").equalsIgnoreCase("prod")) {
                log.error("补充溯源码KHZR信息失败,失败原因:{}", "跨货主调拨入库,找不到对应的出库的包裹:" + realWarehouseCode + ":" + poNo);
                return null;
            } else {
                try {
                    if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                        String message = String.format("[补充溯源信息KHZR]仓库:%s,单号:%s,批次ID:%s", skuLotDTO.getWarehouseCode(), poNo, skuLotDTO.getCode());
                        WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                    }
                } catch (Exception e) {
                    //..ex
                }
                throw new RuntimeException("跨货主调拨入库,找不到对应的出库的包裹:" + realWarehouseCode + ":" + poNo);
            }
        }
        //获取所有的分配数据记录
        AllocationOrderParam allocationOrderParam = new AllocationOrderParam();
        allocationOrderParam.setSkuCode(skuLotDTO.getSkuCode());
        allocationOrderParam.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList()));
        allocationOrderParam.setWaveCodeList(packageDTOList.stream().map(PackageDTO::getWaveCode).distinct().collect(Collectors.toList()));
        List<AllocationOrderDTO> allocationOrderDTOFindList = remoteAllocationOrderClient.getList(allocationOrderParam);
        if (CollectionUtils.isEmpty(allocationOrderDTOFindList)) {
            if (!System.getenv("SPRING_PROFILES_ACTIVE").equalsIgnoreCase("prod")) {
                log.error("补充溯源码KHZR信息失败,失败原因:{}", "跨货主调拨入库,找不到对应的出库的包裹分配记录::" + realWarehouseCode + ":" + poNo);
                return null;
            } else {
                try {
                    if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                        String message = String.format("[补充溯源信息KHZR]仓库:%s,单号:%s,批次ID:%s", skuLotDTO.getWarehouseCode(), poNo, skuLotDTO.getCode());
                        WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                    }
                } catch (Exception e) {
                    //..ex
                }
                throw new RuntimeException("跨货主调拨入库,找不到对应的出库的包裹分配记录::" + realWarehouseCode + ":" + poNo);
            }
        }
        List<AllocationOrderDTO> allocationOrderDTOListNew = new ArrayList<>();
        for (PackageDTO packageDTO : packageDTOList) {
            List<AllocationOrderDTO> orderDTOS = allocationOrderDTOFindList.stream()
                    .filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode()))
                    .filter(a -> a.getWaveCode().equals(packageDTO.getWaveCode())).collect(Collectors.toList());
            allocationOrderDTOListNew.addAll(orderDTOS);
        }
        AllocationOrderDTO allocationOrderDTO = allocationOrderDTOListNew.get(0);
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setCode(allocationOrderDTO.getSkuLotNo());
        SkuLotDTO skuLotDTOOrigin = iSkuLotClient.get(skuLotParam).getData();
        if (skuLotDTOOrigin == null) {
            if (!System.getenv("SPRING_PROFILES_ACTIVE").equalsIgnoreCase("prod")) {
                log.error("补充溯源码KHZR信息失败,失败原因:{}", "跨货主调拨入库,找不到对应的出库的包裹分配记录批次:" + realWarehouseCode + ":" + poNo);
                return null;
            } else {
                try {
                    if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                        String message = String.format("[补充溯源信息KHZR]仓库:%s,单号:%s,批次ID:%s", skuLotDTO.getWarehouseCode(), poNo, skuLotDTO.getCode());
                        WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                    }
                } catch (Exception e) {
                    //..ex
                }
                throw new RuntimeException("跨货主调拨入库,找不到对应的出库的包裹分配记录批次:" + realWarehouseCode + ":" + poNo);
            }
        }
        return skuLotDTOOrigin;
    }

    /**
     * @param skuLotDTO
     * @param asnDTO
     * @return void
     * <AUTHOR>
     * @describe: 补差入库补充溯源信息
     * 补差入库：
     * 1. 当前补差入库为前序采购单的多货，当前补差入库和前序采购入库的清关溯源信息一致； 前序的采购的入库单和清关单可以通过关联单据信息来关联
     * 2. 补差入库的relatedOrders中单据类型为【CG】的单据号orderCode（实际为前序采购单的淘天入库单号，也就是wms的上游单号），在本仓内查找上游单号是此单号的入库单，再通过入库单找清关单的方式找到溯源信息，或者直接在入库单的收货作业批次中查询到
     * @date 2024/3/26 15:56
     */
    private void handBCRKSource(SkuLotDTO skuLotDTO, AsnDTO asnDTO) {
        //获取关联清关单号
        JSONObject jsonObject = JSONUtil.parseObj(asnDTO.getExtraJson());
        if (!jsonObject.containsKey("relatedOrders") || StringUtils.isEmpty(jsonObject.getStr("relatedOrders"))
                || !JSONUtil.isJsonArray(jsonObject.getStr("relatedOrders"))) {
            return;
        }
        JSONArray relatedOrders = JSONUtil.parseArray(jsonObject.getStr("relatedOrders"));
        List<RelateOrdersTaoTianDTO> relateOrders = JSONUtil.toList(relatedOrders, RelateOrdersTaoTianDTO.class);
        if (CollectionUtils.isEmpty(relatedOrders)) {
            return;
        }
        String poNo = "";
        //补差入库
        RelateOrdersTaoTianDTO relateOrder = relateOrders.stream().filter(a -> a.getOrderType().equals("CG")).findFirst().orElse(null);
        if (relateOrder != null) {
            poNo = relateOrder.getOrderCode();
        }
        if (StringUtils.isEmpty(poNo)) {
            return;
        }
        SkuLotParam skuLotParam = new SkuLotParam();
        skuLotParam.setSkuCode(skuLotDTO.getSkuCode());
        skuLotParam.setExternalLinkBillNo(poNo);
        skuLotParam.setCargoCode(skuLotDTO.getCargoCode());
        List<SkuLotDTO> skuLotDTOList = iSkuLotClient.getList(skuLotParam).getData();
        if (CollectionUtils.isEmpty(skuLotDTOList)) {
            log.error("handBCRKSource empty:{}", skuLotDTO.getCode() + ":" + skuLotDTO.getWarehouseCode());
            try {
                if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                    String message = String.format("[补充溯源信息BCRK]仓库:%s,单号:%s,批次ID:%s", skuLotDTO.getWarehouseCode(), poNo, skuLotDTO.getCode());
                    WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                }
            } catch (Exception e) {
                //..ex
            }
            return;
        }
        skuLotDTOList = skuLotDTOList.stream().filter(a -> !StringUtils.isEmpty(a.getExtraJson())
                && a.getExtraJson().contains("skuLotSourceDTO")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuLotDTOList)) {
            log.error("handBCRKSource skuLotSourceDTO empty:{}", skuLotDTO.getCode() + ":" + skuLotDTO.getWarehouseCode());
            try {
                if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                    String message = String.format("[补充溯源信息BCRK]仓库:%s,单号:%s,批次ID:%s", skuLotDTO.getWarehouseCode(), poNo, skuLotDTO.getCode());
                    WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                }
            } catch (Exception e) {
                //..ex
            }
            return;
        }
        SkuLotDTO lotDTO = skuLotDTOList.get(0);
        //
        SkuLotSourceExtraJsonDTO skuLotSourceExtraJsonDTO = JSONUtil.toBean(lotDTO.getExtraJson(), SkuLotSourceExtraJsonDTO.class);
        skuLotSourceExtraJsonDTO.setFindSource("OK");
        skuLotDTO.setExtraJson(JSONUtil.toJsonStr(skuLotSourceExtraJsonDTO));
        iSkuLotClient.modifyBatch(Arrays.asList(skuLotDTO));
    }

    /**
     * @param skuLotDTO
     * @param asnDTO
     * @return void
     * <AUTHOR>
     * @describe: 采购入库补充溯源信息
     * 采购入库：
     * 同一个采购入的单子，wms的仓储入库单和ccs的清关单中，可以通过ToB履行单关联识别为同一票的清关和入库；
     * 通过仓储入库单中relatedOrders中，orderType为TOB_FULFILL的orderCode，去查询清关单中相同的orderCode的清关单上的溯源码信息
     * CCS提供通过ToB履行单号查询清关单上溯源信息的接口，并按要求进行国家码的转换
     * @date 2024/3/26 15:56
     */
    private void handCGRKSource(SkuLotDTO skuLotDTO, AsnDTO asnDTO) {
        //获取关联清关单号
        String orderCode = "";
        JSONObject jsonObject = JSONUtil.parseObj(asnDTO.getExtraJson());
        if (!jsonObject.containsKey("relatedOrders") || StringUtils.isEmpty(jsonObject.getStr("relatedOrders"))
                || !JSONUtil.isJsonArray(jsonObject.getStr("relatedOrders"))) {
            return;
        }
        JSONArray relatedOrders = JSONUtil.parseArray(jsonObject.getStr("relatedOrders"));
        List<RelateOrdersTaoTianDTO> relateOrders = JSONUtil.toList(relatedOrders, RelateOrdersTaoTianDTO.class);
        if (CollectionUtils.isEmpty(relatedOrders)) {
            return;
        }
        //采购入库
        if (Objects.equals(asnDTO.getOutType(), AsnOutTypeEnum.CGRK.getCode())) {
            RelateOrdersTaoTianDTO relateOrder = relateOrders.stream().filter(a -> a.getOrderType().equals("TOB_FULFILL")).findFirst().orElse(null);
            if (relateOrder != null) {
                orderCode = relateOrder.getOrderCode();
            }
        }
        if (StringUtils.isEmpty(orderCode)) {
            return;
        }
        //清关单号
        remoteTenantHelper.setTenantId(asnDTO.getWarehouseCode(), asnDTO.getCargoCode());
        RpcResult<List<TaotianLogisticsTraceInfoResVo>> rpcResult = inventoryOrderRpc.getTaotianLogisticsTraceInfoV2(orderCode);
        log.info("handCGRKSource-result:{} {}", orderCode, JSONUtil.toJsonStr(rpcResult));
        if (!Objects.equals(rpcResult.getCode(), 200)) {
            if (!System.getenv("SPRING_PROFILES_ACTIVE").equalsIgnoreCase("prod")) {
                log.error(String.format("补充溯源码信息失败，失败原因：[%s] ，所属仓库：[%s] ，单据号：[%s]", rpcResult.getMessage(), skuLotDTO.getWarehouseCode(), orderCode + ":" + skuLotDTO.getCode()));
                return;
            }
            try {
                if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                    String message = String.format("[补充溯源信息]仓库:%s,单号:%s,批次ID:%s", skuLotDTO.getWarehouseCode(), orderCode, skuLotDTO.getCode());
                    WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                }
            } catch (Exception e) {
                //..ex
            }
            throw new RuntimeException("补充溯源码信息错误[采购入库]:" + skuLotDTO.getCode() + skuLotDTO.getWarehouseCode() + ":" + rpcResult.getMessage());
        }
        if (!StringUtils.isEmpty(rpcResult.getData())) {
            List<TaotianLogisticsTraceInfoResVo> logisticsTraceInfoResVoList = rpcResult.getData();
            TaotianLogisticsTraceInfoResVo logisticsTraceInfoResVo = logisticsTraceInfoResVoList.stream()
                    .filter(a -> Objects.equals(a.getItemCode(), skuLotDTO.getSkuCode())).findFirst().orElse(null);
            if (logisticsTraceInfoResVo == null) {
                log.info("logisticsTraceInfoResVo-error:{}", "补充溯源码信息错误empty[采购入库]:", skuLotDTO.getCode() + ":" + skuLotDTO.getSkuCode());
                try {
                    if (defaultWarehouseCodeConfig != null && !CollectionUtils.isEmpty(defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList())) {
                        String message = String.format("[补充溯源信息empty]仓库:%s,单号:%s,批次ID:%s", skuLotDTO.getWarehouseCode(), orderCode, skuLotDTO.getCode());
                        WechatUtil.sendMessage(message, defaultWarehouseCodeConfig.getNoticeWarmMessageUrlList());
                    }
                } catch (Exception e) {
                    //..ex
                }
                return;
            }
            SkuLotSourceExtraJsonDTO skuLotSourceExtraJsonDTO = new SkuLotSourceExtraJsonDTO();
            skuLotSourceExtraJsonDTO.setFindSource("OK");
            SkuLotSourceDTO skuLotSourceDTO = ConverterUtil.convert(logisticsTraceInfoResVo, SkuLotSourceDTO.class);
            skuLotSourceExtraJsonDTO.setSkuLotSourceDTO(skuLotSourceDTO);
            skuLotDTO.setExtraJson(JSONUtil.toJsonStr(skuLotSourceExtraJsonDTO));
            iSkuLotClient.modifyBatch(Arrays.asList(skuLotDTO));
        }
    }


}
