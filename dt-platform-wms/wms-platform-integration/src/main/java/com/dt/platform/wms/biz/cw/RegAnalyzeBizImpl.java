package com.dt.platform.wms.biz.cw;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.dt.component.common.enums.cw.CWCollectTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuUomDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.bill.dto.cw.RegAnalyzeDTO;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.integration.IRemoteCargoOwnerClient;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Slf4j
@Component
public class RegAnalyzeBizImpl implements IRegAnalyzeBiz {

    private static final String LEFT_BRACKET = "\\(";

    @Autowired
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Autowired
    private IRemoteSkuClient remoteSkuClient;

    @Override
    public RegAnalyzeDTO analyze(String barCode) {
        try {
            RegAnalyzeDTO regAnalyzeDTO1 = analyzeV2(barCode);
            if (null != regAnalyzeDTO1) {
                return regAnalyzeDTO1;
            }

            if (!isReg(barCode) && !isSerial(barCode)) {
                throw ExceptionUtil.exceptionWithMessage("解析失败");
            }

            CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.cwCargo();
            if (null == cargoOwnerDTO) {
                throw ExceptionUtil.exceptionWithMessage("没有配置CW货主无法解析托盘号和箱码");
            }

            RegAnalyzeDTO regAnalyzeDTO = new RegAnalyzeDTO();
            for (String sub : barCode.split(LEFT_BRACKET)) {
                if (sub.startsWith("00)")) {
                    regAnalyzeDTO.setReg(sub.substring(3).trim());
                    regAnalyzeDTO.setCollectType(CWCollectTypeEnum.SCAN_PALLET.getCode());
                    regAnalyzeDTO.setCollectTypeDesc(CWCollectTypeEnum.SCAN_PALLET.getMessage());
                }
                if (sub.startsWith("02")) {
                    regAnalyzeDTO.setUpc(sub.substring(3).trim());
                }
                if (sub.startsWith("11")) {
                    regAnalyzeDTO.setManufDateDesc(sub.substring(3).trim());
                    regAnalyzeDTO.setManufDate(parseCWDate(regAnalyzeDTO.getManufDateDesc()));
                }
                if (sub.startsWith("17")) {
                    regAnalyzeDTO.setExpireDateDesc(sub.substring(3).trim());
                    regAnalyzeDTO.setExpireDate(parseCWDate(regAnalyzeDTO.getExpireDateDesc()));
                }
                if (sub.startsWith("10")) {
                    regAnalyzeDTO.setLotNumber(sub.substring(3).trim());
                }
                if (sub.startsWith("37")) {
                    regAnalyzeDTO.setBoxCount(Integer.parseInt(sub.substring(3).trim()));
                }
                if (sub.startsWith("01")) {
                    regAnalyzeDTO.setUpc(sub.substring(3).trim());
                }
                if (sub.startsWith("21")) {
                    regAnalyzeDTO.setSerial(sub.substring(3).trim());
                    regAnalyzeDTO.setCollectType(CWCollectTypeEnum.SCAN_CARTON.getCode());
                    regAnalyzeDTO.setCollectTypeDesc(CWCollectTypeEnum.SCAN_CARTON.getMessage());
                    regAnalyzeDTO.setBoxCount(1);
                }
            }

            regAnalyzeDTO.setTotalCount(regAnalyzeDTO.getBoxCount());
            if (StrUtil.isNotBlank(regAnalyzeDTO.getUpc())) {
                SkuUpcParam skuUpcParam = new SkuUpcParam();
                skuUpcParam.setCargoCode(cargoOwnerDTO.getCode());
                skuUpcParam.setUpcCode(regAnalyzeDTO.getUpc());
                SkuUpcDTO skuUpc = remoteSkuClient.getSkuUpc(skuUpcParam);
                if (null == skuUpc) {
                    throw ExceptionUtil.exceptionWithMessage("CW货主对应条码信息不存在");
                }

                SkuUomDTO skuUomDTO = remoteSkuClient.querySkuUomBySkuCode(cargoOwnerDTO.getCode(), skuUpc.getSkuCode(), skuUpc.getPackageUnitCode());
                if (null == skuUomDTO) {
                    throw ExceptionUtil.exceptionWithMessage("CW货主对应包装单位不存在");
                }

                Integer currentCount = skuUomDTO.getPackageQty().multiply(new BigDecimal(regAnalyzeDTO.getBoxCount())).intValue();
                regAnalyzeDTO.setTotalCount(currentCount);
                regAnalyzeDTO.setSkuCode(skuUpc.getSkuCode());
            }
            regAnalyzeDTO.setCargoCode(cargoOwnerDTO.getCode());
            return regAnalyzeDTO;
        } catch (Exception exception) {
            if (exception instanceof BaseException) {
                throw exception;
            }else {
                throw ExceptionUtil.exceptionWithMessage("解析失败");
            }
        }

    }

    private RegAnalyzeDTO analyzeV2(String barCode) {
        try {

            if (barCode.contains("#")) {
                String[] parts = barCode.split("#+");
                if (parts.length < 3) {
                    return null;
                }
                String part = parts[1];
                if (part.length() < 18) {
                    return null;
                }
                String batchNo = part.substring(0, 6);
                String productDate = part.substring(6, 12);
                String expireDate = part.substring(12, 18);
                RegAnalyzeDTO regAnalyzeDTO = new RegAnalyzeDTO();
                regAnalyzeDTO.setLotNumber(batchNo);
                regAnalyzeDTO.setManufDate(parseCWDate(productDate));
                regAnalyzeDTO.setExpireDate(parseCWDate(expireDate));
                regAnalyzeDTO.setReg(parts[2]);
                return regAnalyzeDTO;
            }
            return null;
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            return null;
        }
    }

    @ApiOperation("是不是托盘号")
    private boolean isReg(String reg) {
        if (StrUtil.isBlank(reg)) return false;
        return reg.contains("(00)") && reg.contains("(37)");
    }

    @ApiOperation("是不是箱码")
    private boolean isSerial(String reg) {
        if (StrUtil.isBlank(reg)) return false;
        return reg.contains("(01)") && reg.contains("(21)");
    }

    private static Long parseCWDate(String dateStr) {
        String substring = String.valueOf(DateTime.now().year()).substring(0, 2);
        return DateUtil.parse(substring + dateStr, "yyyyMMdd").getTime();
    }
}

