package com.dt.platform.wms.biz.stock.core;

import cn.hutool.core.util.StrUtil;
import com.dt.component.common.enums.stock.OperationTypeEnum;
import com.dt.component.common.enums.stock.StockModuleVersionEnum;
import com.dt.component.common.enums.stock.StockTransactionStatusEnum;
import com.dt.component.common.enums.stock.TradeTypeEnum;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.dto.StockSerialDTO;
import com.dt.domain.core.stock.dto.StockTransactionDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
public class AllocationStockTransactionHelper {

    @Autowired
    private StockHelper stockHelper;

    /**
     * 汇单占用三级库存
     *
     * @param stockLocationDTO
     * @param stockTransactionDTO
     * @param stockSerialDTOList
     */
    public void allocation(StockLocationDTO stockLocationDTO, StockTransactionDTO stockTransactionDTO, List<StockSerialDTO> stockSerialDTOList) {
        if (StockModuleVersionEnum.V_FINANCE_REFUND_SHIPMENT.getCode().equals(stockTransactionDTO.getStockModuleVersion())) {
            allocationFrozen(stockLocationDTO, stockTransactionDTO, stockSerialDTOList);
        } else {
            allocationAvailable(stockLocationDTO, stockTransactionDTO, stockSerialDTOList);
        }
    }

    private void allocationAvailable(StockLocationDTO stockLocationDTO, StockTransactionDTO stockTransactionDTO, List<StockSerialDTO> stockSerialDTOList) {

        BigDecimal needQty = stockTransactionDTO.getChangeQty().abs().subtract(stockTransactionDTO.getDoneQty());
        BigDecimal processAbleQuantity = stockLocationDTO.getAvailableQty();
        BigDecimal processQty;

        if (processAbleQuantity.compareTo(needQty) < 0) {
            processQty = processAbleQuantity;
            stockTransactionDTO.setStatus(StockTransactionStatusEnum.FAIL.getCode());
        } else {
            processQty = needQty;
            stockTransactionDTO.setStatus(StockTransactionStatusEnum.DONE.getCode());
        }

        stockLocationDTO.setAvailableQty(stockLocationDTO.getAvailableQty().subtract(processQty));
        stockLocationDTO.setOccupyQty(stockLocationDTO.getOccupyQty().add(processQty));

        stockTransactionDTO.setDoneQty(stockTransactionDTO.getDoneQty().add(processQty));

        if (processQty.compareTo(BigDecimal.ZERO) > 0) {
            StockSerialDTO stockSerialDTO = stockHelper.convertFromTransaction(stockTransactionDTO);
            stockSerialDTO.setAvailableChange(processQty.negate());
            stockSerialDTO.setOccupyChange(processQty);
            stockSerialDTOList.add(stockSerialDTO);
        }
    }

    private void allocationFrozen(StockLocationDTO stockLocationDTO, StockTransactionDTO stockTransactionDTO, List<StockSerialDTO> stockSerialDTOList) {

        BigDecimal needQty = stockTransactionDTO.getChangeQty().abs().subtract(stockTransactionDTO.getDoneQty());
        BigDecimal processAbleQuantity = stockLocationDTO.getFrozenQty();
        BigDecimal processQty;

        if (processAbleQuantity.compareTo(needQty) < 0) {
            processQty = processAbleQuantity;
            stockTransactionDTO.setStatus(StockTransactionStatusEnum.FAIL.getCode());
        } else {
            processQty = needQty;
            stockTransactionDTO.setStatus(StockTransactionStatusEnum.DONE.getCode());
        }

        stockLocationDTO.setFrozenQty(stockLocationDTO.getFrozenQty().subtract(processQty));
        stockLocationDTO.setOccupyQty(stockLocationDTO.getOccupyQty().add(processQty));

        stockTransactionDTO.setDoneQty(stockTransactionDTO.getDoneQty().add(processQty));

        if (processQty.compareTo(BigDecimal.ZERO) > 0) {
            StockSerialDTO stockSerial = stockHelper.convertFromTransaction(stockTransactionDTO);
            stockSerial.setFrozenChange(processQty.negate());
            stockSerial.setOccupyChange(processQty);
            stockSerialDTOList.add(stockSerial);
        }
    }

    /**
     * 取消汇单占用三级库存
     *
     * @param stockLocationDTO
     * @param stockTransactionDTO
     * @param stockSerialDTOList
     */
    public void cancelAllocation(StockLocationDTO stockLocationDTO, StockTransactionDTO stockTransactionDTO, List<StockSerialDTO> stockSerialDTOList, String customerTradeType, String customerOperationType) {
        if (StockModuleVersionEnum.V_FINANCE_REFUND_SHIPMENT.getCode().equals(stockTransactionDTO.getStockModuleVersion())) {
            cancelAllocationFrozen(stockLocationDTO, stockTransactionDTO, stockSerialDTOList, customerTradeType, customerOperationType);
        } else {
            cancelAllocationAvailable(stockLocationDTO, stockTransactionDTO, stockSerialDTOList );
        }
    }

    private void cancelAllocationAvailable(StockLocationDTO stockLocationDTO, StockTransactionDTO stockTransactionDTO, List<StockSerialDTO> stockSerialDTOList) {
        String customerTradeType = TradeTypeEnum.TRADE_TYPE_CANCEL_ALLOCATION.getType();
        String customerOperationType = OperationTypeEnum.OPERATION_CANCEL_ALLOCATION.getType();
        OperationTypeEnum operationTypeEnum = OperationTypeEnum.getEnum(stockTransactionDTO.getOperationType());
        if (operationTypeEnum.equals(OperationTypeEnum.OPERATION_SHIPMENT)) {
            customerTradeType = TradeTypeEnum.TRADE_TYPE_CANEL_SHIPMENT.getType();
            customerOperationType = OperationTypeEnum.OPERATION_CANCEL_SHIPMENT.getType();
        }

        BigDecimal needQty = stockTransactionDTO.getDoneQty();
        BigDecimal processAbleQuantity = stockLocationDTO.getOccupyQty();
        BigDecimal processQty;

        if (processAbleQuantity.compareTo(needQty) < 0) {
            processQty = processAbleQuantity;
            stockTransactionDTO.setStatus(StockTransactionStatusEnum.CANCEL_ONGOING.getCode());
        } else {
            processQty = needQty;
            stockTransactionDTO.setStatus(StockTransactionStatusEnum.CANCELLED.getCode());
        }

        stockLocationDTO.setAvailableQty(stockLocationDTO.getAvailableQty().add(processQty));
        stockLocationDTO.setOccupyQty(stockLocationDTO.getOccupyQty().subtract(processQty));

        stockTransactionDTO.setDoneQty(stockTransactionDTO.getDoneQty().subtract(processQty));

        if (processQty.compareTo(BigDecimal.ZERO) > 0) {
            StockSerialDTO stockSerial = stockHelper.convertFromTransaction(stockTransactionDTO);
            stockSerial.setTradeTime(System.currentTimeMillis());
            stockSerial.setCreatedTime(System.currentTimeMillis());
            stockSerial.setAvailableChange(processQty);
            stockSerial.setOccupyChange(processQty.negate());
            stockSerial.setTradeType(customerTradeType);
            stockSerial.setOperationType(customerOperationType);
            stockSerialDTOList.add(stockSerial);
        }
    }

    private void cancelAllocationFrozen(StockLocationDTO stockLocationDTO, StockTransactionDTO stockTransactionDTO, List<StockSerialDTO> stockSerialDTOList, String customerTradeType, String customerOperationType) {
        if (StrUtil.isBlank(customerTradeType)) {
            customerTradeType = TradeTypeEnum.TRADE_TYPE_CANCEL_ALLOCATION.getType();
        }
        if (StrUtil.isBlank(customerOperationType)) {
            customerOperationType = OperationTypeEnum.OPERATION_CANCEL_ALLOCATION.getType();
        }

        BigDecimal needQty = stockTransactionDTO.getDoneQty();
        BigDecimal processAbleQuantity = stockLocationDTO.getOccupyQty();
        BigDecimal processQty;

        if (processAbleQuantity.compareTo(needQty) < 0) {
            processQty = processAbleQuantity;
            stockTransactionDTO.setStatus(StockTransactionStatusEnum.CANCEL_ONGOING.getCode());
        } else {
            processQty = needQty;
            stockTransactionDTO.setStatus(StockTransactionStatusEnum.CANCELLED.getCode());
        }

        stockLocationDTO.setFrozenQty(stockLocationDTO.getFrozenQty().add(processQty));
        stockLocationDTO.setOccupyQty(stockLocationDTO.getOccupyQty().subtract(processQty));

        stockTransactionDTO.setDoneQty(stockTransactionDTO.getDoneQty().subtract(processQty));

        if (processQty.compareTo(BigDecimal.ZERO) > 0) {
            StockSerialDTO stockSerial = stockHelper.convertFromTransaction(stockTransactionDTO);
            stockSerial.setTradeTime(System.currentTimeMillis());
            stockSerial.setCreatedTime(System.currentTimeMillis());
            stockSerial.setFrozenChange(processQty);
            stockSerial.setOccupyChange(processQty.negate());
            stockSerial.setTradeType(customerTradeType);
            stockSerial.setOperationType(customerOperationType);
            stockSerialDTOList.add(stockSerial);
        }
    }
}