package com.dt.platform.wms.integration;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.ZoneDTO;
import com.dt.domain.base.param.ZoneBatchParam;
import com.dt.domain.base.param.ZoneParam;
import com.dt.platform.wms.biz.dto.PhysicalPartitionBindDTO;

import java.util.List;

public interface IRemoteZoneClient {

    /**
     * 新增库区信息
     *
     * @param param
     * @return
     */
    Boolean save(ZoneParam param);

    Boolean saveBatch(ZoneBatchParam param);

    /**
     * 修改库区信息
     * ID | Code | idList | codeList 四选一
     *
     * @param param
     * @return
     */
    Boolean modify(ZoneParam param);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Boolean checkExits(ZoneParam param);

    /**
     * 获取库区信息
     *
     * @param param
     * @return
     */
    ZoneDTO get(ZoneParam param);

    /**
     * 获取库区列表
     *
     * @param param
     * @return
     */
    List<ZoneDTO> getList(ZoneParam param);

    /**
     * 功能描述:  校验数据是否被禁用
     * 创建时间:  2020/12/18 9:01 上午
     *
     * @param zoneDTOList:
     * @return void
     * <AUTHOR>
     */
    void checkStatus(List<ZoneDTO> zoneDTOList);

    /**
     * 分页获取库区
     *
     * @param param
     * @return
     */
    Page<ZoneDTO> getPage(ZoneParam param);

    /**
     * 获取暂存区的库区
     *
     * @param type
     * @param skuQuality
     * @return
     */
    ZoneDTO queryTemporaryByTypeAndSkuQuality(String type, String skuQuality);

    /**
     * 获取库区
     *
     * @param type
     * @return
     */
    List<ZoneDTO> queryZoneByType(String type);

    List<IdNameVO> getAllZoneList();

    /**
     * 获取拣选和暂存区
     * @return
     */
    List<IdNameVO> getZonePickAndStorageList();
    /**
     * @param warehouseCode
     * @param zoneCodeList
     * @return java.util.List<com.dt.platform.wms.biz.dto.PhysicalPartitionBindDTO>
     * <AUTHOR>
     * @describe:
     * @date 2023/4/20 17:41
     */
    List<PhysicalPartitionBindDTO> getPhysicalPartitionList(String warehouseCode);
    List<PhysicalPartitionBindDTO> getPhysicalPartitionList(String warehouseCode,List<String> zoneCodeList);
}
