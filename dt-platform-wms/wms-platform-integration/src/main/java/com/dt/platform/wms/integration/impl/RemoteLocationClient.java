package com.dt.platform.wms.integration.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.*;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.client.ILocationClient;
import com.dt.domain.base.dto.LocationDTO;
import com.dt.domain.base.dto.TunnelDTO;
import com.dt.domain.base.dto.ZoneDTO;
import com.dt.domain.base.param.LocationBatchParam;
import com.dt.domain.base.param.LocationParam;
import com.dt.domain.base.param.TunnelParam;
import com.dt.domain.base.param.ZoneParam;
import com.dt.platform.wms.integration.IRemoteLocationClient;
import com.dt.platform.wms.integration.IRemoteTunnelClient;
import com.dt.platform.wms.integration.IRemoteZoneClient;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class RemoteLocationClient implements IRemoteLocationClient {

    @DubboReference(timeout = 60000)
    private ILocationClient locationClient;

    @Resource
    private IRemoteZoneClient iRemoteZoneClient;

    @Resource
    private IRemoteTunnelClient iRemoteTunnelClient;

    @Override
    public Boolean save(LocationParam param) {
        return locationClient.save(param).getData();
    }

    @Override
    public Boolean saveBatch(LocationBatchParam param) {
        return locationClient.saveBatch(param).getData();
    }
    @Override
    public Boolean updateBatch(LocationBatchParam param) {
        return locationClient.updateBatch(param).getData();
    }
    @Override
    public Boolean modify(LocationParam param) {
        return locationClient.modify(param).getData();
    }

    @Override
    public Boolean checkExits(LocationParam param) {
        return locationClient.checkExits(param).getData();
    }

    @Override
    public LocationDTO get(LocationParam param) {
        return locationClient.get(param).getData();
    }

    @Override
    public List<LocationDTO> getList(LocationParam param) {
        return locationClient.getList(param).getData();
    }

    @Override
    public void checkStatus(List<LocationDTO> locationDTOS) {
        for (LocationDTO locationDTO : locationDTOS) {
            if (Objects.equals(locationDTO.getStatus(), LocationStatusEnum.STATUS_DISABLED.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("库位:%s被禁用，不允许使用该库位。", locationDTO.getCode()));
            }
        }
    }

    @Override
    public Page<LocationDTO> getPage(LocationParam param) {
        return locationClient.getPage(param).getData();
    }

    @Override
    public LocationDTO queryTemporaryByType(String type, String zoneCode) {
        LocationParam param = new LocationParam();
        param.setType(type);
        param.setZoneCode(zoneCode);
        List<LocationDTO> locationDTOS = locationClient.getList(param).getData();
        if (CollectionUtils.isEmpty(locationDTOS)) {
            return null;
        }
        //有多个默认区第一个
        return locationDTOS.get(0);
    }

    @Override
    public List<LocationDTO> queryLocationByType(List<String> typeList) {
        LocationParam param = new LocationParam();
        param.setTypeList(typeList);
        return locationClient.getList(param).getData();
    }

    @Override
    public LocationDTO queryLocationByCode(String locationCode) {
        LocationParam param = new LocationParam();
        param.setCode(locationCode);
        return locationClient.get(param).getData();
    }

    @Override
    public List<IdNameVO> getAllLocationList() {
        return locationClient.getAllLocationList().getData();
    }


}
