package com.danding.exporter.excel.exporter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.exporter.database.pack.entity.Package;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.danding.exporter.database.pack.mapper.PackageMapper;
import com.danding.exporter.database.pack.util.PackageUtil;
import com.danding.exporter.database.pick.entity.Pick;
import com.danding.exporter.database.pick.entity.PickDetail;
import com.danding.exporter.database.pick.mapper.PickDetailMapper;
import com.danding.exporter.database.pick.mapper.PickMapper;
import com.danding.exporter.database.shipment.entity.ShipmentOrder;
import com.danding.exporter.database.shipment.entity.ShipmentOrderMaterial;
import com.danding.exporter.database.shipment.mapper.ShipmentOrderMapper;
import com.danding.exporter.database.shipment.mapper.ShipmentOrderMaterialMapper;
import com.danding.exporter.database.sku.mapper.SkuMapper;
import com.danding.exporter.excel.ExcelExportEnum;
import com.danding.exporter.excel.bo.pack.ExcelExportPackageBO;
import com.danding.exporter.pack.dto.PackageQry;
import com.danding.exporter.rpc.*;
import com.dt.component.common.enums.bill.*;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.CarrierParam;
import com.dt.domain.base.param.SalePlatformParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.platform.utils.LambdaHelpUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-09 10:56
 */
@Component("packAnalysisRowDataOrderExporter")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PackAnalysisRowDataOrderExporter extends AbstractDefaultExporter<Package, ExcelExportPackageBO> {

    @Resource
    private PackageMapper packageMapper;

    @Resource
    private PackageUtil packageUtil;

    @Resource
    private CargoOwnerRpcMapper cargoOwnerRpcMapper;

    @Resource
    private SkuLotRpcMapper skuLotRpcMapper;

    @Resource
    private SkuMapper skuMapper;
    @Resource
    private SkuUpcRpcMapper skuUpcRpcMapper;

    @Resource
    private SalePlatformRpcMapper salePlatformRpcMapper;

    @Resource
    private ShipmentOrderMapper shipmentOrderMapper;

    @Resource
    private PickMapper pickMapper;

    @Resource
    private AreaRpcMapper areaRpcMapper;

    @Resource
    private PickDetailMapper pickDetailMapper;

    @Resource
    private ShipmentOrderMaterialMapper shipmentOrderMaterialMapper;

    @Resource
    private CarrierRpcMapper carrierRpcMapper;

    private static final String EXPORT_TIME_FORMAT = "yyyy/MM/dd HH:mm:ss";


    @Override
    public CommonExcelHandler<Package, ExcelExportPackageBO> createHandler() {
        return packageList -> {
            if (CollectionUtils.isEmpty(packageList)) {
                return Lists.newArrayList();
            }
            List<String> cargoCodeList = packageList.stream().map(Package::getCargoCode).distinct().collect(Collectors.toList());
            Map<String, CargoOwnerDTO> cargoOwnerDTOMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(cargoCodeList)) {
                CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
                cargoOwnerParam.setCodeList(cargoCodeList);
                cargoOwnerDTOMap.putAll(cargoOwnerRpcMapper.getList(cargoOwnerParam).stream().collect(Collectors.toMap(it -> StrUtil.join(StrUtil.COLON, it.getWarehouseCode(), it.getCode()), Function.identity())));
            }

            // 承运商
            List<String> carrierCodeList = packageList.stream().map(Package::getCarrierCode).distinct().collect(Collectors.toList());
            CarrierParam carrierParam = new CarrierParam();
            carrierParam.setCodeList(carrierCodeList);
            List<CarrierDTO> carrierDTOList = carrierRpcMapper.getList(carrierParam);
            Map<String, CarrierDTO> carrierDTOMap = carrierDTOList.stream().collect(Collectors.toMap(it -> it.getCode().toLowerCase(), Function.identity()));

            SalePlatformParam salePlatformParam = new SalePlatformParam();
            List<SalePlatformDTO> salePlatformDTOList = salePlatformRpcMapper.getList(salePlatformParam);
            Map<String, SalePlatformDTO> salePlatformDTOMap = salePlatformDTOList.stream().collect(Collectors.toMap(it -> it.getCode().toLowerCase(), Function.identity()));
            //获取出库单信息
            List<String> shipmentOrderCodeList = packageList.stream().map(com.danding.exporter.database.pack.entity.Package::getShipmentOrderCode).distinct().collect(Collectors.toList());
            List<ShipmentOrder> shipmentOrderDTOList = shipmentOrderMapper.selectList(new QueryWrapper<ShipmentOrder>().lambda()
                    .select(ShipmentOrder::getRemark, ShipmentOrder::getReceiverAreaName, ShipmentOrder::getTradeNo, ShipmentOrder::getReceiverCityName,
                            ShipmentOrder::getSaleShop, ShipmentOrder::getReceiverProvName, ShipmentOrder::getPreSaleType, ShipmentOrder::getCreatedTime,
                            ShipmentOrder::getOrderType, ShipmentOrder::getExpShipTime, ShipmentOrder::getShipmentOrderCode)
                    .in(ShipmentOrder::getShipmentOrderCode, shipmentOrderCodeList));
            Map<String, ShipmentOrder> shipmentOrderDTOMap = shipmentOrderDTOList.stream().collect(Collectors.toMap(ShipmentOrder::getShipmentOrderCode, Function.identity()));
            //获取包材信息
            List<ShipmentOrderMaterial> shipmentOrderMaterialDTOList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(shipmentOrderDTOList)) {
                shipmentOrderMaterialDTOList = shipmentOrderMaterialMapper.selectList(new QueryWrapper<ShipmentOrderMaterial>().lambda()
                        .select(ShipmentOrderMaterial::getShipmentOrderCode, ShipmentOrderMaterial::getRecPackUpcCode).in(ShipmentOrderMaterial::getShipmentOrderCode, shipmentOrderCodeList));
                if (CollectionUtils.isEmpty(shipmentOrderMaterialDTOList)) {
                    shipmentOrderMaterialDTOList = new ArrayList<>();
                }
            }
            Map<String, List<ShipmentOrderMaterial>> shipmentOrderMaterialDTOMap = shipmentOrderMaterialDTOList.stream().collect(Collectors.groupingBy(ShipmentOrderMaterial::getShipmentOrderCode));
            //拣选单号和面单打印次数
            List<PickDetail> pickDetailDTOList = pickDetailMapper.selectList(new QueryWrapper<PickDetail>().lambda()
                    .select(PickDetail::getPickCode, PickDetail::getPackageCode, PickDetail::getPackageStatus)
                    .in(PickDetail::getPackageCode, packageList.stream().map(packageDTO -> packageDTO.getPackageCode()).distinct().collect(Collectors.toList()))
                    .eq(PickDetail::getFlag, PickEnum.PickDetailFlagEnum.ORIGIN.getCode()));
            List<Pick> pickDTOList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(pickDetailDTOList)) {
                pickDTOList = pickMapper.selectList(new QueryWrapper<Pick>().lambda()
                        .select(Pick::getPickCode, Pick::getStatus).in(Pick::getPickCode, pickDetailDTOList.stream().map(pickDetailDTO -> pickDetailDTO.getPickCode()).distinct().collect(Collectors.toList())));
                if (CollectionUtils.isEmpty(pickDTOList)) {
                    pickDTOList = new ArrayList<>();
                }
            }
            Map<String, List<PickDetail>> pickDetailMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(pickDetailDTOList)) {
                pickDetailMap = pickDetailDTOList.stream().collect(Collectors.groupingBy(PickDetail::getPackageCode));
            }
            Map<String, List<PickDetail>> finalPickDetailMap = pickDetailMap;
            List<Pick> finalPickDTOList = pickDTOList;
            List<ExcelExportPackageBO> excelExportPackageBOList = new ArrayList<>();
            packageList.stream().forEach(packageDTO -> {
                ExcelExportPackageBO excelExportPackageBO = com.dt.platform.utils.ConverterUtil.convert(packageDTO, ExcelExportPackageBO.class);
                ShipmentOrder shipmentOrderDTO = shipmentOrderDTOMap.get(packageDTO.getShipmentOrderCode());
                if (shipmentOrderDTO == null) {
                    excelExportPackageBO.setRemark("");
                    excelExportPackageBO.setReceiverAreaName("");
                    excelExportPackageBO.setReceiverCityName("");
                    excelExportPackageBO.setReceiverProvName("");
                    excelExportPackageBO.setTradeNo("");
                    excelExportPackageBO.setSaleShop("");
                    excelExportPackageBO.setOrdCreateTime("");
                    excelExportPackageBO.setPreSaleTypeName("");
                    excelExportPackageBO.setExpShipTimeDate("");
                    excelExportPackageBO.setTradeType("");
                    excelExportPackageBO.setPackageMaterial("");
                } else {
                    excelExportPackageBO.setRemark(shipmentOrderDTO.getRemark());
                    excelExportPackageBO.setReceiverAreaName(shipmentOrderDTO.getReceiverAreaName());
                    excelExportPackageBO.setReceiverCityName(shipmentOrderDTO.getReceiverCityName());
                    excelExportPackageBO.setReceiverProvName(shipmentOrderDTO.getReceiverProvName());
                    excelExportPackageBO.setTradeNo(shipmentOrderDTO.getTradeNo());
                    excelExportPackageBO.setSaleShop(shipmentOrderDTO.getSaleShop());
                    excelExportPackageBO.setOrdCreateTime(com.dt.platform.utils.ConverterUtil.convertVoTime(shipmentOrderDTO.getCreatedTime(), EXPORT_TIME_FORMAT));
                    if (!StringUtils.isEmpty(shipmentOrderDTO.getPreSaleType())) {
                        excelExportPackageBO.setPreSaleTypeName(ShipmentPreSaleTypeEnum.fromCode(shipmentOrderDTO.getPreSaleType()).getMessage());
                    } else {
                        excelExportPackageBO.setPreSaleTypeName("");
                    }
                    excelExportPackageBO.setExpShipTimeDate(com.dt.platform.utils.ConverterUtil.convertVoTime(shipmentOrderDTO.getExpShipTime() == null ? 0L : shipmentOrderDTO.getExpShipTime(), EXPORT_TIME_FORMAT));
                    excelExportPackageBO.setTradeType(ShipmentOrderEnum.ORDER_TYPE.findEnumDesc(shipmentOrderDTO.getOrderType()).getDesc());
                    if (!CollectionUtils.isEmpty(shipmentOrderMaterialDTOMap) && shipmentOrderMaterialDTOMap.containsKey(shipmentOrderDTO.getShipmentOrderCode())) {
                        excelExportPackageBO.setPackageMaterial(shipmentOrderMaterialDTOMap.get(shipmentOrderDTO.getShipmentOrderCode()).stream().map(shipmentOrderMaterialDTO -> shipmentOrderMaterialDTO.getRecPackUpcCode()).collect(Collectors.joining(",")));
                    }
                }
                if (!StringUtils.isEmpty(packageDTO.getIsPre())) {
                    excelExportPackageBO.setPackageType(PackEnum.TYPE.findEnumDesc(packageDTO.getIsPre()).getDesc());
                } else {
                    excelExportPackageBO.setPackageType(PackEnum.TYPE.NORMAL.getCode());
                }
                excelExportPackageBO.setPickCode("");
                if (!CollectionUtils.isEmpty(finalPickDetailMap) && finalPickDetailMap.containsKey(packageDTO.getPackageCode())) {
                    List<PickDetail> detailDTOList = finalPickDetailMap.get(packageDTO.getPackageCode());
                    List<String> pickCodeList = detailDTOList.stream().map(PickDetail::getPickCode).collect(Collectors.toList());
                    List<Pick> pickDTOS = finalPickDTOList.stream().filter(pickDTO -> pickCodeList.contains(pickDTO.getPickCode())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(pickDTOS)) {
                        if (pickDTOS.size() == 1) {
                            excelExportPackageBO.setPickCode(pickDTOS.get(0).getPickCode());
                        } else {
                            Pick pickDTO = pickDTOS.stream().filter(pick -> !pick.getStatus().equals(PickEnum.PickStatusEnum.CANCEL_STATUS.getCode())).findFirst().orElse(null);
                            if (pickDTO != null) {
                                excelExportPackageBO.setPickCode(pickDTO.getPickCode());
                            }
                        }
                    }
                }
                //TODO 2023-04-04 许总要求注释
                excelExportPackageBO.setRealWeight((packageDTO.getRealWeight() == null ? BigDecimal.ZERO : packageDTO.getRealWeight()).setScale(3, RoundingMode.FLOOR).toString());

                excelExportPackageBO.setWeight((packageDTO.getWeight() == null ? BigDecimal.ZERO : packageDTO.getWeight()).setScale(3, RoundingMode.FLOOR).toString());
                excelExportPackageBO.setActualPackWeight((packageDTO.getActualPackWeight() == null ? BigDecimal.ZERO : packageDTO.getActualPackWeight()).setScale(3, RoundingMode.FLOOR).toString());

                excelExportPackageBO.setVolumetricWeight((packageDTO.getVolumetricWeight() == null ? BigDecimal.ZERO : packageDTO.getVolumetricWeight()).setScale(3, RoundingMode.FLOOR).toString());
                Optional<CargoOwnerDTO> cargoOwnerDTO = Optional.ofNullable(cargoOwnerDTOMap.get(StrUtil.join(StrUtil.COLON, packageDTO.getWarehouseCode(), packageDTO.getCargoCode())));
                cargoOwnerDTO.ifPresent(it -> excelExportPackageBO.setCargoOwner(it.getName()));
                Optional<SalePlatformDTO> salePlatformDTO = Optional.ofNullable(salePlatformDTOMap.get(packageDTO.getSalePlatform().toLowerCase()));
                salePlatformDTO.ifPresent(it -> excelExportPackageBO.setSalePlatform(it.getName()));


                Optional<CarrierDTO> carrierDTO = Optional.ofNullable(carrierDTOMap.get(packageDTO.getCarrierCode().toLowerCase()));
                carrierDTO.ifPresent(it -> excelExportPackageBO.setCarrierName(it.getName()));

                excelExportPackageBO.setExpSkuQty(packageDTO.getPackageSkuQty());
                excelExportPackageBO.setStatus(PackEnum.STATUS.findEnumDesc(packageDTO.getStatus()).getDesc());
                excelExportPackageBO.setPickCompleteSkuDate(com.dt.platform.utils.ConverterUtil.convertVoTime(packageDTO.getPickCompleteSkuDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBO.setCheckStartDate(com.dt.platform.utils.ConverterUtil.convertVoTime(packageDTO.getCheckStartDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBO.setCheckCompleteDate(com.dt.platform.utils.ConverterUtil.convertVoTime(packageDTO.getCheckCompleteDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBO.setOutStockDate(com.dt.platform.utils.ConverterUtil.convertVoTime(packageDTO.getOutStockDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBO.setInterceptCancelDate(com.dt.platform.utils.ConverterUtil.convertVoTime(packageDTO.getInterceptCancelDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBOList.add(excelExportPackageBO);
            });
            return excelExportPackageBOList;
        };
    }

    @Override
    public Cursor<Package> getCursor(Map<String, Object> param) {
        PackageQry packageQry = JSON.parseObject(JSON.toJSONString(param), PackageQry.class);
        buildQry(packageQry);
        //处理地区级联多选
        Map<String, List<String>> mapAreaMap = areaRpcMapper.getHandAreaCascade(packageQry.getReceiverProvList(), packageQry.getReceiverCityList(), packageQry.getReceiverAreaList());
        if (!CollectionUtils.isEmpty(mapAreaMap)) {
            packageQry.setReceiverProvList(mapAreaMap.getOrDefault("receiverProvList", null));
            packageQry.setReceiverCityList(mapAreaMap.getOrDefault("receiverCityList", null));
            packageQry.setReceiverAreaList(mapAreaMap.getOrDefault("receiverAreaList", null));
        }
        //获取包裹查询字段
        packageQry.setTableFieldList(LambdaHelpUtils.convertToFieldNameList(
                PackageDTO::getPackageCode, PackageDTO::getPoNo, PackageDTO::getSoNo, PackageDTO::getShipmentOrderCode,
                PackageDTO::getStatus, PackageDTO::getCargoCode, PackageDTO::getActualPackWeight, PackageDTO::getBusinessType,
                PackageDTO::getIsPre, PackageDTO::getCarrierCode, PackageDTO::getSalePlatform, PackageDTO::getSaleShopId,
                PackageDTO::getVolumetricWeight, PackageDTO::getExpressBranch, PackageDTO::getExpressBranchName, PackageDTO::getExpressNo,
                PackageDTO::getExpressAccount, PackageDTO::getRealWeight, PackageDTO::getRecPackUpc, PackageDTO::getActualPackUpc,
                PackageDTO::getPackageSkuQty, PackageDTO::getOutSkuQty, PackageDTO::getOutStockDate, PackageDTO::getPickCompleteSkuDate,
                PackageDTO::getCheckCompleteDate, PackageDTO::getInterceptCancelDate, PackageDTO::getRemark, PackageDTO::getWarehouseCode,
                PackageDTO::getCheckStartDate, PackageDTO::getCarrierName, PackageDTO::getId
        ));
        //
        buildPackAnalysisRowDataParam(packageQry);

        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(packageQry);
        return packageMapper.streamQuery(queryWrapper);
    }


    private void buildQry(PackageQry packageQry) {
        //处理订单标记
        if (packageQry != null && !StringUtils.isEmpty(packageQry.getOrderTagList())) {
            packageQry.setOrderTag(OrderTagEnum.queryParamListToInteger(packageQry.getOrderTagList()));
        }
        //upc查询
        if (!CollectionUtils.isEmpty(packageQry.getUpcCodeList())) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCodeList(packageQry.getUpcCodeList());
            if (!CollectionUtils.isEmpty(packageQry.getCargoCodeList())) {
                skuUpcParam.setCargoCodeList(packageQry.getCargoCodeList());
            }
            List<SkuUpcDTO> skuUpcDTOList = skuUpcRpcMapper.getSkuUpcList(skuUpcParam);
            if (CollectionUtils.isEmpty(skuUpcDTOList)) {
                packageQry.setCargoCodeList(Arrays.asList("---------------"));
            }
            if (!CollectionUtils.isEmpty(packageQry.getSkuCodeList())) {
                List<String> skuCodeList = packageQry.getSkuCodeList();
                skuCodeList.retainAll(skuUpcDTOList.stream().map(SkuUpcDTO::getSkuCode).collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(skuCodeList)) {
                    packageQry.setCargoCodeList(Arrays.asList("---------------"));
                } else {
                    packageQry.setSkuCodeList(skuCodeList);
                }
            } else {
                packageQry.setSkuCodeList(skuUpcDTOList.stream().map(SkuUpcDTO::getSkuCode).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 构建出库单分析行数据查询参数
     *
     * @param packageQry 查询参数
     */
    private void buildPackAnalysisRowDataParam(PackageQry packageQry) {
        //必要参数
        if (StringUtils.isEmpty(packageQry.getColumnKey())) {
            packageQry.setShipmentOrderCode("-----------------------");
        }
        //创建状态
        if (Objects.equals(packageQry.getColumnKey(), "createdOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.CREATE_STATUS.getCode());
        }
        //预处理失败
        if (Objects.equals(packageQry.getColumnKey(), "pretreatmentFailOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.PRETREATMENT_FAIL.getCode());
        }
        //预处理完成
        if (Objects.equals(packageQry.getColumnKey(), "pretreatmentCompleteOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.PRETREATMENT_SUCCESS.getCode());
        }
        //汇总失败
        if (Objects.equals(packageQry.getColumnKey(), "collectedFailOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.ASSGIN_STOCK_STATUS.getCode());
        }
        //汇总完成
        if (Objects.equals(packageQry.getColumnKey(), "collectedOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode());
        }

        //拣选开始
        if (Objects.equals(packageQry.getColumnKey(), "pickStartOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.PICK_BEGIN_STATUS.getCode());
        }
        //拣选完成
        if (Objects.equals(packageQry.getColumnKey(), "pickEndOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode());
        }

        //复核开始
        if (Objects.equals(packageQry.getColumnKey(), "checkStartOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode());
        }
        //复核完成
        if (Objects.equals(packageQry.getColumnKey(), "checkCompleteOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        }

        //缺货出库
        if (Objects.equals(packageQry.getColumnKey(), "interceptCancelOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode());
        }

        //已出库
        if (Objects.equals(packageQry.getColumnKey(), "outOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        }
        //拦截
        if (Objects.equals(packageQry.getColumnKey(), "interceptOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode());
        }
        //取消
        if (Objects.equals(packageQry.getColumnKey(), "cancelOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode());
        }
        //缺货出库
        if (Objects.equals(packageQry.getColumnKey(), "shortageOutOrderCount")) {
            packageQry.setStatus(PackEnum.STATUS.UNENOUGH_STOCK_STATUS.getCode());
        }
        //--------------------状态码转换------------------

        //创建时间
        if (packageQry.getCreateTimeStart() != null && packageQry.getCreateTimeEnd() != null) {
            packageQry.setCreatedTimeStart(packageQry.getCreateTimeStart());
            packageQry.setCreatedTimeEnd(packageQry.getCreateTimeEnd());
        }
        //商品数
        if (packageQry.getSkuQtyMax() != null) {
            packageQry.setEndSkuCount(packageQry.getSkuQtyMax());
        }
        if (packageQry.getSkuQtyMin() != null) {
            packageQry.setStartSkuCount(packageQry.getSkuQtyMin());
        }
        //订单数
        if (packageQry.getSkuTypeQtyMax() != null) {
            packageQry.setEndSkuTypeCount(packageQry.getSkuTypeQtyMax());
        }
        if (packageQry.getSkuTypeQtyMin() != null) {
            packageQry.setStartSkuTypeCount(packageQry.getSkuTypeQtyMin());
        }
        //重量
        if (packageQry.getWeightMin() != null) {
            packageQry.setWeightStart(BigDecimal.valueOf(packageQry.getWeightMin()));
        }
        if (packageQry.getWeightMax() != null) {
            packageQry.setWeightEnd(BigDecimal.valueOf(packageQry.getWeightMax()));
        }
        //-----------------付款时间------------------
        if (packageQry.getPayDateStart() != null) {
            packageQry.setStartPayTime(packageQry.getPayDateStart());
        }
        if (packageQry.getPayDateEnd() != null) {
            packageQry.setEndPayTime(packageQry.getPayDateEnd());
        }
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getPayDate_day())) {
            long time = DateUtil.parse(packageQry.getPayDate_day(), "yyyy-MM-dd").getTime();
            packageQry.setStartPayTime(DateUtil.beginOfDay(new Date(time)).getTime());
            packageQry.setEndPayTime(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getPayDate_hour())) {
            long time = DateUtil.parse(packageQry.getPayDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageQry.setStartPayTime(time);
            packageQry.setEndPayTime(time + 3600L * 1000);
        }
        //-----------------付款时间------------------

        //-----------------创建时间------------------
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getCreatedTime_day())) {
            long time = DateUtil.parse(packageQry.getCreatedTime_day(), "yyyy-MM-dd").getTime();
            packageQry.setCreatedTimeStart(DateUtil.beginOfDay(new Date(time)).getTime());
            packageQry.setCreatedTimeEnd(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getCreatedTime_hour())) {
            long time = DateUtil.parse(packageQry.getCreatedTime_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageQry.setCreatedTimeStart(time);
            packageQry.setCreatedTimeEnd(time + 3600L * 1000);
        }
        //-----------------创建时间------------------

        //-----------------出库时间------------------
        if (packageQry.getOutStockDateStart() != null) {
            packageQry.setStartOutStockDate(packageQry.getOutStockDateStart());
        }
        if (packageQry.getOutStockDateEnd() != null) {
            packageQry.setEndOutStockDate(packageQry.getOutStockDateEnd());
        }
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getOutStockDate_day())) {
            long time = DateUtil.parse(packageQry.getOutStockDate_day(), "yyyy-MM-dd").getTime();
            packageQry.setStartOutStockDate(DateUtil.beginOfDay(new Date(time)).getTime());
            packageQry.setEndOutStockDate(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getOutStockDate_hour())) {
            long time = DateUtil.parse(packageQry.getOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageQry.setStartOutStockDate(time);
            packageQry.setEndOutStockDate(time + 3600L * 1000);
        }
        //-----------------出库时间------------------

        //-----------------拣选完成------------------
        if (packageQry.getPickCompleteSkuDateStart() != null) {
            packageQry.setPickCompleteSkuDateStart(packageQry.getPickCompleteDateStart());
        }
        if (packageQry.getPickCompleteSkuDateEnd() != null) {
            packageQry.setPickCompleteSkuDateEnd(packageQry.getPickCompleteDateEnd());
        }
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getPickCompleteSkuDate_day())) {
            long time = DateUtil.parse(packageQry.getPickCompleteSkuDate_day(), "yyyy-MM-dd").getTime();
            packageQry.setPickCompleteSkuDateStart(DateUtil.beginOfDay(new Date(time)).getTime());
            packageQry.setPickCompleteSkuDateEnd(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getPickCompleteSkuDate_hour())) {
            long time = DateUtil.parse(packageQry.getPickCompleteSkuDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageQry.setPickCompleteSkuDateStart(time);
            packageQry.setPickCompleteSkuDateEnd(time + 3600L * 1000);
        }
        //-----------------拣选完成------------------

        //-----------------复核完成------------------
        if (packageQry.getCheckCompleteDateStart() != null) {
            packageQry.setCheckCompleteDateStart(packageQry.getCheckCompleteDateStart());
        }
        if (packageQry.getCheckCompleteDateEnd() != null) {
            packageQry.setCheckCompleteDateEnd(packageQry.getCheckCompleteDateEnd());
        }
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getCheckCompleteDate_day())) {
            long time = DateUtil.parse(packageQry.getCheckCompleteDate_day(), "yyyy-MM-dd").getTime();
            packageQry.setCheckCompleteDateStart(DateUtil.beginOfDay(new Date(time)).getTime());
            packageQry.setCheckCompleteDateEnd(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getCheckCompleteDate_hour())) {
            long time = DateUtil.parse(packageQry.getCheckCompleteDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageQry.setCheckCompleteDateStart(time);
            packageQry.setCheckCompleteDateEnd(time + 3600L * 1000);
        }
        //-----------------复核完成------------------


        //-----------------预计出库时间------------------
        if (packageQry.getExpOutStockDateStart() != null) {
            packageQry.setStartExpOutStockDate(packageQry.getExpOutStockDateStart());
        }
        if (packageQry.getExpOutStockDateEnd() != null) {
            packageQry.setEndExpOutStockDate(packageQry.getExpOutStockDateEnd());
        }
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getExpOutStockDate_day())) {
            long time = DateUtil.parse(packageQry.getExpOutStockDate_day(), "yyyy-MM-dd").getTime();
            packageQry.setStartExpOutStockDate(DateUtil.beginOfDay(new Date(time)).getTime());
            packageQry.setEndExpOutStockDate(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(packageQry.getExpOutStockDate_hour())) {
            long time = DateUtil.parse(packageQry.getExpOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            packageQry.setStartExpOutStockDate(time);
            packageQry.setEndExpOutStockDate(time + 3600L * 1000);
        }
        //-----------------预计出库时间------------------
    }


    @Override
    public String getSheetName() {
        return ExcelExportEnum.EXPORT_PACK_ROW_ANALYSIS_REPORT.getFuncName();
    }


}
