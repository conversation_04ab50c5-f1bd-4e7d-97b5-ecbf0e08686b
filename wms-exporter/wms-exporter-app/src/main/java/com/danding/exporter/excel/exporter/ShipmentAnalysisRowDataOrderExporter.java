package com.danding.exporter.excel.exporter;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.exporter.common.ConverterUtil;
import com.danding.exporter.database.shipment.entity.ShipmentOrder;
import com.danding.exporter.database.shipment.mapper.ShipmentOrderMapper;
import com.danding.exporter.database.shipment.util.ShipmentOrderUtil;
import com.danding.exporter.domain.pack.gateway.IPackageGateway;
import com.danding.exporter.domain.pick.gateway.IPickDetailGateway;
import com.danding.exporter.domain.shipment.gateway.IShipmentOrderDetailGateway;
import com.danding.exporter.domain.shipment.gateway.IShipmentOrderGateway;
import com.danding.exporter.excel.ExcelExportEnum;
import com.danding.exporter.excel.bo.ShipmentOrderBO;
import com.danding.exporter.rpc.*;
import com.danding.exporter.shipment.dto.ShipmentOrderQry;
import com.dt.component.common.enums.bill.*;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.param.AreaParam;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.CarrierParam;
import com.dt.domain.base.param.SalePlatformParam;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022-09-09 10:56
 */
@Component("shipmentAnalysisRowDataOrderExporter")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ShipmentAnalysisRowDataOrderExporter extends AbstractDefaultExporter<ShipmentOrder, ShipmentOrderBO> {

    @Resource
    private ShipmentOrderMapper shipmentOrderMapper;

    @Resource
    private ShipmentOrderUtil shipmentOrderUtil;

    @Resource
    private IPackageGateway packageGateway;

    @Resource
    private IPickDetailGateway pickDetailGateway;

    @Resource
    private IShipmentOrderDetailGateway shipmentOrderDetailGateway;

    @Resource
    private IShipmentOrderGateway shipmentOrderGateway;

    @Resource
    private CargoOwnerRpcMapper cargoOwnerRpcMapper;

    @Resource
    private CargoConfigRpcMapper cargoConfigRpcMapper;

    @Resource
    private AreaRpcMapper areaRpcMapper;

    @Resource
    private CarrierRpcMapper carrierRpcMapper;

    @Resource
    private SalePlatformRpcMapper salePlatformRpcMapper;

    @Override
    public CommonExcelHandler<ShipmentOrder, ShipmentOrderBO> createHandler() {
        return list -> {
            // 货主编码
            Set<String> cargoCodeSet = new HashSet<>();
            Set<String> areaCodeSet = new HashSet<>();
            Set<String> carrierCodeSet = new HashSet<>();
            Set<String> salePlatformSet = new HashSet<>();
            list.forEach(it -> {
                cargoCodeSet.add(it.getCargoCode());

                areaCodeSet.add(it.getReceiverArea());
                areaCodeSet.add(it.getReceiverCity());
                areaCodeSet.add(it.getReceiverProv());

                carrierCodeSet.add(it.getCarrierCode());
                salePlatformSet.add(it.getSalePlatform());
            });
            List<String> cargoCodeList = new ArrayList<>(cargoCodeSet);
            List<String> areaCodeList = new ArrayList<>(areaCodeSet);
            List<String> carrierCodeList = new ArrayList<>(carrierCodeSet);
            List<String> salePlatformList = new ArrayList<>(salePlatformSet);

            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(cargoCodeList);
            List<CargoOwnerDTO> cargoOwnerDTOList = cargoOwnerRpcMapper.getList(cargoOwnerParam);
            Map<String, CargoOwnerDTO> cargoOwnerDTOMap =
                    cargoOwnerDTOList.stream().collect(Collectors.toMap(CargoOwnerDTO::getCode, Function.identity()));
            // 货主配置
            List<DecimalPlaceDTO> decimalPlaceDTOList = cargoConfigRpcMapper.getList(cargoCodeList);
            Map<String, DecimalPlaceDTO> decimalPlaceDTOMap =
                    decimalPlaceDTOList.stream().collect(Collectors.toMap(DecimalPlaceDTO::getCargoCode, Function.identity(), (k1, k2) -> k1));
            // 省市区
            AreaParam areaParam = new AreaParam();
            areaParam.setAreaCodeList(areaCodeList);
            List<AreaDTO> areaDTOList = areaRpcMapper.getList(areaParam);
            Map<String, AreaDTO> areaDTOMap =
                    areaDTOList.stream().collect(Collectors.toMap(AreaDTO::getAreaCode, Function.identity()));
            // 承运商
            CarrierParam carrierParam = new CarrierParam();
            carrierParam.setCodeList(carrierCodeList);
            List<CarrierDTO> carrierDTOList = carrierRpcMapper.getList(carrierParam);
            Map<String, CarrierDTO> carrierDTOMap =
                    carrierDTOList.stream().collect(Collectors.toMap(CarrierDTO::getCode, Function.identity()));
            // 销售平台
            SalePlatformParam salePlatformParam = new SalePlatformParam();
            salePlatformParam.setCodeList(salePlatformList);
            List<SalePlatformDTO> salePlatformDTOList = salePlatformRpcMapper.getList(salePlatformParam);
            Map<String, SalePlatformDTO> salePlatformDTOMap =
                    salePlatformDTOList.stream().collect(Collectors.toMap(SalePlatformDTO::getCode, Function.identity()));

            List<ShipmentOrderBO> result;
            result = list.stream().flatMap(a -> Stream.of(getShipmentOrderVO(cargoOwnerDTOMap,
                    decimalPlaceDTOMap, areaDTOMap, carrierDTOMap, salePlatformDTOMap, a))).collect(Collectors.toList());
            return result;
        };
    }

    @Override
    public Cursor<ShipmentOrder> getCursor(Map<String, Object> param) {
        ShipmentOrderQry shipmentOrderQry = JSON.parseObject(JSON.toJSONString(param), ShipmentOrderQry.class);
        shipmentOrderGateway.buildQry(shipmentOrderQry);
        //处理地区级联多选
        Map<String, List<String>> mapAreaMap = areaRpcMapper.getHandAreaCascade(shipmentOrderQry.getReceiverProvList(), shipmentOrderQry.getReceiverCityList(), shipmentOrderQry.getReceiverAreaList());
        if (!CollectionUtils.isEmpty(mapAreaMap)) {
            shipmentOrderQry.setReceiverProvList(mapAreaMap.getOrDefault("receiverProvList", null));
            shipmentOrderQry.setReceiverCityList(mapAreaMap.getOrDefault("receiverCityList", null));
            shipmentOrderQry.setReceiverAreaList(mapAreaMap.getOrDefault("receiverAreaList", null));
        }
        //
        buildShipmentAnalysisRowDataParam(shipmentOrderQry);

        LambdaQueryWrapper<ShipmentOrder> queryWrapper = shipmentOrderUtil.getQueryWrapper(shipmentOrderQry);
        return shipmentOrderMapper.streamQuery(queryWrapper);
    }

    /**
     * 构建出库单分析行数据查询参数
     *
     * @param param 查询参数
     */
    private void buildShipmentAnalysisRowDataParam(ShipmentOrderQry param) {
        //必要参数
        if (StringUtils.isEmpty(param.getColumnKey())) {
            param.setShipmentOrderCode("-----------------------");
        }
        //--------------------状态码转换------------------
        //创建状态
        if (Objects.equals(param.getColumnKey(), "createdOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode());
        }
        //预处理失败
        if (Objects.equals(param.getColumnKey(), "pretreatmentFailOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_FAIL_STATUS.getCode());
        }
        //预处理完成
        if (Objects.equals(param.getColumnKey(), "pretreatmentCompleteOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        }
        //已汇总
        if (Objects.equals(param.getColumnKey(), "collectedOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode());
        }
        //复核开始
        if (Objects.equals(param.getColumnKey(), "checkStartOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode());
        }
        //复核完成
        if (Objects.equals(param.getColumnKey(), "checkCompleteOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode());
        }
        //部分出库
        if (Objects.equals(param.getColumnKey(), "partialOutOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
        }
        //已出库
        if (Objects.equals(param.getColumnKey(), "outOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
        }
        //拦截
        if (Objects.equals(param.getColumnKey(), "interceptOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.INTERCEPT_STATUS.getCode());
        }
        //取消
        if (Objects.equals(param.getColumnKey(), "cancelOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.CANCEL_STATUS.getCode());
        }
        //缺货出库
        if (Objects.equals(param.getColumnKey(), "shortageOutOrderCount")) {
            param.setStatus(ShipmentOrderEnum.STATUS.OUT_OF_OUT_STOCK_STATUS.getCode());
        }
        //--------------------状态码转换------------------

        //创建时间
        if (param.getCreateTimeStart() != null && param.getCreateTimeEnd() != null) {
            param.setCreatedTimeStart(param.getCreateTimeStart());
            param.setCreatedTimeEnd(param.getCreateTimeEnd());
        }
        //商品数
        if (param.getSkuQtyMax() != null) {
            param.setEndSkuCount(param.getSkuQtyMax());
        }
        if (param.getSkuQtyMin() != null) {
            param.setStartSkuCount(param.getSkuQtyMin());
        }
        //订单数
        if (param.getSkuTypeQtyMax() != null) {
            param.setEndSkuTypeCount(param.getSkuTypeQtyMax());
        }
        if (param.getSkuTypeQtyMin() != null) {
            param.setStartSkuTypeCount(param.getSkuTypeQtyMin());
        }
        //重量
        if (param.getWeightMin() != null) {
            param.setWeightStart(BigDecimal.valueOf(param.getWeightMin()));
        }
        if (param.getWeightMax() != null) {
            param.setWeightEnd(BigDecimal.valueOf(param.getWeightMax()));
        }
        //-----------------付款时间------------------
        if (param.getPayDateStart() != null) {
            param.setStartPayTime(param.getPayDateStart());
        }
        if (param.getPayDateEnd() != null) {
            param.setEndPayTime(param.getPayDateEnd());
        }
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getPayDate_day())) {
            long time = DateUtil.parse(param.getPayDate_day(), "yyyy-MM-dd").getTime();
            param.setStartPayTime(DateUtil.beginOfDay(new Date(time)).getTime());
            param.setEndPayTime(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getPayDate_hour())) {
            long time = DateUtil.parse(param.getPayDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            param.setStartPayTime(time);
            param.setEndPayTime(time + 3600L * 1000);
        }
        //-----------------付款时间------------------

        //-----------------创建时间------------------
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getCreatedTime_day())) {
            long time = DateUtil.parse(param.getCreatedTime_day(), "yyyy-MM-dd").getTime();
            param.setCreatedTimeStart(DateUtil.beginOfDay(new Date(time)).getTime());
            param.setCreatedTimeEnd(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getCreatedTime_hour())) {
            long time = DateUtil.parse(param.getCreatedTime_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            param.setCreatedTimeStart(time);
            param.setCreatedTimeEnd(time + 3600L * 1000);
        }
        //-----------------创建时间------------------

        //-----------------出库时间------------------
        if (param.getOutStockDateStart() != null) {
            param.setStartOutStockDate(param.getOutStockDateStart());
        }
        if (param.getOutStockDateEnd() != null) {
            param.setEndOutStockDate(param.getOutStockDateEnd());
        }
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getOutStockDate_day())) {
            long time = DateUtil.parse(param.getOutStockDate_day(), "yyyy-MM-dd").getTime();
            param.setStartOutStockDate(DateUtil.beginOfDay(new Date(time)).getTime());
            param.setEndOutStockDate(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getOutStockDate_hour())) {
            long time = DateUtil.parse(param.getOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            param.setStartOutStockDate(time);
            param.setEndOutStockDate(time + 3600L * 1000);
        }
        //-----------------出库时间------------------

        //-----------------预计出库时间------------------
        if (param.getExpOutStockDateStart() != null) {
            param.setStartExpOutStockDate(param.getExpOutStockDateStart());
        }
        if (param.getExpOutStockDateEnd() != null) {
            param.setEndExpOutStockDate(param.getExpOutStockDateEnd());
        }
        //按天
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getExpOutStockDate_day())) {
            long time = DateUtil.parse(param.getExpOutStockDate_day(), "yyyy-MM-dd").getTime();
            param.setStartExpOutStockDate(DateUtil.beginOfDay(new Date(time)).getTime());
            param.setEndExpOutStockDate(DateUtil.endOfDay(new Date(time)).getTime());
        }
        //按小时
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getExpOutStockDate_hour())) {
            long time = DateUtil.parse(param.getExpOutStockDate_hour(), "yyyy-MM-dd HH:mm:ss").getTime();
            param.setStartExpOutStockDate(time);
            param.setEndExpOutStockDate(time + 3600L * 1000);
        }
        //-----------------预计出库时间------------------
    }


    @Override
    public String getSheetName() {
        return ExcelExportEnum.EXPORT_SHIPMENT_ROW_ANALYSIS_REPORT.getFuncName();
    }

    private ShipmentOrderBO getShipmentOrderVO(Map<String, CargoOwnerDTO> cargoOwnerDTOMap,
                                               Map<String, DecimalPlaceDTO> decimalPlaceDTOMap, Map<String, AreaDTO> areaDTOMap,
                                               Map<String, CarrierDTO> carrierDTOMap, Map<String, SalePlatformDTO> salePlatformDTOMap,
                                               ShipmentOrder shipmentOrderDTO) {
        CargoOwnerDTO cargoOwnerDTO = cargoOwnerDTOMap.get(shipmentOrderDTO.getCargoCode());
        DecimalPlaceDTO decimalPlaceDTO = decimalPlaceDTOMap.get(shipmentOrderDTO.getCargoCode());
        CarrierDTO carrierDTO = carrierDTOMap.get(shipmentOrderDTO.getCarrierCode());

        ShipmentOrderBO shipmentOrderBO = new ShipmentOrderBO();
        BeanUtils.copyProperties(shipmentOrderDTO, shipmentOrderBO);
        ShipmentCustomsClearanceTypeEnum.getByCode(shipmentOrderDTO.getCustomsClearanceType()).ifPresent(it -> shipmentOrderBO.setCustomsClearanceType(it.getName()));
        ShipmentCustomsClearanceStatusEnum.getByCode(shipmentOrderDTO.getCustomsClearanceStatus()).ifPresent(it -> shipmentOrderBO.setCustomsClearanceStatus(it.getName()));

        // 最晚发货时间
        if (shipmentOrderDTO.getExpShipTime() != null) {
            shipmentOrderBO.setExpShipTime(ConverterUtil.convertVoTime(shipmentOrderDTO.getExpShipTime()));
        }
        // 预售类型
        if (shipmentOrderDTO.getPreSaleType() != null) {
            shipmentOrderBO
                    .setPreSaleType(ShipmentPreSaleTypeEnum.fromCode(shipmentOrderDTO.getPreSaleType()).getMessage());
        }

        shipmentOrderBO
                .setSkuQty((shipmentOrderDTO.getSkuQty() == null ? BigDecimal.ZERO : shipmentOrderDTO.getSkuQty())
                        .setScale(decimalPlaceDTO.getNumber(), RoundingMode.FLOOR).toString());
        shipmentOrderBO
                .setOutSkuQty((shipmentOrderDTO.getOutSkuQty() == null ? BigDecimal.ZERO : shipmentOrderDTO.getOutSkuQty())
                        .setScale(decimalPlaceDTO.getNumber(), RoundingMode.FLOOR).toString());
        shipmentOrderBO.setCheckCompleteDate(ConverterUtil.convertVoTime(shipmentOrderDTO.getCheckCompleteDate()));
        shipmentOrderBO.setCheckStartDate(ConverterUtil.convertVoTime(shipmentOrderDTO.getCheckStartDate()));
        shipmentOrderBO.setInterceptCancelDate(ConverterUtil.convertVoTime(shipmentOrderDTO.getInterceptCancelDate()));
        shipmentOrderBO.setOutStockDate(ConverterUtil.convertVoTime(shipmentOrderDTO.getOutStockDate()));
        shipmentOrderBO.setCreatedTime(ConverterUtil.convertVoTime(shipmentOrderDTO.getCreatedTime()));
        shipmentOrderBO
                .setFirstPackOutStockDate(ConverterUtil.convertVoTime(shipmentOrderDTO.getFirstPackOutStockDate()));
        shipmentOrderBO.setPickSkuDate(ConverterUtil.convertVoTime(shipmentOrderDTO.getPickSkuDate()));
        shipmentOrderBO.setSalePlatform(null);
        if (!StringUtils.isEmpty(shipmentOrderDTO.getSalePlatform())) {
            SalePlatformDTO salePlatformDTO = salePlatformDTOMap.get(shipmentOrderDTO.getSalePlatform());
            if (salePlatformDTO != null) {
                shipmentOrderBO.setSalePlatform(salePlatformDTO.getName());
            }
        }
        shipmentOrderBO.setOrderTag("");
        if (!StringUtils.isEmpty(shipmentOrderDTO.getOrderTag())) {
            Set<OrderTagEnum> orderTagEnumList = OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag());
            if (!CollectionUtils.isEmpty(orderTagEnumList)) {
                String orderTagName = orderTagEnumList.stream().map(OrderTagEnum::getDesc).collect(Collectors.joining("|"));
                shipmentOrderBO.setOrderTag(orderTagName);
            } else {
                shipmentOrderBO.setOrderTag("");
            }
        }
        shipmentOrderBO.setPayDate(ConverterUtil.convertVoTime(shipmentOrderDTO.getPayDate()));
        ShipmentOrderEnum.STATUS status = ShipmentOrderEnum.STATUS.findOrderStatus(shipmentOrderDTO.getStatus());
        String statusDesc = status == null ? "" : status.getDesc();
        shipmentOrderBO.setStatusName(statusDesc);
        if (!StringUtils.isEmpty(shipmentOrderDTO.getCarrierCode())) {
            if (carrierDTO != null) {
                shipmentOrderBO.setCarrierName(carrierDTO.getName());
            }
        }
        if (!StringUtils.isEmpty(shipmentOrderDTO.getReceiverArea())) {
            AreaDTO areaDTO = areaDTOMap.get(shipmentOrderDTO.getReceiverArea());
            if (areaDTO != null) {
                shipmentOrderBO.setReceiverArea(areaDTO.getAreaName());
            }
        } else {
            shipmentOrderBO.setReceiverArea(shipmentOrderDTO.getReceiverAreaName());
        }

        if (!StringUtils.isEmpty(shipmentOrderDTO.getReceiverCity())) {
            AreaDTO areaDTO = areaDTOMap.get(shipmentOrderDTO.getReceiverCity());
            if (areaDTO != null) {
                shipmentOrderBO.setReceiverCity(areaDTO.getAreaName());
            }
        } else {
            shipmentOrderBO.setReceiverCity(shipmentOrderDTO.getReceiverCityName());
        }

        if (!StringUtils.isEmpty(shipmentOrderDTO.getReceiverProv())) {
            AreaDTO areaDTO = areaDTOMap.get(shipmentOrderDTO.getReceiverProv());
            if (areaDTO != null) {
                shipmentOrderBO.setReceiverProv(areaDTO.getAreaName());
            }
        } else {
            shipmentOrderBO.setReceiverProv(shipmentOrderDTO.getReceiverProvName());
        }
        if (!StringUtils.isEmpty(shipmentOrderDTO.getCargoCode())) {
            if (cargoOwnerDTO != null) {
                shipmentOrderBO.setCargoName(cargoOwnerDTO.getName());
            } else {
                shipmentOrderBO.setCargoName("");
            }
        } else {
            shipmentOrderBO.setCargoName("");
        }

        // 推荐包材
        if (StrUtil.isNotBlank(shipmentOrderDTO.getExtraJson())) {
            shipmentOrderBO.setCalculateMaterialCode(JSONUtil.parseObj(shipmentOrderDTO.getExtraJson()).getStr("calculateMaterialBarCode"));
        }

        return shipmentOrderBO;
    }

}
