package com.danding.exporter.excel.exporter;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.exporter.database.pack.entity.Package;
import com.danding.exporter.excel.ExcelExportEnum;
import com.danding.exporter.excel.bo.pack.ExcelExportPackageBO;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.domain.bill.dto.pkg.PackAnalysisBillDTO;
import com.dt.domain.bill.param.pkg.PackAnalysisBillParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.*;
import com.dt.platform.wms.dto.base.CarrierBizDTO;
import com.dt.platform.wms.dto.base.WarehouseBizDTO;
import com.dt.platform.wms.dto.cargo.CargoOwnerBizDTO;
import com.dt.platform.wms.dto.sale.SalePlatformBizDTO;
import com.dt.platform.wms.param.cargo.CargoOwnerBizParam;
import com.dt.platform.wms.param.carrier.CarrierBizParam;
import com.dt.platform.wms.param.sale.SalePlatformQueryBizParam;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-09 10:56
 */
@Component("packAnalysisProductDataOrderExporter")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PackAnalysisDataOrderExporter extends AbstractDefaultExporter<Package, ExcelExportPackageBO> {


    @DubboReference
    private IAreaBizClient areaBizClient;

    @DubboReference
    private IWarehouseBizClient iWarehouseBizClient;

    @DubboReference
    private ICargoOwnerBizClient cargoOwnerBizClient;

    @DubboReference
    private ICarrierBizClient iCarrierBizClient;

    @DubboReference
    private ISalePlatformBizClient salePlatformBizClient;

    @DubboReference
    private IPackageBizClient packageBizClient;

    @Override
    public CommonExcelHandler<Package, ExcelExportPackageBO> createHandler() {
        return null;
    }

    @Override
    public Cursor<Package> getCursor(Map<String, Object> param) {
        return null;
    }

    @Override
    public String getSheetName() {
        return ExcelExportEnum.EXPORT_PACK_ANALYSIS_REPORT.getFuncName();
    }

    @Override
    public boolean isCursorQuery() {
        return Boolean.FALSE;
    }


    @Override
    @DS("#DTWMS")
    public File doExport() {
        File file = new File(UUID.randomUUID().toString());

        ExcelWriter excelWriter = EasyExcel.write(file).build();

        PackAnalysisBillParam shipmentAnalysisParam = JSON.parseObject(JSON.toJSONString(param), PackAnalysisBillParam.class);
        if (shipmentAnalysisParam != null) {

            if (CollectionUtils.isEmpty(shipmentAnalysisParam.getAnalysisDimensions())) {
                shipmentAnalysisParam.setAnalysisDimensions(new ArrayList<>());
            }
            //add header`
            List<List<String>> head = buildHeadList(shipmentAnalysisParam.getAnalysisDimensions());

            WriteSheet writeSheet = EasyExcel.writerSheet(0, "生产进度").head(head).build();

            validateCreateTimeParam(shipmentAnalysisParam);

            List<List<Object>> exportDataList = getExportDataList(shipmentAnalysisParam);

            excelWriter.write(exportDataList, writeSheet);
        }
        excelWriter.finish();

        return file;
    }

    /**
     * 获取导出数据列表
     *
     * @param shipmentAnalysisParam 查询参数
     * @return 导出数据列表
     */
    public List<List<Object>> getExportDataList(PackAnalysisBillParam shipmentAnalysisParam) {
        //行级数据
        List<List<Object>> data = new ArrayList<>();

        if (shipmentAnalysisParam == null) {
            return data;
        }

        List<PackAnalysisBillDTO> shipmentAnalysisBillDTOList = new ArrayList<>();
        //数据来源
        PackAnalysisBillParam packAnalysisBillParam = ConverterUtil.convert(shipmentAnalysisParam, PackAnalysisBillParam.class);
        packAnalysisBillParam.setPageSize(2000);
        Page<PackAnalysisBillDTO> packAnalysisPage = packageBizClient.getPackAnalysisPage(packAnalysisBillParam).getData();
        for (int i = 1; i <= packAnalysisPage.getPages(); i++) {
            packAnalysisBillParam.setCurrentPage(i);
            packAnalysisBillParam.setPageSize(2000);
            packAnalysisPage = packageBizClient.getPackAnalysisPage(packAnalysisBillParam).getData();
            if (!CollectionUtils.isEmpty(packAnalysisPage.getRecords())) {
                shipmentAnalysisBillDTOList.addAll(packAnalysisPage.getRecords());
            }
        }
        if (!CollectionUtils.isEmpty(shipmentAnalysisBillDTOList)) {

            List<PackAnalysisBillDTO> shipmentAnalysisDTORowList = ConverterUtil.convertList(shipmentAnalysisBillDTOList, PackAnalysisBillDTO.class);
            buildVo(shipmentAnalysisDTORowList);
            //add rows
            for (PackAnalysisBillDTO shipmentAnalysisDTO : shipmentAnalysisDTORowList) {
                List<Object> rowData = buildRowData(shipmentAnalysisDTO, shipmentAnalysisParam.getAnalysisDimensions());
                data.add(rowData);
            }
        }

        return data;
    }


    /**
     * 获取货主映射
     *
     * @param dataList 数据列表
     * @return 货主映射
     */
    private Map<String, CargoOwnerBizDTO> getCargoOwnerMap(List<PackAnalysisBillDTO> dataList) {
        CargoOwnerBizParam cargoOwnerBizParam = new CargoOwnerBizParam();
        cargoOwnerBizParam.setCodeList(dataList.stream().map(PackAnalysisBillDTO::getCargoCode).distinct().collect(Collectors.toList()));
        List<CargoOwnerBizDTO> cargoOwnerBizDTOList = cargoOwnerBizClient.queryList(cargoOwnerBizParam).getData();
        return cargoOwnerBizDTOList.stream().collect(Collectors.toMap(CargoOwnerBizDTO::getCode, Function.identity(), (a, b) -> a));
    }

    /**
     * 获取快递公司映射
     *
     * @param dataList 数据列表
     * @return 快递公司映射
     */
    private Map<String, CarrierBizDTO> getCarrierMap(List<PackAnalysisBillDTO> dataList) {
        CarrierBizParam carrierBizParam = new CarrierBizParam();
        carrierBizParam.setCodeList(dataList.stream().map(PackAnalysisBillDTO::getCarrierCode).distinct().collect(Collectors.toList()));
        List<CarrierBizDTO> carrierBizDTOList = iCarrierBizClient.getList(carrierBizParam).getData();
        return carrierBizDTOList.stream().collect(Collectors.toMap(CarrierBizDTO::getCode, Function.identity(), (a, b) -> a));
    }

    /**
     * 获取平台映射
     *
     * @param dataList 数据列表
     * @return 平台映射
     */
    private Map<String, SalePlatformBizDTO> getSalePlatformMap(List<PackAnalysisBillDTO> dataList) {
        SalePlatformQueryBizParam platformQueryBizParam = new SalePlatformQueryBizParam();
        platformQueryBizParam.setCodeList(dataList.stream().map(PackAnalysisBillDTO::getSalePlatform).distinct().collect(Collectors.toList()));
        List<SalePlatformBizDTO> platformBizDTOList = salePlatformBizClient.getList(platformQueryBizParam).getData();
        return platformBizDTOList.stream().collect(Collectors.toMap(SalePlatformBizDTO::getCode, Function.identity(), (a, b) -> a));
    }

    /**
     * 构建VO
     *
     * @param dataList
     */
    private void buildVo(List<PackAnalysisBillDTO> dataList) {
        //获取仓库名称
        WarehouseBizDTO warehouseBizDTO = iWarehouseBizClient.queryByCode(CurrentRouteHolder.getWarehouseCode()).getData();

        Map<String, CargoOwnerBizDTO> cargoOwnerMap = getCargoOwnerMap(dataList);
        Map<String, CarrierBizDTO> carrierMap = getCarrierMap(dataList);
        Map<String, SalePlatformBizDTO> platformMap = getSalePlatformMap(dataList);

        dataList.forEach(a -> {
            a.setWarehouseName(warehouseBizDTO != null ? warehouseBizDTO.getName() : "");
            a.setCargoName(cargoOwnerMap.getOrDefault(a.getCargoCode(), new CargoOwnerBizDTO()).getName());
            a.setCarrierName(carrierMap.getOrDefault(a.getCarrierCode(), new CarrierBizDTO()).getName());
            a.setSalePlatformName(platformMap.getOrDefault(a.getSalePlatform(), new SalePlatformBizDTO()).getName());
            if (a.getPackageStruct() != null) {
                a.setPackageStructName(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(a.getPackageStruct()).getDesc());
            }
            //创建小时
            if (StringUtils.isNotEmpty(a.getCreatedTime_hour())) {
                a.setCreatedTime_hour(a.getCreatedTime_hour() + ":00:00");
            }
            //预计出库时间小时
            if (StringUtils.isNotEmpty(a.getExpOutStockDate_hour())) {
                a.setExpOutStockDate_hour(a.getExpOutStockDate_hour() + ":00:00");
            }
            //出库时间小时
            if (StringUtils.isNotEmpty(a.getOutStockDate_hour())) {
                a.setOutStockDate_hour(a.getOutStockDate_hour() + ":00:00");
            }
            //付款时间小时
            if (StringUtils.isNotEmpty(a.getPayDate_hour())) {
                a.setPayDate_hour(a.getPayDate_hour() + ":00:00");
            }
        });
    }

    /**
     * 校验创建时间参数
     * 创建时间必填，且跨度不能超过一个自然月
     *
     * @param param 查询参数
     * @return 校验结果，如果有错误返回错误结果，否则返回 null
     */
    private void validateCreateTimeParam(PackAnalysisBillParam param) {

        // 校验创建时间参数
        if (param.getCreateTimeStart() == null || param.getCreateTimeEnd() == null) {
            param.setCreatedTimeStart(100L);
            param.setCreatedTimeEnd(100L);
            return;
        }
        //创建时间
        checkCreateTimeRange(param);

        //字段转换
        if (StringUtils.isNotEmpty(param.getField()) && sortList.contains(param.getField())) {
            param.setSortField(param.getField());
        }
        //字段转换
        if (StringUtils.isNotEmpty(param.getOrder()) && Objects.equals(param.getOrder(), "ascend")) {
            param.setSortOrder("ASC");
        }
        if (StringUtils.isNotEmpty(param.getOrder()) && Objects.equals(param.getOrder(), "descend")) {
            param.setSortOrder("DESC");
        }

        Map<String, List<String>> mapAreaMap = areaBizClient.handAreaCascade(param.getReceiverProvList(), param.getReceiverCityList(), param.getReceiverAreaList());
        if (!CollectionUtils.isEmpty(mapAreaMap)) {
            param.setReceiverProvList(mapAreaMap.getOrDefault("receiverProvList", null));
            param.setReceiverCityList(mapAreaMap.getOrDefault("receiverCityList", null));
            param.setReceiverAreaList(mapAreaMap.getOrDefault("receiverAreaList", null));
        }


    }

    /**
     * 校验创建时间范围
     * 这里可以添加具体的逻辑来检查创建时间范围是否符合业务要求
     *
     * @param param 查询参数
     */
    private void checkCreateTimeRange(PackAnalysisBillParam param) {
        // 计算时间跨度（毫秒）
        long timeSpan = param.getCreateTimeEnd() - param.getCreateTimeStart();
        // 一个自然月的最大毫秒数（按31天计算）
        long maxMonthSpan = 31L * 24 * 60 * 60 * 1000;

        if (timeSpan <= 0) {
            param.setCreatedTimeStart(100L);
            param.setCreatedTimeEnd(100L);
            return;
        }

        if (timeSpan > maxMonthSpan) {
            param.setCreatedTimeStart(100L);
            param.setCreatedTimeEnd(100L);
            return;
        }
    }

    /**
     * 获取值或空字符串
     *
     * @param value 值
     * @return 如果值为null，则返回空字符串，否则返回原值
     */
    private static Object getValueOrEmpty(Object value) {
        return value == null ? "" : value;
    }

    /**
     * 构建行数据
     *
     * @param packAnalysisBillDTO 数据对象
     * @param analysisDimensions  分析维度列表
     * @return 行数据列表
     */
    private static List<Object> buildRowData(PackAnalysisBillDTO packAnalysisBillDTO, List<String> analysisDimensions) {
        List<Object> rowData = new ArrayList<>();
        //添加仓库名称
        rowData.add(getValueOrEmpty(packAnalysisBillDTO.getWarehouseName()));

        // 动态维度字段值
        analysisDimensionSortOrder.forEach(it -> {
            if (analysisDimensions.contains(it)) {
                ColumnConfig config = DIMENSION_CONFIG_MAP.get(it);
                if (config != null) {
                    Object value = config.valueExtractor.apply(packAnalysisBillDTO);
                    rowData.add(getValueOrEmpty(value));
                }
            }
        });
        //下面一定有返回值
        rowData.add(packAnalysisBillDTO.getOrderCount());
        rowData.add(packAnalysisBillDTO.getSkuQtySum());
        rowData.add(packAnalysisBillDTO.getCreatedOrderCount());
        rowData.add(packAnalysisBillDTO.getPretreatmentFailOrderCount());
        rowData.add(packAnalysisBillDTO.getPretreatmentCompleteOrderCount());
        rowData.add(packAnalysisBillDTO.getCollectedOrderCount());
        rowData.add(packAnalysisBillDTO.getCheckStartOrderCount());
        rowData.add(packAnalysisBillDTO.getCheckCompleteOrderCount());
//        rowData.add(packAnalysisBillDTO.getPartialOutOrderCount());
        rowData.add(packAnalysisBillDTO.getOutOrderCount());
        rowData.add(packAnalysisBillDTO.getInterceptOrderCount());
        rowData.add(packAnalysisBillDTO.getCancelOrderCount());
        rowData.add(packAnalysisBillDTO.getShortageOutOrderCount());
        return rowData;
    }

    /**
     * 列配置类，用于存储表头名称和对应的值提取函数
     */
    private static class ColumnConfig {
        final String headerName;
        final Function<PackAnalysisBillDTO, Object> valueExtractor;

        ColumnConfig(String headerName, Function<PackAnalysisBillDTO, Object> valueExtractor) {
            this.headerName = headerName;
            this.valueExtractor = valueExtractor;
        }
    }

    // 维度字段配置映射
    private static final Map<String, ColumnConfig> DIMENSION_CONFIG_MAP = new LinkedHashMap<>();

    static {
        // 保持插入顺序的配置
        DIMENSION_CONFIG_MAP.put("cargoCode", new ColumnConfig("货主", dto -> dto.getCargoName()));
        DIMENSION_CONFIG_MAP.put("businessType", new ColumnConfig("业务类型", PackAnalysisBillDTO::getBusinessType));
        DIMENSION_CONFIG_MAP.put("salePlatform", new ColumnConfig("平台", dto -> dto.getSalePlatformName()));
        DIMENSION_CONFIG_MAP.put("packageStruct", new ColumnConfig("订单结构", dto -> dto.getPackageStructName()));
        DIMENSION_CONFIG_MAP.put("carrierCode", new ColumnConfig("快递", dto -> dto.getCarrierName()));
        DIMENSION_CONFIG_MAP.put("expOutStockDate_day", new ColumnConfig("预计出库时间（天）", PackAnalysisBillDTO::getExpOutStockDate_day));
        DIMENSION_CONFIG_MAP.put("expOutStockDate_hour", new ColumnConfig("预计出库时间（小时）", PackAnalysisBillDTO::getExpOutStockDate_hour));
        DIMENSION_CONFIG_MAP.put("createTime_day", new ColumnConfig("创建时间（天）", PackAnalysisBillDTO::getCreatedTime_day));
        DIMENSION_CONFIG_MAP.put("createTime_hour", new ColumnConfig("创建时间（小时）", PackAnalysisBillDTO::getCreatedTime_hour));
        DIMENSION_CONFIG_MAP.put("payDate_day", new ColumnConfig("付款时间（天）", PackAnalysisBillDTO::getPayDate_day));
        DIMENSION_CONFIG_MAP.put("payDate_hour", new ColumnConfig("付款时间（小时）", PackAnalysisBillDTO::getPayDate_hour));
        DIMENSION_CONFIG_MAP.put("outStockDate_day", new ColumnConfig("出库时间（天）", PackAnalysisBillDTO::getOutStockDate_day));
        DIMENSION_CONFIG_MAP.put("outStockDate_hour", new ColumnConfig("出库时间（小时）", PackAnalysisBillDTO::getOutStockDate_hour));
        DIMENSION_CONFIG_MAP.put("receiverProvName", new ColumnConfig("省份", PackAnalysisBillDTO::getReceiverProvName));
        DIMENSION_CONFIG_MAP.put("receiverCityName", new ColumnConfig("城市", PackAnalysisBillDTO::getReceiverCityName));
        DIMENSION_CONFIG_MAP.put("receiverAreaName", new ColumnConfig("区/县", PackAnalysisBillDTO::getReceiverAreaName));
    }

    // 分析维度排序
    private static final List<String> sortList = Arrays.asList(
            "cargoCode", "businessType", "salePlatform", "packageStruct",
            "expOutStockDate_day", "expOutStockDate_hour", "createTime_day",
            "createTime_hour", "payDate_day", "payDate_hour", "outStockDate_day",
            "outStockDate_hour", "carrierCode",
//            "receiverProvName", "receiverCityName", "receiverAreaName",
            "createdOrderCount", "orderCount", "pretreatmentFailOrderCount",
            "pretreatmentCompleteOrderCount", "collectedOrderCount", "checkStartOrderCount",
            "checkCompleteOrderCount", "partialOutOrderCount", "outOrderCount", "skuQtySum",
            "interceptOrderCount", "cancelOrderCount", "shortageOutOrderCount"
    );

    // 固定统计字段表头
    private static final List<String> STATIC_HEADERS = Arrays.asList(
            "订单总数", "商品件数", "创建状态订单数", "预处理失败订单数",
            "预处理完成订单数", "已汇总订单数", "复核开始订单数", "复核完成订单数",
            "部分出库订单数", "已出库订单数", "拦截订单数", "取消订单数", "缺货出库订单数"
    );
    // 分析维度排序
    private static final List<String> analysisDimensionSortOrder = Arrays.asList(
            "cargoCode", "businessType", "salePlatform", "carrierCode", "packageStruct",
            "expOutStockDate_day", "expOutStockDate_hour", "createTime_day",
            "createTime_hour", "payDate_day", "payDate_hour", "outStockDate_day",
            "outStockDate_hour", "receiverProvName", "receiverCityName", "receiverAreaName"
    );

    /**
     * 构建表头列表
     *
     * @param analysisDimensions 分析维度列表
     * @return 表头列表
     */
    private static List<List<String>> buildHeadList(List<String> analysisDimensions) {
        List<List<String>> head = new ArrayList<>();

        // 添加固定字段
        head.add(Collections.singletonList("仓库"));

        //添加动态维度字段
        analysisDimensionSortOrder.forEach(it -> {
            if (analysisDimensions.contains(it)) {
                String headerName = DIMENSION_CONFIG_MAP.get(it).headerName;
                head.add(Collections.singletonList(headerName));
            }
        });

        // 添加固定统计字段
        STATIC_HEADERS.stream()
                .map(Collections::singletonList)
                .forEach(head::add);

        return head;
    }
}
