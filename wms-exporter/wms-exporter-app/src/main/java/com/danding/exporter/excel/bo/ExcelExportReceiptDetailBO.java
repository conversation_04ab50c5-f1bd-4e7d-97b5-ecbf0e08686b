package com.danding.exporter.excel.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/12/24 10:18
 */
@Data
public class ExcelExportReceiptDetailBO implements Serializable {

    @ExcelProperty(value = "收货作业批次号")
    @ColumnWidth(20)
    private String recId;

    @ExcelProperty(value = "入库通知单号")
    @ColumnWidth(20)
    private String asnId;

    @ExcelProperty(value = "ERP单号")
    @ColumnWidth(20)
    private String soNo;

    @ExcelProperty(value = "上游单号")
    @ColumnWidth(20)
    private String poNo;

    @ExcelProperty(value = "货主")
    @ColumnWidth(20)
    private String cargoName;

    @ExcelProperty(value = "业务类型")
    @ColumnWidth(10)
    private String typeName;

    @ExcelProperty(value = "单据状态")
    @ColumnWidth(10)
    private String statusName;

    @ExcelProperty(value = "容器号")
    @ColumnWidth(15)
    private String contCode;

    @ExcelProperty(value = "托盘号")
    @ColumnWidth(15)
    private String palletCode;

    @ExcelProperty(value = "商品代码")
    @ColumnWidth(20)
    private String skuCode;

    @ExcelProperty(value = "商品条码")
    @ColumnWidth(20)
    private String upcCode;

    @ExcelProperty(value = "商品名称")
    @ColumnWidth(30)
    private String skuName;

    @ExcelProperty(value = "商品数量")
    @ColumnWidth(10)
    private String skuQty;

    @ExcelProperty(value = "批次ID")
    @ColumnWidth(20)
    private String skuLotNo;

    @ExcelProperty(value = "商品属性")
    @ColumnWidth(10)
    private String skuQualityName;



    @ExcelProperty(value = "外部批次编码")
    @ColumnWidth(20)
    private String externalSkuLotNo;

    @ExcelProperty(value = "入库日期")
    @ColumnWidth(20)
    private String receiveDate;

    @ExcelProperty(value = "生产日期")
    @ColumnWidth(20)
    private String manufDate;

    @ExcelProperty(value = "失效日期")
    @ColumnWidth(20)
    private String expireDate;

    @ExcelProperty(value = "生产批次号")
    @ColumnWidth(25)
    private String productionNo;

    @ExcelProperty(value = "入库关联号")
    @ColumnWidth(25)
    private String externalLinkBillNo;

    @ExcelProperty(value = "残次等级")
    @ColumnWidth(10)
    private String inventoryTypeName;

    @ExcelProperty(value = "暗码")
    @ColumnWidth(25)
    private String validityCode;

    @ExcelProperty(value = "托盘号(属性)")
    @ColumnWidth(25)
    private String palletCodeDetail;

    @ExcelProperty(value = "箱码")
    @ColumnWidth(25)
    private String boxCode;

    @ExcelProperty(value = "禁售日期")
    @ColumnWidth(25)
    private String withdrawDate;

    @ExcelProperty(value = "收货库位")
    @ColumnWidth(10)
    private String locationCode;

    @ExcelProperty(value = "异常ID")
    @ColumnWidth(10)
    private String abnormalId;

    @ExcelProperty(value = "总体积(cm³)")
    @ColumnWidth(10)
    private String volume;

    @ExcelProperty(value = "总毛重(kg)")
    @ColumnWidth(10)
    private String grossWeight;

    @ExcelProperty(value = "收货方式")
    @ColumnWidth(10)
    private String recFlag;

    @ExcelProperty(value = "上架完成时间")
    @ColumnWidth(20)
    private String completeShelfTime;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(20)
    private String createdTime;

    @ExcelProperty(value = "创建人")
    @ColumnWidth(10)
    private String createdBy;

    @ExcelProperty(value = "修改时间")
    @ColumnWidth(20)
    private String updatedTime;

    @ExcelProperty(value = "修改人")
    @ColumnWidth(10)
    private String updatedBy;
}
