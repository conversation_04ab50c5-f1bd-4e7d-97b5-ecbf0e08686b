package com.danding.exporter.excel.exporter;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.exporter.asn.dto.data.AsnDTO;
import com.danding.exporter.database.receipt.entity.Receipt;
import com.danding.exporter.database.receipt.mapper.ReceiptMapper;
import com.danding.exporter.database.receipt.util.ReceiptUtil;
import com.danding.exporter.excel.ExcelExportEnum;
import com.danding.exporter.excel.bo.ExcelExportReceiptDetailBO;
import com.danding.exporter.receipt.dto.ReceiptQry;
import com.danding.exporter.rpc.CargoOwnerRpcMapper;
import com.danding.exporter.rpc.DecimalPlaceRpcMapper;
import com.danding.exporter.rpc.ReceiptDetailRpcMapper;
import com.danding.exporter.rpc.SkuLotRpcMapper;
import com.dt.component.common.enums.SortEnum;
import com.dt.component.common.enums.asn.AsnTypeEnum;
import com.dt.component.common.enums.rec.ReceiptStatusEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.DecimalPlaceDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.bill.dto.ReceiptDetailDTO;
import com.dt.domain.bill.param.ReceiptDetailParam;
import com.dt.platform.utils.ConverterUtil;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-16 10:56
 */
@Component("receiptDetailWriteEventExporter")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ReceiptDetailExporter extends AbstractDefaultExporter<Receipt, ExcelExportReceiptDetailBO> {


    @Resource
    private CargoOwnerRpcMapper cargoOwnerRpcMapper;
    @Resource
    ReceiptMapper receiptMapper;
    @Resource
    SkuLotRpcMapper skuLotRpcMapper;
    @Resource
    ReceiptUtil receiptUtil;
    @Resource
    DecimalPlaceRpcMapper decimalPlaceRpcMapper;
    @Resource
    ReceiptDetailRpcMapper receiptDetailRpcMapper;
    @Resource
    com.danding.exporter.asn.api.IAsnService iAsnService;
    @Resource
    ReceiptHelper receiptHelper;

    @Override
    public CommonExcelHandler<Receipt, ExcelExportReceiptDetailBO> createHandler() {
        return list -> {
            Map<String, Receipt> receiptMap = list.stream()
                    .collect(Collectors.toMap(Receipt::getRecId, Function.identity()));

            // 2.查询收货作业批次明细列表
            List<String> recIdList = list.stream().map(Receipt::getRecId)
                    .collect(Collectors.toList());
            ReceiptDetailParam receiptDetailParam = new ReceiptDetailParam();
            receiptDetailParam.setRecIdList(recIdList);
            receiptDetailParam.setSortParamMap(MapUtil.of("rec_id", SortEnum.DESC.getSort()));
            List<ReceiptDetailDTO> clientList = new ArrayList<>();
            receiptDetailParam.setSize(5000);
            Page<ReceiptDetailDTO> receiptDetailDTOPage = receiptDetailRpcMapper
                    .getPage(receiptDetailParam);
//          for (int i = 0; i < receiptDetailDTOPage.getPages(); i++) {
            for (int i = 1; i <= receiptDetailDTOPage.getPages(); i++) {
                receiptDetailParam.setCurrent(i);
                receiptDetailParam.setSize(5000);
                Page<ReceiptDetailDTO> receiptDetailDTOPageNew = receiptDetailRpcMapper
                        .getPage(receiptDetailParam);
                clientList.addAll(receiptDetailDTOPageNew.getRecords());
            }
            // 3.查询货主档案列表
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            List<String> cargoCodeList = clientList.stream().map(ReceiptDetailDTO::getCargoCode).distinct()
                    .collect(Collectors.toList());
            cargoOwnerParam.setCodeList(cargoCodeList);
            Map<String, CargoOwnerDTO> cargoOwnerMap = cargoOwnerRpcMapper.getList(cargoOwnerParam)
                    .stream()
                    .collect(Collectors.toMap(CargoOwnerDTO::getCode, Function.identity()));

            // 4.查询货主配置
            Map<String, DecimalPlaceDTO> decimalPlaceMap = decimalPlaceRpcMapper.getList(cargoCodeList)
                    .stream()
                    .collect(Collectors.toMap(DecimalPlaceDTO::getCargoCode, Function.identity()));
            List<String> ansIds = clientList.stream().map(ReceiptDetailDTO::getAsnId).collect(Collectors.toList());
            List<com.danding.exporter.asn.dto.data.AsnDTO> asnDTOList = iAsnService.getAppointMultiple(ansIds, Arrays.asList("asn_id", "so_no", "po_no"));

            // 查询外部批次编码
            SkuLotParam skuLotParam = new SkuLotParam();
            List<String> skuLotNoList = clientList.stream().map(ReceiptDetailDTO::getSkuLotNo)
                    .collect(Collectors.toList());
            List<String> skuCodeList = clientList.stream().map(ReceiptDetailDTO::getSkuCode)
                    .collect(Collectors.toList());
            skuLotParam.setCodeList(skuLotNoList);
            skuLotParam.setCargoCodeList(cargoCodeList);
            skuLotParam.setSkuCodeList(skuCodeList);
            Map<String, SkuLotDTO> skuLotDTOMap = skuLotRpcMapper.getList(skuLotParam)
                    .stream()
                    .collect(Collectors.toMap(
                            it -> StrUtil.join(StrUtil.COLON, it.getCargoCode(), it.getSkuCode(), it.getCode()),
                            Function.identity(), (k1, k2) -> k1));
            // 5.转换为 ExcelExportBOList
            return clientList.stream()
                    .map(receiptDetailDTO -> this
                            .getReceiptDetail(receiptDetailDTO, receiptMap, cargoOwnerMap, decimalPlaceMap, skuLotDTOMap, asnDTOList))
                    .collect(Collectors.toList());

        };
    }

    @Override
    public Cursor<Receipt> getCursor(Map<String, Object> param) {
        return receiptHelper.getCursor(param);
    }


    @Override
    public String getSheetName() {
        return ExcelExportEnum.EXCEL_EXPORT_RECEIPT_DETAIL.getFuncName();
    }

    private ExcelExportReceiptDetailBO getReceiptDetail(ReceiptDetailDTO receiptDetailDTO,
                                                        Map<String, Receipt> receiptMap,
                                                        Map<String, CargoOwnerDTO> cargoOwnerMap,
                                                        Map<String, DecimalPlaceDTO> decimalPlaceMap,
                                                        Map<String, SkuLotDTO> skuLotDTOMap,
                                                        List<AsnDTO> asnDTOList) {

        ExcelExportReceiptDetailBO exportBO = new ExcelExportReceiptDetailBO();
        BeanUtils.copyProperties(receiptDetailDTO, exportBO);

        Receipt receiptDTO = receiptMap.get(receiptDetailDTO.getRecId());
        AsnDTO asnDTO = asnDTOList.stream().filter(a -> a.getAsnId().equalsIgnoreCase(receiptDetailDTO.getAsnId())).findFirst().orElse(null);
        if (asnDTO != null) {
            exportBO.setSoNo(asnDTO.getSoNo());
        } else {
            exportBO.setSoNo("");
        }
        exportBO.setRecFlag(receiptDTO.getRecFlag());
        exportBO.setCompleteShelfTime(ConverterUtil.convertVoTime(receiptDTO.getCompleteShelfDate()));
        exportBO.setTypeName(AsnTypeEnum.fromCode(receiptDTO.getType()).getMessage());
        exportBO.setSkuQualityName(SkuQualityEnum.getEnum(receiptDetailDTO.getSkuQuality()).getMessage());

        exportBO.setInventoryTypeName("");
        if (!StringUtils.isEmpty(receiptDetailDTO.getInventoryType())) {
            exportBO.setInventoryTypeName(InventoryTypeEnum.getEnum(receiptDetailDTO.getInventoryType()).getMessage());
        }

        exportBO.setStatusName(ReceiptStatusEnum.fromCode(receiptDTO.getStatus()).getMessage());
        exportBO.setCreatedTime(ConverterUtil.convertVoTime(receiptDTO.getCreatedTime()));
        exportBO.setCreatedBy(receiptDTO.getCreatedBy());
        exportBO.setPalletCode(receiptDTO.getPalletCode());

        exportBO.setAbnormalId(receiptDetailDTO.getExtNo());

        exportBO.setUpdatedTime(ConverterUtil.convertVoTime(receiptDTO.getUpdatedTime()));
        exportBO.setUpdatedBy(receiptDTO.getUpdatedBy());
        if (!StringUtils.isEmpty(receiptDetailDTO.getCargoCode())) {
            CargoOwnerDTO cargoOwnerDTO = cargoOwnerMap.get(receiptDetailDTO.getCargoCode());
            if (cargoOwnerDTO != null) {
                exportBO.setCargoName(cargoOwnerDTO.getName());
            }
        }

        DecimalPlaceDTO decimalPlaceDTO = decimalPlaceMap.get(receiptDetailDTO.getCargoCode());
        //总重量
        exportBO.setGrossWeight(
                receiptDetailDTO.getGrossWeight().setScale(decimalPlaceDTO.getWeight(), RoundingMode.FLOOR)
                        .toString());
        //总体积
        exportBO.setVolume(
                receiptDetailDTO.getVolume().setScale(decimalPlaceDTO.getVolume(), RoundingMode.FLOOR)
                        .toString());
        exportBO.setSkuQty(
                receiptDetailDTO.getSkuQty().setScale(decimalPlaceDTO.getNumber(), RoundingMode.FLOOR)
                        .toString());

        exportBO.setReceiveDate(ConverterUtil
                .convertVoTime(receiptDetailDTO.getReceiveDate(), receiptDetailDTO.getReceiveDateFormat()));
        exportBO.setManufDate(ConverterUtil
                .convertVoTime(receiptDetailDTO.getManufDate(), receiptDetailDTO.getManufDateFormat()));
        exportBO.setExpireDate(ConverterUtil
                .convertVoTime(receiptDetailDTO.getExpireDate(), receiptDetailDTO.getExpireDateFormat()));
        exportBO.setWithdrawDate(ConverterUtil.convertVoTime(receiptDetailDTO.getWithdrawDate(),
                receiptDetailDTO.getWithdrawDateFormat()));
        // 外部批次编码
        SkuLotDTO skuLotDTO = skuLotDTOMap.get(StrUtil
                .join(StrUtil.COLON, receiptDetailDTO.getCargoCode(), receiptDetailDTO.getSkuCode(),
                        receiptDetailDTO.getSkuLotNo()));
        if (skuLotDTO != null) {
            exportBO.setExternalSkuLotNo(skuLotDTO.getExternalSkuLotNo());
            exportBO.setExternalLinkBillNo(skuLotDTO.getExternalLinkBillNo());
            exportBO.setValidityCode(skuLotDTO.getValidityCode());
            exportBO.setPalletCodeDetail(skuLotDTO.getPalletCode());
            exportBO.setBoxCode(skuLotDTO.getBoxCode());
        }
        return exportBO;
    }

}
