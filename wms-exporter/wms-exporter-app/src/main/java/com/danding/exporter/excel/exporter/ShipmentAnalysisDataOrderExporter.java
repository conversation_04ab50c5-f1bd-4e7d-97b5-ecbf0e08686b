package com.danding.exporter.excel.exporter;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.business.client.rpc.goods.center.param.GoodsRpcQueryParam;
import com.danding.business.client.rpc.goods.center.result.GoodsManagementRpcResult;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.exporter.database.replenish.entity.ReplenishTask;
import com.danding.exporter.database.shipment.entity.ShipmentOrder;
import com.danding.exporter.excel.ExcelExportEnum;
import com.danding.exporter.excel.bo.ExcelExportReplenishTaskBO;
import com.danding.exporter.excel.bo.ShipmentOrderBO;
import com.danding.exporter.replenish.ReplenishRecommendParam;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.ship.ShipmentAnalysisBillDTO;
import com.dt.domain.bill.param.ship.ShipmentAnalysisBillParam;
import com.dt.elasticsearch.wms.dto.ShipmentAnalysisDTO;
import com.dt.elasticsearch.wms.param.ShipmentAnalysisParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.*;
import com.dt.platform.wms.dto.base.CarrierBizDTO;
import com.dt.platform.wms.dto.base.WarehouseBizDTO;
import com.dt.platform.wms.dto.cargo.CargoOwnerBizDTO;
import com.dt.platform.wms.dto.sale.SalePlatformBizDTO;
import com.dt.platform.wms.param.cargo.CargoOwnerBizParam;
import com.dt.platform.wms.param.carrier.CarrierBizParam;
import com.dt.platform.wms.param.sale.SalePlatformQueryBizParam;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-09 10:56
 */
@Component("shipmentAnalysisProductDataOrderExporter")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ShipmentAnalysisDataOrderExporter extends AbstractDefaultExporter<ShipmentOrder, ShipmentOrderBO> {


    @DubboReference
    private IAreaBizClient areaBizClient;

    @DubboReference
    private IWarehouseBizClient iWarehouseBizClient;

    @DubboReference
    private ICargoOwnerBizClient cargoOwnerBizClient;

    @DubboReference
    private ICarrierBizClient iCarrierBizClient;

    @DubboReference
    private ISalePlatformBizClient salePlatformBizClient;

    @DubboReference
    private IShipmentOrderBizClient shipmentOrderBizClient;

    @Override
    public CommonExcelHandler<ShipmentOrder, ShipmentOrderBO> createHandler() {
        return null;
    }

    @Override
    public Cursor<ShipmentOrder> getCursor(Map<String, Object> param) {
        return null;
    }

    @Override
    public String getSheetName() {
        return ExcelExportEnum.EXPORT_SHIPMENT_ANALYSIS_REPORT.getFuncName();
    }

    @Override
    public boolean isCursorQuery() {
        return Boolean.FALSE;
    }


    @Override
    @DS("#DTWMS")
    public File doExport() {
        File file = new File(UUID.randomUUID().toString());

        ExcelWriter excelWriter = EasyExcel.write(file).build();

        ShipmentAnalysisParam shipmentAnalysisParam = JSON.parseObject(JSON.toJSONString(param), ShipmentAnalysisParam.class);
        if (shipmentAnalysisParam != null) {

            if (CollectionUtils.isEmpty(shipmentAnalysisParam.getAnalysisDimensions())) {
                shipmentAnalysisParam.setAnalysisDimensions(new ArrayList<>());
            }
            //add header`
            List<List<String>> head = buildHeadList(shipmentAnalysisParam.getAnalysisDimensions());

            WriteSheet writeSheet = EasyExcel.writerSheet(0, "生产进度").head(head).build();

            validateCreateTimeParam(shipmentAnalysisParam);

            List<List<Object>> exportDataList = getExportDataList(shipmentAnalysisParam);

            excelWriter.write(exportDataList, writeSheet);
        }
        excelWriter.finish();

        return file;
    }

    /**
     * 获取导出数据列表
     *
     * @param shipmentAnalysisParam 查询参数
     * @return 导出数据列表
     */
    public List<List<Object>> getExportDataList(ShipmentAnalysisParam shipmentAnalysisParam) {
        //行级数据
        List<List<Object>> data = new ArrayList<>();

        if (shipmentAnalysisParam == null) {
            return data;
        }

        List<ShipmentAnalysisBillDTO> shipmentAnalysisBillDTOList = new ArrayList<>();
        //数据来源
        ShipmentAnalysisBillParam shipmentAnalysisBillParam = ConverterUtil.convert(shipmentAnalysisParam, ShipmentAnalysisBillParam.class);
        shipmentAnalysisBillParam.setPageSize(2000);
        Page<ShipmentAnalysisBillDTO> shipmentAnalysisPage = shipmentOrderBizClient.getShipmentAnalysisPage(shipmentAnalysisBillParam).getData();
        for (int i = 1; i <= shipmentAnalysisPage.getPages(); i++) {
            shipmentAnalysisBillParam.setCurrentPage(i);
            shipmentAnalysisBillParam.setPageSize(2000);
            shipmentAnalysisPage = shipmentOrderBizClient.getShipmentAnalysisPage(shipmentAnalysisBillParam).getData();
            if (!CollectionUtils.isEmpty(shipmentAnalysisPage.getRecords())) {
                shipmentAnalysisBillDTOList.addAll(shipmentAnalysisPage.getRecords());
            }
        }
        if (!CollectionUtils.isEmpty(shipmentAnalysisBillDTOList)) {

            List<ShipmentAnalysisDTO> shipmentAnalysisDTORowList = ConverterUtil.convertList(shipmentAnalysisBillDTOList, ShipmentAnalysisDTO.class);
            buildVo(shipmentAnalysisDTORowList);
            //add rows
            for (ShipmentAnalysisDTO shipmentAnalysisDTO : shipmentAnalysisDTORowList) {
                List<Object> rowData = buildRowData(shipmentAnalysisDTO, shipmentAnalysisParam.getAnalysisDimensions());
                data.add(rowData);
            }
        }
//        //add total row
//        ShipmentAnalysisDTO shipmentAnalysisDTTotal = BuildData.buildShipmentAnalysisDTOTotal();
//        List<Object> rowData = buildRowData(shipmentAnalysisDTTotal, analysisDimensions);
//        data.add(rowData);

        return data;
    }


    /**
     * 获取货主映射
     *
     * @param dataList 数据列表
     * @return 货主映射
     */
    private Map<String, CargoOwnerBizDTO> getCargoOwnerMap(List<ShipmentAnalysisDTO> dataList) {
        CargoOwnerBizParam cargoOwnerBizParam = new CargoOwnerBizParam();
        cargoOwnerBizParam.setCodeList(dataList.stream().map(ShipmentAnalysisDTO::getCargoCode).distinct().collect(Collectors.toList()));
        List<CargoOwnerBizDTO> cargoOwnerBizDTOList = cargoOwnerBizClient.queryList(cargoOwnerBizParam).getData();
        return cargoOwnerBizDTOList.stream().collect(Collectors.toMap(CargoOwnerBizDTO::getCode, Function.identity(), (a, b) -> a));
    }

    /**
     * 获取快递公司映射
     *
     * @param dataList 数据列表
     * @return 快递公司映射
     */
    private Map<String, CarrierBizDTO> getCarrierMap(List<ShipmentAnalysisDTO> dataList) {
        CarrierBizParam carrierBizParam = new CarrierBizParam();
        carrierBizParam.setCodeList(dataList.stream().map(ShipmentAnalysisDTO::getCarrierCode).distinct().collect(Collectors.toList()));
        List<CarrierBizDTO> carrierBizDTOList = iCarrierBizClient.getList(carrierBizParam).getData();
        return carrierBizDTOList.stream().collect(Collectors.toMap(CarrierBizDTO::getCode, Function.identity(), (a, b) -> a));
    }

    /**
     * 获取平台映射
     *
     * @param dataList 数据列表
     * @return 平台映射
     */
    private Map<String, SalePlatformBizDTO> getSalePlatformMap(List<ShipmentAnalysisDTO> dataList) {
        SalePlatformQueryBizParam platformQueryBizParam = new SalePlatformQueryBizParam();
        platformQueryBizParam.setCodeList(dataList.stream().map(ShipmentAnalysisDTO::getSalePlatform).distinct().collect(Collectors.toList()));
        List<SalePlatformBizDTO> platformBizDTOList = salePlatformBizClient.getList(platformQueryBizParam).getData();
        return platformBizDTOList.stream().collect(Collectors.toMap(SalePlatformBizDTO::getCode, Function.identity(), (a, b) -> a));
    }

    /**
     * 构建VO
     *
     * @param dataList
     */
    private void buildVo(List<ShipmentAnalysisDTO> dataList) {
        //获取仓库名称
        WarehouseBizDTO warehouseBizDTO = iWarehouseBizClient.queryByCode(CurrentRouteHolder.getWarehouseCode()).getData();

        Map<String, CargoOwnerBizDTO> cargoOwnerMap = getCargoOwnerMap(dataList);
        Map<String, CarrierBizDTO> carrierMap = getCarrierMap(dataList);
        Map<String, SalePlatformBizDTO> platformMap = getSalePlatformMap(dataList);

        dataList.forEach(a -> {
            a.setWarehouseName(warehouseBizDTO != null ? warehouseBizDTO.getName() : "");
            a.setCargoName(cargoOwnerMap.getOrDefault(a.getCargoCode(), new CargoOwnerBizDTO()).getName());
            a.setCarrierName(carrierMap.getOrDefault(a.getCarrierCode(), new CarrierBizDTO()).getName());
            a.setSalePlatformName(platformMap.getOrDefault(a.getSalePlatform(), new SalePlatformBizDTO()).getName());
            if (a.getPackageStruct() != null) {
                a.setPackageStructName(ShipmentOrderEnum.PACKAGE_STRUCT.findOrderSkuType(a.getPackageStruct()).getDesc());
            }
            //创建小时
            if (StringUtils.isNotEmpty(a.getCreatedTime_hour())) {
                a.setCreatedTime_hour(a.getCreatedTime_hour() + ":00:00");
            }
            //预计出库时间小时
            if (StringUtils.isNotEmpty(a.getExpOutStockDate_hour())) {
                a.setExpOutStockDate_hour(a.getExpOutStockDate_hour() + ":00:00");
            }
            //出库时间小时
            if (StringUtils.isNotEmpty(a.getOutStockDate_hour())) {
                a.setOutStockDate_hour(a.getOutStockDate_hour() + ":00:00");
            }
            //付款时间小时
            if (StringUtils.isNotEmpty(a.getPayDate_hour())) {
                a.setPayDate_hour(a.getPayDate_hour() + ":00:00");
            }
        });
    }

    /**
     * 校验创建时间参数
     * 创建时间必填，且跨度不能超过一个自然月
     *
     * @param param 查询参数
     * @return 校验结果，如果有错误返回错误结果，否则返回 null
     */
    private void validateCreateTimeParam(ShipmentAnalysisParam param) {

        // 校验创建时间参数
        if (param.getCreateTimeStart() == null || param.getCreateTimeEnd() == null) {
            param.setCreatedTimeStart(100L);
            param.setCreatedTimeEnd(100L);
            return;
        }
        //创建时间
        checkCreateTimeRange(param);

        //字段转换
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getField()) && sortList.contains(param.getField())) {
            param.setSortField(param.getField());
        }
        //字段转换
        if (org.apache.commons.lang.StringUtils.isNotEmpty(param.getOrder()) && Objects.equals(param.getOrder(), "ascend")) {
            param.setSortOrder("ASC");
        }
        if (StringUtils.isNotEmpty(param.getOrder()) && Objects.equals(param.getOrder(), "descend")) {
            param.setSortOrder("DESC");
        }

        Map<String, List<String>> mapAreaMap = areaBizClient.handAreaCascade(param.getReceiverProvList(), param.getReceiverCityList(), param.getReceiverAreaList());
        if (!CollectionUtils.isEmpty(mapAreaMap)) {
            param.setReceiverProvList(mapAreaMap.getOrDefault("receiverProvList", null));
            param.setReceiverCityList(mapAreaMap.getOrDefault("receiverCityList", null));
            param.setReceiverAreaList(mapAreaMap.getOrDefault("receiverAreaList", null));
        }


    }

    /**
     * 校验创建时间范围
     * 这里可以添加具体的逻辑来检查创建时间范围是否符合业务要求
     *
     * @param param 查询参数
     */
    private void checkCreateTimeRange(ShipmentAnalysisParam param) {
        // 计算时间跨度（毫秒）
        long timeSpan = param.getCreateTimeEnd() - param.getCreateTimeStart();
        // 一个自然月的最大毫秒数（按31天计算）
        long maxMonthSpan = 31L * 24 * 60 * 60 * 1000;

        if (timeSpan <= 0) {
            param.setCreatedTimeStart(100L);
            param.setCreatedTimeEnd(100L);
            return;
        }

        if (timeSpan > maxMonthSpan) {
            param.setCreatedTimeStart(100L);
            param.setCreatedTimeEnd(100L);
            return;
        }
    }

    /**
     * 获取值或空字符串
     *
     * @param value 值
     * @return 如果值为null，则返回空字符串，否则返回原值
     */
    private static Object getValueOrEmpty(Object value) {
        return value == null ? "" : value;
    }

    /**
     * 构建行数据
     *
     * @param shipmentAnalysisDTO 数据对象
     * @param analysisDimensions  分析维度列表
     * @return 行数据列表
     */
    private static List<Object> buildRowData(ShipmentAnalysisDTO shipmentAnalysisDTO, List<String> analysisDimensions) {
        List<Object> rowData = new ArrayList<>();
        //添加仓库名称
        rowData.add(getValueOrEmpty(shipmentAnalysisDTO.getWarehouseName()));

        // 动态维度字段值
        analysisDimensionSortOrder.forEach(it -> {
            if (analysisDimensions.contains(it)) {
                ColumnConfig config = DIMENSION_CONFIG_MAP.get(it);
                if (config != null) {
                    Object value = config.valueExtractor.apply(shipmentAnalysisDTO);
                    rowData.add(getValueOrEmpty(value));
                }
            }
        });
        //下面一定有返回值
        rowData.add(shipmentAnalysisDTO.getOrderCount());
        rowData.add(shipmentAnalysisDTO.getSkuQtySum());
        rowData.add(shipmentAnalysisDTO.getCreatedOrderCount());
        rowData.add(shipmentAnalysisDTO.getPretreatmentFailOrderCount());
        rowData.add(shipmentAnalysisDTO.getPretreatmentCompleteOrderCount());
        rowData.add(shipmentAnalysisDTO.getCollectedOrderCount());
        rowData.add(shipmentAnalysisDTO.getCheckStartOrderCount());
        rowData.add(shipmentAnalysisDTO.getCheckCompleteOrderCount());
        rowData.add(shipmentAnalysisDTO.getPartialOutOrderCount());
        rowData.add(shipmentAnalysisDTO.getOutOrderCount());
        rowData.add(shipmentAnalysisDTO.getInterceptOrderCount());
        rowData.add(shipmentAnalysisDTO.getCancelOrderCount());
        rowData.add(shipmentAnalysisDTO.getShortageOutOrderCount());
        return rowData;
    }

    /**
     * 列配置类，用于存储表头名称和对应的值提取函数
     */
    private static class ColumnConfig {
        final String headerName;
        final Function<ShipmentAnalysisDTO, Object> valueExtractor;

        ColumnConfig(String headerName, Function<ShipmentAnalysisDTO, Object> valueExtractor) {
            this.headerName = headerName;
            this.valueExtractor = valueExtractor;
        }
    }

    // 维度字段配置映射
    private static final Map<String, ColumnConfig> DIMENSION_CONFIG_MAP = new LinkedHashMap<>();

    static {
        // 保持插入顺序的配置
        DIMENSION_CONFIG_MAP.put("cargoCode", new ColumnConfig("货主", dto -> dto.getCargoName()));
        DIMENSION_CONFIG_MAP.put("businessType", new ColumnConfig("业务类型", ShipmentAnalysisDTO::getBusinessType));
        DIMENSION_CONFIG_MAP.put("salePlatform", new ColumnConfig("平台", dto -> dto.getSalePlatformName()));
        DIMENSION_CONFIG_MAP.put("packageStruct", new ColumnConfig("订单结构", dto -> dto.getPackageStructName()));
        DIMENSION_CONFIG_MAP.put("carrierCode", new ColumnConfig("快递", dto -> dto.getCarrierName()));
        DIMENSION_CONFIG_MAP.put("expOutStockDate_day", new ColumnConfig("预计出库时间（天）", ShipmentAnalysisDTO::getExpOutStockDate_day));
        DIMENSION_CONFIG_MAP.put("expOutStockDate_hour", new ColumnConfig("预计出库时间（小时）", ShipmentAnalysisDTO::getExpOutStockDate_hour));
        DIMENSION_CONFIG_MAP.put("createTime_day", new ColumnConfig("创建时间（天）", ShipmentAnalysisDTO::getCreatedTime_day));
        DIMENSION_CONFIG_MAP.put("createTime_hour", new ColumnConfig("创建时间（小时）", ShipmentAnalysisDTO::getCreatedTime_hour));
        DIMENSION_CONFIG_MAP.put("payDate_day", new ColumnConfig("付款时间（天）", ShipmentAnalysisDTO::getPayDate_day));
        DIMENSION_CONFIG_MAP.put("payDate_hour", new ColumnConfig("付款时间（小时）", ShipmentAnalysisDTO::getPayDate_hour));
        DIMENSION_CONFIG_MAP.put("outStockDate_day", new ColumnConfig("出库时间（天）", ShipmentAnalysisDTO::getOutStockDate_day));
        DIMENSION_CONFIG_MAP.put("outStockDate_hour", new ColumnConfig("出库时间（小时）", ShipmentAnalysisDTO::getOutStockDate_hour));
        DIMENSION_CONFIG_MAP.put("receiverProvName", new ColumnConfig("省份", ShipmentAnalysisDTO::getReceiverProvName));
        DIMENSION_CONFIG_MAP.put("receiverCityName", new ColumnConfig("城市", ShipmentAnalysisDTO::getReceiverCityName));
        DIMENSION_CONFIG_MAP.put("receiverAreaName", new ColumnConfig("区/县", ShipmentAnalysisDTO::getReceiverAreaName));
    }

    // 分析维度排序
    private static final List<String> sortList = Arrays.asList(
            "cargoCode", "businessType", "salePlatform", "packageStruct",
            "expOutStockDate_day", "expOutStockDate_hour", "createTime_day",
            "createTime_hour", "payDate_day", "payDate_hour", "outStockDate_day",
            "outStockDate_hour", "carrierCode",
//            "receiverProvName", "receiverCityName", "receiverAreaName",
            "createdOrderCount", "orderCount", "pretreatmentFailOrderCount",
            "pretreatmentCompleteOrderCount", "collectedOrderCount", "checkStartOrderCount",
            "checkCompleteOrderCount", "partialOutOrderCount", "outOrderCount", "skuQtySum",
            "interceptOrderCount", "cancelOrderCount", "shortageOutOrderCount"
    );

    // 固定统计字段表头
    private static final List<String> STATIC_HEADERS = Arrays.asList(
            "订单总数", "商品件数", "创建状态订单数", "预处理失败订单数",
            "预处理完成订单数", "已汇总订单数", "复核开始订单数", "复核完成订单数",
            "部分出库订单数", "已出库订单数", "拦截订单数", "取消订单数", "缺货出库订单数"
    );
    // 分析维度排序
    private static final List<String> analysisDimensionSortOrder = Arrays.asList(
            "cargoCode", "businessType", "salePlatform", "carrierCode", "packageStruct",
            "expOutStockDate_day", "expOutStockDate_hour", "createTime_day",
            "createTime_hour", "payDate_day", "payDate_hour", "outStockDate_day",
            "outStockDate_hour", "receiverProvName", "receiverCityName", "receiverAreaName"
    );

    /**
     * 构建表头列表
     *
     * @param analysisDimensions 分析维度列表
     * @return 表头列表
     */
    private static List<List<String>> buildHeadList(List<String> analysisDimensions) {
        List<List<String>> head = new ArrayList<>();

        // 添加固定字段
        head.add(Collections.singletonList("仓库"));

        //添加动态维度字段
        analysisDimensionSortOrder.forEach(it -> {
            if (analysisDimensions.contains(it)) {
                String headerName = DIMENSION_CONFIG_MAP.get(it).headerName;
                head.add(Collections.singletonList(headerName));
            }
        });

        // 添加固定统计字段
        STATIC_HEADERS.stream()
                .map(Collections::singletonList)
                .forEach(head::add);

        return head;
    }
}
