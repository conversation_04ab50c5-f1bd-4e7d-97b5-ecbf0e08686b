<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>danding-business-wms</artifactId>
        <groupId>com.dt</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.danding.exporter</groupId>
    <artifactId>wms-exporter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>wms-exporter</name>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <mybatis-starter.version>2.1.0</mybatis-starter.version>
        <spring-boot.version>2.2.5.RELEASE</spring-boot.version>
        <mybatis-plus-boot-starter.version>3.2.0</mybatis-plus-boot-starter.version>
        <apache.dubbo.version>2.7.9</apache.dubbo.version>
        <alibaba.cloud.version>2.2.1.RELEASE</alibaba.cloud.version>
        <swagger.version>1.5.21</swagger.version>
        <fastjson.version>1.2.56</fastjson.version>
        <kryo.version>4.0.2</kryo.version>
        <kryo.serializers.version>0.45</kryo.serializers.version>
        <component.canal.version>1.0.0-SNAPSHOT</component.canal.version>
        <cola.components.version>4.4.0-SNAPSHOT</cola.components.version>
        <seata.version>1.3.0</seata.version>
        <ares.version>3.1.12-RELEASE</ares.version>

    </properties>

    <modules>
        <module>wms-exporter-client</module>
        <module>wms-exporter-adapter</module>
        <module>wms-exporter-app</module>
        <module>wms-exporter-domain</module>
        <module>wms-exporter-infrastructure</module>
        <module>start</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-rpc-configuration-client</artifactId>
                <version>${ares.version}</version>
            </dependency>
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <!-- rocketmq -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>2.1.1</version>
            </dependency>
            <!-- rocketmq -->
            <!--            canal 消费组件-->
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>dt-component-canal</artifactId>
                <version>${component.canal.version}</version>
            </dependency>
            <!--            canal 消费组件-->
            <!--文档注释-->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!--文档注释-->
            <!--       fastjson     -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--       fastjson     -->
            <!--       jackson     -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.10.2</version>
            </dependency>
            <!--       jackson     -->
            <!--            nacos-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${alibaba.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--            nacos-->
            <!--            dubbo-->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${apache.dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo</artifactId>
                <version>${kryo.version}</version>
            </dependency>
            <dependency>
                <groupId>de.javakaffee</groupId>
                <artifactId>kryo-serializers</artifactId>
                <version>${kryo.serializers.version}</version>
            </dependency>
            <!--            dubbo-->
            <!--            网关-->
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>soul-client-apache-dubbo</artifactId>
                <version>2.1.3-SNAPSHOT</version>
            </dependency>
            <!--            网关-->
            <!--            用户中心-->
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ucenter-client-dubbo</artifactId>
                <version>1.1.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ucenter-client-autoconfigure</artifactId>
                <version>1.1.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>uc-component-dubbo</artifactId>
                <version>1.1.4-SNAPSHOT</version>
            </dependency>
            <!--            用户中心-->

            <!--Project modules-->
            <dependency>
                <groupId>com.danding.exporter</groupId>
                <artifactId>wms-exporter-adapter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding.exporter</groupId>
                <artifactId>wms-exporter-app</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding.exporter</groupId>
                <artifactId>wms-exporter-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding.exporter</groupId>
                <artifactId>wms-exporter-domain</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding.exporter</groupId>
                <artifactId>wms-exporter-infrastructure</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!--Project modules End-->
            <!--Spring Boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--mybatis-plus 启动器-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>
            <!--Spring Boot End-->
            <!--Validation API-->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.0.22.Final</version>
            </dependency>
            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.web</groupId>
                <artifactId>javax.el</artifactId>
                <version>2.2.6</version>
            </dependency>
            <!--Validation API End -->
            <!-- Misc -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.22</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.4</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.alibaba</groupId>-->
            <!--                <artifactId>fastjson</artifactId>-->
            <!--                <version>1.2.55</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.1</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>dt-component-utils</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!-- Misc End -->
            <dependency>
                <groupId>com.danding.cola</groupId>
                <artifactId>danding-components-bom</artifactId>
                <version>${cola.components.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>danding</id>
            <name>danding release resp</name>
            <url>http://mvn.yang800.cn/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>danding</id>
            <name>danding snapshot resp</name>
            <url>http://mvn.yang800.cn/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>danding</id>
            <name>danding</name>
            <url>http://mvn.yang800.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
