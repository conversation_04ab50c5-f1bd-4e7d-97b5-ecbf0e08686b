package com.danding.exporter.excel;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum ExcelExportEnum {
    EXCEL_EXPORT_SN_STOCK_SERIAL("EXCEL_EXPORT_SN_STOCK_SERIAL", "SN库存流水", "SN库存流水.xlsx", "snStockSerialExporter"),
    EXCEL_EXPORT_SN_STOCK("EXCEL_EXPORT_SN_STOCK", "SN库存", "SN库存.xlsx", "snStockExporter"),
    EXCEL_EXPORT_SHIPMENT_ORDER("DT_SHIPMENT_ORDER_EXPORT", "出库订单", "出库订单.xlsx", "shipmentOrderWriteEventExporter"),
    EXPORT_SHIPMENT_ROW_ANALYSIS_REPORT("EXPORT_SHIPMENT_ROW_ANALYSIS_REPORT", "出库订单汇总", "出库订单汇总.xlsx", "shipmentAnalysisRowDataOrderExporter"),
    EXPORT_SHIPMENT_ANALYSIS_REPORT("EXPORT_SHIPMENT_ANALYSIS_REPORT", "出库生产进度", "出库生产进度.xlsx", "shipmentAnalysisProductDataOrderExporter"),

    EXPORT_PACK_ROW_ANALYSIS_REPORT("EXPORT_PACK_ROW_ANALYSIS_REPORT", "包裹订单汇总", "包裹订单汇总.xlsx", "packAnalysisRowDataOrderExporter"),
    EXPORT_PACK_ANALYSIS_REPORT("EXPORT_PACK_ANALYSIS_REPORT", "包裹生产进度", "包裹生产进度.xlsx", "packAnalysisProductDataOrderExporter"),


    EXCEL_EXPORT_SHIPMENT_NO_VIDEO_ORDER("DT_SHIPMENT_ORDER_NO_VIDEO_EXPORT", "淘天出库B2C单无视频", "淘天出库B2C单无视频.xlsx", "shipmentOrderNoVideoWriteEventExporter"),
    EXCEL_EXPORT_ABNORMAL_ORDER("DT_ABNORMAL_ORDER_EXPORT", "异常单", "异常单.xlsx", "abnormalWriteEventExporter"),
    EXCEL_ASN_ORDER("DT_ASN_EXPORT", "到货通知单明细", "到货通知单明细.xlsx", "asnWriteEventExporter"),
    EXCEL_EXPORT_INVENTORY_DETAIL("DT_EXCEL_EXPORT_INVENTORY_DETAIL", "导出盘点单明细", "导出盘点单明细.xlsx", "inventoryDetailWriteEventExporter"),
    EXCEL_EXPORT_LOCATION_EXPORT("EXCEL_EXPORT_LOCATION_EXPORT", "导出库位使用率报表", "导出库位使用率报表.xlsx", "locationExportEventExporter"),

    EXCEL_EXPORT_LOCATION_STATISTICS("DT_LOCATION_STATISTICS_EXPORT", "计费库位统计", "计费库位统计.xlsx", "locationStatisticsWriteEventExporter"),

    EXCEL_EXPORT_LOCATION_VOLUME("DT_LOCATION_VOLUME_EXPORT", "库位货物体积导出", "库位货物体积.xlsx",
            "locationVolumeWriteEventExporter"),

    EXCEL_EXPORT_PRE_PLAN("DT_PRE_PLAN_EXPORT", "预包计划导出", "预包计划导出.xlsx", "prePackagePlanWriteEventExporter"),
    EXCEL_EXPORT_PRE_PACKAGE_DETAIL("DT_PRE_PACKAGE_DETAIL_EXPORT", "预包条码明细导出", "预包条码明细导出.xlsx", "prePackageSkuDetailWriteEventExporter"),

    EXCEL_EXPORT_SECOND_CLASS_ACCOUNT("DT_SECOND_CLASS_ACCOUNT_EXPORT", "二级帐", "二级帐导出数据.xlsx",
            "secondClassAccountWriteEventExporter"),

    EXCEL_EXPORT_SHIPMENT_DETAIL("DT_SHIPMENT_DETAIL_EXPORT", "出库单原始明细", "出库单原始明细.xlsx",
            "shipmentDetailWriteEventExporter"),
    EXCEL_EXPORT_SHIP_ORDER("DT_SHIP_ORDER_DETAIL_EXPORT", "包裹库存占用明细", "包裹库存占用明细.xlsx",
            "shipmentOrderDetailWritEventExporter"),
    EXCEL_EXPORT_CONTAINER("DT_CONTAINER_EXPORT", "容器", "容器.xlsx", "containerWritEventExporter"),
    EXCEL_EXPORT_FIRST_CLASS_ACCOUNT("DT_FIRST_CLASS_ACCOUNT_EXPORT", "一级帐", "一级帐导出数据.xlsx",
            "firstClassAccountWriteEventExporter"),
    EXCEL_EXPORT_OUT_SOURCE_CODE("DT_OUT_SOURCE_CODE_EXPORT", "出库扫码记录", "出库扫码记录.xlsx",
            "outSourceCodeWriteEventExporter"),
    EXCEL_EXPORT_ORDER_DATE_SUMMARY("DT_ORDER_DATE_SUMMARY_EXPORT", "订单日汇总", "订单日汇总.xlsx",
            "orderDateSummaryWriteEventExporter"),
    EXCEL_EXPORT_LOCATION("DT_LOCATION_EXPORT", "导出库位", "库位.xlsx", "locationWriteEventExporter"),
    EXCEL_EXPORT_PACKAGE_DATE_SUMMARY("DT_PACKAGE_DATE_SUMMARY_EXPORT", "包裹日汇总", "包裹日汇总.xlsx",
            "packageDateSummaryWriteEventExporter"),
    // add by nobody
    EXCEL_EXPORT_ZONE("DT_ZONE_EXPORT", "导出库区", "库区档案.xlsx", "zoneWriteEventExporter"),
    EXCEL_EXPORT_WORKBENCH("DT_WORKBENCH_EXPORT", "质检台", "质检台.xlsx", "workbenchWritEventExporter"),
    EXCEL_EXPORT_VALIDITY_PERIOD_WARN("DT_VALIDITY_PERIOD_WARN_EXPORT", "效期预警", "效期预警.xlsx", "validityPeriodWarnWriteEventExporter"),
    EXCEL_EXPORT_TUNNEL("DT_TUNNEL_EXPORT", "导出巷道", "巷道档案.xlsx", "tunnelWriteEventExporter"),
    EXCEL_EXPORT_THIRD_CLASS_ACCOUNT("DT_THIRD_CLASS_ACCOUNT_EXPORT", "三级账", "三级帐导出数据.xlsx", "thirdClassAccountWriteEventExporter"),
    EXCEL_EXPORT_TALLY_GUIDE("DT_TALLY_GUIDE_EXPORT", "理库指引导出", "理库指引导出.xlsx", "tallyGuideWriteEventExporter"),
    EXCEL_EXPORT_TALLY_DETAIL("EXCEL_EXPORT_TALLY_DETAIL", "导出理货报告明细", "导出理货报告明细.xlsx", "tallyDetailWriteEventExporter"),
    EXCEL_EXPORT_SUPERVISION_ORIGIN_RECORD("EXCEL_EXPORT_SUPERVISION_ORIGIN_RECORD", "导出原始明细", "导出原始明细.xlsx", "supervisionWriteEventExporter"),
    EXCEL_EXPORT_SUPERVISION_MOVE_DETAIL("EXCEL_EXPORT_SUPERVISION_MOVE_DETAIL", "导出监管明细", "导出监管明细.xlsx", "supervisionMoveWriteEventExporter"),
    EXCEL_STOCK_SNAPSHOT_EXPORT("EXCEL_STOCK_SNAPSHOT_EXPORT", "一级库存结算快照表", "一级库存结算快照表.xlsx", "stockSnapshotWriteEventExporter"),
    EXCEL_EXPORT_STOCK_TRANSACTION("DT_STOCK_TRANSACTION_EXPORT", "核销记录", "核销记录.xlsx", "stockTransactionWriteEventExporter"),
    EXCEL_STOCK_LOCATION_SNAPSHOT_EXPORT("EXCEL_STOCK_LOCATION_SNAPSHOT_EXPORT", "库位批次库存结算快照", "库位批次库存结算快照.xlsx", "stockLocationSnapshotWriteEventExporter"),
    EXCEL_EXPORT_STOCK_SERIAL("DT_STOCK_SERIAL_EXPORT", "库存流水", "库存流水.xlsx", "stockSerialWriteEventExporter"),
    EXCEL_EXPORT_EXPRESS_INTERCEPT("EXCEL_EXPORT_EXPRESS_INTERCEPT", "导出快递拦截指令", "导出快递拦截指令.xlsx", "expressInterceptWriteEventExporter"),
    EXCEL_EXPORT_ORDER_INTERCEPT_SELECT("DT_ORDER_INTERCEPT_EXPORT", "订单拦截查询报表", "订单拦截查询报表.xlsx", "orderInterceptWriteEventExporter"),
    EXCEL_EXPORT_ADJUST_DETAIL("EXCEL_EXPORT_ADJUST_DETAIL", "调整单明细", "调整单明细.xlsx", "adjustDetailExporter"),
    EXCEL_EXPORT_TRANSFER_DETAIL("EXCEL_EXPORT_TRANSFER_DETAIL", "转移单明细", "转移单明细.xlsx", "transferDetailExporter"),
    EXCEL_EXPORT_PERFORMANCE_STATISTICS("EXCEL_EXPORT_PERFORMANCE_STATISTICS", "人员绩效报表", "人员绩效报表.xlsx", "performanceStatisticsEventExporter"),
    EXCEL_EXPORT_FINANCE_STOCK("EXCEL_EXPORT_FINANCE_STOCK", "导出监管库存", "导出监管库存.xlsx", "financeStockWriteEventExporter"),
    EXCEL_EXPORT_STORE_SELECT("DT_STORE_SELECT_EXPORT", "库存查询报表", "库存查询报表.xlsx", "storeSelectWriteEventExporter"),

    EXCEL_EXPORT_SKU("DT_SKU_FILES_EXPORT", "商品档案", "商品档案报表.xlsx", "skuFilesWriteEventExporter"),
    EXCEL_EXPORT_SKU_MATERIAL("DT_EXCEL_EXPORT_SKU_MATERIAL", "商品绑定耗材导出", "商品绑定耗材导出.xlsx", "skuMaterialWriteEventExporter"),

    EXCEL_EXPORT_REDEEM("EXCEL_EXPORT_REDEEM_ORIGIN_RECORD", "导出原始明细", "导出原始明细.xlsx", "redeemWriteEventExporter"),
    EXCEL_EXPORT_REDEEM_MOVE("EXCEL_EXPORT_REDEEM_MOVE_DETAIL", "导出赎回明细", "导出赎回明细.xlsx", "redeemMoveWriteEventExporter"),


    EXCEL_EXPORT_REPLENISH_TASK_MULTI("DT_REPLENISH_TASK_MULTI_EXPORT", "补货库位指引", "补货库位指引.xlsx",
            "replenishTaskMultiWritEventExporter"),
    EXCEL_EXPORT_REPLENISH_TASK("DT_REPLENISH_TASK_EXPORT", "补货库位指引", "补货库位指引.xlsx", "replenishTaskWritEventExporter"),

    EXCEL_EXPORT_RECEIPT("DT_RECEIPT_EXPORT", "收货作业批次", "收货作业批次.xlsx", "receiptWriteEventExporter"),
    EXCEL_EXPORT_RECEIPT_DETAIL("DT_RECEIPT_DETAIL_EXPORT", "收货作业批次明细", "收货作业批次明细.xlsx", "receiptDetailWriteEventExporter"),
    EXCEL_EXPORT_RECEIPT_EXTRA_DETAIL("EXCEL_EXPORT_RECEIPT_EXTRA_DETAIL", "收货作业批次", "收货作业批次.xlsx", "receiptExtraDetailWriteEventExporter"),
    EXCEL_EXPORT_RECEIPT_BILL_RECORD("EXCEL_EXPORT_RECEIPT_BILL_RECORD", "收货单明细", "收货单明细.xlsx", "receiptBillRecordWriteEventExporter"),

    EXCEL_EXPORT_CARGO_OWNER_WAREHOUSE("DT_CARGOOWNER_WAREHOUSE_EXPORT", "货主仓库商品", "货主仓库商品.xlsx", "cargoOwnerWarehouseWriteEventExporter"),
    EXCEL_EXPORT_CARGO_OWNER_CARGO_LOCATION_BATCH_RECORD("DT_CARGOOWNER_CARGOLOCATIONBATCH_EXPORT", "货主货位批次", "货主货位批次.xlsx", "cargoOwnerCargoLocationBatchWriteEventExporter"),
    EXCEL_EXPORT_CARGO_BATCH_INFO("DT_CARGO_BATCHINFO_EXPORT", "货品批次信息", "货品批次信息.xlsx", "cargoBatchInfoWriteEventExporter"),
    EXCEL_EXPORT_CARGO_OWNER_CARGO_LOCATION("DT_CARGOOWNER_CARGOLOCATION_EXPORT", "货主货位商品", "货主货位商品.xlsx", "cargoOwnerCargoLocationWriteEventExporter"),

    EXCEL_EXPORT_DISPOSAL_ORIGIN_RECORD("EXCEL_EXPORT_DISPOSAL_ORIGIN_RECORD", "导出原始明细", "导出原始明细.xlsx", "disposalWriteEventExporter"),
    EXCEL_EXPORT_DISPOSAL_MOVE_DETAIL("EXCEL_EXPORT_DISPOSAL_MOVE_DETAIL", "导出处置明细", "导出处置明细.xlsx", "disposalMoveWriteEventExporter"),
    EXCEL_EXPORT_HAND_OVER_FILES("DT_HANDOVER_FILES_EXPORT", "交接单明细", "交接单明细.xlsx", "handoverFilesWriteEventExporter"),
    EXCEL_EXPORT_NEW_PACKAGE("DT_PACKAGE_NEW_EXPORT", "包裹", "包裹.xlsx", "packageWriteNewEventExporter"),//packageWriteEventExporter 旧
    EXCEL_EXPORT_NEW_PACKAGE_NEW("DT_PACKAGE_NEW_EXPORT_NEW", "包裹", "包裹.xlsx", "packageWriteNewEventExporterNew"),//packageWriteEventExporter 旧
    EXCEL_EXPORT_NEW_PACKAGE_DETAIL("DT_PACKAGE_DETAIL_NEW_EXPORT", "包裹明细", "包裹明细.xlsx", "packageDetailWriteNewEventExporter"),//packageDetailWriteEventExporter  旧
    EXCEL_EXPORT_PICK("EXCEL_EXPORT_PICK", "导出拣选单", "导出拣选单.xlsx", "pickWriteEventExporter"),
    EXCEL_EXPORT_PACKAGE_CHECK("DT_PACKAGE_CHECK_EXPORT", "包裹复核导出", "包裹复核导出.xlsx", "packageCheckWriteEventExporter"),
    EXCEL_EXPORT_MATERIAL_USE_RECORD("DT_EXCEL_EXPORT_MATERIAL_USE_RECORD", "导出耗材用量", "导出耗材用量.xlsx", "materialUseRecordWriteEventExporter"),
    EXCEL_EXPORT_PACKAGEMATERIAL("DT_PACKAGEMATERIAL_EXPORT", "包材", "包材.xlsx", "packageMaterialWritEventExporter"),
    EXCEL_EXPORT_PACKAGEUSE_STATISTICS("DT_PACKAGEUSE_STATISTICS_EXPORT", "报表包材使用统计", "报表包材使用统计.xlsx", "packageUseStatisticsWriteEventExporter"),
    EXCEL_EXPORT_BOX_SKU_DETAIL("EXCEL_EXPORT_BOX_SKU_DETAIL", "导出套盒明细", "导出套盒明细.xlsx", "boxSkuDetailWriteEventExporter"),
    EXCEL_EXPORT_TRUCKING("EXCEL_EXPORT_TRUCKING", "导出装载单", "导出装载单.xlsx", "truckingWriteEventExporter"),
    EXCEL_EXPORT_UPC_STOCK("EXCEL_EXPORT_UPC_STOCK", "包耗材库存", "包耗材库存.xlsx", "upcStockExporter"),
    EXCEL_EXPORT_UPC_STOCK_SERIAL("EXCEL_EXPORT_UPC_STOCK_SERIAL", "包耗材流水", "包耗材流水.xlsx", "upcStockSerialExporter"),
    EXCEL_EXPORT_UPC_STOCK_DIFFERENCE("EXCEL_EXPORT_UPC_STOCK_DIFFERENCE", "包耗材差异", "包耗材差异.xlsx", "upcStockDifferenceExporter"),

    EXCEL_EXPORT_REPLENISH_B2B_TASK_MULTI("DT_REPLENISH_TASK_B2B_EXPORT", "B2B待补货明细", "B2B待补货明细.xlsx", "replenishTaskB2BWritEventExporter"),

    EXCEL_EXPORT_RECEIVE_MATERIAL("EXCEL_RECEIVE_MATERIAL", "包耗材领用单明细", "包耗材领用单明细.xlsx", "receiveMaterialWritEventExporter"),
    EXCEL_EXPORT_STOCK_LOT_EXPORT("DT_STOCK_LOT_EXPORT", "批次库存导出", "批次库存导出.xlsx", "stockLotExporter"),
    EXCEL_EXPORT_STOCK_OCCUPY_EXPORT("EXCEL_EXPORT_STOCK_OCCUPY_EXPORT", "一级库存占用导出", "一级库存占用导出.xlsx", "stockOccupyExporter"),
    EXCEL_EXPORT_STOCK_LOT_OCCUPY_EXPORT("EXCEL_EXPORT_STOCK_LOT_OCCUPY_EXPORT", "批次库存占用导出", "批次库存占用导出.xlsx", "stockLotOccupyExporter"),

    EXCEL_EXPORT_CARGO_OWNER_EXPORT("DT_CARGO_OWNER_EXPORT", "货主导出", "货主导出.xlsx", "cargoOwnerExporter"),
    EXCEL_EXPORT_WAREHOUSE_RENT("DT_WAREHOUSE_RENT_EXPORT", "仓租单明细", "仓租单明细.xlsx", "warehouseRentWriteEventExporter"),
    DT_EXCEL_EXPORT_WAREHOUSE_RENT_VOLUME("DT_EXCEL_EXPORT_WAREHOUSE_RENT_VOLUME", "仓租单长宽高", "仓租单长宽高.xlsx", "warehouseRentVolumeWriteEventExporter"),
    DT_EXCEL_EXPORT_STOCK_EXPORT_FOR_BYTECODE("DT_EXCEL_EXPORT_STOCK_EXPORT_FOR_BYTECODE", "字节迁移库存导出", "字节迁移库存导出.xlsx", "stockLocationExporterForByteCodeMigrate"),

    EXCEL_EXPORT_ZJ_SKU("EXCEL_EXPORT_ZJ_SKU", "商品字节导出", "商品字节导出.xlsx", "skuZJWriteEventExporter"),
    DT_EXCEL_EXPORT_MOVE_DETAIL("DT_EXCEL_EXPORT_MOVE_DETAIL", "移位明细导出", "移位明细导出.xlsx", "moveDetailExporter"),
    DT_EXCEL_EXPORT_SHELF_DETAIL("DT_EXCEL_EXPORT_SHELF_DETAIL", "上架明细导出", "上架明细导出.xlsx", "shelfDetailExporter"),
    DT_EXCEL_EXPORT_OFF_SHELF_DETAIL("DT_EXCEL_EXPORT_OFF_SHELF_DETAIL", "下架明细导出", "下架明细导出.xlsx", "offShelfDetailExporter"),

    EXCEL_EXPORT_TALLY_VALIDITY("DT_EXCEL_EXPORT_TALLY_VALIDITY", "效期码导出", "效期码导出.xlsx", "tallyValidityWriteEventExporter"),

    DT_EXCEL_EXPORT_BOOK_CARRY_OVER("DT_EXCEL_EXPORT_BOOK_CARRY_OVER", "结转单导出", "结转单导出.xlsx", "bookCarryoverExporter"),
    DT_EXCEL_EXPORT_MULTI_PRE_DEF("DT_EXCEL_EXPORT_MULTI_PRE_DEF", "多预包解析", "多预包解析.xlsx", "multiPreExporter"),
    DT_EXCEL_EXPORT_STOCK_LOT_FOR_CUSTOMS("DT_EXCEL_EXPORT_STOCK_LOT_FOR_CUSTOMS", "海关库存导出", "海关库存导出.xlsx", "stockLotForCustomsExporter"),
    DT_EXCEL_EXPORT_CW_TRANSFER("EXCEL_EXPORT_CW_TRANSFER_DETAIL", "CW调拨详情导出", "CW调拨详情导出.xlsx", "cwTransferDetailExporter"),
    DT_EXCEL_EXPORT_CW_STORAGE_APPLICATION("DT_EXCEL_EXPORT_CW_STORAGE_APPLICATION", "入库申请详情", "CW入库申请.xlsx", "CWStorageApplicationExporter"),


    EXPORT_RS_EXTRA_DETAIL("EXPORT_RS_EXTRA_DETAIL", "销退多货详情导出", "销退多货详情.xlsx", "rsExtraDetailApplicationExporter"),
    EXPORT_RS_EXTRA("EXPORT_RS_EXTRA", "销退多货导出", "销退多货.xlsx", "rsExtraApplicationExporter"),
    EXPORT_RS_ORDER("EXPORT_RS_ORDER", "销退单导出", "销退单导出.xlsx", "RSOrderExporter"),
    EXPORT_RS_ORDER_DETAIL("EXPORT_RS_ORDER_DETAIL", "销退单导出单据信息", "销退单导出单据信息.xlsx", "RSOrderDetailExporter"),
    EXPORT_RS_RECEIVE("EXPORT_RS_RECEIVE", "销退单导出收货记录", "销退单导出收货记录.xlsx", "RSOrderReceiveExporter"),
    EXPORT_RS_HANDOVER_DEATIL("EXPORT_RS_HANDOVER_DEATIL", "交接单明细导出", "交接单明细导出.xlsx", "rsHandoverDetailApplicationExporter"),

    EXPORT_RS_EXCEPTION_DEATIL("EXPORT_RS_EXCEPTION_DEATIL", "异常登记明细导出", "异常登记明细导出.xlsx", "rsExceptionExporter"),

    EXCEL_EXPORT_SPECIAL_INFORMATION("EXCEL_EXPORT_SPECIAL_INFORMATION", "采集明细导出", "采集明细导出.xlsx", "specialInformationDetailExporter"),


    EXCEL_EXPORT_FBA_INFORMATION("EXCEL_EXPORT_FBA_INFORMATION", "导出", "导出.xlsx", "fbaInformationExporter"),
    EXCEL_EXPORT_FBA_OUT_PLAN("EXCEL_EXPORT_FBA_OUT_PLAN", "导出", "导出.xlsx", "FBAOutPlanExporter"),
    EXCEL_EXPORT_STOCK_PLEDGE("EXCEL_EXPORT_STOCK_PLEDGE", "质押库存", "质押库存.xlsx", "stockPledgeExporter"),

    DT_PACKAGE_INSPECTION_EXPORT("DT_PACKAGE_INSPECTION_EXPORT", "抽检导出", "抽检导出.xlsx", "packageInspectionExporter"),

    ;

    private String funcCode;
    private String funcName;
    private String excelName;
    private String exporterBeanName;

    ExcelExportEnum(String funcCode, String funcName, String excelName, String exporterBeanName) {
        this.funcCode = funcCode;
        this.funcName = funcName;
        this.excelName = excelName;
        this.exporterBeanName = exporterBeanName;
    }

    public static ExcelExportEnum getEnum(String funcCode) {
        ExcelExportEnum result = Arrays.stream(ExcelExportEnum.values()).filter(a -> a.getFuncCode().equals(funcCode))
                .findFirst().orElse(null);
        if (result == null) {
            throw new IllegalArgumentException();
        }
        return result;
    }
}
