package com.danding.exporter.shipment.dto;

import com.danding.cola.dto.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 出库单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ShipmentOrder对象", description="出库单")
public class ShipmentOrderQry extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    private List<String> packageCodeList;
    private List<String> pickCodeList;
    private List<String> skuCodeList;
    private List<String> upcCodeList;
    private String materialUpcCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;
    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;
    private List<String> shipmentOrderCodeList;
    /**
     * 指定包材
     */
    @ApiModelProperty(value = "指定包材")
    private String materialCode;

    /**
     * 客户单号
     */
    @ApiModelProperty(value = "客户单号")
    private String poNo;
    private List<String> poNoList;
    /**
     * 上游单号
     */
    @ApiModelProperty(value = "上游单号")
    private String soNo;
    private List<String> soNoList;
    /**
     * 全局单号-在但丁云系统中唯一单号
     */
    @ApiModelProperty(value = "全局单号-在但丁云系统中唯一单号")
    private String globalNo;

    /**
     * 交易单号
     */
    @ApiModelProperty(value = "交易单号")
    private String tradeNo;
    private List<String> tradeNoList;
    /**
     * 库存占用类型 前占、中占、
     */
    @ApiModelProperty(value = "库存占用类型 前占、中占、")
    private String occupyType;

    /**
     * 单据来源
     */
    @ApiModelProperty(value = "单据来源")
    private String fromSource;

    /**
     * 外部拓传参数
     */
    @ApiModelProperty(value = "外部拓传参数")
    private String extraParam;

    /**
     * 预处理状态
     */
    @ApiModelProperty(value = "预处理状态")
    private String pretreatmentStatus;
    private List<String> pretreatmentStatusList;
    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String orderType;

    /**
     * 保税类型
     */
    @ApiModelProperty(value = "保税类型")
    private String taxType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 销售平台
     */
    @ApiModelProperty(value = "销售平台")
    private String salePlatform;

    /**
     * 销售店铺ID
     */
    @ApiModelProperty(value = "销售店铺ID")
    private String saleShopId;

    /**
     * 销售店铺
     */
    @ApiModelProperty(value = "销售店铺")
    private String saleShop;

    /**
     * 快递公司编码
     */
    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;
    private List<String> carrierCodeList;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String carrierName;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String expressNo;
    private List<String> expressNoList;
    /**
     * 包裹类型
     */
    @ApiModelProperty(value = "包裹类型")
    private String packageStruct;
    private List<String> packageStructList;

    /**
     * 汇单时间
     */
    @ApiModelProperty(value = "汇单时间")
    private Long collectTime;

    /**
     * 预计出库时间
     */
    @ApiModelProperty(value = "预计出库时间")
    private Long expOutStockDate;
    private Long startExpOutStockDate;
    private Long endExpOutStockDate;
    /**
     * 交易下单时间
     */
    @ApiModelProperty(value = "交易下单时间")
    private Long placeTradeOrderDate;
    private Long startTradeOrderDate;
    private Long endTradeOrderDate;
    /**
     * 付款时间
     */
    @ApiModelProperty(value = "付款时间")
    private Long payDate;
    private Long startPayTime;
    private Long endPayTime;
    /**
     * 拣货开始时间
     */
    @ApiModelProperty(value = "拣货开始时间")
    private Long pickSkuDate;

    /**
     * 拣货完成时间
     */
    @ApiModelProperty(value = "拣货完成时间")
    private Long pickCompleteSkuDate;

    /**
     * 复核开始时间
     */
    @ApiModelProperty(value = "复核开始时间")
    private Long checkStartDate;

    /**
     * 复核完成时间
     */
    @ApiModelProperty(value = "复核完成时间")
    private Long checkCompleteDate;

    /**
     * 首包裹出库时间
     */
    @ApiModelProperty(value = "首包裹出库时间")
    private Long firstPackOutStockDate;
    private Long firstPackOutStockDateStart;
    private Long firstPackOutStockDateEnd;
    /**
     * 出库时间
     */
    @ApiModelProperty(value = "出库时间")
    private Long outStockDate;
    private Long startOutStockDate;
    private Long endOutStockDate;
    /**
     * 拦截取消时间
     */
    @ApiModelProperty(value = "拦截取消时间")
    private Long interceptCancelDate;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 商品品种数
     */
    @ApiModelProperty(value = "商品品种数")
    private Integer skuTypeQty;
    private Integer startSkuTypeCount;
    private Integer endSkuTypeCount;
    /**
     * 订单商品数量
     */
    @ApiModelProperty(value = "订单商品数量")
    private BigDecimal skuQty;
    private Integer startSkuCount;
    private Integer endSkuCount;
    /**
     * 出库商品数量
     */
    @ApiModelProperty(value = "出库商品数量")
    private BigDecimal outSkuQty;

    /**
     * 包裹数量
     */
    @ApiModelProperty(value = "包裹数量")
    private Integer packageQty;

    /**
     * 出库包裹数
     */
    @ApiModelProperty(value = "出库包裹数")
    private Integer outPackageQty;

    /**
     * 回传通知状态
     */
    @ApiModelProperty(value = "回传通知状态")
    private Integer notifyStatus;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer notifyCount;

    /**
     * 回传通知时间
     */
    @ApiModelProperty(value = "回传通知时间")
    private Long notifyTime;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String receiverMan;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String receiverTel;

    /**
     * 收货国家
     */
    @ApiModelProperty(value = "收货国家")
    private String receiverCountry;

    /**
     * 收货省份
     */
    @ApiModelProperty(value = "收货省份")
    private String receiverProv;
    private List<String> receiverProvList;
    /**
     * 收货人市
     */
    @ApiModelProperty(value = "收货人市")
    private String receiverCity;
    private List<String> receiverCityList;
    /**
     * 收货人区
     */
    @ApiModelProperty(value = "收货人区")
    private String receiverArea;
    private List<String> receiverAreaList;
    /**
     * 收货省份名称
     */
    @ApiModelProperty(value = "收货省份名称")
    private String receiverProvName;

    /**
     * 收货人市名称
     */
    @ApiModelProperty(value = "收货人市名称")
    private String receiverCityName;

    /**
     * 收货人区名称
     */
    @ApiModelProperty(value = "收货人区名称")
    private String receiverAreaName;

    /**
     * 收货人邮编
     */
    @ApiModelProperty(value = "收货人邮编")
    private String receiverZipcode;

    /**
     * 收货人详细地址
     */
    @ApiModelProperty(value = "收货人详细地址")
    private String receiverAddress;

    /**
     * 发货人
     */
    @ApiModelProperty(value = "发货人")
    private String senderMan;

    /**
     * 发货人电话
     */
    @ApiModelProperty(value = "发货人电话")
    private String senderTel;

    /**
     * 发货人国家
     */
    @ApiModelProperty(value = "发货人国家")
    private String senderCountry;

    /**
     * 发货人省
     */
    @ApiModelProperty(value = "发货人省")
    private String senderProv;

    /**
     * 发货人市
     */
    @ApiModelProperty(value = "发货人市")
    private String senderCity;

    /**
     * 发货人区
     */
    @ApiModelProperty(value = "发货人区")
    private String senderArea;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String senderProvName;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String senderCityName;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String senderAreaName;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String senderZipcode;

    /**
     * 发货人地址
     */
    @ApiModelProperty(value = "发货人地址")
    private String senderAddress;

    /**
     * 数据加密方式
     */
    @ApiModelProperty(value = "数据加密方式")
    private Integer secureType;

    /**
     * 奇门oaid解密标识
     */
    @ApiModelProperty(value = "奇门oaid解密标识")
    private String oaId;

    /**
     * 订单tag
     */
    @ApiModelProperty(value = "订单tag")
    private Integer orderTag;
    private Integer noContainOrderTag;
    private List<Integer> orderTagList;
    /**
     * 是否是增值包装 T:是 F:否
     */
    @ApiModelProperty(value = "是否是增值包装 T:是 F:否")
    private String extraJson;

    /**
     * 是否4PL
     */
    @ApiModelProperty(value = "是否4PL")
    private Integer is4pl;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 预售类型
     */
    @ApiModelProperty(value = "预售类型")
    private Integer preSaleType;

    /**
     * 发货超时时间
     */
    @ApiModelProperty(value = "发货超时时间")
    private Long expShipTime;
    private Long expShipTimeStart;
    private Long expShipTimeEnd;
    /**
     * 发货超时时间
     */
    @ApiModelProperty(value = "发货超时时间")
    private Long ordPerformDate;

    /**
     * 理论重量
     */
    @ApiModelProperty(value = "理论重量")
    private BigDecimal weight;
    private BigDecimal weightStart;
    private BigDecimal weightEnd;

    @ApiModelProperty(value = "清关状态")
    private String customsClearanceType;
    private List<String> customsClearanceStatusList;

   /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

   /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "状态")
    private String status;
    private List<String> statusList;

    private String orderSkuType;

    @ApiModelProperty(value = "复核完成查询条件")
    private Long checkCompleteDateStart;
    private Long checkCompleteDateEnd;
    @ApiModelProperty(value = "拣选完成查询条件")
    private Long pickCompleteSkuDateStart;
    private Long pickCompleteSkuDateEnd;


    //----------------------------点击查看数据-------------------------

    @ApiModelProperty(value = "预计出库时间（天）")
    private String expOutStockDate_day;

    @ApiModelProperty(value = "预计出库时间（小时）")
    private String expOutStockDate_hour;

    @ApiModelProperty(value = "创建时间（天）")
    private String createdTime_day;

    @ApiModelProperty(value = "创建时间（小时）")
    private String createdTime_hour;

    @ApiModelProperty(value = "付款时间（天）")
    private String payDate_day;

    @ApiModelProperty(value = "付款时间（小时）")
    private String payDate_hour;

    @ApiModelProperty(value = "出库时间（天）")
    private String outStockDate_day;

    @ApiModelProperty(value = "出库时间（小时）")
    private String outStockDate_hour;

    @ApiModelProperty(value = "列key")
    private String columnKey;

    @ApiModelProperty(value = "创建时间开始")
    private Long createTimeStart;

    @ApiModelProperty(value = "创建时间结束")
    private Long createTimeEnd;

    @ApiModelProperty(value = "付款时间开始")
    private Long payDateStart;

    @ApiModelProperty(value = "付款时间结束")
    private Long payDateEnd;

    @ApiModelProperty(value = "预计出库时间开始")
    private Long expOutStockDateStart;

    @ApiModelProperty(value = "预计出库时间结束")
    private Long expOutStockDateEnd;

    @ApiModelProperty(value = "出库时间开始")
    private Long outStockDateStart;

    @ApiModelProperty(value = "出库时间结束")
    private Long outStockDateEnd;

    @ApiModelProperty(value = "品种数最小值")
    private Integer skuTypeQtyMin;

    @ApiModelProperty(value = "品种数最大值")
    private Integer skuTypeQtyMax;

    @ApiModelProperty(value = "商品数最小值")
    private Integer skuQtyMin;

    @ApiModelProperty(value = "商品数最大值")
    private Integer skuQtyMax;

    @ApiModelProperty(value = "理论重量最小值")
    private Integer weightMin;

    @ApiModelProperty(value = "理论重量最大值")
    private Integer weightMax;


    //----------------------------点击查看数据-------------------------

}