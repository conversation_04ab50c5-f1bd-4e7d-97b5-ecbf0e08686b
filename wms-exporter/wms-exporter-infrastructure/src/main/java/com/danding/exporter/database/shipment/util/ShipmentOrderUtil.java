package com.danding.exporter.database.shipment.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.cola.mp.query.QueryWrapper;
import com.danding.exporter.database.shipment.entity.ShipmentOrder;
import com.danding.exporter.shipment.dto.ShipmentOrderQry;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <p>
 * 出库单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Component
public class ShipmentOrderUtil extends QueryWrapper<ShipmentOrder, ShipmentOrderQry> {

    @Override
    public LambdaQueryWrapper<ShipmentOrder> getQueryWrapper(ShipmentOrderQry qry) {
        LambdaQueryWrapper<ShipmentOrder> lambdaQueryWrapper = super.getQueryWrapper(qry);
        lambdaQueryWrapper
                .eq(!ObjectUtils.isEmpty(qry.getShipmentOrderCode()), ShipmentOrder::getShipmentOrderCode,
                        qry.getShipmentOrderCode())
                .eq(!ObjectUtils.isEmpty(qry.getPoNo()), ShipmentOrder::getPoNo, qry.getPoNo())
                .eq(!ObjectUtils.isEmpty(qry.getFromSource()), ShipmentOrder::getFromSource, qry.getFromSource())

                .eq(!ObjectUtils.isEmpty(qry.getSoNo()), ShipmentOrder::getSoNo, qry.getSoNo())
                .in(!ObjectUtils.isEmpty(qry.getSoNoList()), ShipmentOrder::getSoNo, qry.getSoNoList())
                .eq(!ObjectUtils.isEmpty(qry.getWarehouseCode()), ShipmentOrder::getWarehouseCode,
                        qry.getWarehouseCode())
                .in(!ObjectUtils.isEmpty(qry.getPoNoList()), ShipmentOrder::getPoNo, qry.getPoNoList())
                .eq(!ObjectUtils.isEmpty(qry.getCargoCode()), ShipmentOrder::getCargoCode, qry.getCargoCode())
                .in(!ObjectUtils.isEmpty(qry.getCargoCodeList()), ShipmentOrder::getCargoCode, qry.getCargoCodeList())
                .eq(!ObjectUtils.isEmpty(qry.getStatus()), ShipmentOrder::getStatus, qry.getStatus())
                .in(!ObjectUtils.isEmpty(qry.getStatusList()), ShipmentOrder::getStatus, qry.getStatusList())
                .eq(!ObjectUtils.isEmpty(qry.getPretreatmentStatus()), ShipmentOrder::getPretreatmentStatus,
                        qry.getPretreatmentStatus())
                .eq(!ObjectUtils.isEmpty(qry.getOrderType()), ShipmentOrder::getOrderType, qry.getOrderType())
                .eq(!ObjectUtils.isEmpty(qry.getBusinessType()), ShipmentOrder::getBusinessType, qry.getBusinessType())
                .eq(!ObjectUtils.isEmpty(qry.getCarrierCode()), ShipmentOrder::getCarrierCode, qry.getCarrierCode())
                .in(!ObjectUtils.isEmpty(qry.getCarrierCodeList()), ShipmentOrder::getCarrierCode, qry.getCarrierCodeList())

                .eq(!ObjectUtils.isEmpty(qry.getExpressNo()), ShipmentOrder::getExpressNo, qry.getExpressNo())
                .in(!ObjectUtils.isEmpty(qry.getExpressNoList()), ShipmentOrder::getExpressNo, qry.getExpressNoList())
                .eq(!ObjectUtils.isEmpty(qry.getTradeNo()), ShipmentOrder::getTradeNo, qry.getTradeNo())
                .in(!ObjectUtils.isEmpty(qry.getTradeNoList()), ShipmentOrder::getTradeNo, qry.getTradeNoList())
                .eq(!ObjectUtils.isEmpty(qry.getSalePlatform()), ShipmentOrder::getSalePlatform, qry.getSalePlatform())
                .eq(!ObjectUtils.isEmpty(qry.getSaleShop()), ShipmentOrder::getSaleShop, qry.getSaleShop())
                .eq(!ObjectUtils.isEmpty(qry.getPackageStruct()), ShipmentOrder::getPackageStruct,
                        qry.getPackageStruct())
                .in(!ObjectUtils.isEmpty(qry.getPackageStructList()), ShipmentOrder::getPackageStruct,
                        qry.getPackageStructList())
                // 出库单回传状态
                .eq(!ObjectUtils.isEmpty(qry.getNotifyStatus()), ShipmentOrder::getNotifyStatus, qry.getNotifyStatus())
                .eq(!ObjectUtils.isEmpty(qry.getReceiverMan()), ShipmentOrder::getReceiverMan, qry.getReceiverMan())
                .eq(!ObjectUtils.isEmpty(qry.getReceiverTel()), ShipmentOrder::getReceiverTel, qry.getReceiverTel())
                .eq(!ObjectUtils.isEmpty(qry.getReceiverProv()), ShipmentOrder::getReceiverProv, qry.getReceiverProv())
                .in(!ObjectUtils.isEmpty(qry.getReceiverProvList()), ShipmentOrder::getReceiverProv, qry.getReceiverProvList())
                .eq(!ObjectUtils.isEmpty(qry.getReceiverCity()), ShipmentOrder::getReceiverCity, qry.getReceiverCity())
                .in(!ObjectUtils.isEmpty(qry.getReceiverCityList()), ShipmentOrder::getReceiverCity, qry.getReceiverCityList())
                .eq(!ObjectUtils.isEmpty(qry.getReceiverArea()), ShipmentOrder::getReceiverArea, qry.getReceiverArea())
                .in(!ObjectUtils.isEmpty(qry.getReceiverAreaList()), ShipmentOrder::getReceiverArea, qry.getReceiverAreaList())

                .eq(!ObjectUtils.isEmpty(qry.getReceiverProvName()), ShipmentOrder::getReceiverProvName, qry.getReceiverProvName())
                .eq(!ObjectUtils.isEmpty(qry.getReceiverCityName()), ShipmentOrder::getReceiverCityName, qry.getReceiverCityName())
                .eq(!ObjectUtils.isEmpty(qry.getReceiverAreaName()), ShipmentOrder::getReceiverAreaName, qry.getReceiverAreaName())

                .in(!CollectionUtils.isEmpty(qry.getShipmentOrderCodeList()), ShipmentOrder::getShipmentOrderCode,
                        qry.getShipmentOrderCodeList())
                .in(!ObjectUtils.isEmpty(qry.getPretreatmentStatusList()), ShipmentOrder::getPretreatmentStatus,
                        qry.getPretreatmentStatusList())
                .in(!ObjectUtils.isEmpty(qry.getOrderSkuType()), ShipmentOrder::getPackageStruct, qry.getOrderSkuType())
                .ge(!ObjectUtils.isEmpty(qry.getStartExpOutStockDate()), ShipmentOrder::getExpOutStockDate,
                        qry.getStartExpOutStockDate())
                .le(!ObjectUtils.isEmpty(qry.getEndExpOutStockDate()), ShipmentOrder::getExpOutStockDate,
                        qry.getEndExpOutStockDate())
                .ge(!ObjectUtils.isEmpty(qry.getStartOutStockDate()), ShipmentOrder::getOutStockDate,
                        qry.getStartOutStockDate())
                .le(!ObjectUtils.isEmpty(qry.getEndOutStockDate()), ShipmentOrder::getOutStockDate,
                        qry.getEndOutStockDate())
                .ge(!ObjectUtils.isEmpty(qry.getStartPayTime()), ShipmentOrder::getPayDate, qry.getStartPayTime())
                .le(!ObjectUtils.isEmpty(qry.getEndPayTime()), ShipmentOrder::getPayDate, qry.getEndPayTime())
                .ge(!ObjectUtils.isEmpty(qry.getStartTradeOrderDate()), ShipmentOrder::getPlaceTradeOrderDate,
                        qry.getStartTradeOrderDate())
                .le(!ObjectUtils.isEmpty(qry.getEndTradeOrderDate()), ShipmentOrder::getPlaceTradeOrderDate,
                        qry.getEndTradeOrderDate())
                .ge(!ObjectUtils.isEmpty(qry.getStartSkuCount()), ShipmentOrder::getSkuQty, qry.getStartSkuCount())
                .le(!ObjectUtils.isEmpty(qry.getEndSkuCount()), ShipmentOrder::getSkuQty, qry.getEndSkuCount())
                .ge(!ObjectUtils.isEmpty(qry.getStartSkuCount()), ShipmentOrder::getSkuQty, qry.getStartSkuCount())
                .le(!ObjectUtils.isEmpty(qry.getEndSkuCount()), ShipmentOrder::getSkuQty, qry.getEndSkuCount())
                .ge(!ObjectUtils.isEmpty(qry.getStartSkuTypeCount()), ShipmentOrder::getSkuTypeQty,
                        qry.getStartSkuTypeCount())
                .le(!ObjectUtils.isEmpty(qry.getEndSkuTypeCount()), ShipmentOrder::getSkuTypeQty,
                        qry.getEndSkuTypeCount())
                .ge(!ObjectUtils.isEmpty(qry.getFirstPackOutStockDateStart()), ShipmentOrder::getFirstPackOutStockDate,
                        qry.getFirstPackOutStockDateStart())
                .le(!ObjectUtils.isEmpty(qry.getFirstPackOutStockDateEnd()), ShipmentOrder::getFirstPackOutStockDate,
                        qry.getFirstPackOutStockDateEnd())
                // 预售类型
                .eq(!ObjectUtils.isEmpty(qry.getPreSaleType()), ShipmentOrder::getPreSaleType, qry.getPreSaleType())
                // 发货超时时间
                .ge(!ObjectUtils.isEmpty(qry.getExpShipTimeStart()), ShipmentOrder::getExpShipTime,
                        qry.getExpShipTimeStart())
                .le(!ObjectUtils.isEmpty(qry.getExpShipTimeEnd()), ShipmentOrder::getExpShipTime,
                        qry.getExpShipTimeEnd())
                // 理论重量
                .ge(!ObjectUtils.isEmpty(qry.getWeightStart()), ShipmentOrder::getWeight,
                        qry.getWeightStart())
                .le(!ObjectUtils.isEmpty(qry.getWeightEnd()), ShipmentOrder::getWeight,
                        qry.getWeightEnd())
                /**
                 * 测试反应，指定时间范围，未空的被查询出来了，
                 */
                .gt((!ObjectUtils.isEmpty(qry.getStartExpOutStockDate())
                        || !ObjectUtils.isEmpty(qry.getEndExpOutStockDate())), ShipmentOrder::getExpOutStockDate, 0L)
                .gt((!ObjectUtils.isEmpty(qry.getStartOutStockDate())
                        || !ObjectUtils.isEmpty(qry.getEndOutStockDate())), ShipmentOrder::getOutStockDate, 0L)
                .gt((!ObjectUtils.isEmpty(qry.getStartPayTime()) || !ObjectUtils.isEmpty(qry.getEndPayTime())),
                        ShipmentOrder::getPayDate, 0L)
                .gt((!ObjectUtils.isEmpty(qry.getStartTradeOrderDate())
                        || !ObjectUtils.isEmpty(qry.getEndTradeOrderDate())), ShipmentOrder::getPlaceTradeOrderDate, 0L)
                .gt((!ObjectUtils.isEmpty(qry.getFirstPackOutStockDateStart())
                                || !ObjectUtils.isEmpty(qry.getFirstPackOutStockDateEnd())), ShipmentOrder::getFirstPackOutStockDate,
                        0L)
                .gt((!ObjectUtils.isEmpty(qry.getExpShipTimeStart()) || !ObjectUtils.isEmpty(qry.getExpShipTimeEnd())),
                        ShipmentOrder::getExpShipTime, 0L)
                .eq(StrUtil.isNotBlank(qry.getCustomsClearanceType()), ShipmentOrder::getCustomsClearanceType, qry.getCustomsClearanceType())
                .in(CollectionUtil.isNotEmpty(qry.getCustomsClearanceStatusList()),ShipmentOrder::getCustomsClearanceStatus,qry.getCustomsClearanceStatusList())

                .ge(!ObjectUtils.isEmpty(qry.getCheckCompleteDateStart()), ShipmentOrder::getCheckCompleteDate, qry.getCheckCompleteDateStart())
                .le(!ObjectUtils.isEmpty(qry.getCheckCompleteDateEnd()), ShipmentOrder::getCheckCompleteDate, qry.getCheckCompleteDateEnd())
                .gt((!ObjectUtils.isEmpty(qry.getCheckCompleteDateStart()) || !ObjectUtils.isEmpty(qry.getCheckCompleteDateEnd())), ShipmentOrder::getCheckCompleteDate, 0L)

                .ge(!ObjectUtils.isEmpty(qry.getPickCompleteSkuDateStart()), ShipmentOrder::getPickCompleteSkuDate, qry.getPickCompleteSkuDateStart())
                .le(!ObjectUtils.isEmpty(qry.getPickCompleteSkuDateEnd()), ShipmentOrder::getPickCompleteSkuDate, qry.getPickCompleteSkuDateEnd())
                .gt((!ObjectUtils.isEmpty(qry.getPickCompleteSkuDateStart()) || !ObjectUtils.isEmpty(qry.getPickCompleteSkuDateEnd())), ShipmentOrder::getPickCompleteSkuDate, 0L)

        ;
        if (qry.getOrderTag() != null && qry.getOrderTag() > 0) {
            lambdaQueryWrapper.apply(" order_tag = (order_tag|" + qry.getOrderTag() + ")");
        }
        if (qry.getNoContainOrderTag() != null && qry.getNoContainOrderTag() > 0) {
            lambdaQueryWrapper.apply(" order_tag != (order_tag|" + qry.getNoContainOrderTag() + ")");
        }
        return lambdaQueryWrapper;
    }
}
