package com.dt.portal.pda.web.vo.rec;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/1/3 14:32
 */
@Data
public class SkuRecContainerSkuVO implements Serializable {

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "商品属性名称")
    private String skuQualityName;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "生产日期展示")
    private String manufDesc;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "失效日期展示")
    private String expireDesc;

    @ApiModelProperty(value = "收货时间")
    private Long receiveDate;

    @ApiModelProperty(value = "收货时间字符串展示")
    private String receiveDateDesc;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;
    private String inventoryTypeDesc;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;
}
