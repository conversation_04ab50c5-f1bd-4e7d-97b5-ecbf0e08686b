package com.dt.portal.pda.web.client.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.*;
import com.dt.platform.wms.dto.base.LocationBizDTO;
import com.dt.platform.wms.dto.base.ZoneBizDTO;
import com.dt.platform.wms.dto.sku.SkuBizDTO;
import com.dt.platform.wms.dto.sku.SkuLotBizDTO;
import com.dt.platform.wms.dto.stock.StockLocationBizDTO;
import com.dt.platform.wms.param.CodePageParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.base.LocationBizParam;
import com.dt.platform.wms.param.base.ZoneBizParam;
import com.dt.platform.wms.param.sku.SkuBizParam;
import com.dt.platform.wms.param.sku.SkuLotBizParam;
import com.dt.portal.pda.web.client.ISkuOrStockQueryPortalClient;
import com.dt.portal.pda.web.vo.stock.ScanSkuItem;
import com.dt.portal.pda.web.vo.stock.ScanStockItem;
import com.dt.portal.pda.web.vo.stock.page.ScanSkuPageVO;
import com.dt.portal.pda.web.vo.stock.page.ScanStockPageVO;
import com.dt.portal.pda.web.vo.stock.page.StockSkuOrStockPageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class SkuOrStockQueryPortalClientImpl implements ISkuOrStockQueryPortalClient {
    @DubboReference
    private IZoneBizClient izoneBizClient;
    @DubboReference
    private ILocationBizClient locationBizClient;
    @DubboReference
    private ISkuBizClient skuBizClient;
    @DubboReference
    private IScanStockLocationBizClient scanStockLocationBizClient;
    @DubboReference
    private ISkuLotBizClient skuLotBizClient;

//    @Override
//    @SoulClient(path = "/scan/scanSkuStock", desc = "扫描库位,商品信息")
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
//    public Result<Map<String, Object>> scanSkuOrStockList(CodeParam param) {
//        LocationBizParam param1 = new LocationBizParam();
//        param1.setCode(param.getCode());
//        LocationBizDTO locationBizDTO = locationBizClient.getDetail(param1).getData();
//        StockSkuOrStockVO oret = new StockSkuOrStockVO();
//        if (locationBizDTO != null) {
//            CodeParam codeParam = new CodeParam();
//            codeParam.setCode(locationBizDTO.getCode());
//            oret.setType("location");
//            ScanStockVO scanStockVO = new ScanStockVO();
//            scanStockVO.setLocationCode(locationBizDTO.getCode());
//            ZoneBizParam zoneBizParam = new ZoneBizParam();
//            zoneBizParam.setCode(locationBizDTO.getZoneCode());
//            Result<ZoneBizDTO> zoneBizDTOResult = izoneBizClient.getDetail(zoneBizParam);
//            ZoneBizDTO zoneBizDTO = zoneBizDTOResult.getData();
//            if (zoneBizDTO == null) {
//                throw new BaseException(BaseBizEnum.TIP, "库位正次品属性异常");
//            }
//            scanStockVO.setSkuQuality(zoneBizDTO.getSkuQuality());
//            scanStockVO.setSkuQualityName(SkuQualityEnum.getEnum(zoneBizDTO.getSkuQuality()).getMessage());
//            List<StockLocationBizDTO> stockLocationBizDtoList = scanStockLocationBizClient.getScanLocationStockList(codeParam).getData();
//            if (stockLocationBizDtoList == null) {
//                stockLocationBizDtoList = new ArrayList<>();
//            }
//            Comparator<StockLocationBizDTO> cargoCodeAsc = Comparator.comparing(StockLocationBizDTO::getCargoCode);
//            Comparator<StockLocationBizDTO> finalSkuAsc = cargoCodeAsc.thenComparing(Comparator.comparing(StockLocationBizDTO::getSkuCode));
//            stockLocationBizDtoList = stockLocationBizDtoList.stream().sorted(finalSkuAsc).collect(Collectors.toList());
//            BigDecimal availableQty = stockLocationBizDtoList.stream().map(s -> s.getAvailableQty()).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal physicalQty = stockLocationBizDtoList.stream().map(s -> s.getPhysicalQty()).reduce(BigDecimal.ZERO, BigDecimal::add);
//            scanStockVO.setAvailableQty(availableQty);
//            scanStockVO.setPhysicalQty(physicalQty);
//            oret.setScanStockVO(scanStockVO);
//            List<ScanStockItem> scanStockItems = new ArrayList<>();
//            stockLocationBizDtoList.forEach(s -> {
//                ScanStockItem item = new ScanStockItem();
//                item.setSkuQuality(s.getSkuQuality());
//                item.setSkuQualityName(SkuQualityEnum.getEnum(s.getSkuQuality()).getMessage());
//                item.setCargoCode(s.getCargoCode());
//                item.setCargoCodeName(s.getCargoName());
//                item.setAvailableQty(s.getAvailableQty());
//                item.setPhysicalQty(s.getPhysicalQty());
//                item.setSkuLotNo(s.getSkuLotNo());
//                item.setSkuCode(s.getSkuCode());
//                SkuBizParam skuBizParam = new SkuBizParam();
//                skuBizParam.setCode(s.getSkuCode());
//                skuBizParam.setCargoCode(s.getCargoCode());
//                SkuLotBizParam skuLotParam = new SkuLotBizParam();
//                skuLotParam.setSkuCode(s.getSkuCode());
//                skuLotParam.setSkuLotNo(s.getSkuLotNo());
//                skuLotParam.setCargoCode(s.getCargoCode());
//                SkuLotBizDTO skuLotBizDTO = skuLotBizClient.get(skuLotParam);
//                if (skuLotBizDTO == null) {
//                    throw new BaseException(BaseBizEnum.TIP, "库位批次属性异常");
//                }
//                SkuBizDTO skuBizDTO = skuBizClient.getDetail(skuBizParam).getData();
//                if (skuBizDTO != null) {
//                    item.setSkuName(skuBizDTO.getName());
//                    item.setUpcCode(skuBizDTO.getUpcCode());
//                }
//                item.setExpireDate(ConverterUtil.convertVoTime(skuLotBizDTO.getExpireDate(), "yyyy-MM-dd"));
//                item.setProductionDate(ConverterUtil.convertVoTime(skuLotBizDTO.getManufDate(), "yyyy-MM-dd"));
//                scanStockItems.add(item);
//            });
//            scanStockVO.setListItem(scanStockItems);
//        } else {
//            List<SkuBizDTO> findSkuList = skuBizClient.scanSkuCode(param.getCode()).getData();
//            if (CollectionUtils.isEmpty(findSkuList)) {
//                findSkuList = skuBizClient.scanUpcCode(param.getCode()).getData();
//            }
//            if (CollectionUtils.isEmpty(findSkuList)) {
//                throw new BaseException(BaseBizEnum.TIP, "无法扫描到相关信息");
//            } else {
//                ScanSkuVO scanSkuVO = new ScanSkuVO();
//                scanSkuVO.setSkuCode(findSkuList.stream().map(s -> s.getCode()).distinct().collect(Collectors.joining(",")));
//                String skuName = findSkuList.stream().map(s -> s.getName()).distinct().collect(Collectors.joining(","));
//                scanSkuVO.setSkuName(skuName);
//                oret.setScanSkuVO(scanSkuVO);
//                oret.setType("sku");
//                CodeListParam codeParam = new CodeListParam();
//                codeParam.setCodeList(findSkuList.stream().map(s -> s.getCode()).distinct().collect(Collectors.toList()));
//                List<StockLocationBizDTO> stockLocationBizDtoList = scanStockLocationBizClient.getScanSkuStockList(codeParam).getData();
//                Comparator<StockLocationBizDTO> locationCodeAsc = Comparator.comparing(StockLocationBizDTO::getLocationCode);
//                Comparator<StockLocationBizDTO> cargoCodeAsc = locationCodeAsc.thenComparing(Comparator.comparing(StockLocationBizDTO::getCargoCode));
//                Comparator<StockLocationBizDTO> finalPickSeqAsc = cargoCodeAsc.thenComparing(Comparator.comparing(StockLocationBizDTO::getPickSeq));
//                stockLocationBizDtoList = stockLocationBizDtoList.stream().sorted(finalPickSeqAsc).collect(Collectors.toList());
//                List<ScanSkuItem> scanStockItems = new ArrayList<>();
//                stockLocationBizDtoList.forEach(s -> {
//                    ScanSkuItem item = new ScanSkuItem();
//                    item.setSkuQuality(s.getSkuQuality());
//                    item.setPickSeq(s.getPickSeq());
//                    item.setSkuQualityName(SkuQualityEnum.getEnum(s.getSkuQuality()).getMessage());
//                    item.setCargoCode(s.getCargoCode());
//                    item.setCargoCodeName(s.getCargoName());
//                    item.setAvailableQty(s.getAvailableQty());
//                    item.setPhysicalQty(s.getPhysicalQty());
//                    item.setSkuLotNo(s.getSkuLotNo());
//                    item.setLocationCode(s.getLocationCode());
//                    SkuBizParam skuBizParam = new SkuBizParam();
//                    skuBizParam.setCode(s.getSkuCode());
//                    skuBizParam.setCargoCode(s.getCargoCode());
//                    SkuLotBizParam skuLotParam = new SkuLotBizParam();
//                    skuLotParam.setSkuCode(s.getSkuCode());
//                    skuLotParam.setSkuLotNo(s.getSkuLotNo());
//                    skuLotParam.setCargoCode(s.getCargoCode());
//                    SkuLotBizDTO skuLotBizDTO = skuLotBizClient.get(skuLotParam);
//                    if (skuLotBizDTO == null) {
//                        throw new BaseException(BaseBizEnum.TIP, "库位批次属性异常");
//                    }
//                    SkuBizDTO skuBizDTO = skuBizClient.getDetail(skuBizParam).getData();
//                    if (skuBizDTO != null) {
//                        item.setUpcCode(skuBizDTO.getUpcCode());
//                    }
//                    item.setExpireDate(ConverterUtil.convertVoTime(skuLotBizDTO.getExpireDate(), "yyyy-MM-dd"));
//                    item.setProductionDate(ConverterUtil.convertVoTime(skuLotBizDTO.getManufDate(), "yyyy-MM-dd"));
//                    scanStockItems.add(item);
//                });
//                scanSkuVO.setListItem(scanStockItems);
//            }
//        }
//        java.util.Map<String, Object> oretMap = new HashMap<>();
//        oretMap.put("type", oret.getType());
//        if (oret.getScanSkuVO() != null) {
//            oretMap.put("scanSkuVO", oret.getScanSkuVO());
//        } else if (oret.getScanStockVO() != null) {
//            oretMap.put("scanStockVO", oret.getScanStockVO());
//        }
//        return Result.success(oretMap);
//    }


    private ScanSkuItem getScanSkuItem(StockLocationBizDTO s) {
        ScanSkuItem item = new ScanSkuItem();
        item.setSkuQuality(s.getSkuQuality());
        item.setPickSeq(s.getPickSeq());
        item.setSkuQualityName(SkuQualityEnum.getEnum(s.getSkuQuality()).getMessage());
        item.setCargoCode(s.getCargoCode());
        item.setCargoCodeName(s.getCargoName());
        item.setAvailableQty(s.getAvailableQty());
        item.setPhysicalQty(s.getPhysicalQty());
        item.setSkuLotNo(s.getSkuLotNo());
        item.setLocationCode(s.getLocationCode());
        SkuBizParam skuBizParam = new SkuBizParam();
        skuBizParam.setCode(s.getSkuCode());
        skuBizParam.setCargoCode(s.getCargoCode());
        SkuLotBizParam skuLotParam = new SkuLotBizParam();
        skuLotParam.setSkuCode(s.getSkuCode());
        skuLotParam.setSkuLotNo(s.getSkuLotNo());
        skuLotParam.setCargoCode(s.getCargoCode());
        SkuLotBizDTO skuLotBizDTO = skuLotBizClient.get(skuLotParam);
        if (skuLotBizDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "库位批次属性异常");
        }
        SkuBizDTO skuBizDTO = skuBizClient.getDetail(skuBizParam).getData();
        if (skuBizDTO != null) {
            item.setSkuCode(skuBizDTO.getCode());
            item.setSkuName(skuBizDTO.getName());
            item.setUpcCode(skuBizDTO.getUpcCode());
        }
        item.setExpireDate(ConverterUtil.convertVoTime(skuLotBizDTO.getExpireDate(), "yyyy-MM-dd"));
        item.setProductionDate(ConverterUtil.convertVoTime(skuLotBizDTO.getManufDate(), "yyyy-MM-dd"));
        item.setProductionNo(skuLotBizDTO.getProductionNo());
        item.setValidityCode(skuLotBizDTO.getValidityCode());
        item.setInventoryType(skuLotBizDTO.getInventoryType());
        item.setInventoryTypeDesc(InventoryTypeEnum.desc(item.getInventoryType()));
        return item;
    }

    private ScanStockItem getScanStockItem(StockLocationBizDTO s) {
        ScanStockItem item = new ScanStockItem();
        item.setSkuQuality(s.getSkuQuality());
        item.setSkuQualityName(SkuQualityEnum.getEnum(s.getSkuQuality()).getMessage());
        item.setCargoCode(s.getCargoCode());
        item.setCargoCodeName(s.getCargoName());
        item.setAvailableQty(s.getAvailableQty());
        item.setPhysicalQty(s.getPhysicalQty());
        item.setSkuLotNo(s.getSkuLotNo());
        item.setSkuCode(s.getSkuCode());
        SkuBizParam skuBizParam = new SkuBizParam();
        skuBizParam.setCode(s.getSkuCode());
        skuBizParam.setCargoCode(s.getCargoCode());
        SkuLotBizParam skuLotParam = new SkuLotBizParam();
        skuLotParam.setSkuCode(s.getSkuCode());
        skuLotParam.setSkuLotNo(s.getSkuLotNo());
        skuLotParam.setCargoCode(s.getCargoCode());
        SkuLotBizDTO skuLotBizDTO = skuLotBizClient.get(skuLotParam);
        if (skuLotBizDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "库位批次属性异常");
        }
        SkuBizDTO skuBizDTO = skuBizClient.getDetail(skuBizParam).getData();
        if (skuBizDTO != null) {
            item.setSkuCode(skuBizDTO.getCode());
            item.setSkuName(skuBizDTO.getName());
            item.setUpcCode(skuBizDTO.getUpcCode());
        }
        item.setExpireDate(ConverterUtil.convertVoTime(skuLotBizDTO.getExpireDate(), "yyyy-MM-dd"));
        item.setProductionDate(ConverterUtil.convertVoTime(skuLotBizDTO.getManufDate(), "yyyy-MM-dd"));
        item.setProductionNo(skuLotBizDTO.getProductionNo());
        item.setInventoryType(skuLotBizDTO.getInventoryType());
        item.setValidityCode(skuLotBizDTO.getValidityCode());
        item.setInventoryTypeDesc(InventoryTypeEnum.desc(item.getInventoryType()));
        return item;
    }

    @Override
    @SoulClient(path = "/scan/scanPageSkuStock", desc = "扫描库位,商品信息")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<Map<String, Object>> scanPageSkuStock(CodePageParam param) {
        LocationBizParam param1 = new LocationBizParam();
        param1.setCode(param.getCode());
        LocationBizDTO locationBizDTO = locationBizClient.getDetail(param1).getData();
        StockSkuOrStockPageVO oret = new StockSkuOrStockPageVO();
        if (locationBizDTO != null) {
            CodeParam codeParam = new CodeParam();
            codeParam.setCode(locationBizDTO.getCode());
            oret.setType("location");
            ScanStockPageVO scanStockPageVO = new ScanStockPageVO();
            scanStockPageVO.setLocationCode(locationBizDTO.getCode());
            ZoneBizParam zoneBizParam = new ZoneBizParam();
            zoneBizParam.setCode(locationBizDTO.getZoneCode());
            Result<ZoneBizDTO> zoneBizDTOResult = izoneBizClient.getDetail(zoneBizParam);
            ZoneBizDTO zoneBizDTO = zoneBizDTOResult.getData();
            if (zoneBizDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "库位正次品属性异常");
            }
            scanStockPageVO.setSkuQuality(zoneBizDTO.getSkuQuality());
            scanStockPageVO.setSkuQualityName(SkuQualityEnum.getEnum(zoneBizDTO.getSkuQuality()).getMessage());
            Map sumQtyMap = scanStockLocationBizClient.findLocationSumQty(codeParam).getData();
            BigDecimal availableQty = BigDecimal.ZERO, physicalQty = BigDecimal.ZERO;
            if (sumQtyMap == null || sumQtyMap.get("availableQty") == null) {
                availableQty = BigDecimal.ZERO;
            } else {
                availableQty = (BigDecimal) sumQtyMap.get("availableQty");
            }
            if (sumQtyMap == null || sumQtyMap.get("physicalQty") == null) {
                physicalQty = BigDecimal.ZERO;
            } else {
                physicalQty = (BigDecimal) sumQtyMap.get("physicalQty");
            }
            scanStockPageVO.setAvailableQty(availableQty);
            scanStockPageVO.setPhysicalQty(physicalQty);
            oret.setScanStockPageVO(scanStockPageVO);
            StockLocationParam param2 = ConverterUtil.convert(param, StockLocationParam.class);
            param2.setSkuQuality(null);
            param2.setSkuCode(null);
            param2.setLocationCodeList(Arrays.asList(param.getCode()));
            Result<IPage<StockLocationBizDTO>> pageStockLocationBizResult = scanStockLocationBizClient.getScanLocationStockPage(param2);
            List<StockLocationBizDTO> stockBizList = pageStockLocationBizResult.getData().getRecords();
            PageVO<ScanStockItem> pageVO = new PageVO<>();
            PageVO.Page page = new PageVO.Page();
            page.setPageSize(pageStockLocationBizResult.getData().getSize());
            page.setCurrentPage(pageStockLocationBizResult.getData().getCurrent());
            page.setTotalPage(pageStockLocationBizResult.getData().getPages());
            page.setTotalCount(pageStockLocationBizResult.getData().getTotal());
            List<ScanStockItem> voList = stockBizList
                    .stream()
                    .flatMap(a -> Stream.of(getScanStockItem(a)))
                    .collect(Collectors.toList());
            pageVO.setPage(page);
            pageVO.setDataList(voList);
            scanStockPageVO.setListItem(pageVO);
        } else {
            List<SkuBizDTO> findSkuList  = new ArrayList<SkuBizDTO>();
            List<SkuBizDTO> _findSkuList = skuBizClient.scanSkuCode(param.getCode()).getData();
            if(!CollectionUtils.isEmpty(_findSkuList))
            {
                findSkuList.addAll(_findSkuList);
            }
            _findSkuList = skuBizClient.scanUpcCode(param.getCode()).getData();
            if(!CollectionUtils.isEmpty(_findSkuList))
            {
                findSkuList.addAll(_findSkuList);
            }
            /**
             * 两次查询防止代码，与条码相同数据库同一记录两条记录，
             * 通过id去重复
             */
            findSkuList = findSkuList.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(SkuBizDTO::getCode).thenComparing(SkuBizDTO::getCargoCode))),ArrayList::new));
            if (CollectionUtils.isEmpty(findSkuList)) {
                throw new BaseException(BaseBizEnum.TIP, "无法扫描到相关信息");
            } else {
                ScanSkuPageVO scanSkuVO = new ScanSkuPageVO();
                scanSkuVO.setSkuCode(param.getCode());
//                scanSkuVO.setSkuCode(findSkuList.stream().map(s -> s.getCode()).distinct().collect(Collectors.joining(",")));
//                String skuName = findSkuList.stream().map(s -> s.getName()).distinct().collect(Collectors.joining(","));
//                scanSkuVO.setSkuName(skuName);
                oret.setScanSkuPageVO(scanSkuVO);
                oret.setType("sku");
                StockLocationParam param2 = ConverterUtil.convert(param, StockLocationParam.class);
                param2.setSkuCodeList(findSkuList.stream().map(s -> s.getCode()).distinct().collect(Collectors.toList()));
                param2.setCargoCodeList(findSkuList.stream().map(s->s.getCargoCode()).distinct().collect(Collectors.toList()));
                param2.setSkuQuality(null);
                Result<IPage<StockLocationBizDTO>> pageStockLocationBizResult = scanStockLocationBizClient.getScanSkuStockPage(param2);
                List<StockLocationBizDTO> stockBizList = pageStockLocationBizResult.getData().getRecords();
                PageVO<ScanSkuItem> pageVO = new PageVO<>();
                PageVO.Page page = new PageVO.Page();
                page.setPageSize(pageStockLocationBizResult.getData().getSize());
                page.setCurrentPage(pageStockLocationBizResult.getData().getCurrent());
                page.setTotalPage(pageStockLocationBizResult.getData().getPages());
                page.setTotalCount(pageStockLocationBizResult.getData().getTotal());
                List<ScanSkuItem> voList = stockBizList
                        .stream()
                        .flatMap(a -> Stream.of(getScanSkuItem(a)))
                        .collect(Collectors.toList());
                pageVO.setPage(page);
                pageVO.setDataList(voList);
                scanSkuVO.setListItem(pageVO);
            }
        }
        java.util.Map<String, Object> oretMap = new HashMap<>();
        oretMap.put("type", oret.getType());
        if (oret.getScanSkuPageVO() != null) {
            oretMap.put("scanSkuPageVO", oret.getScanSkuPageVO());
        } else if (oret.getScanStockPageVO() != null) {
            oretMap.put("scanStockPageVO", oret.getScanStockPageVO());
        }
        return Result.success(oretMap);
    }
}
