package com.dt.portal.pda.web.vo.stock;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ScanStockItem implements Serializable {
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    @ApiModelProperty(value = "货主编码")
    private String cargoCodeName;
    private String skuCode;
    private String skuName;
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;
    @ApiModelProperty(value = "商品属性")
    private String skuQualityName;
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用=可用数")
    private BigDecimal availableQty;
    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;
    @ApiModelProperty(value = "商品条码")
    private String upcCode;
    @ApiModelProperty(value = "失效日期")
    private String expireDate;
    @ApiModelProperty(value = "生成日期")
    private String productionDate;
    @ApiModelProperty(value = "生产批次号")
    private String productionNo;
    @ApiModelProperty(value = "残次等级")
    private String inventoryType;
    private String inventoryTypeDesc;
    @ApiModelProperty(value = "效期码")
    private String validityCode;

}
