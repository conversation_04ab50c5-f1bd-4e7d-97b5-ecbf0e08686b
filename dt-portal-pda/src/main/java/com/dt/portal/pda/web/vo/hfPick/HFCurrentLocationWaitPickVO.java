package com.dt.portal.pda.web.vo.hfPick;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class HFCurrentLocationWaitPickVO implements Serializable {


    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    @ApiModelProperty(value = "失效日期")
    private String expireDateDesc;

    @ApiModelProperty(value = "待拣货数量")
    private String qty;

}
