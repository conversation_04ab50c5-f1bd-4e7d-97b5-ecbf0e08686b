package com.dt.portal.pda.web.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.dto.stock.StockExpireInversionBizDTO;
import com.dt.platform.wms.param.CodePageParam;
import com.dt.platform.wms.param.stock.StockLocationBizParam;
import com.dt.portal.pda.web.vo.stock.StockLocationVO;
import com.dt.portal.pda.web.vo.stock.StockScanUpcOrLocationVO;

import java.util.List;


/**
 * <p>
 * 三级库存 管理 网关接口
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-14
 */
public interface IStockLocationPortalClient {


    /**
     * 商品查询
     *
     * @param param
     * @return
     */
    Result<StockScanUpcOrLocationVO> getScanUpcOrLocationStockList(CodePageParam param);

    /**
     * 库存查询
     * @param param
     * @return
     */
    Result<PageVO<StockLocationVO>> stockLocationPage(StockLocationBizParam param);

    /**
     * 扫描商品信息
     *
     * @param param
     * @return
     */
    Result<PageVO<StockLocationVO>> getScanUpcStockList(CodePageParam param);

    /**
     * 获取当前库位效期倒挂的数据
     * @param param
     * @return
     */
    Result<List<StockExpireInversionBizDTO>> getStockExpireInversion(StockLocationBizParam param);


}
