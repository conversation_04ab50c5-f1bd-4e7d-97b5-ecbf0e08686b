package com.dt.portal.pda.web.vo.stock;

import com.dt.component.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 三级库存
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "StockLocation对象", description = "三级库存")
public class StockLocationVO extends BaseVO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;
    private String warehouseName;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String cargoName;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private String skuName;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;
    private String skuQualityName;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;
    private String zoneName;

    @ApiModelProperty(value = "库区类型")
    private String zoneType;
    private String zoneTypeName;


    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "库区类型")
    private String locationType;
    private String locationTypeName;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "生产批次")
    private String productionNo;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    @ApiModelProperty(value = "入库日期")
    private String receiveDate;

    @ApiModelProperty(value = "生产日期")
    private String manufDate;

    @ApiModelProperty(value = "失效/过期日期")
    private String expireDate;

    @ApiModelProperty(value = "禁售日期")
    private String withdrawDate;

    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    @ApiModelProperty(value = "待上架数量")
    private BigDecimal waitShelfQty;

    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用-待上架数量=可用数")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "状态码 ")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty("移位单")
    private String moveCode;
    @ApiModelProperty(value = "残次等级")
    private String inventoryType;
    private String inventoryTypeDesc;
}