package com.dt.portal.pda.web.client.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.LocationTypeEnum;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.IStockLocationBizClient;
import com.dt.platform.wms.dto.stock.StockExpireInversionBizDTO;
import com.dt.platform.wms.dto.stock.StockLocationWithLotBizDTO;
import com.dt.platform.wms.param.CodePageParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.stock.StockLocationBizParam;
import com.dt.portal.pda.web.client.IStockLocationPortalClient;
import com.dt.portal.pda.web.vo.stock.StockLocationVO;
import com.dt.portal.pda.web.vo.stock.StockScanUpcOrLocationVO;
import com.dt.portal.pda.web.vo.stock.StockStatisticsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class StockLocationPortalClient implements IStockLocationPortalClient {


    @DubboReference
    private IStockLocationBizClient stockLocationBizClient;

    @Override
    @SoulClient(path = "/stockLocation/getScanUpcOrLocationStockList", desc = "PDA-扫描商品条码或库存获取数据")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<StockScanUpcOrLocationVO> getScanUpcOrLocationStockList(CodePageParam param) {
        log.info("warehouseCode:{} getScanUpcOrLocationStockList:{}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(param));
        Result<StockScanUpcOrLocationVO> result = Result.fail();
        StockScanUpcOrLocationVO stockScanUpcOrLocation = new StockScanUpcOrLocationVO();
        if (StringUtils.isEmpty(param.getType()) || "location".equals(param.getType())) {
            try {
                Result<Page<StockLocationWithLotBizDTO>> stockBizPage = stockLocationBizClient.getScanLocationStockPage(param);
                List<StockLocationWithLotBizDTO> stockBizList = stockBizPage.getData().getRecords();
                //格式化数据
                if (!CollectionUtils.isEmpty(stockBizList)) {
                    PageVO<StockLocationVO> pageVO = new PageVO<>();
                    PageVO.Page page = new PageVO.Page();
                    page.setPageSize(stockBizPage.getData().getSize());
                    page.setCurrentPage(stockBizPage.getData().getCurrent());
                    page.setTotalPage(stockBizPage.getData().getPages());
                    page.setTotalCount(stockBizPage.getData().getTotal());

                    List<StockLocationVO> voList = stockBizList.stream()
                            .flatMap(a -> Stream.of(getStockLocationVO(a)))
                            .collect(Collectors.toList());

                    pageVO.setPage(page);
                    pageVO.setDataList(voList);
                    stockScanUpcOrLocation.setType("location");
                    stockScanUpcOrLocation.setLocation(pageVO);
                }
            } catch (Exception e) {
                //.ignore
            }
        }
        if (StringUtils.isEmpty(param.getType()) || "upc".equals(param.getType())) {
            try {
                CodeParam codeParam = new CodeParam();
                codeParam.setCode(param.getCode());
                Result<List<StockLocationWithLotBizDTO>> stockBizResult = stockLocationBizClient.getScanUpcStockList(codeParam);
                List<StockLocationWithLotBizDTO> stockBizList = stockBizResult.getData();
                if (!CollectionUtils.isEmpty(stockBizList)) {
                    stockScanUpcOrLocation.setType("upc");
                    stockScanUpcOrLocation.setUpc(getStockStatisticsList(stockBizList));
                }
            } catch (Exception e) {
                //.ignore
            }
        }
        if (!ObjectUtils.isEmpty(stockScanUpcOrLocation.getUpc()) || !ObjectUtils.isEmpty(stockScanUpcOrLocation.getLocation())) {
            result = Result.success(stockScanUpcOrLocation);
        } else {
            throw new BaseException(BaseBizEnum.TIP, "未查到库存数据!!!");
        }
        return result;
    }

    @Override
    @SoulClient(path = "/stockLocation/stockLocationPage", desc = "PDA-库存查询")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<PageVO<StockLocationVO>> stockLocationPage(StockLocationBizParam param) {
        log.info("warehouseCode:{} getScanUpcOrLocationStockList:{}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(param));
        Result<Page<StockLocationWithLotBizDTO>> pageResult = stockLocationBizClient.getPageWithLot(param);
        PageVO<StockLocationVO> pageVO = new PageVO<>();
        PageVO.Page page = new PageVO.Page();
        page.setPageSize(pageResult.getData().getSize());
        page.setCurrentPage(pageResult.getData().getCurrent());
        page.setTotalPage(pageResult.getData().getPages());
        page.setTotalCount(pageResult.getData().getTotal());

        List<StockLocationVO> voList = pageResult.getData().getRecords().stream()
                .flatMap(a -> Stream.of(getStockLocationVO(a)))
                .collect(Collectors.toList());

        pageVO.setPage(page);
        pageVO.setDataList(voList);
        return Result.success(pageVO);

    }

    @Override
    @SoulClient(path = "/stockLocation/getScanUpcStockList", desc = "PDA-扫描商品条码获取分页数据")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public Result<PageVO<StockLocationVO>> getScanUpcStockList(CodePageParam param) {
        log.info("warehouseCode:{} getScanUpcStockList:{}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(param));
        Result<PageVO<StockLocationVO>> result = Result.fail();
        PageVO<StockLocationVO> pageVO = new PageVO<>();
        try {
            Result<Page<StockLocationWithLotBizDTO>> stockBizResult = stockLocationBizClient.getScanUpcStockPage(param);
            List<StockLocationWithLotBizDTO> stockBizList = stockBizResult.getData().getRecords();
            //组装VO Page数据
            PageVO.Page page = new PageVO.Page();
            page.setPageSize(stockBizResult.getData().getSize());
            page.setCurrentPage(stockBizResult.getData().getCurrent());
            page.setTotalPage(stockBizResult.getData().getPages());
            page.setTotalCount(stockBizResult.getData().getTotal());
            List<StockLocationVO> voList = stockBizList
                    .stream()
                    .flatMap(a -> Stream.of(getStockLocationVO(a)))
                    .collect(Collectors.toList());
            pageVO.setPage(page);
            pageVO.setDataList(voList);
            result = Result.success(pageVO);
        } catch (Exception e) {
            result = Result.fail(BaseBizEnum.TIP.getValue(), "扫描UPC获取数据异常", null);
        }
        return result;
    }

    @Override
    @SoulClient(path = "/stockLocation/getStockExpireInversion", desc = "获取效期倒挂数据")
    public Result<List<StockExpireInversionBizDTO>> getStockExpireInversion(StockLocationBizParam param) {
        log.info("warehouseCode:{} getStockExpireInversion:{}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(param));
        if (StringUtils.isEmpty(param)) {
            throw new BaseException(BaseBizEnum.TIP, "必要参数不能为空");
        }
        if (StringUtils.isEmpty(param.getCargoCode())) {
            throw new BaseException(BaseBizEnum.TIP, "货主编码必要参数不能为空");
        }
        if (StringUtils.isEmpty(param.getSkuCode())) {
            throw new BaseException(BaseBizEnum.TIP, "商品编码必要参数不能为空");
        }
        if (StringUtils.isEmpty(param.getSkuLotNo())) {
            throw new BaseException(BaseBizEnum.TIP, "当前商品批次必要参数不能为空");
        }
        if (StringUtils.isEmpty(param.getSkuQuality())) {
            throw new BaseException(BaseBizEnum.TIP, "商品属性必要参数不能为空");
        }
        return stockLocationBizClient.getStockExpireInversion(param);
    }

    private List<StockStatisticsVO> getStockStatisticsList(List<StockLocationWithLotBizDTO> stockBizList) {
        List<StockStatisticsVO> stockStatisticsInfoList = new ArrayList<>();
        //格式化数据
        if (!CollectionUtils.isEmpty(stockBizList)) {
            List<StockLocationVO> stockLocationVOS = stockBizList.stream().flatMap(a -> Stream.of(getStockLocationVO(a))).collect(Collectors.toList());

            Map<String, List<StockLocationVO>> listMap = stockLocationVOS.stream().collect(Collectors.groupingBy(a -> a.getCargoCode()));
//                    collect(Collectors.toMap(StockLocationVO::getCargoCode, Function.identity()));
            for (Map.Entry<String, List<StockLocationVO>> entity : listMap.entrySet()) {
                List<StockLocationVO> voList = entity.getValue();
                List<StockLocationVO> avlStockLocationList = voList.stream().filter(a -> SkuQualityEnum.SKU_QUALITY_AVL.getLevel().equals(a.getSkuQuality())).collect(Collectors.toList());
                List<StockLocationVO> damageStockLocationList = voList.stream().filter(a -> SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel().equals(a.getSkuQuality())).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(avlStockLocationList)) {
                    StockStatisticsVO stockStatistics = ConverterUtil.convert(avlStockLocationList.get(0), StockStatisticsVO.class);
                    if (!ObjectUtils.isEmpty(stockStatistics)) {
                        stockStatistics.setPhysicalQty(avlStockLocationList.stream().map(StockLocationVO::getPhysicalQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        stockStatistics.setAvailableQty(avlStockLocationList.stream().map(StockLocationVO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        stockStatistics.setOccupyQty(avlStockLocationList.stream().map(StockLocationVO::getOccupyQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        stockStatistics.setFrozenQty(avlStockLocationList.stream().map(StockLocationVO::getFrozenQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        stockStatistics.setWaitShelfQty(avlStockLocationList.stream().map(StockLocationVO::getWaitShelfQty).reduce(BigDecimal.ZERO, BigDecimal::add));
//                    stockStatistics.setStockLocationList(avlStockLocationList);
                        stockStatisticsInfoList.add(stockStatistics);
                    }
                }
                if (!CollectionUtils.isEmpty(damageStockLocationList)) {
                    StockStatisticsVO stockStatistics = ConverterUtil.convert(damageStockLocationList.get(0), StockStatisticsVO.class);
                    if (!ObjectUtils.isEmpty(stockStatistics)) {
                        stockStatistics.setPhysicalQty(damageStockLocationList.stream().map(StockLocationVO::getPhysicalQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        stockStatistics.setAvailableQty(damageStockLocationList.stream().map(StockLocationVO::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        stockStatistics.setOccupyQty(damageStockLocationList.stream().map(StockLocationVO::getOccupyQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        stockStatistics.setFrozenQty(damageStockLocationList.stream().map(StockLocationVO::getFrozenQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        stockStatistics.setWaitShelfQty(damageStockLocationList.stream().map(StockLocationVO::getWaitShelfQty).reduce(BigDecimal.ZERO, BigDecimal::add));
//                    stockStatistics.setStockLocationList(damageStockLocationList);
                        stockStatisticsInfoList.add(stockStatistics);
                    }
                }
            }
        }
        return stockStatisticsInfoList;
    }

    private StockLocationVO getStockLocationVO(StockLocationWithLotBizDTO a) {
        StockLocationVO stockVO = ConverterUtil.convert(a, StockLocationVO.class);
        if (!ObjectUtils.isEmpty(stockVO)) {
            stockVO.setZoneTypeName(ZoneTypeEnum.getEnum(stockVO.getZoneType()).getName());
            stockVO.setLocationTypeName(LocationTypeEnum.getEnum(stockVO.getLocationType()).getName());
            stockVO.setSkuQualityName(SkuQualityEnum.getEnum(stockVO.getSkuQuality()).getMessage());
            stockVO.setCreatedTime(ConverterUtil.convertVoTime(a.getCreatedTime()));
            stockVO.setUpdatedTime(ConverterUtil.convertVoTime(a.getUpdatedTime()));

            stockVO.setExpireDate(ConverterUtil.convertVoTime(a.getExpireDate(), a.getExpireDateFormat()));
            stockVO.setManufDate(ConverterUtil.convertVoTime(a.getManufDate(), a.getManufDateFormat()));
            stockVO.setReceiveDate(ConverterUtil.convertVoTime(a.getReceiveDate(), a.getReceiveDateFormat()));
            stockVO.setWithdrawDate(ConverterUtil.convertVoTime(a.getWithdrawDate(), a.getWithdrawDateFormat()));
            stockVO.setProductionNo(a.getProductionNo());
            stockVO.setValidityCode(a.getValidityCode());
        }
        return stockVO;
    }
}
