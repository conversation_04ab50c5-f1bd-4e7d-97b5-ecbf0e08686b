package com.dt.portal.pda.web.client.impl;

import cn.hutool.json.JSONUtil;
import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.client.hf.IHFBizClient;
import com.dt.platform.wms.dto.hfPick.*;
import com.dt.platform.wms.dto.pick.PickGuideBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.hfPick.*;
import com.dt.portal.pda.web.client.IHFPortalClient;
import com.dt.portal.pda.web.vo.hfPick.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class HFPortalClientImpl implements IHFPortalClient {


    @DubboReference
    IHFBizClient hfBizClient;

    @Override
    @SoulClient(path = "/pick/getHFTaskForUser", desc = "获取当前用户已有拣选任务")
    public Result<List<HFCurrentPickTaskVO>> getHFTaskForUser() {
        log.info("getHFTaskForUser :{}", CurrentUserHolder.getUserName());
        Result<List<HFCurrentPickTaskBizDTO>> hfTaskForUser = hfBizClient.getHFTaskForUser();
        return Result.success(ConverterUtil.convertList(hfTaskForUser.getData(), HFCurrentPickTaskVO.class));

    }

    @Override
    @SoulClient(path = "/pick/getHFExistsTaskForUser", desc = "获取当前用户是否存在拣选任务")
    public Result<Boolean> getHFExistsTaskForUser() {
        log.info("getHFExistsTaskForUser :{}", CurrentUserHolder.getUserName());
        Result<Boolean> hfTaskForUser = hfBizClient.getHFExistsTaskForUser();
        return Result.success(hfTaskForUser.getData());
    }

    @Override
    @SoulClient(path = "/pick/getHFTaskPage", desc = "获取所有可待拣选任务")
    public Result<HFCurrentPickTaskPageVO> getHFTaskPage(SearchParamBizParam param) {
        log.info("getHFTaskWaitReceive :{}", CurrentUserHolder.getUserName());
        Result<HFCurrentPickTaskPageBizDTO> hfTaskForUser = hfBizClient.getHFTaskPage(param);
        return Result.success(ConverterUtil.convert(hfTaskForUser.getData(), HFCurrentPickTaskPageVO.class));
    }

    @Override
    @SoulClient(path = "/pick/bindContByPickCode", desc = "拣选单绑定任务")
    public Result<String> bindContByPickCode(ContBindPickBizParam param) {
        log.info("bindContByPickCode :{}", JSONUtil.toJsonStr(param));
        if (param == null || StringUtils.isEmpty(param.getContCode())
                || StringUtils.isEmpty(param.getPickCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<String> result = hfBizClient.bindContByPickCode(param);
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/pick/getPickWaitSku", desc = "库位获取拣选拣选数据")
    public Result<List<PickGuidePDAVO>> getPickWaitSku(WaitPickBizParam param) {
        log.info("getPickWaitSku :{}", JSONUtil.toJsonStr(param));
        if (param == null || StringUtils.isEmpty(param.getPickCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        //如果不传值，只显示待检明细
        if (param.getGetAll() == null) {
            param.setGetAll(false);
        }
        Result<List<PickGuideBizDTO>> result = hfBizClient.getPickWaitSku(param);
        return Result.success(ConverterUtil.convertList(result.getData(), PickGuidePDAVO.class));
    }

    @Override
    @SoulClient(path = "/pick/getPickRecommendLocation", desc = "获取拣选单的推荐库位")
    public Result<String> getPickRecommendLocation(CodeParam param) {
        log.info("getPickRecommendLocation :{}", JSONUtil.toJsonStr(param));
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<String> result = hfBizClient.getPickRecommendLocation(param);
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/pick/getSkuFromLocation", desc = "获取库位sku信息")
    public Result<HFPickSkuVO> getSkuFromLocation(PickSkuAndLocationBizParam param) {
        log.info("getSkuFromLocation :{}", JSONUtil.toJsonStr(param));
        if (param == null || StringUtils.isEmpty(param.getPickCode())
                || StringUtils.isEmpty(param.getLocationCode()) || StringUtils.isEmpty(param.getUpcCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<HFPickSkuBizDTO> result = hfBizClient.getSkuFromLocation(param);
        return Result.success(ConverterUtil.convert(result.getData(), HFPickSkuVO.class));
    }

    @Override
    @SoulClient(path = "/pick/commitSku", desc = "提交商品信息")
    public Result<String> commitSku(PickCommitSkuBizParam param) {
        log.info("commitSku :{}", JSONUtil.toJsonStr(param));
        if (param == null
                || StringUtils.isEmpty(param.getPickCode())
                || StringUtils.isEmpty(param.getLocationCode())
                || StringUtils.isEmpty(param.getSkuCode())
                || StringUtils.isEmpty(param.getSkuLotNo())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<String> result = hfBizClient.commitSku(param);
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/pick/commitCWSku", desc = "提交CW商品信息")
    public Result<String> commitCWSku(PickCommitSkuBizParam param) {
        log.info("commitCWSku :{}", JSONUtil.toJsonStr(param));
        if (param == null
                || StringUtils.isEmpty(param.getPickCode())
                || StringUtils.isEmpty(param.getLocationCode())
                || StringUtils.isEmpty(param.getSkuCode())
                || StringUtils.isEmpty(param.getCommitQty())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<String> result = hfBizClient.commitCWSku(param);
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/pick/queryCWSkuQty", desc = "获取CW商品信息数量")
    public Result<BigDecimal> queryCWSkuQty(PickCommitSkuBizParam param) {
        log.info("queryCWSkuQty :{}", JSONUtil.toJsonStr(param));
        if (param == null
                || StringUtils.isEmpty(param.getPickCode())
                || StringUtils.isEmpty(param.getLocationCode())
                || StringUtils.isEmpty(param.getSkuCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<BigDecimal> result = hfBizClient.queryCWSkuQty(param);
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/pick/completePicK", desc = "完成拣选单")
    public Result<Boolean> completePicK(CodeParam param) {
        log.info("completePicK :{}", JSONUtil.toJsonStr(param));
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = hfBizClient.completePicK(param);
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/pick/receivePick", desc = "领取拣选单")
    public Result<Boolean> receivePick(CodeParam param) {
        log.info("receivePick :{}", JSONUtil.toJsonStr(param));
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<Boolean> result = hfBizClient.receivePick(param);
        return Result.success(result.getData());
    }

    @Override
    @SoulClient(path = "/pick/scanLocation", desc = "扫描库位")
    public Result<HFCurrentPickTaskVO> scanLocation(ScanLocationBizParam param) {
        log.info("scanLocation :{}", JSONUtil.toJsonStr(param));
        if (param == null || StringUtils.isEmpty(param.getPickCode())
                || StringUtils.isEmpty(param.getLocationCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<HFCurrentPickTaskBizDTO> result = hfBizClient.scanLocation(param);
        return Result.success(ConverterUtil.convert(result.getData(), HFCurrentPickTaskVO.class));
    }

    @Override
    @SoulClient(path = "/pick/waitPickLocationDetail", desc = "待检库位明细")
    public Result<List<HFCurrentWaitPickVO>> waitPickLocationDetail(CodeParam param) {
        log.info("waitPickLocationDetail :{}", JSONUtil.toJsonStr(param));
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<List<HFCurrentWaitPickBizDTO>> result = hfBizClient.waitPickLocationDetail(param);
        return Result.success(ConverterUtil.convertList(result.getData(), HFCurrentWaitPickVO.class));
    }

    @Override
    @SoulClient(path = "/pick/currentPickLocationDetail", desc = "当前库位明细")
    public Result<List<HFCurrentLocationWaitPickVO>> currentPickLocationDetail(ScanLocationBizParam param) {
        log.info("currentPickLocationDetail :{}", JSONUtil.toJsonStr(param));
        if (param == null || StringUtils.isEmpty(param.getPickCode())
                || StringUtils.isEmpty(param.getLocationCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        Result<List<HFCurrentLocationWaitPickBizDTO>> result = hfBizClient.currentPickLocationDetail(param);
        return Result.success(ConverterUtil.convertList(result.getData(), HFCurrentLocationWaitPickVO.class));
    }

}
