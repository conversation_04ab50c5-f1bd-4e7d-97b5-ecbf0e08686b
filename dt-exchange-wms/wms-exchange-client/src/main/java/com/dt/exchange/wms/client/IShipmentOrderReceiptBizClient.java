package com.dt.exchange.wms.client;

import com.dt.component.common.result.Result;
import com.dt.exchange.wms.param.ModifyShipmentOrderBizParam;
import com.dt.exchange.wms.param.ModifyShipmentOrderNewBizParam;
import com.dt.exchange.wms.param.ShipmentOrderBizParam;

/**
 *
 */
public interface IShipmentOrderReceiptBizClient {
    /**
     * 创建出库单
     *
     * @param shipmentOrderBizParam
     * @return
     */
    Result<String> receipt(ShipmentOrderBizParam shipmentOrderBizParam);

    /**
     * 功能描述:  出库单修改地址
     * 创建时间:  2021/4/2 3:31 下午
     *
     * @param modifyShipmentOrderBizParam:
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     */
    Result<String> modify(ModifyShipmentOrderBizParam modifyShipmentOrderBizParam);

    /**
     * 功能描述:  出库单修改地址抖超 返回结果是新单据的wms出库单号
     * @param modifyShipmentOrderNewBizParam
     * @return
     */
    Result<String> modifyByDouChao(ModifyShipmentOrderNewBizParam modifyShipmentOrderNewBizParam);
}
