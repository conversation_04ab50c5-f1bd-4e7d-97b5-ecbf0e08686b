package com.dt.exchange.wms.biz.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.enums.tally.TallyStatusEnum;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.param.*;
import com.dt.domain.bill.dto.tally.TallyDTO;
import com.dt.domain.bill.dto.tally.TallyDetailDTO;
import com.dt.domain.bill.param.tally.TallyDetailParam;
import com.dt.domain.bill.param.tally.TallyParam;
import com.dt.exchange.wms.biz.IErpSkuForModifyBiz;
import com.dt.exchange.wms.biz.config.WarehouseOtherConfig;
import com.dt.exchange.wms.entity.ResultInfo;
import com.dt.exchange.wms.integration.IRemoteSkuClient;
import com.dt.exchange.wms.integration.tally.IRemoteTallyClient;
import com.dt.exchange.wms.integration.tally.IRemoteTallyDetailClient;
import com.dt.exchange.wms.param.ErpSkuBatchParam;
import com.dt.exchange.wms.param.ErpSkuParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.LambdaHelpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2021/01/12  14:24
 * @Describe
 **/
@Slf4j
@Service
public class ErpSkuForModifyBiz implements IErpSkuForModifyBiz {

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    CheckWarehouseBiz checkWarehouseClient;


    @Resource
    WarehouseOtherConfig warehouseOtherConfig;


    @Resource
    private IRemoteTallyClient remoteTallyClient;

    @Resource
    private IRemoteTallyDetailClient remoteTallyDetailClient;


    @Override
    public ResultInfo receiptSku(ErpSkuBatchParam param) {
        List<ErpSkuParam> erpSkuParamList = param.getErpSkuList();
        //请求参数校验
        if (CollectionUtils.isEmpty(erpSkuParamList) || erpSkuParamList.size() > 1000) {
            return ResultInfo.fail("参数不正确", "");
        }
        List<SkuParam> skuParamList = ConverterUtil.convertList(erpSkuParamList, SkuParam.class);
        if (CollectionUtils.isEmpty(skuParamList)) {
            return ResultInfo.fail("ERP 推送SKU列表不能为空", "");
        }

        //校验传递的数据
        List<SkuParam> modifyParamList = new ArrayList<>();
        for (Iterator<SkuParam> its = skuParamList.iterator(); its.hasNext(); ) {
            SkuParam skuModifyParam = its.next();

            //校验商品code  必填且不允许修改
            if (StringUtils.isEmpty(skuModifyParam.getCode())) {
                return ResultInfo.fail("提供的商品编码异常", "");
            }
            //校验仓库code  必填且不允许修改
            Result<String> checkResult = checkWarehouseClient.checkWareHouse(skuModifyParam.getWarehouseCode());
            if (!checkResult.checkSuccess()) {
                return ResultInfo.fail(checkResult.getMessage(), "");
            }
            //检验货主code  必填且不允许修改
            if (StringUtils.isEmpty(skuModifyParam.getCargoCode())) {
                return ResultInfo.fail("提供的货主编码异常", "");
            }
            SkuDTO skuDTO = remoteSkuClient.querySkuByCode(skuModifyParam.getCode(), skuModifyParam.getCargoCode());
            if (skuDTO == null) {
                return ResultInfo.fail("商品编码未找到商品信息", "");
            }
            Integer lifeCycle = skuModifyParam.getLifeCycle();
            Integer rejectCycle = skuModifyParam.getRejectCycle();
            Integer withdrawCycle = skuModifyParam.getWithdrawCycle();
            Integer warnCycle = skuModifyParam.getWarnCycle();

            if (Objects.equals("", skuModifyParam.getBrandName())) {
                return ResultInfo.fail("品牌名称不能为空", "");
            }
            if (Objects.equals("", skuModifyParam.getBrandCode())) {
                return ResultInfo.fail("品牌编码不能为空", "");
            }
            if (!ObjectUtils.isEmpty(skuModifyParam.getIsLifeMgt()) && !(new Integer(1).equals(skuModifyParam.getIsLifeMgt()))) {
                if (!(new Integer(-1).equals(skuModifyParam.getIsLifeMgt()))) {
                    return ResultInfo.fail("保质期管理设置有误", "");
                }
            }
            if (!ObjectUtils.isEmpty(skuModifyParam.getIsLifeMgt()) && new Integer(1).equals(skuModifyParam.getIsLifeMgt())) {

                if (!ObjectUtils.isEmpty(lifeCycle)) {
                    if (lifeCycle <= 0) {
                        return ResultInfo.fail("保质期天数必须大于0", "");
                    }
                }
                if (!ObjectUtils.isEmpty(rejectCycle)) {
                    if (rejectCycle <= 0) {
                        return ResultInfo.fail("禁收时限天数必须大于0", "");
                    }
                }
                if (!ObjectUtils.isEmpty(withdrawCycle)) {
                    if (withdrawCycle <= 0) {
                        return ResultInfo.fail("禁售时限天数必须大于0", "");
                    }
                }
                if (!ObjectUtils.isEmpty(warnCycle)) {
                    if (warnCycle <= 0) {
                        return ResultInfo.fail("预警天数必须大于0", "");
                    }
                }
                //以下情况，WMS不允许修改效期天数：
                //1. 当前sku，存在【创建、审核中、审核驳回】状态的理货报告，并且有理货明细，不允许修改效期天数， 返回上游“当前商品正在理货，按照旧效期天数有提交明细，不允许修改效期天数”
                //效期天数不一致需要校验
                if (lifeCycle != null && skuDTO.getLifeCycle() != skuModifyParam.getLifeCycle()) {
                    Boolean isTally = checkTallySku(skuModifyParam.getCargoCode(), skuModifyParam.getCode());
                    if (isTally) {
                        return ResultInfo.fail(String.format("当前商品%s正在理货，按照旧效期天数有提交明细，不允许修改效期天数", skuModifyParam.getCode()), "");
                    }
                }
            }
            //todo add 2021-04-22 长宽高，体积，毛重，净重 上游下发不修改
            skuModifyParam.setGrossWeight(null);
            skuModifyParam.setNetWeight(null);
            skuModifyParam.setVolume(null);
            skuModifyParam.setWidth(null);
            skuModifyParam.setHeight(null);
            skuModifyParam.setLength(null);
            skuModifyParam.setItemCode(null);
            //TODO 淘天可以修改条码信息 条码不为空
            // TODO 抖超  2025-05-12 支持修改条码
            Boolean modifyUpc = false;
            if (warehouseOtherConfig.isTaoTian(CurrentRouteHolder.getWarehouseCode())
                    || skuDTO.getTaotian()
                    || skuDTO.getDouChao()) {
                modifyUpc = true;
            }
            if (modifyUpc && !StringUtils.isEmpty(skuModifyParam.getUpcCode())) {
                SkuUpcParam skuUpcParam = new SkuUpcParam();
                skuUpcParam.setCargoCode(skuModifyParam.getCargoCode());
                skuUpcParam.setSkuCode(skuModifyParam.getCode());
                List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
                if (!CollectionUtils.isEmpty(skuUpcList)) {
                    String defaultUpcCode = skuUpcList.stream().filter(a -> Objects.equals(a.getIsDefault(), SkuUpcDefaultEnum.YES.getStatus())).map(SkuUpcDTO::getUpcCode).findFirst().orElse(null);
                    if (defaultUpcCode == null) {
                        return ResultInfo.fail("修改默认条码为空", "");
                    }
                    List<String> upcCodeListFrom = Arrays.asList(skuModifyParam.getUpcCode().split(CommonConstantUtil.COMMA));
                    if (!CollectionUtils.isEmpty(upcCodeListFrom)) {
                        SkuUpcParam skuUpcOtherParam = new SkuUpcParam();
                        skuUpcOtherParam.setCargoCode(skuModifyParam.getCargoCode());
                        skuUpcOtherParam.setUpcCodeList(Arrays.asList(skuModifyParam.getUpcCode().split(CommonConstantUtil.COMMA)));
                        List<SkuUpcDTO> skuUpcOtherList = remoteSkuClient.getSkuUpcList(skuUpcOtherParam);
                        if (!CollectionUtils.isEmpty(skuUpcOtherList)) {
                            List<SkuUpcDTO> upcOtherList = skuUpcOtherList.stream().filter(a -> !Objects.equals(a.getSkuCode(), skuModifyParam.getCode())).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(upcOtherList)) {
                                String errorMsg = upcOtherList.stream().map(it -> String.format("商品条码:%s,存在同货主商品编码:%s", it.getUpcCode(), it.getSkuCode())).collect(Collectors.joining(","));
                                return ResultInfo.fail(errorMsg, "");
                            }
                        }
                    }

                    List<SkuUpcDTO> commitUpcList = new ArrayList<>();
                    List<SkuUpcDTO> removeUpcList = new ArrayList<>();
                    buildSkuUpcList(skuUpcList, commitUpcList, removeUpcList, skuModifyParam.getUpcCode(), defaultUpcCode);

                    //新增条码和修改条码
                    if (!CollectionUtils.isEmpty(commitUpcList)) {
                        skuModifyParam.setSkuUpcModifyTaoTianDTOList(commitUpcList);
                    }
                    //移除条码
                    if (!CollectionUtils.isEmpty(removeUpcList)) {
                        skuModifyParam.setSkuUpcRemoveTaoTianDTOList(removeUpcList);
                    }
                }
            }
            //TODO 由于商品中心化 ERP下发修改仅仅修改商品条码 2024-09-27
            skuModifyParam.setOnlyModifyUpcCode(true);
            log.info("skuModifyParam-receiptSku :{}", JSONUtil.toJsonStr(skuModifyParam));
            modifyParamList.add(skuModifyParam);
        }

        SkuBatchParam skuBatchParam = new SkuBatchParam();
        skuBatchParam.setSkuList(modifyParamList);
        try {
            Result result = remoteSkuClient.modifyBatch(skuBatchParam);
            if ((Boolean) result.getData()) {
                List<String> list = skuBatchParam.getSkuList().stream().map(s -> s.getCode()).collect(Collectors.toList());
                String skuCode = String.join(",", list);
                SkuBatchLogParam skuBatchLogParam = new SkuBatchLogParam();
                skuBatchLogParam.setSkuLogList(skuBatchParam.getSkuList().stream().map(this::buildLog).collect(Collectors.toList()));
                remoteSkuClient.saveBatchLog(skuBatchLogParam);
                return ResultInfo.success("更新成功", skuCode);
            } else {
                return ResultInfo.fail("更新失败", result.getMessage());
            }
        } catch (Exception ex) {
            log.info("更新数据库异常={}", ex.getMessage());
            return ResultInfo.fail("更新数据库异常", "");
        }
    }

    /**
     * @param skuUpcList
     * @param commitUpcList
     * @param removeUpcList
     * @param modifyUpcCode
     * @param defaultUpcCode
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2024/5/20 11:00
     */
    private void buildSkuUpcList(List<SkuUpcDTO> skuUpcList, List<SkuUpcDTO> commitUpcList, List<SkuUpcDTO> removeUpcList, String modifyUpcCode, String defaultUpcCode) {
        List<String> upcCodeOriginList = skuUpcList.stream().map(SkuUpcDTO::getUpcCode).collect(Collectors.toList());
        List<String> upcCodeModifyList = Arrays.asList(modifyUpcCode.split(CommonConstantUtil.COMMA)).stream().distinct().collect(Collectors.toList());
        //完全一样不做处理

        List<String> upcCodeOriginOtherList = ObjectUtil.cloneByStream(upcCodeOriginList).stream().map(String::toUpperCase).collect(Collectors.toList());
        List<String> upcCodeModifyOtherList = ObjectUtil.cloneByStream(upcCodeModifyList).stream().map(String::toUpperCase).collect(Collectors.toList());

        if (upcCodeOriginOtherList.containsAll(upcCodeModifyOtherList) && upcCodeModifyOtherList.containsAll(upcCodeOriginOtherList)) {
            return;
        }
        //新增条码
        for (String upcCode : upcCodeModifyList) {
            if (upcCodeOriginList.contains(upcCode)) {
                continue;
            }
            //新增条码
            SkuUpcDTO skuUpcNewDTO = ObjectUtil.cloneByStream(skuUpcList.get(0));
            skuUpcNewDTO.setId(null);
            skuUpcNewDTO.setVersion(null);
            skuUpcNewDTO.setCreatedTime(null);
            skuUpcNewDTO.setCreatedBy(null);
            skuUpcNewDTO.setUpdatedBy(null);
            skuUpcNewDTO.setUpdatedTime(null);
            skuUpcNewDTO.setUpcCode(upcCode);
            skuUpcNewDTO.setIsDefault(SkuUpcDefaultEnum.NO.getStatus());
            commitUpcList.add(skuUpcNewDTO);
        }
        //移除条码
        for (String upcCode : upcCodeOriginList) {
            if (upcCodeModifyList.contains(upcCode)) {
                continue;
            }
            //移除条码
            removeUpcList.addAll(skuUpcList.stream().filter(a -> upcCode.equalsIgnoreCase(a.getUpcCode())).collect(Collectors.toList()));
        }
        //确认默认条码
        if (removeUpcList.stream().anyMatch(a -> defaultUpcCode.equalsIgnoreCase(a.getUpcCode()))) {
            commitUpcList.stream().findFirst().ifPresent(skuUpcDTO -> skuUpcDTO.setIsDefault(SkuUpcDefaultEnum.YES.getStatus()));
            //移除默认条码，还有剩余条码，需要将原来的其中一条改成默认条码
            if (CollectionUtils.isEmpty(commitUpcList)) {
                skuUpcList.stream().filter(a -> !defaultUpcCode.equalsIgnoreCase(a.getUpcCode())).findFirst().ifPresent(skuUpcDTO -> {
                    skuUpcDTO.setIsDefault(SkuUpcDefaultEnum.YES.getStatus());
                    commitUpcList.add(skuUpcDTO);
                });
            }
        }

    }

    /**
     * @param cargoCode
     * @param skuCode
     * @return com.dt.domain.bill.dto.tally.TallyDTO
     * <AUTHOR>
     * @describe: 校验商品是否有正在操作的理货报告
     * @date 2023/4/3 15:28
     */
    private Boolean checkTallySku(String cargoCode, String skuCode) {
        TallyDetailParam tallyDetailParam = new TallyDetailParam();
        tallyDetailParam.setCargoCode(cargoCode);
        tallyDetailParam.setSkuCode(skuCode);
        List<TallyDetailDTO> tallyDetailDTOList = remoteTallyDetailClient.getAppointMultipleParamList(tallyDetailParam, LambdaHelpUtils.convertToFieldNameList(TallyDetailDTO::getTallyCode));
        if (CollectionUtils.isEmpty(tallyDetailDTOList)) {
            return false;
        }
        TallyParam tallyParam = new TallyParam();
        tallyParam.setTallyCodeList(tallyDetailDTOList.stream().map(TallyDetailDTO::getTallyCode).collect(Collectors.toList()));
        List<TallyDTO> tallyDTOList = remoteTallyClient.getAppointMultipleParamList(tallyParam, LambdaHelpUtils.convertToFieldNameList(TallyDTO::getTallyCode, TallyDTO::getStatus));
        if (CollectionUtils.isEmpty(tallyDTOList)) {
            return false;
        }
        List<String> tallyStatus = Arrays.asList(TallyStatusEnum.CREATE.getCode(), TallyStatusEnum.FAIL_AUTH.getCode(), TallyStatusEnum.WAIT_AUTH.getCode());
        if (tallyDTOList.stream().anyMatch(a -> tallyStatus.contains(a.getStatus()))) {
            return true;
        }
        return false;
    }

    private SkuLogParam buildLog(SkuParam skuParam) {
        SkuLogParam skuLogParam = new SkuLogParam();
        skuLogParam.setCargoCode(skuParam.getCargoCode());
        skuLogParam.setSkuCode(skuParam.getCode());
        skuLogParam.setWarehouseCode(skuParam.getWarehouseCode());
        skuLogParam.setOpBy("System");
        skuLogParam.setOpDate(System.currentTimeMillis());
        skuLogParam.setOpContent("上游下发修改SKU:" + skuParam.getCode());
        skuLogParam.setOpRemark("原始报文:" + JSON.toJSONString(skuParam));
        return skuLogParam;
    }
}
