package com.dt.exchange.wms.integration.impl;

import com.dt.domain.bill.bo.DouChaoModifyAddressBO;
import com.dt.domain.bill.client.IShipmentOrderClient;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.domain.bill.dto.ShipmentOrderLogDTO;
import com.dt.domain.bill.param.NotifyParams;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.exchange.wms.integration.IRemoteShipmentOrderClient;
import com.dt.platform.wms.client.IShipmentOrderBizClient;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RemoteShipmentOrderClientImpl implements IRemoteShipmentOrderClient {
    @DubboReference
    private IShipmentOrderClient shipmentOrderClient;

    @DubboReference
    private IShipmentOrderBizClient shipmentOrderBizClient;

    @Override
    public Boolean saveOrUpdate(ShipmentOrderParam shipmentOrderParam) {

        return shipmentOrderClient.saveOrUpdate(shipmentOrderParam).getData();
    }


    @Override
    public Boolean modify(ShipmentOrderParam shipmentOrderParam) {
        return shipmentOrderClient.modify(shipmentOrderParam).getData();
    }

    @Override
    public ShipmentOrderDTO get(String cargoCode, String warehouseCode, String soNo) {
        ShipmentOrderParam param = new ShipmentOrderParam();
        param.setCargoCode(cargoCode);
        param.setWarehouseCode(warehouseCode);
        param.setSoNo(soNo);
        return shipmentOrderClient.get(param).getData();
    }

    @Override
    public ShipmentOrderDTO get(ShipmentOrderParam param) {
        return shipmentOrderClient.get(param).getData();
    }

    @Override
    public Boolean modifyReceiverInfo(ShipmentOrderParam shipmentOrderParam) {
        return shipmentOrderClient.modifyReceiverInfo(shipmentOrderParam).getData();
    }


    @Override
    public Boolean checkSoNoExistsByCargoCode(String cargoCode, String soNo) {
        ShipmentOrderParam param = new ShipmentOrderParam();
        param.setCargoCode(cargoCode);
        param.setSoNo(soNo);
        return shipmentOrderClient.checkSoNoExistsByCargoCode(param).getData();
    }

    @Override
    public List<String> checkPoNoExistsByCargoCode(String cargoCode, String poNo, List<String> validListStatus) {
        ShipmentOrderParam param = new ShipmentOrderParam();
        param.setCargoCode(cargoCode);
        param.setPoNo(poNo);
        param.setStatusList(validListStatus);
        return shipmentOrderClient.checkPoNoExistsByCargoCode(param).getData();
    }

    @Override
    public Boolean checkExpressExistsByCarrierCode(Long id, String carrierCode, String expressNo) {
        ShipmentOrderParam param = new ShipmentOrderParam();
        param.setCarrierCode(carrierCode);
        param.setExpressNo(expressNo);
        param.setId(id);
        return shipmentOrderClient.checkExpressExistsByCarrierCode(param).getData();
    }


    @Override
    public ShipmentOrderDTO findShipmentOrder(String shipmentOrderCode) {
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setShipmentOrderCode(shipmentOrderCode);
        return shipmentOrderClient.findShipmentOrder(shipmentOrderParam).getData();
    }

    @Override
    public Boolean modifyNotifyStatus(String shipmentOrderCode, List<String> packageCodeList, Long notifyTime, Integer notifyStatus) {
        return shipmentOrderClient.modifyNotifyStatus(shipmentOrderCode, packageCodeList, notifyTime, notifyStatus).getData();
    }

    @Override
    public Boolean modifyStatus(String shipmentOrderCode, Long updateTime, String status) {
        return shipmentOrderClient.modifyStatus(shipmentOrderCode, updateTime, status).getData();
    }

    @Override
    public Boolean saveShipmentLog(ShipmentOrderLogDTO shipmentOrderLogDTO) {
        return shipmentOrderClient.saveShipmentLog(shipmentOrderLogDTO).getData();
    }

    @Override
    public List<ShipmentOrderDetailDTO> getDetailList(ShipmentOrderDetailParam param) {
        return shipmentOrderClient.getDetailList(param).getData();
    }

    @Override
    public List<ShipmentOrderDTO> getNotifyShipmentOrderList(NotifyParams notifyParams) {
        return shipmentOrderClient.getNotifyShipmentOrderList(notifyParams).getData();
    }

    @Override
    public List<ShipmentOrderDTO> getList(ShipmentOrderParam param) {
        return shipmentOrderClient.getList(param).getData();
    }

    @Override
    public void maintainSomeFiledAfterReceipt(ShipmentOrderParam param) {
        shipmentOrderBizClient.maintainWeight(param.getWarehouseCode(), param.getCargoCode(), param.getShipmentOrderCode());
        shipmentOrderBizClient.maintainCarrierIfNeed(param.getWarehouseCode(), param.getCargoCode(), param.getShipmentOrderCode());
    }

    @Override
    public Boolean modifyByDTO(ShipmentOrderDTO shipmentOrderDTO) {
        return shipmentOrderClient.modify(shipmentOrderDTO).getData();
    }

    @Override
    public void commitDouChaoModifyAddress(DouChaoModifyAddressBO douChaoModifyAddressBO) {
         shipmentOrderClient.commitDouChaoModifyAddress(douChaoModifyAddressBO).getData();
    }
}















