package com.dt.exchange.wms.integration;

import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.DouChaoModifyAddressBO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.domain.bill.dto.ShipmentOrderLogDTO;
import com.dt.domain.bill.param.NotifyParams;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.domain.bill.param.ShipmentOrderParam;

import java.util.List;

public interface IRemoteShipmentOrderClient {

    Boolean saveOrUpdate(ShipmentOrderParam shipmentOrderParam);

    /**
     * 通过货主，上游单号,查询单据信息
     *
     * @param cargoCode
     * @param soNo
     * @return
     */
    Boolean checkSoNoExistsByCargoCode(String cargoCode, String soNo);

    List<String> checkPoNoExistsByCargoCode(String cargoCode, String poNo, List<String> validListStatus);

    Boolean checkExpressExistsByCarrierCode(Long id, String carrierCode, String expressNo);

    Boolean modify(ShipmentOrderParam shipmentOrderParam);

    ShipmentOrderDTO get(String cargoCode, String warehouseCode, String soNo);

    ShipmentOrderDTO get(ShipmentOrderParam param);

    Boolean modifyReceiverInfo(ShipmentOrderParam shipmentOrderParam);

    ShipmentOrderDTO findShipmentOrder(String shipmentOrderCode);

    /**
     * 修改通知状态
     *
     * @param shipmentOrderCode
     * @param notifyTime
     * @param notifyStatus
     * @return
     */
    Boolean modifyNotifyStatus(String shipmentOrderCode, List<String> packageCodeList, Long notifyTime, Integer notifyStatus);

    Boolean modifyStatus(String shipmentOrderCode, Long updateTime, String status);

    Boolean saveShipmentLog(ShipmentOrderLogDTO shipmentOrderLogDTO);

    List<ShipmentOrderDetailDTO> getDetailList(ShipmentOrderDetailParam param);

    List<ShipmentOrderDTO> getNotifyShipmentOrderList(NotifyParams notifyParams);
    /**
     * @author: WuXian
     * description:
     * create time: 2021/8/11 13:37
     *
     * @param param
     * @return java.util.List<com.dt.domain.bill.dto.ShipmentOrderDTO>
     */
    List<ShipmentOrderDTO> getList(ShipmentOrderParam param);

    /**
     * 出库单下发后维护部分属性
     * - 理论重量
     * - 承运商
     * @param param
     */
    void maintainSomeFiledAfterReceipt(ShipmentOrderParam param);

    Boolean modifyByDTO(ShipmentOrderDTO shipmentOrderDTO);

    /**
     * 抖超实时修改地址
     * @param douChaoModifyAddressBO
     */
    void commitDouChaoModifyAddress(DouChaoModifyAddressBO douChaoModifyAddressBO);
}
