package com.dt.exchange.wms.client;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.dto.WeChatMessageDTO;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.PackageMaterialDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.log.PackageMaterialLogDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.PackageMaterialParam;
import com.dt.exchange.wms.biz.ICargoConfigInitBiz;
import com.dt.exchange.wms.biz.IErpCargoOwnerBiz;
import com.dt.exchange.wms.biz.config.WarehouseConfig;
import com.dt.exchange.wms.biz.config.WarehouseOtherConfig;
import com.dt.exchange.wms.biz.impl.CheckWarehouseBiz;
import com.dt.exchange.wms.entity.ResultInfo;
import com.dt.exchange.wms.integration.IRemoteCargoOwnerClient;
import com.dt.exchange.wms.integration.IRemotePackageMaterialClient;
import com.dt.exchange.wms.integration.IRemoteSeqRuleClient;
import com.dt.exchange.wms.integration.IRemoteWarehouseClient;
import com.dt.exchange.wms.param.CargoOwnerReceiptBizParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/15 15:31
 */
@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class CargoOwnerReceiptBizClientImpl implements ICargoOwnerReceiptBizClient {

    @Resource
    IErpCargoOwnerBiz erpCargoOwnerBiz;

    @Resource
    CheckWarehouseBiz checkWarehouseClient;

    @Resource
    ICargoConfigInitBiz cargoConfigInitBiz;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Autowired
    private WarehouseConfig warehouseConfig;

    @Autowired
    WarehouseOtherConfig warehouseOtherConfig;

    @Autowired
    IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    private IRemotePackageMaterialClient remotePackageMaterialClient;


    @Override
    public Result<String> receipt(CargoOwnerReceiptBizParam param) {
        try {
            log.info("[op:CargoOwnerReceiptBizClient receipt] param = {}", JSON.toJSONString(param));
            // 设置dubbo上下文
            Result<String> checkResult = checkWarehouseClient.checkWareHouse(param.getWarehouseCode());
            if (!checkResult.checkSuccess()) {
                return checkResult;
            }
            if (StringUtils.isEmpty(param.getOutCode())) {
                return Result.fail(-1, "货主档案编码不能为空", null);
            }
            RLock lock = redissonClient.getLock("dt_wms_cargo_owner_receipt_lock:" + param.getWarehouseCode() + param.getOutCode());
            try {
                if (!lock.tryLock(1L, 10L, TimeUnit.SECONDS)) {
                    return Result.fail(-1, "接受失败", null);
                }
                ResultInfo resultInfo = erpCargoOwnerBiz.receipt(param);
                log.info("[op:CargoOwnerReceiptBizClient receipt] resultInfo = {}", JSON.toJSONString(resultInfo));
                if (resultInfo.isSuccess()) {
                    if (!Objects.equals(resultInfo.getObj(), "CARGO_EXISTS")) {
                        // 初始化货主参数
                        if (warehouseOtherConfig.isYCWarehouse(param.getWarehouseCode())) {
                            cargoConfigInitBiz.initCargoConfigParam(Arrays.asList(param.getOutCode()), param.getWarehouseCode(), true);
                        } else {
                            cargoConfigInitBiz.initCargoConfigParam(Arrays.asList(param.getOutCode()), param.getWarehouseCode(), false);
                        }
                        //货主新增下发通知
                        noticeAddCargo(param.getWarehouseCode(), param);
                        //淘天新增包耗材
                        if (warehouseOtherConfig.isTaoTian(param.getWarehouseCode())) {
                            addTaoTianMaterial(param.getOutCode());
                        }
                    }
                    return Result.success(resultInfo.getObj().toString());
                } else {
                    return Result.fail(-1, resultInfo.getMessage(), null);
                }
            } catch (Exception e) {
                log.error("[op:CargoOwnerReceiptBizClient receipt try {}catch(e)] e = {}", e);
                return Result.fail(-1, "导入异常1：" + e.getMessage(), null);
            } finally {
                if (lock.isLocked()) {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }
        } catch (
                Exception ex) {
            log.error("[op:CargoOwnerReceiptBizClient receipt try {}catch(ex)] ex = {}", ex);
            return Result.fail(-1, ex.getMessage(), null);
        }

    }

    /**
     * @param outCode
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2024/4/1 14:19
     */
    private void addTaoTianMaterial(String outCode) {
        //获取所有货主
        List<CargoOwnerDTO> allCargoOwner = remoteCargoOwnerClient.getAllCargoOwner(new CargoOwnerParam());
        if (CollectionUtils.isEmpty(allCargoOwner)) {
            return;
        }
        CargoOwnerDTO cargoOwnerDTO = allCargoOwner.stream().sorted(Comparator.comparing(CargoOwnerDTO::getId, Comparator.naturalOrder())).findFirst().orElse(null);
        if (cargoOwnerDTO == null) {
            return;
        }
        log.info("addTaoTianMaterial:{}", cargoOwnerDTO.getCode());
        PackageMaterialParam packageMaterialParam = new PackageMaterialParam();
        packageMaterialParam.setCargoCode(cargoOwnerDTO.getCode());
        List<PackageMaterialDTO> packageMaterialDTOList = remotePackageMaterialClient.getList(packageMaterialParam);
        if (CollectionUtils.isEmpty(packageMaterialDTOList)) {
            return;
        }
        //获取当前货主的包耗材
        //获取待提交数据的包耗材
        PackageMaterialParam packageMaterialWaitParam = new PackageMaterialParam();
        packageMaterialWaitParam.setCargoCode(outCode);
        List<PackageMaterialDTO> packageMaterialWaitList = remotePackageMaterialClient.getList(packageMaterialWaitParam);
        if (CollectionUtils.isEmpty(packageMaterialWaitList)) {
            packageMaterialWaitList = new ArrayList<>();
        }
        Map<String, PackageMaterialDTO> packageMaterialDTOMap = packageMaterialWaitList.stream().collect(Collectors.toMap(it -> StrUtil.join("#", it.getCargoCode(), it.getBarCode()), Function.identity()));
        if (CollectionUtils.isEmpty(packageMaterialDTOMap)) {
            packageMaterialDTOMap = new HashMap<>();
        }

        List<PackageMaterialLogDTO> packageMaterialLogDTOList = new ArrayList<>();
        Map<String, PackageMaterialDTO> finalPackageMaterialDTOMap = packageMaterialDTOMap;
        packageMaterialDTOList.forEach(it -> {
            if (!finalPackageMaterialDTOMap.containsKey(StrUtil.join("#", outCode, it.getBarCode()))) {
                it.setId(null);
                it.setCargoCode(outCode);
                it.setCode(remoteSeqRuleClient.findSequence(SeqEnum.PACK_MATERIAL_CODE_000001));

                //日志
                PackageMaterialLogDTO packageMaterialLogDTO = new PackageMaterialLogDTO();
                packageMaterialLogDTO.setCode(it.getCode());
                packageMaterialLogDTO.setOpContent("淘天下发货主,同步包耗材,来源货主编码:" + it.getCargoCode());
                packageMaterialLogDTO.setOpBy("System");
                packageMaterialLogDTO.setOpDate(System.currentTimeMillis());
                packageMaterialLogDTO.setType("");
                packageMaterialLogDTO.setOpRemark("");
                packageMaterialLogDTOList.add(packageMaterialLogDTO);
            }
        });
        if (!CollectionUtils.isEmpty(packageMaterialDTOList)) {
            remotePackageMaterialClient.saveBatch(packageMaterialDTOList);
            remotePackageMaterialClient.saveBatchLog(packageMaterialLogDTOList);
        }
    }

    /**
     * @param warehouseCode
     * @param param
     * @return void
     * @author: WuXian
     * description: 新增货主通知
     * create time: 2022/4/8 15:42
     */
    private void noticeAddCargo(String warehouseCode, CargoOwnerReceiptBizParam param) {
        try {
            List<String> addCargoWeChatWarnUrls = warehouseConfig.getAddCargoWeChatWarnUrls();
            WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(warehouseCode);
            WeChatMessageDTO messageDTO = new WeChatMessageDTO();
            WeChatMessageDTO.Content markdown = new WeChatMessageDTO.Content();

            markdown.setContent(String.format("**仓库编码:%s 仓库名称:%s **\n 新增货主如下: \n %s", CurrentRouteHolder.getWarehouseCode(), warehouseDTO.getName(), param.getName() + ":" + param.getOutCode()));
            messageDTO.setMarkdown(markdown);
            if (!CollectionUtils.isEmpty(addCargoWeChatWarnUrls)) {
                addCargoWeChatWarnUrls.forEach(it -> {
                    HttpUtil.post(it, JSONUtil.toJsonStr(messageDTO));
                });
            }
        } catch (Exception e) {
//            e.printStackTrace();
        }
    }
}
