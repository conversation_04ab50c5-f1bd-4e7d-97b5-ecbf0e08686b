package com.dt.domain.base.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danding.business.client.rpc.goods.center.facade.IGoodsAdminRpcFacade;
import com.danding.business.client.rpc.goods.center.facade.IGoodsManagementRpcReadFacade;
import com.danding.business.client.rpc.goods.center.facade.IGoodsManagementRpcWriteFacade;
import com.danding.business.client.rpc.goods.center.facade.IGoodsRpcWriteFacade;
import com.danding.business.client.rpc.goods.center.param.GoodsManagementRpcAddParam;
import com.danding.business.client.rpc.goods.center.param.GoodsManagementRpcUpdateParam;
import com.danding.business.client.rpc.goods.center.param.GoodsRpcQueryParam;
import com.danding.business.client.rpc.goods.center.param.WmsSku;
import com.danding.business.client.rpc.goods.center.result.GoodsManagementRpcResult;
import com.danding.business.common.ares.enums.goods.GoodsStatus;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.soul.client.common.result.RpcResult;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.Deleted;
import com.dt.component.common.enums.FromSourceEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.cargo.entity.CargoOwner;
import com.dt.domain.base.cargo.service.ICargoOwnerService;
import com.dt.domain.base.config.DefaultBaseConfig;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuUomDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.param.SkuBatchParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUomParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.base.sku.entity.Sku;
import com.dt.domain.base.sku.entity.SkuUom;
import com.dt.domain.base.sku.entity.SkuUpc;
import com.dt.domain.base.sku.service.ISkuLotService;
import com.dt.domain.base.sku.service.ISkuService;
import com.dt.domain.base.sku.service.ISkuUomService;
import com.dt.domain.base.sku.service.ISkuUpcService;
import com.dt.domain.base.util.SkuLotUtil;
import com.dt.domain.base.util.SkuUomUtil;
import com.dt.domain.base.util.SkuUpcUtil;
import com.dt.domain.base.util.SkuUtil;
import com.dt.domain.base.util.sku.SkuConvertCenterUtil;
import com.dt.platform.utils.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
@Slf4j
public class SkuClient implements ISkuClient {

    @Resource
    private ISkuService skuService;

    @Resource
    private ICargoOwnerService cargoOwnerService;

    @Resource
    private ISkuUpcService skuUpcService;

    @Resource
    private ISkuUomService skuUomService;

    @Resource
    private ISkuLotService skuLotService;

    @Resource
    private SkuUtil skuUtil;

    @Resource
    private DefaultBaseConfig defaultBaseConfig;

    @Resource
    private SkuUpcUtil skuUpcUtil;

    @Resource
    private SkuUomUtil skuUomUtil;

    @Resource
    private SkuLotUtil skuLotUtil;

    @DubboReference
    IGoodsManagementRpcReadFacade goodsManagementRpcReadFacade;

    @DubboReference
    IGoodsManagementRpcWriteFacade goodsManagementRpcWriteFacade;

    @DubboReference
    IGoodsRpcWriteFacade goodsRpcWriteFacade;

    @DubboReference
    IGoodsAdminRpcFacade goodsAdminRpcFacade;

    @Resource
    private SkuConvertCenterUtil skuConvertCenterUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> buildWMSAddSave(SkuParam param) {
        if (!StringUtils.isEmpty(param.getId()) && StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        GoodsManagementRpcResult goodsManagementRpcResult = goodsManagementRpcReadFacade.queryRpcGoodsBy(param.getCode(), param.getCargoCode(), CurrentRouteHolder.getWarehouseCode());
        SkuDTO _sku = skuConvertCenterUtil.goodsCenterQueryConvertSkuDTO(goodsManagementRpcResult);
        if (!ObjectUtils.isEmpty(_sku)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("货主:%s,商品代码:%s已存在", param.getCargoCode(), param.getCode()));
        }
        List<SkuUom> skuUomList = ConverterUtil.convertList(param.getSkuUomList(), SkuUom.class);
        List<SkuUpc> skuUpcList = ConverterUtil.convertList(param.getSkuUpcList(), SkuUpc.class);
        //更新uom
        if (!CollectionUtils.isEmpty(skuUomList)) {
            skuUomList.forEach(skuUom -> {
                boolean saveOrUpdate = skuUomService.saveOrUpdate(skuUom);
                if (!saveOrUpdate) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        //更新uom
        if (!CollectionUtils.isEmpty(skuUpcList)) {
            for (SkuUpc skuUpc : skuUpcList) {
                LambdaQueryWrapper<SkuUpc> skuUpcWrapper = new LambdaQueryWrapper<>();
                skuUpcWrapper.eq(SkuUpc::getUpcCode, skuUpc.getUpcCode());
                skuUpcWrapper.eq(SkuUpc::getCargoCode, (skuUpc == null) ? "" : skuUpc.getCargoCode());
                if (!CollectionUtils.isEmpty(skuUpcService.list(skuUpcWrapper))) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("商品条形码[%s]已存在", skuUpc.getUpcCode()));
                }
            }
            skuUpcList.forEach(skuUpc -> {
                boolean saveOrUpdate = skuUpcService.saveOrUpdate(skuUpc);
                if (!saveOrUpdate) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        GoodsManagementRpcAddParam goodsManagementRpcAddParam = skuConvertCenterUtil.addGoodsCenterSkuDTOConvertGoodsManagementRpcAddParam(ConverterUtil.convert(param, SkuDTO.class));
        if (Objects.equals(param.getFromSource(), FromSourceEnum.WMS.value())) {
            skuUpcList.stream()
                    .filter(a -> a.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus()))
                    .findFirst().ifPresent(it -> goodsManagementRpcAddParam.setBarcode(it.getUpcCode()));
        }
        //TODO 新增商品需要设置租户ID
        SimpleTenantHelper.setTenantId(param.getTenantIdByAddSku());
        log.info("buildWMSAddSave-erp-save:{} {}", JSONUtil.toJsonStr(goodsManagementRpcAddParam), param.getTenantIdByAddSku());
        Boolean rpcResult = goodsRpcWriteFacade.addGoods(goodsManagementRpcAddParam);
        if (!rpcResult) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    /**
     * @param warehouseCode
     * @return boolean
     * <AUTHOR>
     * @describe:
     * @date 2023/8/7 13:13
     */
    private boolean goodsCenterConfig(String warehouseCode) {
        if (StringUtils.isEmpty(warehouseCode)) {
            log.info("goodsCenterConfig-empty:{}", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        return true;
//        if (defaultBaseConfig.getGoodsCenter() != null && defaultBaseConfig.getGoodsCenter()) {
//            //全仓开启
//            if (defaultBaseConfig.getGoodsCenterAll() != null && defaultBaseConfig.getGoodsCenterAll()) {
//                return true;
//            }
//            //指定仓库开启
//            if (!CollectionUtils.isEmpty(defaultBaseConfig.getGoodsCenterWarehouseCodeList()) && defaultBaseConfig.getGoodsCenterWarehouseCodeList().contains(warehouseCode)) {
//                return true;
//            }
//        }
//        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> buildWMSOrERPSaveBatch(SkuBatchParam param) {
        List<SkuParam> paramList = param.getSkuList();
        if (CollectionUtils.isEmpty(paramList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        for (SkuParam skuParam : paramList) {
            SkuDTO sku = ConverterUtil.convert(skuParam, SkuDTO.class);
            if (sku.getId() == null && (!CollectionUtils.isEmpty(skuParam.getSkuUomList()))) {
                List<SkuUom> skuUomList = ConverterUtil.convertList(skuParam.getSkuUomList(), SkuUom.class);
                if (CollectionUtils.isEmpty(skuUomList)) {
                    throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
                }
                skuUomList.forEach(skuUom -> {
                    skuUomService.save(skuUom);
                });
            }
            if (sku.getId() == null && (!CollectionUtils.isEmpty(skuParam.getSkuUpcList()))) {
                List<SkuUpc> skuUpcList = ConverterUtil.convertList(skuParam.getSkuUpcList(), SkuUpc.class);
                if (CollectionUtils.isEmpty(skuUpcList)) {
                    throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
                }
                //多次导致默认条码重复，需要移除
                LambdaQueryWrapper<SkuUpc> skuUpcLambdaQueryWrapper = Wrappers.<SkuUpc>query().lambda()
                        .eq(SkuUpc::getSkuCode, sku.getCode())
                        .eq(SkuUpc::getCargoCode, sku.getCargoCode());
                List<SkuUpc> existsUpcList = skuUpcService.list(skuUpcLambdaQueryWrapper);
                if (!CollectionUtils.isEmpty(existsUpcList)) {
                    SkuUpc upcExists = existsUpcList.stream().filter(a -> a.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus())).findFirst().orElse(null);
                    SkuUpc upNew = skuUpcList.stream().filter(a -> a.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus())).findFirst().orElse(null);
                    if (upcExists != null && upNew != null && !Objects.equals(upcExists.getUpcCode(), upNew.getUpcCode())) {
                        upcExists.setUpcCode(upcExists.getUpcCode() + "###" + System.currentTimeMillis());
                        boolean updateById = skuUpcService.updateById(upcExists);
                        if (!updateById) {
                            throw new BaseException(BaseBizEnum.TIP, "移除条码失败");
                        }
                        skuUpcService.removeById(upcExists.getId());
                    }
                }
                skuUpcList.forEach(skuUpc -> {
                    skuUpcService.save(skuUpc);
                });
            }
            sku.setWarehouseCode(warehouseCode);
            //wms自建新增
            if (sku.getFromSource().equalsIgnoreCase(FromSourceEnum.WMS.value())) {
                GoodsManagementRpcAddParam goodsManagementRpcAddParam = skuConvertCenterUtil.addGoodsCenterSkuDTOConvertGoodsManagementRpcAddParam(sku);
                if (Objects.equals(skuParam.getFromSource(), FromSourceEnum.WMS.value())) {
                    skuParam.getSkuUpcList().stream()
                            .filter(a -> a.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus()))
                            .findFirst().ifPresent(it -> goodsManagementRpcAddParam.setBarcode(it.getUpcCode()));
                }
                log.info("buildWMSOrERPSaveBatch-erp-save:{}", JSONUtil.toJsonStr(goodsManagementRpcAddParam));
                Boolean rpcResult = goodsRpcWriteFacade.addGoods(goodsManagementRpcAddParam);
                if (!rpcResult) {
                    throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
                }
            } else {
                GoodsRpcQueryParam goodsRpcQueryParam = new GoodsRpcQueryParam();
                goodsRpcQueryParam.setStatus(GoodsStatus.FORBIDDEN);
                goodsRpcQueryParam.setWarehouseCode(skuParam.getWarehouseCode());
                goodsRpcQueryParam.setOwnerCode(skuParam.getCargoCode());
                goodsRpcQueryParam.setSku(skuParam.getCode());
                List<GoodsManagementRpcResult> goodsManagementRpcResultList = goodsManagementRpcReadFacade.ListRpcGoodsByParam(goodsRpcQueryParam);
                if (!CollectionUtils.isEmpty(goodsManagementRpcResultList)) {
                    skuParam.setVersion(goodsManagementRpcResultList.get(0).getVersion().intValue());
                }
                //ERP下发 修改属性
                GoodsManagementRpcUpdateParam goodsManagementRpcUpdateParam = skuConvertCenterUtil.modifyGoodsCenterSkuDTOConvertGoodsManagementRpcUpdateParam(ConverterUtil.convert(skuParam, SkuDTO.class));
                log.info("buildWMSOrERPSaveBatch-erp-modify:{}", JSONUtil.toJsonStr(goodsManagementRpcUpdateParam));
                RpcResult rpcResult = goodsManagementRpcWriteFacade.editGoods(goodsManagementRpcUpdateParam);
                if (rpcResult.getCode() != 200) {
                    throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
                }
            }
        }
        return Result.success(true);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatchSku(SkuBatchParam param) {
        List<SkuParam> paramList = param.getSkuList();
        if (CollectionUtils.isEmpty(paramList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        for (SkuParam skuParam : paramList) {
            SkuDTO skuDTO = ConverterUtil.convert(skuParam, SkuDTO.class);
            GoodsManagementRpcResult goodsManagementRpcResult = goodsManagementRpcReadFacade.queryRpcGoodsBy(skuParam.getCode(), skuParam.getCargoCode(), CurrentRouteHolder.getWarehouseCode());
            SkuDTO originSkuDTO = skuConvertCenterUtil.goodsCenterQueryConvertSkuDTO(goodsManagementRpcResult);
            if (!ObjectUtils.isEmpty(originSkuDTO)) {
                skuDTO.setName(skuDTO.getName() == null ? originSkuDTO.getName() : skuDTO.getName());
                skuDTO.setRejectCycle(skuDTO.getRejectCycle() == null ? originSkuDTO.getRejectCycle() : skuDTO.getRejectCycle());
                skuDTO.setGrossWeight(skuDTO.getGrossWeight() == null ? originSkuDTO.getGrossWeight() : skuDTO.getGrossWeight());
                skuDTO.setCartonWeight(skuDTO.getCartonWeight() == null ? originSkuDTO.getCartonWeight() : skuDTO.getCartonWeight());
                skuDTO.setNetWeight(skuDTO.getNetWeight() == null ? originSkuDTO.getNetWeight() : skuDTO.getNetWeight());
                skuDTO.setSkuWrap(skuDTO.getSkuWrap() == null ? originSkuDTO.getSkuWrap() : skuDTO.getSkuWrap());
                skuDTO.setWrapQty(skuDTO.getWrapQty() == null ? originSkuDTO.getWrapQty() : skuDTO.getWrapQty());
                skuDTO.setLotRuleCode(skuDTO.getLotRuleCode() == null ? originSkuDTO.getLotRuleCode() : skuDTO.getLotRuleCode());
                skuDTO.setAllocationRuleCode(skuDTO.getAllocationRuleCode() == null ? originSkuDTO.getAllocationRuleCode() : skuDTO.getAllocationRuleCode());
                skuDTO.setTurnoverRuleCode(skuDTO.getTurnoverRuleCode() == null ? originSkuDTO.getTurnoverRuleCode() : skuDTO.getTurnoverRuleCode());
                skuDTO.setLength(skuDTO.getLength() == null ? originSkuDTO.getLength() : skuDTO.getLength());
                skuDTO.setWidth(skuDTO.getWidth() == null ? originSkuDTO.getWidth() : skuDTO.getWidth());
                skuDTO.setHeight(skuDTO.getHeight() == null ? originSkuDTO.getHeight() : skuDTO.getHeight());
                skuDTO.setVolume(skuDTO.getVolume() == null ? originSkuDTO.getVolume() : skuDTO.getVolume());
                skuDTO.setStandardLength(skuDTO.getStandardLength() == null ? originSkuDTO.getStandardLength() : skuDTO.getStandardLength());
                skuDTO.setStandardWidth(skuDTO.getStandardWidth() == null ? originSkuDTO.getStandardWidth() : skuDTO.getStandardWidth());
                skuDTO.setStandardHeight(skuDTO.getStandardHeight() == null ? originSkuDTO.getStandardHeight() : skuDTO.getStandardHeight());
                skuDTO.setStandardVolume(skuDTO.getStandardVolume() == null ? originSkuDTO.getStandardVolume() : skuDTO.getStandardVolume());
                skuDTO.setBracketGauge(skuDTO.getBracketGauge() == null ? originSkuDTO.getBracketGauge() : skuDTO.getBracketGauge());
                skuDTO.setBrandCode(skuDTO.getBrandCode() == null ? originSkuDTO.getBrandCode() : skuDTO.getBrandCode());
                skuDTO.setStyle(skuDTO.getStyle() == null ? originSkuDTO.getStyle() : skuDTO.getStyle());
                skuDTO.setColour(skuDTO.getColour() == null ? originSkuDTO.getColour() : skuDTO.getColour());
                skuDTO.setSkuSize(skuDTO.getSkuSize() == null ? originSkuDTO.getSkuSize() : skuDTO.getSkuSize());
                skuDTO.setYear(skuDTO.getYear() == null ? originSkuDTO.getYear() : skuDTO.getYear());
            }
            GoodsManagementRpcUpdateParam goodsManagementRpcUpdateParam = skuConvertCenterUtil.modifyGoodsCenterSkuDTOConvertGoodsManagementRpcUpdateParam(skuDTO);
            log.info("modifyBatchSku-erp:{}", JSONUtil.toJsonStr(goodsManagementRpcUpdateParam));
            RpcResult rpcResult = goodsManagementRpcWriteFacade.editGoods(goodsManagementRpcUpdateParam);
            if (rpcResult.getCode() != 200) {
                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
            //更新修改默认条码
            List<SkuUpcParam> skuUpcParamList = skuParam.getSkuUpcList();
            if (!CollectionUtils.isEmpty(skuUpcParamList)) {
                for (SkuUpcParam skuUpcParam : skuUpcParamList) {
                    if (skuUpcParam.getId() != null) {
                        LambdaQueryWrapper<SkuUpc> upcQueryWrapper = new LambdaQueryWrapper<>();
                        upcQueryWrapper.eq(SkuUpc::getId, skuUpcParam.getId());
                        SkuUpc skuUpc = skuUpcService.getOne(upcQueryWrapper);
                        if (skuUpc != null) {
                            skuUpc.setIsDefault(skuUpcParam.getIsDefault() == null ? skuUpc.getIsDefault() : skuUpcParam.getIsDefault());
                            skuUpc.setUpcCode(skuUpcParam.getUpcCode() == null ? skuUpc.getUpcCode() : skuUpcParam.getUpcCode());
                            Boolean result = skuUpcService.update(skuUpc, upcQueryWrapper);
                            if (!result) {
                                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
                            }
                        }
                    }
                }
            }
        }

        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkMoreSkuUpc(SkuParam skuParam) {
        if (!CollectionUtils.isEmpty(skuParam.getSkuUpcList())) {
            for (SkuUpcParam skuUpcParam : skuParam.getSkuUpcList()) {
                SkuUpc skuUpc = ConverterUtil.convert(skuUpcParam, SkuUpc.class);
                LambdaQueryWrapper<SkuUpc> skuUpcWrapper = new LambdaQueryWrapper<>();
                skuUpcWrapper.eq(SkuUpc::getUpcCode, skuUpc.getUpcCode());
                skuUpcWrapper.eq(SkuUpc::getCargoCode, (skuUpc == null) ? "" : skuUpc.getCargoCode());
                List<SkuUpc> skuUpcListTemp = skuUpcService.list(skuUpcWrapper);
                if (skuUpcListTemp == null) {
                    skuUpcListTemp = new ArrayList<>();
                }
                skuUpcListTemp = skuUpcListTemp.stream().filter(s -> !s.getId().equals(skuUpc.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(skuUpcListTemp)) {
                    return Result.success(false);
                }
            }
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(SkuParam param) {
        if (StringUtils.isEmpty(param.getId()) &&
                StringUtils.isEmpty(param.getCode()) &&
                CollectionUtils.isEmpty(param.getIdList()) &&
                CollectionUtils.isEmpty(param.getCodeList())
        ) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        GoodsManagementRpcResult goodsManagementRpcResult = goodsManagementRpcReadFacade.queryRpcGoodsBy(param.getCode(), param.getCargoCode(), CurrentRouteHolder.getWarehouseCode());
        SkuDTO sku = skuConvertCenterUtil.goodsCenterQueryConvertSkuDTO(goodsManagementRpcResult);
        if (ObjectUtils.isEmpty(sku)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<SkuUom> skuUomList = ConverterUtil.convertList(param.getSkuUomList(), SkuUom.class);
        List<SkuUpc> skuUpcList = ConverterUtil.convertList(param.getSkuUpcList(), SkuUpc.class);
        //更新uom
        if (!CollectionUtils.isEmpty(skuUomList)) {
            skuUomList.forEach(entity -> {
                boolean saveOrUpdate = skuUomService.saveOrUpdate(entity);
                if (!saveOrUpdate) {
                    throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                }
            });
        }
        //更新uom
        if (!CollectionUtils.isEmpty(skuUpcList)) {
            List<SkuUpc> newSkuUpcList = skuUpcList.stream().filter(s -> s.getId() == null).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(newSkuUpcList)) {
                for (SkuUpc skuUpc : newSkuUpcList) {
                    LambdaQueryWrapper<SkuUpc> skuUpcWrapper = new LambdaQueryWrapper<>();
                    skuUpcWrapper.eq(SkuUpc::getUpcCode, skuUpc.getUpcCode());
                    skuUpcWrapper.eq(SkuUpc::getCargoCode, (skuUpc == null) ? "" : skuUpc.getCargoCode());
                    if (!CollectionUtils.isEmpty(skuUpcService.list(skuUpcWrapper))) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("商品条形码[%s]已存在", skuUpc.getUpcCode()));
                    }
                }
            }
            List<SkuUpc> editSkuUpcList = skuUpcList.stream().filter(s -> s.getId() != null && s.getId() > 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(editSkuUpcList)) {
                for (SkuUpc skuUpc : editSkuUpcList) {
                    LambdaQueryWrapper<SkuUpc> skuUpcWrapper = new LambdaQueryWrapper<>();
                    skuUpcWrapper.eq(SkuUpc::getUpcCode, skuUpc.getUpcCode());
                    skuUpcWrapper.eq(SkuUpc::getCargoCode, (skuUpc == null) ? "" : skuUpc.getCargoCode());
                    List<SkuUpc> skuUpcListTemp = skuUpcService.list(skuUpcWrapper);
                    if (skuUpcListTemp == null) {
                        skuUpcListTemp = new ArrayList<>();
                    }
                    skuUpcListTemp = skuUpcListTemp.stream().filter(upc -> !upc.getId().equals(skuUpc.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(skuUpcListTemp)) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("商品条形码[%s]已存在", skuUpc.getUpcCode()));
                    }
                }
            }
            if (!CollectionUtil.isEmpty(skuUpcList)) {
                skuUpcList.forEach(skuUpc -> {
                    boolean saveOrUpdate = skuUpcService.saveOrUpdate(skuUpc);
                    if (!saveOrUpdate) {
                        throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
                    }
                });
            }
        }
        if (!CollectionUtils.isEmpty(skuUomList)) {
            List<Long> delIdList = skuUomList.stream()
                    .filter(a -> Deleted.DELETE.getCode().equals(a.getDeleted()))
                    .flatMap(a -> Stream.of(a.getId()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delIdList)) {
                skuUomService.removeByIds(delIdList);
            }
        }
        //删除upc
        if (!CollectionUtils.isEmpty(skuUpcList)) {
            List<Long> delIdList = skuUpcList.stream()
                    .filter(a -> Deleted.DELETE.getCode().equals(a.getDeleted()))
                    .flatMap(a -> Stream.of(a.getId()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delIdList)) {
                skuUpcService.removeByIds(delIdList);
            }
        }
        GoodsManagementRpcUpdateParam goodsManagementRpcUpdateParam = skuConvertCenterUtil.modifyGoodsCenterSkuDTOConvertGoodsManagementRpcUpdateParam(ConverterUtil.convert(param, SkuDTO.class));
        log.info("modify-erp:{}", JSONUtil.toJsonStr(goodsManagementRpcUpdateParam));
        RpcResult rpcResult = goodsManagementRpcWriteFacade.editGoods(goodsManagementRpcUpdateParam);
        if (rpcResult.getCode() != 200) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }


        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result modifyBatch(SkuBatchParam param) {
        List<SkuParam> paramList = param.getSkuList();
        if (CollectionUtils.isEmpty(paramList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        for (SkuParam skuParam : paramList) {
            if (StringUtils.isEmpty(skuParam.getCode()) &&
                    StringUtils.isEmpty(skuParam.getWarehouseCode()) &&
                    StringUtils.isEmpty(skuParam.getCargoCode())
            ) {
                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
            GoodsManagementRpcResult goodsManagementRpcResult = goodsManagementRpcReadFacade.queryRpcGoodsBy(skuParam.getCode(), skuParam.getCargoCode(), CurrentRouteHolder.getWarehouseCode());
            SkuDTO sku = skuConvertCenterUtil.goodsCenterQueryConvertSkuDTO(goodsManagementRpcResult);
            if (ObjectUtils.isEmpty(sku)) {
                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
            if (!ObjectUtils.isEmpty(skuParam.getIsLifeMgt()) && new Integer(1).equals(skuParam.getIsLifeMgt())) {
                if (ObjectUtils.isEmpty(skuParam.getLifeCycle())) {
                    if (ObjectUtils.isEmpty(sku.getLifeCycle()) || sku.getLifeCycle() <= 0) {
                        return Result.fail(null, "保质期天数必须大于0", false);
                    }
                }
                if (ObjectUtils.isEmpty(skuParam.getRejectCycle())) {
                    if (ObjectUtils.isEmpty(sku.getRejectCycle()) || sku.getRejectCycle() <= 0) {
                        return Result.fail(null, "禁收时限天数必须大于0", false);
                    }
                }
                if (ObjectUtils.isEmpty(skuParam.getWithdrawCycle())) {
                    if (ObjectUtils.isEmpty(sku.getWithdrawCycle()) || sku.getWithdrawCycle() <= 0) {
                        return Result.fail(null, "禁售时限天数必须大于0", false);
                    }
                }
                if (ObjectUtils.isEmpty(skuParam.getWarnCycle())) {
                    if (ObjectUtils.isEmpty(sku.getWarnCycle()) || sku.getWarnCycle() <= 0) {
                        return Result.fail(null, "预警天数必须大于0", false);
                    }
                }
            }
            //防止更新表内不可修改字段，故做一层可修改字段的新DO封装
            //上游传了就更新，不传还是库内原值
            SkuDTO skuParamForModify = new SkuDTO();
            skuParamForModify.setBrandCode(skuParam.getBrandCode() == null ? sku.getBrandCode() : skuParam.getBrandCode());
            skuParamForModify.setBrandName(skuParam.getBrandName() == null ? sku.getBrandName() : skuParam.getBrandName());
            skuParamForModify.setStyle(skuParam.getStyle() == null ? sku.getStyle() : skuParam.getStyle());
            skuParamForModify.setColour(skuParam.getColour() == null ? sku.getColour() : skuParam.getColour());
            skuParamForModify.setColourCode(skuParam.getColourCode() == null ? sku.getColourCode() : skuParam.getColourCode());
            skuParamForModify.setSkuSize(skuParam.getSkuSize() == null ? sku.getSkuSize() : skuParam.getSkuSize());
            skuParamForModify.setSizeCode(skuParam.getSizeCode() == null ? sku.getSizeCode() : skuParam.getSizeCode());
            skuParamForModify.setYear(skuParam.getYear() == null ? sku.getYear() : skuParam.getYear());
            skuParamForModify.setSeason(skuParam.getSeason() == null ? sku.getSeason() : skuParam.getSeason());
            skuParamForModify.setPrice(skuParam.getPrice() == null ? sku.getPrice() : skuParam.getPrice());
            skuParamForModify.setLength(skuParam.getLength() == null ? sku.getLength() : skuParam.getLength());
            skuParamForModify.setWidth(skuParam.getWidth() == null ? sku.getWidth() : skuParam.getWidth());
            skuParamForModify.setHeight(skuParam.getHeight() == null ? sku.getHeight() : skuParam.getHeight());
            skuParamForModify.setVolume(skuParam.getVolume() == null ? sku.getVolume() : skuParam.getVolume());
            skuParamForModify.setGrossWeight(skuParam.getGrossWeight() == null ? sku.getGrossWeight() : skuParam.getGrossWeight());
            skuParamForModify.setIsLifeMgt(skuParam.getIsLifeMgt() == null ? sku.getIsLifeMgt() : skuParam.getIsLifeMgt());
            skuParamForModify.setNetWeight(skuParam.getNetWeight() == null ? sku.getNetWeight() : skuParam.getNetWeight());
            skuParamForModify.setLifeCycle(skuParam.getLifeCycle() == null ? sku.getLifeCycle() : skuParam.getLifeCycle());
            skuParamForModify.setRejectCycle(skuParam.getRejectCycle() == null ? sku.getRejectCycle() : skuParam.getRejectCycle());
            skuParamForModify.setWithdrawCycle(skuParam.getWithdrawCycle() == null ? sku.getWithdrawCycle() : skuParam.getWithdrawCycle());
            skuParamForModify.setWarnCycle(skuParam.getWarnCycle() == null ? sku.getWarnCycle() : skuParam.getWarnCycle());
            skuParamForModify.setItemCode(skuParam.getItemCode() == null ? sku.getItemCode() : skuParam.getItemCode());
            /**
             * 上游修改不修改是否新旧标记
             * @王响平
             */
            skuParamForModify.setIsNewRecord(sku.getIsNewRecord());
            skuParamForModify.setWarehouseCode(sku.getWarehouseCode());
            skuParamForModify.setCode(sku.getCode());
            skuParamForModify.setCargoCode(sku.getCargoCode());
            GoodsManagementRpcUpdateParam goodsManagementRpcUpdateParam = skuConvertCenterUtil.modifyGoodsCenterSkuDTOConvertGoodsManagementRpcUpdateParam(skuParamForModify);
            log.info("modifyBatch-erp:{}", JSONUtil.toJsonStr(goodsManagementRpcUpdateParam));
            //TODO 由于商品中心化 ERP下发修改仅仅修改商品条码 2024-09-27
            if (skuParam.getOnlyModifyUpcCode() == null || !skuParam.getOnlyModifyUpcCode()) {
                RpcResult rpcResult = goodsManagementRpcWriteFacade.editGoods(goodsManagementRpcUpdateParam);
                if (rpcResult.getCode() != 200) {
                    log.error("goodsManagementRpcWriteFacade.editGoods fail:{}", warehouseCode + "," + skuParam.getCargoCode() + "," + skuParam.getCode());
                    throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
                }
            } else {
                log.info("onlyModifyUpcCode:{}", warehouseCode + "," + skuParam.getCargoCode() + "," + skuParam.getCode());
            }
            //淘天修改条码
            if (!CollectionUtils.isEmpty(skuParam.getSkuUpcModifyTaoTianDTOList())) {
                skuParam.getSkuUpcModifyTaoTianDTOList().forEach(skuUpcDO -> {
                    boolean saveOrUpdate = skuUpcService.saveOrUpdate(ConverterUtil.convert(skuUpcDO, SkuUpc.class));
                    if (!saveOrUpdate) {
                        throw new BaseException(BaseBizEnum.TIP, "商品条码更新异常" + skuUpcDO.getSkuCode() + "," + skuUpcDO.getUpcCode());
                    }
                });
            }
            //淘天移除条码 先改 后删除
            if (!CollectionUtils.isEmpty(skuParam.getSkuUpcRemoveTaoTianDTOList())) {
                List<Long> removeIdList = skuParam.getSkuUpcRemoveTaoTianDTOList().stream().map(SkuUpcDTO::getId).collect(Collectors.toList());
                skuParam.getSkuUpcRemoveTaoTianDTOList().forEach(skuUpcDO -> {
                    skuUpcDO.setUpcCode(RandomUtil.randomNumbers(8) + "###" + skuUpcDO.getUpcCode());
                    boolean saveOrUpdate = skuUpcService.saveOrUpdate(ConverterUtil.convert(skuUpcDO, SkuUpc.class));
                    if (!saveOrUpdate) {
                        throw new BaseException(BaseBizEnum.TIP, "商品条码更新异常" + skuUpcDO.getSkuCode() + "," + skuUpcDO.getUpcCode());
                    }
                });
                removeIdList.forEach(skuUpcId -> {
                    boolean removeById = skuUpcService.removeById(skuUpcId);
                    if (!removeById) {
                        throw new BaseException(BaseBizEnum.TIP, "商品条码移除异常:" + skuUpcId);
                    }
                });
            }
        }

        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(SkuParam param) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        if (goodsCenterConfig(warehouseCode)) {
            GoodsRpcQueryParam goodsRpcQueryParam = skuConvertCenterUtil.goodsCenterQueryConvertSkuParam(param);
            List<GoodsManagementRpcResult> goodsManagementRpcResultList = goodsManagementRpcReadFacade.ListRpcGoodsByParam(goodsRpcQueryParam);
            List<SkuDTO> skuDTOList = skuConvertCenterUtil.goodsCenterQueryConvertSkuDTOList(goodsManagementRpcResultList);
            return Result.success(skuDTOList.size() != 0);
        } else {
            LambdaQueryWrapper<Sku> wrapper = skuUtil.getQueryWrapper(param);
            Integer count = skuService.count(wrapper);
            return Result.success(count != 0);
        }
    }

    @Override
    public Result<SkuDTO> get(SkuParam param) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        //商品中心化
        GoodsManagementRpcResult goodsManagementRpcResult = goodsManagementRpcReadFacade.queryRpcGoodsBy(param.getCode(), param.getCargoCode(), CurrentRouteHolder.getWarehouseCode());
        if (!System.getenv("SPRING_PROFILES_ACTIVE").equalsIgnoreCase("prod")) {
            log.info("goodsManagementRpcResult-get:{}", JSONUtil.toJsonStr(goodsManagementRpcResult));
        }
        SkuDTO skuDTO = skuConvertCenterUtil.goodsCenterQueryConvertSkuDTO(goodsManagementRpcResult);
        if (skuDTO != null) {
            LambdaQueryWrapper<SkuUpc> upcQueryWrapper = new LambdaQueryWrapper<SkuUpc>();
            upcQueryWrapper.eq(SkuUpc::getSkuCode, skuDTO.getCode());
            upcQueryWrapper.eq(SkuUpc::getCargoCode, skuDTO.getCargoCode());
            List<SkuUpc> list = skuUpcService.list(upcQueryWrapper);
            if (!CollectionUtils.isEmpty(list)) {
                List<SkuUpcDTO> dtoList = ConverterUtil.convertList(list, SkuUpcDTO.class);
                skuDTO.setSkuUpcList(dtoList);
            }
        }
        return Result.success(skuDTO);

    }


    @Override
    public Result<List<SkuDTO>> getList(SkuParam param) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();

        GoodsRpcQueryParam goodsRpcQueryParam = skuConvertCenterUtil.goodsCenterQueryConvertSkuParam(param);
        List<GoodsManagementRpcResult> goodsManagementRpcResultList = goodsManagementRpcReadFacade.ListRpcGoodsByParam(goodsRpcQueryParam);
        return Result.success(skuConvertCenterUtil.goodsCenterQueryConvertSkuDTOList(goodsManagementRpcResultList));

    }

    @Override
    public Result<Page<SkuDTO>> getPage(SkuParam param) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();

        GoodsRpcQueryParam goodsRpcQueryParam = skuConvertCenterUtil.goodsCenterQueryConvertSkuParam(param);
        goodsRpcQueryParam.setCurrentPage(param.getCurrentPage());
        goodsRpcQueryParam.setPageSize(param.getPageSize());
        ListVO<GoodsManagementRpcResult> pageRpcListGoodsByParam = goodsManagementRpcReadFacade.pageRpcListGoodsByParam(goodsRpcQueryParam);
        if (!System.getenv("SPRING_PROFILES_ACTIVE").equalsIgnoreCase("prod")) {
            log.info("goodsManagementRpcResult-getPage:{}", JSONUtil.toJsonStr(pageRpcListGoodsByParam));
        }
        Page<SkuDTO> result = new Page<>();
        result.setSize(pageRpcListGoodsByParam.getPage().getPageSize());
        result.setCurrent(pageRpcListGoodsByParam.getPage().getCurrentPage());
        result.setPages(pageRpcListGoodsByParam.getPage().getTotalPage());
        result.setTotal(pageRpcListGoodsByParam.getPage().getTotalCount());
        List<SkuDTO> vList = skuConvertCenterUtil.goodsCenterQueryConvertSkuDTOList(pageRpcListGoodsByParam.getDataList());
        result.setRecords(vList);
        return Result.success(result);

    }

    @Override
    public Result<List<SkuUpcDTO>> querySkuUpcBySkuCode(SkuUpcParam param) {
        LambdaQueryWrapper<SkuUpc> upcQueryWrapper = skuUpcUtil.getQueryWrapper(param);
        List<SkuUpc> upcList = skuUpcService.list(upcQueryWrapper);
        return Result.success(ConverterUtil.convertList(upcList, SkuUpcDTO.class));
    }

    @Override
    public Result<SkuUomDTO> querySkuUomBySkuCode(SkuUomParam param) {
        LambdaQueryWrapper<SkuUom> uomQueryWrapper = skuUomUtil.getQueryWrapper(param);
        SkuUom uom = skuUomService.getOne(uomQueryWrapper);
        return Result.success(ConverterUtil.convert(uom, SkuUomDTO.class));
    }

    @Override
    public Result<SkuUpcDTO> getSkuUpc(SkuUpcParam param) {
        LambdaQueryWrapper<SkuUpc> upcQueryWrapper = skuUpcUtil.getQueryWrapper(param);
        SkuUpc upc = skuUpcService.getOne(upcQueryWrapper);
        return Result.success(ConverterUtil.convert(upc, SkuUpcDTO.class));
    }

    @Override
    public Result<List<SkuUpcDTO>> getSkuUpcList(SkuUpcParam param) {
        LambdaQueryWrapper<SkuUpc> upcQueryWrapper = skuUpcUtil.getQueryWrapper(param);
        List<SkuUpc> upcList = skuUpcService.list(upcQueryWrapper);
        return Result.success(ConverterUtil.convertList(upcList, SkuUpcDTO.class));
    }

    @Override
    public Result<List<SkuUomDTO>> getSkuUomList(SkuUomParam param) {
        LambdaQueryWrapper<SkuUom> uomQueryWrapper = skuUomUtil.getQueryWrapper(param);
        List<SkuUom> uomList = skuUomService.list(uomQueryWrapper);
        return Result.success(ConverterUtil.convertList(uomList, SkuUomDTO.class));
    }

    @Override
    public Result<List<SkuDTO>> getSkuListToAppointColumn(SkuParam skuParam, List<String> filedList) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();

        skuParam.setFiledList(filedList);
        GoodsRpcQueryParam goodsRpcQueryParam = skuConvertCenterUtil.goodsCenterQueryConvertSkuParam(skuParam);
        List<GoodsManagementRpcResult> goodsManagementRpcResultList = goodsManagementRpcReadFacade.ListRpcGoodsByParam(goodsRpcQueryParam);
        return Result.success(skuConvertCenterUtil.goodsCenterQueryConvertSkuDTOList(goodsManagementRpcResultList));

    }

    @Override
    public Result<List<SkuUpcDTO>> getSkuUpcListToAppointColumn(SkuUpcParam skuUpcParam, List<String> filedList) {
        LambdaQueryWrapper<SkuUpc> queryWrapper = skuUpcUtil.getQueryWrapper(skuUpcParam);
        if (!CollectionUtils.isEmpty(filedList)) {
            queryWrapper.select(SkuUpc.class, i -> filedList.contains(i.getColumn()));
        }
        List<SkuUpc> upcList = skuUpcService.list(queryWrapper);
        return Result.success(ConverterUtil.convertList(upcList, SkuUpcDTO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(SkuDTO skuDTO) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();

        GoodsManagementRpcUpdateParam goodsManagementRpcUpdateParam = skuConvertCenterUtil.modifyGoodsCenterSkuDTOConvertGoodsManagementRpcUpdateParam(skuDTO);
        log.info("modify-erp:{}", JSONUtil.toJsonStr(goodsManagementRpcUpdateParam));
        RpcResult rpcResult = goodsManagementRpcWriteFacade.editGoods(goodsManagementRpcUpdateParam);
        if (rpcResult.getCode() != 200) {
            throw new BaseException(BaseBizEnum.TIP, "更新异常");
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> dataSyncWms(SkuParam skuParam) {
        log.info("dataSyncWms:{}", JSONUtil.toJsonStr(skuParam));
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        //仓未中心化,不允许同步
//        if (!goodsCenterConfig(warehouseCode)) {
//            return Result.success(false);
//        }
        LambdaQueryWrapper<Sku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(!StringUtils.isEmpty(skuParam.getWarehouseCode()), Sku::getWarehouseCode, skuParam.getWarehouseCode());
        queryWrapper.eq(!StringUtils.isEmpty(skuParam.getCargoCode()), Sku::getCargoCode, skuParam.getCargoCode());
        queryWrapper.in(!CollectionUtils.isEmpty(skuParam.getCargoCodeList()), Sku::getCargoCode, skuParam.getCargoCodeList());
        queryWrapper.eq(!StringUtils.isEmpty(skuParam.getCode()), Sku::getCode, skuParam.getCode());
        queryWrapper.in(!CollectionUtils.isEmpty(skuParam.getCodeList()), Sku::getCode, skuParam.getCodeList());
        queryWrapper.apply(" sku_sync = '' ");
        List<Sku> skuList = skuService.list(queryWrapper);
        if (CollectionUtils.isEmpty(skuList)) {
            log.info("dataSyncWms empty:{}", JSONUtil.toJsonStr(skuParam));
            return Result.success(true);
        }
        List<String> cargoCodeList = skuList.stream().map(Sku::getCargoCode).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<CargoOwner> cargoOwnerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cargoOwnerLambdaQueryWrapper.in(CargoOwner::getCode, cargoCodeList);
        cargoOwnerLambdaQueryWrapper.eq(CargoOwner::getFromSource, FromSourceEnum.ERP.value());
        List<CargoOwner> cargoOwnerList = cargoOwnerService.list(cargoOwnerLambdaQueryWrapper);
        Map<String, CargoOwner> cargoOwnerMap = cargoOwnerList.stream().collect(Collectors.toMap(CargoOwner::getCode, Function.identity()));
        skuList.forEach(sku -> {
            try {
                //自建货主不需要同步
                if (cargoOwnerMap.containsKey(sku.getCargoCode())) {
                    SkuUpc skuUpc = new SkuUpc();
                    if (Objects.equals(sku.getFromSource(), FromSourceEnum.WMS.value())) {
                        LambdaQueryWrapper<SkuUpc> upcQueryWrapper = new LambdaQueryWrapper<SkuUpc>();
                        upcQueryWrapper.eq(SkuUpc::getSkuCode, sku.getCode());
                        upcQueryWrapper.eq(SkuUpc::getCargoCode, sku.getCargoCode());
                        upcQueryWrapper.eq(SkuUpc::getIsDefault, SkuUpcDefaultEnum.YES.getStatus());
                        List<SkuUpc> skuUpcList = skuUpcService.list(upcQueryWrapper);
                        if (!CollectionUtils.isEmpty(skuUpcList)) {
                            skuUpc = skuUpcList.get(0);
                        }
                    }
                    WmsSku wmsSku = skuConvertCenterUtil.skuSyncErpData(sku, skuUpc);
                    goodsAdminRpcFacade.DataSyncWms(wmsSku);
                    LambdaQueryWrapper<Sku> skuLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    skuLambdaQueryWrapper
                            .eq(!StringUtils.isEmpty(sku.getCode()), Sku::getCode, sku.getCode())
                            .eq(!StringUtils.isEmpty(sku.getWarehouseCode()), Sku::getWarehouseCode, sku.getWarehouseCode())
                            .eq(!StringUtils.isEmpty(sku.getCargoCode()), Sku::getCargoCode, sku.getCargoCode());
                    Sku serviceOne = skuService.getOne(skuLambdaQueryWrapper);
                    if (!Objects.equals(serviceOne.getCargoCode(), sku.getCargoCode()) || !Objects.equals(serviceOne.getCode(), sku.getCode())
                            || !Objects.equals(serviceOne.getWarehouseCode(), sku.getWarehouseCode())) {
                        log.error("dataSyncWms-error-wms: {} {} {}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(sku), JSONUtil.toJsonStr(serviceOne));

                    } else {
                        sku.setSkuSync(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                        boolean updateById = skuService.update(sku, skuLambdaQueryWrapper);
                        if (!updateById) {
                            log.info("dataSyncWms-update-error-wms: {} {}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(sku));
//                        throw new BaseException(BaseBizEnum.TIP, "同步更新标记失败");
                        }
                    }
                }
            } catch (Exception e) {
                log.info("dataSyncWms:{}", JSONUtil.toJsonStr(sku));
                e.printStackTrace();
            }
        });
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> erpDeleteSkuRemoveWmsSku(List<SkuUpcDTO> skuUpcDTOList) {
        //TODO 移除UPC和UOM 将条码改成【条码+#+货主编码】
        if (!CollectionUtils.isEmpty(skuUpcDTOList)) {
            skuUpcDTOList.forEach(it -> {
                if (!it.getUpcCode().contains("###")) {
                    it.setUpcCode(StrUtil.join("###", it.getUpcCode(), System.currentTimeMillis()));
                    boolean updateById = skuUpcService.updateById(ConverterUtil.convert(it, SkuUpc.class));
                    if (!updateById) {
                        throw new BaseException(BaseBizEnum.TIP, "移除条码失败");
                    }
                    skuUpcService.removeById(it.getId());
                }
            });
        }
        return Result.success(true);
    }
}
