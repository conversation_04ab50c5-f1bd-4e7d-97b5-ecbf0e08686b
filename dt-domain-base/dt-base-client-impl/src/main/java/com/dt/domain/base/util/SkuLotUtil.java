package com.dt.domain.base.util;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.base.param.SkuLotParam;
import com.dt.domain.base.sku.entity.SkuLot;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/9/27 14:22
 */
@Component
public class SkuLotUtil extends QueryWrapper<SkuLot,SkuLotParam> {
    @Override
    public LambdaQueryWrapper<SkuLot> getQueryWrapper(SkuLotParam param) {
        LambdaQueryWrapper<SkuLot> lambdaQueryWrapper =  super.getQueryWrapper(param);
        lambdaQueryWrapper
                .eq(!StringUtils.isEmpty(param.getWarehouseCode()), SkuLot::getWarehouseCode, param.getWarehouseCode())
                .eq(!StringUtils.isEmpty(param.getCargoCode()), SkuLot::getCargoCode, param.getCargoCode())
                .eq(!StringUtils.isEmpty(param.getSkuCode()), SkuLot::getSkuCode, param.getSkuCode())
                .eq(!StringUtils.isEmpty(param.getManufDate()), SkuLot::getManufDate, param.getManufDate())
                .eq(!StringUtils.isEmpty(param.getExpireDate()), SkuLot::getExpireDate, param.getExpireDate())
                .in(CollectionUtil.isNotEmpty(param.getExpireDateList()),SkuLot::getExpireDate,param.getExpireDateList())
                .eq(!StringUtils.isEmpty(param.getReceiveDate()), SkuLot::getReceiveDate, param.getReceiveDate())
                .eq(!StringUtils.isEmpty(param.getProductionNo()), SkuLot::getProductionNo, param.getProductionNo())
                .eq(!StringUtils.isEmpty(param.getSkuQuality()), SkuLot::getSkuQuality, param.getSkuQuality())
                .eq(!StringUtils.isEmpty(param.getWithdrawDate()), SkuLot::getWithdrawDate, param.getWithdrawDate())
                .eq(!StringUtils.isEmpty(param.getExternalSkuLotNo()), SkuLot::getExternalSkuLotNo, param.getExternalSkuLotNo())

                .eq(!StringUtils.isEmpty(param.getCode()), SkuLot::getCode, param.getCode())
                .eq(!StringUtils.isEmpty(param.getStatus()), SkuLot::getStatus, param.getStatus())

                .in(!CollectionUtils.isEmpty(param.getWarehouseCodeList()), SkuLot::getWarehouseCode, param.getWarehouseCodeList())
                .in(!CollectionUtils.isEmpty(param.getCargoCodeList()), SkuLot::getCargoCode, param.getCargoCodeList())
                .in(!CollectionUtils.isEmpty(param.getSkuCodeList()), SkuLot::getSkuCode, param.getSkuCodeList())
                .in(!CollectionUtils.isEmpty(param.getCodeList()), SkuLot::getCode, param.getCodeList())
                .in(!CollectionUtils.isEmpty(param.getSkuQualityList()), SkuLot::getSkuQuality, param.getSkuQualityList())
                .in(!CollectionUtils.isEmpty(param.getExternalSkuLotNoList()), SkuLot::getExternalSkuLotNo, param.getExternalSkuLotNoList())

                .in(!CollectionUtils.isEmpty(param.getStatusList()), SkuLot::getStatus, param.getStatusList())
                .in(!CollectionUtils.isEmpty(param.getProductionNoList()), SkuLot::getProductionNo, param.getProductionNoList())

                .ge(!ObjectUtils.isEmpty(param.getManufDateStart()), SkuLot::getManufDate, param.getManufDateStart())
                .lt(!ObjectUtils.isEmpty(param.getManufDateEnd()), SkuLot::getManufDate, param.getManufDateEnd())
                .ge(!StringUtils.isEmpty(param.getExpireDateStart()), SkuLot::getExpireDate, param.getExpireDateStart())
                .lt(!StringUtils.isEmpty(param.getExpireDateEnd()), SkuLot::getExpireDate, param.getExpireDateEnd())
                .ge(!StringUtils.isEmpty(param.getReceiveDateStart()), SkuLot::getReceiveDate, param.getReceiveDateStart())
                .lt(!StringUtils.isEmpty(param.getReceiveDateEnd()), SkuLot::getReceiveDate, param.getReceiveDateEnd())
                .ge(!StringUtils.isEmpty(param.getWithdrawDateStart()), SkuLot::getWithdrawDate, param.getWithdrawDateStart())
                .between(!StringUtils.isEmpty(param.getWithdrawDateEnd()), SkuLot::getWithdrawDate,0, param.getWithdrawDateEnd())

                .eq(!StringUtils.isEmpty(param.getExternalLinkBillNo()), SkuLot::getExternalLinkBillNo, param.getExternalLinkBillNo())
                .in(!CollectionUtils.isEmpty(param.getExternalLinkBillNoList()), SkuLot::getExternalLinkBillNo, param.getExternalLinkBillNoList())

                .eq(!StringUtils.isEmpty(param.getLinkSkuLotNo()), SkuLot::getLinkSkuLotNo, param.getLinkSkuLotNo())
                .in(!CollectionUtils.isEmpty(param.getLinkSkuLotNoList()), SkuLot::getLinkSkuLotNo, param.getLinkSkuLotNoList())
                .eq(!StringUtils.isEmpty(param.getRemark()), SkuLot::getRemark, param.getRemark())

                .eq(!StringUtils.isEmpty(param.getPalletCode()), SkuLot::getPalletCode, param.getPalletCode())
                .in(!CollectionUtils.isEmpty(param.getPalletCodeList()), SkuLot::getPalletCode, param.getPalletCodeList())

                .eq(!StringUtils.isEmpty(param.getBoxCode()), SkuLot::getBoxCode, param.getBoxCode())
                .in(!CollectionUtils.isEmpty(param.getBoxCodeList()), SkuLot::getBoxCode, param.getBoxCodeList())

                .eq(!StringUtils.isEmpty(param.getValidityCode()), SkuLot::getValidityCode, param.getValidityCode())
                .in(!CollectionUtils.isEmpty(param.getValidityCodeList()), SkuLot::getValidityCode, param.getValidityCodeList())
        ;
        return lambdaQueryWrapper;

    }


}
