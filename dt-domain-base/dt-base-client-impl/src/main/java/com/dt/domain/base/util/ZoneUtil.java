package com.dt.domain.base.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.base.param.ZoneParam;
import com.dt.domain.base.warehouse.entity.Zone;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Component
public class ZoneUtil extends QueryWrapper<Zone,ZoneParam> {

    @Override
    public LambdaQueryWrapper<Zone> getQueryWrapper(ZoneParam param) {
        LambdaQueryWrapper<Zone> lambdaQueryWrapper =  super.getQueryWrapper(param);
        lambdaQueryWrapper
                .eq(!ObjectUtils.isEmpty(param.getCode()), Zone::getCode, param.getCode())
                .like(!ObjectUtils.isEmpty(param.getName()), Zone::getName, param.getName())
                .eq(!ObjectUtils.isEmpty(param.getEqName()), Zone::getName, param.getEqName())
                .eq(!ObjectUtils.isEmpty(param.getPickSeq()), Zone::getPickSeq, param.getPickSeq())
                .eq(!ObjectUtils.isEmpty(param.getType()), Zone::getType, param.getType())
                .in(!ObjectUtils.isEmpty(param.getTypeList()), Zone::getType, param.getTypeList())
                .eq(!ObjectUtils.isEmpty(param.getStorageRule()), Zone::getStorageRule, param.getStorageRule())
                .eq(!ObjectUtils.isEmpty(param.getSkuQuality()), Zone::getSkuQuality, param.getSkuQuality())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), Zone::getStatus, param.getStatus())
                .eq(!ObjectUtils.isEmpty(param.getCreatedBy()), Zone::getCreatedBy, param.getCreatedBy())
                .eq(!ObjectUtils.isEmpty(param.getUpdatedBy()), Zone::getUpdatedBy, param.getUpdatedBy())
                .in(!CollectionUtils.isEmpty(param.getCodeList()), Zone::getCode, param.getCodeList())

                .eq(!ObjectUtils.isEmpty(param.getPhysicalPartition()), Zone::getPhysicalPartition, param.getPhysicalPartition())
                .in(!CollectionUtils.isEmpty(param.getPhysicalPartitionList()), Zone::getPhysicalPartition, param.getPhysicalPartitionList())
                .gt(!ObjectUtils.isEmpty(param.getUpdatedTimeStart()),Zone::getUpdatedTime,param.getUpdatedTimeStart())
                .le(!ObjectUtils.isEmpty(param.getUpdatedTimeEnd()),Zone::getUpdatedTime,param.getUpdatedTimeEnd())
        ;
        return lambdaQueryWrapper;
    }

}
