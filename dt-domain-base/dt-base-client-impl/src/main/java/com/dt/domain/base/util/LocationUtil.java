package com.dt.domain.base.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.base.param.LocationParam;
import com.dt.domain.base.warehouse.entity.Location;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Component
public class LocationUtil extends QueryWrapper<Location, LocationParam> {

    @Override
    public LambdaQueryWrapper<Location> getQueryWrapper(LocationParam param) {
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper queryWrapper = getSortWrapper(param);
        LambdaQueryWrapper<Location> lambdaQueryWrapper = queryWrapper.lambda();
        lambdaQueryWrapper
                .eq(!ObjectUtils.isEmpty(param.getCode()), Location::getCode, param.getCode())
                .eq(StrUtil.isNotBlank(param.getThermostatic()),Location::getThermostatic,param.getThermostatic())
                .eq(!ObjectUtils.isEmpty(param.getZoneCode()), Location::getZoneCode, param.getZoneCode())
                .eq(StrUtil.isNotBlank(param.getZoneType()),Location::getZoneType,param.getZoneType())
                .eq(!ObjectUtils.isEmpty(param.getChargingModel()), Location::getChargingModel, param.getChargingModel())
                .eq(!ObjectUtils.isEmpty(param.getTunnelCode()), Location::getTunnelCode, param.getTunnelCode())
                .eq(!ObjectUtils.isEmpty(param.getPickSeq()), Location::getPickSeq, param.getPickSeq())
                .eq(!ObjectUtils.isEmpty(param.getShelfSeq()), Location::getShelfSeq, param.getShelfSeq())
                .eq(!ObjectUtils.isEmpty(param.getType()), Location::getType, param.getType())
                .eq(!ObjectUtils.isEmpty(param.getUseMode()), Location::getUseMode, param.getUseMode())
                .in(!ObjectUtils.isEmpty(param.getTypeList()), Location::getType, param.getTypeList())
                .eq(!ObjectUtils.isEmpty(param.getStorageRule()), Location::getStorageRule, param.getStorageRule())
                .eq(!ObjectUtils.isEmpty(param.getMixRuleCode()), Location::getMixRuleCode, param.getMixRuleCode())
                .eq(!ObjectUtils.isEmpty(param.getMaxSkuNum()), Location::getMaxSkuNum, param.getMaxSkuNum())
                .eq(!ObjectUtils.isEmpty(param.getMaxTypeNum()), Location::getMaxTypeNum, param.getMaxTypeNum())
                .eq(!ObjectUtils.isEmpty(param.getShelfType()), Location::getShelfType, param.getShelfType())
                .eq(!ObjectUtils.isEmpty(param.getStatus()), Location::getStatus, param.getStatus())
                .eq(!ObjectUtils.isEmpty(param.getCreatedBy()), Location::getCreatedBy, param.getCreatedBy())
                .eq(!ObjectUtils.isEmpty(param.getUpdatedBy()), Location::getUpdatedBy, param.getUpdatedBy())
                .in(!CollectionUtils.isEmpty(param.getCodeList()), Location::getCode, param.getCodeList())
                .in(!CollectionUtils.isEmpty(param.getZoneCodeList()), Location::getZoneCode, param.getZoneCodeList())
                .in(!CollectionUtils.isEmpty(param.getTunnelCodeList()), Location::getTunnelCode, param.getTunnelCodeList())
                .in(!CollectionUtils.isEmpty(param.getChargingModelList()), Location::getChargingModel, param.getChargingModelList())
                .gt(!ObjectUtils.isEmpty(param.getUpdatedTimeStart()), Location::getUpdatedTime, param.getUpdatedTimeStart())
                .le(!ObjectUtils.isEmpty(param.getUpdatedTimeEnd()), Location::getUpdatedTime, param.getUpdatedTimeEnd())
                .exists(!ObjectUtils.isEmpty(param.getSkuQuality()), "SELECT 1 FROM dt_zone WHERE code = zone_code AND sku_quality = '" + param.getSkuQuality() + "'")
        ;

        if (ObjectUtil.isNotEmpty(param.getSelectColumnList())) {
            lambdaQueryWrapper.select(Location.class, tableFieldInfo -> param.getSelectColumnList().contains(tableFieldInfo.getColumn().toUpperCase()));
        }
        if (param.getLocationTag() != null && param.getLocationTag() > 0) {
            lambdaQueryWrapper.apply(" location_tag = (location_tag|" + param.getLocationTag() + ")");
        }
        return lambdaQueryWrapper;
    }


}
