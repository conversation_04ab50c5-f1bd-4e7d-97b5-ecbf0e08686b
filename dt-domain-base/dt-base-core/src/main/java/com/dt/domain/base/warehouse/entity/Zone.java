package com.dt.domain.base.warehouse.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库区管理
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_zone")
@ApiModel(value="Zone对象", description="库区管理")
public class Zone extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "库区编码 不允许修改，唯一")
    private String code;

    @ApiModelProperty(value = "库区名称")
    private String name;

    @ApiModelProperty(value = "拣货序号")
    private Long pickSeq;

    @ApiModelProperty(value = "库区类型")
    private String type;

    @ApiModelProperty(value = "存放规则")
    private String storageRule;

    @ApiModelProperty(value = "正残属性")
    private String skuQuality;

    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "防火物理分区")
    private String physicalPartition;


}