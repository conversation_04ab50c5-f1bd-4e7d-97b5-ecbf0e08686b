package com.dt.domain.base.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/15 13:59
 */
@Data
public class InitCargoConfigParam implements Serializable {

    @ApiModelProperty("货主编码")
    private List<String> cargoCodeList;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("仓库编码/是否盐城")
    private Boolean ycWarehouse;

}
