package com.dt.domain.base.param;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 库区管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Zone对象", description="库区管理")
public class ZoneParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "库区编码 不允许修改，唯一")
    private String code;

    @ApiModelProperty(value = "库区名称")
    private String name;

    @ApiModelProperty(value = "库区名称")
    private String eqName;

    @ApiModelProperty(value = "拣货序号")
    private Long pickSeq;

    @ApiModelProperty(value = "库区类型 字典组:ZONE_TYPE")
    private String type;
    private List<String> typeList;

    @ApiModelProperty(value = "存放规则 字典组:LOCATION_MIX_RULE")
    private String storageRule;

    @ApiModelProperty(value = "正残属性 字典组:SKU_STATUS")
    private String skuQuality;

    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "库区编码列表")
    private List<String> codeList;

    @ApiModelProperty(value = "物理防火分区编码列表")
    private List<String> physicalPartitionList;
    private String physicalPartition;


}