package com.dt.domain.base.dto;

import cn.hutool.core.util.StrUtil;
import com.dt.component.common.dto.BaseDTO;
import com.dt.component.common.enums.sku.SkuOpenRuleEnum;
import com.dt.domain.base.dto.sku.SkuImagesUrlDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 商品档案
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "Sku对象", description = "商品档案")
public class SkuDTO extends BaseDTO implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码 不允许修改,唯一")
    private String code;

    @ApiModelProperty(value = "商品名")
    private String name;
    @ApiModelProperty(value = "物料编码")
    private String goodCode;

    @ApiModelProperty(value = "统一料号")
    private String itemCode;

    @ApiModelProperty(value = "品牌编码")
    private String brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "商品分类")
    private String type;

    @ApiModelProperty(value = "颜色")
    private String colour;

    @ApiModelProperty(value = "颜色代码")
    private String colourCode;

    @ApiModelProperty(value = "款号")
    private String style;

    @ApiModelProperty(value = "尺码")
    private String skuSize;

    @ApiModelProperty(value = "尺码代码")
    private String sizeCode;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "季节")
    private String season;

    @ApiModelProperty(value = "单价 默认为0，保留三位小数")
    private BigDecimal price;

    @ApiModelProperty(value = "长(cm) 默认为0，保留一位小数")
    private BigDecimal length;

    @ApiModelProperty(value = "宽(cm) 默认为0，保留一位小数")
    private BigDecimal width;

    @ApiModelProperty(value = "高(cm) 默认为0，保留一位小数")
    private BigDecimal height;

    @ApiModelProperty(value = "体积(cm³) 默认为0，保留一位小数，体积等于长*宽*高，自动计算")
    private BigDecimal volume;

    @ApiModelProperty(value = "毛重(kg) 默认为0，保留三位小数")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "净重(kg) 默认为0，保留三位小数")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "运输单元重量(kg) 默认为0，保留三位小数")
    private BigDecimal cartonWeight;

    @ApiModelProperty(value = "保质期管理 ")
    private Integer isLifeMgt;

    @ApiModelProperty(value = "耗材是否计重")
    private Integer materialAddWeight;

    @ApiModelProperty(value = "保质期(天) 如果保质期管理为是，必须大于0")
    private Integer lifeCycle;

    @ApiModelProperty(value = "禁收时限(天) 如果保质期管理为是，必须大于0")
    private Integer rejectCycle;

    @ApiModelProperty(value = "禁售时限(天) 如果保质期管理为是，必须大于0.[失效时期-禁售天数]")
    private Integer withdrawCycle;

    @ApiModelProperty(value = "预警天数(天) 如果保质期管理为是，必须大于0")
    private Integer warnCycle;

    @ApiModelProperty(value = "是否是新记录：默认是新:1,[1:新,-1:旧]")
    private Integer isNewRecord;

    @ApiModelProperty(value = "批次规则 不允许为空，默认标准规则(批次规则档案)")
    private String lotRuleCode;

    @ApiModelProperty(value = "分配规则 取值分配规则档案")
    private String allocationRuleCode;

    @ApiModelProperty(value = "周转规则 取值周转规则档案")
    private String turnoverRuleCode;

    @ApiModelProperty(value = "状态码 -1：禁用 1：启用")
    private Integer status;

    @ApiModelProperty(value = "订单标记")
    private Integer skuTag;

    @ApiModelProperty(value = "商品包装：整箱、拆零、异形")
    private String skuWrap;

    @ApiModelProperty(value = "订单拆分规则")
    private BigDecimal wrapQty;
    @ApiModelProperty(value = "数据来源")
    private String fromSource;
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "uom 列表数据")
    private List<SkuUomDTO> skuUomList;

    @ApiModelProperty(value = "upc 列表数据")
    private List<SkuUpcDTO> skuUpcList;

    @ApiModelProperty(value = "是否预包商品")
    private String isPre;

    @ApiModelProperty(value = "标准方案长")
    private BigDecimal standardLength;

    @ApiModelProperty(value = "标准方案宽")
    private BigDecimal standardWidth;

    @ApiModelProperty(value = "标准方案搞")
    private BigDecimal standardHeight;

    @ApiModelProperty(value = "标准方案体积")
    private BigDecimal standardVolume;

    @ApiModelProperty(value = "托规")
    private Integer bracketGauge;


    @ApiModelProperty(value = "erp报文")
    private String erpLog;

    @ApiModelProperty(value = "是否入口类 默认null 表示未维护")
    private Integer isEntrance;
    private String isEntranceDesc;

    @ApiModelProperty(value = "箱规")
    private Integer cartonPcs;

    @ApiModelProperty(value = "是否赠品 ZC=正常商品、ZP=赠品")
    private String itemType;
    private String itemTypeDesc;

    @ApiModelProperty(value = "创建人")
    private Long createdUserId;


    @ApiModelProperty(value = "是否淘天 true淘天  false 非淘天")
    private Boolean taotian;
    private String taotianDesc;

    @ApiModelProperty(value = "是否淘天 true抖超  false 非抖超")
    private Boolean douChao;
    private String douChaoDesc;

    private Map<String, Object> featureWms;

    private Map<String, Object> modifyFeatureWms;


    @ApiModelProperty(value = "标识PC和pda修改,修改待测量和待重新测量的skuTag")
    private Boolean modifyNotifyTag;

    /**
     * 商品中心tag1，add 宋
     */
    private Long tag1;

    public String uniqueKey() {
        return StrUtil.join(StrUtil.COLON, warehouseCode, cargoCode, code);
    }

    //解析featureWms
    //是否需要扫描升级【溯源码】isUptracSourceCode
    // isUptracSourceCode：1代表升级版溯源码，需流程管控出库扫描； 其余值 0或者空标识不需要管控
    public Boolean getNeedUptracSourceCodeTag() {
        if (this.taotian != null) {
            if (this.taotian) {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("isUptracSourceCode")
                        && Objects.equals(featureWms.get("isUptracSourceCode") + "", "1")) {
                    return true;
                }
            } else {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("wmsSourceCode")
                        && Objects.equals(featureWms.get("wmsSourceCode") + "", SkuOpenRuleEnum.OPEN_TAG.getCode() + "")) {
                    return true;
                }
            }
        }
        return false;
    }

    //1 代表贴【防伪扣】并出库扫描； 0或者空不需要扫描
    //是否需要扫描【防伪扣】antiCounterfeitingBuckle
    public Boolean getAntiCounterfeitingBuckleTag() {
        if (this.taotian != null) {
            if (this.taotian) {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("antiCounterfeitingBuckle")
                        && Objects.equals(featureWms.get("antiCounterfeitingBuckle") + "", "1")) {
                    return true;
                }
            } else {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("wmsAntiCounterfeitingBuckle")
                        && Objects.equals(featureWms.get("wmsAntiCounterfeitingBuckle") + "", SkuOpenRuleEnum.OPEN_TAG.getCode() + "")) {
                    return true;
                }
            }
        }
        return false;
    }

    //是否需要扫描【易撕贴】pullTape
    //1代表需要装【易撕贴】并出库扫描； 0或者空不需要扫描
    public Boolean getPullTapeTag() {
        if (this.taotian != null) {
            if (this.taotian) {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("pullTape")
                        && Objects.equals(featureWms.get("pullTape") + "", "1")) {
                    return true;
                }
            } else {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("wmsPullTape")
                        && Objects.equals(featureWms.get("wmsPullTape") + "", SkuOpenRuleEnum.OPEN_TAG.getCode() + "")) {
                    return true;
                }
            }
        }
        return false;
    }

    //【防伪扣】码段规则，需要在发货出库和逆向回仓时校验是否符合码段规则【正则】
    public String getAntiCounterfeitingBuckleRule() {
        if (this.taotian != null) {
            if (this.taotian) {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("antiCounterfeitingBuckleRule")) {
                    return featureWms.get("antiCounterfeitingBuckleRule") + "";
                }
            } else {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("wmsAntiCounterfeitingBuckleRule")) {
                    return featureWms.get("wmsAntiCounterfeitingBuckleRule") + "";
                }
            }
        }
        return "";
    }

    //【溯源码】码段规则，需要在发货出库和逆向回仓时校验是否符合码段规则【正则】
    public String getNeedUptracSourceCodeRule() {
        if (this.taotian != null) {
            if (this.taotian) {
                //淘天默认写死 【数字字母1-60位】
                return "[0-9A-Za-z]{1,60}";
            } else {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("wmsSourceCodeRule")) {
                    return featureWms.get("wmsSourceCodeRule") + "";
                }

            }
        }
        return "";
    }

    //【易撕贴】码段规则，需要在发货出库和逆向回仓时校验是否符合码段规则【正则】
    public String getPullTapeRule() {
        if (this.taotian != null) {
            if (this.taotian) {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("pullTapeRule")) {
                    return featureWms.get("pullTapeRule") + "";
                }
            } else {
                if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("wmsPullTapeRule")) {
                    return featureWms.get("wmsPullTapeRule") + "";
                }
            }
        }
        return "";
    }

    //淘天是否非效期品    "shelfLifeMgmt": "0" 无效期管理    "shelfLifeMgmt": "1",效期管理
    public Boolean getTaoTianNoShelfLifeMgmt() {
        if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("shelfLifeMgmt")
                && Objects.equals(featureWms.get("shelfLifeMgmt") + "", "0")) {
            return true;
        }
        return false;
    }

    //淘天是否非效期品    "shelfLifeMgmt": "0" 无效期管理    "shelfLifeMgmt": "1",效期管理
    public Boolean getTaoTianShelfLifeMgmt() {
        if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("shelfLifeMgmt")
                && Objects.equals(featureWms.get("shelfLifeMgmt") + "", "1")) {
            return true;
        }
        return false;
    }

    //是否开启SN管理 true 开启 false 不开启
    // 货品上的isSNMgmt 是否需要SN管理（Y=需要SN管理、N=不需要、空=不需要） 并且sn的管理模式 mode字段为 SN管理模式（1=出库+入库、2=出库+销退）
    public Boolean getSNMgmtIn() {
        if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("isSNMgmt")
                && Objects.equals(featureWms.get("isSNMgmt") + "", "Y")
                && featureWms.containsKey("mode")
                && Objects.equals(featureWms.get("mode") + "", "1")) {
            return true;
        }
        return false;
    }

    //货品上的isSNMgmt 是否需要SN管理（Y=需要SN管理、N=不需要、空=不需要） 并且sn的管理模式 mode字段为 SN管理模式（1=出库+入库、2=出库+销退）
    public Boolean getSNMgmtOutNeed() {
        if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("isSNMgmt")
                && Objects.equals(featureWms.get("isSNMgmt") + "", "Y")) {
            if (featureWms.containsKey("mode") && Objects.equals(featureWms.get("mode") + "", "1")) {
                return true;
            }
            if (featureWms.containsKey("mode") && Objects.equals(featureWms.get("mode") + "", "2")) {
                return true;
            }
        }
        return false;
    }

    //货品上的isSNMgmt 是否需要SN管理（Y=需要SN管理、N=不需要、空=不需要） 并且sn的管理模式 mode字段为 SN管理模式（1=出库+入库、2=出库+销退）
    public Boolean getSNMgmtOutAndIn() {
        if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("isSNMgmt")
                && Objects.equals(featureWms.get("isSNMgmt") + "", "Y")) {
            if (featureWms.containsKey("mode") && Objects.equals(featureWms.get("mode") + "", "1")) {
                return true;
            }
        }
        return false;
    }

    //货品上的isSNMgmt 是否需要SN管理（Y=需要SN管理、N=不需要、空=不需要） 并且sn的管理模式 mode字段为 SN管理模式（1=出库+入库、2=出库+销退）
    public Boolean getSNMgmtOutAndXT() {
        if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("isSNMgmt")
                && Objects.equals(featureWms.get("isSNMgmt") + "", "Y")) {
            if (featureWms.containsKey("mode") && Objects.equals(featureWms.get("mode") + "", "2")) {
                return true;
            }
        }
        return false;
    }

    //货品上的isSNMgmt 是否需要SN管理（Y=需要SN管理、N=不需要、空=不需要） 并且sn的管理模式 mode字段为 SN管理模式（1=出库+入库、2=出库+销退）
    public Boolean getSNMgmtXT() {
        if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("isSNMgmt")
                && Objects.equals(featureWms.get("isSNMgmt") + "", "Y")
                && featureWms.containsKey("mode")
                && Objects.equals(featureWms.get("mode") + "", "2")) {
            return true;
        }
        return false;
    }

//    //获取SN正则 数组
//    public List<String> getSNMgmtRuleRegular() {
//        if (!CollectionUtils.isEmpty(this.featureWms) && featureWms.containsKey("snRuleGroups")
//                && Objects.equals(featureWms.get("snRuleGroups") + "", "Y")) {
//            return new ArrayList<>();
//        }
//        return Lists.newArrayList();
//    }


    @ApiModelProperty("商品图片")
    private SkuImagesUrlDTO skuImagesUrlDTO;

    @ApiModelProperty(value = "SN正则表达式数组")
    private List<String> ruleRegularExpressionList;

    @ApiModelProperty("货品Id")
    private String erpGoodsCode;
}