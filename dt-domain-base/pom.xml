<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.dt</groupId>
    <artifactId>dt-domain-base</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>dt-domain-base</name>
    <description>WMS基础数据服务</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>2.2.5.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <!--other domain-->

        <!--component-->
        <component.common.version>1.0.0-SNAPSHOT</component.common.version>
        <component.mp.version>1.0.0-SNAPSHOT</component.mp.version>
        <component.redis.version>1.0.0-SNAPSHOT</component.redis.version>
        <component.utils.version>1.0.0-SNAPSHOT</component.utils.version>
        <component.uid.version>1.0.0-SNAPSHOT</component.uid.version>
        <saas.version>1.0.2-RELEASE</saas.version>
        <ares.version>3.1.12-RELEASE</ares.version>
        <!--Jar-->
        <seata.version>1.3.0</seata.version>
        <java.version>1.8</java.version>
        <joda.version>2.10.4</joda.version>
        <lombok.version>1.18.22</lombok.version>
        <swagger.version>1.5.21</swagger.version>
        <kryo.version>4.0.2</kryo.version>
        <kryo.serializers.version>0.45</kryo.serializers.version>
        <apollo.version>1.0-SNAPSHOT</apollo.version>
        <p6spy.version>1.5.7</p6spy.version>

        <!--Spring-->
        <spring.boot.version>2.2.5.RELEASE</spring.boot.version>
        <spring.cloud.version>Hoxton.SR3</spring.cloud.version>
        <alibaba.cloud.version>2.2.1.RELEASE</alibaba.cloud.version>
        <apache.dubbo.version>2.7.9</apache.dubbo.version>

        <!--other-->
        <start-class>com.dt.BaseApplication</start-class>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.source.version>3.0.1</maven.source.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.compilerVersion>${java.version}</maven.compiler.compilerVersion>
        <!--docker-->
        <docker.file.version>1.4.12</docker.file.version>
        <docker.repository>hub.docker.com</docker.repository>
    </properties>


    <modules>
        <module>dt-base-client</module>
        <module>dt-base-client-impl</module>
        <module>dt-base-core</module>
        <module>dt-base-starter</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!--SpringBoot 依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- SpringCloud 依赖-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--SpringCloud 阿里依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${alibaba.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${apache.dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.gavlyukovskiy</groupId>
                <artifactId>p6spy-spring-boot-starter</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <!--使用 lombok 简化 Java 代码-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo</artifactId>
                <version>${kryo.version}</version>
            </dependency>
            <dependency>
                <groupId>de.javakaffee</groupId>
                <artifactId>kryo-serializers</artifactId>
                <version>${kryo.serializers.version}</version>
            </dependency>
            <!--文档注释-->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!--Component start-->
            <!--common基础类-->
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>dt-component-common</artifactId>
                <version>${component.common.version}</version>
            </dependency>
            <!--mybatis plus-->
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>dt-component-mp</artifactId>
                <version>${component.mp.version}</version>
            </dependency>
            <!--redis-->
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>dt-component-redis</artifactId>
                <version>${component.redis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>dt-component-uid</artifactId>
                <version>${component.uid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>dt-component-utils</artifactId>
                <version>${component.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-all</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding.component</groupId>
                <artifactId>apolloclient-component</artifactId>
                <version>${apollo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>dt-component-saas</artifactId>
                <version>${saas.version}</version>
            </dependency>
            <!--Component end-->
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-goods-center-rpc-client</artifactId>
                <version>${ares.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>danding</id>
            <name>danding release resp</name>
            <url>http://mvn.yang800.cn/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>danding</id>
            <name>danding snapshot resp</name>
            <url>http://mvn.yang800.cn/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>danding</id>
            <name>danding</name>
            <url>http://mvn.yang800.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>
