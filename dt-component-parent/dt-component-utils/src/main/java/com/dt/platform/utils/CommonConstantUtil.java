package com.dt.platform.utils;

/**
 * <AUTHOR>
 * @date 2021/6/30 9:13
 */
public class CommonConstantUtil {
    /**
     * 每分钟秒数
     */
    public static final int SECONDS_PER_MINUTE = 60;
    /**
     * 每小时分钟数
     */
    public static final int MINUTES_PER_HOUR = 60;
    /**
     * 每天小时数
     */
    public static final int HOURS_PER_DAY = 24;
    /**
     * 每天秒数
     */
    public static final int SECONDS_PER_DAY = 86400;
    /**
     * 每天毫秒数
     */
    public static final long DAY_MILLISECONDS = 86400000L;
    /**
     * 小时毫秒数
     */
    public static final long HOURS_MILLISECONDS = 3600000L;
    /**
     * 拦截单分布式锁前缀 +warehouseCode+shipmentOrderCode  前缀+仓库编码+出库单号
     */
    public static final String INTERCEPT_PREFIX = "dt_wms_intercept_lock:";
    public static final String INTERCEPT_PREFIX_CODE = "code";
    public static final String INTERCEPT_PREFIX_STATUS_SUCCESS = "0";
    public static final String INTERCEPT_PREFIX_STATUS_FAIL = "-1";
    public static final String INTERCEPT_PREFIX_MESSAGE = "message";
    public static final String INTERCEPT_PREFIX_CACHE = "dt_customIntercept:";

    public static final String ASN_PART_CALL_BACK = "dt_wms_asn_call_back_lock:";

    //供应链金融
    public static final String SUPERVISION_LOCK = "dt_wms_supervision_lock:";
    public static final String REDEEM_LOCK = "dt_wms_supervision_lock:";
    public static final String DISPOSAL_LOCK = "dt_wms_supervision_lock:";

    public static final String FINANCE_CHANEL = "finance_redis_chanel";

    public static final String TRUCKING_LOCK = "dt_wms_trucking_lock";

    public static final String INTERCEPT_ORDER_OUT_BOUND = "intercept_order_out_bound";

    public static final String WMS_INNER_INTERCEPT = "DT-INTERCEPT-FROM-INNER-OMS";

    public static final String COLLECT_ALLOCATION_CHANEL = "collect_allocation_redis_chanel";

    /**
     * 逗号
     */
    public static final String COMMA = ",";
    /**
     * 分号
     */
    public static final String SEMICOLON = ";";
    public static final String ANALYSIS_SPLIT = ":::";

    /**
     * 分号
     */
    public static final String PDA = "PDA";

    //正则车牌
    public static final String CAR_GREEN = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-HJ-NP-Z0-9]{1}[A-HJ-NP-Z0-9]{6}";
    public static final String CAR_BLUE = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-HJ-NP-Z0-9]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂]{1}$";
    /**
     * PDA抽检拦截
     */
    public static final String WMS_INSPECTION_PDA = "PDA";//PDA
    /**
     * wcs称重拦截
     */
    public static final String WMS_WCS_WEIGHT = "WCS";//WCS
    public static final String WMS_WEB_WEIGHT = "WEB";//WEB
    //容器下架类型
    public static final String CONTAINER_OFF_SHELF = "OFF_SHELF";
    public static final String CONTAINER_PICK = "PICK";

    public static final String SKU_LOG_CALL_BACK_TAOTIAN = "商品信息回传淘天成功";
    //京东送货上门的特殊码
    public static final String JDWJ_DELIVERY_TO_DOOR = "JDWJ_DELIVERY_TO_DOOR";

    public static final String SPECIAL_DELIVERY_TO_DOOR = "SPECIAL_DELIVERY_TO_DOOR";
    public static final String ORIGINAL_PLATFORM = "WMS_ORIGINAL_PLATFORM";
    public static final String DOUYIN_SHOP_ID = "DOUYIN_SHOP_ID";

    public static final String TALLY_PALLET_PREFIX = "L";
    public static final String RECEIPT_PALLET_PREFIX = "S";

    public static final String ASN_RECEIPT_LOCK = "dt_wms_asn_lock:";

    public static final String OMS_NOTIFY_SHIP_STATUS = "OMS_CALLBACK_OK";
    public static final String OMS_NOTIFY_SHIP_STATUS_OK = "OK";
}
