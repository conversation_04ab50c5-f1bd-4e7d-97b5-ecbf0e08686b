package com.dt.component.common.enums.excel;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;

@Getter
public enum ExcelImportEnum {

    EXCEL_IMPORT_ENTRANCE("EXCEL_IMPORT_ENTRANCE", "导入一线入境", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/28034083213.xlsx", "entranceReadEventListener"),
    EXCEL_IMPORT_ZONE("DT_ZONE_IMPORT", "导入库区", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/6458018917.xls", "zoneAnalysisReadEventListener"),
    EXCEL_IMPORT_TUNNEL("DT_TUNNEL_IMPORT", "导入巷道", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/25247641229.xls", "tunnelAnalysisReadEventListener"),
    EXCEL_IMPORT_LOCATION("DT_LOCATION_IMPORT", "导入库位", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/42674537847.xls", "locationAnalysisReadEventListener"),
    EXCEL_IMPORT_MODIFY_LOCATION("DT_LOCATION_MODIFY_IMPORT", "导入修改库位", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/42674535320.xls", "locationModifyReadEventListener"),
    EXCEL_IMPORT_CONTAINER("DT_CONTAINER_IMPORT", "导入容器", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/6359758353.xlsx", "containerReadEventListener"),
    EXCEL_IMPORT_WORKBENCH("DT_WORKBENCH_IMPORT", "导入质检台", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/2111702137.xlsx", "workbenchReadEventListener"),
    //    EXCEL_IMPORT_PACKAGEMATERIAL("DT_PACKAGEMATERIAL_IMPORT","导入包材","https://dante-img.oss-cn-hangzhou.aliyuncs.com/2111699143.xls","packageMaterialReadEventListener"),
    EXCEL_IMPORT_PACKAGEMATERIAL("DT_PACKAGEMATERIAL_IMPORT", "导入包材", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/19364937978.xls", "packageMaterialReadEventListener"),
    //类已经注释
    EXCEL_IMPORT_RECEIPT_AND_SHELF_TEMP("DT_RECEIPT_AND_SHELF_TMP", "导入收货上架单据", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/33956008317.xls", "receiptAndShelfTempReadEventListener"),
    //receiptAndAsnAndTallyReadEventListener 目前没有开放(生成收货作业批次和上架单)
    EXCEL_RECEIPT_AND_ASN_AND_TALLY_IMPORT("DT_RECEIPT_AND_ASN_AND_TALLY_IMPORT", "导入收货和上架单作业批次", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/4306182502.xls", "receiptAndAsnAndTallyReadEventListener"),
    //导入收货作业批次 -- 不校验禁收
//    EXCEL_RECEIPT_IMPORT("DT_RECEIPT_IMPORT", "导入收货作业批次", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/91856223285.xls", "receiptImportReadEventListener"),
    EXCEL_RECEIPT_IMPORT("DT_RECEIPT_IMPORT", "导入收货作业批次", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/8826424027.xls", "receiptImportReadEventListener"),
//    EXCEL_IMPORT_SKU_FILES("DT_SKU_FILES_IMPORT", "导入编辑商品档案", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/4029726497.xls", "skuFilesReadEventListener"),
//    EXCEL_IMPORT_SKU_FILES("DT_SKU_FILES_IMPORT", "导入编辑商品档案", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/28165952346.xls", "skuFilesReadEventListener"),
    EXCEL_IMPORT_SKU_FILES("DT_SKU_FILES_IMPORT", "导入编辑商品档案", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/3263066847.xls", "skuFilesReadEventListener"),


    EXCEL_IMPORT_CREATE_SKU_FILES("DT_SKU_FILES_IMPORT_CREATE", "导入新建商品档案", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/4029724355.xlsx", "skuCreateFilesReadEventListener"),
    //导入收货作业批次 -- 检验禁收
//    EXCEL_RECEIPT_IMPORT_WITHDRAW("EXCEL_RECEIPT_IMPORT_WITHDRAW", "导入收货作业批次", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/91856222614.xls", "receiptImportReadEventListener"),
    EXCEL_RECEIPT_IMPORT_WITHDRAW("EXCEL_RECEIPT_IMPORT_WITHDRAW", "导入收货作业批次", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/8826424027.xls", "receiptImportReadEventListener"),

//    EXCEL_IMPORT_TALLY_AND_DETAIL("DT_TALLY_AND_DETAIL","导入理货报告","https://dante-img.oss-cn-hangzhou.aliyuncs.com/15237108875.xlsx","tallyReadEventListener"),
//    EXCEL_IMPORT_TALLY_AND_DETAIL("DT_TALLY_AND_DETAIL","导入理货报告","https://dante-img.oss-cn-hangzhou.aliyuncs.com/56515767386.xlsx","tallyReadEventListener"),
    EXCEL_IMPORT_TALLY_AND_DETAIL("DT_TALLY_AND_DETAIL","导入理货报告","https://dante-img.oss-cn-hangzhou.aliyuncs.com/61836409222.xlsx","tallyReadEventListener"),

    EXCEL_IMPORT_ASN("EXCEL_IMPORT_ASN","导入入库通知单","https://dante-img.oss-cn-hangzhou.aliyuncs.com/9706830866.xlsx","asnImportReadEventListener"),
    EXCEL_IMPORT_SHIPMENT("EXCEL_IMPORT_SHIPMENT","导入出库单","https://dante-img.oss-cn-hangzhou.aliyuncs.com/44094572673.xlsx","shipmentImportReadEventListener"),
    EXCEL_IMPORT_SKU_MATERIAL("EXCEL_IMPORT_SKU_MATERIAL","导入绑定耗材","https://dante-img.oss-cn-hangzhou.aliyuncs.com/60941270588.xls","skuMaterialReadEventListener"),
    EXCEL_IMPORT_MODIFY_MATERIAL("EXCEL_IMPORT_MODIFY_MATERIAL","导入修改包材","https://dante-img.oss-cn-hangzhou.aliyuncs.com/19364835364.xls","packageMaterialModifyReadEventListener"),
//    EXCEL_IMPORT_TRANSFER("EXCEL_IMPORT_TRANSFER","导入转移单","https://dante-img.oss-cn-hangzhou.aliyuncs.com/57719978660.xls","transferReadEventListener"),

    EXCEL_IMPORT_TRANSFER("EXCEL_IMPORT_TRANSFER","导入转移单","https://dante-img.oss-cn-hangzhou.aliyuncs.com/90808165204.xls","transferReadEventListener"),
//    EXCEL_IMPORT_TRANSFER_MODIFY("EXCEL_IMPORT_TRANSFER_MODIFY","导入修改转移单","https://dante-img.oss-cn-hangzhou.aliyuncs.com/57719980787.xls","transferReadModifyEventListener"),
    EXCEL_IMPORT_TRANSFER_MODIFY("EXCEL_IMPORT_TRANSFER_MODIFY","导入修改转移单","https://dante-img.oss-cn-hangzhou.aliyuncs.com/383449894.xls","transferReadModifyEventListener"),


    EXCEL_IMPORT_RECEIPT_BILL_RECORD("EXCEL_IMPORT_RECEIPT_BILL_RECORD","导入收货单据明细","https://dante-img.oss-cn-hangzhou.aliyuncs.com/71703701740.xlsx","receiptBillRecordReadEventListener"),
    //导入多货收货作业批次 -- 不校验禁收
//    EXCEL_EXTRA_RECEIPT_IMPORT("DT_EXTRA_RECEIPT_IMPORT", "导入多货收货作业批次", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/35225090638.xls", "receiptExtraImportReadEventListener"),
    EXCEL_EXTRA_RECEIPT_IMPORT("DT_EXTRA_RECEIPT_IMPORT", "导入多货收货作业批次", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/10150222399.xls", "receiptExtraImportReadEventListener"),
    //导入多货收货作业批次 -- 检验禁收
//    EXCEL_EXTRA_RECEIPT_IMPORT_WITHDRAW("EXCEL_EXTRA_RECEIPT_IMPORT_WITHDRAW", "导入多货收货作业批次", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/35225090638.xls", "receiptExtraImportReadEventListener"),
    EXCEL_EXTRA_RECEIPT_IMPORT_WITHDRAW("EXCEL_EXTRA_RECEIPT_IMPORT_WITHDRAW", "导入多货收货作业批次", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/10150222399.xls", "receiptExtraImportReadEventListener"),

    EXCEL_IMPORT_REMOVE_SKU_PACKAGE_MATERIAL_FILES("DT_SKU_REMOVE_PACK_MATERIAL_IMPORT_CREATE", "导入删除耗材绑定", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/17918148456.xlsx", "skuBindPackageMaterialRemoveEventListener"),

    DT_EXCEL_IMPORT_WAREHOUSE_RENT_VOLUME("DT_EXCEL_IMPORT_WAREHOUSE_RENT_VOLUME", "导入仓租商品长宽高", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/81396781255.xls", "warehouseRentVolumeEventListener"),
    DT_EXCEL_IMPORT_SW_IN_INIT("DT_EXCEL_IMPORT_SW_IN_INIT", "CW导入初始化库存", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/9755727059.xls", "CWStorageApplicationCollectImportListener"),
    DT_EXCEL_IMPORT_MOVE("DT_EXCEL_IMPORT_MOVE", "导入移位", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/53371761681.xls", "moveImportReadEventListener"),

    DT_EXCEL_IMPORT_FBA_INFORMATION("EXCEL_IMPORT_FBA_INFORMATION", "导入fba数据", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/40665613512.xls", "fbaInformationImportReadEventListener"),
    DT_EXCEL_IMPORT_FBA_OUT_PLAN("DT_EXCEL_IMPORT_FBA_OUT_PLAN", "导入fba出库计划数据", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/72095655507.xlsx", "FBAOutPlanReadEventListener"),

    DT_EXCEL_IMPORT_SALES_ORDER_ORIGIN("DT_EXCEL_IMPORT_SALES_ORDER_ORIGIN", "导入退货原单管理", "https://dante-img.oss-cn-hangzhou.aliyuncs.com/79291643733.xlsx", "salesOrderOriginReadEventListener"),
    ;

    private String funcCode;
    private String funcName;
    private String templateUrl;
    private String listenerBeanName;

    ExcelImportEnum(String funcCode, String funcName, String templateUrl, String listenerBeanName) {
        this.funcCode = funcCode;
        this.funcName = funcName;
        this.templateUrl = templateUrl;
        this.listenerBeanName = listenerBeanName;
    }

    public static ExcelImportEnum getEnum(String funcCode) {
        ExcelImportEnum result = Arrays.stream(ExcelImportEnum.values())
                .filter(a -> a.getFuncCode().equals(funcCode))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return result;
    }
}
