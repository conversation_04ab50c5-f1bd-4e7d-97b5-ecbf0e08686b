package com.dt.component.common.enums.rs;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Getter
@NoArgsConstructor
public enum RSReturnTypeEnum {

    ORIGIN_BACK(1, "原单退回"),
    CUSTOMER_RETURNS(2, "客退"),
    CUSTOMER_REJECT(3, "拒签"),
    TMS_CUT(4, "配拦截"),
    NO_HEAD(5, "无头包裹"),
    ;

    Integer code;

    String message;

    RSReturnTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static RSReturnTypeEnum fromInt(Integer code) {
        for (RSReturnTypeEnum from : RSReturnTypeEnum.values()) {
            if (Objects.equals(from.code, code)) {
                return from;
            }
        }
        throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
    }

    public static boolean notContain(Integer code) {
        for (RSReturnTypeEnum value : RSReturnTypeEnum.values()) {
            if (value.getCode().equals(code)) return false;
        }
        return true;
    }

    public static String desc(Integer code) {
        for (RSReturnTypeEnum value : RSReturnTypeEnum.values()) {
            if (value.getCode().equals(code)) return value.getMessage();
        }
        return "";
    }
}