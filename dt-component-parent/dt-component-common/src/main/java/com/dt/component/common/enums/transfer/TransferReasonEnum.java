package com.dt.component.common.enums.transfer;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;

/**
 * 转移原因 盘点、异常、其他
 * Created by nobody on 2020/12/28 17:35
 */
@Getter
public enum TransferReasonEnum  {
    INVENTORY("5","盘点"),
    EXCEPTION("10","异常"),
    OTHER("15","其他"),
    ;

    private String code;
    private String name;

    TransferReasonEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TransferReasonEnum getEnum(String code) {
        TransferReasonEnum result = Arrays.stream(TransferReasonEnum.values())
                .filter(a -> a.getCode().equals(code))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return result;
    }

    public static TransferReasonEnum fromName(String name) {
        TransferReasonEnum transferReasonEnum = Arrays.stream(TransferReasonEnum.values())
                .filter(it -> it.getName().equals(name))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(transferReasonEnum)) {
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return transferReasonEnum;
    }
}
