package com.dt.component.common.enums.asn;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2025/06/10
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum AsnStatDimensionEnum {
    DATE("DATE", "日期"),
    DATE_AND_CARGO("DATE_AND_CARGO", "日期+货主")
    ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 描述信息
     */
    private final String message;
}
