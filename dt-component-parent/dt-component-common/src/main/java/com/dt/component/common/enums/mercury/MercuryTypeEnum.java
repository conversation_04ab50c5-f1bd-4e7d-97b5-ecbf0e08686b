package com.dt.component.common.enums.mercury;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <p>
 * 单据类型枚举
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-14
 */
@Getter
@ApiModel("单据类型枚举")
public enum MercuryTypeEnum implements Serializable {
    SKU_REMEASURE("10", "商品重新测量"),
    SKU_MATERIAL("11", "淘天包耗材下发"),
    ASN_PRIORITY("12", "入库单优先级下发"),
    ASN_INSTRUCT("13", "品控入仓验货指令下发"),
    ASN_APPROVE("14", "品控入仓验货查验结果下发"),
    TALLY_ABNORMAL("15", "理货异常ID下发"),
    RETURN_ORDER_CREATE("50", "淘天销退单创建"),
    RETURN_ORDER_ADDITIONAL("51", "淘天销退单附加指令"),
    RETURN_ORDER_CANCEL("52", "淘天销退单取消"),

    WAREHOUSE_JOB_EXCEPTION("53", "仓内作业异常处理指令下发"),
    RETURN_ORDER_HOLD("54", "HOLD单指令下发"),
    ;
    private String type;
    private String name;

    MercuryTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }


    public static MercuryTypeEnum getEnum(String type) {
        MercuryTypeEnum result = Arrays.stream(MercuryTypeEnum.values())
                .filter(a -> a.getType().equals(type))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return result;
    }

    public String message() {
        return this.name;
    }

    public String value() {
        return this.type;
    }
}
