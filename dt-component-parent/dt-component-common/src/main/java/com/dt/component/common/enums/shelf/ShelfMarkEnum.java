package com.dt.component.common.enums.shelf;


import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/5 17:01
 */
@Getter
public enum ShelfMarkEnum {

    TAO_TIAN(1, "淘天"),
    WAIT_SHELF(2, "待申请"),
    AUTH_SHELF(3, "申请通过"),
    WAIT_CHECK(4, "待校验"),
    CHECK_AUTH(5, "校验通过"),
    SCAN_SN(6, "SN上架单"),
    ENTRY_ALLOW(7, "可入区"),
    ENTRY_DENY(8, "不可入区"),
    HOLD(9, "合流"),
    ;

    static Integer BASE = 1;
    private final Integer code;

    private final String desc;

    ShelfMarkEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @param orderTag
     * @return java.util.Set<com.dt.component.common.enums.bill.OrderTagEnum>
     * @author: WuXian
     * description: 反解析tag
     * create time: 2022/1/5 17:13
     */
    public static Set<ShelfMarkEnum> NumToEnum(Integer orderTag) {
        Set<ShelfMarkEnum> orderTagEnumSet = new HashSet<>();
        for (ShelfMarkEnum type : values()) {
            Integer mask = BASE << type.code();
            if (orderTag != 0 && mask == (mask & orderTag)) {
                orderTagEnumSet.add(type);
            }
        }
        return orderTagEnumSet;
    }

    /**
     * @param orderTagEnumList
     * @return int
     * @author: WuXian
     * description: 订单tag获取值
     * create time: 2022/1/5 17:13
     */
    public static Integer enumToNum(List<ShelfMarkEnum> orderTagEnumList) {
        Integer type = 0;
        for (ShelfMarkEnum orderTagEnum : orderTagEnumList.stream().distinct().collect(Collectors.toList())) {
            type += BASE << orderTagEnum.code();
        }
        return type;
    }

    /**
     * @param orderTagEnum
     * @return int
     * @author: WuXian
     * description: 订单tag获取值
     * create time: 2022/1/5 17:13
     */
    public static Integer enumToNum(ShelfMarkEnum orderTagEnum) {
        return BASE << orderTagEnum.code();
    }

    /**
     * @param orderTagList
     * @return java.lang.Integer
     * @author: WuXian
     * description:  解析tag值
     * create time: 2022/1/5 18:01
     */
    public static Integer queryParamListToInteger(List<Integer> orderTagList) {
        List<ShelfMarkEnum> orderTagEnumList = new ArrayList<>();
        Arrays.stream(ShelfMarkEnum.values()).forEach(it -> {
            if (orderTagList.contains(it.getCode())) {
                orderTagEnumList.add(it);
            }
        });
        Integer type = ShelfMarkEnum.enumToNum(orderTagEnumList);
        return type;
    }

    public final Integer code() {
        return code;
    }


    public static void main(String[] args) {
        System.out.println(NumToEnum(42));
    }
}
