package com.dt.component.common.enums.transfer;

import com.dt.component.common.enums.IResultEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;

@Getter
@ApiModel("AdjustReasonEnum 调整业务场景")
public enum TransferBusinessTypeEnum implements IResultEnum {

    WAREHOUSE_SKU_AVL_TO_DAMAGE("201", "库内商品正转残"),
    WAREHOUSE_SKU_DAMAGE_TO_AVL("202", "库内商品残转正"),
    STOCK_ATTR_CHANGE("203", "库存批次属性转移"),
    WAREHOUSE_SKU_EXPIRE_TO_DAMAGE("204", "库内商品过期转残"),
    ;
    private String code;
    private String name;

    TransferBusinessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public static TransferBusinessTypeEnum getEnum(String code) {
        TransferBusinessTypeEnum result = Arrays.stream(TransferBusinessTypeEnum.values())
                .filter(a -> a.getCode().equals(code))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(result)) {
           return null;
        }
        return result;
    }

    @Override
    public String message() {
        return this.name;
    }

    @Override
    public Integer value() {
        return Integer.parseInt(this.code);
    }
}
