package com.dt.component.common.enums.cargo;


import cn.hutool.json.JSONUtil;
import com.dt.component.common.vo.IdNameVO;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/1 17:01
 */
@Getter
public enum CargoTagEnum {
    /**
     * 货主标记1
     */
    FIRST_TAG(1, "D5"),
    /**
     * 货主标记2
     */
    SECOND_TAG(2, "B2"),
    CW_CARGO(3, "cw货主"),

    LY_CARGO(4, "乐漾货主"),
    TT_CARGO(5, "淘天货主"),

    ZI_WI_SOURCE(6, "ziwi金蝶货主"),

    ;

    static Integer BASE = 1;
    private final Integer code;

    private final String desc;

    CargoTagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @param orderTag
     * @return java.util.Set<com.dt.component.common.enums.bill.OrderTagEnum>
     * @author: WuXian
     * description: 反解析tag
     * create time: 2022/1/5 17:13
     */
    public static Set<CargoTagEnum> NumToEnum(Integer orderTag) {
        Set<CargoTagEnum> orderTagEnumSet = new HashSet<>();
        for (CargoTagEnum type : values()) {
            Integer mask = BASE << type.code();
            if (orderTag != 0 && mask == (mask & orderTag)) {
                orderTagEnumSet.add(type);
            }
        }
        return orderTagEnumSet;
    }

    /**
     * @param orderTagEnumList
     * @return int
     * @author: WuXian
     * description: 订单tag获取值
     * create time: 2022/1/5 17:13
     */
    public static Integer enumToNum(List<CargoTagEnum> orderTagEnumList) {
        Integer type = 0;
        for (CargoTagEnum orderTagEnum : orderTagEnumList.stream().distinct().collect(Collectors.toList())) {
            type += BASE << orderTagEnum.code();
        }
        return type;
    }

    /**
     * @param orderTagEnum
     * @return int
     * @author: WuXian
     * description: 订单tag获取值
     * create time: 2022/1/5 17:13
     */
    public static Integer enumToNum(CargoTagEnum orderTagEnum) {
        return BASE << orderTagEnum.code();
    }

    /**
     * @param orderTagList
     * @return java.lang.Integer
     * @author: WuXian
     * description:  解析tag值
     * create time: 2022/1/5 18:01
     */
    public static Integer queryParamListToInteger(List<Integer> orderTagList) {
        List<CargoTagEnum> orderTagEnumList = new ArrayList<>();
        Arrays.stream(CargoTagEnum.values()).forEach(it -> {
            if (orderTagList.contains(it.getCode())) {
                orderTagEnumList.add(it);
            }
        });
        Integer type = CargoTagEnum.enumToNum(orderTagEnumList);
        return type;
    }

    public final Integer code() {
        return code;
    }

    /**
     * @param cargoTag
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 乐漾货主
     * @date 2024/9/20 13:24
     */
    public static Boolean lyCargo(Integer cargoTag) {
        if (cargoTag != null && CargoTagEnum.NumToEnum(cargoTag).contains(LY_CARGO)) {
            return true;
        }
        return false;
    }


    public static void main(String[] args) {
//        Integer orderTag = CargoTagEnum.enumToNum(CargoTagEnum.FIRST_TAG);
//        //方式一
//        if (!StringUtils.isEmpty(orderTag) && CargoTagEnum.NumToEnum(orderTag)
//                .stream().anyMatch(a -> a.getCode().equals(CargoTagEnum.FIRST_TAG.code()))) {
//            System.out.println(CargoTagEnum.FIRST_TAG.getDesc());
//        } else {
//            System.out.println("不满足要求");
//        }
//        //方式二
//        if ((CargoTagEnum.enumToNum(CargoTagEnum.FIRST_TAG) | orderTag) == orderTag) {
//            System.out.println(CargoTagEnum.FIRST_TAG.getDesc());
//        } else {
//            System.out.println("不满足要求");
//        }
//        //方式三
//        if (CargoTagEnum.NumToEnum(orderTag).contains(CargoTagEnum.FIRST_TAG)) {
//            System.out.println(CargoTagEnum.FIRST_TAG.getDesc());
//        } else {
//            System.out.println("不满足要求");
//        }
//
//
//        Integer type = CargoTagEnum.enumToNum(Arrays.asList(CargoTagEnum.FIRST_TAG));
//        System.out.println(type);
//        System.out.println(CargoTagEnum.enumToNum(Arrays.asList(CargoTagEnum.FIRST_TAG)));
        Set<CargoTagEnum> cargoTagEnumSet = CargoTagEnum.NumToEnum(2);
        System.out.println(JSONUtil.toJsonStr(IdNameVO.build(cargoTagEnumSet.stream().collect(Collectors.toList()), "code", "desc")));

    }
}
