package com.dt.component.common.enums.cargo;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/17 16:56
 */
@Getter
@NoArgsConstructor
public enum CargoConfigParamEnum implements Serializable {
//
//    /*** 是否超收*/
//    ALLOW_SCANIN_OVERSHIP("RECEIVE_CONFIG", "ALLOW_SCANIN_OVERSHIP", "超收配置", "1:允许超收,-1:不允许超收","-1"),
//    /*** 是否超品*/
//    ALLOW_SCANIN_OVERSHIP_SKU("RECEIVE_CONFIG", "ALLOW_SCANIN_OVERSHIP_SKU", "超品配置", "1:允许超品,-1:不允许超品","-1"),
//    /*** 超收百分比*/
//    ALLOW_SCANIN_OVERSHIP_PERCENT("RECEIVE_CONFIG", "ALLOW_SCANIN_OVERSHIP_PERCENT", "百分比配置", "填写正整数","0"),

    /*** 是否开启理货报告*/
    TALLY_REPORT("RECEIVE_CONFIG", "TALLY_REPORT", "理货报告", "1:开启,-1:禁用", "-1"),
    /**
     * 部分回传
     */
    PART_CALL_BACK("RECEIVE_CONFIG", "PART_CALL_BACK", "部分回传", "1:开启,-1:禁用", "-1"),

    RECEIVE_BEFORE_REVIEW("RECEIVE_CONFIG", "RECEIVE_BEFORE_REVIEW", "先收后审", "1:开启,-1:禁用", "-1"),

    /**
     * 长宽高
     */
    LENGTH_WIDTH_HEIGHT_FORMAT("OTHER_CONFIG", "LENGTH_WIDTH_HEIGHT_FORMAT", "长宽高保留小数位", "填写正整数", "1"),
    /**
     * 体积
     */
    VOLUME_FORMAT("OTHER_CONFIG", "VOLUME_FORMAT", "体积保留小数位", "填写正整数", "3"),
    /**
     * 重量
     */
    WEIGHT_FORMAT("OTHER_CONFIG", "WEIGHT_FORMAT", "重量保留小数位", "填写正整数", "3"),
    /**
     * 数量
     */
    NUMBER_FORMAT("OTHER_CONFIG", "NUMBER_FORMAT", "数量保留小数位", "填写正整数", "0"),

    /**允许部分出库*/
//    ALLOW_PARTIAL_DELIVER("OUT_BOUND_CONFIG","ALLOW_PARTIAL_DELIVER","是否允许部分出库配置","1:允许部分出库,-1:不允许部分出库",""),

    /**
     * 是否校验重量差
     */
    ALLOW_CHECK_WEIGHT("OUT_BOUND_CONFIG", "ALLOW_CHECK_WEIGHT", "是否校验包裹重量差", "1:校验,-1:不校验", "1"),
    /**
     * 商品包装拆单
     */
    SKU_PACKAGE_UNIT_SPLIT_RULE("OUT_BOUND_CONFIG", "SKU_PACKAGE_UNIT_SPLIT_RULE", "商品包装拆单", "1:开启,-1:关闭", "-1"),
    /**
     * 订单拆分规则拆单
     */
    SHIPMENT_ORDER_SPLIT_RULE("OUT_BOUND_CONFIG", "SHIPMENT_ORDER_SPLIT_RULE", "订单拆分规则拆单", "1:开启,-1:关闭", "-1"),
    /**
     * 批量复核提示包裹数
     */
    BATCH_CHECK_NOTE("OUT_BOUND_CONFIG", "BATCH_CHECK_NOTE", "批量复核提示包裹数", "填写正整数", "1"),
    /**
     * 校验重量百分比
     */
    ALLOW_CHECK_WEIGHT_PERCENT("OUT_BOUND_CONFIG", "ALLOW_CHECK_WEIGHT_PERCENT", "重量差百分比配置", "【重量下限/克】-【重量上限/克】,【上下浮动绝对值/克】,【重量浮动百分比】;", "1-50000,0,10%;"),
    /**
     * 抽检系数
     */
    SPOT_CHECK("OUT_BOUND_CONFIG", "SPOT_CHECK", "抽检系数", "填写正整数", "2"),
    /**
     * 溯源码规则
     */
    SOURCE_CODE_PATTERN("OUT_BOUND_CONFIG", "SOURCE_CODE_PATTERN", "溯源码规则", "请填写正则表达式", ""),
    /**
     * 是否开启溯源码
     */
    SOURCE_CODE("OUT_BOUND_CONFIG", "SOURCE_CODE", "溯源码", "1:开启,-1:关闭", "-1"),

    SOURCE_CODE_REPEATED("OUT_BOUND_CONFIG", "SOURCE_CODE_REPEATED", "溯源码重复性校验", "1:允许同商品的溯源码相同,隐含包裹内溯源码可重复,2:同商品溯源码不允许重复,包裹内不同商品允许重复,3:同商品溯源码不允许重复,包裹内不同商品之间也不允许重复", "1"),
    /**
     * 包材是否强校验
     */
    CHECK_PACKMATERIAL("OUT_BOUND_CONFIG", "CHECK_PACKMATERIAL", "包材是否强校验", "1:开启,-1:关闭", "-1"),
    /**
     * 拣选单是否拼接重量
     */
    PICK_CONCAT_WEIGHT("OUT_BOUND_CONFIG", "PICK_CONCAT_WEIGHT", "拣选单是否拼接重量", "1:开启,-1:关闭", "-1"),
//    /**拣选单是否展示效期*/
//    PICK_VALIDITY_PERIOD("OUT_BOUND_CONFIG","PICK_VALIDITY_PERIOD","拣选单是否展示效期(C单)","1:开启,-1:关闭","-1"),
    /**
     * 库存配置 erp下发货主校验 wms自建货主不检验
     */
    OUT_STOCK_CHECK("STOCK_CONFIG", "OUT_STOCK_CHECK", "上游校验库存", "1:开启,-1:关闭", "1"),

//    /**
//     * 货主中占
//     */
//    CENTRAL_OCCUPY("OUT_BOUND_CONFIG", "CENTRAL_OCCUPY", "货主中占标记", "1:开启,-1:关闭", "-1"),
    /**
     * 货主存储区拣货
     */
    STORE_OCCUPY("OUT_BOUND_CONFIG", "STORE_OCCUPY", "货主存储区拣货", "1:开启,-1:关闭", "-1"),

    IS_4PL_CHECK("INTERNAL_CONFIG", "IS_4PL_CHECK", "是否4PL货主", "1:是,-1:否", "-1"),
    /**
     * 货主脱敏配置
     */
    CARGO_DESENSITIZATION_CONFIG("OUT_BOUND_CONFIG", "CARGO_DESENSITIZATION_CONFIG", "货主脱敏配置", "express:面单;packingList:清单;allPacking:总装箱清单;", ""),

    PACK_CHECK_OUT_CALL_BACK("OUT_BOUND_CONFIG", "PACK_CHECK_OUT_CALL_BACK", "复核完虚拟出库", "1:开启,-1:开启", "-1"),
    PACK_MATERIAL_LOCK_MIN_NUM("OUT_BOUND_CONFIG", "PACK_MATERIAL_LOCK_MIN_NUM", "包材固定最小数", "填写正整数", "10"),
    ;

    /**
     * 分组 --CargoConfigGroupEnum
     */
    protected String group;
    /**
     * 编码
     */
    protected String code;
    /**
     * 名称
     */
    protected String name;

    /**
     * 描述信息
     */
    protected String message;

    /**
     * 默认值
     */
    protected String value;

    /**
     * 构造方法
     */
    CargoConfigParamEnum(String group, String code, String name, String message, String value) {
        this.group = group;
        this.code = code;
        this.name = name;
        this.message = message;
        this.value = value;
    }

    public CargoConfigParamEnum getEnum(String code) {
        for (CargoConfigParamEnum param : values()) {
            if (param.getCode().equals(code)) {
                return param;
            }
        }
        return null;
    }

    public static CargoConfigParamEnum fromCode(String code) {
        for (CargoConfigParamEnum from : CargoConfigParamEnum.values()) {
            if (Objects.equals(from.code, code)) {
                return from;
            }
        }
        throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
    }

    public static List<CargoConfigParamEnum> getList(String param) {
        List<CargoConfigParamEnum> list = Arrays.asList(CargoConfigParamEnum.values());
        if (list.isEmpty()) {
            return new ArrayList<>();
        }
//        list = list.stream().filter(entity -> Objects.equals(entity.getGroup(), param)).collect(Collectors.toList());
        return list.stream().filter(entity -> Objects.equals(entity.getGroup(), param)).collect(Collectors.toList());
    }

    /**
     * 判断数值是否属于枚举类的值
     *
     * @param key
     * @return
     */
    public static boolean isInclude(String key) {
        boolean include = false;
        for (CargoConfigParamEnum e : CargoConfigParamEnum.values()) {
            if (Objects.equals(e.getCode(), key)) {
                include = true;
                break;
            }
        }
        return include;
    }
}
