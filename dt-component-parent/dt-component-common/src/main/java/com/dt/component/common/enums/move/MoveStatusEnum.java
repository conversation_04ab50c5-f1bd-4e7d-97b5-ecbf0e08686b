package com.dt.component.common.enums.move;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.IResultEnum;
import com.dt.component.common.exceptions.BaseException;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;

/**
 * <p>
 * 移位单状态枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-14
 */
@Getter
@ApiModel("Move 状态枚举")
public enum MoveStatusEnum implements IResultEnum {

    STATUS_WAIT_MOVE("10", "待移位"),
    STATUS_DOING("11", "移位中"),
    STATUS_COMPLETED("12", "移位完成"),
    STATUS_CANCELED("13", "已取消"),
    ;

    private String status;
    private String name;

    MoveStatusEnum(String status, String name) {
        this.status = status;
        this.name = name;
    }


    public static MoveStatusEnum getEnum(String status) {
        MoveStatusEnum result = Arrays.stream(MoveStatusEnum.values())
                .filter(a -> a.getStatus().equals(status))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return result;
    }

    @Override
    public String message() {
        return this.name;
    }

    @Override
    public Integer value() {
        return Integer.parseInt(this.status);
    }
}
