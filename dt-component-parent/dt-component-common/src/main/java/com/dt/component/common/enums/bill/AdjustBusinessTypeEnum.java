package com.dt.component.common.enums.bill;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.IResultEnum;
import com.dt.component.common.exceptions.BaseException;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;


@Getter
@ApiModel("AdjustReasonEnum 调整业务场景")
public enum AdjustBusinessTypeEnum implements IResultEnum {

    STOCK_INVENTORY("101", "库存盘点"),
    MATERIAL_INVENTORY("102", "包耗材盘点"),
    MATERIAL_OUT("103", "包耗材出库"),
    STOCK_INIT("104", "库存初始化"),
    RETURN_IN("105", "销退入库"),
    SYSTEM_ERROR("106", "系统异常"),
    OP_EXCEPTION("107", "异常登记"),
    ;
    private String code;
    private String name;

    AdjustBusinessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public static AdjustBusinessTypeEnum getEnum(String code) {
        AdjustBusinessTypeEnum result = Arrays.stream(AdjustBusinessTypeEnum.values())
                .filter(a -> a.getCode().equals(code))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(result)) {
           return null;
        }
        return result;
    }

    @Override
    public String message() {
        return this.name;
    }

    @Override
    public Integer value() {
        return Integer.parseInt(this.code);
    }
}
