package com.dt.component.common.enums.bill;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.IResultEnum;
import com.dt.component.common.exceptions.BaseException;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;

/**
 * <p>
 * 移位单状态枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-14
 */
@Getter
@ApiModel("Adjust 类型枚举")
public enum AdjustTypeEnum implements IResultEnum {

    ADD("1", "调增"),
    SUBTRACT("5", "调减"),
//    ADJUST("10", "转移"),
    ;

    private String status;
    private String name;

    AdjustTypeEnum(String status, String name) {
        this.status = status;
        this.name = name;
    }


    public static AdjustTypeEnum getEnum(String status) {
        AdjustTypeEnum result = Arrays.stream(AdjustTypeEnum.values())
                .filter(a -> a.getStatus().equals(status))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return result;
    }

    @Override
    public String message() {
        return this.name;
    }

    @Override
    public Integer value() {
        return Integer.parseInt(this.status);
    }
}
