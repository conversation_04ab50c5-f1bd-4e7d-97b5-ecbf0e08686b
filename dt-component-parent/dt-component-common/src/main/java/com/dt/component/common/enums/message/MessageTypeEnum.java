package com.dt.component.common.enums.message;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Optional;

@Getter
@ApiModel("消息类型")
@Slf4j
public enum MessageTypeEnum implements Serializable {

    CW_IN_CONFIRM("mt001", "CW入库申请确认"),
    CW_IN_CONFIRM_COMPLETE("mt002", "CW入库申请完成通知"),
    CW_IN_COLLECT("mt003", "CW入库采集"),
    CW_OUT_CONFIRM("mt010", "CW出库申请确认"),
    CW_OUT_CONFIRM_COMPLETE("mt011", "CW出库申请完成通知"),

    OPERATION_COMPLETE_CONTAINER("5", "完成容器"),
    OPERATION_CIRCLE_GOODS_COMPLETE_CONTAINER("6", "分销退货入库"),
    OPERATION_CANCEL_CIRCLE_GOODS_COMPLETE_CONTAINER("7", "取消分销退货入库"),
    OPERATION_RECEIPT_SHELF("10", "收货上架"),
    OPERATION_SHIPMENT("15", "预处理"),
    OPERATION_CONVERGE("20", "汇单"),
    OPERATION_INTERCEPT("25", "拦截"),
    OPERATION_MOVE("30", "移位"),
    OPERATION_PICK_RETURN("35", "拣选交回"),
    OPERATION_RETURN_COMPLETE_CONTAINER("40", "归位提交容器"),
    OPERATION_RETURN_SHELF("45", "归位上架"),
    OPERATION_STOCK("50", "出库"),
    OPERATION_CIRCLE_GOODS_STOCK("51", "分销圈货出库"),
    OPERATION_CIRCLE_GOODS_SALE_STOCK("52", "分销销售出库"),
    OPERATION_ADJUST("55", "调整"),
    OPERATION_CANCEL_ALLOCATION("60", "取消分配"),
    OPERATION_CANCEL_PICK("65", "取消拣选"),
    OPERATION_CANCEL_SHIPMENT("70", "取消预处理"),
    OPERATION_TRANSFER("75", "转移"),
    OPERATION_TRANSFER_SUBMIT("80", "转移提交审核"),
    OPERATION_TRANSFER_CONFIRM("85", "转移确认"),
    OPERATION_TRANSFER_CANCEL("90", "转移取消"),
    OPERATION_ADJUST_SUBMIT("95", "调整单提交审核"),
    OPERATION_ADJUST_CONFIRM("100", "调整单确认"),
    OPERATION_ADJUST_CANCEL("105", "调整单取消"),
    OPERATION_RECEIPT_CANCEL("110", "取消收货"),
    OPERATION_PRE_PACKAGE_LOCK_ORIGIN("115", "预包锁定来源批次"),
    OPERATION_PRE_PACKAGE_REVIEW("116", "预包复核"),
    OPERATION_PRE_PACKAGE_SHELF("117", "预包上架"),
    OPERATION_PRE_UPC_SHELF("118", "预包新条码上架"),
    OPERATION_PRE_SPLIT_LOCK_ORIGIN("119", "预包拆包锁定来源批次"),
    OPERATION_PRE_SPLIT_SUBMIT("120", "预包拆包提交容器"),
    OPERATION_PRE_SPLIT_SHELF("121", "预包拆包上架"),
    OPERATION_FINANCE_SUPERVISION_PREPARE("122", "预监管"),
    OPERATION_FINANCE_SUPERVISION_CANCEL("123", "监管取消"),
    OPERATION_FINANCE_SUPERVISION_MOVE("124", "监管移位"),
    OPERATION_FINANCE_REDEEM_THAW("125", "赎回解冻"),
    OPERATION_FINANCE_REDEEM_MOVE("126", "赎回移位"),
    OPERATION_FINANCE_DISPOSAL("127", "监管处置"),
    OPERATION_FINANCE_DISPOSAL_MOVE("128", "监管处置移位"),
    OPERATION_UPSTREAM_OCCUPY("130", "上游占用"),
    OPERATION_UPSTREAM_RELEASE("131", "上游释放占用"),
    OPERATION_ADD_FROZEN("140", "增加占用"),
    OPERATION_SHELF_RELEASE("150", "上架解冻"),
    OPERATION_OFF_SHELF("160", "下架"),
    OPERATION_CW_STORAGE_APPLICATION("170", "CW入库申请"),
    OPERATION_CW_TRANSFER_CONFIRM("171", "CW调拨确认"),
    OPERATION_CW_TRANSFER_COLLECT("172", "CW调拨采集"),
    OPERATION_CW_TRANSFER_SHELF("173", "CW调拨上架"),
    OPERATION_CW_TRANSFER_OUT("174", "CW调拨出库"),
    OPERATION_SALE_RETURN_RECEIPT("180", "质检完成"), // 销退质检完成
    OPERATION_SALE_RETURN_SHELF("181", "销退上架"),
    OPERATION_BOM_ASSEMBLE("188", "组套确认","bomAssembleHandler"),
    OPERATION_BOM_DISASSEMBLE("189", "拆套确认","bomDisassembleHandler"),
    OPERATION_BOM_ASSEMBLE_SHELF("190", "组套上架"),
    OPERATION_BOM_DISASSEMBLE_SHELF("191", "拆套上架"),
    OPERATION_SALE_RETURN_SYNC_CCS("500", "同步销退单到CCS","salesReturnOrderSyncCCS"),
    OPERATION_SALE_RETURN_SYNC_ERP("501", "同步销退单到ERP"),
    OPERATION_SALE_RETURN_SHELF_SYNC_ERP("502", "销退上架同步ERP","salesReturnOrderShelfSyncERP"),

    OPERATION_SALE_RETURN_CREATE_CALLBACK("600", "销退单创建回告"),
    OPERATION_SALE_RETURN_ARRIVE_CALLBACK("601", "销退单交接回告"),
    OPERATION_SALE_RETURN_REJECT_CALLBACK("602", "销退单拒收回告"),
    OPERATION_SALE_RETURN_PULL("603", "销退单拉单"),
    OPERATION_SALE_RETURN_REJECT_CALLBACK_ORIGIN("604", "销退单拒收回告正向单据"),
    OPERATION_SALE_RETURN_RECEIVE_CALLBACK_ORIGIN("605", "销退单签收回告正向单据"),
    OPERATION_SALE_RETURN_SHELF_CALLBACK("606", "销退单上架回告","remoteSalesReturnOrderCallback"),
    OPERATION_ADJUST_COMPLETE_CALLBACK("610", "调整确认回告"),
    OPERATION_TRANSFER_OCCUPY_CALLBACK("611", "转移占用回告"),
    OPERATION_TRANSFER_COMPLETE_CALLBACK("612", "转移确认回告"),
    OPERATION_TRANSFER_OCCUPY_CANCEL_CALLBACK("613", "转移占用取消回告"),

    OPERATION_BOM_ASSEMBLE_LOCK("700", "BOM组套锁定库存","bomAssembleLockHandler"),
    OPERATION_BOM_ASSEMBLE_LOCK_CALLBACK("701", "BOM组套锁定库存回传","bomAssembleLockCallBackHandler"),
    OPERATION_BOM_DISASSEMBLE_LOCK("702", "BOM拆套锁定库存","bomDisassembleLockHandler"),
    OPERATION_BOM_DISASSEMBLE_LOCK_CALLBACK("703", "BOM拆套锁定库存回传","bomDisassembleLockCallBackHandler"),
    OPERATION_BOM_SHELF_CALLBACK("704", "BOM上架回传","bomShelfCallBackHandler"),
    ;

    private final String type;
    private final String name;
    private final String messageHandler;

    MessageTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
        this.messageHandler = "";
    }
    
    MessageTypeEnum(String type, String name,String messageHandler) {
        this.type = type;
        this.name = name;
        this.messageHandler = messageHandler;
    }

    public static MessageTypeEnum getEnum(String type) {
        MessageTypeEnum result =
                Arrays.stream(MessageTypeEnum.values()).filter(a -> a.getType().equals(type)).findFirst().orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            log.info("{} not support",type);
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return result;
    }

    public static Optional<MessageTypeEnum> optional(String type) {
        MessageTypeEnum result =
                Arrays.stream(MessageTypeEnum.values()).filter(a -> a.getType().equals(type)).findFirst().orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            return Optional.empty();
        }
        return Optional.of(result);
    }
    

    public String message() {
        return this.name;
    }

    public String value() {
        return this.type;
    }
}
