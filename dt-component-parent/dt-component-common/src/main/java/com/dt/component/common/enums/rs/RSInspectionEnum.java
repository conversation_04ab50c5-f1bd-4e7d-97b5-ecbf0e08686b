package com.dt.component.common.enums.rs;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Getter
@NoArgsConstructor
public enum RSInspectionEnum {

    PASS(1, "通过"),
    FAIL(2, "不通过"),
    PART_PASS(3, "部分通过"),
    ;

    Integer code;

    String message;

    RSInspectionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static Boolean notContains(Integer code) {
        for (RSInspectionEnum value : RSInspectionEnum.values()) {
            if (value.getCode().equals(code)) return false;
        }
        return true;
    }

    public static String desc(Integer code) {
        for (RSInspectionEnum value : RSInspectionEnum.values()) {
            if (value.getCode().equals(code)) return value.getMessage();
        }
        return "";
    }

    public static RSInspectionEnum fromInt(Integer code) {
        for (RSInspectionEnum from : RSInspectionEnum.values()) {
            if (Objects.equals(from.code, code)) {
                return from;
            }
        }
        throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
    }
}