package com.dt.component.common.enums.rs;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@NoArgsConstructor
public enum RSBillSourceEnum {

    OMS(1, "OMS"),
    MALL_DIRECT(2, "天猫直营"),
    MALL_PLATFORM(3, "天猫平台"),
    DY_MARKET(4, "抖店超市"),
    ;

    Integer code;

    String message;

    RSBillSourceEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static RSBillSourceEnum fromInt(Integer code) {
        for (RSBillSourceEnum from : RSBillSourceEnum.values()) {
            if (Objects.equals(from.code, code)) {
                return from;
            }
        }
        throw new BaseException(BaseBizEnum.TIP,"不支持的销退单单据来源");
    }

    public static boolean notContain(Integer code) {
        for (RSBillSourceEnum value : RSBillSourceEnum.values()) {
            if (value.getCode().equals(code)) return false;
        }
        return true;
    }

    public static String desc(Integer code) {
        for (RSBillSourceEnum value : RSBillSourceEnum.values()) {
            if (value.getCode().equals(code)) return value.getMessage();
        }
        return "";
    }
    
    public static List<Integer> taoTianBillSourceCodeList() {
        return Stream.of(MALL_DIRECT, MALL_PLATFORM).map(RSBillSourceEnum::getCode).collect(Collectors.toList());
    }
}