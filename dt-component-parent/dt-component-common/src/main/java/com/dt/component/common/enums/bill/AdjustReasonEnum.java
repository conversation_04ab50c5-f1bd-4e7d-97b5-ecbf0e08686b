package com.dt.component.common.enums.bill;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.IResultEnum;
import com.dt.component.common.exceptions.BaseException;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;

/**
 * <p>
 * 移位单状态枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-14
 */
@Getter
@ApiModel("AdjustReasonEnum 调整原因枚举")
public enum AdjustReasonEnum implements IResultEnum {

    STATUS_CHECK("10", "盘点"),
    STATUS_EXCEPTION("20", "异常"),
    STATUS_OTHER("30", "其它"),
    ;
    private String code;
    private String name;

    AdjustReasonEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public static AdjustReasonEnum getEnum(String code) {
        AdjustReasonEnum result = Arrays.stream(AdjustReasonEnum.values())
                .filter(a -> a.getCode().equals(code))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return result;
    }

    @Override
    public String message() {
        return this.name;
    }

    @Override
    public Integer value() {
        return Integer.parseInt(this.code);
    }
}
