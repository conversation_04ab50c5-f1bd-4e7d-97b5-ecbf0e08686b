package com.dt.component.common.enums.bill;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.IResultEnum;
import com.dt.component.common.exceptions.BaseException;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;

/**
 * <p>
 * 移位单状态枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-14
 */
@Getter
@ApiModel("Adjust 状态枚举")
public enum AdjustStatusEnum implements IResultEnum {

    CREATED("5", "创建"), AUDIT_FAILURE("8", "审核驳回"), STATUS_WAIT("10", "审核中"), STATUS_CHECKED("20", "已审核"),
    STATUS_COMPLETED("30", "已完成"), STATUS_CANCELED("40", "已取消"),
    IN_AUDIT("50","仓内审核中"),
    IN_REJECT("60","仓内审核驳回"),
    IN_CHECKED("70","仓内审核通过"),
    ERP_AUDIT("80","上游审核中"),
    ERP_REJECT("90","上游驳回"),
    ERP_CHECKED("100","上游审核通过")
    ;

    private String status;
    private String name;

    AdjustStatusEnum(String status, String name) {
        this.status = status;
        this.name = name;
    }

    public static AdjustStatusEnum getEnum(String status) {
        AdjustStatusEnum result =
                Arrays.stream(AdjustStatusEnum.values()).filter(a -> a.getStatus().equals(status)).findFirst().orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return result;
    }

    @Override
    public String message() {
        return this.name;
    }

    @Override
    public Integer value() {
        return Integer.parseInt(this.status);
    }
}
