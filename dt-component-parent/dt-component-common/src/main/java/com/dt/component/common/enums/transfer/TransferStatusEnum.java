package com.dt.component.common.enums.transfer;

import java.util.Arrays;

import org.springframework.util.ObjectUtils;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;

import lombok.Getter;

/**
 * 转移单状态 创建、已审核、已完成、已取消 Created by nobody on 2020/12/28 17:35
 */
@Getter
public enum TransferStatusEnum {
    CREATED("5", "创建"), UNDER_REVIEW("7", "审核中"), AUDIT_FAILURE("8", "审核驳回"), EXAMINED("10", "已审核"),
    DONE("15", "已完成"),
    CANCEL("20", "已取消"),
    IN_AUDIT("50","仓内审核中"),
    IN_REJECT("60","仓内审核驳回"),
    IN_CHECKED("70","仓内审核通过"),
    ERP_AUDIT("80","上游审核中"),
    ERP_REJECT("90","上游驳回"),
    ERP_CHECKED("100","上游审核通过")
    ;

    private String code;
    private String name;

    TransferStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TransferStatusEnum getEnum(String code) {
        TransferStatusEnum result =
            Arrays.stream(TransferStatusEnum.values()).filter(a -> a.getCode().equals(code)).findFirst().orElse(null);
        if (ObjectUtils.isEmpty(result)) {
            throw new BaseException(BaseBizEnum.UN_SUPPORT_OPERATER);
        }
        return result;
    }
}
